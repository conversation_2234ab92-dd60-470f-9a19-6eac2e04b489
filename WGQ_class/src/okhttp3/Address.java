/*     */ package okhttp3;
/*     */ 
/*     */ import java.net.Proxy;
/*     */ import java.net.ProxySelector;
/*     */ import java.util.List;
/*     */ import javax.net.SocketFactory;
/*     */ import javax.net.ssl.HostnameVerifier;
/*     */ import javax.net.ssl.SSLSocketFactory;
/*     */ import okhttp3.internal.Util;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public final class Address
/*     */ {
/*     */   final HttpUrl url;
/*     */   final Dns dns;
/*     */   final SocketFactory socketFactory;
/*     */   final Authenticator proxyAuthenticator;
/*     */   final List<Protocol> protocols;
/*     */   final List<ConnectionSpec> connectionSpecs;
/*     */   final ProxySelector proxySelector;
/*     */   final Proxy proxy;
/*     */   final SSLSocketFactory sslSocketFactory;
/*     */   final HostnameVerifier hostnameVerifier;
/*     */   final CertificatePinner certificatePinner;
/*     */   
/*     */   public Address(String paramString, int paramInt, Dns paramDns, SocketFactory paramSocketFactory, SSLSocketFactory paramSSLSocketFactory, HostnameVerifier paramHostnameVerifier, CertificatePinner paramCertificatePinner, Authenticator paramAuthenticator, Proxy paramProxy, List<Protocol> paramList, List<ConnectionSpec> paramList1, ProxySelector paramProxySelector) {
/*  56 */     this
/*     */ 
/*     */ 
/*     */       
/*  60 */       .url = (new HttpUrl.Builder()).scheme((paramSSLSocketFactory != null) ? "https" : "http").host(paramString).port(paramInt).build();
/*     */     
/*  62 */     if (paramDns == null) throw new NullPointerException("dns == null"); 
/*  63 */     this.dns = paramDns;
/*     */     
/*  65 */     if (paramSocketFactory == null) throw new NullPointerException("socketFactory == null"); 
/*  66 */     this.socketFactory = paramSocketFactory;
/*     */     
/*  68 */     if (paramAuthenticator == null) {
/*  69 */       throw new NullPointerException("proxyAuthenticator == null");
/*     */     }
/*  71 */     this.proxyAuthenticator = paramAuthenticator;
/*     */     
/*  73 */     if (paramList == null) throw new NullPointerException("protocols == null"); 
/*  74 */     this.protocols = Util.immutableList(paramList);
/*     */     
/*  76 */     if (paramList1 == null) throw new NullPointerException("connectionSpecs == null"); 
/*  77 */     this.connectionSpecs = Util.immutableList(paramList1);
/*     */     
/*  79 */     if (paramProxySelector == null) {
/*     */       
/*  81 */       logger.error("============proxySelector is null,now reset....");
/*     */       try {
/*  83 */         Class<?> clazz = Class.forName("sun.net.spi.DefaultProxySelector");
/*  84 */         if (clazz != null && ProxySelector.class.isAssignableFrom(clazz)) {
/*  85 */           paramProxySelector = (ProxySelector)clazz.newInstance();
/*     */         }
/*  87 */       } catch (Exception exception) {
/*  88 */         paramProxySelector = null;
/*  89 */         logger.error("============error occured!" + exception.getMessage());
/*  90 */         throw new NullPointerException("proxySelector == null");
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/*  95 */     this.proxySelector = paramProxySelector;
/*     */     
/*  97 */     this.proxy = paramProxy;
/*  98 */     this.sslSocketFactory = paramSSLSocketFactory;
/*  99 */     this.hostnameVerifier = paramHostnameVerifier;
/* 100 */     this.certificatePinner = paramCertificatePinner;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public HttpUrl url() {
/* 108 */     return this.url;
/*     */   }
/*     */ 
/*     */   
/*     */   public Dns dns() {
/* 113 */     return this.dns;
/*     */   }
/*     */ 
/*     */   
/*     */   public SocketFactory socketFactory() {
/* 118 */     return this.socketFactory;
/*     */   }
/*     */ 
/*     */   
/*     */   public Authenticator proxyAuthenticator() {
/* 123 */     return this.proxyAuthenticator;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<Protocol> protocols() {
/* 131 */     return this.protocols;
/*     */   }
/*     */   
/*     */   public List<ConnectionSpec> connectionSpecs() {
/* 135 */     return this.connectionSpecs;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public ProxySelector proxySelector() {
/* 143 */     return this.proxySelector;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Proxy proxy() {
/* 151 */     return this.proxy;
/*     */   }
/*     */ 
/*     */   
/*     */   public SSLSocketFactory sslSocketFactory() {
/* 156 */     return this.sslSocketFactory;
/*     */   }
/*     */ 
/*     */   
/*     */   public HostnameVerifier hostnameVerifier() {
/* 161 */     return this.hostnameVerifier;
/*     */   }
/*     */ 
/*     */   
/*     */   public CertificatePinner certificatePinner() {
/* 166 */     return this.certificatePinner;
/*     */   }
/*     */   
/*     */   public boolean equals(Object paramObject) {
/* 170 */     if (paramObject instanceof Address) {
/* 171 */       Address address = (Address)paramObject;
/* 172 */       return (this.url.equals(address.url) && this.dns
/* 173 */         .equals(address.dns) && this.proxyAuthenticator
/* 174 */         .equals(address.proxyAuthenticator) && this.protocols
/* 175 */         .equals(address.protocols) && this.connectionSpecs
/* 176 */         .equals(address.connectionSpecs) && this.proxySelector
/* 177 */         .equals(address.proxySelector) && 
/* 178 */         Util.equal(this.proxy, address.proxy) && 
/* 179 */         Util.equal(this.sslSocketFactory, address.sslSocketFactory) && 
/* 180 */         Util.equal(this.hostnameVerifier, address.hostnameVerifier) && 
/* 181 */         Util.equal(this.certificatePinner, address.certificatePinner));
/*     */     } 
/* 183 */     return false;
/*     */   }
/*     */   
/*     */   public int hashCode() {
/* 187 */     int i = 17;
/* 188 */     i = 31 * i + this.url.hashCode();
/* 189 */     i = 31 * i + this.dns.hashCode();
/* 190 */     i = 31 * i + this.proxyAuthenticator.hashCode();
/* 191 */     i = 31 * i + this.protocols.hashCode();
/* 192 */     i = 31 * i + this.connectionSpecs.hashCode();
/* 193 */     i = 31 * i + this.proxySelector.hashCode();
/* 194 */     i = 31 * i + ((this.proxy != null) ? this.proxy.hashCode() : 0);
/* 195 */     i = 31 * i + ((this.sslSocketFactory != null) ? this.sslSocketFactory.hashCode() : 0);
/* 196 */     i = 31 * i + ((this.hostnameVerifier != null) ? this.hostnameVerifier.hashCode() : 0);
/* 197 */     i = 31 * i + ((this.certificatePinner != null) ? this.certificatePinner.hashCode() : 0);
/* 198 */     return i;
/*     */   }
/* 200 */   private static Logger logger = LoggerFactory.getLogger(Address.class);
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/okhttp3/Address.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */