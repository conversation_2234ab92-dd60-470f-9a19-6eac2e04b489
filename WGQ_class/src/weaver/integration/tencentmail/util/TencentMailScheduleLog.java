/*     */ package weaver.integration.tencentmail.util;
/*     */ 
/*     */ import com.alibaba.fastjson.JSON;
/*     */ import java.text.SimpleDateFormat;
/*     */ import java.util.Calendar;
/*     */ import java.util.Date;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import org.apache.commons.lang.StringUtils;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ import weaver.interfaces.schedule.BaseCronJob;
/*     */ 
/*     */ 
/*     */ 
/*     */ public class TencentMailScheduleLog
/*     */   extends BaseCronJob
/*     */ {
/*  21 */   private static final Logger newLog = LoggerFactory.getLogger(TencentMailScheduleLog.class);
/*     */ 
/*     */   
/*     */   public void execute() {
/*  25 */     newLog.info("执行腾讯企业邮箱日志清理计划任务开始");
/*     */     
/*  27 */     HashMap hashMap = (HashMap)getSetting();
/*  28 */     newLog.info(String.format("日志清理设置：%s", new Object[] { JSON.toJSONString(hashMap) }));
/*  29 */     String str1 = (String)hashMap.get("cleanStatus");
/*  30 */     String str2 = (String)hashMap.get("cleanType");
/*  31 */     String str3 = (String)hashMap.get("cleanCustomValue");
/*     */     
/*  33 */     if (StringUtils.isEmpty(str1) || StringUtils.isEmpty(str2)) {
/*  34 */       newLog.error("参数cleanStatus或cleanType为空");
/*     */       return;
/*     */     } 
/*  37 */     if ("0".equals(str1)) {
/*  38 */       newLog.error("日志清理已关闭");
/*     */       
/*     */       return;
/*     */     } 
/*  42 */     if ("4".equals(str2) && StringUtils.isEmpty(str3)) {
/*  43 */       newLog.error("cleanCustomValue参数为空");
/*     */       
/*     */       return;
/*     */     } 
/*  47 */     cleanLog(str2, str3);
/*  48 */     newLog.info("执行腾讯企业邮箱日志清理计划任务结束");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, String> getSetting() {
/*  56 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  57 */     RecordSet recordSet = new RecordSet();
/*  58 */     String str = "select * from QQMailLogSetting";
/*  59 */     boolean bool = recordSet.executeQuery(str, new Object[0]);
/*  60 */     if (bool && recordSet.next()) {
/*  61 */       String str1 = Util.null2String(recordSet.getString("CleanStatus"));
/*  62 */       String str2 = Util.null2String(recordSet.getString("cleanType"));
/*  63 */       String str3 = Util.null2String(recordSet.getString("CleanCustomValue"));
/*     */       
/*  65 */       hashMap.put("cleanStatus", str1);
/*  66 */       hashMap.put("cleanType", str2);
/*  67 */       hashMap.put("cleanCustomValue", str3);
/*     */     } 
/*  69 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void cleanLog(String paramString1, String paramString2) {
/*  78 */     String str = getDate(paramString1, paramString2);
/*  79 */     if (StringUtils.isEmpty(str)) {
/*  80 */       newLog.error("计算日期为空");
/*     */       
/*     */       return;
/*     */     } 
/*  84 */     RecordSet recordSet = new RecordSet();
/*     */     try {
/*  86 */       newLog.info("腾讯企业邮箱清理 " + str + "之前的日期");
/*  87 */       String str1 = " delete from QQMailLog where logdate < ? ";
/*  88 */       recordSet.executeUpdate(str1, new Object[] { str });
/*  89 */     } catch (Exception exception) {
/*  90 */       newLog.error("清理日志出错", exception);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getDate(String paramString1, String paramString2) {
/* 102 */     int i = 0;
/* 103 */     if ("1".equals(paramString1)) {
/* 104 */       i = 30;
/* 105 */     } else if ("2".equals(paramString1)) {
/* 106 */       i = 90;
/* 107 */     } else if ("3".equals(paramString1)) {
/* 108 */       i = 180;
/* 109 */     } else if ("4".equals(paramString1)) {
/*     */       try {
/* 111 */         i = Integer.parseInt(paramString2);
/* 112 */       } catch (Exception exception) {
/* 113 */         return null;
/*     */       } 
/*     */       
/* 116 */       if (i <= 0) {
/* 117 */         return null;
/*     */       }
/*     */     } else {
/* 120 */       return null;
/*     */     } 
/*     */     
/* 123 */     SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
/* 124 */     Calendar calendar = Calendar.getInstance();
/* 125 */     calendar.setTime(new Date());
/* 126 */     int j = calendar.get(5);
/*     */     
/* 128 */     calendar.set(5, j - i);
/*     */     
/* 130 */     return simpleDateFormat.format(calendar.getTime());
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/tencentmail/util/TencentMailScheduleLog.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */