/*     */ package weaver.integration.workflowtrigger.targetdata.impl;
/*     */ 
/*     */ import com.alibaba.fastjson.JSON;
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.general.Util;
/*     */ import weaver.integration.framework.Context;
/*     */ import weaver.integration.framework.converter.IConvert;
/*     */ import weaver.integration.framework.data.GeneralData;
/*     */ import weaver.integration.framework.data.RecordData;
/*     */ import weaver.integration.framework.data.TableData;
/*     */ import weaver.integration.framework.data.field.FieldData;
/*     */ import weaver.integration.framework.mapping.impl.FieldDataMapping;
/*     */ import weaver.integration.framework.mapping.impl.GeneralDataMapping;
/*     */ import weaver.integration.framework.mapping.impl.RecordDataMapping;
/*     */ import weaver.integration.framework.mapping.impl.TableDataMapping;
/*     */ import weaver.integration.framework.mappingdefine.impl.GeneralMappingDefine;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ import weaver.integration.workflowtrigger.config.WorkflowTriggerConfig;
/*     */ import weaver.integration.workflowtrigger.config.WorkflowTriggerDetailConfig;
/*     */ import weaver.integration.workflowtrigger.targetdata.ITargetData;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class TargetData4Common
/*     */   implements ITargetData
/*     */ {
/*     */   public List<GeneralDataMapping> setTargetData(WorkflowTriggerConfig paramWorkflowTriggerConfig, List<GeneralData> paramList) {
/*  38 */     GeneralMappingDefine generalMappingDefine = paramWorkflowTriggerConfig.getGeneralMappingDefine();
/*     */     
/*  40 */     ArrayList<GeneralDataMapping> arrayList = new ArrayList();
/*     */     
/*  42 */     for (GeneralData generalData : paramList) {
/*  43 */       GeneralDataMapping generalDataMapping = (GeneralDataMapping)generalMappingDefine.toMapping();
/*  44 */       List<TableDataMapping> list = generalDataMapping.getTableDataMappingList();
/*  45 */       for (byte b = 0; b < list.size(); b++) {
/*  46 */         TableDataMapping tableDataMapping = list.get(b);
/*     */ 
/*     */ 
/*     */         
/*  50 */         List<FieldDataMapping> list1 = tableDataMapping.getTemplateFieldDataMappingList();
/*  51 */         List<TableData> list2 = generalData.getTableDataList();
/*  52 */         List list3 = new ArrayList();
/*  53 */         if (list2.size() > b) {
/*  54 */           list3 = ((TableData)list2.get(b)).getRecordDataList();
/*     */         }
/*  56 */         if (list3 != null && list3.size() != 0)
/*     */         {
/*     */ 
/*     */           
/*  60 */           for (RecordData recordData : list3) {
/*  61 */             Map map = recordData.getFieldData();
/*     */             
/*  63 */             List<FieldDataMapping> list4 = cloneFieldDataMappingList(list1);
/*     */             
/*  65 */             for (FieldDataMapping fieldDataMapping : list4)
/*     */             {
/*  67 */               fieldDataMapping.getSourceField().setFieldValue(map.get(fieldDataMapping.getSourceField().getFieldName().toLowerCase()));
/*     */             }
/*     */ 
/*     */             
/*  71 */             RecordDataMapping recordDataMapping = new RecordDataMapping();
/*     */             
/*  73 */             recordDataMapping.setFieldDataMappingList(list4);
/*  74 */             recordDataMapping.setGeneralDataList(paramList);
/*     */             try {
/*  76 */               TableData tableData = generalData.getTableDataList().get(b);
/*  77 */               recordDataMapping.setTableData(tableData);
/*  78 */             } catch (Exception exception) {
/*  79 */               exception.printStackTrace();
/*     */             } 
/*     */ 
/*     */ 
/*     */             
/*  84 */             ((TableDataMapping)generalDataMapping.getTableDataMappingList().get(b)).getRecordDataMappingList().add(recordDataMapping);
/*     */ 
/*     */             
/*  87 */             generalDataMapping.mapping();
/*     */           } 
/*     */         }
/*     */       } 
/*     */       
/*  92 */       arrayList.add(generalDataMapping);
/*     */     } 
/*     */     
/*  95 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<GeneralDataMapping> setTargetData(WorkflowTriggerConfig paramWorkflowTriggerConfig, List<GeneralData> paramList, List<String> paramList1) {
/* 111 */     GeneralMappingDefine generalMappingDefine = paramWorkflowTriggerConfig.getGeneralMappingDefine();
/*     */     
/* 113 */     ArrayList<GeneralDataMapping> arrayList = new ArrayList();
/* 114 */     byte b = 0;
/* 115 */     for (GeneralData generalData : paramList) {
/* 116 */       String str = paramList1.get(b);
/* 117 */       GeneralDataMapping generalDataMapping = null;
/* 118 */       List<TableDataMapping> list = null;
/* 119 */       generalDataMapping = (GeneralDataMapping)generalMappingDefine.toMapping();
/* 120 */       list = generalDataMapping.getTableDataMappingList();
/* 121 */       for (byte b1 = 0; b1 < list.size(); b1++) {
/* 122 */         TableDataMapping tableDataMapping = null;
/* 123 */         tableDataMapping = list.get(b1);
/*     */ 
/*     */ 
/*     */         
/* 127 */         List<FieldDataMapping> list1 = null;
/* 128 */         list1 = tableDataMapping.getTemplateFieldDataMappingList();
/* 129 */         List<TableData> list2 = generalData.getTableDataList();
/* 130 */         List list3 = new ArrayList();
/* 131 */         if (list2.size() > b1) {
/* 132 */           list3 = ((TableData)list2.get(b1)).getRecordDataList();
/*     */         }
/* 134 */         if (list3 != null && list3.size() != 0)
/*     */         {
/*     */ 
/*     */           
/* 138 */           for (RecordData recordData : list3) {
/* 139 */             Map map = recordData.getFieldData();
/*     */             
/* 141 */             List<FieldDataMapping> list4 = cloneFieldDataMappingList(list1);
/*     */             
/* 143 */             for (FieldDataMapping fieldDataMapping : list4) {
/*     */               
/* 145 */               Map map1 = getTransData(fieldDataMapping, recordData, str, map.get(fieldDataMapping.getSourceField().getFieldName().toLowerCase()));
/* 146 */               WorkflowTriggerDetailConfig workflowTriggerDetailConfig = (WorkflowTriggerDetailConfig)fieldDataMapping.getContext().getAttributes().get("dtCfg");
/* 147 */               String str1 = Util.null2String(workflowTriggerDetailConfig.getConvertClass());
/* 148 */               String str2 = fieldDataMapping.getContext().getFieldId();
/* 149 */               String str3 = "";
/* 150 */               if (str2.equals("-2")) {
/* 151 */                 str3 = str;
/* 152 */               } else if (str1.equals("")) {
/* 153 */                 str3 = Util.null2String(map.get(fieldDataMapping.getSourceField().getFieldName().toLowerCase()));
/*     */               } else {
/* 155 */                 str3 = transFactory(str1, map1);
/*     */               } 
/* 157 */               fieldDataMapping.getSourceField().setFieldValue(str3);
/*     */             } 
/*     */ 
/*     */             
/* 161 */             RecordDataMapping recordDataMapping = new RecordDataMapping();
/*     */             
/* 163 */             recordDataMapping.setFieldDataMappingList(list4);
/* 164 */             recordDataMapping.setGeneralDataList(paramList);
/*     */             try {
/* 166 */               TableData tableData = generalData.getTableDataList().get(b1);
/* 167 */               recordDataMapping.setTableData(tableData);
/* 168 */             } catch (Exception exception) {
/* 169 */               exception.printStackTrace();
/*     */             } 
/*     */ 
/*     */ 
/*     */             
/* 174 */             ((TableDataMapping)generalDataMapping.getTableDataMappingList().get(b1)).getRecordDataMappingList().add(recordDataMapping);
/*     */ 
/*     */             
/* 177 */             generalDataMapping.mapping();
/*     */           } 
/*     */         }
/*     */       } 
/*     */       
/* 182 */       arrayList.add(generalDataMapping);
/* 183 */       b++;
/*     */     } 
/*     */     
/* 186 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<GeneralDataMapping> setTargetData(WorkflowTriggerConfig paramWorkflowTriggerConfig, List<GeneralData> paramList, String paramString) {
/* 200 */     GeneralMappingDefine generalMappingDefine = paramWorkflowTriggerConfig.getGeneralMappingDefine();
/*     */     
/* 202 */     ArrayList<GeneralDataMapping> arrayList = new ArrayList();
/*     */     
/* 204 */     for (GeneralData generalData : paramList) {
/* 205 */       GeneralDataMapping generalDataMapping = null;
/* 206 */       List<TableDataMapping> list = null;
/* 207 */       generalDataMapping = (GeneralDataMapping)generalMappingDefine.toMapping();
/* 208 */       list = generalDataMapping.getTableDataMappingList();
/* 209 */       for (byte b = 0; b < list.size(); b++) {
/* 210 */         TableDataMapping tableDataMapping = null;
/* 211 */         tableDataMapping = list.get(b);
/*     */ 
/*     */ 
/*     */         
/* 215 */         List<FieldDataMapping> list1 = null;
/* 216 */         list1 = tableDataMapping.getTemplateFieldDataMappingList();
/* 217 */         List<TableData> list2 = generalData.getTableDataList();
/* 218 */         List list3 = new ArrayList();
/* 219 */         if (list2.size() > b) {
/* 220 */           list3 = ((TableData)list2.get(b)).getRecordDataList();
/*     */         }
/* 222 */         if (list3 != null && list3.size() != 0)
/*     */         {
/*     */ 
/*     */           
/* 226 */           for (RecordData recordData : list3) {
/* 227 */             Map map = recordData.getFieldData();
/*     */             
/* 229 */             List<FieldDataMapping> list4 = cloneFieldDataMappingList(list1);
/*     */             
/* 231 */             for (FieldDataMapping fieldDataMapping : list4) {
/*     */               
/* 233 */               Map map1 = getTransData(fieldDataMapping, recordData, paramString, map.get(fieldDataMapping.getSourceField().getFieldName().toLowerCase()));
/* 234 */               WorkflowTriggerDetailConfig workflowTriggerDetailConfig = (WorkflowTriggerDetailConfig)fieldDataMapping.getContext().getAttributes().get("dtCfg");
/* 235 */               String str1 = Util.null2String(workflowTriggerDetailConfig.getConvertClass());
/* 236 */               String str2 = fieldDataMapping.getContext().getFieldId();
/* 237 */               String str3 = "";
/* 238 */               if (str2.equals("-2")) {
/* 239 */                 str3 = paramString;
/* 240 */               } else if (str1.equals("")) {
/* 241 */                 str3 = Util.null2String(map.get(fieldDataMapping.getSourceField().getFieldName().toLowerCase()));
/*     */               } else {
/* 243 */                 str3 = transFactory(str1, map1);
/*     */               } 
/* 245 */               fieldDataMapping.getSourceField().setFieldValue(str3);
/*     */             } 
/*     */ 
/*     */             
/* 249 */             RecordDataMapping recordDataMapping = new RecordDataMapping();
/*     */             
/* 251 */             recordDataMapping.setFieldDataMappingList(list4);
/* 252 */             recordDataMapping.setGeneralDataList(paramList);
/*     */             try {
/* 254 */               TableData tableData = generalData.getTableDataList().get(b);
/* 255 */               recordDataMapping.setTableData(tableData);
/* 256 */             } catch (Exception exception) {
/* 257 */               exception.printStackTrace();
/*     */             } 
/*     */ 
/*     */ 
/*     */             
/* 262 */             ((TableDataMapping)generalDataMapping.getTableDataMappingList().get(b)).getRecordDataMappingList().add(recordDataMapping);
/*     */ 
/*     */             
/* 265 */             generalDataMapping.mapping();
/*     */           } 
/*     */         }
/*     */       } 
/*     */       
/* 270 */       arrayList.add(generalDataMapping);
/*     */     } 
/*     */     
/* 273 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<FieldDataMapping> cloneFieldDataMappingList(List<FieldDataMapping> paramList) {
/* 283 */     ArrayList<FieldDataMapping> arrayList = new ArrayList();
/*     */     
/* 285 */     for (FieldDataMapping fieldDataMapping1 : paramList) {
/* 286 */       FieldDataMapping fieldDataMapping2 = cloneFieldDataMapping(fieldDataMapping1);
/* 287 */       arrayList.add(fieldDataMapping2);
/*     */     } 
/*     */     
/* 290 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<FieldDataMapping> cloneFieldDataMappingList(List<FieldDataMapping> paramList, String paramString) {
/* 302 */     ArrayList<FieldDataMapping> arrayList = new ArrayList();
/*     */     
/* 304 */     for (FieldDataMapping fieldDataMapping1 : paramList) {
/* 305 */       FieldDataMapping fieldDataMapping2 = cloneFieldDataMapping(fieldDataMapping1, paramString);
/* 306 */       arrayList.add(fieldDataMapping2);
/*     */     } 
/*     */     
/* 309 */     return arrayList;
/*     */   }
/*     */   
/*     */   private FieldDataMapping cloneFieldDataMapping(FieldDataMapping paramFieldDataMapping, String paramString) {
/* 313 */     FieldDataMapping fieldDataMapping = new FieldDataMapping();
/* 314 */     FieldData fieldData1 = new FieldData();
/* 315 */     fieldData1.setFieldName(paramFieldDataMapping.getSourceField().getFieldName());
/* 316 */     fieldData1.setFieldValue(paramFieldDataMapping.getSourceField().getFieldValue());
/* 317 */     fieldData1.setFieldLabel(paramFieldDataMapping.getSourceField().getFieldLabel());
/* 318 */     fieldData1.setFieldType(paramFieldDataMapping.getSourceField().getFieldType());
/* 319 */     FieldData fieldData2 = new FieldData();
/* 320 */     Map map = getTransData(paramFieldDataMapping, paramString, null);
/* 321 */     WorkflowTriggerDetailConfig workflowTriggerDetailConfig = (WorkflowTriggerDetailConfig)paramFieldDataMapping.getContext().getAttributes().get("dtCfg");
/* 322 */     String str1 = Util.null2String(workflowTriggerDetailConfig.getConvertClass());
/* 323 */     String str2 = "";
/* 324 */     if (str1.equals("")) {
/* 325 */       str2 = Util.null2String(paramFieldDataMapping.getSourceField().getFieldValue());
/*     */     } else {
/* 327 */       str2 = transFactory(str1, map);
/*     */     } 
/* 329 */     fieldData2.setFieldName(paramFieldDataMapping.getTargetField().getFieldName());
/* 330 */     fieldData2.setFieldValue(str2);
/* 331 */     fieldData2.setFieldLabel(paramFieldDataMapping.getTargetField().getFieldLabel());
/* 332 */     fieldData2.setFieldType(paramFieldDataMapping.getTargetField().getFieldType());
/* 333 */     fieldDataMapping.setContext(paramFieldDataMapping.getContext());
/* 334 */     fieldDataMapping.setSourceField(fieldData1);
/* 335 */     fieldDataMapping.setTargetField(fieldData2);
/*     */     
/* 337 */     return fieldDataMapping;
/*     */   }
/*     */ 
/*     */   
/*     */   private FieldDataMapping cloneFieldDataMapping(FieldDataMapping paramFieldDataMapping) {
/* 342 */     FieldDataMapping fieldDataMapping = new FieldDataMapping();
/* 343 */     FieldData fieldData1 = new FieldData();
/* 344 */     fieldData1.setFieldName(paramFieldDataMapping.getSourceField().getFieldName());
/* 345 */     fieldData1.setFieldValue(paramFieldDataMapping.getSourceField().getFieldValue());
/* 346 */     fieldData1.setFieldLabel(paramFieldDataMapping.getSourceField().getFieldLabel());
/* 347 */     fieldData1.setFieldType(paramFieldDataMapping.getSourceField().getFieldType());
/* 348 */     FieldData fieldData2 = new FieldData();
/* 349 */     fieldData2.setFieldName(paramFieldDataMapping.getTargetField().getFieldName());
/* 350 */     fieldData2.setFieldValue(paramFieldDataMapping.getTargetField().getFieldValue());
/* 351 */     fieldData2.setFieldLabel(paramFieldDataMapping.getTargetField().getFieldLabel());
/* 352 */     fieldData2.setFieldType(paramFieldDataMapping.getTargetField().getFieldType());
/* 353 */     fieldDataMapping.setContext(paramFieldDataMapping.getContext());
/* 354 */     fieldDataMapping.setSourceField(fieldData1);
/* 355 */     fieldDataMapping.setTargetField(fieldData2);
/*     */     
/* 357 */     return fieldDataMapping;
/*     */   }
/*     */   
/* 360 */   private Logger logger = LoggerFactory.getLogger(getClass());
/*     */   
/*     */   private Map getTransData(FieldDataMapping paramFieldDataMapping, String paramString, Object paramObject) {
/* 363 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 364 */     String str1 = paramFieldDataMapping.getSourceField().getFieldName();
/* 365 */     Context context = paramFieldDataMapping.getContext();
/* 366 */     String str2 = context.getChangeType();
/* 367 */     String str3 = context.getCustomsql();
/* 368 */     String str4 = context.getFieldId();
/* 369 */     String str5 = context.getWffileddbtype();
/* 370 */     RecordData recordData = context.getRecordData();
/* 371 */     Map map1 = context.getWorkflowVariables();
/* 372 */     Map map2 = context.getAttributes();
/* 373 */     String str6 = (String)map2.get("attachment_settings");
/* 374 */     String str7 = Util.null2String(map2.get("datasourceid"));
/* 375 */     Object object = null;
/* 376 */     if (str4.equals("-2") && paramObject == null) {
/* 377 */       paramObject = paramString;
/* 378 */       object = Util.null2String(paramObject);
/*     */     } else {
/* 380 */       object = paramObject;
/*     */     } 
/*     */ 
/*     */     
/* 384 */     String str8 = (String)map1.get("workflowid");
/* 385 */     hashMap.put("wffileddbtype", str5);
/* 386 */     hashMap.put("recordData", recordData);
/* 387 */     hashMap.put("datasourceid", str7);
/* 388 */     hashMap.put("customsql", str3);
/* 389 */     hashMap.put("sourcevalue", object);
/* 390 */     hashMap.put("sourceFieldName", str1);
/* 391 */     hashMap.put("changetype", str2);
/* 392 */     hashMap.put("fieldid", str4);
/* 393 */     hashMap.put("attachment_settings", str6);
/* 394 */     hashMap.put("createrid", paramString);
/* 395 */     hashMap.put("workflowid", str8);
/* 396 */     hashMap.put("filename", str1);
/* 397 */     return hashMap;
/*     */   }
/*     */   
/*     */   private Map getTransData(FieldDataMapping paramFieldDataMapping, RecordData paramRecordData, String paramString, Object paramObject) {
/* 401 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 402 */     String str1 = paramFieldDataMapping.getSourceField().getFieldName();
/* 403 */     Context context = paramFieldDataMapping.getContext();
/* 404 */     String str2 = context.getChangeType();
/* 405 */     String str3 = context.getCustomsql();
/* 406 */     String str4 = context.getFieldId();
/* 407 */     String str5 = context.getWffileddbtype();
/* 408 */     Map map1 = context.getWorkflowVariables();
/* 409 */     Map map2 = context.getAttributes();
/* 410 */     String str6 = (String)map2.get("attachment_settings");
/* 411 */     String str7 = Util.null2String(map2.get("datasourceid"));
/* 412 */     String str8 = Util.null2String(map2.get("dbtype"));
/* 413 */     Object object = null;
/* 414 */     if (str4.equals("-2") && paramObject == null) {
/* 415 */       paramObject = paramString;
/* 416 */       object = Util.null2String(paramObject);
/*     */     } else {
/* 418 */       object = paramObject;
/*     */     } 
/* 420 */     hashMap.put("sourcevalue", object);
/* 421 */     if (str6 != null) {
/* 422 */       JSONObject jSONObject = JSON.parseObject(str6);
/* 423 */       if (jSONObject != null) {
/* 424 */         String str = Util.null2String(jSONObject.getString("db_field_title"));
/* 425 */         if ("null".equalsIgnoreCase(str)) {
/* 426 */           str = "";
/*     */         }
/* 428 */         if (!"".equals(str)) {
/* 429 */           String str10 = Util.null2String(paramRecordData.getFieldData().get(str.toLowerCase()));
/*     */           
/* 431 */           hashMap.put("imgFileName", str10);
/*     */         } 
/* 433 */         Object object1 = paramRecordData.getFieldData().get(str1.toLowerCase());
/* 434 */         hashMap.put("fieldpath", object1);
/*     */       } 
/*     */     } 
/*     */     
/* 438 */     String str9 = (String)map1.get("workflowid");
/* 439 */     hashMap.put("wffileddbtype", str5);
/* 440 */     hashMap.put("dbtype", str8);
/* 441 */     hashMap.put("recordData", paramRecordData);
/* 442 */     hashMap.put("datasourceid", str7);
/* 443 */     hashMap.put("customsql", str3);
/* 444 */     hashMap.put("sourceFieldName", str1);
/* 445 */     hashMap.put("changetype", str2);
/* 446 */     hashMap.put("fieldid", str4);
/* 447 */     hashMap.put("attachment_settings", str6);
/* 448 */     hashMap.put("createrid", paramString);
/* 449 */     hashMap.put("workflowid", str9);
/* 450 */     hashMap.put("filename", str1);
/* 451 */     return hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String transFactory(String paramString, Map paramMap) {
/* 460 */     String str = "";
/*     */     try {
/* 462 */       IConvert iConvert = (IConvert)Class.forName(paramString).newInstance();
/* 463 */       str = (String)iConvert.convert(paramMap);
/* 464 */       return str;
/* 465 */     } catch (Exception exception) {
/* 466 */       return null;
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/workflowtrigger/targetdata/impl/TargetData4Common.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */