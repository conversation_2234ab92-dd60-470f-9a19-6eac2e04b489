package weaver.integration.workflowtrigger.targetdata;

import java.util.List;
import weaver.integration.framework.data.GeneralData;
import weaver.integration.framework.mapping.impl.GeneralDataMapping;
import weaver.integration.workflowtrigger.config.WorkflowTriggerConfig;

public interface ITargetData {
  List<GeneralDataMapping> setTargetData(WorkflowTriggerConfig paramWorkflowTriggerConfig, List<GeneralData> paramList);
  
  List<GeneralDataMapping> setTargetData(WorkflowTriggerConfig paramWorkflowTriggerConfig, List<GeneralData> paramList, String paramString);
  
  List<GeneralDataMapping> setTargetData(WorkflowTriggerConfig paramWorkflowTriggerConfig, List<GeneralData> paramList, List paramList1);
}


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/workflowtrigger/targetdata/ITargetData.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */