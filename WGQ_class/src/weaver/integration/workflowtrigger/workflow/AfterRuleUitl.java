/*     */ package weaver.integration.workflowtrigger.workflow;
/*     */ 
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ import weaver.integration.workflowtrigger.config.WorkflowTriggerConfig;
/*     */ import weaver.workflow.workflow.WorkflowVersion;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class AfterRuleUitl
/*     */ {
/*  24 */   private static Logger newlog = LoggerFactory.getLogger(AfterRuleUitl.class);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean isExistForCreator(String paramString) {
/*  32 */     String str = "select id from hrmresource where id=? union  select id from hrmresourcemanager  where id= ?  ";
/*  33 */     if (paramString.equals("")) {
/*  34 */       return false;
/*     */     }
/*  36 */     boolean bool = false;
/*  37 */     RecordSet recordSet = new RecordSet();
/*  38 */     recordSet.executeQuery(str, new Object[] { paramString, paramString });
/*  39 */     if (recordSet.next()) {
/*  40 */       bool = true;
/*     */     }
/*  42 */     return bool;
/*     */   }
/*     */ 
/*     */   
/*     */   public static boolean CheckDetailSetting(String paramString) {
/*  47 */     String str = "select mainid from outerdatawfsetdetail where mainid=?";
/*  48 */     boolean bool = false;
/*  49 */     RecordSet recordSet = new RecordSet();
/*  50 */     recordSet.executeQuery(str, new Object[] { paramString });
/*  51 */     if (recordSet.next()) {
/*  52 */       bool = true;
/*     */     }
/*  54 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean IsCreateNode(String paramString) {
/*  65 */     String str1 = "select currentnodetype from workflow_requestbase where requestid=? ";
/*  66 */     RecordSet recordSet = new RecordSet();
/*  67 */     String str2 = "";
/*  68 */     recordSet.executeQuery(str1, new Object[] { paramString });
/*  69 */     if (recordSet.next()) {
/*  70 */       str2 = Util.null2String(recordSet.getString("currentnodetype"));
/*     */     }
/*  72 */     if (!str2.equals("0")) {
/*  73 */       return false;
/*     */     }
/*  75 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String IscreateWf(WorkflowTriggerConfig paramWorkflowTriggerConfig, String paramString) {
/*  86 */     String str1 = "";
/*  87 */     String str2 = WorkflowVersion.getVersionStringByWfid(paramWorkflowTriggerConfig.getWorkflowid());
/*  88 */     if (str2.startsWith(",")) {
/*  89 */       str2 = str2.substring(1, str2.length());
/*     */     }
/*  91 */     if (str2.endsWith(",")) {
/*  92 */       str2 = str2.substring(0, str2.length() - 1);
/*     */     }
/*  94 */     String str3 = "select requestid from outerdatawfdetail where workflowid in(" + str2 + ") and mainid='" + paramWorkflowTriggerConfig.getId() + "' and keyfieldvalue='" + paramString + "' and outermaintable ='" + paramWorkflowTriggerConfig.getOutermaintable() + "' ";
/*  95 */     newlog.info("检查是否已触发 : " + str3 + " --start |  keyid:" + paramString);
/*     */     try {
/*  97 */       RecordSet recordSet = new RecordSet();
/*  98 */       boolean bool = recordSet.executeQuery(str3, new Object[0]);
/*  99 */       newlog.info("查询数量:" + recordSet.getCounts() + " | falg:" + bool);
/* 100 */       if (recordSet.next()) {
/* 101 */         str1 = Util.null2String(recordSet.getString("requestid"));
/* 102 */         newlog.info("keyid : " + paramString + " 已经触发!requestid:" + str1);
/*     */       }
/*     */     
/* 105 */     } catch (Exception exception) {
/* 106 */       exception.printStackTrace();
/* 107 */       newlog.error(exception);
/*     */     } 
/* 109 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String IscreateWfbynolog(WorkflowTriggerConfig paramWorkflowTriggerConfig, String paramString) {
/* 118 */     String str1 = "";
/* 119 */     String str2 = WorkflowVersion.getVersionStringByWfid(paramWorkflowTriggerConfig.getWorkflowid());
/* 120 */     if (str2.startsWith(",")) {
/* 121 */       str2 = str2.substring(1, str2.length());
/*     */     }
/* 123 */     if (str2.endsWith(",")) {
/* 124 */       str2 = str2.substring(0, str2.length() - 1);
/*     */     }
/* 126 */     String str3 = "select requestid from outerdatawfdetail where workflowid in(" + str2 + ") and mainid='" + paramWorkflowTriggerConfig.getId() + "' and keyfieldvalue='" + paramString + "' and outermaintable ='" + paramWorkflowTriggerConfig.getOutermaintable() + "' ";
/*     */     
/*     */     try {
/* 129 */       RecordSet recordSet = new RecordSet();
/* 130 */       boolean bool = recordSet.executeQuery(str3, new Object[0]);
/*     */       
/* 132 */       if (recordSet.next()) {
/* 133 */         str1 = Util.null2String(recordSet.getString("requestid"));
/*     */       }
/*     */     }
/* 136 */     catch (Exception exception) {
/* 137 */       exception.printStackTrace();
/* 138 */       newlog.error(exception);
/*     */     } 
/* 140 */     return str1;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/workflowtrigger/workflow/AfterRuleUitl.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */