/*     */ package weaver.integration.workflowtrigger.workflow;
/*     */ 
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.conn.RecordSetDataSource;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class TriggerLogUtil
/*     */ {
/*  18 */   public static Logger newlog = LoggerFactory.getLogger(TriggerLogUtil.class);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void saveRequestLog2(String paramString1, String paramString2, String paramString3, String paramString4, int paramInt1, int paramInt2, String paramString5) {
/*  31 */     String str = "INSERT INTO Int_Wftrigger_Log\n        ( trgid ,\n          wfid ,\n          requestid ,\n          requestname ,\n          opttime ,\n          result ,\n          clientip ,\n          serverip ,\n          summary ,\n          fail_summary\n        )\nVALUES  ( ? , \n          ? , \n          ? , \n          ? ,\n          ? ,\n          ? ,\n          ? ,\n          ? ,\n          ? ,\n          ?  \n        )";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  57 */     (new RecordSet()).executeUpdate(str, new Object[] { paramString1, paramString2, Integer.valueOf(paramInt2), paramString5, TimeUtil.getCurrentTimeString(), Integer.valueOf(paramInt1), "", paramString3, "", paramString4 });
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean saveOutTrigLog(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6, String paramString7, String paramString8, String paramString9) {
/*  76 */     RecordSetDataSource recordSetDataSource = new RecordSetDataSource(paramString1);
/*     */     
/*     */     try {
/*  79 */       String str = "";
/*  80 */       if (!"".equals(paramString5))
/*  81 */         str = str + paramString5 + "='" + paramString8 + "'"; 
/*  82 */       if (!"".equals(paramString5)) {
/*  83 */         str = str + ("".equals(str) ? (paramString6 + "='" + paramString9 + "'") : ("," + paramString6 + "='" + paramString9 + "'"));
/*     */       }
/*  85 */       if (!"".equals(paramString3)) {
/*     */         
/*  87 */         paramString3 = paramString3.trim();
/*  88 */         if (paramString3.indexOf("set") == 0)
/*  89 */           paramString3 = paramString3.substring(4); 
/*  90 */         str = str + ("".equals(str) ? paramString3 : ("," + paramString3));
/*     */       } 
/*     */       
/*  93 */       if (!"".equals(str))
/*     */       {
/*  95 */         String str1 = "update " + paramString2 + " set " + str + " where " + paramString4 + "='" + paramString7 + "'";
/*  96 */         newlog.info("saveOutTrigLog 外部记录日志 sql : " + str1);
/*  97 */         boolean bool = false;
/*  98 */         bool = recordSetDataSource.execute(str1);
/*     */ 
/*     */         
/* 101 */         newlog.info("sql回写到外部的结果:" + bool);
/*     */       }
/*     */     
/*     */     }
/* 105 */     catch (Exception exception) {
/*     */       
/* 107 */       newlog.error("sql回写到外部的结果出错！", exception);
/* 108 */       return false;
/*     */     } 
/* 110 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean saveUpdateWfFlag(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6) {
/* 125 */     RecordSetDataSource recordSetDataSource = new RecordSetDataSource(paramString1);
/*     */     
/*     */     try {
/* 128 */       String str1 = "";
/* 129 */       String str2 = "";
/* 130 */       if (paramString5.equals("2") && !"".equals(paramString6)) {
/* 131 */         str2 = str2 + ("".equals(str2) ? (paramString6 + "='0'") : ("," + paramString6 + "='0'"));
/*     */       }
/*     */ 
/*     */       
/* 135 */       if (!"".equals(str1)) {
/*     */         
/* 137 */         str1 = str1.trim();
/* 138 */         if (str1.indexOf("set") == 0)
/* 139 */           str1 = str1.substring(4); 
/* 140 */         str2 = str2 + ("".equals(str2) ? str1 : ("," + str1));
/*     */       } 
/*     */       
/* 143 */       if (!"".equals(str2))
/*     */       {
/* 145 */         String str = "update " + paramString2 + " set " + str2 + " where " + paramString3 + "='" + paramString4 + "'";
/* 146 */         boolean bool = recordSetDataSource.executeSql(str);
/* 147 */         newlog.info("saveUpdateWfFlag 记录更新流程标志 sql : " + str + ",sql执行结果:" + bool);
/*     */       }
/*     */     
/* 150 */     } catch (Exception exception) {
/*     */       
/* 152 */       newlog.error("数据触发出错！", exception);
/* 153 */       return false;
/*     */     } 
/* 155 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean saveTrigLog(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5) {
/*     */     try {
/* 170 */       boolean bool = false;
/* 171 */       RecordSet recordSet = new RecordSet();
/* 172 */       String str = "select requestid from outerdatawfdetail where keyfieldvalue='" + paramString4 + "' and outermaintable = '" + paramString5 + "' and workflowid=" + paramString1 + " and mainid=" + paramString3;
/* 173 */       recordSet.executeSql(str);
/* 174 */       if (recordSet.next()) {
/* 175 */         bool = true;
/*     */       }
/* 177 */       if (bool) {
/* 178 */         str = "update outerdatawfdetail set requestid=" + paramString2 + " where mainid=" + paramString3 + " and workflowid=" + paramString1 + " and outermaintable = '" + paramString5 + "' and keyfieldvalue='" + paramString4 + "'";
/*     */       } else {
/* 180 */         str = "insert into outerdatawfdetail(mainid,workflowid,requestid,keyfieldvalue,outermaintable) values(" + paramString3 + "," + paramString1 + "," + paramString2 + ",'" + paramString4 + "','" + paramString5 + "')";
/*     */       } 
/* 182 */       newlog.info("saveTrigLog 记录日志 sql : " + str);
/* 183 */       recordSet.executeSql(str);
/* 184 */     } catch (Exception exception) {
/* 185 */       newlog.error("saveTrigLog 记录日志出错！", exception);
/* 186 */       return false;
/*     */     } 
/* 188 */     return true;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/workflowtrigger/workflow/TriggerLogUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */