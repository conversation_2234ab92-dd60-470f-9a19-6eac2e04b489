/*    */ package weaver.integration.workflowtrigger.workflow;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.general.ThreadVarLanguage;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ public class WorkFlowMsgUtil {
/*  9 */   private static Map msg = new HashMap<>();
/*    */   public WorkFlowMsgUtil() {
/* 11 */     msg.put("-1", "-1：" + SystemEnv.getHtmlLabelName(388847, ThreadVarLanguage.getLang()) + "");
/* 12 */     msg.put("-2", "-2：" + SystemEnv.getHtmlLabelName(10003693, ThreadVarLanguage.getLang()) + "");
/* 13 */     msg.put("-3", "-3：" + SystemEnv.getHtmlLabelName(388847, ThreadVarLanguage.getLang()) + "");
/* 14 */     msg.put("-4", "-4：" + SystemEnv.getHtmlLabelName(388848, ThreadVarLanguage.getLang()) + "");
/* 15 */     msg.put("-5", "-5：" + SystemEnv.getHtmlLabelName(388849, ThreadVarLanguage.getLang()) + "");
/* 16 */     msg.put("-6", "-6：" + SystemEnv.getHtmlLabelName(388850, ThreadVarLanguage.getLang()) + "");
/* 17 */     msg.put("-7", "-7：" + SystemEnv.getHtmlLabelName(388851, ThreadVarLanguage.getLang()) + "");
/* 18 */     msg.put("-8", "-8：" + SystemEnv.getHtmlLabelName(388852, ThreadVarLanguage.getLang()) + "");
/* 19 */     msg.put("-9", "-9：" + SystemEnv.getHtmlLabelName(388853, ThreadVarLanguage.getLang()) + "");
/*    */   }
/*    */   
/*    */   public static String getMsg(String paramString) {
/* 23 */     return Util.null2String(msg.get(paramString));
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/workflowtrigger/workflow/WorkFlowMsgUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */