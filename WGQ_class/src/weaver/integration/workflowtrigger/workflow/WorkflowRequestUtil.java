/*     */ package weaver.integration.workflowtrigger.workflow;
/*     */ 
/*     */ import com.engine.hrm.biz.HrmClassifiedProtectionBiz;
/*     */ import com.engine.integration.biz.IPUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.Util;
/*     */ import weaver.integration.framework.data.RecordData;
/*     */ import weaver.integration.framework.mapping.impl.FieldDataMapping;
/*     */ import weaver.integration.framework.mapping.impl.GeneralDataMapping;
/*     */ import weaver.integration.framework.mapping.impl.RecordDataMapping;
/*     */ import weaver.integration.framework.mapping.impl.TableDataMapping;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ import weaver.integration.workflowtrigger.config.WorkflowTriggerConfig;
/*     */ import weaver.soa.workflow.request.RequestInfo;
/*     */ import weaver.soa.workflow.request.RequestService;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.workflow.request.RequestManageWriteBackAction;
/*     */ import weaver.workflow.webservices.WorkflowBaseInfo;
/*     */ import weaver.workflow.webservices.WorkflowDetailTableInfo;
/*     */ import weaver.workflow.webservices.WorkflowMainTableInfo;
/*     */ import weaver.workflow.webservices.WorkflowRequestInfo;
/*     */ import weaver.workflow.webservices.WorkflowRequestTableField;
/*     */ import weaver.workflow.webservices.WorkflowRequestTableRecord;
/*     */ import weaver.workflow.webservices.WorkflowServiceImpl;
/*     */ import weaver.workflow.workflow.WfForceOver;
/*     */ 
/*     */ public class WorkflowRequestUtil
/*     */ {
/*  32 */   private static Logger newlog = LoggerFactory.getLogger(WorkflowRequestUtil.class);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String createWorkflowRequest(WorkflowTriggerConfig paramWorkflowTriggerConfig, List<GeneralDataMapping> paramList) {
/*  40 */     for (GeneralDataMapping generalDataMapping : paramList) {
/*  41 */       createWorkflowRequest(paramWorkflowTriggerConfig, generalDataMapping);
/*     */     }
/*     */     
/*  44 */     return "-1";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static String createWorkflowRequest(WorkflowTriggerConfig paramWorkflowTriggerConfig, GeneralDataMapping paramGeneralDataMapping) {
/*  56 */     ArrayList<String> arrayList = IPUtil.getRealIp();
/*  57 */     String str1 = "";
/*  58 */     if (arrayList != null && arrayList.size() > 0) {
/*  59 */       str1 = arrayList.get(0);
/*     */     }
/*     */     
/*  62 */     boolean bool1 = BeforeRuleUtil.CheckWhitelist(paramWorkflowTriggerConfig);
/*  63 */     if (!bool1) {
/*  64 */       newlog.info(" 当前机器Ip不在设置的触发白名单内，不进行触发流程 ");
/*  65 */       return "-101";
/*     */     } 
/*  67 */     WorkflowRequestInfo workflowRequestInfo = new WorkflowRequestInfo();
/*  68 */     WorkflowBaseInfo workflowBaseInfo = new WorkflowBaseInfo();
/*  69 */     workflowBaseInfo.setWorkflowId(paramWorkflowTriggerConfig.getWorkflowid());
/*  70 */     String str2 = Util.null2String(paramWorkflowTriggerConfig.getIsnextnode());
/*  71 */     if (str2.equals("2")) {
/*  72 */       workflowRequestInfo.setIsnextflow("0");
/*     */     } else {
/*  74 */       workflowRequestInfo.setIsnextflow("1");
/*     */     } 
/*     */     
/*  77 */     String str3 = "";
/*  78 */     String str4 = "";
/*  79 */     String str5 = "";
/*  80 */     String str6 = Util.null2String(paramWorkflowTriggerConfig.getKeyfield());
/*  81 */     String str7 = "";
/*  82 */     String str8 = "";
/*  83 */     String str9 = Util.null2String(paramWorkflowTriggerConfig.getIsupdatewfdataField());
/*     */ 
/*     */     
/*  86 */     List<RecordDataMapping> list = ((TableDataMapping)paramGeneralDataMapping.getTableDataMappingList().get(0)).getRecordDataMappingList();
/*     */     
/*  88 */     ArrayList<FieldDataMapping> arrayList1 = new ArrayList();
/*  89 */     RecordData recordData = null;
/*  90 */     for (byte b1 = 0; b1 < list.size(); b1++) {
/*  91 */       RecordDataMapping recordDataMapping = list.get(b1);
/*  92 */       recordData = recordDataMapping.getTableData().getRecordDataList().get(b1);
/*  93 */       str4 = Util.null2String(recordData.getFieldData().get(str6.toLowerCase()));
/*  94 */       str5 = Util.null2String(recordData.getFieldData().get(str9.toLowerCase()));
/*  95 */       List<FieldDataMapping> list1 = recordDataMapping.getFieldDataMappingList();
/*  96 */       for (byte b = 0; b < list1.size(); b++) {
/*  97 */         String str = ((FieldDataMapping)list1.get(b)).getContext().getFieldId();
/*  98 */         ((FieldDataMapping)list1.get(b)).getContext().setRecordData(recordData);
/*  99 */         if (str.equals("-2")) {
/* 100 */           str3 = Util.null2String(((FieldDataMapping)list1.get(b)).getTargetField().getFieldValue());
/*     */         
/*     */         }
/* 103 */         else if (str.equals("-1")) {
/* 104 */           str8 = Util.null2String(((FieldDataMapping)list1.get(b)).getTargetField().getFieldValue());
/*     */         }
/* 106 */         else if (str.equals("-3")) {
/* 107 */           str7 = Util.null2String(((FieldDataMapping)list1.get(b)).getTargetField().getFieldValue());
/*     */         } else {
/*     */           
/* 110 */           arrayList1.add(list1.get(b));
/*     */         } 
/*     */       } 
/*     */     } 
/* 114 */     newlog.info("-------------流程触发创建人：" + str3 + " | 触发创建标题：" + str8);
/* 115 */     workflowRequestInfo.setRequestName(str8);
/*     */ 
/*     */     
/* 118 */     boolean bool2 = Util.null2String(paramWorkflowTriggerConfig.getIsalways()).equals("1");
/* 119 */     String str10 = "";
/* 120 */     if (!bool2) {
/* 121 */       str10 = AfterRuleUitl.IscreateWf(paramWorkflowTriggerConfig, str4);
/*     */     }
/* 123 */     boolean bool = (Util.null2String(paramWorkflowTriggerConfig.getIsupdatewfdata()).equalsIgnoreCase("2") && str5.equals("1")) ? true : false;
/*     */     
/* 125 */     if (!bool2 && !bool && !str10.equals("") && Util.getIntValue(str10) > 0) {
/* 126 */       newlog.info("非固定创建流程,未设置更新流程数据，流程已创建,不再重新创建流程！");
/* 127 */       return "-106";
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 133 */     boolean bool3 = AfterRuleUitl.isExistForCreator(str3);
/* 134 */     if (!bool3) {
/*     */ 
/*     */       
/* 137 */       TriggerLogUtil.saveRequestLog2(paramWorkflowTriggerConfig.getId(), paramWorkflowTriggerConfig.getWorkflowid(), str1, "" + SystemEnv.getHtmlLabelName(10003694, ThreadVarLanguage.getLang()) + "OA" + SystemEnv.getHtmlLabelName(10003695, ThreadVarLanguage.getLang()) + "!", 0, 0, "");
/* 138 */       return "-102";
/*     */     } 
/*     */ 
/*     */     
/* 142 */     boolean bool4 = AfterRuleUitl.CheckDetailSetting(paramWorkflowTriggerConfig.getId());
/* 143 */     if (!bool4) {
/*     */       
/* 145 */       TriggerLogUtil.saveRequestLog2(paramWorkflowTriggerConfig.getId(), paramWorkflowTriggerConfig.getWorkflowid(), str1, "" + SystemEnv.getHtmlLabelName(160, ThreadVarLanguage.getLang()) + "MAINID【\" + config.getId() + \"】 " + SystemEnv.getHtmlLabelName(10003696, ThreadVarLanguage.getLang()) + "!", 0, 0, "");
/* 146 */       return "-103";
/*     */     } 
/*     */ 
/*     */     
/* 150 */     if (!"".equals(str7) && !"1".equals(str7) && !"2".equals(str7) && !"3".equals(str7) && !"4".equals(str7)) {
/*     */ 
/*     */       
/* 153 */       TriggerLogUtil.saveRequestLog2(paramWorkflowTriggerConfig.getId() + "", paramWorkflowTriggerConfig.getWorkflowid(), str1, "" + SystemEnv.getHtmlLabelName(10003615, ThreadVarLanguage.getLang()) + "," + SystemEnv.getHtmlLabelName(10003616, ThreadVarLanguage.getLang()) + "", 0, 0, "");
/* 154 */       return "-104";
/*     */     } 
/*     */ 
/*     */     
/* 158 */     if (!"".equals(str7) && !"4".equals(str7) && !"".equals(str3)) {
/* 159 */       HrmClassifiedProtectionBiz hrmClassifiedProtectionBiz = new HrmClassifiedProtectionBiz();
/* 160 */       int i = Util.getIntValue(hrmClassifiedProtectionBiz.getMaxResourceSecLevelById(str3), 4);
/* 161 */       if (i > Util.getIntValue(str7, 4)) {
/*     */         
/* 163 */         TriggerLogUtil.saveRequestLog2(paramWorkflowTriggerConfig.getId() + "", paramWorkflowTriggerConfig.getWorkflowid(), str1, "" + SystemEnv.getHtmlLabelName(10003617, ThreadVarLanguage.getLang()) + "," + SystemEnv.getHtmlLabelName(10003618, ThreadVarLanguage.getLang()) + "," + SystemEnv.getHtmlLabelName(10003616, ThreadVarLanguage.getLang()) + "", 0, 0, "");
/* 164 */         return "-105";
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 171 */     WorkflowMainTableInfo workflowMainTableInfo = new WorkflowMainTableInfo();
/* 172 */     WorkflowRequestTableRecord[] arrayOfWorkflowRequestTableRecord = new WorkflowRequestTableRecord[1];
/* 173 */     WorkflowRequestTableField[] arrayOfWorkflowRequestTableField = new WorkflowRequestTableField[arrayList1.size()];
/*     */     
/* 175 */     for (byte b2 = 0; b2 < arrayList1.size(); b2++) {
/* 176 */       arrayOfWorkflowRequestTableField[b2] = new WorkflowRequestTableField();
/* 177 */       FieldDataMapping fieldDataMapping = arrayList1.get(b2);
/* 178 */       String str12 = fieldDataMapping.getTargetField().getFieldName();
/* 179 */       String str13 = Util.null2String(fieldDataMapping.getTargetField().getFieldValue());
/* 180 */       arrayOfWorkflowRequestTableField[b2].setFieldName(str12);
/* 181 */       arrayOfWorkflowRequestTableField[b2].setFieldValue(str13);
/* 182 */       arrayOfWorkflowRequestTableField[b2].setView(true);
/* 183 */       arrayOfWorkflowRequestTableField[b2].setEdit(true);
/*     */     } 
/* 185 */     arrayOfWorkflowRequestTableRecord[0] = new WorkflowRequestTableRecord();
/* 186 */     arrayOfWorkflowRequestTableRecord[0].setWorkflowRequestTableFields(arrayOfWorkflowRequestTableField);
/* 187 */     workflowMainTableInfo.setRequestRecords(arrayOfWorkflowRequestTableRecord);
/*     */ 
/*     */     
/* 190 */     WorkflowDetailTableInfo[] arrayOfWorkflowDetailTableInfo = null;
/* 191 */     if (paramGeneralDataMapping.getTableDataMappingList().size() > 1) {
/* 192 */       arrayOfWorkflowDetailTableInfo = new WorkflowDetailTableInfo[paramGeneralDataMapping.getTableDataMappingList().size() - 1];
/* 193 */       for (byte b = 1; b < paramGeneralDataMapping.getTableDataMappingList().size(); b++) {
/* 194 */         TableDataMapping tableDataMapping = paramGeneralDataMapping.getTableDataMappingList().get(b);
/* 195 */         int i = tableDataMapping.getRecordDataMappingList().size();
/* 196 */         String str = tableDataMapping.getTargetTableName();
/* 197 */         WorkflowRequestTableRecord[] arrayOfWorkflowRequestTableRecord1 = new WorkflowRequestTableRecord[i];
/* 198 */         for (byte b3 = 0; b3 < tableDataMapping.getRecordDataMappingList().size(); b3++) {
/* 199 */           List<FieldDataMapping> list1 = ((RecordDataMapping)tableDataMapping.getRecordDataMappingList().get(b3)).getFieldDataMappingList();
/* 200 */           WorkflowRequestTableField[] arrayOfWorkflowRequestTableField1 = new WorkflowRequestTableField[list1.size()];
/* 201 */           for (byte b4 = 0; b4 < list1.size(); b4++) {
/* 202 */             arrayOfWorkflowRequestTableField1[b4] = new WorkflowRequestTableField();
/* 203 */             String str12 = ((FieldDataMapping)list1.get(b4)).getTargetField().getFieldName();
/* 204 */             FieldDataMapping fieldDataMapping = list1.get(b4);
/* 205 */             String str13 = Util.null2String(fieldDataMapping.getTargetField().getFieldValue());
/* 206 */             arrayOfWorkflowRequestTableField1[b4].setView(true);
/* 207 */             arrayOfWorkflowRequestTableField1[b4].setEdit(true);
/* 208 */             arrayOfWorkflowRequestTableField1[b4].setFieldName(str12);
/* 209 */             arrayOfWorkflowRequestTableField1[b4].setFieldValue(str13);
/*     */           } 
/* 211 */           arrayOfWorkflowRequestTableRecord1[b3] = new WorkflowRequestTableRecord();
/* 212 */           arrayOfWorkflowRequestTableRecord1[b3].setWorkflowRequestTableFields(arrayOfWorkflowRequestTableField1);
/*     */         } 
/* 214 */         arrayOfWorkflowDetailTableInfo[b - 1] = new WorkflowDetailTableInfo();
/* 215 */         arrayOfWorkflowDetailTableInfo[b - 1].setWorkflowRequestTableRecords(arrayOfWorkflowRequestTableRecord1);
/* 216 */         arrayOfWorkflowDetailTableInfo[b - 1].setTableDBName(str);
/*     */       } 
/*     */     } 
/*     */     
/* 220 */     workflowRequestInfo.setWorkflowBaseInfo(workflowBaseInfo);
/* 221 */     workflowRequestInfo.setWorkflowDetailTableInfos(arrayOfWorkflowDetailTableInfo);
/* 222 */     workflowRequestInfo.setWorkflowMainTableInfo(workflowMainTableInfo);
/*     */ 
/*     */     
/* 225 */     WorkflowServiceImpl workflowServiceImpl = new WorkflowServiceImpl();
/* 226 */     WfForceOver wfForceOver = new WfForceOver();
/* 227 */     RequestManageWriteBackAction requestManageWriteBackAction = new RequestManageWriteBackAction();
/* 228 */     String str11 = "";
/*     */     
/* 230 */     if (bool2 || str10.equals("")) {
/* 231 */       str11 = workflowServiceImpl.doCreateWorkflowRequest(workflowRequestInfo, Integer.parseInt(str3));
/* 232 */       String str12 = paramWorkflowTriggerConfig.getDatarecordtype();
/* 233 */       String str13 = Util.null2String(paramWorkflowTriggerConfig.getWorkflowid());
/* 234 */       String str14 = Util.null2String(paramWorkflowTriggerConfig.getOutermaintable());
/* 235 */       String str15 = paramWorkflowTriggerConfig.getId();
/* 236 */       String str16 = Util.null2String(paramWorkflowTriggerConfig.getDatasourceid());
/* 237 */       String str17 = Util.null2String(paramWorkflowTriggerConfig.getSuccessback());
/* 238 */       String str18 = Util.null2String(paramWorkflowTriggerConfig.getFailback());
/* 239 */       String str19 = Util.null2String(paramWorkflowTriggerConfig.getFTriggerFlag());
/* 240 */       String str20 = Util.null2String(paramWorkflowTriggerConfig.getRequestid());
/* 241 */       String str21 = Util.null2String(paramWorkflowTriggerConfig.getFTriggerFlagValue());
/* 242 */       String str22 = Util.null2String(paramWorkflowTriggerConfig.getIsupdatewfdata());
/* 243 */       newlog.info(" 调用流程接口返回信息  ：" + str11);
/* 244 */       if (Util.getIntValue(str11) > 0) {
/* 245 */         if ("2".equals(str12) || "".equals(str12)) {
/* 246 */           TriggerLogUtil.saveOutTrigLog(str16, str14, str17, str6, str19, str20, str4, str21, str11);
/*     */         }
/* 248 */         if (str22.equals("2") && !str9.equals("")) {
/* 249 */           TriggerLogUtil.saveUpdateWfFlag(str16, str14, str6, str4, str22, str9);
/*     */         }
/* 251 */         TriggerLogUtil.saveTrigLog(str13, str11, str15, str4, str14);
/* 252 */         TriggerLogUtil.saveRequestLog2(paramWorkflowTriggerConfig.getId(), paramWorkflowTriggerConfig.getWorkflowid(), str1, "", 1, Util.getIntValue(str11), str8);
/* 253 */         if (wfForceOver.isOver(Util.getIntValue(str11))) {
/* 254 */           requestManageWriteBackAction.doWriteBack(Util.getIntValue(str11));
/*     */         }
/*     */       } else {
/* 257 */         String str = "";
/* 258 */         WorkFlowMsgUtil workFlowMsgUtil = new WorkFlowMsgUtil();
/* 259 */         str = WorkFlowMsgUtil.getMsg(str11);
/* 260 */         if (str.equals("")) {
/* 261 */           str = "" + SystemEnv.getHtmlLabelName(10003697, ThreadVarLanguage.getLang()) + "";
/*     */         }
/* 263 */         if ("2".equals(str12) || "".equals(str12)) {
/* 264 */           TriggerLogUtil.saveOutTrigLog(str16, str14, str18, str6, str19, str20, str4, str21, str11);
/*     */         }
/* 266 */         TriggerLogUtil.saveRequestLog2(paramWorkflowTriggerConfig.getId() + "", paramWorkflowTriggerConfig.getWorkflowid(), str1, str, 0, 0, "");
/*     */       
/*     */       }
/*     */     
/*     */     }
/* 271 */     else if (Util.getIntValue(str10) > 0) {
/* 272 */       boolean bool5 = AfterRuleUitl.IsCreateNode(str10);
/* 273 */       String str = paramWorkflowTriggerConfig.getUpdateisnextnode();
/*     */       
/* 275 */       if (!bool5) {
/* 276 */         newlog.info("requestid:" + str10 + ",这个请求不在创建节点,触发功能不能更新数据！");
/* 277 */         return "-107";
/*     */       } 
/* 279 */       if (str.equals("1")) {
/* 280 */         String str12 = Util.null2String(paramWorkflowTriggerConfig.getOutermaintable());
/* 281 */         String str13 = Util.null2String(paramWorkflowTriggerConfig.getDatasourceid());
/* 282 */         String str14 = Util.null2String(paramWorkflowTriggerConfig.getIsupdatewfdata());
/* 283 */         String str15 = workflowServiceImpl.submitWorkflowRequest(workflowRequestInfo, Util.getIntValue(str10, 0), Util.getIntValue(str3), "submit", "");
/*     */         
/* 285 */         TriggerLogUtil.saveUpdateWfFlag(str13, str12, str6, str4, str14, str9);
/* 286 */         return str10;
/*     */       } 
/* 288 */       workflowRequestInfo.setRequestId(str10);
/*     */       try {
/* 290 */         String str12 = Util.null2String(paramWorkflowTriggerConfig.getOutermaintable());
/* 291 */         String str13 = Util.null2String(paramWorkflowTriggerConfig.getDatasourceid());
/* 292 */         String str14 = Util.null2String(paramWorkflowTriggerConfig.getIsupdatewfdata());
/* 293 */         RequestInfo requestInfo = workflowServiceImpl.toRequestInfo(workflowRequestInfo);
/* 294 */         RequestService requestService = new RequestService();
/* 295 */         requestService.saveRequest(requestInfo);
/* 296 */         TriggerLogUtil.saveUpdateWfFlag(str13, str12, str6, str4, str14, str9);
/* 297 */         return str10;
/* 298 */       } catch (Exception exception) {
/* 299 */         exception.printStackTrace();
/* 300 */         newlog.info("requestid:" + str10 + ",这个请求触发功能更新数据异常:" + exception);
/* 301 */         return "-108";
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 307 */     return str11;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/workflowtrigger/workflow/WorkflowRequestUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */