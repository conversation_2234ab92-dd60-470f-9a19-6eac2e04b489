/*     */ package weaver.integration.workflowtrigger.workflow;
/*     */ 
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.general.Util;
/*     */ import weaver.integration.framework.converter.IConvert;
/*     */ import weaver.integration.framework.data.RecordData;
/*     */ import weaver.integration.framework.mapping.impl.FieldDataMapping;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class Rulefactory
/*     */ {
/*  22 */   private static Logger newlog = LoggerFactory.getLogger(Rulefactory.class);
/*  23 */   public Map rules = new HashMap<>();
/*     */   
/*     */   public Rulefactory() {
/*  26 */     this.rules.put("1", "weaver.integration.framework.converter.workflow.RequestNameConvertor");
/*  27 */     this.rules.put("2", "weaver.integration.framework.converter.workflow.CreaterConvertor");
/*  28 */     this.rules.put("3", "weaver.integration.framework.converter.workflow.SeclevelConvertor");
/*  29 */     this.rules.put("4", "weaver.integration.framework.converter.workflow.CommonBrowserConvertor");
/*  30 */     this.rules.put("5", "weaver.integration.framework.converter.workflow.FixedValueConvertor");
/*  31 */     this.rules.put("6", "weaver.integration.framework.converter.workflow.Acc4DocIdConvertor");
/*  32 */     this.rules.put("7", "weaver.integration.framework.converter.workflow.CustomSQLConvertor");
/*     */   }
/*     */   
/*     */   public Object convert(FieldDataMapping paramFieldDataMapping) {
/*  36 */     String str1 = Util.null2String(paramFieldDataMapping.getContext().getFieldId());
/*  37 */     String str2 = Util.null2String(paramFieldDataMapping.getContext().getChangeType());
/*  38 */     String str3 = Util.null2String(paramFieldDataMapping.getContext().getFieldHtmltype());
/*  39 */     String str4 = Util.null2String(paramFieldDataMapping.getContext().getCustomsql());
/*  40 */     String str5 = "";
/*     */     
/*  42 */     String str6 = paramFieldDataMapping.getContext().getFieldDBType();
/*     */ 
/*     */     
/*  45 */     if (str1.equals("-2")) {
/*  46 */       str5 = "2";
/*  47 */     } else if (str1.equals("-1")) {
/*  48 */       str5 = "1";
/*  49 */     } else if (str1.equals("-3")) {
/*  50 */       str5 = "3";
/*  51 */     } else if ("7".equals(str2)) {
/*  52 */       str5 = "5";
/*  53 */     } else if (!"6".equals(str2)) {
/*  54 */       if (str3.equals("3")) {
/*  55 */         str5 = "4";
/*  56 */       } else if (str3.equals("6")) {
/*  57 */         str5 = "6";
/*     */       } else {
/*  59 */         String str = paramFieldDataMapping.getSourceField().getFieldName();
/*  60 */         RecordData recordData = paramFieldDataMapping.getContext().getRecordData();
/*  61 */         Map map = recordData.getFieldData();
/*  62 */         return datatimeDelZero(str6, Util.null2String(map.get(str.toLowerCase())));
/*     */       } 
/*  64 */     } else if (!"".equals(str4) && !"7".equals(str2)) {
/*  65 */       str5 = "7";
/*     */     } else {
/*  67 */       String str = paramFieldDataMapping.getSourceField().getFieldName();
/*  68 */       RecordData recordData = paramFieldDataMapping.getContext().getRecordData();
/*  69 */       Map map = recordData.getFieldData();
/*  70 */       return datatimeDelZero(str6, Util.null2String(map.get(str.toLowerCase())));
/*     */     } 
/*     */     
/*  73 */     IConvert iConvert = transFactory(str5);
/*  74 */     Object object = iConvert.convert(paramFieldDataMapping);
/*     */     
/*  76 */     if (!"".equals(str6) && str6 != null && str6.contains("datetime")) {
/*  77 */       object = datatimeDelZero(str6, Util.null2String(object));
/*     */     }
/*  79 */     return object;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String datatimeDelZero(String paramString1, String paramString2) {
/*  90 */     if (!"".equals(paramString1) && paramString1 != null) {
/*  91 */       if (paramString1.contains("(")) {
/*  92 */         paramString1 = paramString1.substring(0, paramString1.indexOf("("));
/*     */       }
/*     */ 
/*     */ 
/*     */       
/*  97 */       if ((paramString1.toLowerCase().contains("datetime") || "date".equals(paramString1.toLowerCase()) || "timestamp".equals(paramString1.toLowerCase())) && paramString2.contains(".")) {
/*  98 */         paramString2 = paramString2.substring(0, paramString2.lastIndexOf("."));
/*     */       }
/*     */     } 
/* 101 */     return paramString2;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public IConvert transFactory(String paramString) {
/*     */     try {
/* 111 */       String str = Util.null2String(this.rules.get(paramString));
/* 112 */       newlog.info("  转换规则工厂 ：转换类： " + str);
/* 113 */       return (IConvert)Class.forName(str).newInstance();
/*     */     }
/* 115 */     catch (Exception exception) {
/* 116 */       newlog.error(exception);
/* 117 */       return null;
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/workflowtrigger/workflow/Rulefactory.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */