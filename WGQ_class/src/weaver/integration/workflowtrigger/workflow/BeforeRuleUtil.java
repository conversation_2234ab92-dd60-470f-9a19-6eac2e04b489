/*    */ package weaver.integration.workflowtrigger.workflow;
/*    */ 
/*    */ import com.engine.integration.biz.IPUtil;
/*    */ import java.util.ArrayList;
/*    */ import weaver.general.ThreadVarLanguage;
/*    */ import weaver.integration.workflowtrigger.config.WorkflowTriggerConfig;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class BeforeRuleUtil
/*    */ {
/*    */   public static boolean CheckWhitelist(WorkflowTriggerConfig paramWorkflowTriggerConfig) {
/* 21 */     boolean bool = false;
/* 22 */     ArrayList<String> arrayList = IPUtil.getRealIp();
/* 23 */     String str1 = paramWorkflowTriggerConfig.getWhitelist();
/* 24 */     String str2 = "";
/* 25 */     if (arrayList != null && arrayList.size() > 0) {
/* 26 */       str2 = arrayList.get(0);
/*    */     }
/*    */     
/* 29 */     if (str1 != null && !"".equals(str1)) {
/* 30 */       str1 = "," + str1 + ",";
/* 31 */       if (arrayList != null && arrayList.size() > 0) {
/* 32 */         str2 = arrayList.get(0);
/* 33 */         for (String str : arrayList) {
/* 34 */           if (str1.indexOf("," + str + ",") >= 0) {
/* 35 */             bool = true;
/*    */             break;
/*    */           } 
/*    */         } 
/*    */       } 
/* 40 */       if (!bool)
/*    */       {
/* 42 */         TriggerLogUtil.saveRequestLog2(paramWorkflowTriggerConfig.getId(), paramWorkflowTriggerConfig.getWorkflowid(), str2, "" + SystemEnv.getHtmlLabelName(21805, ThreadVarLanguage.getLang()) + "IP: " + str2 + " " + SystemEnv.getHtmlLabelName(10003610, ThreadVarLanguage.getLang()) + "IP" + SystemEnv.getHtmlLabelName(10003611, ThreadVarLanguage.getLang()) + "!", 0, 0, "");
/*    */       }
/*    */     } else {
/* 45 */       bool = true;
/*    */     } 
/* 47 */     return bool;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/workflowtrigger/workflow/BeforeRuleUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */