/*     */ package weaver.integration.workflowtrigger.config;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import javax.persistence.Column;
/*     */ import javax.persistence.Table;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @Table(name = "outerdatawfperiodset")
/*     */ public class WorkflowTriggerPeriodConfig
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1822271057009738491L;
/*     */   private String periodvalue;
/*     */   private String scope;
/*     */   private String type;
/*     */   private String val2;
/*     */   private String daytime;
/*     */   private String weekday;
/*     */   private String weektime;
/*     */   private String monthseq;
/*     */   private String monthday;
/*     */   private String monthtime;
/*     */   
/*     */   @Column(name = "periodvalue")
/*     */   public String getPeriodvalue() {
/*  32 */     return this.periodvalue;
/*     */   }
/*     */   
/*     */   public void setPeriodvalue(String paramString) {
/*  36 */     this.periodvalue = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "scope")
/*     */   public String getScope() {
/*  41 */     return this.scope;
/*     */   }
/*     */   
/*     */   public void setScope(String paramString) {
/*  45 */     this.scope = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "type")
/*     */   public String getType() {
/*  50 */     return this.type;
/*     */   }
/*     */   
/*     */   public void setType(String paramString) {
/*  54 */     this.type = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "val2")
/*     */   public String getVal2() {
/*  59 */     return this.val2;
/*     */   }
/*     */   
/*     */   public void setVal2(String paramString) {
/*  63 */     this.val2 = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "daytime")
/*     */   public String getDaytime() {
/*  68 */     return this.daytime;
/*     */   }
/*     */   
/*     */   public void setDaytime(String paramString) {
/*  72 */     this.daytime = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "weekday")
/*     */   public String getWeekday() {
/*  77 */     return this.weekday;
/*     */   }
/*     */   
/*     */   public void setWeekday(String paramString) {
/*  81 */     this.weekday = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "weektime")
/*     */   public String getWeektime() {
/*  86 */     return this.weektime;
/*     */   }
/*     */   
/*     */   public void setWeektime(String paramString) {
/*  90 */     this.weektime = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "monthseq")
/*     */   public String getMonthseq() {
/*  95 */     return this.monthseq;
/*     */   }
/*     */   
/*     */   public void setMonthseq(String paramString) {
/*  99 */     this.monthseq = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "monthday")
/*     */   public String getMonthday() {
/* 104 */     return this.monthday;
/*     */   }
/*     */   
/*     */   public void setMonthday(String paramString) {
/* 108 */     this.monthday = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "monthtime")
/*     */   public String getMonthtime() {
/* 113 */     return this.monthtime;
/*     */   }
/*     */   
/*     */   public void setMonthtime(String paramString) {
/* 117 */     this.monthtime = paramString;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/workflowtrigger/config/WorkflowTriggerPeriodConfig.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */