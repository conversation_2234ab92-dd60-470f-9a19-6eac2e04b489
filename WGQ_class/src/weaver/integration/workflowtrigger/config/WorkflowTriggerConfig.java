/*     */ package weaver.integration.workflowtrigger.config;@Table(name = "outerdatawfset")
/*     */ public class WorkflowTriggerConfig implements Serializable { private List<String> sqlList; private GeneralMappingDefine generalMappingDefine; private static final long serialVersionUID = 1822271057009738491L;
/*     */   private String id;
/*     */   private String setname;
/*     */   private String workflowid;
/*     */   private String outermaintable;
/*     */   private String outermainwhere;
/*     */   private String successback;
/*     */   private String failback;
/*     */   private String outerdetailtables;
/*     */   private String outerdetailwheres;
/*     */   private String datasourceid;
/*     */   private String datarecordtype;
/*     */   private String keyfield;
/*     */   private String requestid;
/*     */   private String FTriggerFlag;
/*     */   private String FTriggerFlagValue;
/*     */   
/*     */   public List<String> getSqlList() {
/*  20 */     return this.sqlList;
/*     */   } private String typename; private String isview; private String CreateDate; private String CreateTime; private String ModifyDate; private String ModifyTime; private String isupdatewfdata; private String isupdatewfdataField; private String isnextnode; private String whitelist; private String isalways; private String formid; private String updateisnextnode; private String source; private String weavercreater; private String datasourcetype; private List<WorkflowTriggerDetailConfig> workflowTriggerDetailConfigs; private WorkflowTriggerPeriodConfig workflowTriggerPeriodConfig;
/*     */   public void setSqlList(List<String> paramList) {
/*  23 */     this.sqlList = paramList;
/*     */   }
/*     */   public GeneralMappingDefine getGeneralMappingDefine() {
/*  26 */     return this.generalMappingDefine;
/*     */   }
/*     */   public void setGeneralMappingDefine(GeneralMappingDefine paramGeneralMappingDefine) {
/*  29 */     this.generalMappingDefine = paramGeneralMappingDefine;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void init() {
/*  37 */     buildSqlList();
/*     */ 
/*     */     
/*  40 */     buildGeneralMappingDefine();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void buildSqlList() {
/*  47 */     ArrayList<String> arrayList = new ArrayList();
/*     */ 
/*     */     
/*  50 */     arrayList.add(buildMainSql());
/*     */ 
/*     */     
/*  53 */     arrayList.addAll(buildDetailSqlList());
/*     */     
/*  55 */     this.sqlList = arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String buildMainSql() {
/*  63 */     return (new WorkflowTriggerInitializer()).buildMainSql(this);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<String> buildDetailSqlList() {
/*  71 */     return (new WorkflowTriggerInitializer()).buildDetailSqlList(this);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void buildGeneralMappingDefine() {
/*  78 */     GeneralMappingDefine generalMappingDefine = (new WorkflowTriggerInitializer()).buildGeneralMappingDefine(this);
/*     */     
/*  80 */     this.generalMappingDefine = generalMappingDefine;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @Column(name = "source")
/*     */   public String getSource() {
/* 138 */     return this.source;
/*     */   }
/*     */   
/*     */   public void setSource(String paramString) {
/* 142 */     this.source = paramString;
/*     */   }
/*     */   
/*     */   @Id
/*     */   @Column(name = "id")
/*     */   public String getId() {
/* 148 */     return this.id;
/*     */   }
/*     */   
/*     */   public void setId(String paramString) {
/* 152 */     this.id = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "setname")
/*     */   public String getSetname() {
/* 157 */     return this.setname;
/*     */   }
/*     */   
/*     */   public void setSetname(String paramString) {
/* 161 */     this.setname = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "workflowid")
/*     */   public String getWorkflowid() {
/* 166 */     return this.workflowid;
/*     */   }
/*     */   
/*     */   public void setWorkflowid(String paramString) {
/* 170 */     this.workflowid = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "outermaintable")
/*     */   public String getOutermaintable() {
/* 175 */     return this.outermaintable;
/*     */   }
/*     */   
/*     */   public void setOutermaintable(String paramString) {
/* 179 */     this.outermaintable = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "outermainwhere")
/*     */   public String getOutermainwhere() {
/* 184 */     return this.outermainwhere;
/*     */   }
/*     */   
/*     */   public void setOutermainwhere(String paramString) {
/* 188 */     this.outermainwhere = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "successback")
/*     */   public String getSuccessback() {
/* 193 */     return this.successback;
/*     */   }
/*     */   
/*     */   public void setSuccessback(String paramString) {
/* 197 */     this.successback = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "failback")
/*     */   public String getFailback() {
/* 202 */     return this.failback;
/*     */   }
/*     */   
/*     */   public void setFailback(String paramString) {
/* 206 */     this.failback = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "outerdetailtables")
/*     */   public String getOuterdetailtables() {
/* 211 */     return this.outerdetailtables;
/*     */   }
/*     */   
/*     */   public void setOuterdetailtables(String paramString) {
/* 215 */     this.outerdetailtables = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "outerdetailwheres")
/*     */   public String getOuterdetailwheres() {
/* 220 */     return this.outerdetailwheres;
/*     */   }
/*     */   
/*     */   public void setOuterdetailwheres(String paramString) {
/* 224 */     this.outerdetailwheres = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "datasourceid")
/*     */   public String getDatasourceid() {
/* 229 */     return this.datasourceid;
/*     */   }
/*     */   
/*     */   public void setDatasourceid(String paramString) {
/* 233 */     this.datasourceid = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "datarecordtype")
/*     */   public String getDatarecordtype() {
/* 238 */     return this.datarecordtype;
/*     */   }
/*     */   
/*     */   public void setDatarecordtype(String paramString) {
/* 242 */     this.datarecordtype = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "keyfield")
/*     */   public String getKeyfield() {
/* 247 */     return this.keyfield;
/*     */   }
/*     */   
/*     */   public void setKeyfield(String paramString) {
/* 251 */     this.keyfield = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "requestid")
/*     */   public String getRequestid() {
/* 256 */     return this.requestid;
/*     */   }
/*     */   
/*     */   public void setRequestid(String paramString) {
/* 260 */     this.requestid = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "FTriggerFlag")
/*     */   public String getFTriggerFlag() {
/* 265 */     return this.FTriggerFlag;
/*     */   }
/*     */   
/*     */   public void setFTriggerFlag(String paramString) {
/* 269 */     this.FTriggerFlag = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "FTriggerFlagValue")
/*     */   public String getFTriggerFlagValue() {
/* 274 */     return this.FTriggerFlagValue;
/*     */   }
/*     */   
/*     */   public void setFTriggerFlagValue(String paramString) {
/* 278 */     this.FTriggerFlagValue = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "typename")
/*     */   public String getTypename() {
/* 283 */     return this.typename;
/*     */   }
/*     */   
/*     */   public void setTypename(String paramString) {
/* 287 */     this.typename = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "isview")
/*     */   public String getIsview() {
/* 292 */     return this.isview;
/*     */   }
/*     */   
/*     */   public void setIsview(String paramString) {
/* 296 */     this.isview = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "CreateDate")
/*     */   public String getCreateDate() {
/* 301 */     return this.CreateDate;
/*     */   }
/*     */   
/*     */   public void setCreateDate(String paramString) {
/* 305 */     this.CreateDate = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "CreateTime")
/*     */   public String getCreateTime() {
/* 310 */     return this.CreateTime;
/*     */   }
/*     */   
/*     */   public void setCreateTime(String paramString) {
/* 314 */     this.CreateTime = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "ModifyDate")
/*     */   public String getModifyDate() {
/* 319 */     return this.ModifyDate;
/*     */   }
/*     */   
/*     */   public void setModifyDate(String paramString) {
/* 323 */     this.ModifyDate = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "ModifyTime")
/*     */   public String getModifyTime() {
/* 328 */     return this.ModifyTime;
/*     */   }
/*     */   
/*     */   public void setModifyTime(String paramString) {
/* 332 */     this.ModifyTime = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "isupdatewfdata")
/*     */   public String getIsupdatewfdata() {
/* 337 */     return this.isupdatewfdata;
/*     */   }
/*     */   
/*     */   public void setIsupdatewfdata(String paramString) {
/* 341 */     this.isupdatewfdata = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "isupdatewfdataField")
/*     */   public String getIsupdatewfdataField() {
/* 346 */     return this.isupdatewfdataField;
/*     */   }
/*     */   
/*     */   public void setIsupdatewfdataField(String paramString) {
/* 350 */     this.isupdatewfdataField = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "isnextnode")
/*     */   public String getIsnextnode() {
/* 355 */     return this.isnextnode;
/*     */   }
/*     */   
/*     */   public void setIsnextnode(String paramString) {
/* 359 */     this.isnextnode = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "whitelist")
/*     */   public String getWhitelist() {
/* 364 */     return this.whitelist;
/*     */   }
/*     */   
/*     */   public void setWhitelist(String paramString) {
/* 368 */     this.whitelist = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "isalways")
/*     */   public String getIsalways() {
/* 373 */     return this.isalways;
/*     */   }
/*     */   
/*     */   public void setIsalways(String paramString) {
/* 377 */     this.isalways = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "formid")
/*     */   public String getFormid() {
/* 382 */     return this.formid;
/*     */   }
/*     */   
/*     */   public void setFormid(String paramString) {
/* 386 */     this.formid = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "updateisnextnode")
/*     */   public String getUpdateisnextnode() {
/* 391 */     return this.updateisnextnode;
/*     */   }
/*     */   
/*     */   public void setUpdateisnextnode(String paramString) {
/* 395 */     this.updateisnextnode = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDatasourcetype() {
/* 403 */     return this.datasourcetype;
/*     */   }
/*     */   
/*     */   public void setDatasourcetype(String paramString) {
/* 407 */     this.datasourcetype = paramString;
/*     */   }
/*     */   
/*     */   public List<WorkflowTriggerDetailConfig> getWorkflowTriggerDetailConfigs() {
/* 411 */     return this.workflowTriggerDetailConfigs;
/*     */   }
/*     */   
/*     */   public void setWorkflowTriggerDetailConfigs(List<WorkflowTriggerDetailConfig> paramList) {
/* 415 */     this.workflowTriggerDetailConfigs = paramList;
/*     */   }
/*     */   
/*     */   public WorkflowTriggerPeriodConfig getWorkflowTriggerPeriodConfig() {
/* 419 */     return this.workflowTriggerPeriodConfig;
/*     */   }
/*     */   
/*     */   public void setWorkflowTriggerPeriodConfig(WorkflowTriggerPeriodConfig paramWorkflowTriggerPeriodConfig) {
/* 423 */     this.workflowTriggerPeriodConfig = paramWorkflowTriggerPeriodConfig;
/*     */   }
/*     */   
/*     */   public String getWeavercreater() {
/* 427 */     return this.weavercreater;
/*     */   }
/*     */   
/*     */   public void setWeavercreater(String paramString) {
/* 431 */     this.weavercreater = paramString;
/*     */   } }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/workflowtrigger/config/WorkflowTriggerConfig.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */