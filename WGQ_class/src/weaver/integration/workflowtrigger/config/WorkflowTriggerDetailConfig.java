/*     */ package weaver.integration.workflowtrigger.config;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import javax.persistence.Column;
/*     */ import javax.persistence.Id;
/*     */ import javax.persistence.Table;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @Table(name = "outerdatawfsetdetail")
/*     */ public class WorkflowTriggerDetailConfig
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1822271057009738491L;
/*     */   private String id;
/*     */   private String mainid;
/*     */   private String wffieldid;
/*     */   private String wffieldname;
/*     */   private String wffieldhtmltype;
/*     */   private String wffieldtype;
/*     */   private String wffielddbtype;
/*     */   private String outerfieldname;
/*     */   private String changetype;
/*     */   private String iswriteback;
/*     */   private String customsql;
/*     */   private String CreateDate;
/*     */   private String CreateTime;
/*     */   private String ModifyDate;
/*     */   private String ModifyTime;
/*     */   private String attachment_type;
/*     */   private String attachment_settings;
/*     */   private String datasourceid;
/*     */   private String ConvertClass;
/*     */   
/*     */   @Id
/*     */   @Column(name = "id")
/*     */   public String getId() {
/*  47 */     return this.id;
/*     */   }
/*     */   
/*     */   public void setId(String paramString) {
/*  51 */     this.id = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "mainid")
/*     */   public String getMainid() {
/*  56 */     return this.mainid;
/*     */   }
/*     */   
/*     */   public void setMainid(String paramString) {
/*  60 */     this.mainid = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "wffieldid")
/*     */   public String getWffieldid() {
/*  65 */     return this.wffieldid;
/*     */   }
/*     */   
/*     */   public void setWffieldid(String paramString) {
/*  69 */     this.wffieldid = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "wffieldname")
/*     */   public String getWffieldname() {
/*  74 */     return this.wffieldname;
/*     */   }
/*     */   
/*     */   public void setWffieldname(String paramString) {
/*  78 */     this.wffieldname = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "wffieldhtmltype")
/*     */   public String getWffieldhtmltype() {
/*  83 */     return this.wffieldhtmltype;
/*     */   }
/*     */   
/*     */   public void setWffieldhtmltype(String paramString) {
/*  87 */     this.wffieldhtmltype = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "wffieldtype")
/*     */   public String getWffieldtype() {
/*  92 */     return this.wffieldtype;
/*     */   }
/*     */   
/*     */   public void setWffieldtype(String paramString) {
/*  96 */     this.wffieldtype = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "wffielddbtype")
/*     */   public String getWffielddbtype() {
/* 101 */     return this.wffielddbtype;
/*     */   }
/*     */   
/*     */   public void setWffielddbtype(String paramString) {
/* 105 */     this.wffielddbtype = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "outerfieldname")
/*     */   public String getOuterfieldname() {
/* 110 */     return this.outerfieldname;
/*     */   }
/*     */   
/*     */   public void setOuterfieldname(String paramString) {
/* 114 */     this.outerfieldname = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "changetype")
/*     */   public String getChangetype() {
/* 119 */     return this.changetype;
/*     */   }
/*     */   
/*     */   public void setChangetype(String paramString) {
/* 123 */     this.changetype = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "iswriteback")
/*     */   public String getIswriteback() {
/* 128 */     return this.iswriteback;
/*     */   }
/*     */   
/*     */   public void setIswriteback(String paramString) {
/* 132 */     this.iswriteback = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "customsql")
/*     */   public String getCustomsql() {
/* 137 */     return this.customsql;
/*     */   }
/*     */   
/*     */   public void setCustomsql(String paramString) {
/* 141 */     this.customsql = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "CreateDate")
/*     */   public String getCreateDate() {
/* 146 */     return this.CreateDate;
/*     */   }
/*     */   
/*     */   public void setCreateDate(String paramString) {
/* 150 */     this.CreateDate = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "CreateTime")
/*     */   public String getCreateTime() {
/* 155 */     return this.CreateTime;
/*     */   }
/*     */   
/*     */   public void setCreateTime(String paramString) {
/* 159 */     this.CreateTime = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "ModifyDate")
/*     */   public String getModifyDate() {
/* 164 */     return this.ModifyDate;
/*     */   }
/*     */   
/*     */   public void setModifyDate(String paramString) {
/* 168 */     this.ModifyDate = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "ModifyTime")
/*     */   public String getModifyTime() {
/* 173 */     return this.ModifyTime;
/*     */   }
/*     */   
/*     */   public void setModifyTime(String paramString) {
/* 177 */     this.ModifyTime = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "attachment_type")
/*     */   public String getAttachment_type() {
/* 182 */     return this.attachment_type;
/*     */   }
/*     */   
/*     */   public void setAttachment_type(String paramString) {
/* 186 */     this.attachment_type = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "attachment_settings")
/*     */   public String getAttachment_settings() {
/* 191 */     return this.attachment_settings;
/*     */   }
/*     */   
/*     */   public void setAttachment_settings(String paramString) {
/* 195 */     this.attachment_settings = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "datasourceid")
/*     */   public String getDatasourceid() {
/* 200 */     return this.datasourceid;
/*     */   }
/*     */   
/*     */   public void setDatasourceid(String paramString) {
/* 204 */     this.datasourceid = paramString;
/*     */   }
/*     */   
/*     */   @Column(name = "ConvertClass")
/*     */   public String getConvertClass() {
/* 209 */     return this.ConvertClass;
/*     */   }
/*     */   public void setConvertClass(String paramString) {
/* 212 */     this.ConvertClass = paramString;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/workflowtrigger/config/WorkflowTriggerDetailConfig.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */