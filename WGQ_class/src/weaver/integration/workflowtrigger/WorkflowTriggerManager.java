/*     */ package weaver.integration.workflowtrigger;
/*     */ 
/*     */ import com.alibaba.druid.sql.PagerUtils;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.concurrent.ConcurrentHashMap;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.conn.RecordSetDataSource;
/*     */ import weaver.general.StaticObj;
/*     */ import weaver.general.Util;
/*     */ import weaver.integration.framework.data.GeneralData;
/*     */ import weaver.integration.framework.mapping.impl.GeneralDataMapping;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ import weaver.integration.page.PageUtil;
/*     */ import weaver.integration.workflowtrigger.config.WorkflowTriggerConfig;
/*     */ import weaver.integration.workflowtrigger.sourcedata.ISourceData;
/*     */ import weaver.integration.workflowtrigger.sourcedata.impl.SourceData4DB;
/*     */ import weaver.integration.workflowtrigger.sourcedata.impl.SourceData4ESB;
/*     */ import weaver.integration.workflowtrigger.targetdata.impl.TargetData4Common;
/*     */ import weaver.integration.workflowtrigger.workflow.WorkflowRequestUtil;
/*     */ import weaver.interfaces.cache.impl.IntegrationCache4WFTrigger;
/*     */ import weaver.interfaces.datasource.DataSource;
/*     */ import weaver.interfaces.workflow.browser.util.SQLHelper;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class WorkflowTriggerManager
/*     */ {
/*     */   public static final String DATASOURCETYPE_DB = "DB";
/*     */   public static final String DATASOURCETYPE_ESB = "ESB";
/*  41 */   private Map<String, ISourceData> sourceDataMap = new HashMap<>();
/*     */   
/*  43 */   public static volatile Map<Integer, Boolean> LockMap = new ConcurrentHashMap<>();
/*  44 */   private String SQL = "select id from outerdatawfset";
/*     */ 
/*     */   
/*     */   public WorkflowTriggerManager() {
/*  48 */     this.sourceDataMap.put("DB", new SourceData4DB());
/*  49 */     this.sourceDataMap.put("ESB", new SourceData4ESB());
/*  50 */     RecordSet recordSet = new RecordSet();
/*  51 */     recordSet.executeQuery(this.SQL, new Object[0]);
/*  52 */     newlog.info(" before 锁集合 ：" + LockMap);
/*  53 */     while (recordSet.next()) {
/*  54 */       int i = recordSet.getInt(1);
/*  55 */       Object object = LockMap.get(Integer.valueOf(i));
/*     */       
/*  57 */       if (object == null) {
/*  58 */         LockMap.put(Integer.valueOf(recordSet.getInt(1)), Boolean.valueOf(true));
/*     */       }
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  66 */   private static Logger newlog = LoggerFactory.getLogger(WorkflowTriggerManager.class);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public synchronized void execute(int paramInt) {
/*  74 */     boolean bool = ((Boolean)LockMap.get(Integer.valueOf(paramInt))).booleanValue();
/*  75 */     newlog.info(" 获取锁（" + paramInt + "）  ：" + bool);
/*  76 */     if (bool) {
/*  77 */       LockMap.put(Integer.valueOf(paramInt), Boolean.valueOf(false));
/*     */       try {
/*  79 */         execute(getConfig(paramInt));
/*     */       }
/*  81 */       catch (Exception exception) {
/*  82 */         exception.printStackTrace();
/*     */       } finally {
/*     */         
/*  85 */         LockMap.put(Integer.valueOf(paramInt), Boolean.valueOf(true));
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void execute(WorkflowTriggerConfig paramWorkflowTriggerConfig) {
/*  99 */     String str1 = paramWorkflowTriggerConfig.getDatasourceid();
/* 100 */     DataSource dataSource = (DataSource)StaticObj.getServiceByFullname("datasource." + str1, DataSource.class);
/* 101 */     String str2 = dataSource.getType();
/* 102 */     SQLHelper sQLHelper = new SQLHelper();
/* 103 */     String str3 = sQLHelper.getDruidDatabaseType(str2);
/* 104 */     List<String> list = paramWorkflowTriggerConfig.getSqlList();
/* 105 */     String str4 = list.get(0);
/* 106 */     char c = 'È';
/* 107 */     String str5 = PagerUtils.count(str4, str3);
/*     */     
/* 109 */     newlog.info(" 获取外部数据总数sql  ：" + str5);
/* 110 */     RecordSetDataSource recordSetDataSource = new RecordSetDataSource(str1);
/* 111 */     recordSetDataSource.executeSql(str5);
/* 112 */     int i = 0;
/* 113 */     if (recordSetDataSource.next()) {
/* 114 */       i = recordSetDataSource.getInt(1);
/*     */     }
/*     */     
/* 117 */     newlog.info(" 获取外部数据总数  ：" + i);
/* 118 */     int j = i % c;
/* 119 */     int k = i / c;
/* 120 */     if (j > 0) {
/* 121 */       k++;
/*     */     }
/*     */     
/* 124 */     newlog.info(" 是否支持物理分页  ：" + PageUtil.isSupportPage(str2));
/* 125 */     if (!PageUtil.isSupportPage(str2)) {
/* 126 */       k = 1;
/*     */     }
/* 128 */     for (byte b = 0; b < k; b++) {
/* 129 */       int m = b * c;
/* 130 */       String str = paramWorkflowTriggerConfig.getDatarecordtype();
/* 131 */       boolean bool = Util.null2String(paramWorkflowTriggerConfig.getIsalways()).equals("1");
/* 132 */       if ("2".equals(str) && !bool) {
/* 133 */         m = 0;
/*     */       }
/*     */ 
/*     */       
/* 137 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 138 */       List<GeneralData> list1 = null;
/* 139 */       List list2 = null;
/* 140 */       Map map = getSourceDataNew(paramWorkflowTriggerConfig, c, m);
/* 141 */       list1 = (List)map.get("generalDataList");
/*     */       
/* 143 */       list2 = (List)map.get("creater");
/*     */ 
/*     */       
/* 146 */       List<GeneralDataMapping> list3 = setTargetData(paramWorkflowTriggerConfig, list1, list2);
/*     */       
/* 148 */       createWorkflowRequest(paramWorkflowTriggerConfig, list3);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public WorkflowTriggerConfig getConfig(int paramInt) {
/* 163 */     IntegrationCache4WFTrigger integrationCache4WFTrigger = new IntegrationCache4WFTrigger();
/* 164 */     Object object = integrationCache4WFTrigger.getCacheByKey("" + paramInt);
/* 165 */     if (object == null) {
/* 166 */       object = integrationCache4WFTrigger.getObjectFromDB("" + paramInt);
/* 167 */       integrationCache4WFTrigger.addCache(paramInt + "", object);
/*     */     } 
/* 169 */     return (WorkflowTriggerConfig)object;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<GeneralData> getSourceData(WorkflowTriggerConfig paramWorkflowTriggerConfig) {
/* 179 */     String str = paramWorkflowTriggerConfig.getSource();
/* 180 */     ISourceData iSourceData = this.sourceDataMap.get(str);
/* 181 */     return iSourceData.getSourceData(paramWorkflowTriggerConfig);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map getSourceDataNew(WorkflowTriggerConfig paramWorkflowTriggerConfig) {
/* 192 */     String str = paramWorkflowTriggerConfig.getSource();
/* 193 */     ISourceData iSourceData = this.sourceDataMap.get(str);
/* 194 */     return iSourceData.getSourceDataNew(paramWorkflowTriggerConfig);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map getSourceDataNew(WorkflowTriggerConfig paramWorkflowTriggerConfig, int paramInt1, int paramInt2) {
/* 206 */     String str = paramWorkflowTriggerConfig.getSource();
/* 207 */     ISourceData iSourceData = this.sourceDataMap.get(str);
/* 208 */     return iSourceData.getSourceDataNew(paramWorkflowTriggerConfig, paramInt1, paramInt2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<GeneralDataMapping> setTargetData(WorkflowTriggerConfig paramWorkflowTriggerConfig, List<GeneralData> paramList) {
/* 222 */     TargetData4Common targetData4Common = new TargetData4Common();
/* 223 */     return targetData4Common.setTargetData(paramWorkflowTriggerConfig, paramList);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<GeneralDataMapping> setTargetData(WorkflowTriggerConfig paramWorkflowTriggerConfig, List<GeneralData> paramList, String paramString) {
/* 236 */     TargetData4Common targetData4Common = new TargetData4Common();
/* 237 */     return targetData4Common.setTargetData(paramWorkflowTriggerConfig, paramList, paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<GeneralDataMapping> setTargetData(WorkflowTriggerConfig paramWorkflowTriggerConfig, List<GeneralData> paramList, List paramList1) {
/* 251 */     TargetData4Common targetData4Common = new TargetData4Common();
/* 252 */     return targetData4Common.setTargetData(paramWorkflowTriggerConfig, paramList, paramList1);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String createWorkflowRequest(WorkflowTriggerConfig paramWorkflowTriggerConfig, List<GeneralDataMapping> paramList) {
/* 289 */     return WorkflowRequestUtil.createWorkflowRequest(paramWorkflowTriggerConfig, paramList);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/workflowtrigger/WorkflowTriggerManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */