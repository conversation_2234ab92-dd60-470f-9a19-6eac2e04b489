/*     */ package weaver.integration.workflowtrigger.biz;
/*     */ 
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSetDataSource;
/*     */ import weaver.general.Util;
/*     */ import weaver.integration.framework.converter.IConvert;
/*     */ import weaver.integration.framework.converter.common.CommonConvert;
/*     */ import weaver.integration.framework.converter.workflow.Acc4DocIdConvertor;
/*     */ import weaver.integration.framework.converter.workflow.CommonBrowserConvertor;
/*     */ import weaver.integration.framework.converter.workflow.CreaterConvertor;
/*     */ import weaver.integration.framework.converter.workflow.CustomSQLConvertor;
/*     */ import weaver.integration.framework.converter.workflow.FixedValueConvertor;
/*     */ import weaver.integration.framework.converter.workflow.RequestNameConvertor;
/*     */ import weaver.integration.framework.converter.workflow.SeclevelConvertor;
/*     */ import weaver.integration.framework.data.RecordData;
/*     */ import weaver.integration.framework.mapping.impl.FieldDataMapping;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ import weaver.integration.workflowtrigger.config.WorkflowTriggerConfig;
/*     */ import weaver.integration.workflowtrigger.config.WorkflowTriggerDetailConfig;
/*     */ import weaver.interfaces.cache.impl.IntegrationCache4WFTrigger;
/*     */ 
/*     */ @Deprecated
/*     */ public class WorkflowTriggerTransformer
/*     */   implements IConvert
/*     */ {
/*     */   public Object convert(Object paramObject) {
/*  29 */     return paramObject;
/*     */   }
/*     */ 
/*     */   
/*     */   public Object convert(Object paramObject, RecordData paramRecordData) {
/*  34 */     FieldDataMapping fieldDataMapping = (FieldDataMapping)paramObject;
/*  35 */     Object object1 = fieldDataMapping.getSourceField().getFieldValue();
/*  36 */     Object object2 = null;
/*  37 */     if (object1 instanceof String) {
/*  38 */       object2 = (new CommonConvert()).convert(object1);
/*     */     } else {
/*  40 */       Object object = object1;
/*     */     } 
/*  42 */     if (fieldDataMapping.getBo() != null) {
/*     */       
/*  44 */       WorkflowTriggerBO workflowTriggerBO = (WorkflowTriggerBO)fieldDataMapping.getBo();
/*  45 */       WorkflowTriggerDetailConfig workflowTriggerDetailConfig = workflowTriggerBO.getWorkflowTriggerDetailConfig();
/*     */       
/*  47 */       String str1 = Util.null2String(workflowTriggerDetailConfig.getWffieldid());
/*     */       
/*  49 */       String str2 = Util.null2String(workflowTriggerDetailConfig.getChangetype());
/*     */       
/*  51 */       String str3 = Util.null2String(workflowTriggerDetailConfig.getWffieldhtmltype());
/*  52 */       String str4 = Util.null2String(workflowTriggerDetailConfig.getWffieldname());
/*     */       
/*  54 */       String str5 = Util.null2String(workflowTriggerDetailConfig.getCustomsql());
/*  55 */       String str6 = Util.null2String(workflowTriggerDetailConfig.getOuterfieldname());
/*  56 */       String str7 = Util.null2String(workflowTriggerDetailConfig.getWffieldtype());
/*  57 */       String str8 = Util.null2String(workflowTriggerDetailConfig.getWffielddbtype());
/*  58 */       String str9 = Util.null2String(workflowTriggerDetailConfig.getAttachment_settings());
/*     */ 
/*     */       
/*  61 */       IntegrationCache4WFTrigger integrationCache4WFTrigger = new IntegrationCache4WFTrigger();
/*  62 */       String str10 = Util.null2String(workflowTriggerDetailConfig.getMainid());
/*  63 */       WorkflowTriggerConfig workflowTriggerConfig = (WorkflowTriggerConfig)integrationCache4WFTrigger.getCacheByKey(str10);
/*  64 */       String str11 = Util.null2String(workflowTriggerConfig.getWorkflowid());
/*  65 */       String str12 = Util.null2String(workflowTriggerConfig.getDatasourcetype());
/*     */ 
/*     */ 
/*     */       
/*  69 */       Map map1 = paramRecordData.getFieldData();
/*     */ 
/*     */ 
/*     */       
/*  73 */       RecordSetDataSource recordSetDataSource = new RecordSetDataSource();
/*  74 */       Map map2 = new HashMap<>();
/*  75 */       String str13 = workflowTriggerConfig.getDatasourceid();
/*  76 */       String str14 = workflowTriggerConfig.getOutermaintable();
/*  77 */       if (!str13.equals("")) {
/*  78 */         map2 = recordSetDataSource.getAllColumnWithTypes(str13, str14);
/*     */       }
/*     */ 
/*     */       
/*  82 */       String str15 = workflowTriggerBO.getCreater();
/*  83 */       if (str1.equals("-2")) {
/*  84 */         Object object = (new CreaterConvertor()).convert(fieldDataMapping);
/*  85 */       } else if (str1.equals("-1")) {
/*  86 */         Object object = (new RequestNameConvertor()).convert(fieldDataMapping);
/*  87 */       } else if (str1.equals("-3")) {
/*  88 */         Object object = (new SeclevelConvertor()).convert(fieldDataMapping);
/*     */       
/*     */       }
/*  91 */       else if (!str6.equals("") || ("7".equals(str2) && !"".equals(str5))) {
/*  92 */         String str = "";
/*  93 */         if (!str6.equals("")) {
/*  94 */           str6 = str6.substring(str6.indexOf(".") + 1);
/*  95 */           str = Util.null2String(map2.get(str6.toLowerCase()));
/*     */         } 
/*  97 */         if (str.contains("(")) {
/*  98 */           str = str.substring(0, str.indexOf("("));
/*     */         }
/* 100 */         if (str.equals("number") || str.equals("NUMBER") || str.equals("float") || str.equals("Real") || str.equals("real")) {
/* 101 */           object2 = Util.null2String(map1.get(str6.toLowerCase()));
/* 102 */           if (object2.startsWith(".")) {
/* 103 */             object2 = "0" + object2;
/*     */           }
/* 105 */           if (object2.startsWith("-.")) {
/* 106 */             object2 = object2.replaceFirst("-.", "-0.");
/*     */           }
/* 108 */         } else if ("7".equals(str2) || "6".equals(str3)) {
/*     */ 
/*     */           
/* 111 */           if ("7".equals(str2))
/*     */           {
/* 113 */             Object object = (new FixedValueConvertor()).convert(fieldDataMapping);
/*     */           }
/*     */         } else {
/*     */           
/* 117 */           object2 = Util.null2String(map1.get(str6.toLowerCase()));
/*     */         } 
/* 119 */         if (!"6".equals(str2) && !"7".equals(str2)) {
/* 120 */           if (str3.equals("3")) {
/* 121 */             Object object = (new CommonBrowserConvertor()).convert(fieldDataMapping);
/* 122 */           } else if (str3.equals("6")) {
/* 123 */             Object object = (new Acc4DocIdConvertor()).convert(fieldDataMapping);
/*     */           }
/*     */         
/*     */         }
/* 127 */         else if (!"".equals(str5) && 
/* 128 */           !"7".equals(str2)) {
/*     */ 
/*     */ 
/*     */ 
/*     */           
/* 133 */           object2 = (new CustomSQLConvertor()).convert(fieldDataMapping);
/*     */         } 
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 148 */     return object2;
/*     */   }
/*     */   
/* 151 */   private Logger newlog = LoggerFactory.getLogger(WorkflowTriggerTransformer.class);
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/workflowtrigger/biz/WorkflowTriggerTransformer.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */