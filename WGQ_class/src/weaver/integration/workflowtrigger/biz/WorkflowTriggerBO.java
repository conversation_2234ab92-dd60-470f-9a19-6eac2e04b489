/*    */ package weaver.integration.workflowtrigger.biz;
/*    */ 
/*    */ import java.util.List;
/*    */ import weaver.integration.framework.data.GeneralData;
/*    */ import weaver.integration.framework.data.TableData;
/*    */ import weaver.integration.workflowtrigger.config.WorkflowTriggerDetailConfig;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @Deprecated
/*    */ public class WorkflowTriggerBO
/*    */ {
/*    */   private WorkflowTriggerDetailConfig workflowTriggerDetailConfig;
/*    */   private String fieldName;
/*    */   private String fieldId;
/*    */   private String fieldValue;
/*    */   private String fieldHtmlType;
/*    */   private String fieldType;
/*    */   private String creater;
/*    */   private List<GeneralData> generalDataList;
/*    */   private TableData tableData;
/*    */   
/*    */   public String getCreater() {
/* 26 */     return this.creater;
/*    */   }
/*    */   
/*    */   public void setCreater(String paramString) {
/* 30 */     this.creater = paramString;
/*    */   }
/*    */   
/*    */   public TableData getTableData() {
/* 34 */     return this.tableData;
/*    */   }
/*    */   
/*    */   public void setTableData(TableData paramTableData) {
/* 38 */     this.tableData = paramTableData;
/*    */   }
/*    */   
/*    */   public List<GeneralData> getGeneralDataList() {
/* 42 */     return this.generalDataList;
/*    */   }
/*    */   
/*    */   public void setGeneralDataList(List<GeneralData> paramList) {
/* 46 */     this.generalDataList = paramList;
/*    */   }
/*    */   
/*    */   public String getFieldHtmlType() {
/* 50 */     return this.fieldHtmlType;
/*    */   }
/*    */   
/*    */   public void setFieldHtmlType(String paramString) {
/* 54 */     this.fieldHtmlType = paramString;
/*    */   }
/*    */   
/*    */   public String getFieldType() {
/* 58 */     return this.fieldType;
/*    */   }
/*    */   
/*    */   public void setFieldType(String paramString) {
/* 62 */     this.fieldType = paramString;
/*    */   }
/*    */   
/*    */   public WorkflowTriggerDetailConfig getWorkflowTriggerDetailConfig() {
/* 66 */     return this.workflowTriggerDetailConfig;
/*    */   }
/*    */   
/*    */   public void setWorkflowTriggerDetailConfig(WorkflowTriggerDetailConfig paramWorkflowTriggerDetailConfig) {
/* 70 */     this.workflowTriggerDetailConfig = paramWorkflowTriggerDetailConfig;
/*    */   }
/*    */   
/*    */   public String getFieldName() {
/* 74 */     return this.fieldName;
/*    */   }
/*    */   
/*    */   public void setFieldName(String paramString) {
/* 78 */     this.fieldName = paramString;
/*    */   }
/*    */   
/*    */   public String getFieldId() {
/* 82 */     return this.fieldId;
/*    */   }
/*    */   
/*    */   public void setFieldId(String paramString) {
/* 86 */     this.fieldId = paramString;
/*    */   }
/*    */   
/*    */   public String getFieldValue() {
/* 90 */     return this.fieldValue;
/*    */   }
/*    */   
/*    */   public void setFieldValue(String paramString) {
/* 94 */     this.fieldValue = paramString;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/workflowtrigger/biz/WorkflowTriggerBO.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */