/*     */ package weaver.integration.workflowtrigger.biz;
/*     */ 
/*     */ import com.alibaba.druid.sql.SQLUtils;
/*     */ import java.sql.Connection;
/*     */ import java.sql.DatabaseMetaData;
/*     */ import java.sql.SQLException;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.TreeMap;
/*     */ import java.util.stream.Collectors;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.conn.RecordSetDataSource;
/*     */ import weaver.general.StaticObj;
/*     */ import weaver.general.Util;
/*     */ import weaver.integration.framework.Context;
/*     */ import weaver.integration.framework.data.field.FieldData;
/*     */ import weaver.integration.framework.mappingdefine.impl.FieldMappingDefine;
/*     */ import weaver.integration.framework.mappingdefine.impl.GeneralMappingDefine;
/*     */ import weaver.integration.framework.mappingdefine.impl.TableMappingDefine;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ import weaver.integration.workflowtrigger.config.WorkflowTriggerConfig;
/*     */ import weaver.integration.workflowtrigger.config.WorkflowTriggerDetailConfig;
/*     */ import weaver.interfaces.cache.impl.IntegrationCache4DataSource;
/*     */ import weaver.interfaces.datasource.DataSource;
/*     */ import weaver.interfaces.workflow.browser.BrowserForE8.Util.ParseSqlUtilE8;
/*     */ import weaver.workflow.workflow.WorkflowComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class WorkflowTriggerInitializer
/*     */ {
/*  42 */   private static Logger newlog = LoggerFactory.getLogger(WorkflowTriggerInitializer.class);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String buildMainSql(WorkflowTriggerConfig paramWorkflowTriggerConfig) {
/*  49 */     String str1 = "";
/*  50 */     String str2 = paramWorkflowTriggerConfig.getDatasourceid();
/*  51 */     IntegrationCache4DataSource integrationCache4DataSource = new IntegrationCache4DataSource();
/*  52 */     DataSource dataSource = (DataSource)integrationCache4DataSource.getCacheByKey(str2);
/*  53 */     if (dataSource != null) {
/*  54 */       Connection connection = null;
/*     */       try {
/*  56 */         connection = dataSource.getConnection();
/*  57 */         DatabaseMetaData databaseMetaData = connection.getMetaData();
/*  58 */         String str3 = databaseMetaData.getURL();
/*     */ 
/*     */         
/*  61 */         String str4 = "";
/*  62 */         if (str3.indexOf("oracle") > -1) {
/*  63 */           str4 = "oracle";
/*  64 */         } else if (str3.indexOf("mysql") > -1) {
/*  65 */           str4 = "mysql";
/*  66 */         } else if (str3.indexOf("dm") > -1) {
/*  67 */           str4 = "dm";
/*  68 */         } else if (str3.indexOf("oscar") > -1) {
/*  69 */           str4 = "st";
/*  70 */         } else if (str3.indexOf("kingbase") > -1) {
/*  71 */           str4 = "jc";
/*  72 */         } else if (str3.indexOf("zenith") > -1) {
/*  73 */           str4 = "gs";
/*  74 */         } else if (str3.indexOf("gbase") > -1) {
/*  75 */           str4 = "mysql";
/*  76 */         } else if (str3.indexOf("gbase") > -1) {
/*  77 */           str4 = "mysql";
/*  78 */         } else if (str3.indexOf("postgresql") > -1) {
/*  79 */           str4 = "postgresql";
/*     */         } else {
/*  81 */           str4 = "sqlserver";
/*     */         } 
/*     */         
/*  84 */         this.logger.info(" 数据源dbtype ：" + str4);
/*  85 */         paramWorkflowTriggerConfig.setDatasourcetype(str4);
/*  86 */         String str5 = Util.null2String(paramWorkflowTriggerConfig.getOutermainwhere());
/*  87 */         boolean bool = "1".equals(paramWorkflowTriggerConfig.getIsalways());
/*  88 */         String str6 = Util.null2String(paramWorkflowTriggerConfig.getDatarecordtype());
/*  89 */         String str7 = Util.null2String(paramWorkflowTriggerConfig.getRequestid());
/*  90 */         String str8 = Util.null2String(paramWorkflowTriggerConfig.getFTriggerFlag());
/*  91 */         String str9 = Util.null2String(paramWorkflowTriggerConfig.getFTriggerFlagValue());
/*  92 */         String str10 = Util.null2String(paramWorkflowTriggerConfig.getIsupdatewfdata());
/*  93 */         String str11 = Util.null2String(paramWorkflowTriggerConfig.getIsupdatewfdataField());
/*  94 */         String str12 = "";
/*  95 */         if (str5.equals("")) {
/*  96 */           str5 = " where 1=1  ";
/*     */         } else {
/*  98 */           ParseSqlUtilE8 parseSqlUtilE8 = new ParseSqlUtilE8(null);
/*  99 */           str5 = SQLUtils.format("select * from tablename " + str5, "postgresql");
/* 100 */           str12 = parseSqlUtilE8.parserOrder(str5);
/* 101 */           if (!str12.trim().equals("")) {
/* 102 */             str5 = str5.replace(str12.replaceFirst(" ", ""), " ");
/*     */           }
/*     */           
/* 105 */           str5 = str5.replace("select * from tablename ", " ");
/* 106 */           if (str5.indexOf("WHERE") > -1) {
/* 107 */             str5 = str5.substring(str5.indexOf("WHERE"));
/*     */           }
/* 109 */           else if (str5.indexOf("where") > -1) {
/* 110 */             str5 = str5.substring(str5.indexOf("where"));
/*     */           } 
/*     */         } 
/*     */ 
/*     */         
/* 115 */         if (!bool && ("2".equals(str6) || "".equals(str6))) {
/* 116 */           str5 = str5 + " and ((1=1 ";
/*     */           
/* 118 */           if (str4.toLowerCase().indexOf("gs") > -1) {
/* 119 */             if (!"".equals(str7))
/* 120 */               str5 = str5 + " and " + str7 + " is null "; 
/* 121 */             if (!"".equals(str8) && !"".equals(str9))
/* 122 */               str5 = str5 + " and ( " + str8 + " is null or " + str8 + "!='" + str9 + "' ) "; 
/* 123 */           } else if (str4.toLowerCase().indexOf("postgresql") > -1) {
/* 124 */             if (!"".equals(str7))
/* 125 */               str5 = str5 + " and coalesce(" + str7 + ",'0')='0' "; 
/* 126 */             if (!"".equals(str8) && !"".equals(str9))
/* 127 */               str5 = str5 + " and coalesce(" + str8 + ",'0')!='" + str9 + "' "; 
/* 128 */           } else if (str4.toLowerCase().indexOf("oracle") > -1 || str4.toLowerCase().indexOf("jc") > -1 || str4.toLowerCase().indexOf("dm") > -1 || str4.toLowerCase().indexOf("st") > -1) {
/* 129 */             if (!"".equals(str7))
/* 130 */               str5 = str5 + " and nvl(" + str7 + ",'0')='0' "; 
/* 131 */             if (!"".equals(str8) && !"".equals(str9))
/* 132 */               str5 = str5 + " and nvl(" + str8 + ",'0')!='" + str9 + "' "; 
/* 133 */           } else if (str4.toLowerCase().indexOf("sqlserver") > -1 || str4.toLowerCase().indexOf("sybase") > -1) {
/* 134 */             if (!"".equals(str7))
/* 135 */               str5 = str5 + " and isnull(" + str7 + ",'0')='0' "; 
/* 136 */             if (!"".equals(str8) && !"".equals(str9))
/* 137 */               str5 = str5 + " and isnull(" + str8 + ",'0')!='" + str9 + "' "; 
/* 138 */           } else if (str4.toLowerCase().indexOf("informix") > -1) {
/* 139 */             if (!"".equals(str7))
/* 140 */               str5 = str5 + " and " + str7 + " is null "; 
/* 141 */             if (!"".equals(str8) && !"".equals(str9))
/* 142 */               str5 = str5 + " and " + str8 + "!='" + str9 + "' "; 
/* 143 */           } else if (str4.toLowerCase().indexOf("mysql") > -1) {
/* 144 */             if (!"".equals(str7))
/* 145 */               str5 = str5 + " and IFNULL(" + str7 + ",'0')='0' "; 
/* 146 */             if (!"".equals(str8) && !"".equals(str9))
/* 147 */               str5 = str5 + " and IFNULL(" + str8 + ",'0')!='" + str9 + "' "; 
/* 148 */           } else if (str4.toLowerCase().indexOf("db2") > -1) {
/* 149 */             if (!"".equals(str7))
/* 150 */               str5 = str5 + " and coalesce(" + str7 + ",'0')='0' "; 
/* 151 */             if (!"".equals(str8) && !"".equals(str9))
/* 152 */               str5 = str5 + " and coalesce(" + str8 + ",'0')!='" + str9 + "' "; 
/*     */           } else {
/* 154 */             if (!"".equals(str7))
/* 155 */               str5 = str5 + " and " + str7 + " is null "; 
/* 156 */             if (!"".equals(str8) && !"".equals(str9))
/* 157 */               str5 = str5 + " and " + str8 + "!='" + str9 + "' "; 
/*     */           } 
/* 159 */           str5 = str5 + " ) ";
/* 160 */           if (str10.equals("2") && !str11.equals("")) {
/* 161 */             str5 = str5 + " or " + str11 + "='1' ";
/*     */           }
/* 163 */           str5 = str5 + " ) ";
/*     */         } 
/* 165 */         str1 = "select * from " + paramWorkflowTriggerConfig.getOutermaintable() + " " + str5;
/* 166 */       } catch (SQLException sQLException) {
/* 167 */         str1 = "";
/* 168 */         sQLException.printStackTrace();
/* 169 */         this.logger.error("===================buildMainSql error occured!!!" + sQLException.getMessage());
/*     */       } finally {
/* 171 */         if (connection != null) {
/*     */           try {
/* 173 */             connection.close();
/* 174 */           } catch (SQLException sQLException) {
/* 175 */             sQLException.printStackTrace();
/*     */           } 
/*     */         }
/*     */       } 
/*     */     } 
/*     */     
/* 181 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<String> buildDetailSqlList(WorkflowTriggerConfig paramWorkflowTriggerConfig) {
/* 190 */     ArrayList<String> arrayList1 = new ArrayList();
/* 191 */     String str1 = paramWorkflowTriggerConfig.getOuterdetailtables();
/* 192 */     String str2 = paramWorkflowTriggerConfig.getOuterdetailwheres();
/* 193 */     String str3 = paramWorkflowTriggerConfig.getDatarecordtype();
/* 194 */     String str4 = paramWorkflowTriggerConfig.getRequestid();
/* 195 */     String str5 = paramWorkflowTriggerConfig.getOutermaintable();
/* 196 */     String str6 = paramWorkflowTriggerConfig.getKeyfield();
/* 197 */     ArrayList<String> arrayList2 = Util.TokenizerString(str1, ",");
/* 198 */     if (str2 == null || "".equals(str2)) {
/* 199 */       return arrayList1;
/*     */     }
/* 201 */     String[] arrayOfString = str2.split("\\$@\\|@\\$");
/* 202 */     ArrayList<String> arrayList3 = new ArrayList(); byte b;
/* 203 */     for (b = 0; b < arrayOfString.length; b++) {
/* 204 */       arrayList3.add(arrayOfString[b]);
/*     */     }
/* 206 */     if (arrayList2 != null && str2 != null && arrayList2.size() == arrayList3.size()) {
/*     */       
/* 208 */       for (b = 0; b < arrayList2.size(); b++) {
/* 209 */         String str = arrayList2.get(b);
/* 210 */         if ("-".equals(str)) {
/* 211 */           arrayList1.add("");
/*     */         } else {
/*     */           
/* 214 */           String str7 = Util.null2String(arrayList3.get(b));
/* 215 */           if (str7.equals("-")) {
/* 216 */             str7 = "";
/*     */           }
/* 218 */           str7 = str7.replaceFirst("where", " and ");
/* 219 */           str7 = str7.replaceFirst("WHERE", " and ");
/* 220 */           str7 = str7.replaceFirst("Where", " and ");
/* 221 */           str7 = str7.replaceFirst("WHere", " and ");
/*     */ 
/*     */ 
/*     */           
/* 225 */           String str8 = "";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */           
/* 232 */           str8 = "select * from " + str + " ," + str5 + "  where 1=1 and " + str5 + "." + str6 + " = '${keyid}'" + str7;
/* 233 */           arrayList1.add(str8);
/*     */         } 
/*     */       } 
/* 236 */     } else if (arrayList2 != null && arrayList2.size() > 0) {
/* 237 */       this.logger.error("==================外部明细表配置有错误,请检查!!!");
/*     */     } 
/*     */     
/* 240 */     return arrayList1;
/*     */   }
/*     */ 
/*     */   
/*     */   public GeneralMappingDefine buildGeneralMappingDefine(WorkflowTriggerConfig paramWorkflowTriggerConfig) {
/* 245 */     GeneralMappingDefine generalMappingDefine = new GeneralMappingDefine();
/* 246 */     ArrayList arrayList = new ArrayList();
/* 247 */     String str1 = paramWorkflowTriggerConfig.getOutermaintable();
/* 248 */     String str2 = paramWorkflowTriggerConfig.getOuterdetailtables();
/* 249 */     String str3 = paramWorkflowTriggerConfig.getDatasourceid();
/*     */     
/* 251 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 252 */     Map map = getColumnWithTypes(str3, str1);
/* 253 */     hashMap.put(str1, map);
/* 254 */     if (str2 != null) {
/* 255 */       String[] arrayOfString = str2.split(",");
/* 256 */       if (arrayOfString != null) {
/* 257 */         for (String str : arrayOfString) {
/* 258 */           if (!str.equals("-")) {
/* 259 */             Map map2 = getColumnWithTypes(str3, str);
/* 260 */             hashMap.put(str, map2);
/*     */           } 
/*     */         } 
/*     */       }
/*     */     } 
/*     */ 
/*     */     
/* 267 */     String str4 = paramWorkflowTriggerConfig.getWorkflowid();
/* 268 */     List list = paramWorkflowTriggerConfig.getWorkflowTriggerDetailConfigs();
/*     */     
/* 270 */     Map<String, WorkflowTriggerDetailConfig> map1 = (Map)list.stream().collect(Collectors.toMap(WorkflowTriggerDetailConfig::getWffieldid, paramWorkflowTriggerDetailConfig -> paramWorkflowTriggerDetailConfig));
/*     */     
/* 272 */     WorkflowComInfo workflowComInfo = new WorkflowComInfo();
/* 273 */     RecordSet recordSet = new RecordSet();
/* 274 */     String str5 = "";
/* 275 */     int i = Util.getIntValue(Util.null2String(workflowComInfo.getIsBill(str4)), -1);
/* 276 */     String str6 = Util.null2String(workflowComInfo.getFormId(str4));
/* 277 */     TreeMap<Object, Object> treeMap = new TreeMap<>();
/*     */     
/* 279 */     TableMappingDefine tableMappingDefine = null;
/* 280 */     ArrayList<FieldMappingDefine> arrayList1 = null;
/* 281 */     if (i == 1) {
/* 282 */       String str = "SELECT * FROM Workflow_billdetailtable where billid=? order by id";
/* 283 */       recordSet.executeQuery(str, new Object[] { str6 });
/* 284 */       this.logger.info("=1=================sql2：" + str + "    formid:" + str6);
/* 285 */       while (recordSet.next()) {
/* 286 */         tableMappingDefine = new TableMappingDefine();
/* 287 */         arrayList1 = new ArrayList();
/* 288 */         tableMappingDefine.setSourceTableName(recordSet.getString("tablename"));
/* 289 */         this.logger.info("=======1===========SourceTableName：" + recordSet.getString("tablename"));
/* 290 */         tableMappingDefine.setTargetTableName(recordSet.getString("tablename"));
/* 291 */         tableMappingDefine.setTableOrder(recordSet.getInt("orderid"));
/* 292 */         tableMappingDefine.setFieldMappingDefineList(arrayList1);
/* 293 */         treeMap.put(Integer.valueOf(recordSet.getInt("orderid")), tableMappingDefine);
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 299 */     buildGeneralSpecialMappingDefine((Map)treeMap, map1, paramWorkflowTriggerConfig);
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 304 */     if (i == 0) {
/* 305 */       str5 = "select workflow_formfield.fieldid as id,fieldname as name,workflow_fieldlable.fieldlable as label,workflow_formdict.fieldhtmltype as htmltype,workflow_formdict.type as type,workflow_formdict.fielddbtype from workflow_formfield,workflow_formdict,workflow_fieldlable where workflow_fieldlable.formid = workflow_formfield.formid and workflow_fieldlable.isdefault = 1 and workflow_fieldlable.fieldid =workflow_formfield.fieldid and workflow_formdict.id = workflow_formfield.fieldid and workflow_formfield.formid=" + str6;
/* 306 */     } else if (i == 1) {
/*     */       
/* 308 */       str5 = "SELECT * FROM (\nselect t1.id as id,0 AS detailtable_id,t1.fieldname as name,t1.fieldlabel as label,t1.fieldhtmltype as htmltype,t1.type as type,t1.fielddbtype,t1.detailtable \nfrom workflow_billfield t1 WHERE billid=" + str6 + " AND ( detailtable='' or detailtable is null )\nUNION all\nselect t1.id,t2.id AS detailtable_id,t1.fieldname as name,t1.fieldlabel as label,t1.fieldhtmltype as htmltype,t1.type as type,t1.fielddbtype,t1.detailtable \nfrom workflow_billfield t1,Workflow_billdetailtable t2 WHERE  t1.billid=t2.billid AND t1.detailtable=t2.tablename AND t1.billid =" + str6 + " \n ) tt ORDER BY tt.detailtable_id";
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 316 */     this.logger.info("=====search sql:" + str5);
/* 317 */     recordSet.executeQuery(str5, new Object[0]);
/*     */     
/* 319 */     while (recordSet.next()) {
/* 320 */       String str7 = recordSet.getString("id");
/* 321 */       String str8 = recordSet.getString("name");
/* 322 */       String str9 = recordSet.getString("label");
/* 323 */       String str10 = recordSet.getString("htmltype");
/* 324 */       String str11 = recordSet.getString("type");
/* 325 */       String str12 = recordSet.getString("fielddbtype");
/* 326 */       String str13 = recordSet.getString("detailtable");
/*     */ 
/*     */       
/* 329 */       int j = 0;
/* 330 */       if ("".equals(str13)) {
/* 331 */         str13 = "main";
/*     */       } else {
/* 333 */         j = Util.getIntValue(str13.substring(str13.lastIndexOf("_dt") + 3, str13.length()), 0);
/*     */       } 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 339 */       if (treeMap.containsKey(Integer.valueOf(j))) {
/* 340 */         tableMappingDefine = (TableMappingDefine)treeMap.get(Integer.valueOf(j));
/* 341 */         List list1 = tableMappingDefine.getFieldMappingDefineList();
/*     */       } else {
/* 343 */         tableMappingDefine = new TableMappingDefine();
/* 344 */         arrayList1 = new ArrayList();
/* 345 */         tableMappingDefine.setSourceTableName(str13);
/* 346 */         this.logger.info("=========2=========SourceTableName：" + str13);
/* 347 */         tableMappingDefine.setTargetTableName(str13);
/* 348 */         tableMappingDefine.setTableOrder(j);
/* 349 */         tableMappingDefine.setFieldMappingDefineList(arrayList1);
/*     */         
/* 351 */         treeMap.put(Integer.valueOf(j), tableMappingDefine);
/*     */       } 
/* 353 */       String str14 = "main";
/*     */       
/* 355 */       if (map1.containsKey(str7)) {
/*     */         
/* 357 */         WorkflowTriggerDetailConfig workflowTriggerDetailConfig = map1.get(str7);
/* 358 */         String str15 = Util.null2String(workflowTriggerDetailConfig.getOuterfieldname());
/* 359 */         if (!"".equals(str15) && str15.lastIndexOf(".") > 0) {
/* 360 */           str15 = str15.substring(str15.lastIndexOf(".") + 1, str15.length());
/* 361 */           str14 = Util.null2String(workflowTriggerDetailConfig.getOuterfieldname()).substring(0, Util.null2String(workflowTriggerDetailConfig.getOuterfieldname()).lastIndexOf("."));
/*     */         } 
/*     */         
/* 364 */         Map map2 = null;
/* 365 */         if (hashMap != null && hashMap.size() > 0) {
/* 366 */           map2 = (Map)hashMap.get(str14);
/*     */         }
/*     */         
/* 369 */         String str16 = "";
/* 370 */         if (map2 != null && str15 != null && !str15.equals("")) {
/* 371 */           str16 = Util.null2String(map2.get(str15));
/*     */         }
/*     */         
/* 374 */         FieldData fieldData1 = new FieldData();
/* 375 */         fieldData1.setFieldName(str15);
/* 376 */         fieldData1.setFieldType(str11);
/* 377 */         fieldData1.setFieldLabel(str9);
/*     */ 
/*     */         
/* 380 */         FieldData fieldData2 = new FieldData();
/* 381 */         fieldData2.setFieldName(str8);
/* 382 */         fieldData2.setFieldType(str11);
/* 383 */         fieldData2.setFieldLabel(str9);
/*     */ 
/*     */         
/* 386 */         FieldMappingDefine fieldMappingDefine = new FieldMappingDefine();
/* 387 */         fieldMappingDefine.setSourceField(fieldData1);
/* 388 */         fieldMappingDefine.setTargetField(fieldData2);
/*     */         
/* 390 */         fieldMappingDefine.setContext(generateContext(paramWorkflowTriggerConfig, workflowTriggerDetailConfig, str7, str8, str10, str11, str12, str16));
/*     */ 
/*     */         
/* 393 */         arrayList1.add(fieldMappingDefine);
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 401 */     arrayList = new ArrayList(treeMap.values());
/* 402 */     this.logger.info("==================tables：" + treeMap);
/* 403 */     generalMappingDefine.setTableMappingDefineList(arrayList);
/*     */     
/* 405 */     return generalMappingDefine;
/*     */   }
/*     */   
/*     */   private void buildGeneralSpecialMappingDefine(Map<Integer, TableMappingDefine> paramMap, Map<String, WorkflowTriggerDetailConfig> paramMap1, WorkflowTriggerConfig paramWorkflowTriggerConfig) {
/* 409 */     TableMappingDefine tableMappingDefine = new TableMappingDefine();
/* 410 */     ArrayList<FieldMappingDefine> arrayList = new ArrayList();
/* 411 */     tableMappingDefine.setSourceTableName("main");
/* 412 */     tableMappingDefine.setTargetTableName("main");
/* 413 */     tableMappingDefine.setTableOrder(0);
/* 414 */     tableMappingDefine.setFieldMappingDefineList(arrayList);
/* 415 */     paramMap.put(Integer.valueOf(0), tableMappingDefine);
/*     */     
/* 417 */     FieldData fieldData1 = null;
/* 418 */     FieldData fieldData2 = null;
/* 419 */     FieldMappingDefine fieldMappingDefine = null;
/*     */ 
/*     */     
/* 422 */     if (paramMap1.containsKey("-1")) {
/*     */       
/* 424 */       WorkflowTriggerDetailConfig workflowTriggerDetailConfig = paramMap1.get("-1");
/* 425 */       String str = Util.null2String(workflowTriggerDetailConfig.getOuterfieldname());
/* 426 */       if (!"".equals(str) && str.lastIndexOf(".") > 0) {
/* 427 */         str = str.substring(str.lastIndexOf(".") + 1, str.length());
/*     */       }
/*     */       
/* 430 */       fieldData1 = new FieldData();
/* 431 */       fieldData1.setFieldName(str);
/* 432 */       fieldData1.setFieldType("1");
/* 433 */       fieldData1.setFieldLabel("");
/* 434 */       fieldData2 = new FieldData();
/* 435 */       fieldData2.setFieldName("requestname");
/* 436 */       fieldData2.setFieldType("1");
/* 437 */       fieldData2.setFieldLabel("");
/* 438 */       fieldMappingDefine = new FieldMappingDefine();
/* 439 */       fieldMappingDefine.setSourceField(fieldData1);
/* 440 */       fieldMappingDefine.setTargetField(fieldData2);
/*     */       
/* 442 */       fieldMappingDefine.setContext(generateContext(paramWorkflowTriggerConfig, workflowTriggerDetailConfig, "-1", "requestname", "1", "1", "varchar"));
/* 443 */       arrayList.add(fieldMappingDefine);
/*     */     } 
/*     */     
/* 446 */     if (paramMap1.containsKey("-2")) {
/*     */       
/* 448 */       WorkflowTriggerDetailConfig workflowTriggerDetailConfig = paramMap1.get("-2");
/* 449 */       String str = Util.null2String(workflowTriggerDetailConfig.getOuterfieldname());
/*     */       
/* 451 */       paramWorkflowTriggerConfig.setWeavercreater(str);
/* 452 */       fieldData1 = new FieldData();
/* 453 */       fieldData1.setFieldName(str);
/* 454 */       fieldData1.setFieldType("1");
/* 455 */       fieldData1.setFieldLabel("");
/* 456 */       fieldData2 = new FieldData();
/* 457 */       fieldData2.setFieldName("creater");
/* 458 */       fieldData2.setFieldType("1");
/* 459 */       fieldData2.setFieldLabel("");
/* 460 */       fieldMappingDefine = new FieldMappingDefine();
/* 461 */       fieldMappingDefine.setSourceField(fieldData1);
/* 462 */       fieldMappingDefine.setTargetField(fieldData2);
/*     */       
/* 464 */       fieldMappingDefine.setContext(generateContext(paramWorkflowTriggerConfig, workflowTriggerDetailConfig, "-2", "creater", "3", "1", "int"));
/* 465 */       arrayList.add(fieldMappingDefine);
/*     */     } 
/*     */     
/* 468 */     if (paramMap1.containsKey("-3")) {
/*     */       
/* 470 */       WorkflowTriggerDetailConfig workflowTriggerDetailConfig = paramMap1.get("-3");
/* 471 */       String str = Util.null2String(workflowTriggerDetailConfig.getOuterfieldname());
/* 472 */       if (!"".equals(str) && str.lastIndexOf(".") > 0) {
/* 473 */         str = str.substring(str.lastIndexOf(".") + 1, str.length());
/*     */       }
/*     */       
/* 476 */       fieldData1 = new FieldData();
/* 477 */       fieldData1.setFieldName(str);
/* 478 */       fieldData1.setFieldType("1");
/* 479 */       fieldData1.setFieldLabel("");
/* 480 */       fieldData2 = new FieldData();
/* 481 */       fieldData2.setFieldName("classification");
/* 482 */       fieldData2.setFieldType("1");
/* 483 */       fieldData2.setFieldLabel("");
/* 484 */       fieldMappingDefine = new FieldMappingDefine();
/* 485 */       fieldMappingDefine.setSourceField(fieldData1);
/* 486 */       fieldMappingDefine.setTargetField(fieldData2);
/*     */       
/* 488 */       fieldMappingDefine.setContext(generateContext(paramWorkflowTriggerConfig, workflowTriggerDetailConfig, "-3", "classification", "5", "1", "int"));
/* 489 */       arrayList.add(fieldMappingDefine);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private Context generateContext(WorkflowTriggerConfig paramWorkflowTriggerConfig, WorkflowTriggerDetailConfig paramWorkflowTriggerDetailConfig, String paramString1, String paramString2, String paramString3, String paramString4, String paramString5) {
/* 507 */     Context context = new Context();
/* 508 */     context.setFieldId(paramString1);
/* 509 */     context.setFieldName(paramString2);
/* 510 */     context.setFieldHtmltype(paramString3);
/* 511 */     context.setFieldType(paramString4);
/* 512 */     context.setFieldDBType(paramString5);
/* 513 */     context.setChangeType(paramWorkflowTriggerDetailConfig.getChangetype());
/* 514 */     context.setCustomsql(paramWorkflowTriggerDetailConfig.getCustomsql());
/* 515 */     context.setWffileddbtype(paramWorkflowTriggerDetailConfig.getWffielddbtype());
/* 516 */     context.getWorkflowVariables().put("workflowid", paramWorkflowTriggerConfig.getWorkflowid());
/* 517 */     if ("6".equals(paramString3)) {
/* 518 */       context.getAttributes().put("attachment_settings", paramWorkflowTriggerDetailConfig.getAttachment_settings());
/* 519 */       context.getAttributes().put("dbtype", paramWorkflowTriggerConfig.getDatasourcetype());
/*     */     } 
/* 521 */     context.getAttributes().put("datasourceid", paramWorkflowTriggerDetailConfig.getDatasourceid());
/* 522 */     context.getAttributes().put("dtCfg", paramWorkflowTriggerDetailConfig);
/*     */     
/* 524 */     return context;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private Context generateContext(WorkflowTriggerConfig paramWorkflowTriggerConfig, WorkflowTriggerDetailConfig paramWorkflowTriggerDetailConfig, String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6) {
/* 541 */     Context context = new Context();
/* 542 */     context.setFieldId(paramString1);
/* 543 */     context.setFieldName(paramString2);
/* 544 */     context.setFieldHtmltype(paramString3);
/* 545 */     context.setFieldType(paramString4);
/* 546 */     context.setFieldDBType(paramString5);
/* 547 */     context.setChangeType(paramWorkflowTriggerDetailConfig.getChangetype());
/* 548 */     context.setCustomsql(paramWorkflowTriggerDetailConfig.getCustomsql());
/* 549 */     context.setWffileddbtype(paramString6);
/* 550 */     context.getWorkflowVariables().put("workflowid", paramWorkflowTriggerConfig.getWorkflowid());
/* 551 */     if ("6".equals(paramString3)) {
/* 552 */       context.getAttributes().put("attachment_settings", paramWorkflowTriggerDetailConfig.getAttachment_settings());
/* 553 */       context.getAttributes().put("dbtype", paramWorkflowTriggerConfig.getDatasourcetype());
/*     */     } 
/* 555 */     context.getAttributes().put("datasourceid", paramWorkflowTriggerDetailConfig.getDatasourceid());
/* 556 */     context.getAttributes().put("dtCfg", paramWorkflowTriggerDetailConfig);
/* 557 */     DataSource dataSource = (DataSource)StaticObj.getServiceByFullname("datasource." + paramWorkflowTriggerConfig.getDatasourceid(), DataSource.class);
/* 558 */     context.getAttributes().put("dbtype", dataSource.getType());
/* 559 */     return context;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private Map getColumnWithTypes(String paramString1, String paramString2) {
/* 570 */     RecordSetDataSource recordSetDataSource = new RecordSetDataSource();
/* 571 */     Map map = new HashMap<>();
/* 572 */     if (!paramString1.equals("")) {
/* 573 */       map = recordSetDataSource.getAllColumnWithTypes(paramString1, paramString2);
/*     */     }
/* 575 */     return map;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/* 595 */   private Logger logger = LoggerFactory.getLogger(getClass());
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/workflowtrigger/biz/WorkflowTriggerInitializer.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */