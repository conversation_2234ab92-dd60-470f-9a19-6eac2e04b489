package weaver.integration.workflowtrigger.sourcedata;

import java.util.List;
import java.util.Map;
import weaver.integration.framework.data.GeneralData;
import weaver.integration.workflowtrigger.config.WorkflowTriggerConfig;

public interface ISourceData {
  List<GeneralData> getSourceData(WorkflowTriggerConfig paramWorkflowTriggerConfig);
  
  Map getSourceDataNew(WorkflowTriggerConfig paramWorkflowTriggerConfig);
  
  Map getSourceDataNew(WorkflowTriggerConfig paramWorkflowTriggerConfig, int paramInt1, int paramInt2);
}


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/workflowtrigger/sourcedata/ISourceData.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */