/*     */ package weaver.integration.workflowtrigger.sourcedata.impl;
/*     */ 
/*     */ import com.alibaba.fastjson.JSON;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSetDataSource;
/*     */ import weaver.general.StaticObj;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.Util;
/*     */ import weaver.integration.framework.converter.IConvert;
/*     */ import weaver.integration.framework.data.GeneralData;
/*     */ import weaver.integration.framework.data.RecordData;
/*     */ import weaver.integration.framework.data.TableData;
/*     */ import weaver.integration.framework.mappingdefine.impl.TableMappingDefine;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ import weaver.integration.page.PageUtil;
/*     */ import weaver.integration.workflowtrigger.config.WorkflowTriggerConfig;
/*     */ import weaver.integration.workflowtrigger.config.WorkflowTriggerDetailConfig;
/*     */ import weaver.integration.workflowtrigger.sourcedata.ISourceData;
/*     */ import weaver.integration.workflowtrigger.workflow.AfterRuleUitl;
/*     */ import weaver.integration.workflowtrigger.workflow.TriggerLogUtil;
/*     */ import weaver.interfaces.datasource.DataSource;
/*     */ import weaver.interfaces.workflow.browser.BrowserForE8.Util.ParseSqlUtilE8;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SourceData4DB
/*     */   implements ISourceData
/*     */ {
/*     */   public List<GeneralData> getSourceData(WorkflowTriggerConfig paramWorkflowTriggerConfig) {
/*  39 */     String str1 = paramWorkflowTriggerConfig.getDatasourceid();
/*  40 */     List<String> list = paramWorkflowTriggerConfig.getSqlList();
/*  41 */     String str2 = paramWorkflowTriggerConfig.getKeyfield();
/*  42 */     if (str2 != null) {
/*  43 */       str2 = str2.toLowerCase();
/*     */     }
/*     */ 
/*     */     
/*  47 */     ArrayList<GeneralData> arrayList = new ArrayList();
/*     */ 
/*     */     
/*  50 */     String str3 = list.get(0);
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  55 */     this.logger.info("流程触发获取外部数据：" + str3);
/*  56 */     RecordSetDataSource recordSetDataSource = new RecordSetDataSource(str1);
/*  57 */     byte b = 0;
/*  58 */     recordSetDataSource.executeSql(str3);
/*     */     
/*  60 */     while (recordSetDataSource.next()) {
/*  61 */       boolean bool = Util.null2String(paramWorkflowTriggerConfig.getIsalways()).equals("1");
/*  62 */       String str4 = recordSetDataSource.getString(paramWorkflowTriggerConfig.getKeyfield());
/*  63 */       RecordSetDataSource recordSetDataSource1 = new RecordSetDataSource(str1);
/*  64 */       String str5 = Util.null2String(paramWorkflowTriggerConfig.getIsupdatewfdataField());
/*  65 */       String str6 = "0";
/*  66 */       if (str5 != null) {
/*  67 */         recordSetDataSource1.executeSql("select " + str5 + "  from " + paramWorkflowTriggerConfig.getOutermaintable() + " where " + paramWorkflowTriggerConfig.getKeyfield() + "=isupdatewfdataField");
/*  68 */         recordSetDataSource1.next();
/*  69 */         str6 = recordSetDataSource1.getString(str5);
/*     */       } 
/*  71 */       String str7 = "";
/*  72 */       if (!bool) {
/*  73 */         str7 = AfterRuleUitl.IscreateWf(paramWorkflowTriggerConfig, str4);
/*     */       }
/*  75 */       boolean bool1 = (Util.null2String(paramWorkflowTriggerConfig.getIsupdatewfdata()).equalsIgnoreCase("2") && str6.equals("1")) ? true : false;
/*  76 */       if (!bool && !bool1 && !str7.equals("") && Util.getIntValue(str7) > 0) {
/*  77 */         this.logger.info("非固定创建流程,未设置更新流程数据，流程已创建,不再重新创建流程！");
/*     */         
/*     */         continue;
/*     */       } 
/*  81 */       GeneralData generalData = new GeneralData();
/*     */ 
/*     */       
/*  84 */       ArrayList<TableData> arrayList1 = new ArrayList();
/*     */ 
/*     */       
/*  87 */       ArrayList<RecordData> arrayList2 = new ArrayList();
/*     */ 
/*     */       
/*  90 */       Map<String, Object> map = setRecordData(recordSetDataSource, arrayList2, b);
/*     */ 
/*     */ 
/*     */ 
/*     */       
/*  95 */       TableData tableData = buildTableData(((TableMappingDefine)paramWorkflowTriggerConfig.getGeneralMappingDefine().getTableMappingDefineList().get(0)).getSourceTableName(), arrayList2);
/*  96 */       arrayList1.add(tableData);
/*     */       
/*  98 */       if (list.size() > 1) {
/*  99 */         String str = (String)map.get(str2);
/*     */ 
/*     */ 
/*     */         
/* 103 */         for (byte b1 = 1; b1 < list.size(); b1++) {
/* 104 */           TableData tableData1 = getDetailTableData(paramWorkflowTriggerConfig, str1, list, str, b1);
/* 105 */           arrayList1.add(tableData1);
/*     */         } 
/*     */       } 
/*     */       
/* 109 */       generalData.setTableDataList(arrayList1);
/*     */       
/* 111 */       arrayList.add(generalData);
/* 112 */       b++;
/*     */     } 
/*     */     
/* 115 */     return arrayList;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map getSourceDataNew(WorkflowTriggerConfig paramWorkflowTriggerConfig) {
/* 120 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 121 */     String str1 = "";
/* 122 */     String str2 = paramWorkflowTriggerConfig.getDatasourceid();
/* 123 */     List<String> list = paramWorkflowTriggerConfig.getSqlList();
/* 124 */     String str3 = paramWorkflowTriggerConfig.getKeyfield();
/* 125 */     String str4 = paramWorkflowTriggerConfig.getWeavercreater();
/* 126 */     if (str3 != null) {
/* 127 */       str3 = str3.toLowerCase();
/*     */     }
/*     */ 
/*     */     
/* 131 */     ArrayList<GeneralData> arrayList = new ArrayList();
/*     */     
/* 133 */     String str5 = list.get(0);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 139 */     RecordSetDataSource recordSetDataSource = new RecordSetDataSource(str2);
/* 140 */     List list1 = paramWorkflowTriggerConfig.getWorkflowTriggerDetailConfigs();
/* 141 */     String str6 = "";
/* 142 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 143 */     for (WorkflowTriggerDetailConfig workflowTriggerDetailConfig : list1) {
/* 144 */       String str = workflowTriggerDetailConfig.getWffieldid();
/* 145 */       if (str.equals("-2")) {
/* 146 */         String str7 = workflowTriggerDetailConfig.getDatasourceid();
/* 147 */         str6 = Util.null2String(workflowTriggerDetailConfig.getConvertClass());
/* 148 */         String str8 = workflowTriggerDetailConfig.getCustomsql();
/* 149 */         hashMap2.put("datasourceid", str7);
/* 150 */         hashMap2.put("customsql", str8);
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 157 */     byte b = 0;
/* 158 */     recordSetDataSource.executeSql(str5);
/*     */     
/* 160 */     while (recordSetDataSource.next()) {
/* 161 */       boolean bool = Util.null2String(paramWorkflowTriggerConfig.getIsalways()).equals("1");
/* 162 */       String str7 = recordSetDataSource.getString(paramWorkflowTriggerConfig.getKeyfield());
/* 163 */       RecordSetDataSource recordSetDataSource1 = new RecordSetDataSource(str2);
/* 164 */       String str8 = Util.null2String(paramWorkflowTriggerConfig.getIsupdatewfdataField());
/* 165 */       String str9 = "0";
/* 166 */       if (str8 != null) {
/* 167 */         recordSetDataSource1.executeSql("select " + str8 + "  from " + paramWorkflowTriggerConfig.getOutermaintable() + " where " + paramWorkflowTriggerConfig.getKeyfield() + "=isupdatewfdataField");
/* 168 */         recordSetDataSource1.next();
/* 169 */         str9 = recordSetDataSource1.getString(str8);
/*     */       } 
/* 171 */       String str10 = "";
/* 172 */       if (!bool) {
/* 173 */         str10 = AfterRuleUitl.IscreateWf(paramWorkflowTriggerConfig, str7);
/*     */       }
/* 175 */       boolean bool1 = (Util.null2String(paramWorkflowTriggerConfig.getIsupdatewfdata()).equalsIgnoreCase("2") && str9.equals("1")) ? true : false;
/* 176 */       if (!bool && !bool1 && !str10.equals("") && Util.getIntValue(str10) > 0) {
/* 177 */         this.logger.info("非固定创建流程,未设置更新流程数据，流程已创建,不再重新创建流程！");
/*     */         
/*     */         continue;
/*     */       } 
/* 181 */       GeneralData generalData = new GeneralData();
/*     */ 
/*     */       
/* 184 */       ArrayList<TableData> arrayList1 = new ArrayList();
/*     */ 
/*     */       
/* 187 */       ArrayList<RecordData> arrayList2 = new ArrayList();
/*     */ 
/*     */       
/* 190 */       Map<String, Object> map = setRecordData(recordSetDataSource, arrayList2, b);
/*     */       
/* 192 */       str1 = Util.null2String((String)map.get(str4.toLowerCase()), "1");
/* 193 */       String str11 = str1;
/* 194 */       String str12 = paramWorkflowTriggerConfig.getDatarecordtype();
/* 195 */       String str13 = Util.null2String(paramWorkflowTriggerConfig.getOutermaintable());
/* 196 */       String str14 = Util.null2String(paramWorkflowTriggerConfig.getDatasourceid());
/* 197 */       String str15 = Util.null2String(paramWorkflowTriggerConfig.getFailback());
/* 198 */       String str16 = Util.null2String(paramWorkflowTriggerConfig.getFTriggerFlag());
/* 199 */       String str17 = Util.null2String(paramWorkflowTriggerConfig.getRequestid());
/* 200 */       String str18 = Util.null2String(paramWorkflowTriggerConfig.getFTriggerFlagValue());
/*     */ 
/*     */ 
/*     */       
/*     */       try {
/* 205 */         hashMap2.put("sourcevalue", str1);
/* 206 */         IConvert iConvert = (IConvert)Class.forName(str6).newInstance();
/* 207 */         str1 = (String)iConvert.convert(hashMap2);
/* 208 */         boolean bool2 = AfterRuleUitl.isExistForCreator(str1);
/* 209 */         if (!bool2) {
/*     */           
/* 211 */           if ("2".equals(str12) || "".equals(str12)) {
/* 212 */             TriggerLogUtil.saveOutTrigLog(str14, str13, str15, str3, str16, str17, str7, str18, "-1");
/*     */           }
/* 214 */           TriggerLogUtil.saveRequestLog2(paramWorkflowTriggerConfig.getId(), paramWorkflowTriggerConfig.getWorkflowid(), "", "" + SystemEnv.getHtmlLabelName(534746, ThreadVarLanguage.getLang()) + "'" + str11 + "'" + SystemEnv.getHtmlLabelName(534748, ThreadVarLanguage.getLang()), 0, 0, "");
/*     */           
/*     */           continue;
/*     */         } 
/* 218 */       } catch (Exception exception) {}
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 223 */       TableData tableData = buildTableData(((TableMappingDefine)paramWorkflowTriggerConfig.getGeneralMappingDefine().getTableMappingDefineList().get(0)).getSourceTableName(), arrayList2);
/* 224 */       arrayList1.add(tableData);
/*     */       
/* 226 */       if (list.size() > 1) {
/* 227 */         String str = (String)map.get(str3);
/*     */         
/* 229 */         for (byte b1 = 1; b1 < list.size(); b1++) {
/* 230 */           TableData tableData1 = getDetailTableData(paramWorkflowTriggerConfig, str2, list, str, b1);
/* 231 */           arrayList1.add(tableData1);
/*     */         } 
/*     */       } 
/*     */       
/* 235 */       generalData.setTableDataList(arrayList1);
/*     */       
/* 237 */       arrayList.add(generalData);
/* 238 */       b++;
/*     */     } 
/* 240 */     hashMap1.put("generalDataList", arrayList);
/* 241 */     hashMap1.put("creater", str1);
/* 242 */     return hashMap1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map getSourceDataNew(WorkflowTriggerConfig paramWorkflowTriggerConfig, int paramInt1, int paramInt2) {
/* 253 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 254 */     String str1 = "";
/* 255 */     String str2 = paramWorkflowTriggerConfig.getDatasourceid();
/* 256 */     DataSource dataSource = (DataSource)StaticObj.getServiceByFullname("datasource." + str2, DataSource.class);
/* 257 */     String str3 = dataSource.getType();
/* 258 */     List<String> list = paramWorkflowTriggerConfig.getSqlList();
/* 259 */     String str4 = paramWorkflowTriggerConfig.getKeyfield();
/*     */     
/* 261 */     String str5 = paramWorkflowTriggerConfig.getOutermaintable();
/*     */     
/* 263 */     if (str4 != null) {
/* 264 */       str4 = str4.toLowerCase();
/*     */     }
/*     */ 
/*     */     
/* 268 */     ArrayList<GeneralData> arrayList = new ArrayList();
/*     */     
/* 270 */     String str6 = list.get(0);
/*     */     
/* 272 */     String str7 = paramWorkflowTriggerConfig.getKeyfield();
/* 273 */     ParseSqlUtilE8 parseSqlUtilE8 = new ParseSqlUtilE8(null);
/* 274 */     if (parseSqlUtilE8.parserOrder(str6).indexOf(" ORDER BY ") == -1)
/*     */     {
/* 276 */       str6 = str6 + " ORDER BY " + str7;
/*     */     }
/* 278 */     RecordSetDataSource recordSetDataSource = new RecordSetDataSource(str2);
/* 279 */     List list1 = paramWorkflowTriggerConfig.getWorkflowTriggerDetailConfigs();
/* 280 */     String str8 = "";
/* 281 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 282 */     for (WorkflowTriggerDetailConfig workflowTriggerDetailConfig : list1) {
/* 283 */       String str = workflowTriggerDetailConfig.getWffieldid();
/* 284 */       if (str.equals("-2")) {
/* 285 */         String str9 = workflowTriggerDetailConfig.getDatasourceid();
/* 286 */         str8 = Util.null2String(workflowTriggerDetailConfig.getConvertClass());
/* 287 */         String str10 = workflowTriggerDetailConfig.getCustomsql();
/* 288 */         hashMap2.put("datasourceid", str9);
/* 289 */         hashMap2.put("customsql", str10);
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 295 */     ArrayList<String> arrayList1 = new ArrayList();
/* 296 */     str6 = PageUtil.limit(str6, str3, paramInt1, paramInt2);
/* 297 */     byte b = 0;
/* 298 */     recordSetDataSource.executeSql(str6);
/* 299 */     this.logger.info("流程触发获取外部主表数据SQL：" + str6 + "   查询数量count:" + recordSetDataSource.getCounts());
/* 300 */     while (recordSetDataSource.next()) {
/* 301 */       boolean bool = Util.null2String(paramWorkflowTriggerConfig.getIsalways()).equals("1");
/* 302 */       String str9 = recordSetDataSource.getString(paramWorkflowTriggerConfig.getKeyfield());
/* 303 */       RecordSetDataSource recordSetDataSource1 = new RecordSetDataSource(str2);
/* 304 */       String str10 = Util.null2String(paramWorkflowTriggerConfig.getIsupdatewfdataField());
/* 305 */       String str11 = "0";
/* 306 */       if (str10 != null && !"".equals(str10)) {
/* 307 */         recordSetDataSource1.executeQueryWithDatasource("select " + str10 + "  from " + paramWorkflowTriggerConfig.getOutermaintable() + " where " + paramWorkflowTriggerConfig.getKeyfield() + "=?", str2, new Object[] { str9 });
/* 308 */         if (recordSetDataSource1.next()) {
/* 309 */           str11 = recordSetDataSource1.getString(str10);
/*     */         }
/*     */       } 
/* 312 */       String str12 = "";
/* 313 */       if (!bool) {
/* 314 */         str12 = AfterRuleUitl.IscreateWfbynolog(paramWorkflowTriggerConfig, str9);
/*     */       }
/* 316 */       boolean bool1 = (Util.null2String(paramWorkflowTriggerConfig.getIsupdatewfdata()).equalsIgnoreCase("2") && str11.equals("1")) ? true : false;
/* 317 */       if (!bool && !bool1 && !str12.equals("") && Util.getIntValue(str12) > 0) {
/* 318 */         this.logger.info("非固定创建流程,未设置更新流程数据，流程已创建,不再重新创建流程！");
/*     */         
/*     */         continue;
/*     */       } 
/* 322 */       GeneralData generalData = new GeneralData();
/* 323 */       String str13 = paramWorkflowTriggerConfig.getWeavercreater();
/*     */       
/* 325 */       ArrayList<TableData> arrayList2 = new ArrayList();
/*     */ 
/*     */       
/* 328 */       ArrayList<RecordData> arrayList3 = new ArrayList();
/*     */ 
/*     */       
/* 331 */       Map<String, Object> map = setRecordData(recordSetDataSource, arrayList3, b);
/* 332 */       boolean bool2 = false;
/* 333 */       if (str13 == null || str13.equals("") || (str13 != null && !str13.equals("") && str13.indexOf(str5) <= -1)) {
/* 334 */         bool2 = true;
/*     */       }
/*     */       
/* 337 */       if (!"".equals(str13) && str13.lastIndexOf(".") > 0) {
/* 338 */         str13 = str13.substring(str13.lastIndexOf(".") + 1, str13.length());
/*     */       }
/* 340 */       str1 = Util.null2String(map.get(str13.toLowerCase()));
/* 341 */       String str14 = str1;
/* 342 */       String str15 = paramWorkflowTriggerConfig.getDatarecordtype();
/* 343 */       String str16 = Util.null2String(paramWorkflowTriggerConfig.getOutermaintable());
/* 344 */       String str17 = Util.null2String(paramWorkflowTriggerConfig.getDatasourceid());
/* 345 */       String str18 = Util.null2String(paramWorkflowTriggerConfig.getFailback());
/* 346 */       String str19 = Util.null2String(paramWorkflowTriggerConfig.getFTriggerFlag());
/* 347 */       String str20 = Util.null2String(paramWorkflowTriggerConfig.getRequestid());
/* 348 */       String str21 = Util.null2String(paramWorkflowTriggerConfig.getFTriggerFlagValue());
/*     */       try {
/* 350 */         hashMap2.put("sourcevalue", str1);
/* 351 */         IConvert iConvert = (IConvert)Class.forName(str8).newInstance();
/* 352 */         str1 = Util.null2String(iConvert.convert(hashMap2));
/* 353 */         if (str1 == null || str1.equals("")) {
/* 354 */           if (bool2) {
/* 355 */             str1 = "1";
/*     */           } else {
/*     */             
/* 358 */             if ("2".equals(str15) || "".equals(str15)) {
/* 359 */               TriggerLogUtil.saveOutTrigLog(str17, str16, str18, str4, str19, str20, str9, str21, "-1");
/*     */             }
/* 361 */             TriggerLogUtil.saveRequestLog2(paramWorkflowTriggerConfig.getId(), paramWorkflowTriggerConfig.getWorkflowid(), "", "" + SystemEnv.getHtmlLabelName(534746, ThreadVarLanguage.getLang()) + "'" + str14 + "'" + SystemEnv.getHtmlLabelName(534748, ThreadVarLanguage.getLang()), 0, 0, "");
/*     */             
/*     */             continue;
/*     */           } 
/*     */         } else {
/* 366 */           boolean bool3 = AfterRuleUitl.isExistForCreator(str1);
/* 367 */           if (!bool3) {
/* 368 */             if ("2".equals(str15) || "".equals(str15)) {
/* 369 */               TriggerLogUtil.saveOutTrigLog(str17, str16, str18, str4, str19, str20, str9, str21, "-1");
/*     */             }
/*     */             
/* 372 */             TriggerLogUtil.saveRequestLog2(paramWorkflowTriggerConfig.getId(), paramWorkflowTriggerConfig.getWorkflowid(), "", "" + SystemEnv.getHtmlLabelName(534746, ThreadVarLanguage.getLang()) + "'" + str14 + "'" + SystemEnv.getHtmlLabelName(534748, ThreadVarLanguage.getLang()), 0, 0, "");
/*     */             
/*     */             continue;
/*     */           } 
/*     */         } 
/* 377 */       } catch (Exception exception) {}
/*     */ 
/*     */       
/* 380 */       TableData tableData = buildTableData(((TableMappingDefine)paramWorkflowTriggerConfig.getGeneralMappingDefine().getTableMappingDefineList().get(0)).getSourceTableName(), arrayList3);
/* 381 */       arrayList2.add(tableData);
/*     */       
/* 383 */       if (list.size() > 1) {
/*     */ 
/*     */         
/* 386 */         String str = (String)map.get(str4);
/*     */         
/* 388 */         for (byte b1 = 1; b1 < list.size(); b1++) {
/* 389 */           TableData tableData1 = getDetailTableData(paramWorkflowTriggerConfig, str2, list, str, b1);
/* 390 */           arrayList2.add(tableData1);
/*     */         } 
/*     */       } 
/*     */       
/* 394 */       generalData.setTableDataList(arrayList2);
/*     */       
/* 396 */       arrayList.add(generalData);
/* 397 */       arrayList1.add(str1);
/*     */       
/* 399 */       b++;
/*     */     } 
/* 401 */     hashMap1.put("generalDataList", arrayList);
/* 402 */     hashMap1.put("creater", arrayList1);
/* 403 */     return hashMap1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private TableData getDetailTableData(WorkflowTriggerConfig paramWorkflowTriggerConfig, String paramString1, List<String> paramList, String paramString2, int paramInt) {
/* 419 */     ArrayList<RecordData> arrayList = new ArrayList();
/* 420 */     TableData tableData = new TableData();
/*     */     
/* 422 */     String str1 = paramList.get(paramInt);
/* 423 */     if (str1 == null || "".equals(str1)) {
/* 424 */       return tableData;
/*     */     }
/* 426 */     if (str1 != null && str1.indexOf("${keyid}") >= 0) {
/* 427 */       str1 = str1.replace("${keyid}", Util.null2String(paramString2));
/*     */     }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 445 */     if ("".equals(str1)) {
/* 446 */       TableData tableData1 = new TableData();
/* 447 */       tableData1.setTableName("");
/* 448 */       tableData1.setRecordDataList(arrayList);
/* 449 */       return tableData1;
/*     */     } 
/*     */     
/* 452 */     RecordSetDataSource recordSetDataSource = new RecordSetDataSource(paramString1);
/* 453 */     recordSetDataSource.executeSql(str1);
/* 454 */     this.logger.info("****************************getDetailTableData detailsql:" + str1 + "  查询数量:" + recordSetDataSource.getCounts());
/* 455 */     byte b = 0;
/* 456 */     while (recordSetDataSource.next()) {
/*     */       
/* 458 */       setRecordData(recordSetDataSource, arrayList, b);
/* 459 */       b++;
/*     */     } 
/*     */ 
/*     */     
/* 463 */     List<TableMappingDefine> list = paramWorkflowTriggerConfig.getGeneralMappingDefine().getTableMappingDefineList();
/* 464 */     this.logger.info("*************getDetailTableData******* i=" + paramInt + "      tableMappingDefineList:" + JSON.toJSONString(list));
/* 465 */     String str2 = ((TableMappingDefine)list.get(paramInt)).getSourceTableName();
/* 466 */     tableData = buildTableData(str2, arrayList);
/*     */     
/* 468 */     return tableData;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private TableData buildTableData(String paramString, List<RecordData> paramList) {
/* 478 */     TableData tableData = new TableData();
/* 479 */     tableData.setTableName(paramString);
/* 480 */     tableData.setRecordDataList(paramList);
/*     */     
/* 482 */     return tableData;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private Map<String, Object> setRecordData(RecordSetDataSource paramRecordSetDataSource, List<RecordData> paramList, int paramInt) {
/* 494 */     RecordData recordData = new RecordData();
/*     */ 
/*     */     
/* 497 */     Map<String, Object> map = getFieldDataMap(paramRecordSetDataSource, paramInt);
/*     */     
/* 499 */     recordData.setFieldData(map);
/* 500 */     paramList.add(recordData);
/*     */     
/* 502 */     return map;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private Map<String, Object> getFieldDataMap(RecordSetDataSource paramRecordSetDataSource, int paramInt) {
/* 512 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/* 514 */     String[] arrayOfString = paramRecordSetDataSource.getColumnName();
/* 515 */     int[] arrayOfInt = paramRecordSetDataSource.getColumnType();
/* 516 */     for (byte b = 0; b < arrayOfString.length; b++) {
/* 517 */       String str = arrayOfString[b];
/* 518 */       int i = arrayOfInt[b];
/* 519 */       Object object = null;
/* 520 */       if (i == -4 || i == 2004 || i == -2) {
/*     */         
/* 522 */         Object[] arrayOfObject = paramRecordSetDataSource.getArray().get(paramInt);
/* 523 */         if (arrayOfObject != null && arrayOfObject.length > b) {
/* 524 */           object = arrayOfObject[b];
/*     */         }
/*     */       } else {
/*     */         
/* 528 */         object = paramRecordSetDataSource.getString(str);
/*     */       } 
/* 530 */       hashMap.put(str.toLowerCase(), object);
/*     */     } 
/*     */     
/* 533 */     return (Map)hashMap;
/*     */   }
/*     */   
/* 536 */   private Logger logger = LoggerFactory.getLogger(SourceData4DB.class);
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/workflowtrigger/sourcedata/impl/SourceData4DB.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */