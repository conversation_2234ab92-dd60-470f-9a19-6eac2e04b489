/*    */ package weaver.integration.workflowtrigger.sourcedata.impl;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import weaver.integration.framework.data.GeneralData;
/*    */ import weaver.integration.workflowtrigger.config.WorkflowTriggerConfig;
/*    */ import weaver.integration.workflowtrigger.sourcedata.ISourceData;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class SourceData4ESB
/*    */   implements ISourceData
/*    */ {
/*    */   public List<GeneralData> getSourceData(WorkflowTriggerConfig paramWorkflowTriggerConfig) {
/* 22 */     return new ArrayList();
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public Map getSourceDataNew(WorkflowTriggerConfig paramWorkflowTriggerConfig) {
/* 31 */     return null;
/*    */   }
/*    */ 
/*    */   
/*    */   public Map getSourceDataNew(WorkflowTriggerConfig paramWorkflowTriggerConfig, int paramInt1, int paramInt2) {
/* 36 */     return null;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/workflowtrigger/sourcedata/impl/SourceData4ESB.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */