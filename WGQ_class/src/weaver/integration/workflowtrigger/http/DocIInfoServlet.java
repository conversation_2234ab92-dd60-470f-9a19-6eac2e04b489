/*     */ package weaver.integration.workflowtrigger.http;
/*     */ 
/*     */ import DBstep.iMsgServer2000;
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import java.io.BufferedInputStream;
/*     */ import java.io.BufferedReader;
/*     */ import java.io.ByteArrayInputStream;
/*     */ import java.io.ByteArrayOutputStream;
/*     */ import java.io.File;
/*     */ import java.io.FileInputStream;
/*     */ import java.io.FileOutputStream;
/*     */ import java.io.IOException;
/*     */ import java.io.InputStream;
/*     */ import java.io.InputStreamReader;
/*     */ import java.io.OutputStreamWriter;
/*     */ import java.util.Enumeration;
/*     */ import java.util.zip.ZipEntry;
/*     */ import java.util.zip.ZipFile;
/*     */ import java.util.zip.ZipInputStream;
/*     */ import javax.servlet.ServletException;
/*     */ import javax.servlet.http.HttpServlet;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.ConnStatement;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.file.ImageFileManager;
/*     */ import weaver.file.Prop;
/*     */ import weaver.general.Util;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class DocIInfoServlet
/*     */   extends HttpServlet
/*     */ {
/*  38 */   private String tmpzippath = Util.null2String(Prop.getPropValue("", "tmpzippath"));
/*     */ 
/*     */   
/*  41 */   private Logger logger = LoggerFactory.getLogger(DocIInfoServlet.class);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected void doPost(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) throws ServletException, IOException {
/*  52 */     JSONObject jSONObject = new JSONObject();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getImageFileName(String paramString) {
/* 118 */     String str1 = "";
/* 119 */     String str2 = "SELECT a.imagefilename  from ImageFile a , DocDetail b,DocImageFile c  where  c.imagefileid=a.imagefileid and c.docid=b.id and b.id=" + paramString;
/*     */ 
/*     */     
/* 122 */     RecordSet recordSet = new RecordSet();
/* 123 */     recordSet.executeSql(str2);
/* 124 */     if (recordSet.next()) {
/* 125 */       str1 = Util.null2String(recordSet.getString(1));
/*     */     }
/*     */ 
/*     */     
/* 129 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private String getImageFileId(String paramString) {
/* 135 */     String str1 = "";
/* 136 */     String str2 = "SELECT a.imagefileid  from ImageFile a , DocDetail b,DocImageFile c  where  c.imagefileid=a.imagefileid and c.docid=b.id and b.id=" + paramString;
/*     */ 
/*     */ 
/*     */     
/* 140 */     RecordSet recordSet = new RecordSet();
/* 141 */     recordSet.executeSql(str2);
/* 142 */     if (recordSet.next()) {
/* 143 */       str1 = Util.null2String(recordSet.getString(1));
/*     */     }
/*     */     
/* 146 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public byte[] getImageFileByteArray(int paramInt) throws Exception {
/* 158 */     Long long_ = Long.valueOf(System.currentTimeMillis());
/* 159 */     byte[] arrayOfByte = null;
/* 160 */     ConnStatement connStatement = null;
/*     */     try {
/* 162 */       connStatement = new ConnStatement();
/*     */       
/* 164 */       String str = "select a.* from ImageFile a , DocDetail b,DocImageFile c  where  c.imagefileid=a.imagefileid and c.docid=b.id and b.id=" + paramInt;
/* 165 */       boolean bool = connStatement.getDBType().equals("oracle");
/* 166 */       connStatement.setStatementSql(str);
/* 167 */       connStatement.executeQuery();
/* 168 */       if (connStatement.next()) {
/* 169 */         String str1 = Util.null2String(connStatement.getString("iszip"));
/* 170 */         String str2 = Util.null2String(connStatement.getString("filerealpath"));
/* 171 */         String str3 = Util.null2String(connStatement.getString("imagefilename"));
/*     */         
/* 173 */         byte[] arrayOfByte1 = new byte[1024];
/* 174 */         BufferedInputStream bufferedInputStream = null;
/* 175 */         ZipInputStream zipInputStream = null;
/*     */         
/* 177 */         if (str2.equals(""))
/* 178 */         { if (bool) {
/* 179 */             bufferedInputStream = new BufferedInputStream(connStatement.getBlobBinary("imagefile"));
/*     */           } else {
/* 181 */             bufferedInputStream = new BufferedInputStream(connStatement.getBinaryStream("imagefile"));
/*     */           }  }
/* 183 */         else { byte[] arrayOfByte2 = new byte[1024];
/* 184 */           File file = new File(str2);
/* 185 */           if (str1.equals("1")) {
/* 186 */             String str4 = "";
/* 187 */             if (!str3.equals("") && str3.contains(".")) {
/* 188 */               str4 = str3.substring(str3.lastIndexOf(".") + 1);
/*     */             }
/* 190 */             String str5 = long_ + "_" + paramInt + "." + str4;
/* 191 */             unZipFiles1(paramInt, this.tmpzippath, str5);
/* 192 */             file = new File(this.tmpzippath + str5);
/* 193 */             bufferedInputStream = new BufferedInputStream(new FileInputStream(file));
/*     */           } else {
/* 195 */             bufferedInputStream = new BufferedInputStream(new FileInputStream(file));
/*     */           } 
/*     */           
/* 198 */           ByteArrayOutputStream byteArrayOutputStream = null;
/*     */           
/* 200 */           try { byteArrayOutputStream = new ByteArrayOutputStream(); int i;
/* 201 */             while ((i = bufferedInputStream.read(arrayOfByte2)) != -1) {
/* 202 */               byteArrayOutputStream.write(arrayOfByte2, 0, i);
/* 203 */               byteArrayOutputStream.flush();
/*     */             } 
/* 205 */             arrayOfByte = byteArrayOutputStream.toByteArray(); }
/* 206 */           catch (Exception exception) {  }
/*     */           finally
/* 208 */           { if (bufferedInputStream != null) {
/* 209 */               bufferedInputStream.close();
/*     */             }
/* 211 */             if (zipInputStream != null) {
/* 212 */               zipInputStream.close();
/*     */             }
/* 214 */             if (byteArrayOutputStream != null) {
/* 215 */               byteArrayOutputStream.flush();
/* 216 */               byteArrayOutputStream.close();
/*     */             }  }
/*     */            }
/*     */       
/*     */       } 
/* 221 */     } catch (Exception exception) {
/* 222 */       exception.printStackTrace();
/* 223 */       if (null != connStatement) {
/* 224 */         connStatement.close();
/*     */       }
/*     */     } finally {
/* 227 */       if (null != connStatement) {
/* 228 */         connStatement.close();
/*     */       }
/*     */     } 
/* 231 */     return arrayOfByte;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public byte[] getImageFileByteArray1(int paramInt) throws Exception {
/* 241 */     byte[] arrayOfByte = null;
/*     */     
/*     */     try {
/* 244 */       InputStream inputStream = ImageFileManager.getInputStreamById(paramInt);
/* 245 */       byte[] arrayOfByte1 = new byte[1024];
/*     */       
/* 247 */       ByteArrayOutputStream byteArrayOutputStream = null;
/*     */       try {
/* 249 */         byteArrayOutputStream = new ByteArrayOutputStream(); int i;
/* 250 */         while ((i = inputStream.read(arrayOfByte1)) != -1) {
/* 251 */           byteArrayOutputStream.write(arrayOfByte1, 0, i);
/* 252 */           byteArrayOutputStream.flush();
/*     */         } 
/* 254 */         byte[] arrayOfByte2 = byteArrayOutputStream.toByteArray();
/* 255 */         iMsgServer2000 iMsgServer2000 = new iMsgServer2000();
/* 256 */         iMsgServer2000.MsgFileBody(arrayOfByte2);
/* 257 */         arrayOfByte2 = iMsgServer2000.ToDocument(iMsgServer2000.MsgFileBody());
/* 258 */         inputStream = new ByteArrayInputStream(arrayOfByte2);
/* 259 */         byteArrayOutputStream.close();
/*     */       }
/* 261 */       catch (Exception exception) {
/* 262 */         if (byteArrayOutputStream != null) byteArrayOutputStream.close();
/*     */       
/*     */       } 
/* 265 */       byteArrayOutputStream = null;
/*     */       
/* 267 */       try { byteArrayOutputStream = new ByteArrayOutputStream(); int i;
/* 268 */         while ((i = inputStream.read(arrayOfByte1)) != -1) {
/* 269 */           byteArrayOutputStream.write(arrayOfByte1, 0, i);
/* 270 */           byteArrayOutputStream.flush();
/*     */         } 
/* 272 */         arrayOfByte = byteArrayOutputStream.toByteArray(); }
/* 273 */       catch (Exception exception) {  }
/*     */       finally
/* 275 */       { if (inputStream != null) {
/* 276 */           inputStream.close();
/*     */         }
/* 278 */         if (byteArrayOutputStream != null) {
/* 279 */           byteArrayOutputStream.flush();
/* 280 */           byteArrayOutputStream.close();
/*     */         }
/*     */          }
/*     */     
/* 284 */     } catch (Exception exception) {
/* 285 */       exception.printStackTrace();
/*     */     } 
/*     */     
/* 288 */     return arrayOfByte;
/*     */   }
/*     */ 
/*     */   
/*     */   public static String getCode(int paramInt) throws Exception {
/* 293 */     InputStream inputStream = ImageFileManager.getInputStreamById(paramInt);
/* 294 */     byte[] arrayOfByte = new byte[3];
/* 295 */     inputStream.read(arrayOfByte);
/* 296 */     String str = "gb2312";
/* 297 */     if (arrayOfByte[0] == -1 && arrayOfByte[1] == -2) {
/* 298 */       str = "UTF-16";
/* 299 */     } else if (arrayOfByte[0] == -2 && arrayOfByte[1] == -1) {
/* 300 */       str = "Unicode";
/* 301 */     } else if (arrayOfByte[0] == -17 && arrayOfByte[1] == -69 && arrayOfByte[2] == -65) {
/* 302 */       str = "UTF-8";
/* 303 */     }  inputStream.close();
/*     */     
/* 305 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void unZipFiles1(int paramInt, String paramString1, String paramString2) {
/* 315 */     InputStream inputStream = ImageFileManager.getInputStreamById(paramInt);
/*     */     
/*     */     try {
/* 318 */       String str1 = (paramString1 + paramString2).replaceAll("\\*", "/");
/*     */       
/* 320 */       File file1 = new File(str1.substring(0, str1.lastIndexOf('/')));
/* 321 */       if (!file1.exists()) {
/* 322 */         file1.mkdirs();
/*     */       }
/* 324 */       File file2 = new File(str1);
/* 325 */       if (!file2.exists()) {
/* 326 */         file2.createNewFile();
/*     */       }
/* 328 */       StringBuilder stringBuilder = new StringBuilder("");
/* 329 */       InputStreamReader inputStreamReader = new InputStreamReader(inputStream, "UTF-8");
/* 330 */       BufferedReader bufferedReader = new BufferedReader(inputStreamReader);
/*     */       
/* 332 */       String str2 = "";
/* 333 */       while (null != (str2 = bufferedReader.readLine())) {
/* 334 */         stringBuilder.append(str2);
/* 335 */         stringBuilder.append("\r\n");
/*     */       } 
/* 337 */       bufferedReader.close();
/* 338 */       String str3 = stringBuilder.toString();
/* 339 */       OutputStreamWriter outputStreamWriter = new OutputStreamWriter(new FileOutputStream(str1), "utf-8");
/* 340 */       outputStreamWriter.append(str3);
/* 341 */       outputStreamWriter.close();
/* 342 */     } catch (Exception exception) {}
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void unZipFiles(String paramString1, String paramString2, String paramString3) throws IOException {
/* 355 */     unZipFiles(new File(paramString1), paramString2, paramString3);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void unZipFiles(File paramFile, String paramString1, String paramString2) throws IOException {
/* 364 */     File file = new File(paramString1);
/* 365 */     if (!file.exists()) {
/* 366 */       file.mkdirs();
/*     */     }
/* 368 */     ZipFile zipFile = new ZipFile(paramFile);
/* 369 */     for (Enumeration<? extends ZipEntry> enumeration = zipFile.entries(); enumeration.hasMoreElements(); ) {
/* 370 */       ZipEntry zipEntry = enumeration.nextElement();
/* 371 */       InputStream inputStream = zipFile.getInputStream(zipEntry);
/* 372 */       String str = (paramString1 + paramString2).replaceAll("\\*", "/");
/*     */       
/* 374 */       File file1 = new File(str.substring(0, str.lastIndexOf('/')));
/* 375 */       if (!file1.exists()) {
/* 376 */         file1.mkdirs();
/*     */       }
/*     */       
/* 379 */       if ((new File(str)).isDirectory()) {
/*     */         continue;
/*     */       }
/* 382 */       FileOutputStream fileOutputStream = new FileOutputStream(str);
/* 383 */       byte[] arrayOfByte = new byte[1024];
/*     */       int i;
/* 385 */       while ((i = inputStream.read(arrayOfByte)) > 0) {
/* 386 */         fileOutputStream.write(arrayOfByte, 0, i);
/*     */       }
/* 388 */       inputStream.close();
/* 389 */       fileOutputStream.close();
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/workflowtrigger/http/DocIInfoServlet.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */