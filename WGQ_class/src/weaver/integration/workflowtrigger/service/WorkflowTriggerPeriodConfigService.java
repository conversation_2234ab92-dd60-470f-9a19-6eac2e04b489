/*    */ package weaver.integration.workflowtrigger.service;
/*    */ 
/*    */ import java.util.List;
/*    */ import weaver.crazydream.util.Condition;
/*    */ import weaver.integration.customfield.service.AbstractService;
/*    */ import weaver.integration.customfield.util.CommonEntityService;
/*    */ import weaver.integration.workflowtrigger.config.WorkflowTriggerPeriodConfig;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class WorkflowTriggerPeriodConfigService
/*    */   implements AbstractService
/*    */ {
/*    */   @Deprecated
/*    */   public String add(Object paramObject) {
/* 18 */     return "";
/*    */   }
/*    */ 
/*    */   
/*    */   @Deprecated
/*    */   public boolean update(Object paramObject) {
/* 24 */     return false;
/*    */   }
/*    */ 
/*    */   
/*    */   @Deprecated
/*    */   public boolean delete(String paramString) {
/* 30 */     return false;
/*    */   }
/*    */ 
/*    */   
/*    */   @Deprecated
/*    */   public Object get(String paramString) {
/* 36 */     return null;
/*    */   }
/*    */ 
/*    */   
/*    */   public List<?> getList(Condition paramCondition) {
/* 41 */     CommonEntityService commonEntityService = new CommonEntityService(WorkflowTriggerPeriodConfig.class);
/* 42 */     return commonEntityService.getEntitiesByConditionToList(paramCondition);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/workflowtrigger/service/WorkflowTriggerPeriodConfigService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */