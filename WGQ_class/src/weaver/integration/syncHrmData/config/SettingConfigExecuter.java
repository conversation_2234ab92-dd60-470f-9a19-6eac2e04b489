/*    */ package weaver.integration.syncHrmData.config;
/*    */ 
/*    */ import weaver.general.Util;
/*    */ import weaver.integration.syncHrmData.cominfo.SettingConfigCominfo;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class SettingConfigExecuter
/*    */ {
/*    */   public SettingConfigBean getConfig(String paramString) {
/* 14 */     SettingConfigBean settingConfigBean = new SettingConfigBean();
/* 15 */     SettingConfigCominfo settingConfigCominfo = new SettingConfigCominfo();
/*    */     
/* 17 */     settingConfigBean.setIsuselhr(Util.null2String(settingConfigCominfo.getIsuselhr(paramString)));
/* 18 */     settingConfigBean.setIntetype(Util.null2String(settingConfigCominfo.getIntetype(paramString)));
/* 19 */     settingConfigBean.setDbsource(Util.null2String(settingConfigCominfo.getDbsource(paramString)));
/*    */     
/* 21 */     settingConfigBean.setWebserviceurl(Util.null2String(settingConfigCominfo.getWebserviceurl(paramString)));
/* 22 */     settingConfigBean.setInvoketype(Util.null2String(settingConfigCominfo.getInvoketype(paramString)));
/* 23 */     settingConfigBean.setCustomparams(Util.null2String(settingConfigCominfo.getCustomparams(paramString)));
/* 24 */     settingConfigBean.setCustominterface(Util.null2String(settingConfigCominfo.getCustominterface(paramString)));
/* 25 */     settingConfigBean.setHrmethod(Util.null2String(settingConfigCominfo.getHrmethod(paramString)));
/*    */     
/* 27 */     settingConfigBean.setTimeModul(Util.null2String(settingConfigCominfo.getTimeModul(paramString)));
/* 28 */     settingConfigBean.setFrequency(Util.null2String(settingConfigCominfo.getFrequency(paramString)));
/* 29 */     settingConfigBean.setFrequencyy(Util.null2String(settingConfigCominfo.getFrequencyy(paramString)));
/* 30 */     settingConfigBean.setCreateType(Util.null2String(settingConfigCominfo.getCreateType(paramString)));
/* 31 */     settingConfigBean.setCreateTime(Util.null2String(settingConfigCominfo.getCreateTime(paramString)));
/* 32 */     settingConfigBean.setHourTime(Util.null2String(settingConfigCominfo.getHourTime(paramString)));
/*    */     
/* 34 */     settingConfigBean.setJobtable(Util.null2String(settingConfigCominfo.getJobtable(paramString)));
/* 35 */     settingConfigBean.setDepttable(Util.null2String(settingConfigCominfo.getDepttable(paramString)));
/* 36 */     settingConfigBean.setSubcomtable(Util.null2String(settingConfigCominfo.getSubcomtable(paramString)));
/* 37 */     settingConfigBean.setHrmtable(Util.null2String(settingConfigCominfo.getHrmtable(paramString)));
/*    */     
/* 39 */     settingConfigBean.setJobmothod(Util.null2String(settingConfigCominfo.getJobmothod(paramString)));
/* 40 */     settingConfigBean.setDeptmothod(Util.null2String(settingConfigCominfo.getDeptmothod(paramString)));
/* 41 */     settingConfigBean.setSubcommothod(Util.null2String(settingConfigCominfo.getSubcommothod(paramString)));
/* 42 */     settingConfigBean.setHrmmethod(Util.null2String(settingConfigCominfo.getHrmmethod(paramString)));
/*    */     
/* 44 */     settingConfigBean.setJobparam(Util.null2String(settingConfigCominfo.getJobparam(paramString)));
/* 45 */     settingConfigBean.setDeptparam(Util.null2String(settingConfigCominfo.getDeptparam(paramString)));
/* 46 */     settingConfigBean.setSubcomparam(Util.null2String(settingConfigCominfo.getSubcomparam(paramString)));
/* 47 */     settingConfigBean.setHrmparam(Util.null2String(settingConfigCominfo.getHrmparam(paramString)));
/*    */     
/* 49 */     settingConfigBean.setDefaultPwd(Util.null2String(settingConfigCominfo.getDefaultPwd(paramString)));
/* 50 */     settingConfigBean.setPwdSyncType(Util.null2String(settingConfigCominfo.getPwdSyncType(paramString)));
/*    */     
/* 52 */     settingConfigBean.setIssynrtx(Util.null2String(settingConfigCominfo.getIssynrtx(paramString)));
/* 53 */     settingConfigBean.setUseMultiLang(Util.isEnableMultiLang());
/* 54 */     if (Util.isEnableMultiLang()) {
/* 55 */       settingConfigBean.setLang_(Util.null2String(settingConfigCominfo.getLang_(paramString)));
/*    */     }
/* 57 */     settingConfigBean.setAfter_clazz_(Util.null2String(settingConfigCominfo.getAfter_clazz_(paramString)));
/*    */     
/* 59 */     settingConfigBean.setSubcomouternew(Util.null2String(settingConfigCominfo.getSubcomts(paramString)));
/* 60 */     settingConfigBean.setDeptouternew(Util.null2String(settingConfigCominfo.getDeptts(paramString)));
/* 61 */     settingConfigBean.setJobouternew(Util.null2String(settingConfigCominfo.getJobts(paramString)));
/* 62 */     settingConfigBean.setHrmouternew(Util.null2String(settingConfigCominfo.getHrmts(paramString)));
/*    */     
/* 64 */     return settingConfigBean;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/syncHrmData/config/SettingConfigExecuter.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */