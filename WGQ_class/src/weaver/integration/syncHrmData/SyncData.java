/*     */ package weaver.integration.syncHrmData;
/*     */ 
/*     */ import com.weaver.general.TimeUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Iterator;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.Set;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.companyvirtual.ResourceVirtualComInfo;
/*     */ import weaver.hrm.job.JobTitlesComInfo;
/*     */ import weaver.hrm.resource.CustomHrmSyn;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ import weaver.integration.syncHrmData.config.SettingConfigBean;
/*     */ import weaver.integration.syncHrmData.config.SettingConfigExecuter;
/*     */ import weaver.integration.syncHrmData.log.SynLogBean;
/*     */ import weaver.integration.syncHrmData.log.SynLogUtil;
/*     */ import weaver.integration.syncHrmData.log.SyncLogDetailBean;
/*     */ import weaver.integration.syncHrmData.synchronization.SyncDepartment;
/*     */ import weaver.integration.syncHrmData.synchronization.SyncHrmresource;
/*     */ import weaver.integration.syncHrmData.synchronization.SyncJobTitle;
/*     */ import weaver.integration.syncHrmData.synchronization.SyncSubCompany;
/*     */ 
/*     */ public class SyncData
/*     */ {
/*  33 */   private static Logger newlog = LoggerFactory.getLogger(SyncData.class);
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static volatile SyncData syncData;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static SyncData getInstance() {
/*  45 */     if (syncData == null) {
/*  46 */       synchronized (SyncData.class) {
/*  47 */         if (syncData == null) {
/*  48 */           syncData = new SyncData();
/*     */         }
/*     */       } 
/*     */     }
/*  52 */     return syncData;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public synchronized Map<String, Object> syn(String paramString, User paramUser, int paramInt) {
/*  63 */     String str1 = paramUser.getLastname();
/*  64 */     int i = paramUser.getLanguage();
/*  65 */     Map<String, Object> map = (Map)new HashMap<>();
/*  66 */     RecordSet recordSet1 = new RecordSet();
/*  67 */     RecordSet recordSet2 = new RecordSet();
/*  68 */     String str2 = recordSet1.getDBType();
/*  69 */     String str3 = "select id from hrsyncset";
/*  70 */     RecordSet recordSet3 = new RecordSet();
/*  71 */     recordSet3.executeQuery(str3, new Object[0]);
/*  72 */     String str4 = "1";
/*  73 */     if (recordSet3.next()) {
/*  74 */       str4 = Util.null2String(recordSet3.getString("id"), "1");
/*     */     }
/*     */     try {
/*  77 */       SettingConfigExecuter settingConfigExecuter = new SettingConfigExecuter();
/*  78 */       SettingConfigBean settingConfigBean = settingConfigExecuter.getConfig(str4);
/*  79 */       String str = settingConfigBean.getIntetype();
/*  80 */       newlog.info("******************************************同步开始******************************************");
/*  81 */       SynLogBean synLogBean = new SynLogBean();
/*  82 */       synLogBean.setStartdate(TimeUtil.getCurrentTimeString());
/*  83 */       synLogBean.setSynoperator(str1);
/*  84 */       synLogBean.setSyntype(Util.getIntValue(paramString, 0));
/*  85 */       synLogBean.setSynmethod(paramInt);
/*  86 */       int j = SynLogUtil.startInitLogAndRtnID(synLogBean);
/*  87 */       if ("3".equals(str)) {
/*  88 */         newlog.info("---------------Start synchronization:custom---------------");
/*  89 */         CustomHrmSyn customHrmSyn = new CustomHrmSyn();
/*  90 */         map = customHrmSyn.syn(paramString);
/*  91 */         if (map != null) {
/*  92 */           ArrayList<SyncLogDetailBean> arrayList = new ArrayList();
/*  93 */           Set set = map.keySet();
/*  94 */           for (Iterator<String> iterator = set.iterator(); iterator.hasNext(); ) {
/*  95 */             String str5 = Util.null2String(iterator.next());
/*  96 */             List<Map> list = (List)map.get(str5);
/*  97 */             if (list != null) {
/*  98 */               for (byte b = 0; b < list.size(); b++) {
/*  99 */                 SyncLogDetailBean syncLogDetailBean = new SyncLogDetailBean();
/* 100 */                 Map map1 = list.get(b);
/* 101 */                 String str6 = Util.null2String(map1.get("OUTPK"));
/* 102 */                 String str7 = Util.null2String(map1.get("PK"));
/* 103 */                 String str8 = Util.null2String(map1.get("Memo"));
/* 104 */                 String str9 = Util.null2String(map1.get("Success"));
/* 105 */                 String str10 = Util.null2String(map1.get("ErrorMessage"));
/* 106 */                 syncLogDetailBean.setSynid(j);
/* 107 */                 syncLogDetailBean.setSyntype(Util.getIntValue(str5));
/* 108 */                 syncLogDetailBean.setOutpk(str6);
/* 109 */                 syncLogDetailBean.setPk(str7);
/* 110 */                 syncLogDetailBean.setMemo(str8);
/* 111 */                 syncLogDetailBean.setSynstate(Util.getIntValue(str9));
/* 112 */                 syncLogDetailBean.setError(str10);
/* 113 */                 syncLogDetailBean.setLanguage(i);
/* 114 */                 arrayList.add(syncLogDetailBean);
/*     */               } 
/*     */             }
/*     */           } 
/* 118 */           SynLogUtil.insertDetailBatch(arrayList);
/* 119 */           newlog.info("---------------End of synchronization:custom---------------");
/*     */         } 
/*     */       } else {
/*     */         
/* 123 */         boolean bool1 = false;
/* 124 */         boolean bool2 = false;
/* 125 */         if (paramString.equals("1")) {
/* 126 */           map = SyncSubCompany.synSubCompany(map, settingConfigBean, j, i);
/* 127 */         } else if (paramString.equals("2")) {
/* 128 */           map = SyncDepartment.synDepartment(map, settingConfigBean, j, i);
/* 129 */         } else if (paramString.equals("3")) {
/* 130 */           map = SyncJobTitle.synJobTitles(map, settingConfigBean, j, i);
/* 131 */         } else if (paramString.equals("4")) {
/* 132 */           map = SyncHrmresource.synResource(map, settingConfigBean, j, i);
/*     */         } else {
/*     */           
/* 135 */           map = SyncSubCompany.synSubCompany(map, settingConfigBean, j, i);
/*     */           
/* 137 */           map = SyncDepartment.synDepartment(map, settingConfigBean, j, i);
/*     */           
/* 139 */           map = SyncJobTitle.synJobTitles(map, settingConfigBean, j, i);
/*     */           
/* 141 */           map = SyncHrmresource.synResource(map, settingConfigBean, j, i);
/*     */         } 
/*     */       } 
/*     */       try {
/* 145 */         SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/* 146 */         subCompanyComInfo.removeCompanyCache();
/* 147 */         DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/* 148 */         departmentComInfo.removeCompanyCache();
/* 149 */         ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 150 */         resourceComInfo.removeResourceCache();
/* 151 */         JobTitlesComInfo jobTitlesComInfo = new JobTitlesComInfo();
/* 152 */         jobTitlesComInfo.removeJobTitlesCache();
/*     */         
/* 154 */         ResourceVirtualComInfo resourceVirtualComInfo = new ResourceVirtualComInfo();
/* 155 */         resourceVirtualComInfo.removeResourceVirtualCache();
/* 156 */       } catch (Exception exception) {
/* 157 */         newlog.error("Handle data cache exceptions:" + exception);
/*     */       } 
/* 159 */       recordSet1.executeSql("select id from hrsyndetail where synstate=0 and synid=" + j);
/* 160 */       RecordSet recordSet = new RecordSet();
/* 161 */       if (recordSet1.next()) {
/* 162 */         recordSet.executeSql("update hrsynlog set synresult=1,enddate='" + TimeUtil.getCurrentTimeString() + "' where id=" + j);
/*     */       } else {
/* 164 */         recordSet2.executeSql("select id from hrsyndetail where synstate =6 and synid=" + j);
/* 165 */         if (recordSet2.next()) {
/* 166 */           recordSet.executeSql("update hrsynlog set synresult=2,enddate='" + TimeUtil.getCurrentTimeString() + "' where id=" + j);
/*     */         } else {
/* 168 */           recordSet.executeSql("update hrsynlog set synresult=0,enddate='" + TimeUtil.getCurrentTimeString() + "' where id=" + j);
/*     */         } 
/*     */       } 
/* 171 */       newlog.error("******************************************同步结束******************************************");
/* 172 */     } catch (Exception exception) {
/* 173 */       newlog.error(exception, exception);
/*     */     } 
/* 175 */     return map;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/syncHrmData/SyncData.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */