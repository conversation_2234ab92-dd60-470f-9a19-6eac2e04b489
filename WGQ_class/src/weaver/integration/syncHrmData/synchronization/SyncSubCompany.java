/*    */ package weaver.integration.syncHrmData.synchronization;
/*    */ 
/*    */ import com.alibaba.fastjson.JSON;
/*    */ import com.engine.common.service.impl.HrmSynCommonServiceImpl;
/*    */ import com.engine.common.util.HrmSynUtil;
/*    */ import com.engine.common.util.ServiceUtil;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import java.util.UUID;
/*    */ import weaver.general.TimeUtil;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ import weaver.integration.logging.Logger;
/*    */ import weaver.integration.logging.LoggerFactory;
/*    */ import weaver.integration.syncHrmData.Handler.impl.SynDataByWebservice;
/*    */ import weaver.integration.syncHrmData.Handler.impl.SyncDataByDB;
/*    */ import weaver.integration.syncHrmData.config.MappingBean;
/*    */ import weaver.integration.syncHrmData.config.MappingBeanExecuter;
/*    */ import weaver.integration.syncHrmData.config.SettingConfigBean;
/*    */ import weaver.integration.syncHrmData.log.SynLogUtil;
/*    */ import weaver.integration.syncHrmData.util.SyncUtil;
/*    */ 
/*    */ public class SyncSubCompany {
/* 26 */   private static Logger newlog = LoggerFactory.getLogger(SyncSubCompany.class); public static Map<String, Object> synSubCompany(Map<String, Object> paramMap, SettingConfigBean paramSettingConfigBean, int paramInt1, int paramInt2) throws Exception { Map map1; List list1; SyncDataByDB syncDataByDB; int i; char c; List list2; SynDataByWebservice synDataByWebservice; Map map2;
/*    */     List list3;
/*    */     Map map3;
/* 29 */     String str1 = paramSettingConfigBean.getIntetype();
/* 30 */     String str2 = UUID.randomUUID() + "";
/* 31 */     String str3 = TimeUtil.getCurrentDateString();
/* 32 */     String str4 = SyncUtil.getIncrementDate("1");
/* 33 */     User user = SyncUtil.getUser();
/* 34 */     MappingBean mappingBean = MappingBeanExecuter.getSetMap("1");
/* 35 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 36 */     hashMap1.put("keyword", mappingBean.getKey_oa());
/* 37 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 38 */     hashMap2.put("taskId", str2);
/* 39 */     hashMap2.put("ruleparam", hashMap1);
/* 40 */     HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 41 */     newlog.info("******************************************同步分部开始******************************************");
/* 42 */     ArrayList arrayList = new ArrayList();
/* 43 */     switch (str1) {
/*    */       case "1":
/* 45 */         syncDataByDB = new SyncDataByDB();
/* 46 */         i = syncDataByDB.getSubcompanyTotalCnt(str4);
/* 47 */         c = 'Ϩ';
/* 48 */         newlog.info("当前分页数为：" + c);
/* 49 */         if (i > c) {
/* 50 */           int j = i / c;
/* 51 */           j = (i % c == 0) ? j : (j + 1);
/* 52 */           for (byte b = 0; b < j; b++) {
/* 53 */             List list = syncDataByDB.getSubcompanyData(str4, b, c, i, mappingBean);
/* 54 */             hashMap2.put("datas", list);
/*    */             
/* 56 */             Map map4 = ((HrmSynCommonServiceImpl)ServiceUtil.getService(HrmSynCommonServiceImpl.class, user)).synSubCompany(hashMap2, user);
/* 57 */             newlog.info("接口输入信息：" + JSON.toJSONString(hashMap2));
/* 58 */             newlog.info("接口返回值：" + JSON.toJSONString(map4));
/*    */             
/* 60 */             Map map5 = SyncUtil.handleResult2File(paramMap, mappingBean, list, "1", paramInt1, paramInt2, map4);
/* 61 */             SynLogUtil.WriteLog2File(map5, Util.getIntValue("1"));
/*    */           }  break;
/*    */         } 
/* 64 */         list2 = syncDataByDB.getSubcompanyData(str4, mappingBean);
/* 65 */         hashMap2.put("datas", list2);
/* 66 */         list1 = list2;
/* 67 */         map1 = ((HrmSynCommonServiceImpl)ServiceUtil.getService(HrmSynCommonServiceImpl.class, user)).synSubCompany(hashMap2, user);
/* 68 */         newlog.info("接口输入信息：" + JSON.toJSONString(hashMap2));
/* 69 */         newlog.info("接口返回值：" + JSON.toJSONString(map1));
/*    */ 
/*    */         
/* 72 */         map2 = SyncUtil.handleResult2File(paramMap, mappingBean, list1, "1", paramInt1, paramInt2, map1);
/* 73 */         SynLogUtil.WriteLog2File(map2, Util.getIntValue("1"));
/*    */         break;
/*    */       
/*    */       case "2":
/* 77 */         synDataByWebservice = new SynDataByWebservice(paramSettingConfigBean);
/* 78 */         list3 = synDataByWebservice.getSubcompanyData(str4, mappingBean);
/* 79 */         hashMap2.put("datas", list3);
/* 80 */         list1 = list3;
/* 81 */         map1 = ((HrmSynCommonServiceImpl)ServiceUtil.getService(HrmSynCommonServiceImpl.class, user)).synSubCompany(hashMap2, user);
/* 82 */         newlog.info("接口输入信息：" + JSON.toJSONString(hashMap2));
/* 83 */         newlog.info("接口返回值：" + JSON.toJSONString(map1));
/*    */         
/* 85 */         map3 = SyncUtil.handleResult2File(paramMap, mappingBean, list1, "1", paramInt1, paramInt2, map1);
/* 86 */         SynLogUtil.WriteLog2File(map3, Util.getIntValue("1"));
/*    */         break;
/*    */     } 
/*    */ 
/*    */ 
/*    */ 
/*    */     
/* 93 */     (new HrmSynUtil()).synSubCompanyRelation(str2, mappingBean.getKey_oa(), user);
/* 94 */     SyncUtil.updateSyncData("1", str3);
/* 95 */     newlog.info("******************************************同步分部结束******************************************");
/* 96 */     return paramMap; }
/*    */ 
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/syncHrmData/synchronization/SyncSubCompany.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */