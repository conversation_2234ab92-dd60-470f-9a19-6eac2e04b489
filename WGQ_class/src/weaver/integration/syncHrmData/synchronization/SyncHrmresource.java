/*     */ package weaver.integration.syncHrmData.synchronization;
/*     */ 
/*     */ import com.alibaba.fastjson.JSON;
/*     */ import com.engine.common.service.impl.HrmSynCommonServiceImpl;
/*     */ import com.engine.common.util.HrmSynUtil;
/*     */ import com.engine.common.util.ServiceUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.UUID;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ import weaver.integration.syncHrmData.Handler.impl.SynDataByWebservice;
/*     */ import weaver.integration.syncHrmData.Handler.impl.SyncDataByDB;
/*     */ import weaver.integration.syncHrmData.config.MappingBean;
/*     */ import weaver.integration.syncHrmData.config.MappingBeanExecuter;
/*     */ import weaver.integration.syncHrmData.config.SettingConfigBean;
/*     */ import weaver.integration.syncHrmData.log.SynLogUtil;
/*     */ import weaver.integration.syncHrmData.util.SyncUtil;
/*     */ 
/*     */ public class SyncHrmresource
/*     */ {
/*  27 */   private static Logger newlog = LoggerFactory.getLogger(SyncHrmresource.class); public static Map<String, Object> synResource(Map<String, Object> paramMap, SettingConfigBean paramSettingConfigBean, int paramInt1, int paramInt2) throws Exception { Map map1; List list1; SyncDataByDB syncDataByDB; int i; char c; List list2; SynDataByWebservice synDataByWebservice;
/*     */     Map map2;
/*     */     List list3;
/*     */     Map map3;
/*  31 */     String str1 = paramSettingConfigBean.getIntetype();
/*  32 */     String str2 = UUID.randomUUID() + "";
/*  33 */     String str3 = TimeUtil.getCurrentDateString();
/*  34 */     String str4 = SyncUtil.getIncrementDate("4");
/*  35 */     User user = SyncUtil.getUser();
/*     */     
/*  37 */     MappingBean mappingBean1 = MappingBeanExecuter.getSetMap("4");
/*  38 */     MappingBean mappingBean2 = MappingBeanExecuter.getSetMap("1");
/*  39 */     MappingBean mappingBean3 = MappingBeanExecuter.getSetMap("2");
/*  40 */     MappingBean mappingBean4 = MappingBeanExecuter.getSetMap("3");
/*  41 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  42 */     hashMap1.put("keyword", mappingBean1.getKey_oa());
/*  43 */     hashMap1.put("subcompany", mappingBean2.getKey_oa());
/*  44 */     hashMap1.put("department", mappingBean3.getKey_oa());
/*  45 */     hashMap1.put("jobtitle", "".equals(Util.null2String(mappingBean4.getKey_oa())) ? "jobtitlename" : mappingBean4.getKey_oa());
/*  46 */     hashMap1.put("seclevel", "normal");
/*     */     
/*  48 */     boolean bool = true;
/*  49 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  50 */     hashMap2.put("taskId", str2);
/*  51 */     hashMap2.put("ruleparam", hashMap1);
/*  52 */     HashMap<Object, Object> hashMap3 = new HashMap<>();
/*  53 */     newlog.info("******************************************同步人员开始11******************************************");
/*  54 */     ArrayList arrayList = new ArrayList();
/*  55 */     switch (str1) {
/*     */       case "1":
/*  57 */         syncDataByDB = new SyncDataByDB();
/*  58 */         i = syncDataByDB.getHrTotalCnt(str4);
/*  59 */         c = 'Ϩ';
/*  60 */         newlog.info("当前分页数为：" + c);
/*  61 */         if (i > c) {
/*  62 */           int j = i / c;
/*  63 */           j = (i % c == 0) ? j : (j + 1);
/*  64 */           for (byte b = 0; b < j; b++) {
/*  65 */             List list = syncDataByDB.getHrData(str4, b, c, i, mappingBean1);
/*     */             
/*  67 */             for (Map<String, String> map : (Iterable<Map<String, String>>)list) {
/*  68 */               if (map.get("password") != null) {
/*  69 */                 if (paramSettingConfigBean.getPwdSyncType().equalsIgnoreCase("1")) {
/*  70 */                   map.put("password", Util.getEncrypt(map.get("password")));
/*     */                 }
/*  72 */                 bool = false;
/*     */               } 
/*     */             } 
/*  75 */             if (bool)
/*  76 */               hashMap1.put("password", "normal"); 
/*  77 */             hashMap2.put("datas", list);
/*     */             
/*  79 */             Map map4 = ((HrmSynCommonServiceImpl)ServiceUtil.getService(HrmSynCommonServiceImpl.class, user)).synResource(hashMap2, user);
/*  80 */             newlog.info("接口输入信息：" + JSON.toJSONString(hashMap2));
/*  81 */             newlog.info("接口返回值：" + JSON.toJSONString(map4));
/*     */             
/*  83 */             Map map5 = SyncUtil.handleResult2File(paramMap, mappingBean1, list, "4", paramInt1, paramInt2, map4);
/*  84 */             SynLogUtil.WriteLog2File(map5, Util.getIntValue("4"));
/*     */           }  break;
/*     */         } 
/*  87 */         list2 = syncDataByDB.getHrData(str4, mappingBean1);
/*     */         
/*  89 */         for (Map<String, String> map : (Iterable<Map<String, String>>)list2) {
/*  90 */           if (map.get("password") != null) {
/*  91 */             if (paramSettingConfigBean.getPwdSyncType().equalsIgnoreCase("1")) {
/*  92 */               map.put("password", Util.getEncrypt(map.get("password")));
/*     */             }
/*  94 */             bool = false;
/*     */           } 
/*     */         } 
/*  97 */         if (bool)
/*  98 */           hashMap1.put("password", "normal"); 
/*  99 */         hashMap2.put("datas", list2);
/* 100 */         list1 = list2;
/* 101 */         map1 = ((HrmSynCommonServiceImpl)ServiceUtil.getService(HrmSynCommonServiceImpl.class, user)).synResource(hashMap2, user);
/* 102 */         newlog.info("接口输入信息：" + JSON.toJSONString(hashMap2));
/* 103 */         newlog.info("接口返回值：" + JSON.toJSONString(map1));
/*     */         
/* 105 */         map2 = SyncUtil.handleResult2File(paramMap, mappingBean1, list1, "4", paramInt1, paramInt2, map1);
/* 106 */         SynLogUtil.WriteLog2File(map2, Util.getIntValue("4"));
/*     */         break;
/*     */       
/*     */       case "2":
/* 110 */         synDataByWebservice = new SynDataByWebservice(paramSettingConfigBean);
/* 111 */         list3 = synDataByWebservice.getHrData(str4, mappingBean1);
/*     */         
/* 113 */         for (Map<String, String> map : (Iterable<Map<String, String>>)list3) {
/* 114 */           if (map.get("password") != null) {
/* 115 */             if (paramSettingConfigBean.getPwdSyncType().equalsIgnoreCase("1")) {
/* 116 */               map.put("password", Util.getEncrypt(map.get("password")));
/*     */             }
/* 118 */             bool = false;
/*     */           } 
/*     */         } 
/* 121 */         if (bool)
/* 122 */           hashMap1.put("password", "normal"); 
/* 123 */         hashMap2.put("datas", list3);
/* 124 */         list1 = list3;
/* 125 */         map1 = ((HrmSynCommonServiceImpl)ServiceUtil.getService(HrmSynCommonServiceImpl.class, user)).synResource(hashMap2, user);
/* 126 */         newlog.info("接口输入信息：" + JSON.toJSONString(hashMap2));
/* 127 */         newlog.info("接口返回值：" + JSON.toJSONString(map1));
/*     */         
/* 129 */         map3 = SyncUtil.handleResult2File(paramMap, mappingBean1, list1, "4", paramInt1, paramInt2, map1);
/* 130 */         SynLogUtil.WriteLog2File(map3, Util.getIntValue("4"));
/*     */         break;
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 137 */     (new HrmSynUtil()).synHrmResourceRelation(str2, mappingBean1.getKey_oa(), user);
/* 138 */     SyncUtil.updateSyncData("4", str3);
/* 139 */     newlog.info("******************************************同步人员结束******************************************");
/* 140 */     return paramMap; }
/*     */ 
/*     */ 
/*     */   
/*     */   public static void main(String[] paramArrayOfString) {
/* 145 */     User user = new User();
/*     */ 
/*     */ 
/*     */     
/* 149 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 150 */     hashMap1.put("keyword", "outkey");
/* 151 */     hashMap1.put("subcompany", "outkey");
/* 152 */     hashMap1.put("department", "outkey");
/* 153 */     hashMap1.put("jobtitle", "jobtitlename");
/* 154 */     hashMap1.put("seclevel", "normal");
/*     */ 
/*     */     
/* 157 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*     */     
/* 159 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*     */     
/* 161 */     hashMap2.put("departmentid", "2066614");
/* 162 */     hashMap2.put("lastname", "刘炸炸");
/* 163 */     hashMap2.put("email", "<EMAIL>");
/* 164 */     hashMap2.put("jobtitle", "Field Product Manager");
/* 165 */     hashMap2.put("loginid", "RS40031");
/* 166 */     hashMap2.put("status", "1");
/*     */     
/* 168 */     HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 169 */     hashMap3.put("field1", "aaa");
/* 170 */     hashMap3.put("field2", "bbb");
/* 171 */     hashMap3.put("field3", "ccc");
/* 172 */     hashMap2.put("cusFieldInfo", hashMap3);
/* 173 */     arrayList.add(hashMap2);
/*     */     
/* 175 */     HashMap<Object, Object> hashMap4 = new HashMap<>();
/* 176 */     String str = Util.null2String(UUID.randomUUID());
/* 177 */     hashMap4.put("taskId", str);
/* 178 */     hashMap4.put("ruleparam", hashMap1);
/* 179 */     hashMap4.put("datas", arrayList);
/*     */ 
/*     */     
/* 182 */     Map map = ((HrmSynCommonServiceImpl)ServiceUtil.getService(HrmSynCommonServiceImpl.class, user)).synResource(hashMap4, user);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 188 */     (new HrmSynUtil()).synHrmResourceRelation(str, "outkey", user);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/syncHrmData/synchronization/SyncHrmresource.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */