/*     */ package weaver.integration.syncHrmData.synchronization;
/*     */ 
/*     */ import com.alibaba.fastjson.JSON;
/*     */ import com.engine.common.service.impl.HrmSynCommonServiceImpl;
/*     */ import com.engine.common.util.ServiceUtil;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.UUID;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ import weaver.integration.syncHrmData.Handler.impl.SynDataByWebservice;
/*     */ import weaver.integration.syncHrmData.Handler.impl.SyncDataByDB;
/*     */ import weaver.integration.syncHrmData.config.MappingBean;
/*     */ import weaver.integration.syncHrmData.config.MappingBeanExecuter;
/*     */ import weaver.integration.syncHrmData.config.SettingConfigBean;
/*     */ import weaver.integration.syncHrmData.log.SynLogUtil;
/*     */ import weaver.integration.syncHrmData.util.SyncUtil;
/*     */ 
/*     */ public class SyncJobTitle
/*     */ {
/*  26 */   private static Logger newlog = LoggerFactory.getLogger(SyncJobTitle.class); public static Map<String, Object> synJobTitles(Map<String, Object> paramMap, SettingConfigBean paramSettingConfigBean, int paramInt1, int paramInt2) throws Exception { Map map1; SyncDataByDB syncDataByDB; int i; char c; List list1; SynDataByWebservice synDataByWebservice; Map map2;
/*     */     List list2;
/*     */     Map map3;
/*  29 */     String str1 = paramSettingConfigBean.getIntetype();
/*  30 */     String str2 = UUID.randomUUID() + "";
/*  31 */     String str3 = TimeUtil.getCurrentDateString();
/*  32 */     String str4 = SyncUtil.getIncrementDate("3");
/*  33 */     User user = SyncUtil.getUser();
/*  34 */     MappingBean mappingBean = MappingBeanExecuter.getSetMap("3");
/*  35 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  36 */     hashMap1.put("keyword", mappingBean.getKey_oa());
/*  37 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  38 */     hashMap2.put("taskId", str2);
/*  39 */     hashMap2.put("ruleparam", hashMap1);
/*  40 */     ArrayList arrayList1 = new ArrayList();
/*  41 */     paramMap.put("3", arrayList1);
/*  42 */     HashMap<Object, Object> hashMap3 = new HashMap<>();
/*  43 */     newlog.info("******************************************同步岗位开始******************************************");
/*  44 */     ArrayList arrayList2 = new ArrayList();
/*  45 */     switch (str1) {
/*     */       case "1":
/*  47 */         syncDataByDB = new SyncDataByDB();
/*  48 */         i = syncDataByDB.getJobTitleTotalCnt(str4);
/*  49 */         c = 'Ϩ';
/*  50 */         newlog.info("当前分页数为：" + c);
/*  51 */         if (i > c) {
/*  52 */           int j = i / c;
/*  53 */           j = (i % c == 0) ? j : (j + 1);
/*  54 */           for (byte b = 0; b < j; b++) {
/*  55 */             List list = syncDataByDB.getJobTitleData(str4, b, c, i, mappingBean);
/*  56 */             if (list.isEmpty()) {
/*  57 */               newlog.error("无岗位数据可同步");
/*     */               break;
/*     */             } 
/*  60 */             hashMap2.put("datas", list);
/*     */             
/*  62 */             Map map4 = ((HrmSynCommonServiceImpl)ServiceUtil.getService(HrmSynCommonServiceImpl.class, user)).synJobtitle(hashMap2, user);
/*  63 */             newlog.info("接口输入信息：" + JSON.toJSONString(hashMap2));
/*  64 */             newlog.info("接口返回值：" + JSON.toJSONString(map4));
/*     */             
/*  66 */             Map map5 = SyncUtil.handleResult2File(paramMap, mappingBean, list, "3", paramInt1, paramInt2, map4);
/*  67 */             SynLogUtil.WriteLog2File(map5, Util.getIntValue("3"));
/*     */           }  break;
/*     */         } 
/*  70 */         list1 = syncDataByDB.getJobTitleData(str4, mappingBean);
/*  71 */         if (list1.isEmpty()) {
/*  72 */           newlog.error("无岗位数据可同步");
/*     */           break;
/*     */         } 
/*  75 */         hashMap2.put("datas", list1);
/*     */         
/*  77 */         map1 = ((HrmSynCommonServiceImpl)ServiceUtil.getService(HrmSynCommonServiceImpl.class, user)).synJobtitle(hashMap2, user);
/*  78 */         newlog.info("接口输入信息：" + JSON.toJSONString(hashMap2));
/*  79 */         newlog.info("接口返回值：" + JSON.toJSONString(map1));
/*     */         
/*  81 */         map2 = SyncUtil.handleResult2File(paramMap, mappingBean, list1, "3", paramInt1, paramInt2, map1);
/*  82 */         SynLogUtil.WriteLog2File(map2, Util.getIntValue("3"));
/*     */         break;
/*     */       
/*     */       case "2":
/*  86 */         synDataByWebservice = new SynDataByWebservice(paramSettingConfigBean);
/*  87 */         list2 = synDataByWebservice.getJobTitleData(str4, mappingBean);
/*  88 */         if (list2.isEmpty()) {
/*  89 */           newlog.error("无岗位数据可同步");
/*     */           break;
/*     */         } 
/*  92 */         hashMap2.put("datas", list2);
/*     */         
/*  94 */         map1 = ((HrmSynCommonServiceImpl)ServiceUtil.getService(HrmSynCommonServiceImpl.class, user)).synJobtitle(hashMap2, user);
/*  95 */         newlog.info("接口输入信息：" + JSON.toJSONString(hashMap2));
/*  96 */         newlog.info("接口返回值：" + JSON.toJSONString(map1));
/*     */         
/*  98 */         map3 = SyncUtil.handleResult2File(paramMap, mappingBean, list2, "3", paramInt1, paramInt2, map1);
/*  99 */         SynLogUtil.WriteLog2File(map3, Util.getIntValue("3"));
/*     */         break;
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 106 */     SyncUtil.updateSyncData("3", str3);
/* 107 */     newlog.info("******************************************同步岗位结束******************************************");
/* 108 */     return paramMap; }
/*     */ 
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/syncHrmData/synchronization/SyncJobTitle.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */