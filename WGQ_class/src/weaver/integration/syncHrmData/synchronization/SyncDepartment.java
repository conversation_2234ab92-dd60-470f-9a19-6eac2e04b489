/*    */ package weaver.integration.syncHrmData.synchronization;
/*    */ 
/*    */ import com.alibaba.fastjson.JSON;
/*    */ import com.engine.common.service.impl.HrmSynCommonServiceImpl;
/*    */ import com.engine.common.util.HrmSynUtil;
/*    */ import com.engine.common.util.ServiceUtil;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import java.util.UUID;
/*    */ import weaver.general.TimeUtil;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ import weaver.integration.logging.Logger;
/*    */ import weaver.integration.logging.LoggerFactory;
/*    */ import weaver.integration.syncHrmData.Handler.impl.SynDataByWebservice;
/*    */ import weaver.integration.syncHrmData.Handler.impl.SyncDataByDB;
/*    */ import weaver.integration.syncHrmData.config.MappingBean;
/*    */ import weaver.integration.syncHrmData.config.MappingBeanExecuter;
/*    */ import weaver.integration.syncHrmData.config.SettingConfigBean;
/*    */ import weaver.integration.syncHrmData.log.SynLogUtil;
/*    */ import weaver.integration.syncHrmData.util.SyncUtil;
/*    */ 
/*    */ public class SyncDepartment {
/* 26 */   private static Logger newlog = LoggerFactory.getLogger(SyncDepartment.class); public static Map<String, Object> synDepartment(Map<String, Object> paramMap, SettingConfigBean paramSettingConfigBean, int paramInt1, int paramInt2) throws Exception { Map map1; List list1; SyncDataByDB syncDataByDB; int i; char c; List list2; SynDataByWebservice synDataByWebservice; Map map2;
/*    */     List list3;
/*    */     Map map3;
/* 29 */     String str1 = paramSettingConfigBean.getIntetype();
/* 30 */     String str2 = UUID.randomUUID() + "";
/* 31 */     String str3 = TimeUtil.getCurrentDateString();
/* 32 */     String str4 = SyncUtil.getIncrementDate("2");
/* 33 */     User user = SyncUtil.getUser();
/* 34 */     MappingBean mappingBean1 = MappingBeanExecuter.getSetMap("2");
/* 35 */     MappingBean mappingBean2 = MappingBeanExecuter.getSetMap("1");
/* 36 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 37 */     hashMap1.put("keyword", mappingBean1.getKey_oa());
/* 38 */     hashMap1.put("subcompany", mappingBean2.getKey_oa());
/* 39 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 40 */     hashMap2.put("taskId", str2);
/* 41 */     hashMap2.put("ruleparam", hashMap1);
/* 42 */     HashMap<Object, Object> hashMap3 = new HashMap<>();
/* 43 */     newlog.info("******************************************同步部门开始******************************************");
/* 44 */     ArrayList arrayList = new ArrayList();
/* 45 */     switch (str1) {
/*    */       case "1":
/* 47 */         syncDataByDB = new SyncDataByDB();
/* 48 */         i = syncDataByDB.getDepartmentTotalCnt(str4);
/* 49 */         c = 'Ϩ';
/* 50 */         newlog.info("当前分页数为：" + c);
/* 51 */         if (i > c) {
/* 52 */           int j = i / c;
/* 53 */           j = (i % c == 0) ? j : (j + 1);
/* 54 */           for (byte b = 0; b < j; b++) {
/* 55 */             List list = syncDataByDB.getDepartmentData(str4, b, c, i, mappingBean1);
/* 56 */             hashMap2.put("datas", list);
/* 57 */             Map map4 = ((HrmSynCommonServiceImpl)ServiceUtil.getService(HrmSynCommonServiceImpl.class, user)).synDepartment(hashMap2, user);
/* 58 */             newlog.error("接口输入信息为：" + JSON.toJSONString(hashMap2));
/* 59 */             newlog.error("接口返回值为：" + JSON.toJSONString(map4));
/*    */             
/* 61 */             Map map5 = SyncUtil.handleResult2File(paramMap, mappingBean1, list, "2", paramInt1, paramInt2, map4);
/* 62 */             SynLogUtil.WriteLog2File(map5, Util.getIntValue("2"));
/*    */           }  break;
/*    */         } 
/* 65 */         list2 = syncDataByDB.getDepartmentData(str4, mappingBean1);
/* 66 */         hashMap2.put("datas", list2);
/* 67 */         list1 = list2;
/* 68 */         map1 = ((HrmSynCommonServiceImpl)ServiceUtil.getService(HrmSynCommonServiceImpl.class, user)).synDepartment(hashMap2, user);
/* 69 */         newlog.error("接口输入信息为：" + JSON.toJSONString(hashMap2));
/* 70 */         newlog.error("接口返回值为：" + JSON.toJSONString(map1));
/*    */         
/* 72 */         map2 = SyncUtil.handleResult2File(paramMap, mappingBean1, list1, "2", paramInt1, paramInt2, map1);
/* 73 */         SynLogUtil.WriteLog2File(map2, Util.getIntValue("2"));
/*    */         break;
/*    */       
/*    */       case "2":
/* 77 */         synDataByWebservice = new SynDataByWebservice(paramSettingConfigBean);
/* 78 */         list3 = synDataByWebservice.getDepartmentData(str4, mappingBean1);
/* 79 */         hashMap2.put("datas", list3);
/* 80 */         list1 = list3;
/* 81 */         map1 = ((HrmSynCommonServiceImpl)ServiceUtil.getService(HrmSynCommonServiceImpl.class, user)).synDepartment(hashMap2, user);
/* 82 */         newlog.error("接口输入信息为：" + JSON.toJSONString(hashMap2));
/* 83 */         newlog.error("接口返回值为：" + JSON.toJSONString(map1));
/*    */         
/* 85 */         map3 = SyncUtil.handleResult2File(paramMap, mappingBean1, list1, "2", paramInt1, paramInt2, map1);
/* 86 */         SynLogUtil.WriteLog2File(map3, Util.getIntValue("2"));
/*    */         break;
/*    */     } 
/*    */ 
/*    */ 
/*    */ 
/*    */     
/* 93 */     (new HrmSynUtil()).synDepartmentRelation(str2, mappingBean1.getKey_oa(), user);
/* 94 */     SyncUtil.updateSyncData("2", str3);
/* 95 */     newlog.info("******************************************同步部门结束******************************************");
/* 96 */     return paramMap; }
/*    */ 
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/syncHrmData/synchronization/SyncDepartment.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */