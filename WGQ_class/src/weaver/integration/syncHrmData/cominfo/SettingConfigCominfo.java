/*     */ package weaver.integration.syncHrmData.cominfo;
/*     */ 
/*     */ import weaver.cache.CacheBase;
/*     */ import weaver.cache.CacheColumn;
/*     */ import weaver.cache.CacheColumnType;
/*     */ import weaver.cache.PKColumn;
/*     */ 
/*     */ public class SettingConfigCominfo
/*     */   extends CacheBase {
/*     */   private static final long serialVersionUID = 7513768830046427681L;
/*  11 */   protected static String TABLE_NAME = "hrsyncset";
/*  12 */   protected static String TABLE_WHERE = "";
/*  13 */   protected static String TABLE_ORDER = null;
/*     */   
/*     */   @PKColumn(type = CacheColumnType.NUMBER)
/*  16 */   protected static String PK_NAME = "id";
/*     */   
/*     */   @CacheColumn(name = "isuselhr")
/*     */   protected static int isuselhr;
/*     */   
/*     */   @CacheColumn(name = "hourTime")
/*     */   protected static int hourTime;
/*     */   
/*     */   @CacheColumn(name = "intetype")
/*     */   protected static int intetype;
/*     */   
/*     */   @CacheColumn(name = "dbsource")
/*     */   protected static int dbsource;
/*     */   
/*     */   @CacheColumn(name = "webserviceurl")
/*     */   protected static int webserviceurl;
/*     */   
/*     */   @CacheColumn(name = "invoketype")
/*     */   protected static int invoketype;
/*     */   
/*     */   @CacheColumn(name = "customparams")
/*     */   protected static int customparams;
/*     */   
/*     */   @CacheColumn(name = "custominterface")
/*     */   protected static int custominterface;
/*     */   
/*     */   @CacheColumn(name = "hrmethod")
/*     */   protected static int hrmethod;
/*     */   
/*     */   @CacheColumn(name = "TimeModul")
/*     */   protected static int TimeModul;
/*     */   
/*     */   @CacheColumn(name = "Frequency")
/*     */   protected static int Frequency;
/*     */   
/*     */   @CacheColumn(name = "frequencyy")
/*     */   protected static int frequencyy;
/*     */   
/*     */   @CacheColumn(name = "createType")
/*     */   protected static int createType;
/*     */   
/*     */   @CacheColumn(name = "createTime")
/*     */   protected static int createTime;
/*     */   
/*     */   @CacheColumn(name = "depttable")
/*     */   protected static int depttable;
/*     */   
/*     */   @CacheColumn(name = "subcomtable")
/*     */   protected static int subcomtable;
/*     */   
/*     */   @CacheColumn(name = "jobtable")
/*     */   protected static int jobtable;
/*     */   
/*     */   @CacheColumn(name = "hrmtable")
/*     */   protected static int hrmtable;
/*     */   
/*     */   @CacheColumn(name = "jobmothod")
/*     */   protected static int jobmothod;
/*     */   
/*     */   @CacheColumn(name = "jobparam")
/*     */   protected static int jobparam;
/*     */   
/*     */   @CacheColumn(name = "deptmothod")
/*     */   protected static int deptmothod;
/*     */   
/*     */   @CacheColumn(name = "deptparam")
/*     */   protected static int deptparam;
/*     */   
/*     */   @CacheColumn(name = "subcommothod")
/*     */   protected static int subcommothod;
/*     */   
/*     */   @CacheColumn(name = "subcomparam")
/*     */   protected static int subcomparam;
/*     */   
/*     */   @CacheColumn(name = "hrmmethod")
/*     */   protected static int hrmmethod;
/*     */   
/*     */   @CacheColumn(name = "hrmparam")
/*     */   protected static int hrmparam;
/*     */   
/*     */   @CacheColumn(name = "defaultPwd")
/*     */   protected static int defaultPwd;
/*     */   
/*     */   @CacheColumn(name = "pwdSyncType")
/*     */   protected static int pwdSyncType;
/*     */   
/*     */   @CacheColumn(name = "subcomouternew")
/*     */   protected static int subcomouternew;
/*     */   
/*     */   @CacheColumn(name = "deptouternew")
/*     */   protected static int deptouternew;
/*     */   
/*     */   @CacheColumn(name = "jobouternew")
/*     */   protected static int jobouternew;
/*     */   
/*     */   @CacheColumn(name = "hrmouternew")
/*     */   protected static int hrmouternew;
/*     */   
/*     */   @CacheColumn(name = "issynrtx")
/*     */   protected static int issynrtx;
/*     */   
/*     */   @CacheColumn(name = "lang_")
/*     */   protected static int lang_;
/*     */   
/*     */   @CacheColumn(name = "after_clazz_")
/*     */   protected static int after_clazz_;
/*     */   
/*     */   public String getId() {
/* 124 */     return (String)getRowValue(0);
/*     */   }
/*     */   
/*     */   public String getIsuselhr() {
/* 128 */     return (String)getRowValue(isuselhr);
/*     */   }
/*     */   
/*     */   public String getHourTime() {
/* 132 */     return (String)getRowValue(hourTime);
/*     */   }
/*     */   
/*     */   public String getIntetype() {
/* 136 */     return (String)getRowValue(intetype);
/*     */   }
/*     */   
/*     */   public String getDbsource() {
/* 140 */     return (String)getRowValue(dbsource);
/*     */   }
/*     */   
/*     */   public String getWebserviceurl() {
/* 144 */     return (String)getRowValue(webserviceurl);
/*     */   }
/*     */   
/*     */   public String getInvoketype() {
/* 148 */     return (String)getRowValue(invoketype);
/*     */   }
/*     */   
/*     */   public String getCustomparams() {
/* 152 */     return (String)getRowValue(customparams);
/*     */   }
/*     */   
/*     */   public String getCustominterface() {
/* 156 */     return (String)getRowValue(custominterface);
/*     */   }
/*     */   
/*     */   public String getHrmethod() {
/* 160 */     return (String)getRowValue(hrmethod);
/*     */   }
/*     */   
/*     */   public String getTimeModul() {
/* 164 */     return (String)getRowValue(TimeModul);
/*     */   }
/*     */   
/*     */   public String getFrequency() {
/* 168 */     return (String)getRowValue(Frequency);
/*     */   }
/*     */   
/*     */   public String getFrequencyy() {
/* 172 */     return (String)getRowValue(frequencyy);
/*     */   }
/*     */   
/*     */   public String getCreateType() {
/* 176 */     return (String)getRowValue(createType);
/*     */   }
/*     */   
/*     */   public String getCreateTime() {
/* 180 */     return (String)getRowValue(createTime);
/*     */   }
/*     */   
/*     */   public String getDepttable() {
/* 184 */     return (String)getRowValue(depttable);
/*     */   }
/*     */   
/*     */   public String getSubcomtable() {
/* 188 */     return (String)getRowValue(subcomtable);
/*     */   }
/*     */   
/*     */   public String getJobtable() {
/* 192 */     return (String)getRowValue(jobtable);
/*     */   }
/*     */   
/*     */   public String getHrmtable() {
/* 196 */     return (String)getRowValue(hrmtable);
/*     */   }
/*     */   
/*     */   public String getJobmothod() {
/* 200 */     return (String)getRowValue(jobmothod);
/*     */   }
/*     */   
/*     */   public String getJobparam() {
/* 204 */     return (String)getRowValue(jobparam);
/*     */   }
/*     */   
/*     */   public String getDeptmothod() {
/* 208 */     return (String)getRowValue(deptmothod);
/*     */   }
/*     */   
/*     */   public String getDeptparam() {
/* 212 */     return (String)getRowValue(deptparam);
/*     */   }
/*     */   
/*     */   public String getSubcommothod() {
/* 216 */     return (String)getRowValue(subcommothod);
/*     */   }
/*     */   
/*     */   public String getSubcomparam() {
/* 220 */     return (String)getRowValue(subcomparam);
/*     */   }
/*     */   
/*     */   public String getHrmmethod() {
/* 224 */     return (String)getRowValue(hrmmethod);
/*     */   }
/*     */   
/*     */   public String getHrmparam() {
/* 228 */     return (String)getRowValue(hrmparam);
/*     */   }
/*     */   
/*     */   public String getDefaultPwd() {
/* 232 */     return (String)getRowValue(defaultPwd);
/*     */   }
/*     */   
/*     */   public String getPwdSyncType() {
/* 236 */     return (String)getRowValue(pwdSyncType);
/*     */   }
/*     */   
/*     */   public String getSubcomts() {
/* 240 */     return (String)getRowValue(subcomouternew);
/*     */   }
/*     */   
/*     */   public String getDeptts() {
/* 244 */     return (String)getRowValue(deptouternew);
/*     */   }
/*     */   
/*     */   public String getJobts() {
/* 248 */     return (String)getRowValue(jobouternew);
/*     */   }
/*     */   
/*     */   public String getHrmts() {
/* 252 */     return (String)getRowValue(hrmouternew);
/*     */   }
/*     */   
/*     */   public String getIssynrtx() {
/* 256 */     return (String)getRowValue(issynrtx);
/*     */   }
/*     */   
/*     */   public String getLang_() {
/* 260 */     return (String)getRowValue(lang_);
/*     */   }
/*     */   
/*     */   public String getAfter_clazz_() {
/* 264 */     return (String)getRowValue(after_clazz_);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public String getId(String paramString) {
/* 270 */     return (String)getValue(0, paramString);
/*     */   }
/*     */   
/*     */   public String getIsuselhr(String paramString) {
/* 274 */     return (String)getValue(isuselhr, paramString);
/*     */   }
/*     */   
/*     */   public String getHourTime(String paramString) {
/* 278 */     return (String)getValue(hourTime, paramString);
/*     */   }
/*     */   
/*     */   public String getIntetype(String paramString) {
/* 282 */     return (String)getValue(intetype, paramString);
/*     */   }
/*     */   
/*     */   public String getDbsource(String paramString) {
/* 286 */     return (String)getValue(dbsource, paramString);
/*     */   }
/*     */   
/*     */   public String getWebserviceurl(String paramString) {
/* 290 */     return (String)getValue(webserviceurl, paramString);
/*     */   }
/*     */   
/*     */   public String getInvoketype(String paramString) {
/* 294 */     return (String)getValue(invoketype, paramString);
/*     */   }
/*     */   
/*     */   public String getCustomparams(String paramString) {
/* 298 */     return (String)getValue(customparams, paramString);
/*     */   }
/*     */   
/*     */   public String getCustominterface(String paramString) {
/* 302 */     return (String)getValue(custominterface, paramString);
/*     */   }
/*     */   
/*     */   public String getHrmethod(String paramString) {
/* 306 */     return (String)getValue(hrmethod, paramString);
/*     */   }
/*     */   
/*     */   public String getTimeModul(String paramString) {
/* 310 */     return (String)getValue(TimeModul, paramString);
/*     */   }
/*     */   
/*     */   public String getFrequency(String paramString) {
/* 314 */     return (String)getValue(Frequency, paramString);
/*     */   }
/*     */   
/*     */   public String getFrequencyy(String paramString) {
/* 318 */     return (String)getValue(frequencyy, paramString);
/*     */   }
/*     */   
/*     */   public String getCreateType(String paramString) {
/* 322 */     return (String)getValue(createType, paramString);
/*     */   }
/*     */   
/*     */   public String getCreateTime(String paramString) {
/* 326 */     return (String)getValue(createTime, paramString);
/*     */   }
/*     */   
/*     */   public String getDepttable(String paramString) {
/* 330 */     return (String)getValue(depttable, paramString);
/*     */   }
/*     */   
/*     */   public String getSubcomtable(String paramString) {
/* 334 */     return (String)getValue(subcomtable, paramString);
/*     */   }
/*     */   
/*     */   public String getJobtable(String paramString) {
/* 338 */     return (String)getValue(jobtable, paramString);
/*     */   }
/*     */   
/*     */   public String getHrmtable(String paramString) {
/* 342 */     return (String)getValue(hrmtable, paramString);
/*     */   }
/*     */   
/*     */   public String getJobmothod(String paramString) {
/* 346 */     return (String)getValue(jobmothod, paramString);
/*     */   }
/*     */   
/*     */   public String getJobparam(String paramString) {
/* 350 */     return (String)getValue(jobparam, paramString);
/*     */   }
/*     */   
/*     */   public String getDeptmothod(String paramString) {
/* 354 */     return (String)getValue(deptmothod, paramString);
/*     */   }
/*     */   
/*     */   public String getDeptparam(String paramString) {
/* 358 */     return (String)getValue(deptparam, paramString);
/*     */   }
/*     */   
/*     */   public String getSubcommothod(String paramString) {
/* 362 */     return (String)getValue(subcommothod, paramString);
/*     */   }
/*     */   
/*     */   public String getSubcomparam(String paramString) {
/* 366 */     return (String)getValue(subcomparam, paramString);
/*     */   }
/*     */   
/*     */   public String getHrmmethod(String paramString) {
/* 370 */     return (String)getValue(hrmmethod, paramString);
/*     */   }
/*     */   
/*     */   public String getHrmparam(String paramString) {
/* 374 */     return (String)getValue(hrmparam, paramString);
/*     */   }
/*     */   
/*     */   public String getDefaultPwd(String paramString) {
/* 378 */     return (String)getValue(defaultPwd, paramString);
/*     */   }
/*     */   
/*     */   public String getPwdSyncType(String paramString) {
/* 382 */     return (String)getValue(pwdSyncType, paramString);
/*     */   }
/*     */   
/*     */   public String getSubcomts(String paramString) {
/* 386 */     return (String)getValue(subcomouternew, paramString);
/*     */   }
/*     */   
/*     */   public String getDeptts(String paramString) {
/* 390 */     return (String)getValue(deptouternew, paramString);
/*     */   }
/*     */   
/*     */   public String getJobts(String paramString) {
/* 394 */     return (String)getValue(jobouternew, paramString);
/*     */   }
/*     */   
/*     */   public String getHrmts(String paramString) {
/* 398 */     return (String)getValue(hrmouternew, paramString);
/*     */   }
/*     */   
/*     */   public String getIssynrtx(String paramString) {
/* 402 */     return (String)getValue(issynrtx, paramString);
/*     */   }
/*     */   
/*     */   public String getLang_(String paramString) {
/* 406 */     return (String)getValue(lang_, paramString);
/*     */   }
/*     */   
/*     */   public String getAfter_clazz_(String paramString) {
/* 410 */     return (String)getValue(after_clazz_, paramString);
/*     */   }
/*     */ 
/*     */   
/*     */   public void updateSettingConfigCache(String paramString) {
/* 415 */     updateCache(paramString);
/*     */   }
/*     */   
/*     */   public void deleteSettingConfigCache(String paramString) {
/* 419 */     deleteCache(paramString);
/*     */   }
/*     */   
/*     */   public void reloadSettingConfigCache() {
/* 423 */     removeCache();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/syncHrmData/cominfo/SettingConfigCominfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */