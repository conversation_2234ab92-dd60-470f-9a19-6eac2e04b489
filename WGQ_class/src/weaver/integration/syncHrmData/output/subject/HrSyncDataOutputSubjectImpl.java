/*    */ package weaver.integration.syncHrmData.output.subject;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.List;
/*    */ import weaver.integration.hrm.output.observer.IObserver;
/*    */ import weaver.integration.hrm.output.subject.ISubject;
/*    */ import weaver.integration.logging.Logger;
/*    */ import weaver.integration.logging.LoggerFactory;
/*    */ import weaver.integration.syncHrmData.output.bean.RecordData;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrSyncDataOutputSubjectImpl
/*    */   implements ISubject<RecordData>
/*    */ {
/* 24 */   private Logger newLog = LoggerFactory.getLogger(HrSyncDataOutputSubjectImpl.class);
/*    */   private List<IObserver<RecordData>> list;
/*    */   
/*    */   public HrSyncDataOutputSubjectImpl() {
/* 28 */     this.list = new ArrayList<>();
/*    */   }
/*    */   private RecordData oaId;
/*    */   
/*    */   public void registerObserver(IObserver<RecordData> paramIObserver) {
/* 33 */     this.list.add(paramIObserver);
/*    */   }
/*    */ 
/*    */   
/*    */   public void removeObserver(IObserver<RecordData> paramIObserver) {
/* 38 */     if (!this.list.isEmpty()) {
/* 39 */       this.list.remove(paramIObserver);
/*    */     }
/*    */   }
/*    */ 
/*    */   
/*    */   public void notifyObserver() {
/* 45 */     for (IObserver<RecordData> iObserver : this.list) {
/* 46 */       iObserver.save(this.oaId);
/*    */     }
/*    */   }
/*    */ 
/*    */   
/*    */   public void setData(RecordData paramRecordData) {
/* 52 */     this.oaId = paramRecordData;
/* 53 */     notifyObserver();
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/syncHrmData/output/subject/HrSyncDataOutputSubjectImpl.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */