/*    */ package weaver.integration.syncHrmData.output.subject.impl;
/*    */ 
/*    */ import weaver.integration.syncHrmData.output.bean.RecordData;
/*    */ import weaver.integration.syncHrmData.output.observer.HrSyncObserverService;
/*    */ import weaver.integration.syncHrmData.output.subject.IHrSyncOutputService;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrSyncDepartmentOutputServiceImpl
/*    */   implements IHrSyncOutputService<RecordData>
/*    */ {
/*    */   public boolean save(RecordData paramRecordData) {
/* 17 */     HrSyncObserverService.getdepartmentRecordDataISubject().setData(paramRecordData);
/* 18 */     return true;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/syncHrmData/output/subject/impl/HrSyncDepartmentOutputServiceImpl.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */