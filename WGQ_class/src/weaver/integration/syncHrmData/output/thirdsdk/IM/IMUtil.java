/*     */ package weaver.integration.syncHrmData.output.thirdsdk.IM;
/*     */ 
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ import weaver.integration.syncHrmData.config.SettingConfigBean;
/*     */ import weaver.integration.syncHrmData.config.SettingConfigExecuter;
/*     */ import weaver.integration.syncHrmData.output.bean.RecordData;
/*     */ import weaver.rtx.OrganisationCom;
/*     */ import weaver.rtx.OrganisationComRunnable;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class IMUtil
/*     */ {
/*  25 */   private Logger newLog = LoggerFactory.getLogger(IMUtil.class);
/*     */ 
/*     */   
/*     */   private static IMUtil imUtil;
/*     */ 
/*     */   
/*  31 */   private OrganisationCom rtxtmp = new OrganisationCom();
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  36 */   private String issynrtx = "";
/*     */ 
/*     */   
/*     */   public IMUtil() {
/*  40 */     String str1 = "select id from hrsyncset";
/*  41 */     RecordSet recordSet = new RecordSet();
/*  42 */     recordSet.executeQuery(str1, new Object[0]);
/*  43 */     String str2 = "1";
/*  44 */     if (recordSet.next()) {
/*  45 */       str2 = Util.null2String(recordSet.getString("id"), "1");
/*     */     }
/*     */     try {
/*  48 */       SettingConfigExecuter settingConfigExecuter = new SettingConfigExecuter();
/*  49 */       SettingConfigBean settingConfigBean = settingConfigExecuter.getConfig(str2);
/*  50 */       this.issynrtx = settingConfigBean.getIssynrtx();
/*  51 */     } catch (Exception exception) {
/*  52 */       this.newLog.error(exception, exception);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void SynRtx(String paramString, RecordData paramRecordData) {
/*     */     try {
/*  65 */       if (!"1".equals(this.issynrtx)) {
/*     */         return;
/*     */       }
/*  68 */       this.newLog.info("触发HR同步数据IM中，操作类型：" + paramString + "||数据：" + paramRecordData.toString());
/*  69 */       RecordSet recordSet = new RecordSet();
/*  70 */       int i = 0;
/*  71 */       if (paramString.equals("1")) {
/*  72 */         String str1 = paramRecordData.getOutKey();
/*  73 */         String str2 = paramRecordData.getOutVlue();
/*  74 */         recordSet.executeSql("select id,canceled from hrmsubcompany where " + str1 + "='" + str2 + "'");
/*  75 */         if (recordSet.next()) {
/*  76 */           i = recordSet.getInt("id");
/*  77 */           String str = Util.null2String(recordSet.getString("canceled"));
/*  78 */           if ("".equals(str) || "0".equals(str)) {
/*  79 */             this.rtxtmp.editSubCompany(i);
/*     */           } else {
/*  81 */             this.rtxtmp.deleteSubCompany(i);
/*     */           }
/*     */         
/*     */         } 
/*  85 */       } else if (paramString.equals("2")) {
/*  86 */         String str1 = paramRecordData.getOutKey();
/*  87 */         String str2 = paramRecordData.getOutVlue();
/*  88 */         recordSet.executeSql("select id,canceled from hrmdepartment where " + str1 + "='" + str2 + "'");
/*  89 */         if (recordSet.next()) {
/*  90 */           i = recordSet.getInt("id");
/*  91 */           String str = Util.null2String(recordSet.getString("canceled"));
/*  92 */           if ("".equals(str) || "0".equals(str)) {
/*  93 */             this.rtxtmp.editDepartment(i);
/*     */           } else {
/*  95 */             this.rtxtmp.deleteDepartment(i);
/*     */           } 
/*     */         } 
/*  98 */       } else if (paramString.equals("4")) {
/*  99 */         String str1 = paramRecordData.getOutKey();
/* 100 */         String str2 = paramRecordData.getOutVlue();
/* 101 */         recordSet.executeSql("select id,loginid,status from hrmresource where " + str1 + "='" + str2 + "'");
/* 102 */         if (recordSet.next()) {
/* 103 */           i = recordSet.getInt("id");
/* 104 */           String str3 = Util.null2String(recordSet.getString("loginid"));
/* 105 */           String str4 = Util.null2String(recordSet.getString("status"));
/* 106 */           String str5 = "";
/* 107 */           if ("4".equals(str4) || "5".equals(str4) || "6".equals(str4) || "7".equals(str4)) {
/* 108 */             if (this.rtxtmp.checkUser(i)) {
/* 109 */               str5 = "delete";
/*     */             } else {
/* 111 */               str5 = "";
/*     */             }
/*     */           
/* 114 */           } else if (this.rtxtmp.checkUser(i)) {
/* 115 */             str5 = "update";
/*     */           } else {
/* 117 */             str5 = "add";
/*     */           } 
/*     */           
/* 120 */           if ("add".equals(str5)) {
/* 121 */             this.rtxtmp.addUser(i);
/* 122 */           } else if ("update".equals(str5)) {
/* 123 */             this.rtxtmp.editUser(i);
/* 124 */           } else if ("delete".equals(str5)) {
/* 125 */             (new Thread((Runnable)new OrganisationComRunnable("user", "dismiss", i + "-" + str3))).start();
/*     */           } 
/*     */         } 
/*     */       } 
/* 129 */     } catch (Exception exception) {
/* 130 */       this.newLog.error("HR同步调用IM集成出现异常:", exception);
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/syncHrmData/output/thirdsdk/IM/IMUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */