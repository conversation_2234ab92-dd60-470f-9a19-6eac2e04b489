/*    */ package weaver.integration.syncHrmData.output.cache;
/*    */ 
/*    */ import weaver.cache.CacheColumn;
/*    */ import weaver.cache.CacheColumnType;
/*    */ import weaver.cache.PKColumn;
/*    */ import weaver.integration.cache.CommonCache;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrSyncObserverCache
/*    */   extends CommonCache
/*    */ {
/* 16 */   protected static String TABLE_NAME = "hrsync_observers";
/*    */ 
/*    */ 
/*    */   
/* 20 */   protected static String TABLE_WHERE = null;
/*    */ 
/*    */ 
/*    */   
/* 24 */   protected static String TABLE_ORDER = "id";
/*    */   
/*    */   @PKColumn(type = CacheColumnType.NUMBER)
/* 27 */   protected static String PK_NAME = "id";
/*    */   
/*    */   @CacheColumn
/*    */   protected static int scope;
/*    */   @CacheColumn
/*    */   protected static int type;
/*    */   @CacheColumn
/*    */   protected static int observer_clazz;
/*    */   @CacheColumn
/*    */   protected static int isopen;
/*    */   
/*    */   public String getScope() {
/* 39 */     return (String)getRowValue(scope);
/*    */   }
/*    */   public String getType() {
/* 42 */     return (String)getRowValue(type);
/*    */   }
/*    */   public String getObserver_clazz() {
/* 45 */     return (String)getRowValue(observer_clazz);
/*    */   }
/*    */   public String getIsopen() {
/* 48 */     return (String)getRowValue(isopen);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/syncHrmData/output/cache/HrSyncObserverCache.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */