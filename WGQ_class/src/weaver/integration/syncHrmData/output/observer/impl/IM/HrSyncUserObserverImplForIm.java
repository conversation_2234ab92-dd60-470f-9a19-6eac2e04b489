/*    */ package weaver.integration.syncHrmData.output.observer.impl.IM;
/*    */ 
/*    */ import weaver.integration.hrm.output.observer.IObserver;
/*    */ import weaver.integration.syncHrmData.output.bean.RecordData;
/*    */ import weaver.integration.syncHrmData.output.thirdsdk.IM.IMUtil;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrSyncUserObserverImplForIm
/*    */   implements IObserver<RecordData>
/*    */ {
/*    */   public void save(RecordData paramRecordData) {
/* 18 */     IMUtil iMUtil = new IMUtil();
/* 19 */     iMUtil.SynRtx("4", paramRecordData);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/syncHrmData/output/observer/impl/IM/HrSyncUserObserverImplForIm.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */