/*    */ package weaver.integration.syncHrmData.output.observer.impl.IM;
/*    */ 
/*    */ import weaver.integration.hrm.output.observer.IObserver;
/*    */ import weaver.integration.syncHrmData.output.bean.RecordData;
/*    */ import weaver.integration.syncHrmData.output.thirdsdk.IM.IMUtil;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class HrSyncSubcompanyObserverImplForIm
/*    */   implements IObserver<RecordData>
/*    */ {
/*    */   public void save(RecordData paramRecordData) {
/* 18 */     IMUtil iMUtil = new IMUtil();
/* 19 */     iMUtil.SynRtx("1", paramRecordData);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/syncHrmData/output/observer/impl/IM/HrSyncSubcompanyObserverImplForIm.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */