/*     */ package weaver.integration.syncHrmData.output.observer;
/*     */ 
/*     */ import java.util.concurrent.ConcurrentHashMap;
/*     */ import weaver.general.Util;
/*     */ import weaver.integration.hrm.output.observer.IObserver;
/*     */ import weaver.integration.hrm.output.subject.ISubject;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ import weaver.integration.syncHrmData.output.bean.RecordData;
/*     */ import weaver.integration.syncHrmData.output.cache.HrSyncObserverCache;
/*     */ import weaver.integration.syncHrmData.output.subject.HrSyncDataOutputSubjectImpl;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrSyncObserverService
/*     */ {
/*  27 */   private static Logger newLog = LoggerFactory.getLogger(HrSyncObserverService.class);
/*     */ 
/*     */   
/*     */   private static ConcurrentHashMap<String, Object> allObservers;
/*     */ 
/*     */ 
/*     */   
/*     */   public static void removeCache() {
/*  35 */     allObservers = null;
/*     */   }
/*     */   public static ConcurrentHashMap<String, Object> getAllObservers() {
/*  38 */     if (allObservers == null) {
/*  39 */       synchronized (HrSyncObserverService.class) {
/*  40 */         if (allObservers == null) {
/*  41 */           allObservers = new ConcurrentHashMap<>();
/*     */ 
/*     */           
/*  44 */           HrSyncDataOutputSubjectImpl hrSyncDataOutputSubjectImpl1 = new HrSyncDataOutputSubjectImpl();
/*  45 */           registerObserversByType((ISubject<RecordData>)hrSyncDataOutputSubjectImpl1, 1);
/*  46 */           HrSyncDataOutputSubjectImpl hrSyncDataOutputSubjectImpl2 = new HrSyncDataOutputSubjectImpl();
/*  47 */           registerObserversByType((ISubject<RecordData>)hrSyncDataOutputSubjectImpl2, 2);
/*  48 */           HrSyncDataOutputSubjectImpl hrSyncDataOutputSubjectImpl3 = new HrSyncDataOutputSubjectImpl();
/*  49 */           registerObserversByType((ISubject<RecordData>)hrSyncDataOutputSubjectImpl3, 3);
/*  50 */           HrSyncDataOutputSubjectImpl hrSyncDataOutputSubjectImpl4 = new HrSyncDataOutputSubjectImpl();
/*  51 */           registerObserversByType((ISubject<RecordData>)hrSyncDataOutputSubjectImpl4, 4);
/*     */           
/*  53 */           allObservers.put("observerable_subcompany", hrSyncDataOutputSubjectImpl1);
/*  54 */           allObservers.put("observerable_department", hrSyncDataOutputSubjectImpl2);
/*  55 */           allObservers.put("observerable_jobtitle", hrSyncDataOutputSubjectImpl3);
/*  56 */           allObservers.put("observerable_user", hrSyncDataOutputSubjectImpl4);
/*     */         } 
/*     */       } 
/*     */     }
/*     */ 
/*     */     
/*  62 */     return allObservers;
/*     */   }
/*     */   
/*     */   public static ISubject<RecordData> getSubcompanyRecordDataISubject() {
/*  66 */     return (ISubject<RecordData>)getAllObservers().get("observerable_subcompany");
/*     */   }
/*     */   
/*     */   public static ISubject<RecordData> getdepartmentRecordDataISubject() {
/*  70 */     return (ISubject<RecordData>)getAllObservers().get("observerable_department");
/*     */   }
/*     */   
/*     */   public static ISubject<RecordData> getjobtitleRecordDataISubject() {
/*  74 */     return (ISubject<RecordData>)getAllObservers().get("observerable_jobtitle");
/*     */   }
/*     */   
/*     */   public static ISubject<RecordData> getuserRecordDataISubject() {
/*  78 */     return (ISubject<RecordData>)getAllObservers().get("observerable_user");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean registerObserversByType(ISubject<RecordData> paramISubject, int paramInt) {
/*  89 */     HrSyncObserverCache hrSyncObserverCache = new HrSyncObserverCache();
/*  90 */     while (hrSyncObserverCache.next()) {
/*  91 */       String str1 = hrSyncObserverCache.getObserver_clazz();
/*  92 */       String str2 = hrSyncObserverCache.getIsopen();
/*  93 */       String str3 = hrSyncObserverCache.getType();
/*     */       try {
/*  95 */         newLog.error("遍历观察者：" + str1);
/*  96 */         if ("1".equals(str2) && Util.getIntValue(str3, -1) == paramInt) {
/*  97 */           IObserver iObserver = (IObserver)Class.forName(str1).newInstance();
/*  98 */           newLog.error("注册观察者：" + str1);
/*  99 */           paramISubject.registerObserver(iObserver);
/*     */         } 
/* 101 */       } catch (Exception exception) {
/* 102 */         newLog.error("注册观察者出错：" + exception);
/*     */       } 
/*     */     } 
/* 105 */     return true;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/syncHrmData/output/observer/HrSyncObserverService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */