/*     */ package weaver.integration.syncHrmData.Handler.impl;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Iterator;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.Set;
/*     */ import java.util.regex.Matcher;
/*     */ import java.util.regex.Pattern;
/*     */ import weaver.general.SoapService;
/*     */ import weaver.general.Util;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ import weaver.integration.syncHrmData.Handler.SyncDataHandler;
/*     */ import weaver.integration.syncHrmData.config.MappingBean;
/*     */ import weaver.integration.syncHrmData.config.SettingConfigBean;
/*     */ import weaver.integration.syncHrmData.trans.FieldValueTrans;
/*     */ import weaver.integration.syncHrmData.util.SyncStringUtil;
/*     */ import weaver.wsclient.bean.MethodBean;
/*     */ import weaver.wsclient.util.WSDLFacade;
/*     */ 
/*     */ public class SynDataByWebservice
/*     */   implements SyncDataHandler {
/*  25 */   private static Logger newlog = LoggerFactory.getLogger(SynDataByWebservice.class);
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  30 */   private String jobmothod = "";
/*     */ 
/*     */ 
/*     */   
/*  34 */   private String jobparam = "";
/*     */ 
/*     */ 
/*     */   
/*  38 */   private String deptmothod = "";
/*     */ 
/*     */ 
/*     */   
/*  42 */   private String deptparam = "";
/*     */ 
/*     */ 
/*     */   
/*  46 */   private String subcommothod = "";
/*     */ 
/*     */ 
/*     */   
/*  50 */   private String subcomparam = "";
/*     */ 
/*     */ 
/*     */   
/*  54 */   private String hrmmethod = "";
/*     */ 
/*     */ 
/*     */   
/*  58 */   private String hrmparam = "";
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  63 */   private Map jobvalues = new HashMap<>();
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  68 */   private Map deptvalues = new HashMap<>();
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  73 */   private Map subvalues = new HashMap<>();
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  78 */   private Map hrmvalues = new HashMap<>();
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  83 */   private String webserviceurl = "";
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public SynDataByWebservice() {}
/*     */ 
/*     */ 
/*     */   
/*     */   public SynDataByWebservice(SettingConfigBean paramSettingConfigBean) {
/*  93 */     this.webserviceurl = paramSettingConfigBean.getWebserviceurl();
/*  94 */     this.subcommothod = paramSettingConfigBean.getSubcommothod();
/*  95 */     this.deptmothod = paramSettingConfigBean.getDeptmothod();
/*  96 */     this.jobmothod = paramSettingConfigBean.getJobmothod();
/*  97 */     this.hrmmethod = paramSettingConfigBean.getHrmmethod();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<Map<String, Object>> getSubcompanyData(String paramString, MappingBean paramMappingBean) throws Exception {
/* 108 */     Map<String, String> map1 = paramMappingBean.getFieldMap();
/* 109 */     Map<String, String> map2 = paramMappingBean.getTransMap();
/* 110 */     Map<String, String> map3 = paramMappingBean.getTranTypeMap();
/* 111 */     getParamMap("1");
/* 112 */     return getDataFromWS(paramString, this.webserviceurl, this.subcommothod, this.subvalues, map1, map2, map3);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<Map<String, Object>> getDepartmentData(String paramString, MappingBean paramMappingBean) throws Exception {
/* 123 */     Map<String, String> map1 = paramMappingBean.getFieldMap();
/* 124 */     Map<String, String> map2 = paramMappingBean.getTransMap();
/* 125 */     Map<String, String> map3 = paramMappingBean.getTranTypeMap();
/* 126 */     getParamMap("2");
/* 127 */     return getDataFromWS(paramString, this.webserviceurl, this.deptmothod, this.deptvalues, map1, map2, map3);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<Map<String, Object>> getJobTitleData(String paramString, MappingBean paramMappingBean) throws Exception {
/* 138 */     Map<String, String> map1 = paramMappingBean.getFieldMap();
/* 139 */     Map<String, String> map2 = paramMappingBean.getTransMap();
/* 140 */     Map<String, String> map3 = paramMappingBean.getTranTypeMap();
/* 141 */     getParamMap("3");
/* 142 */     return getDataFromWS(paramString, this.webserviceurl, this.jobmothod, this.jobvalues, map1, map2, map3);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<Map<String, Object>> getHrData(String paramString, MappingBean paramMappingBean) throws Exception {
/* 154 */     Map<String, String> map1 = paramMappingBean.getFieldMap();
/* 155 */     Map<String, String> map2 = paramMappingBean.getTransMap();
/* 156 */     Map<String, String> map3 = paramMappingBean.getTranTypeMap();
/* 157 */     getParamMap("4");
/* 158 */     return getDataFromWS(paramString, this.webserviceurl, this.hrmmethod, this.hrmvalues, map1, map2, map3);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<Map<String, Object>> getDataFromWS(String paramString1, String paramString2, String paramString3, Map<String, String> paramMap1, Map<String, String> paramMap2, Map<String, String> paramMap3, Map<String, String> paramMap4) throws Exception {
/* 173 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 174 */     WSDLFacade wSDLFacade = new WSDLFacade();
/* 175 */     paramString2 = wSDLFacade.getWebserviceUrlFromDB(paramString2);
/* 176 */     MethodBean methodBean = wSDLFacade.getWSMethodFromDB(paramString3);
/* 177 */     paramString3 = methodBean.getMethodname();
/* 178 */     String str1 = methodBean.getMethodreturntype();
/* 179 */     newlog.error("getDataFromWS webserviceurl : " + paramString2 + " mothod : " + paramString3 + " pvalues : " + paramMap1 + " returntype : " + str1);
/* 180 */     String str2 = SoapService.serviceSend(paramString2, paramString3, paramMap1, str1);
/* 181 */     newlog.error("result : " + str2);
/* 182 */     if (!str2.equals("")) {
/* 183 */       Set<String> set = paramMap2.keySet();
/* 184 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 185 */       int i = 0;
/* 186 */       for (Iterator<String> iterator = set.iterator(); iterator.hasNext(); ) {
/* 187 */         String str3 = Util.null2String(iterator.next());
/* 188 */         String str4 = Util.null2String(paramMap2.get(str3));
/* 189 */         List<String> list = SoapService.parseServiceResult(str2, str4);
/* 190 */         if (null != list) {
/* 191 */           i = list.size();
/* 192 */           for (byte b = 0; b < list.size(); b++) {
/* 193 */             String str5 = Util.null2String(list.get(b));
/* 194 */             String str6 = Util.null2String(paramMap3.get(SyncStringUtil.getTransqlMapKey(str3, str4)));
/* 195 */             String str7 = Util.null2String(paramMap4.get(SyncStringUtil.getTransqlMapKey(str3, str4)), "0");
/* 196 */             str7 = "".equals(str7) ? "0" : str7;
/* 197 */             if (!"".equals(str6)) {
/* 198 */               str5 = FieldValueTrans.getTranValue(str7, str6, str5, "", str3, str4);
/*     */             }
/* 200 */             list.set(b, str5);
/*     */           } 
/* 202 */           hashMap.put(str4, list);
/*     */         } 
/*     */       } 
/* 205 */       if (i > 0) {
/* 206 */         for (byte b = 0; b < i; b++) {
/*     */           try {
/* 208 */             HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 209 */             HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 210 */             for (Iterator<String> iterator1 = set.iterator(); iterator1.hasNext(); ) {
/* 211 */               String str3 = Util.null2String(iterator1.next());
/* 212 */               String str4 = Util.null2String(paramMap2.get(str3));
/* 213 */               List<String> list = (List)hashMap.get(str4);
/* 214 */               String str5 = Util.null2String(list.get(b));
/*     */               
/* 216 */               str3 = str3.toLowerCase();
/* 217 */               if (!isCustomField(str3)) {
/* 218 */                 hashMap1.put(str3, str5); continue;
/* 219 */               }  hashMap2.put(str3.replace("#cus#", ""), str5);
/*     */             } 
/* 221 */             if (!hashMap2.isEmpty())
/* 222 */               hashMap1.put("cusFieldInfo", hashMap2); 
/* 223 */             arrayList.add(hashMap1);
/* 224 */           } catch (Exception exception) {
/* 225 */             return null;
/*     */           } 
/*     */         } 
/*     */       }
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 233 */     return (List)arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isCustomField(String paramString) {
/* 242 */     Pattern pattern = Pattern.compile("^#cus#.*");
/* 243 */     Matcher matcher = pattern.matcher(paramString);
/* 244 */     if (matcher.find())
/* 245 */       return true; 
/* 246 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void getParamMap(String paramString) {
/* 255 */     WSDLFacade wSDLFacade = new WSDLFacade();
/* 256 */     if (paramString.equals("1")) {
/* 257 */       this.subvalues = wSDLFacade.getWSMethodParamValueFromDB(this.subcommothod, paramString, "");
/* 258 */     } else if (paramString.equals("2")) {
/* 259 */       this.deptvalues = wSDLFacade.getWSMethodParamValueFromDB(this.deptmothod, paramString, "");
/* 260 */     } else if (paramString.equals("3")) {
/* 261 */       this.jobvalues = wSDLFacade.getWSMethodParamValueFromDB(this.jobmothod, paramString, "");
/* 262 */     } else if (paramString.equals("4")) {
/* 263 */       this.hrmvalues = wSDLFacade.getWSMethodParamValueFromDB(this.hrmmethod, paramString, "");
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public int getSubcompanyTotalCnt(String paramString) throws Exception {
/* 269 */     return 0;
/*     */   }
/*     */ 
/*     */   
/*     */   public int getDepartmentTotalCnt(String paramString) throws Exception {
/* 274 */     return 0;
/*     */   }
/*     */ 
/*     */   
/*     */   public int getJobTitleTotalCnt(String paramString) throws Exception {
/* 279 */     return 0;
/*     */   }
/*     */ 
/*     */   
/*     */   public int getHrTotalCnt(String paramString) throws Exception {
/* 284 */     return 0;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<Map<String, Object>> getSubcompanyData(String paramString, int paramInt1, int paramInt2, int paramInt3, MappingBean paramMappingBean) throws Exception {
/* 291 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public List<Map<String, Object>> getDepartmentData(String paramString, int paramInt1, int paramInt2, int paramInt3, MappingBean paramMappingBean) throws Exception {
/* 296 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public List<Map<String, Object>> getJobTitleData(String paramString, int paramInt1, int paramInt2, int paramInt3, MappingBean paramMappingBean) throws Exception {
/* 301 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public List<Map<String, Object>> getHrData(String paramString, int paramInt1, int paramInt2, int paramInt3, MappingBean paramMappingBean) throws Exception {
/* 306 */     return null;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/syncHrmData/Handler/impl/SynDataByWebservice.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */