/*     */ package weaver.integration.syncHrmData.Handler.impl;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Iterator;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.Set;
/*     */ import java.util.regex.Matcher;
/*     */ import java.util.regex.Pattern;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.conn.RecordSetDataSource;
/*     */ import weaver.general.Util;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ import weaver.integration.syncHrmData.Handler.SyncDataHandler;
/*     */ import weaver.integration.syncHrmData.config.MappingBean;
/*     */ import weaver.integration.syncHrmData.config.SettingConfigBean;
/*     */ import weaver.integration.syncHrmData.config.SettingConfigExecuter;
/*     */ import weaver.integration.syncHrmData.trans.FieldValueTrans;
/*     */ import weaver.integration.syncHrmData.util.SyncStringUtil;
/*     */ 
/*     */ public class SyncDataByDB implements SyncDataHandler {
/*  23 */   private static Logger newlog = LoggerFactory.getLogger(SyncDataByDB.class);
/*     */   private RecordSetDataSource syncrs;
/*  25 */   private String dbsource = "";
/*  26 */   private String dbType = "";
/*     */   
/*  28 */   private String subcomts = "";
/*  29 */   private String subcomtable = "";
/*     */   
/*  31 */   private String deptts = "";
/*  32 */   private String depttable = "";
/*     */   
/*  34 */   private String jobts = "";
/*  35 */   private String jobtable = "";
/*     */   
/*  37 */   private String hrmts = "";
/*  38 */   private String hrmtable = "";
/*     */ 
/*     */ 
/*     */   
/*     */   public SyncDataByDB() {
/*  43 */     String str1 = "select id from hrsyncset";
/*  44 */     RecordSet recordSet = new RecordSet();
/*  45 */     recordSet.executeQuery(str1, new Object[0]);
/*  46 */     String str2 = "1";
/*  47 */     if (recordSet.next()) {
/*  48 */       str2 = Util.null2String(recordSet.getString("id"), "1");
/*     */     }
/*  50 */     SettingConfigExecuter settingConfigExecuter = new SettingConfigExecuter();
/*  51 */     SettingConfigBean settingConfigBean = settingConfigExecuter.getConfig(str2);
/*  52 */     this.subcomts = settingConfigBean.getSubcomouternew();
/*  53 */     this.subcomtable = settingConfigBean.getSubcomtable();
/*  54 */     this.deptts = settingConfigBean.getDeptouternew();
/*  55 */     this.depttable = settingConfigBean.getDepttable();
/*  56 */     this.jobtable = settingConfigBean.getJobtable();
/*  57 */     this.jobts = settingConfigBean.getJobouternew();
/*  58 */     this.hrmts = settingConfigBean.getHrmouternew();
/*  59 */     this.hrmtable = settingConfigBean.getHrmtable();
/*  60 */     this.dbsource = settingConfigBean.getDbsource();
/*     */     
/*  62 */     this.syncrs = new RecordSetDataSource(this.dbsource);
/*  63 */     this.dbType = this.syncrs.getDBType();
/*     */   }
/*     */ 
/*     */   
/*     */   public SyncDataByDB(SettingConfigBean paramSettingConfigBean) {
/*  68 */     this.syncrs = new RecordSetDataSource(this.dbsource);
/*  69 */     this.dbType = this.syncrs.getDBType();
/*  70 */     this.subcomts = paramSettingConfigBean.getSubcomouternew();
/*  71 */     this.subcomtable = paramSettingConfigBean.getSubcomtable();
/*  72 */     this.deptts = paramSettingConfigBean.getDeptouternew();
/*  73 */     this.depttable = paramSettingConfigBean.getDepttable();
/*  74 */     this.jobtable = paramSettingConfigBean.getJobtable();
/*  75 */     this.jobts = paramSettingConfigBean.getJobouternew();
/*  76 */     this.hrmts = paramSettingConfigBean.getHrmouternew();
/*  77 */     this.hrmtable = paramSettingConfigBean.getHrmtable();
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public int getSubcompanyTotalCnt(String paramString) throws Exception {
/*  83 */     return getTotalCnt(paramString, this.subcomts, this.subcomtable);
/*     */   }
/*     */ 
/*     */   
/*     */   public int getDepartmentTotalCnt(String paramString) throws Exception {
/*  88 */     return getTotalCnt(paramString, this.deptts, this.depttable);
/*     */   }
/*     */ 
/*     */   
/*     */   public int getJobTitleTotalCnt(String paramString) throws Exception {
/*  93 */     return getTotalCnt(paramString, this.jobts, this.jobtable);
/*     */   }
/*     */ 
/*     */   
/*     */   public int getHrTotalCnt(String paramString) throws Exception {
/*  98 */     return getTotalCnt(paramString, this.hrmts, this.hrmtable);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private int getTotalCnt(String paramString1, String paramString2, String paramString3) {
/* 109 */     String str = "select count(*) from " + paramString3;
/* 110 */     if (!"".equals(paramString2)) {
/* 111 */       str = str + " where " + paramString2 + " >= '" + paramString1 + "'";
/*     */     }
/* 113 */     this.syncrs.executeSql(str);
/* 114 */     if (this.syncrs.next()) {
/* 115 */       return Util.getIntValue(this.syncrs.getString(1), 0);
/*     */     }
/* 117 */     return 0;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<Map<String, Object>> getSubcompanyData(String paramString, MappingBean paramMappingBean) throws Exception {
/* 128 */     Map<String, String> map1 = paramMappingBean.getFieldMap();
/* 129 */     Map<String, String> map2 = paramMappingBean.getTransMap();
/* 130 */     Map<String, String> map3 = paramMappingBean.getTranTypeMap();
/* 131 */     String str = "select * from " + this.subcomtable;
/* 132 */     if (!"".equals(this.subcomts)) {
/* 133 */       str = str + " where " + this.subcomts + " >= '" + paramString + "' order by " + this.subcomts;
/*     */     }
/* 135 */     this.syncrs.executeSql(str);
/* 136 */     this.syncrs.getCounts();
/* 137 */     return getDataFromSql(this.syncrs, this.dbsource, map1, map2, map3, this.subcomts);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List getDepartmentData(String paramString, MappingBean paramMappingBean) throws Exception {
/* 148 */     Map<String, String> map1 = paramMappingBean.getFieldMap();
/* 149 */     Map<String, String> map2 = paramMappingBean.getTransMap();
/* 150 */     Map<String, String> map3 = paramMappingBean.getTranTypeMap();
/* 151 */     String str1 = paramMappingBean.getParentkey();
/* 152 */     String str2 = paramMappingBean.getKey();
/* 153 */     String str3 = "select * from " + this.depttable;
/* 154 */     String str4 = "";
/* 155 */     if (!"".equals(str1)) {
/* 156 */       str4 = str4 + ("".equals(str4) ? str1 : ("," + str1));
/*     */     }
/* 158 */     if (!"".equals(str2)) {
/* 159 */       str4 = str4 + ("".equals(str4) ? str2 : ("," + str2));
/*     */     }
/* 161 */     if (!"".equals(this.deptts)) {
/* 162 */       str4 = str4 + ("".equals(str4) ? this.deptts : ("," + this.deptts));
/* 163 */       str3 = str3 + " where " + this.deptts + " >= '" + paramString + "' order by " + str4;
/*     */     } else {
/* 165 */       str3 = str3 + " order by " + str4;
/*     */     } 
/* 167 */     this.syncrs.executeSql(str3);
/* 168 */     return getDataFromSql(this.syncrs, this.dbsource, map1, map2, map3, this.deptts);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<Map<String, Object>> getJobTitleData(String paramString, MappingBean paramMappingBean) throws Exception {
/* 179 */     Map<String, String> map1 = paramMappingBean.getFieldMap();
/* 180 */     Map<String, String> map2 = paramMappingBean.getTransMap();
/* 181 */     Map<String, String> map3 = paramMappingBean.getTranTypeMap();
/* 182 */     if ("".equals(Util.null2String(this.jobtable))) {
/* 183 */       return new ArrayList<>();
/*     */     }
/* 185 */     String str = "select * from " + this.jobtable;
/* 186 */     if (!"".equals(this.jobts)) {
/* 187 */       str = str + " where " + this.jobts + " >= '" + paramString + "' order by " + this.jobts;
/*     */     }
/* 189 */     this.syncrs.executeSql(str);
/* 190 */     newlog.error("获取岗位数据 sql ：" + str);
/* 191 */     return getDataFromSql(this.syncrs, this.dbsource, map1, map2, map3, this.jobts);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<Map<String, Object>> getHrData(String paramString, MappingBean paramMappingBean) throws Exception {
/* 202 */     Map<String, String> map1 = paramMappingBean.getFieldMap();
/* 203 */     Map<String, String> map2 = paramMappingBean.getTransMap();
/* 204 */     Map<String, String> map3 = paramMappingBean.getTranTypeMap();
/* 205 */     String str1 = paramMappingBean.getParentkey();
/* 206 */     String str2 = paramMappingBean.getKey();
/* 207 */     String str3 = "select * from " + this.hrmtable;
/* 208 */     String str4 = "";
/* 209 */     if (!"".equals(str1)) {
/* 210 */       str4 = str4 + ("".equals(str4) ? str1 : ("," + str1));
/*     */     }
/* 212 */     if (!"".equals(this.hrmts)) {
/* 213 */       str4 = str4 + ("".equals(str4) ? this.hrmts : ("," + this.hrmts));
/*     */     }
/* 215 */     if (!"".equals(str2)) {
/* 216 */       str4 = str4 + ("".equals(str4) ? str2 : ("," + str2));
/*     */     }
/* 218 */     if (!"".equals(this.hrmts)) {
/* 219 */       str3 = str3 + " where " + this.hrmts + " >= '" + paramString + "' order by " + str4;
/*     */     } else {
/* 221 */       str3 = str3 + " order by " + str4;
/*     */     } 
/* 223 */     this.syncrs.executeSql(str3);
/* 224 */     return getDataFromSql(this.syncrs, this.dbsource, map1, map2, map3, this.hrmts);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<Map<String, Object>> getSubcompanyData(String paramString, int paramInt1, int paramInt2, int paramInt3, MappingBean paramMappingBean) throws Exception {
/* 239 */     Map<String, String> map1 = paramMappingBean.getFieldMap();
/* 240 */     Map<String, String> map2 = paramMappingBean.getTransMap();
/* 241 */     Map<String, String> map3 = paramMappingBean.getTranTypeMap();
/* 242 */     int i = paramInt2 * paramInt1 + 1;
/* 243 */     int j = paramInt2 * (paramInt1 + 1);
/* 244 */     if (j > paramInt3) {
/* 245 */       j = paramInt3;
/*     */     }
/* 247 */     String str1 = paramMappingBean.getKey();
/*     */     
/* 249 */     String str2 = "";
/* 250 */     String str3 = "";
/* 251 */     if (!"".equals(str1)) {
/* 252 */       str3 = str3 + ("".equals(str3) ? str1 : ("," + str1));
/*     */     }
/* 254 */     if (!"".equals(this.subcomts)) {
/* 255 */       str3 = str3 + ("".equals(str3) ? this.subcomts : ("," + this.subcomts));
/*     */     }
/* 257 */     str3 = " order by " + str3;
/*     */     
/* 259 */     if (this.dbType.equals("sqlserver")) {
/* 260 */       String str = "select * from " + this.subcomtable;
/* 261 */       if (!"".equals(this.subcomts)) {
/* 262 */         str = str + " where " + this.subcomts + " >= '" + paramString + "' ";
/*     */       }
/* 264 */       int k = paramInt2;
/* 265 */       if (j % paramInt2 != 0)
/* 266 */         k = j % paramInt2; 
/* 267 */       str2 = "select *\tfrom (select top " + k + " * from (select top " + j + " * from (" + str + ") sourcetable " + str3 + " asc )\tas temp_sum_hrmSource " + str3 + " desc ) temp_order " + str3 + " asc";
/*     */     
/*     */     }
/* 270 */     else if (this.dbType.equals("mysql")) {
/* 271 */       str2 = "select * from " + this.subcomtable;
/* 272 */       if (!"".equals(this.subcomts)) {
/* 273 */         str2 = str2 + " where " + this.subcomts + " >= '" + paramString + "' order by " + this.subcomts;
/*     */       }
/* 275 */       str2 = str2 + " limit " + (i - 1) + "," + paramInt2;
/*     */     } else {
/* 277 */       str2 = "select rownum rn,w.* from " + this.subcomtable + " w ";
/* 278 */       if (!"".equals(this.subcomts)) {
/* 279 */         str2 = str2 + " where " + this.subcomts + " >= '" + paramString + "' and rownum <= " + j;
/*     */       } else {
/* 281 */         str2 = str2 + " where rownum <=" + j;
/*     */       } 
/* 283 */       str2 = "select * from (" + str2 + ") where rn >=" + i;
/* 284 */       str2 = str2 + " " + str3;
/*     */     } 
/* 286 */     newlog.info("获取分部分页sql:" + str2);
/* 287 */     this.syncrs.executeSql(str2);
/* 288 */     return getDataFromSql(this.syncrs, this.dbsource, map1, map2, map3, this.subcomts);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<Map<String, Object>> getDepartmentData(String paramString, int paramInt1, int paramInt2, int paramInt3, MappingBean paramMappingBean) throws Exception {
/* 303 */     Map<String, String> map1 = paramMappingBean.getFieldMap();
/* 304 */     Map<String, String> map2 = paramMappingBean.getTransMap();
/* 305 */     Map<String, String> map3 = paramMappingBean.getTranTypeMap();
/* 306 */     String str1 = paramMappingBean.getParentkey();
/* 307 */     String str2 = paramMappingBean.getKey();
/* 308 */     int i = paramInt2 * paramInt1 + 1;
/* 309 */     int j = paramInt2 * (paramInt1 + 1);
/* 310 */     if (j > paramInt3) {
/* 311 */       j = paramInt3;
/*     */     }
/* 313 */     String str3 = "";
/* 314 */     String str4 = "";
/* 315 */     if (!"".equals(str1));
/*     */ 
/*     */ 
/*     */     
/* 319 */     if (!"".equals(str2)) {
/* 320 */       str4 = str4 + ("".equals(str4) ? str2 : ("," + str2));
/*     */     }
/* 322 */     if (!"".equals(this.deptts)) {
/* 323 */       str4 = str4 + ("".equals(str4) ? this.deptts : ("," + this.deptts));
/*     */     }
/*     */     
/* 326 */     if (this.dbType.equals("sqlserver")) {
/* 327 */       String str = "select * from " + this.depttable;
/* 328 */       if (!"".equals(this.deptts)) {
/* 329 */         str = str + " where " + this.deptts + " >= '" + paramString + "' ";
/*     */       }
/* 331 */       int k = paramInt2;
/* 332 */       if (j % paramInt2 != 0)
/* 333 */         k = j % paramInt2; 
/* 334 */       str3 = "select *\tfrom (select top " + k + " * from (select top " + j + " * from (" + str + ") sourcetable order by " + str4 + " asc )\tas temp_sum_hrmSource order by " + str4 + " desc ) temp_order order by " + str4 + " asc";
/*     */     
/*     */     }
/* 337 */     else if (this.dbType.equals("mysql")) {
/* 338 */       str3 = "select * from " + this.depttable;
/* 339 */       if (!"".equals(this.deptts)) {
/* 340 */         str3 = str3 + " where " + this.deptts + " >= '" + paramString + "' ";
/*     */       }
/* 342 */       str3 = str3 + " order by " + str4;
/* 343 */       str3 = str3 + " limit " + (i - 1) + "," + paramInt2;
/*     */     } else {
/* 345 */       str3 = "select rownum rn,w.* from " + this.depttable + " w ";
/* 346 */       if (!"".equals(this.deptts)) {
/* 347 */         str3 = str3 + " where " + this.deptts + " >= '" + paramString + "' and rownum <= " + j;
/*     */       } else {
/* 349 */         str3 = str3 + " where rownum <=" + j;
/*     */       } 
/* 351 */       str3 = "select * from (" + str3 + ") where rn >=" + i;
/* 352 */       str3 = str3 + " order by " + str4;
/*     */     } 
/* 354 */     newlog.info("获取部门分页sql:" + str3);
/* 355 */     this.syncrs.executeSql(str3);
/* 356 */     return getDataFromSql(this.syncrs, this.dbsource, map1, map2, map3, this.deptts);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<Map<String, Object>> getJobTitleData(String paramString, int paramInt1, int paramInt2, int paramInt3, MappingBean paramMappingBean) throws Exception {
/* 372 */     if ("".equals(Util.null2String(this.jobtable))) {
/* 373 */       return new ArrayList<>();
/*     */     }
/* 375 */     Map<String, String> map1 = paramMappingBean.getFieldMap();
/* 376 */     Map<String, String> map2 = paramMappingBean.getTransMap();
/* 377 */     Map<String, String> map3 = paramMappingBean.getTranTypeMap();
/* 378 */     int i = paramInt2 * paramInt1 + 1;
/* 379 */     int j = paramInt2 * (paramInt1 + 1);
/* 380 */     if (j > paramInt3) {
/* 381 */       j = paramInt3;
/*     */     }
/* 383 */     String str1 = "";
/* 384 */     String str2 = "";
/* 385 */     String str3 = paramMappingBean.getKey();
/* 386 */     if (!"".equals(str3)) {
/* 387 */       str2 = str2 + ("".equals(str2) ? str3 : ("," + str3));
/*     */     }
/* 389 */     if (!"".equals(this.jobts)) {
/* 390 */       str2 = str2 + ("".equals(str2) ? this.jobts : ("," + this.jobts));
/*     */     }
/* 392 */     str2 = " order by " + str2;
/*     */ 
/*     */     
/* 395 */     if (this.dbType.equals("sqlserver")) {
/* 396 */       int k = paramInt2;
/* 397 */       if (j % paramInt2 != 0)
/* 398 */         k = j % paramInt2; 
/* 399 */       String str = "select * from " + this.jobtable;
/* 400 */       if (!"".equals(this.jobts)) {
/* 401 */         str = str + " where " + this.jobts + " >= '" + paramString + "' ";
/*     */       }
/* 403 */       str1 = "select *\tfrom (select top " + k + " * from (select top " + j + " * from (" + str + ") sourcetable " + str2 + " asc )\tas temp_sum_hrmSource " + str2 + " desc ) temp_order " + str2 + " asc";
/*     */     
/*     */     }
/* 406 */     else if (this.dbType.equals("mysql")) {
/* 407 */       str1 = "select * from " + this.jobtable;
/* 408 */       if (!"".equals(this.jobts)) {
/* 409 */         str1 = str1 + " where " + this.jobts + " >= '" + paramString + "' order by " + this.jobts;
/*     */       }
/* 411 */       str1 = str1 + " limit " + (i - 1) + "," + paramInt2;
/*     */     } else {
/* 413 */       str1 = "select rownum rn,w.* from " + this.jobtable + " w ";
/* 414 */       if (!"".equals(this.jobts)) {
/* 415 */         str1 = str1 + " where " + this.jobts + " >= '" + paramString + "' and rownum <= " + j;
/*     */       } else {
/* 417 */         str1 = str1 + " where rownum <=" + j;
/*     */       } 
/* 419 */       str1 = "select * from (" + str1 + ") where rn >=" + i;
/* 420 */       str1 = str1 + " " + str2;
/*     */     } 
/* 422 */     this.syncrs.executeSql(str1);
/* 423 */     newlog.error("获取岗位分页数据 sql ：" + str1);
/* 424 */     return getDataFromSql(this.syncrs, this.dbsource, map1, map2, map3, this.jobts);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<Map<String, Object>> getHrData(String paramString, int paramInt1, int paramInt2, int paramInt3, MappingBean paramMappingBean) throws Exception {
/* 440 */     Map<String, String> map1 = paramMappingBean.getFieldMap();
/* 441 */     Map<String, String> map2 = paramMappingBean.getTransMap();
/* 442 */     Map<String, String> map3 = paramMappingBean.getTranTypeMap();
/* 443 */     String str1 = paramMappingBean.getParentkey();
/* 444 */     String str2 = paramMappingBean.getKey();
/* 445 */     int i = paramInt2 * paramInt1 + 1;
/* 446 */     int j = paramInt2 * (paramInt1 + 1);
/* 447 */     if (j > paramInt3) {
/* 448 */       j = paramInt3;
/*     */     }
/* 450 */     String str3 = "";
/* 451 */     String str4 = "";
/* 452 */     if (!"".equals(str1));
/*     */ 
/*     */ 
/*     */     
/* 456 */     if (!"".equals(this.hrmts)) {
/* 457 */       str4 = str4 + ("".equals(str4) ? this.hrmts : ("," + this.hrmts));
/*     */     }
/* 459 */     if (!"".equals(str2)) {
/* 460 */       str4 = str4 + ("".equals(str4) ? str2 : ("," + str2));
/*     */     }
/*     */     
/* 463 */     if (this.dbType.equals("sqlserver")) {
/* 464 */       String str = "select * from " + this.hrmtable;
/* 465 */       int k = paramInt2;
/* 466 */       if (j % paramInt2 != 0)
/* 467 */         k = j % paramInt2; 
/* 468 */       if (!"".equals(this.hrmts)) {
/* 469 */         str = str + " where " + this.hrmts + " >= '" + paramString + "' ";
/*     */       }
/* 471 */       str3 = "select *\tfrom (select top " + k + " * from (select top " + j + " * from (" + str + ") sourcetable order by " + str4 + " asc )\tas temp_sum_hrmSource order by " + str4 + " desc ) temp_order order by " + str4 + " asc";
/*     */     
/*     */     }
/* 474 */     else if (this.dbType.equals("mysql")) {
/* 475 */       str3 = "select * from " + this.hrmtable;
/* 476 */       if (!"".equals(this.hrmts)) {
/* 477 */         str3 = str3 + " where " + this.hrmts + " >= '" + paramString + "' ";
/*     */       }
/* 479 */       str3 = str3 + " order by " + str4;
/* 480 */       str3 = str3 + " limit " + (i - 1) + "," + paramInt2;
/*     */     } else {
/* 482 */       str3 = "select rownum rn,w.* from " + this.hrmtable + " w ";
/* 483 */       if (!"".equals(this.hrmts)) {
/* 484 */         str3 = str3 + " where " + this.hrmts + " >= '" + paramString + "' and rownum <= " + j;
/*     */       } else {
/* 486 */         str3 = str3 + " where rownum <=" + j;
/*     */       } 
/* 488 */       str3 = "select * from (" + str3 + ") where rn >=" + i;
/* 489 */       str3 = str3 + " order by " + str4;
/*     */     } 
/* 491 */     newlog.info("SynResource 获取人员:" + str3);
/* 492 */     this.syncrs.executeSql(str3);
/* 493 */     return getDataFromSql(this.syncrs, this.dbsource, map1, map2, map3, this.hrmts);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<Map<String, Object>> getDataFromSql(RecordSetDataSource paramRecordSetDataSource, String paramString1, Map<String, String> paramMap1, Map<String, String> paramMap2, Map<String, String> paramMap3, String paramString2) throws Exception {
/* 508 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 509 */     while (paramRecordSetDataSource.next()) {
/* 510 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 511 */       HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 512 */       Set<String> set = paramMap1.keySet();
/* 513 */       for (Iterator<String> iterator = set.iterator(); iterator.hasNext(); ) {
/* 514 */         String str1 = Util.null2String(iterator.next());
/* 515 */         String str2 = Util.null2String(paramMap1.get(str1));
/* 516 */         String str3 = "";
/* 517 */         String str4 = Util.null2String(paramMap2.get(SyncStringUtil.getTransqlMapKey(str1, str2)));
/* 518 */         String str5 = Util.null2String(paramMap3.get(SyncStringUtil.getTransqlMapKey(str1, str2)), "0");
/* 519 */         str5 = "".equals(str5) ? "0" : str5;
/* 520 */         if (!"".equals(str2)) {
/* 521 */           str3 = paramRecordSetDataSource.getString(str2);
/*     */         } else {
/* 523 */           str3 = "";
/*     */         } 
/* 525 */         if (!"".equals(str4)) {
/* 526 */           str3 = FieldValueTrans.getTranValue(str5, str4, str3, paramString1, str1, str2);
/*     */         }
/*     */         
/* 529 */         str1 = str1.toLowerCase();
/* 530 */         if (!isCustomField(str1)) {
/* 531 */           hashMap1.put(str1, str3); continue;
/* 532 */         }  hashMap2.put(str1.replace("#cus#", ""), str3);
/*     */       } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 539 */       if (!hashMap2.isEmpty()) {
/* 540 */         hashMap1.put("cusFieldInfo", hashMap2);
/*     */       }
/* 542 */       arrayList.add(hashMap1);
/*     */     } 
/* 544 */     return (List)arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isCustomField(String paramString) {
/* 553 */     Pattern pattern = Pattern.compile("^#cus#.*");
/* 554 */     Matcher matcher = pattern.matcher(paramString);
/* 555 */     if (matcher.find())
/* 556 */       return true; 
/* 557 */     return false;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/syncHrmData/Handler/impl/SyncDataByDB.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */