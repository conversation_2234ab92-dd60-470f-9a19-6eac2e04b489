package weaver.integration.syncHrmData.Handler;

import java.util.List;
import java.util.Map;
import weaver.integration.syncHrmData.config.MappingBean;

public interface SyncDataHandler {
  int getSubcompanyTotalCnt(String paramString) throws Exception;
  
  int getDepartmentTotalCnt(String paramString) throws Exception;
  
  int getJobTitleTotalCnt(String paramString) throws Exception;
  
  int getHrTotalCnt(String paramString) throws Exception;
  
  List<Map<String, Object>> getSubcompanyData(String paramString, MappingBean paramMappingBean) throws Exception;
  
  List<Map<String, Object>> getDepartmentData(String paramString, MappingBean paramMappingBean) throws Exception;
  
  List<Map<String, Object>> getJobTitleData(String paramString, MappingBean paramMappingBean) throws Exception;
  
  List<Map<String, Object>> getHrData(String paramString, MappingBean paramMappingBean) throws Exception;
  
  List<Map<String, Object>> getSubcompanyData(String paramString, int paramInt1, int paramInt2, int paramInt3, MappingBean paramMappingBean) throws Exception;
  
  List<Map<String, Object>> getDepartmentData(String paramString, int paramInt1, int paramInt2, int paramInt3, MappingBean paramMappingBean) throws Exception;
  
  List<Map<String, Object>> getJobTitleData(String paramString, int paramInt1, int paramInt2, int paramInt3, MappingBean paramMappingBean) throws Exception;
  
  List<Map<String, Object>> getHrData(String paramString, int paramInt1, int paramInt2, int paramInt3, MappingBean paramMappingBean) throws Exception;
}


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/integration/syncHrmData/Handler/SyncDataHandler.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */