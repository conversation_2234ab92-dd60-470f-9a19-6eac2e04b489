/*    */ package weaver.parseBrowser;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.general.StaticObj;
/*    */ 
/*    */ public class SapBrowserComInfo
/*    */   extends BaseBean {
/* 10 */   private StaticObj staticobj = null;
/*    */   
/* 12 */   private ArrayList allBrowserId = new ArrayList();
/*    */   
/* 14 */   private HashMap allBrowserMap = new HashMap<Object, Object>();
/*    */   
/*    */   public ArrayList getAllBrowserId() {
/* 17 */     return this.allBrowserId;
/*    */   }
/*    */   
/*    */   public void setAllBrowserId(ArrayList paramArrayList) {
/* 21 */     this.allBrowserId = paramArrayList;
/*    */   }
/*    */   
/*    */   public HashMap getAllBrowserMap() {
/* 25 */     return this.allBrowserMap;
/*    */   }
/*    */   
/*    */   public void setAllBrowserMap(HashMap paramHashMap) {
/* 29 */     this.allBrowserMap = paramHashMap;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public SapBaseBrowser getSapBaseBrowser(String paramString) {
/* 38 */     SapBaseBrowser sapBaseBrowser = (SapBaseBrowser)this.allBrowserMap.get(paramString);
/* 39 */     if (sapBaseBrowser == null) {
/* 40 */       sapBaseBrowser = new SapBaseBrowser();
/*    */     }
/* 42 */     return sapBaseBrowser;
/*    */   }
/*    */   
/*    */   public SapBrowserComInfo() {
/* 46 */     this.staticobj = StaticObj.getInstance();
/*    */     try {
/* 48 */       setSettingCache();
/* 49 */     } catch (Exception exception) {}
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void setSettingCache() {
/* 57 */     if (this.staticobj.getObject("SapBrowserComInfo.allBrowserId") == null) {
/* 58 */       ParseBrowser parseBrowser = new ParseBrowser();
/* 59 */       SapBrowser sapBrowser = parseBrowser.getSapBrowser();
/* 60 */       this.staticobj.putObject("SapBrowserComInfo.allBrowserId", sapBrowser.getAllBrowserId());
/* 61 */       this.staticobj.putObject("SapBrowserComInfo.allBrowserMap", sapBrowser.getAllBrowserMap());
/*    */     } 
/* 63 */     this.allBrowserId = (ArrayList)this.staticobj.getObject("SapBrowserComInfo.allBrowserId");
/* 64 */     this.allBrowserMap = (HashMap)this.staticobj.getObject("SapBrowserComInfo.allBrowserMap");
/* 65 */     if (this.allBrowserId == null) {
/* 66 */       this.allBrowserId = new ArrayList();
/*    */     }
/* 68 */     if (this.allBrowserMap == null) {
/* 69 */       this.allBrowserMap = new HashMap<Object, Object>();
/*    */     }
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void removeSapBrowserComInfo() {
/* 77 */     this.staticobj.removeObject("SapBrowserComInfo.allBrowserId");
/* 78 */     this.staticobj.removeObject("SapBrowserComInfo.allBrowserMap");
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/parseBrowser/SapBrowserComInfo.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */