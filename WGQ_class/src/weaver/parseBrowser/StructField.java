/*    */ package weaver.parseBrowser;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ 
/*    */ public class StructField
/*    */ {
/*  7 */   private String StructName = "";
/*    */   
/*  9 */   private ArrayList StructFieldList = new ArrayList();
/*    */   
/*    */   public String getStructName() {
/* 12 */     return this.StructName;
/*    */   }
/*    */   
/*    */   public void setStructName(String paramString) {
/* 16 */     this.StructName = paramString;
/*    */   }
/*    */   
/*    */   public ArrayList getStructFieldList() {
/* 20 */     return this.StructFieldList;
/*    */   }
/*    */   
/*    */   public void setStructFieldList(ArrayList paramArrayList) {
/* 24 */     this.StructFieldList = paramArrayList;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/parseBrowser/StructField.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */