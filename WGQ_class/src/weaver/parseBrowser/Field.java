/*    */ package weaver.parseBrowser;
/*    */ 
/*    */ public class Field
/*    */ {
/*  5 */   private String name = "";
/*    */   
/*  7 */   private String desc = "";
/*    */   
/*  9 */   private String display = "";
/*    */   
/* 11 */   private String search = "";
/*    */   
/* 13 */   private String from = "";
/*    */   
/* 15 */   private String fromOaField = "";
/*    */   
/* 17 */   private String searchvalue = "";
/*    */   
/* 19 */   private String constant = "";
/*    */   private boolean identity = false;
/*    */   
/*    */   public String getFromOaField() {
/* 23 */     return this.fromOaField;
/*    */   }
/*    */   
/*    */   public void setFromOaField(String paramString) {
/* 27 */     this.fromOaField = paramString;
/*    */   }
/*    */   
/*    */   public String getName() {
/* 31 */     return this.name;
/*    */   }
/*    */   
/*    */   public void setName(String paramString) {
/* 35 */     this.name = paramString;
/*    */   }
/*    */   
/*    */   public String getDesc() {
/* 39 */     return this.desc;
/*    */   }
/*    */   
/*    */   public void setDesc(String paramString) {
/* 43 */     this.desc = paramString;
/*    */   }
/*    */   
/*    */   public String getDisplay() {
/* 47 */     return this.display;
/*    */   }
/*    */   
/*    */   public void setDisplay(String paramString) {
/* 51 */     this.display = paramString;
/*    */   }
/*    */   
/*    */   public String getFrom() {
/* 55 */     return this.from;
/*    */   }
/*    */   
/*    */   public void setFrom(String paramString) {
/* 59 */     this.from = paramString;
/*    */   }
/*    */   
/*    */   public String getSearch() {
/* 63 */     return this.search;
/*    */   }
/*    */   
/*    */   public void setSearch(String paramString) {
/* 67 */     this.search = paramString;
/*    */   }
/*    */   
/*    */   public String getSearchvalue() {
/* 71 */     return this.searchvalue;
/*    */   }
/*    */   
/*    */   public void setSearchvalue(String paramString) {
/* 75 */     this.searchvalue = paramString;
/*    */   }
/*    */   
/*    */   public String getConstant() {
/* 79 */     return this.constant;
/*    */   }
/*    */   
/*    */   public void setConstant(String paramString) {
/* 83 */     this.constant = paramString;
/*    */   }
/*    */   
/*    */   public boolean isIdentity() {
/* 87 */     return this.identity;
/*    */   }
/*    */   
/*    */   public void setIdentity(boolean paramBoolean) {
/* 91 */     this.identity = paramBoolean;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/parseBrowser/Field.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */