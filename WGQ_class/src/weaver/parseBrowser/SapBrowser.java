/*    */ package weaver.parseBrowser;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ 
/*    */ public class SapBrowser
/*    */ {
/*  8 */   private ArrayList allBrowserId = new ArrayList();
/*    */   
/* 10 */   private HashMap allBrowserMap = new HashMap<Object, Object>();
/*    */   
/*    */   public ArrayList getAllBrowserId() {
/* 13 */     return this.allBrowserId;
/*    */   }
/*    */   
/*    */   public void setAllBrowserId(ArrayList paramArrayList) {
/* 17 */     this.allBrowserId = paramArrayList;
/*    */   }
/*    */   
/*    */   public HashMap getAllBrowserMap() {
/* 21 */     return this.allBrowserMap;
/*    */   }
/*    */   
/*    */   public void setAllBrowserMap(HashMap paramHashMap) {
/* 25 */     this.allBrowserMap = paramHashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/parseBrowser/SapBrowser.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */