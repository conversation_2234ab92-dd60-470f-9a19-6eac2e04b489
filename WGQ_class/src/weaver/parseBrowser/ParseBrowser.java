/*     */ package weaver.parseBrowser;
/*     */ 
/*     */ import java.io.File;
/*     */ import java.io.FileInputStream;
/*     */ import java.io.FileWriter;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import org.dom4j.Document;
/*     */ import org.dom4j.DocumentHelper;
/*     */ import org.dom4j.Element;
/*     */ import org.dom4j.Node;
/*     */ import org.dom4j.io.OutputFormat;
/*     */ import org.dom4j.io.SAXReader;
/*     */ import org.dom4j.io.XMLWriter;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.Util;
/*     */ import weaver.security.util.SecurityMethodUtil;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ParseBrowser
/*     */   extends BaseBean
/*     */ {
/*  32 */   public static String sapbrowserxmlpath = GCONST.getRootPath() + "WEB-INF" + File.separatorChar + "config" + File.separatorChar + "SapBrowser.xml";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public SapBrowser getSapBrowser() {
/*  50 */     String str1 = GCONST.getRootPath();
/*  51 */     String str2 = str1 + "WEB-INF" + File.separatorChar + "config" + File.separatorChar + "SapBrowser.xml";
/*     */ 
/*     */     
/*  54 */     Document document = readXml(str2);
/*  55 */     if (document == null) {
/*  56 */       return null;
/*     */     }
/*  58 */     return parseXml(document);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public SapBrowser parseXml(Document paramDocument) {
/*  67 */     SapBrowser sapBrowser = new SapBrowser();
/*  68 */     ArrayList<String> arrayList = new ArrayList();
/*  69 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*  71 */     Element element = paramDocument.getRootElement();
/*  72 */     List<Element> list = element.selectNodes("browser");
/*  73 */     for (byte b = 0; b < list.size(); b++) {
/*  74 */       SapBaseBrowser sapBaseBrowser = new SapBaseBrowser();
/*  75 */       String str1 = "";
/*  76 */       String str2 = "";
/*  77 */       ArrayList arrayList1 = new ArrayList();
/*  78 */       ArrayList arrayList2 = new ArrayList();
/*  79 */       ArrayList arrayList3 = new ArrayList();
/*  80 */       ArrayList arrayList4 = new ArrayList();
/*  81 */       ArrayList arrayList5 = new ArrayList();
/*  82 */       ArrayList arrayList6 = new ArrayList();
/*  83 */       ArrayList arrayList7 = new ArrayList();
/*     */       
/*  85 */       Element element1 = list.get(b);
/*  86 */       String str3 = Util.null2String(element1.attributeValue("authFlag"));
/*  87 */       String str4 = Util.null2String(element1.attributeValue("authWorkflowID"));
/*  88 */       str2 = getBrowserid(element1);
/*  89 */       str1 = getFunctionName(element1);
/*     */ 
/*     */       
/*  92 */       if (!str2.equals("") && !str1.equals("")) {
/*     */ 
/*     */ 
/*     */         
/*  96 */         arrayList1 = getInput_parameterImport(element1);
/*     */         
/*  98 */         arrayList2 = getInput_parameterTable(element1);
/*     */         
/* 100 */         arrayList3 = getInput_parameterStruct(element1);
/*     */         
/* 102 */         arrayList4 = getOutput_parameterExport(element1);
/*     */         
/* 104 */         arrayList5 = getOutput_parameterTable(element1);
/*     */         
/* 106 */         arrayList6 = getOutput_parameterStruct(element1);
/*     */         
/* 108 */         arrayList7 = getAssignment(element1);
/*     */ 
/*     */ 
/*     */         
/* 112 */         sapBaseBrowser.setSapbrowserid(str2);
/* 113 */         sapBaseBrowser.setFunction(str1);
/* 114 */         sapBaseBrowser.setAssignment(arrayList7);
/* 115 */         sapBaseBrowser.setExport_output(arrayList4);
/* 116 */         sapBaseBrowser.setImport_input(arrayList1);
/* 117 */         sapBaseBrowser.setTable_input(arrayList2);
/* 118 */         sapBaseBrowser.setTable_output(arrayList5);
/* 119 */         sapBaseBrowser.setStruct_input(arrayList3);
/* 120 */         sapBaseBrowser.setStruct_output(arrayList6);
/* 121 */         sapBaseBrowser.setAuthFlag(str3);
/* 122 */         sapBaseBrowser.setAuthWorkflowID(str4);
/*     */         
/* 124 */         arrayList.add(str2);
/* 125 */         hashMap.put(str2, sapBaseBrowser);
/*     */       } 
/* 127 */     }  sapBrowser.setAllBrowserId(arrayList);
/* 128 */     sapBrowser.setAllBrowserMap(hashMap);
/* 129 */     return sapBrowser;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public ArrayList getAssignment(Element paramElement) {
/* 138 */     ArrayList<Field> arrayList = new ArrayList();
/* 139 */     Element element = paramElement.element("assignment_parameter");
/* 140 */     if (element != null) {
/* 141 */       List<Element> list = element.selectNodes("field");
/* 142 */       for (byte b = 0; b < list.size(); b++) {
/* 143 */         Element element1 = list.get(b);
/* 144 */         String str1 = Util.null2String(element1.elementText("name")).toUpperCase();
/* 145 */         String str2 = Util.null2String(element1.elementText("from")).toUpperCase();
/*     */         
/* 147 */         if (!str1.equals("") && !str2.equals("")) {
/* 148 */           Field field = new Field();
/* 149 */           field.setName(str1);
/* 150 */           field.setFrom(str2);
/* 151 */           arrayList.add(field);
/*     */         } 
/*     */       } 
/*     */     } 
/*     */     
/* 156 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public ArrayList getOutput_parameterTable(Element paramElement) {
/* 165 */     ArrayList<TableField> arrayList = new ArrayList();
/* 166 */     Element element = paramElement.element("output_parameter");
/* 167 */     if (element != null) {
/* 168 */       List<Element> list = element.selectNodes("table");
/* 169 */       for (byte b = 0; b < list.size(); b++) {
/* 170 */         Element element1 = list.get(b);
/* 171 */         if (element1 != null) {
/* 172 */           String str = element1.attributeValue("name");
/* 173 */           if (!str.equals("")) {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */             
/* 180 */             ArrayList<Field> arrayList1 = new ArrayList();
/* 181 */             TableField tableField = new TableField();
/* 182 */             tableField.setTableName(str);
/* 183 */             List<Element> list1 = element1.selectNodes("field");
/* 184 */             for (byte b1 = 0; b1 < list1.size(); b1++) {
/* 185 */               Element element2 = list1.get(b1);
/* 186 */               String str1 = Util.null2String(element2.elementText("name"));
/* 187 */               String str2 = Util.null2String(element2.elementText("desc"));
/* 188 */               String str3 = Util.null2String(element2.elementText("display")).toUpperCase();
/* 189 */               String str4 = Util.null2String(element2.elementText("search")).toUpperCase();
/*     */ 
/*     */               
/* 192 */               String str5 = Util.null2String(element2.elementText("identity")).toUpperCase();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */               
/* 201 */               if (!str5.equals("")) {
/* 202 */                 tableField.setIdentityField(Util.null2String(tableField.getIdentityField()) + "," + str1);
/*     */               }
/*     */ 
/*     */ 
/*     */ 
/*     */               
/* 208 */               if (str4.equals("")) {
/* 209 */                 str4 = "N";
/*     */               }
/*     */               
/* 212 */               if (!str1.equals("")) {
/* 213 */                 Field field = new Field();
/* 214 */                 field.setName(str1);
/* 215 */                 field.setDesc(str2);
/* 216 */                 field.setDisplay(str3);
/* 217 */                 field.setSearch(str4);
/*     */                 
/* 219 */                 field.setIdentity((str5.equals("Y") || str5.equals("1")));
/*     */                 
/* 221 */                 arrayList1.add(field);
/*     */               } 
/*     */             } 
/*     */             
/* 225 */             tableField.setTableFieldList(arrayList1);
/* 226 */             arrayList.add(tableField);
/*     */           } 
/*     */         } 
/*     */       } 
/* 230 */     }  return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public ArrayList getOutput_parameterExport(Element paramElement) {
/* 239 */     ArrayList<Field> arrayList = new ArrayList();
/* 240 */     Element element = paramElement.element("output_parameter");
/* 241 */     if (element != null) {
/* 242 */       Element element1 = element.element("export");
/* 243 */       if (element1 != null) {
/* 244 */         List<Element> list = element1.selectNodes("field");
/* 245 */         for (byte b = 0; b < list.size(); b++) {
/* 246 */           Element element2 = list.get(b);
/* 247 */           String str1 = Util.null2String(element2.elementText("name"));
/* 248 */           String str2 = Util.null2String(element2.elementText("desc"));
/* 249 */           String str3 = Util.null2String(element2.elementText("display")).toUpperCase();
/*     */ 
/*     */ 
/*     */ 
/*     */           
/* 254 */           if (!str1.equals("")) {
/* 255 */             Field field = new Field();
/* 256 */             field.setName(str1);
/* 257 */             field.setDesc(str2);
/* 258 */             field.setDisplay(str3);
/* 259 */             arrayList.add(field);
/*     */           } 
/*     */         } 
/*     */       } 
/*     */     } 
/*     */     
/* 265 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public ArrayList getOutput_parameterStruct(Element paramElement) {
/* 274 */     ArrayList<StructField> arrayList = new ArrayList();
/* 275 */     Element element = paramElement.element("output_parameter");
/* 276 */     if (element != null) {
/* 277 */       List<Element> list = element.selectNodes("struct");
/* 278 */       for (byte b = 0; b < list.size(); b++) {
/* 279 */         Element element1 = list.get(b);
/* 280 */         if (element1 != null) {
/* 281 */           String str = element1.attributeValue("name");
/* 282 */           if (!str.equals("")) {
/*     */ 
/*     */ 
/*     */ 
/*     */             
/* 287 */             writeLog("=======================  getOutput_parameterStruct  ===========================================");
/*     */ 
/*     */             
/* 290 */             ArrayList<Field> arrayList1 = new ArrayList();
/* 291 */             StructField structField = new StructField();
/* 292 */             structField.setStructName(str);
/* 293 */             List<Element> list1 = element1.selectNodes("field");
/* 294 */             for (byte b1 = 0; b1 < list1.size(); b1++) {
/* 295 */               Element element2 = list1.get(b1);
/* 296 */               String str1 = Util.null2String(element2.elementText("name"));
/* 297 */               String str2 = Util.null2String(element2.elementText("desc"));
/* 298 */               String str3 = Util.null2String(element2.elementText("display")).toUpperCase();
/* 299 */               String str4 = Util.null2String(element2.elementText("search")).toUpperCase();
/*     */ 
/*     */ 
/*     */               
/* 303 */               if (str4.equals("")) {
/* 304 */                 str4 = "N";
/*     */               }
/*     */               
/* 307 */               if (!str1.equals("")) {
/* 308 */                 Field field = new Field();
/* 309 */                 field.setName(str1);
/* 310 */                 field.setDesc(str2);
/* 311 */                 field.setDisplay(str3);
/* 312 */                 field.setSearch(str4);
/* 313 */                 arrayList1.add(field);
/* 314 */                 writeLog(str1 + "\t@\t" + str2 + "\t\t@\t" + str3 + "\t@\t" + str4);
/*     */               } 
/*     */             } 
/* 317 */             writeLog("structFieldList.size():" + arrayList1.size());
/* 318 */             structField.setStructFieldList(arrayList1);
/* 319 */             arrayList.add(structField);
/*     */           } 
/*     */         } 
/*     */       } 
/* 323 */     }  return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public ArrayList getInput_parameterTable(Element paramElement) {
/* 331 */     return new ArrayList();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public ArrayList getInput_parameterImport(Element paramElement) {
/* 340 */     ArrayList<Field> arrayList = new ArrayList();
/* 341 */     Element element = paramElement.element("input_parameter");
/* 342 */     if (element != null) {
/* 343 */       Element element1 = element.element("import");
/* 344 */       if (element1 != null) {
/* 345 */         List<Element> list = element1.selectNodes("field");
/* 346 */         for (byte b = 0; b < list.size(); b++) {
/* 347 */           Element element2 = list.get(b);
/* 348 */           String str1 = Util.null2String(element2.elementText("name"));
/* 349 */           String str2 = Util.null2String(element2.elementText("from_oa_field")).toUpperCase();
/*     */ 
/*     */           
/* 352 */           String str3 = Util.null2String(element2.elementText("constant"));
/*     */           
/* 354 */           if (!str1.equals("") && !str2.equals("")) {
/* 355 */             Field field = new Field();
/* 356 */             field.setName(str1);
/* 357 */             field.setFromOaField(str2);
/*     */             
/* 359 */             field.setConstant(str3);
/* 360 */             arrayList.add(field);
/*     */           } 
/*     */         } 
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 367 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public ArrayList getInput_parameterStruct(Element paramElement) {
/* 375 */     ArrayList<StructField> arrayList = new ArrayList();
/* 376 */     Element element = paramElement.element("input_parameter");
/* 377 */     if (element != null) {
/* 378 */       List<Element> list = element.selectNodes("struct");
/* 379 */       for (byte b = 0; b < list.size(); b++) {
/* 380 */         Element element1 = list.get(b);
/* 381 */         if (element1 != null) {
/* 382 */           String str = element1.attributeValue("name");
/* 383 */           if (!str.equals("")) {
/*     */ 
/*     */ 
/*     */ 
/*     */             
/* 388 */             writeLog("=======================  getInput_parameterStruct  ===========================================");
/* 389 */             writeLog("structName:" + str);
/*     */             
/* 391 */             ArrayList<Field> arrayList1 = new ArrayList();
/* 392 */             StructField structField = new StructField();
/* 393 */             structField.setStructName(str);
/* 394 */             List<Element> list1 = element1.selectNodes("field");
/* 395 */             for (byte b1 = 0; b1 < list1.size(); b1++) {
/* 396 */               Element element2 = list1.get(b1);
/* 397 */               String str1 = Util.null2String(element2.elementText("name"));
/* 398 */               String str2 = Util.null2String(element2.elementText("from_oa_field")).toUpperCase();
/* 399 */               String str3 = Util.null2String(element2.elementText("constant"));
/*     */               
/* 401 */               writeLog("name:" + str1 + "\t\tfromOaField:" + str2);
/* 402 */               if (!str1.equals("") && !str2.equals("")) {
/* 403 */                 Field field = new Field();
/* 404 */                 field.setName(str1);
/* 405 */                 field.setFromOaField(str2);
/* 406 */                 field.setConstant(str3);
/* 407 */                 arrayList1.add(field);
/*     */               } 
/*     */             } 
/* 410 */             writeLog("structFieldList.size():" + arrayList1.size());
/* 411 */             structField.setStructFieldList(arrayList1);
/* 412 */             arrayList.add(structField);
/*     */           } 
/*     */         } 
/*     */       } 
/* 416 */     }  return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getBrowserid(Element paramElement) {
/* 425 */     String str = "";
/* 426 */     str = Util.null2String(paramElement.attributeValue("id"));
/* 427 */     str = Util.null2String(str);
/* 428 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFunctionName(Element paramElement) {
/* 437 */     String str = "";
/* 438 */     List<Node> list = paramElement.selectNodes("function");
/* 439 */     if (list != null) {
/* 440 */       str = ((Node)list.get(0)).getText();
/*     */     }
/* 442 */     str = Util.null2String(str);
/* 443 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Document readXml(String paramString) {
/* 451 */     Document document = null;
/*     */     try {
/* 453 */       SAXReader sAXReader = new SAXReader();
/* 454 */       new SecurityMethodUtil(); SecurityMethodUtil.setReaderFeature(sAXReader);
/* 455 */       document = sAXReader.read(new FileInputStream(paramString));
/* 456 */     } catch (Exception exception) {
/* 457 */       exception.printStackTrace();
/* 458 */       writeLog(exception);
/*     */     } 
/* 460 */     return document;
/*     */   }
/*     */ 
/*     */   
/*     */   public void saveSAPBrowser(SapBaseBrowser paramSapBaseBrowser) throws Exception {
/* 465 */     String str1 = paramSapBaseBrowser.getSapbrowserid();
/* 466 */     String str2 = paramSapBaseBrowser.getFunction();
/* 467 */     ArrayList<Field> arrayList1 = paramSapBaseBrowser.getAssignment();
/* 468 */     ArrayList<Field> arrayList2 = paramSapBaseBrowser.getExport_output();
/* 469 */     ArrayList<Field> arrayList3 = paramSapBaseBrowser.getImport_input();
/* 470 */     ArrayList<StructField> arrayList4 = paramSapBaseBrowser.getStruct_input();
/* 471 */     ArrayList<StructField> arrayList5 = paramSapBaseBrowser.getStruct_output();
/* 472 */     ArrayList arrayList = paramSapBaseBrowser.getTable_input();
/* 473 */     ArrayList<TableField> arrayList6 = paramSapBaseBrowser.getTable_output();
/* 474 */     String str3 = Util.null2String(paramSapBaseBrowser.getAuthFlag());
/* 475 */     String str4 = Util.null2String(paramSapBaseBrowser.getAuthWorkflowID());
/*     */     
/* 477 */     Document document = readXml(sapbrowserxmlpath);
/* 478 */     if (document == null) {
/* 479 */       document = DocumentHelper.createDocument();
/* 480 */       document.addElement("module")
/* 481 */         .addAttribute("id", "sapbrowser")
/* 482 */         .addAttribute("version", "1.0.0");
/*     */     } 
/* 484 */     Element element1 = document.getRootElement();
/*     */     
/* 486 */     List<Element> list = element1.selectNodes("browser[@id='" + str1 + "']");
/*     */ 
/*     */     
/* 489 */     if (list != null && list.size() > 0) {
/* 490 */       Element element = list.get(0);
/* 491 */       element1.remove(element);
/*     */     } 
/*     */ 
/*     */     
/* 495 */     Element element2 = element1.addElement("browser").addAttribute("id", str1);
/* 496 */     if (str3.equalsIgnoreCase("Y")) {
/* 497 */       element2.addAttribute("authFlag", str3).addAttribute("authWorkflowID", str4);
/*     */     }
/*     */     
/* 500 */     Element element3 = element2.addElement("function");
/* 501 */     element3.setText(str2);
/*     */     
/* 503 */     Element element4 = element2.addElement("input_parameter");
/*     */ 
/*     */     
/* 506 */     if (arrayList3 != null && arrayList3.size() > 0) {
/* 507 */       Element element = element4.addElement("import");
/* 508 */       for (byte b = 0; b < arrayList3.size(); b++) {
/* 509 */         Field field = arrayList3.get(b);
/* 510 */         String str5 = field.getName();
/* 511 */         String str6 = field.getFromOaField();
/* 512 */         String str7 = field.getConstant();
/*     */         
/* 514 */         Element element7 = element.addElement("field");
/*     */         
/* 516 */         Element element8 = element7.addElement("name");
/* 517 */         element8.setText(str5);
/*     */         
/* 519 */         if (!str6.equals("")) {
/* 520 */           Element element9 = element7.addElement("from_oa_field");
/* 521 */           element9.setText(field.getFromOaField());
/*     */         } 
/* 523 */         if (!str7.equals("")) {
/* 524 */           Element element9 = element7.addElement("constant");
/* 525 */           element9.setText(str7);
/*     */         } 
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 531 */     if (arrayList4 != null && arrayList4.size() > 0) {
/* 532 */       for (byte b = 0; b < arrayList4.size(); b++) {
/* 533 */         StructField structField = arrayList4.get(b);
/* 534 */         String str = structField.getStructName();
/* 535 */         ArrayList<Field> arrayList7 = structField.getStructFieldList();
/*     */         
/* 537 */         Element element = element4.addElement("struct").addAttribute("name", str);
/* 538 */         for (byte b1 = 0; b1 < arrayList7.size(); b1++) {
/* 539 */           Field field = arrayList7.get(b1);
/* 540 */           String str5 = field.getName();
/* 541 */           String str6 = field.getFromOaField();
/* 542 */           String str7 = field.getConstant();
/*     */           
/* 544 */           Element element7 = element.addElement("field");
/*     */           
/* 546 */           Element element8 = element7.addElement("name");
/* 547 */           element8.setText(str5);
/*     */           
/* 549 */           if (!str6.equals("")) {
/* 550 */             Element element9 = element7.addElement("from_oa_field");
/* 551 */             element9.setText(field.getFromOaField());
/*     */           } 
/* 553 */           if (!str7.equals("")) {
/* 554 */             Element element9 = element7.addElement("constant");
/* 555 */             element9.setText(str7);
/*     */           } 
/*     */         } 
/*     */       } 
/*     */     }
/*     */ 
/*     */     
/* 562 */     Element element5 = element2.addElement("output_parameter");
/*     */ 
/*     */     
/* 565 */     if (arrayList2 != null && arrayList2.size() > 0) {
/* 566 */       Element element = element5.addElement("export");
/* 567 */       for (byte b = 0; b < arrayList2.size(); b++) {
/* 568 */         Field field = arrayList2.get(b);
/* 569 */         String str5 = field.getName();
/* 570 */         String str6 = field.getDesc();
/* 571 */         String str7 = field.getDisplay();
/*     */         
/* 573 */         Element element7 = element.addElement("field");
/*     */         
/* 575 */         Element element8 = element7.addElement("name");
/* 576 */         element8.setText(str5);
/*     */         
/* 578 */         Element element9 = element7.addElement("desc");
/* 579 */         element9.setText(str6);
/*     */         
/* 581 */         Element element10 = element7.addElement("display");
/* 582 */         element10.setText(str7.equals("") ? "N" : str7);
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 587 */     if (arrayList5 != null && arrayList5.size() > 0) {
/* 588 */       for (byte b = 0; b < arrayList5.size(); b++) {
/* 589 */         StructField structField = arrayList5.get(b);
/* 590 */         String str = structField.getStructName();
/* 591 */         ArrayList<Field> arrayList7 = structField.getStructFieldList();
/*     */         
/* 593 */         Element element = element5.addElement("struct").addAttribute("name", str);
/* 594 */         for (byte b1 = 0; b1 < arrayList7.size(); b1++) {
/* 595 */           Field field = arrayList7.get(b1);
/* 596 */           String str5 = field.getName();
/* 597 */           String str6 = field.getDesc();
/* 598 */           String str7 = field.getDisplay();
/* 599 */           String str8 = field.getSearch();
/*     */           
/* 601 */           Element element7 = element.addElement("field");
/*     */           
/* 603 */           Element element8 = element7.addElement("name");
/* 604 */           element8.setText(str5);
/*     */ 
/*     */           
/* 607 */           Element element9 = element7.addElement("desc");
/* 608 */           element9.setText(str6);
/*     */           
/* 610 */           Element element10 = element7.addElement("display");
/* 611 */           element10.setText(str7.equals("") ? "N" : str7);
/*     */           
/* 613 */           Element element11 = element7.addElement("search");
/* 614 */           element11.setText(str8.equals("") ? "N" : str8);
/*     */         } 
/*     */       } 
/*     */     }
/*     */ 
/*     */     
/* 620 */     if (arrayList6 != null && arrayList6.size() > 0) {
/* 621 */       for (byte b = 0; b < arrayList6.size(); b++) {
/* 622 */         TableField tableField = arrayList6.get(b);
/* 623 */         String str = tableField.getTableName();
/* 624 */         ArrayList<Field> arrayList7 = tableField.getTableFieldList();
/*     */         
/* 626 */         Element element = element5.addElement("table").addAttribute("name", str);
/* 627 */         for (byte b1 = 0; b1 < arrayList7.size(); b1++) {
/* 628 */           Field field = arrayList7.get(b1);
/* 629 */           String str5 = field.getName();
/* 630 */           String str6 = field.getDesc();
/* 631 */           String str7 = field.getDisplay();
/* 632 */           String str8 = field.getSearch();
/* 633 */           boolean bool = field.isIdentity();
/*     */           
/* 635 */           Element element7 = element.addElement("field");
/*     */           
/* 637 */           Element element8 = element7.addElement("name");
/* 638 */           element8.setText(str5);
/*     */ 
/*     */           
/* 641 */           Element element9 = element7.addElement("desc");
/* 642 */           element9.setText(str6);
/*     */           
/* 644 */           Element element10 = element7.addElement("display");
/* 645 */           element10.setText(str7.equals("") ? "N" : str7);
/*     */           
/* 647 */           Element element11 = element7.addElement("search");
/* 648 */           element11.setText(str8.equals("") ? "N" : str8);
/*     */           
/* 650 */           if (bool) {
/* 651 */             Element element12 = element7.addElement("identity");
/* 652 */             element12.setText("Y");
/*     */           } 
/*     */         } 
/*     */       } 
/*     */     }
/*     */ 
/*     */     
/* 659 */     Element element6 = element2.addElement("assignment_parameter");
/* 660 */     if (arrayList1 != null && arrayList1.size() > 0) {
/* 661 */       for (byte b = 0; b < arrayList1.size(); b++) {
/* 662 */         Field field = arrayList1.get(b);
/* 663 */         String str5 = field.getName();
/* 664 */         String str6 = field.getFrom();
/*     */         
/* 666 */         Element element7 = element6.addElement("field");
/*     */         
/* 668 */         Element element8 = element7.addElement("name");
/* 669 */         element8.setText(str5);
/*     */         
/* 671 */         Element element9 = element7.addElement("from");
/* 672 */         element9.setText(str6);
/*     */       } 
/*     */     }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 680 */     writeXml(document);
/*     */   }
/*     */ 
/*     */   
/*     */   public void deleteSAPBrowser(String paramString) throws Exception {
/* 685 */     Document document = readXml(sapbrowserxmlpath);
/* 686 */     if (document == null) {
/* 687 */       document = DocumentHelper.createDocument();
/* 688 */       document.addElement("module")
/* 689 */         .addAttribute("id", "sapbrowser")
/* 690 */         .addAttribute("version", "1.0.0");
/*     */     } 
/* 692 */     Element element = document.getRootElement();
/*     */     
/* 694 */     List<Element> list = element.selectNodes("browser[@id='" + paramString + "']");
/*     */ 
/*     */     
/* 697 */     if (list != null && list.size() > 0) {
/* 698 */       Element element1 = list.get(0);
/* 699 */       element.remove(element1);
/*     */     } 
/*     */     
/* 702 */     writeXml(document);
/*     */   }
/*     */   
/*     */   public void writeXml(Document paramDocument) throws Exception {
/* 706 */     OutputFormat outputFormat = OutputFormat.createPrettyPrint();
/* 707 */     outputFormat.setEncoding(GCONST.XML_UTF8);
/* 708 */     XMLWriter xMLWriter = new XMLWriter(new FileWriter(sapbrowserxmlpath), outputFormat);
/* 709 */     xMLWriter.write(paramDocument);
/* 710 */     xMLWriter.close();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void main(String[] paramArrayOfString) throws Exception {
/* 772 */     (new ParseBrowser()).saveSAPBrowser((SapBaseBrowser)null);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/parseBrowser/ParseBrowser.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */