/*     */ package weaver.parseBrowser;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ 
/*     */ public class SapBaseBrowser
/*     */ {
/*   7 */   private String sapbrowserid = "";
/*     */   
/*   9 */   private String function = "";
/*     */   
/*  11 */   private String authFlag = "";
/*  12 */   private String authWorkflowID = "";
/*     */   
/*  14 */   private ArrayList import_input = new ArrayList();
/*     */   
/*  16 */   private ArrayList table_input = new ArrayList();
/*     */   
/*  18 */   private ArrayList struct_input = new ArrayList();
/*     */   
/*  20 */   private ArrayList export_output = new ArrayList();
/*     */   
/*  22 */   private ArrayList table_output = new ArrayList();
/*     */   
/*  24 */   private ArrayList struct_output = new ArrayList();
/*     */   
/*  26 */   private ArrayList assignment = new ArrayList();
/*     */   
/*     */   public String getFunction() {
/*  29 */     return this.function;
/*     */   }
/*     */   
/*     */   public void setFunction(String paramString) {
/*  33 */     this.function = paramString;
/*     */   }
/*     */   
/*     */   public ArrayList getImport_input() {
/*  37 */     return this.import_input;
/*     */   }
/*     */   
/*     */   public void setImport_input(ArrayList paramArrayList) {
/*  41 */     this.import_input = paramArrayList;
/*     */   }
/*     */   
/*     */   public ArrayList getTable_input() {
/*  45 */     return this.table_input;
/*     */   }
/*     */   
/*     */   public void setTable_input(ArrayList paramArrayList) {
/*  49 */     this.table_input = paramArrayList;
/*     */   }
/*     */   
/*     */   public ArrayList getExport_output() {
/*  53 */     return this.export_output;
/*     */   }
/*     */   
/*     */   public void setExport_output(ArrayList paramArrayList) {
/*  57 */     this.export_output = paramArrayList;
/*     */   }
/*     */   
/*     */   public ArrayList getTable_output() {
/*  61 */     return this.table_output;
/*     */   }
/*     */   
/*     */   public void setTable_output(ArrayList paramArrayList) {
/*  65 */     this.table_output = paramArrayList;
/*     */   }
/*     */   
/*     */   public ArrayList getAssignment() {
/*  69 */     return this.assignment;
/*     */   }
/*     */   
/*     */   public void setAssignment(ArrayList paramArrayList) {
/*  73 */     this.assignment = paramArrayList;
/*     */   }
/*     */   
/*     */   public ArrayList getStruct_input() {
/*  77 */     return this.struct_input;
/*     */   }
/*     */   
/*     */   public void setStruct_input(ArrayList paramArrayList) {
/*  81 */     this.struct_input = paramArrayList;
/*     */   }
/*     */   
/*     */   public ArrayList getStruct_output() {
/*  85 */     return this.struct_output;
/*     */   }
/*     */   
/*     */   public void setStruct_output(ArrayList paramArrayList) {
/*  89 */     this.struct_output = paramArrayList;
/*     */   }
/*     */   
/*     */   public String getSapbrowserid() {
/*  93 */     return this.sapbrowserid;
/*     */   }
/*     */   
/*     */   public void setSapbrowserid(String paramString) {
/*  97 */     this.sapbrowserid = paramString;
/*     */   }
/*     */   
/*     */   public String getAuthFlag() {
/* 101 */     return this.authFlag;
/*     */   }
/*     */   
/*     */   public void setAuthFlag(String paramString) {
/* 105 */     this.authFlag = paramString;
/*     */   }
/*     */   
/*     */   public String getAuthWorkflowID() {
/* 109 */     return this.authWorkflowID;
/*     */   }
/*     */   
/*     */   public void setAuthWorkflowID(String paramString) {
/* 113 */     this.authWorkflowID = paramString;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/parseBrowser/SapBaseBrowser.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */