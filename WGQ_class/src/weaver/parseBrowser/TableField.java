/*    */ package weaver.parseBrowser;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ 
/*    */ public class TableField
/*    */ {
/*  7 */   private String tableName = "";
/*    */   
/*  9 */   private ArrayList tableFieldList = new ArrayList();
/*    */   
/* 11 */   private String identityField = "";
/*    */   
/*    */   public String getTableName() {
/* 14 */     return this.tableName;
/*    */   }
/*    */   
/*    */   public void setTableName(String paramString) {
/* 18 */     this.tableName = paramString;
/*    */   }
/*    */   
/*    */   public ArrayList getTableFieldList() {
/* 22 */     return this.tableFieldList;
/*    */   }
/*    */   
/*    */   public void setTableFieldList(ArrayList paramArrayList) {
/* 26 */     this.tableFieldList = paramArrayList;
/*    */   }
/*    */   
/*    */   public String getIdentityField() {
/* 30 */     return this.identityField;
/*    */   }
/*    */   
/*    */   public void setIdentityField(String paramString) {
/* 34 */     this.identityField = paramString;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/parseBrowser/TableField.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */