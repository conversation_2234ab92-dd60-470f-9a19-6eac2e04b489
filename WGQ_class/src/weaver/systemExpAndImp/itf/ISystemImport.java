package weaver.systemExpAndImp.itf;

import java.util.Map;
import weaver.hrm.User;

public interface ISystemImport {
  Map<String, Object> doImport(Map<String, Object> paramMap, User paramUser);
  
  void setCount(int paramInt);
  
  void setImportIndex(int paramInt);
}


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/systemExpAndImp/itf/ISystemImport.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */