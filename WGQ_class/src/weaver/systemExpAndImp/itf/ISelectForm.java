package weaver.systemExpAndImp.itf;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.browser.bean.SearchConditionOption;
import java.util.List;
import weaver.hrm.User;

public interface ISelectForm {
  List<Object> getSelectFormColumns(User paramUser, List<SearchConditionOption> paramList);
  
  List<Object> getSelectFormColumns(User paramUser, List<SearchConditionOption> paramList, JSONArray paramJSONArray);
  
  List<Object> getImportResultColumns(User paramUser, List<SearchConditionOption> paramList);
  
  JSONObject getSelectFormOnlyReadColumns(User paramUser, List<SearchConditionOption> paramList);
  
  JSONObject getSelectFormImportTypeCells0(User paramUser, List<SearchConditionOption> paramList);
  
  JSONObject getSelectFormImportTypeCells1(User paramUser, List<SearchConditionOption> paramList);
  
  J<PERSON>NObject getSelectFormImportTypeCells2(User paramUser, List<SearchConditionOption> paramList);
}


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/systemExpAndImp/itf/ISelectForm.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */