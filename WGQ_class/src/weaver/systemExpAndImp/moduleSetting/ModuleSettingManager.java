/*     */ package weaver.systemExpAndImp.moduleSetting;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Collection;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import weaver.backup.logging.Logger;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systemExpAndImp.logging.LoggerFactory;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ModuleSettingManager
/*     */ {
/*  23 */   private Logger log = LoggerFactory.getLogger(ModuleSettingManager.class);
/*  24 */   private static JSONObject moduleIDInfos = new JSONObject();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String showEditPage(String paramString1, String paramString2) {
/*  32 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/*  33 */     String str = Util.null2String(arrayOfString[0]);
/*  34 */     return "<a href=\"javascript:doEditById('" + str + "')\">" + paramString2 + "</a>";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getIsEnable(String paramString1, String paramString2) {
/*  45 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/*  46 */     int i = Util.getIntValue(arrayOfString[0], 7);
/*     */     
/*  48 */     if ("y".equalsIgnoreCase(paramString1))
/*     */     {
/*  50 */       return SystemEnv.getHtmlLabelName(163, i);
/*     */     }
/*     */     
/*  53 */     return SystemEnv.getHtmlLabelName(161, i);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getName(String paramString1, String paramString2) {
/*  64 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/*  65 */     int i = Util.getIntValue(arrayOfString[0], 7);
/*     */     
/*  67 */     String str = SystemEnv.getHtmlLabelNames(Util.null2String(paramString1), i);
/*  68 */     if (str == null || str.length() == 0) {
/*  69 */       str = paramString1;
/*     */     }
/*     */     
/*  72 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getParentName(String paramString1, String paramString2) {
/*  83 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/*  84 */     int i = Util.getIntValue(arrayOfString[0], 7);
/*     */     
/*  86 */     return SystemEnv.getHtmlLabelNames(Util.null2String(paramString1), i);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCheckBox(String paramString) {
/*  96 */     String str1, arrayOfString[] = Util.TokenizerString2(paramString, "+");
/*  97 */     String str2 = Util.null2String(arrayOfString[0]);
/*  98 */     String str3 = Util.null2String(arrayOfString[1]);
/*  99 */     String str4 = Util.null2String(arrayOfString[2]);
/* 100 */     if ("1".equalsIgnoreCase(str3)) {
/* 101 */       str1 = "false";
/*     */     } else {
/* 103 */       String str = "select 1 from workflow_exchange where type = ?";
/* 104 */       RecordSet recordSet = new RecordSet();
/* 105 */       recordSet.executeQuery(str, new Object[] { str4 });
/* 106 */       if (recordSet.next()) {
/* 107 */         str1 = "false";
/*     */       }
/* 109 */       else if ("y".equals(str2)) {
/* 110 */         str1 = "false";
/*     */       } else {
/* 112 */         str1 = "true";
/*     */       } 
/*     */     } 
/*     */     
/* 116 */     return str1;
/*     */   }
/*     */   
/*     */   public List getOperateList(String paramString1, String paramString2) {
/* 120 */     ArrayList<String> arrayList = new ArrayList();
/*     */     
/* 122 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/* 123 */     String str1 = Util.null2String(arrayOfString[0]);
/* 124 */     String str2 = Util.null2String(arrayOfString[1]);
/*     */     
/* 126 */     if ("1".equalsIgnoreCase(str2)) {
/* 127 */       arrayList.add("true");
/* 128 */       arrayList.add("false");
/* 129 */       arrayList.add("false");
/*     */       
/* 131 */       if ("y".equals(str1)) {
/* 132 */         arrayList.add("false");
/* 133 */         arrayList.add("true");
/*     */       } else {
/* 135 */         arrayList.add("true");
/* 136 */         arrayList.add("false");
/*     */       } 
/* 138 */       return arrayList;
/*     */     } 
/*     */ 
/*     */     
/* 142 */     arrayList.add("false");
/*     */     
/* 144 */     arrayList.add("true");
/*     */     
/* 146 */     if ("y".equals(str1)) {
/* 147 */       arrayList.add("false");
/* 148 */       arrayList.add("false");
/* 149 */       arrayList.add("true");
/*     */     } else {
/* 151 */       String str = "select 1 from workflow_exchange where type = ?";
/* 152 */       RecordSet recordSet = new RecordSet();
/* 153 */       recordSet.executeQuery(str, new Object[] { paramString1 });
/* 154 */       if (recordSet.next()) {
/* 155 */         arrayList.add("false");
/*     */       } else {
/* 157 */         arrayList.add("true");
/*     */       } 
/* 159 */       arrayList.add("true");
/* 160 */       arrayList.add("false");
/*     */     } 
/* 162 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public HashMap<String, HashMap<String, Object>> getEndModules() {
/* 169 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 170 */     ArrayList<HashMap<String, Object>> arrayList = getModules();
/* 171 */     getEndModule(arrayList, (HashMap)hashMap);
/* 172 */     return (HashMap)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public HashMap<String, Object> getModuleById(String paramString) {
/* 179 */     return getModuleById(paramString, 7);
/*     */   }
/*     */   
/*     */   public HashMap<String, Object> getModuleById(String paramString, int paramInt) {
/* 183 */     return getModuleById(paramString, paramInt, true);
/*     */   }
/*     */   
/*     */   public HashMap<String, Object> getModuleById(String paramString, int paramInt, boolean paramBoolean) {
/* 187 */     if (!moduleIDInfos.containsKey(paramString + "_" + paramInt)) {
/* 188 */       String str = "select * from workflow_exchange_module where isenable = 'y' and id = ? order by showorder";
/* 189 */       if (!paramBoolean) {
/* 190 */         str = "select * from workflow_exchange_module where id = ? order by showorder";
/*     */       }
/* 192 */       RecordSet recordSet = new RecordSet();
/* 193 */       recordSet.executeQuery(str, new Object[] { paramString });
/* 194 */       ArrayList arrayList = new ArrayList();
/*     */       
/* 196 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 197 */       if (recordSet.next()) {
/* 198 */         hashMap.put("id", paramString);
/* 199 */         hashMap.put("parentid", recordSet.getString("parentid"));
/* 200 */         hashMap.put("browsertype", recordSet.getString("browsertype"));
/* 201 */         String str1 = recordSet.getString("name");
/* 202 */         if (str1 == null || str1.length() == 0) {
/* 203 */           str1 = recordSet.getString("description");
/*     */         } else {
/* 205 */           str1 = SystemEnv.getHtmlLabelNames(str1, paramInt);
/*     */         } 
/* 207 */         hashMap.put("name", str1);
/* 208 */         hashMap.put("isenable", recordSet.getString("isenable"));
/* 209 */         hashMap.put("showorder", recordSet.getString("showorder"));
/* 210 */         hashMap.put("selectid", recordSet.getString("selectid"));
/* 211 */         hashMap.put("selectname", recordSet.getString("selectname"));
/*     */       } 
/* 213 */       moduleIDInfos.put(paramString + "_" + paramInt, hashMap);
/*     */     } 
/* 215 */     return (HashMap<String, Object>)moduleIDInfos.get(paramString + "_" + paramInt);
/*     */   }
/*     */   
/*     */   public String getModuleNameById(String paramString, int paramInt) {
/* 219 */     return getModuleNameById(paramString, paramInt, true);
/*     */   }
/*     */   
/*     */   public String getModuleNameById(String paramString, int paramInt, boolean paramBoolean) {
/* 223 */     HashMap<String, Object> hashMap = getModuleById(paramString, paramInt, paramBoolean);
/* 224 */     return Util.null2String(hashMap.get("name"));
/*     */   }
/*     */   
/*     */   public String getModuleNameWithParentById(String paramString, int paramInt) {
/* 228 */     String str1 = getModuleNameById(paramString, paramInt, false);
/* 229 */     HashMap<String, Object> hashMap = getModuleById(paramString, paramInt, false);
/* 230 */     String str2 = Util.null2String(hashMap.get("parentid"));
/* 231 */     String str3 = getModuleNameById(str2, paramInt, false);
/*     */     
/* 233 */     return str3 + "-" + str1;
/*     */   }
/*     */ 
/*     */   
/*     */   private void getEndModule(ArrayList<HashMap<String, Object>> paramArrayList, HashMap<String, HashMap<String, Object>> paramHashMap) {
/* 238 */     for (byte b = 0; b < paramArrayList.size(); b++) {
/* 239 */       HashMap<String, Object> hashMap = paramArrayList.get(b);
/* 240 */       if (hashMap.containsKey("childs")) {
/* 241 */         ArrayList<HashMap<String, Object>> arrayList = (ArrayList)hashMap.get("childs");
/* 242 */         getEndModule(arrayList, paramHashMap);
/*     */       } else {
/* 244 */         paramHashMap.put(Util.null2String(hashMap.get("id")), hashMap);
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public ArrayList<HashMap<String, Object>> getModules() {
/* 256 */     ArrayList<HashMap<String, Object>> arrayList = getEnableModules();
/* 257 */     ArrayList<String> arrayList1 = getBaseModuleIds();
/*     */     
/* 259 */     return orderModules(arrayList, arrayList1);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private ArrayList<HashMap<String, Object>> orderModules(ArrayList<HashMap<String, Object>> paramArrayList, ArrayList<String> paramArrayList1) {
/* 269 */     ArrayList<HashMap<String, Object>> arrayList = new ArrayList();
/*     */ 
/*     */ 
/*     */     
/* 273 */     for (byte b = 0; b < paramArrayList1.size(); b++) {
/* 274 */       String str = paramArrayList1.get(b);
/* 275 */       HashMap<String, Object> hashMap = getCurrentModule(paramArrayList, str);
/* 276 */       ArrayList<HashMap<String, Object>> arrayList1 = getChildModules(paramArrayList, str);
/* 277 */       if (arrayList1.size() > 0) {
/* 278 */         hashMap.put("childs", arrayList1);
/*     */       }
/* 280 */       arrayList.add(hashMap);
/*     */     } 
/*     */     
/* 283 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private ArrayList<HashMap<String, Object>> getChildModules(ArrayList<HashMap<String, Object>> paramArrayList, String paramString) {
/* 293 */     ArrayList<HashMap<String, ArrayList<HashMap<String, Object>>>> arrayList = new ArrayList();
/*     */ 
/*     */     
/* 296 */     for (byte b = 0; b < paramArrayList.size(); b++) {
/* 297 */       HashMap<String, ArrayList<HashMap<String, Object>>> hashMap = (HashMap)paramArrayList.get(b);
/* 298 */       String str = (String)hashMap.get("id");
/* 299 */       if (paramString != null && paramString.equals(hashMap.get("parentid"))) {
/* 300 */         ArrayList<HashMap<String, Object>> arrayList1 = getChildModules(paramArrayList, str);
/* 301 */         if (arrayList1.size() > 0) {
/* 302 */           hashMap.put("childs", arrayList1);
/*     */         }
/* 304 */         arrayList.add(hashMap);
/*     */       } 
/*     */     } 
/* 307 */     return (ArrayList)arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private HashMap<String, Object> getCurrentModule(ArrayList<HashMap<String, Object>> paramArrayList, String paramString) {
/* 317 */     ArrayList arrayList = new ArrayList();
/* 318 */     for (byte b = 0; b < paramArrayList.size(); b++) {
/* 319 */       HashMap<String, Object> hashMap = paramArrayList.get(b);
/* 320 */       if (paramString != null && paramString.equals(hashMap.get("id"))) {
/* 321 */         return hashMap;
/*     */       }
/*     */     } 
/* 324 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public ArrayList<HashMap<String, Object>> getEnableModules() {
/* 332 */     String str = "select * from workflow_exchange_module where isenable = 'y' order by showorder";
/* 333 */     RecordSet recordSet = new RecordSet();
/* 334 */     recordSet.executeQuery(str, new Object[0]);
/* 335 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*     */ 
/*     */     
/* 338 */     while (recordSet.next()) {
/* 339 */       String str1 = recordSet.getString("id");
/*     */       
/* 341 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 342 */       hashMap.put("id", str1);
/* 343 */       hashMap.put("name", recordSet.getString("name"));
/* 344 */       hashMap.put("parentid", recordSet.getString("parentid"));
/* 345 */       hashMap.put("browsertype", recordSet.getString("browsertype"));
/* 346 */       hashMap.put("description", recordSet.getString("description"));
/* 347 */       hashMap.put("isenable", recordSet.getString("isenable"));
/* 348 */       hashMap.put("showorder", recordSet.getString("showorder"));
/* 349 */       hashMap.put("selectid", recordSet.getString("selectid"));
/* 350 */       hashMap.put("selectname", recordSet.getString("selectname"));
/*     */       
/* 352 */       arrayList.add(hashMap);
/*     */     } 
/* 354 */     return (ArrayList)arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public ArrayList<String> getBaseModuleIds() {
/* 364 */     String str1 = "select * from workflow_exchange_module where isenable = 'y' order by showorder";
/* 365 */     RecordSet recordSet1 = new RecordSet();
/* 366 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/* 368 */     recordSet1.executeQuery(str1, new Object[0]);
/* 369 */     while (recordSet1.next()) {
/* 370 */       ArrayList<String> arrayList1; String str3 = recordSet1.getString("id");
/* 371 */       String str4 = recordSet1.getString("parentid");
/* 372 */       if (hashMap.containsKey(str4)) {
/* 373 */         arrayList1 = (ArrayList)hashMap.get(str4);
/*     */       } else {
/* 375 */         arrayList1 = new ArrayList();
/*     */       } 
/* 377 */       if (!arrayList1.contains(str3)) {
/* 378 */         arrayList1.add(str3);
/*     */       }
/* 380 */       hashMap.put(str4, arrayList1);
/*     */     } 
/*     */     
/* 383 */     RecordSet recordSet2 = new RecordSet();
/* 384 */     String str2 = "select * from workflow_exchange_module where isenable = 'y' and (parentid is null or parentid = '' or parentid = 'ecology') order by showorder";
/* 385 */     recordSet2.executeQuery(str2, new Object[0]);
/* 386 */     ArrayList<String> arrayList = new ArrayList();
/* 387 */     while (recordSet2.next()) {
/* 388 */       String str = recordSet2.getString("id");
/* 389 */       arrayList.add(str);
/* 390 */       if (hashMap.containsKey(str)) {
/* 391 */         arrayList.addAll((Collection<? extends String>)hashMap.get(str));
/*     */       }
/*     */     } 
/*     */     
/* 395 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public HashMap<String, HashMap<String, Object>> getImportEndModules() {
/* 402 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 403 */     ArrayList<HashMap<String, Object>> arrayList = getImportModules();
/* 404 */     getEndModule(arrayList, (HashMap)hashMap);
/* 405 */     return (HashMap)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public ArrayList<HashMap<String, Object>> getImportModules() {
/* 415 */     ArrayList<HashMap<String, Object>> arrayList = getImportEnableModules();
/* 416 */     ArrayList<String> arrayList1 = getImportModuleIds();
/*     */     
/* 418 */     return orderModules(arrayList, arrayList1);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public ArrayList<HashMap<String, Object>> getImportEnableModules() {
/* 426 */     String str = "select * from workflow_exchange_module order by showorder";
/* 427 */     RecordSet recordSet = new RecordSet();
/* 428 */     recordSet.executeQuery(str, new Object[0]);
/* 429 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*     */ 
/*     */     
/* 432 */     while (recordSet.next()) {
/* 433 */       String str1 = recordSet.getString("id");
/*     */       
/* 435 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 436 */       hashMap.put("id", str1);
/* 437 */       hashMap.put("name", recordSet.getString("name"));
/* 438 */       hashMap.put("parentid", recordSet.getString("parentid"));
/* 439 */       hashMap.put("browsertype", recordSet.getString("browsertype"));
/* 440 */       hashMap.put("description", recordSet.getString("description"));
/* 441 */       hashMap.put("isenable", recordSet.getString("isenable"));
/* 442 */       hashMap.put("showorder", recordSet.getString("showorder"));
/* 443 */       hashMap.put("selectid", recordSet.getString("selectid"));
/* 444 */       hashMap.put("selectname", recordSet.getString("selectname"));
/*     */       
/* 446 */       arrayList.add(hashMap);
/*     */     } 
/* 448 */     return (ArrayList)arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public ArrayList<String> getImportModuleIds() {
/* 458 */     String str1 = "select * from workflow_exchange_module order by showorder";
/* 459 */     RecordSet recordSet1 = new RecordSet();
/* 460 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/* 462 */     recordSet1.executeQuery(str1, new Object[0]);
/* 463 */     while (recordSet1.next()) {
/* 464 */       ArrayList<String> arrayList1; String str3 = recordSet1.getString("id");
/* 465 */       String str4 = recordSet1.getString("parentid");
/* 466 */       if (hashMap.containsKey(str4)) {
/* 467 */         arrayList1 = (ArrayList)hashMap.get(str4);
/*     */       } else {
/* 469 */         arrayList1 = new ArrayList();
/*     */       } 
/* 471 */       if (!arrayList1.contains(str3)) {
/* 472 */         arrayList1.add(str3);
/*     */       }
/* 474 */       hashMap.put(str4, arrayList1);
/*     */     } 
/*     */     
/* 477 */     RecordSet recordSet2 = new RecordSet();
/* 478 */     String str2 = "select * from workflow_exchange_module where (parentid is null or parentid = '' or parentid = 'ecology') order by showorder";
/* 479 */     recordSet2.executeQuery(str2, new Object[0]);
/* 480 */     ArrayList<String> arrayList = new ArrayList();
/* 481 */     while (recordSet2.next()) {
/* 482 */       String str = recordSet2.getString("id");
/* 483 */       arrayList.add(str);
/* 484 */       if (hashMap.containsKey(str)) {
/* 485 */         arrayList.addAll((Collection<? extends String>)hashMap.get(str));
/*     */       }
/*     */     } 
/*     */     
/* 489 */     return arrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/systemExpAndImp/moduleSetting/ModuleSettingManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */