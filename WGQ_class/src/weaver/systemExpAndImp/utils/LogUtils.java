/*    */ package weaver.systemExpAndImp.utils;
/*    */ 
/*    */ import java.io.PrintWriter;
/*    */ import java.io.StringWriter;
/*    */ import weaver.backup.logging.Logger;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class LogUtils
/*    */ {
/*    */   public static void writeLog(Logger paramLogger, Throwable paramThrowable) {
/* 19 */     StringWriter stringWriter = new StringWriter();
/* 20 */     paramThrowable.printStackTrace(new PrintWriter(stringWriter, true));
/* 21 */     writeLog(paramLogger, "error", stringWriter.getBuffer().toString());
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static void writeLog(Logger paramLogger, String paramString) {
/* 30 */     writeLog(paramLogger, "info", paramString);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static void writeLog(Logger paramLogger, String paramString1, String paramString2) {
/* 40 */     if (paramString1.equalsIgnoreCase("info")) {
/* 41 */       paramLogger.info(paramString2);
/* 42 */     } else if (paramString1.equalsIgnoreCase("error")) {
/* 43 */       paramLogger.error(paramString2);
/*    */     } 
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/systemExpAndImp/utils/LogUtils.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */