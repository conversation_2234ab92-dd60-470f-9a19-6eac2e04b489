/*    */ package weaver.systemExpAndImp.utils;
/*    */ 
/*    */ import java.text.SimpleDateFormat;
/*    */ import java.util.Calendar;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class SystemExpAndImpUtils
/*    */ {
/*    */   private User user;
/*    */   public static final String FileExt = "esf";
/*    */   public static final String FileName = "ecology";
/*    */   
/*    */   public User getUser() {
/* 40 */     return this.user;
/*    */   }
/*    */   
/*    */   public void setUser(User paramUser) {
/* 44 */     this.user = paramUser;
/*    */   }
/*    */   
/*    */   private static String getCurrentTime() {
/* 48 */     String str = "yyyy'-'MM'-'dd' 'HHmmss";
/* 49 */     SimpleDateFormat simpleDateFormat = new SimpleDateFormat(str);
/* 50 */     Calendar calendar = Calendar.getInstance();
/*    */     
/* 52 */     return simpleDateFormat.format(calendar.getTime());
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/systemExpAndImp/utils/SystemExpAndImpUtils.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */