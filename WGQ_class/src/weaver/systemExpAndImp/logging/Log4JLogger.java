/*    */ package weaver.systemExpAndImp.logging;
/*    */ 
/*    */ import org.apache.log4j.Logger;
/*    */ import weaver.backup.logging.Logger;
/*    */ 
/*    */ 
/*    */ 
/*    */ public class Log4JLogger
/*    */   implements Logger
/*    */ {
/*    */   private Logger log;
/*    */   private String classname;
/*    */   
/*    */   public String getClassname() {
/* 15 */     return this.classname;
/*    */   }
/*    */   
/*    */   public void setClassname(String paramString) {
/* 19 */     this.classname = paramString;
/*    */   }
/*    */   
/*    */   public boolean isDebugEnabled() {
/* 23 */     return this.log.isDebugEnabled();
/*    */   }
/*    */   
/*    */   public boolean isInfoEnabled() {
/* 27 */     return this.log.isInfoEnabled();
/*    */   }
/*    */   
/*    */   public void debug(Object paramObject) {
/* 31 */     String str = Thread.currentThread().getStackTrace()[2].getMethodName();
/* 32 */     this.log.debug(this.classname + "." + str + "() - " + paramObject);
/*    */   }
/*    */   
/*    */   public void debug(Object paramObject, Throwable paramThrowable) {
/* 36 */     String str = Thread.currentThread().getStackTrace()[2].getMethodName();
/* 37 */     this.log.debug(this.classname + "." + str + "() - " + paramObject, paramThrowable);
/*    */   }
/*    */   
/*    */   public void info(Object paramObject) {
/* 41 */     String str = Thread.currentThread().getStackTrace()[2].getMethodName();
/* 42 */     this.log.info(this.classname + "." + str + "() - " + paramObject);
/*    */   }
/*    */   
/*    */   public void info(Object paramObject, Throwable paramThrowable) {
/* 46 */     String str = Thread.currentThread().getStackTrace()[2].getMethodName();
/* 47 */     this.log.info(this.classname + "." + str + "() - " + paramObject, paramThrowable);
/*    */   }
/*    */   
/*    */   public void warn(Object paramObject) {
/* 51 */     String str = Thread.currentThread().getStackTrace()[2].getMethodName();
/* 52 */     this.log.warn(this.classname + "." + str + "() - " + paramObject);
/*    */   }
/*    */   
/*    */   public void warn(Object paramObject, Throwable paramThrowable) {
/* 56 */     String str = Thread.currentThread().getStackTrace()[2].getMethodName();
/* 57 */     this.log.warn(this.classname + "." + str + "() - " + paramObject, paramThrowable);
/*    */   }
/*    */   
/*    */   public void error(Object paramObject) {
/* 61 */     String str = Thread.currentThread().getStackTrace()[2].getMethodName();
/* 62 */     this.log.error(this.classname + "." + str + "() - " + paramObject);
/*    */   }
/*    */   
/*    */   public void error(Object paramObject, Throwable paramThrowable) {
/* 66 */     String str = Thread.currentThread().getStackTrace()[2].getMethodName();
/* 67 */     this.log.error(this.classname + "." + str + "() - " + paramObject, paramThrowable);
/*    */   }
/*    */   
/*    */   public void init(String paramString) {
/* 71 */     if ("".equals(paramString))
/* 72 */       paramString = "systemei"; 
/* 73 */     this.log = Logger.getLogger(paramString);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/systemExpAndImp/logging/Log4JLogger.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */