/*    */ package weaver.systemExpAndImp.logging;
/*    */ 
/*    */ import weaver.backup.logging.Logger;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class LoggerFactory
/*    */ {
/*    */   private static final String loggerName = "systemei";
/*    */   
/*    */   public static Logger getLogger(String paramString1, String paramString2) {
/* 14 */     if ("".equals(paramString1))
/* 15 */       paramString1 = "systemei"; 
/* 16 */     Log4JLogger log4JLogger = new Log4JLogger();
/* 17 */     log4JLogger.setClassname(paramString2);
/* 18 */     log4JLogger.init(paramString1);
/* 19 */     return log4JLogger;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static Logger getLogger(Class paramClass) {
/* 28 */     return getLogger("systemei", paramClass.getCanonicalName());
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static Logger getLogger(String paramString) {
/* 37 */     return getLogger("systemei", paramString);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static Logger getLogger() {
/* 45 */     String str = Thread.currentThread().getStackTrace()[2].getClassName();
/* 46 */     return getLogger("systemei", str);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/systemExpAndImp/logging/LoggerFactory.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */