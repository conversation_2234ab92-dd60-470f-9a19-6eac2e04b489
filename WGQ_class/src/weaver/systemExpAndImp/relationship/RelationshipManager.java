/*     */ package weaver.systemExpAndImp.relationship;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class RelationshipManager
/*     */ {
/*     */   public String showEID(String paramString1, String paramString2) {
/*  20 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/*  21 */     return Util.null2String(arrayOfString[0]);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String showEditPage(String paramString1, String paramString2) {
/*  32 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/*  33 */     String str = Util.null2String(arrayOfString[0]);
/*  34 */     return "<a href=\"javascript:doEditById('" + paramString1 + "')\">" + str + "</a>";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getIsEnable(String paramString1, String paramString2) {
/*  45 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/*  46 */     int i = Util.getIntValue(arrayOfString[0], 7);
/*     */     
/*  48 */     if ("y".equalsIgnoreCase(paramString1))
/*     */     {
/*  50 */       return SystemEnv.getHtmlLabelName(163, i);
/*     */     }
/*     */     
/*  53 */     return SystemEnv.getHtmlLabelName(161, i);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getModuleName(String paramString1, String paramString2) {
/*  65 */     String str = "select * from workflow_exchange_module where id = ?";
/*  66 */     RecordSet recordSet = new RecordSet();
/*  67 */     recordSet.executeQuery(str, new Object[] { paramString1 });
/*     */     
/*  69 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/*  70 */     int i = Util.getIntValue(arrayOfString[0], 7);
/*     */     
/*  72 */     if (recordSet.next()) {
/*  73 */       String str1 = SystemEnv.getHtmlLabelNames(Util.null2String(recordSet.getString("name")), i);
/*  74 */       if (str1 == null || str1.length() == 0) {
/*  75 */         str1 = Util.null2String(recordSet.getString("name"));
/*     */       }
/*  77 */       return str1;
/*     */     } 
/*     */ 
/*     */     
/*  81 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDescription(String paramString1, String paramString2) {
/*  92 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/*  93 */     int i = Util.getIntValue(arrayOfString[0], 7);
/*     */     
/*  95 */     return SystemEnv.getHtmlLabelNames(paramString1, i);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCheckBox(String paramString) {
/* 105 */     String str1, arrayOfString[] = Util.TokenizerString2(paramString, "+");
/* 106 */     String str2 = Util.null2String(arrayOfString[0]);
/* 107 */     String str3 = Util.null2String(arrayOfString[1]);
/* 108 */     if ("y".equals(str2) || "1".equalsIgnoreCase(str3)) {
/* 109 */       str1 = "false";
/*     */     } else {
/* 111 */       str1 = "true";
/*     */     } 
/* 113 */     return str1;
/*     */   }
/*     */   
/*     */   public List getOperateList(String paramString1, String paramString2) {
/* 117 */     ArrayList<String> arrayList = new ArrayList();
/*     */     
/* 119 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/* 120 */     String str1 = Util.null2String(arrayOfString[0]);
/* 121 */     String str2 = Util.null2String(arrayOfString[1]);
/* 122 */     String str3 = "select 1 from workflow_exchange where parentid = ?";
/* 123 */     RecordSet recordSet = new RecordSet();
/* 124 */     recordSet.executeQuery(str3, new Object[] { paramString1 });
/*     */     
/* 126 */     if ("1".equalsIgnoreCase(str2)) {
/* 127 */       arrayList.add("true");
/* 128 */       arrayList.add("false");
/* 129 */       arrayList.add("false");
/* 130 */       arrayList.add("false");
/* 131 */       arrayList.add("false");
/* 132 */       arrayList.add("true");
/* 133 */       return arrayList;
/*     */     } 
/* 135 */     arrayList.add("false");
/*     */     
/* 137 */     arrayList.add("true");
/*     */     
/* 139 */     if ("y".equals(str1)) {
/* 140 */       arrayList.add("false");
/* 141 */       arrayList.add("false");
/* 142 */       arrayList.add("true");
/*     */     } else {
/*     */       
/* 145 */       if (recordSet.next()) {
/* 146 */         arrayList.add("false");
/*     */       } else {
/* 148 */         arrayList.add("true");
/*     */       } 
/* 150 */       arrayList.add("true");
/* 151 */       arrayList.add("false");
/*     */     } 
/* 153 */     arrayList.add("true");
/*     */ 
/*     */     
/* 156 */     return arrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/systemExpAndImp/relationship/RelationshipManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */