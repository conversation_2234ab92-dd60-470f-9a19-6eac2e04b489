/*     */ package weaver.systemExpAndImp.impl;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONArray;
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.engine.integration.util.WeaTableEditUtil;
/*     */ import com.engine.workflow.entity.WeaTableEditComEntity;
/*     */ import com.engine.workflow.entity.WeaTableEditEntity;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import weaver.backup.logging.Logger;
/*     */ import weaver.backup.services.ImportTypeService;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systemExpAndImp.itf.ISelectForm;
/*     */ import weaver.systemExpAndImp.logging.LoggerFactory;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class DocSelectFrom
/*     */   implements ISelectForm
/*     */ {
/*     */   private String type;
/*  38 */   private Logger log = LoggerFactory.getLogger(getClass());
/*     */   
/*     */   public DocSelectFrom(String paramString) {
/*  41 */     this.type = paramString;
/*     */   }
/*     */ 
/*     */   
/*     */   public List<Object> getSelectFormColumns(User paramUser, List<SearchConditionOption> paramList) {
/*  46 */     return getSelectFormColumns(paramUser, paramList, null);
/*     */   }
/*     */ 
/*     */   
/*     */   public List<Object> getSelectFormColumns(User paramUser, List<SearchConditionOption> paramList, JSONArray paramJSONArray) {
/*  51 */     ArrayList<JSONObject> arrayList = new ArrayList();
/*     */     
/*  53 */     ArrayList<WeaTableEditComEntity> arrayList1 = new ArrayList();
/*     */ 
/*     */     
/*  56 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  57 */     WeaTableEditEntity weaTableEditEntity1 = WeaTableEditUtil.getColumn(ConditionType.CHECKBOX, SystemEnv.getHtmlLabelName(129432, Util.getIntValue(paramUser.getLanguage() + "")), "isImport", "15%", 2, hashMap1);
/*  58 */     JSONObject jSONObject = new JSONObject();
/*  59 */     jSONObject = JSONObject.parseObject(JSONObject.toJSONString(weaTableEditEntity1));
/*  60 */     jSONObject.getJSONArray("com").getJSONObject(0).put("display", "switch");
/*  61 */     arrayList.add(jSONObject);
/*     */ 
/*     */     
/*  64 */     WeaTableEditComEntity weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.INPUT, "1", "name", 120);
/*  65 */     weaTableEditComEntity.setViewAttr(1, true);
/*  66 */     hashMap1 = new HashMap<>();
/*  67 */     hashMap1.put("hasBorder", Boolean.valueOf(false));
/*  68 */     weaTableEditComEntity.setOtherParams(hashMap1);
/*  69 */     arrayList1.add(weaTableEditComEntity);
/*  70 */     WeaTableEditEntity weaTableEditEntity2 = WeaTableEditUtil.getColumn(ConditionType.INPUT, SystemEnv.getHtmlLabelName(195, Util.getIntValue(paramUser.getLanguage() + "")), "name", "15%", 1);
/*  71 */     weaTableEditEntity2.setCom(arrayList1);
/*  72 */     arrayList.add(weaTableEditEntity2);
/*     */ 
/*     */     
/*  75 */     ArrayList<SearchConditionOption> arrayList2 = new ArrayList();
/*     */     
/*  77 */     SearchConditionOption searchConditionOption1 = new SearchConditionOption();
/*  78 */     searchConditionOption1.setKey("2");
/*  79 */     searchConditionOption1.setShowname(SystemEnv.getHtmlLabelName(129294, Util.getIntValue(paramUser.getLanguage() + "")));
/*     */     
/*  81 */     SearchConditionOption searchConditionOption2 = new SearchConditionOption();
/*  82 */     searchConditionOption2.setKey("1");
/*  83 */     searchConditionOption2.setShowname(SystemEnv.getHtmlLabelName(1421, Util.getIntValue(paramUser.getLanguage() + "")));
/*     */     
/*  85 */     SearchConditionOption searchConditionOption3 = new SearchConditionOption();
/*  86 */     searchConditionOption3.setKey("0");
/*  87 */     searchConditionOption3.setShowname(SystemEnv.getHtmlLabelName(17744, Util.getIntValue(paramUser.getLanguage() + "")));
/*  88 */     searchConditionOption3.setSelected(true);
/*     */     
/*  90 */     arrayList2.add(searchConditionOption1);
/*  91 */     arrayList2.add(searchConditionOption2);
/*  92 */     arrayList2.add(searchConditionOption3);
/*  93 */     WeaTableEditEntity weaTableEditEntity3 = WeaTableEditUtil.getColumn(ConditionType.SELECT, SystemEnv.getHtmlLabelName(24863, Util.getIntValue(paramUser.getLanguage() + "")), "importtype", "15%", 2, arrayList2);
/*  94 */     jSONObject = new JSONObject();
/*  95 */     jSONObject = JSONObject.parseObject(JSONObject.toJSONString(weaTableEditEntity3));
/*  96 */     jSONObject.getJSONArray("com").getJSONObject(0).put("viewAttr", Integer.valueOf(1));
/*  97 */     jSONObject.getJSONArray("com").getJSONObject(0).put("hasBorder", Boolean.valueOf(false));
/*  98 */     jSONObject.getJSONArray("com").getJSONObject(0).put("otherParams", "");
/*  99 */     arrayList.add(jSONObject);
/*     */ 
/*     */ 
/*     */     
/* 103 */     if (paramList == null) {
/* 104 */       weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.SELECT, "1", "updateId", 120);
/*     */     } else {
/* 106 */       ArrayList<SearchConditionOption> arrayList3 = new ArrayList();
/*     */       
/*     */       try {
/* 109 */         for (byte b = 0; b < paramList.size(); b++) {
/* 110 */           SearchConditionOption searchConditionOption = ((SearchConditionOption)paramList.get(b)).clone();
/* 111 */           searchConditionOption.setSelected(false);
/* 112 */           arrayList3.add(searchConditionOption);
/*     */         } 
/* 114 */       } catch (CloneNotSupportedException cloneNotSupportedException) {
/* 115 */         this.log.error(cloneNotSupportedException.getMessage(), cloneNotSupportedException);
/* 116 */         arrayList3 = new ArrayList<>();
/*     */       } 
/* 118 */       weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.SELECT, "1", "updateId", 120, arrayList3);
/*     */     } 
/* 120 */     weaTableEditComEntity.setViewAttr(1);
/* 121 */     arrayList1 = new ArrayList<>();
/* 122 */     arrayList1.add(weaTableEditComEntity);
/* 123 */     WeaTableEditEntity weaTableEditEntity4 = WeaTableEditUtil.getColumn(ConditionType.SELECT, SystemEnv.getHtmlLabelName(523786, Util.getIntValue(paramUser.getLanguage() + "")), "updateId", "25%", 1);
/* 124 */     weaTableEditEntity4.setCom(arrayList1);
/* 125 */     arrayList.add(weaTableEditEntity4);
/*     */ 
/*     */     
/* 128 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*     */     
/* 130 */     hashMap2.put("helpfulTip", SystemEnv.getHtmlLabelName(525839, Util.getIntValue(paramUser.getLanguage() + "")));
/* 131 */     weaTableEditEntity1 = WeaTableEditUtil.getColumn(ConditionType.CHECKBOX, SystemEnv.getHtmlLabelName(507298, Util.getIntValue(paramUser.getLanguage() + "")), "importBase", "15%", 2, hashMap2);
/* 132 */     jSONObject = new JSONObject();
/* 133 */     jSONObject = JSONObject.parseObject(JSONObject.toJSONString(weaTableEditEntity1));
/* 134 */     jSONObject.getJSONArray("com").getJSONObject(0).put("display", "switch");
/* 135 */     arrayList.add(jSONObject);
/*     */     
/* 137 */     return (List)arrayList;
/*     */   }
/*     */ 
/*     */   
/*     */   public List<Object> getImportResultColumns(User paramUser, List<SearchConditionOption> paramList) {
/* 142 */     ArrayList<WeaTableEditEntity> arrayList = new ArrayList();
/*     */     
/* 144 */     ArrayList<WeaTableEditComEntity> arrayList1 = new ArrayList();
/*     */     
/* 146 */     WeaTableEditComEntity weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.INPUT, "1", "name", 120);
/* 147 */     weaTableEditComEntity.setViewAttr(1, true);
/* 148 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 149 */     hashMap1.put("hasBorder", Boolean.valueOf(false));
/* 150 */     weaTableEditComEntity.setOtherParams(hashMap1);
/* 151 */     arrayList1.add(weaTableEditComEntity);
/* 152 */     WeaTableEditEntity weaTableEditEntity1 = WeaTableEditUtil.getColumn(ConditionType.INPUT, SystemEnv.getHtmlLabelName(195, Util.getIntValue(paramUser.getLanguage() + "")), "name", "15%", 1);
/* 153 */     weaTableEditEntity1.setCom(arrayList1);
/* 154 */     arrayList.add(weaTableEditEntity1);
/*     */ 
/*     */     
/* 157 */     ArrayList<SearchConditionOption> arrayList2 = new ArrayList();
/* 158 */     SearchConditionOption searchConditionOption1 = new SearchConditionOption();
/* 159 */     searchConditionOption1.setKey("2");
/* 160 */     searchConditionOption1.setShowname(SystemEnv.getHtmlLabelName(129294, Util.getIntValue(paramUser.getLanguage() + "")));
/* 161 */     SearchConditionOption searchConditionOption2 = new SearchConditionOption();
/* 162 */     searchConditionOption2.setKey("1");
/* 163 */     searchConditionOption2.setShowname(SystemEnv.getHtmlLabelName(1421, Util.getIntValue(paramUser.getLanguage() + "")));
/* 164 */     SearchConditionOption searchConditionOption3 = new SearchConditionOption();
/* 165 */     searchConditionOption3.setKey("0");
/* 166 */     searchConditionOption3.setShowname(SystemEnv.getHtmlLabelName(17744, Util.getIntValue(paramUser.getLanguage() + "")));
/* 167 */     searchConditionOption3.setSelected(true);
/* 168 */     arrayList2.add(searchConditionOption1);
/* 169 */     arrayList2.add(searchConditionOption2);
/* 170 */     arrayList2.add(searchConditionOption3);
/* 171 */     WeaTableEditEntity weaTableEditEntity2 = WeaTableEditUtil.getColumn(ConditionType.SELECT, SystemEnv.getHtmlLabelName(24863, Util.getIntValue(paramUser.getLanguage() + "")), "importtype", "15%", 1, arrayList2);
/* 172 */     JSONObject jSONObject = JSONObject.parseObject(JSONObject.toJSONString(weaTableEditEntity2));
/* 173 */     jSONObject.getJSONArray("com").getJSONObject(0).put("viewAttr", Integer.valueOf(1));
/* 174 */     jSONObject.getJSONArray("com").getJSONObject(0).put("hasBorder", Boolean.valueOf(false));
/* 175 */     jSONObject.getJSONArray("com").getJSONObject(0).put("otherParams", "");
/* 176 */     arrayList.add(jSONObject);
/*     */ 
/*     */     
/* 179 */     if (paramList == null) {
/* 180 */       weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.SELECT, "1", "updateId", 120);
/*     */     } else {
/* 182 */       ArrayList<SearchConditionOption> arrayList3 = new ArrayList();
/*     */       
/*     */       try {
/* 185 */         for (byte b = 0; b < paramList.size(); b++) {
/* 186 */           SearchConditionOption searchConditionOption = ((SearchConditionOption)paramList.get(b)).clone();
/* 187 */           searchConditionOption.setSelected(false);
/* 188 */           arrayList3.add(searchConditionOption);
/*     */         } 
/* 190 */       } catch (CloneNotSupportedException cloneNotSupportedException) {
/* 191 */         this.log.error(cloneNotSupportedException.getMessage(), cloneNotSupportedException);
/* 192 */         arrayList3 = new ArrayList<>();
/*     */       } 
/* 194 */       weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.SELECT, "1", "updateId", 120, arrayList3);
/*     */     } 
/* 196 */     weaTableEditComEntity.setViewAttr(1);
/* 197 */     arrayList1 = new ArrayList<>();
/* 198 */     arrayList1.add(weaTableEditComEntity);
/* 199 */     WeaTableEditEntity weaTableEditEntity3 = WeaTableEditUtil.getColumn(ConditionType.SELECT, SystemEnv.getHtmlLabelName(523786, Util.getIntValue(paramUser.getLanguage() + "")), "updateId", "25%", 1);
/* 200 */     weaTableEditEntity3.setCom(arrayList1);
/* 201 */     arrayList.add(weaTableEditEntity3);
/*     */ 
/*     */     
/* 204 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*     */     
/* 206 */     hashMap2.put("helpfulTip", SystemEnv.getHtmlLabelName(525839, Util.getIntValue(paramUser.getLanguage() + "")));
/* 207 */     WeaTableEditEntity weaTableEditEntity4 = WeaTableEditUtil.getColumn(ConditionType.CHECKBOX, SystemEnv.getHtmlLabelName(507298, Util.getIntValue(paramUser.getLanguage() + "")), "importBase", "15%", 1, hashMap2);
/* 208 */     jSONObject = new JSONObject();
/* 209 */     jSONObject = JSONObject.parseObject(JSONObject.toJSONString(weaTableEditEntity4));
/* 210 */     jSONObject.getJSONArray("com").getJSONObject(0).put("display", "switch");
/* 211 */     arrayList.add(jSONObject);
/*     */     
/* 213 */     return (List)arrayList;
/*     */   }
/*     */ 
/*     */   
/*     */   public JSONObject getSelectFormOnlyReadColumns(User paramUser, List<SearchConditionOption> paramList) {
/* 218 */     ImportTypeService importTypeService = new ImportTypeService();
/* 219 */     JSONObject jSONObject1 = new JSONObject();
/*     */     
/* 221 */     JSONObject jSONObject3 = new JSONObject();
/* 222 */     JSONArray jSONArray = new JSONArray();
/*     */ 
/*     */ 
/*     */     
/* 226 */     jSONObject3 = new JSONObject();
/* 227 */     jSONArray = new JSONArray();
/* 228 */     WeaTableEditComEntity weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.CHECKBOX, "1", "isImport", 120);
/* 229 */     JSONObject jSONObject2 = JSONObject.parseObject(JSONObject.toJSONString(weaTableEditComEntity));
/* 230 */     jSONObject2.put("display", "switch");
/* 231 */     jSONObject2.put("viewAttr", Integer.valueOf(1));
/* 232 */     jSONArray.add(jSONObject2);
/* 233 */     jSONObject3.put("com", jSONArray);
/* 234 */     jSONObject1.put("isImport", jSONObject3);
/*     */     
/* 236 */     jSONObject3 = new JSONObject();
/* 237 */     jSONArray = new JSONArray();
/* 238 */     weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.SELECT, "1", "importtype", 120, importTypeService.getImportType("0", paramUser, true));
/* 239 */     weaTableEditComEntity.setViewAttr(1);
/* 240 */     jSONArray.add(weaTableEditComEntity);
/* 241 */     jSONObject3.put("com", jSONArray);
/* 242 */     jSONObject1.put("importtype", jSONObject3);
/*     */ 
/*     */     
/* 245 */     jSONObject3 = new JSONObject();
/* 246 */     jSONArray = new JSONArray();
/* 247 */     if (paramList == null) {
/* 248 */       weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.SELECT, "1", "updateId", 120);
/*     */     } else {
/* 250 */       ArrayList<SearchConditionOption> arrayList = new ArrayList();
/*     */       
/*     */       try {
/* 253 */         for (byte b = 0; b < paramList.size(); b++) {
/* 254 */           SearchConditionOption searchConditionOption = ((SearchConditionOption)paramList.get(b)).clone();
/* 255 */           searchConditionOption.setSelected(false);
/* 256 */           arrayList.add(searchConditionOption);
/*     */         } 
/* 258 */       } catch (CloneNotSupportedException cloneNotSupportedException) {
/* 259 */         this.log.error(cloneNotSupportedException.getMessage(), cloneNotSupportedException);
/* 260 */         arrayList = new ArrayList<>();
/*     */       } 
/* 262 */       weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.SELECT, "1", "updateId", 120, arrayList);
/*     */     } 
/* 264 */     weaTableEditComEntity.setViewAttr(1);
/* 265 */     jSONObject2 = new JSONObject();
/* 266 */     jSONObject2 = JSONObject.parseObject(JSONObject.toJSONString(weaTableEditComEntity));
/* 267 */     jSONObject2.put("viewAttr", Integer.valueOf(1));
/* 268 */     jSONArray.add(jSONObject2);
/* 269 */     jSONObject3.put("com", jSONArray);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 276 */     jSONObject1.put("updateId", jSONObject3);
/*     */ 
/*     */     
/* 279 */     jSONObject3 = new JSONObject();
/* 280 */     jSONArray = new JSONArray();
/* 281 */     weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.CHECKBOX, "1", "importBase", 120);
/* 282 */     jSONObject2 = JSONObject.parseObject(JSONObject.toJSONString(weaTableEditComEntity));
/* 283 */     jSONObject2.put("display", "switch");
/* 284 */     jSONObject2.put("viewAttr", Integer.valueOf(1));
/* 285 */     jSONArray.add(jSONObject2);
/* 286 */     jSONObject3.put("com", jSONArray);
/* 287 */     jSONObject1.put("importBase", jSONObject3);
/* 288 */     return jSONObject1;
/*     */   }
/*     */ 
/*     */   
/*     */   public JSONObject getSelectFormImportTypeCells0(User paramUser, List<SearchConditionOption> paramList) {
/* 293 */     JSONObject jSONObject1 = new JSONObject();
/* 294 */     JSONObject jSONObject2 = new JSONObject();
/* 295 */     JSONArray jSONArray = new JSONArray();
/* 296 */     ArrayList<WeaTableEditComEntity> arrayList = new ArrayList();
/*     */     
/* 298 */     WeaTableEditComEntity weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.INPUT, "1", "importtype", 120);
/* 299 */     weaTableEditComEntity.setViewAttr(1, false);
/* 300 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 301 */     hashMap.put("hasBorder", Boolean.valueOf(false));
/* 302 */     weaTableEditComEntity.setOtherParams(hashMap);
/* 303 */     arrayList.add(weaTableEditComEntity);
/* 304 */     WeaTableEditEntity weaTableEditEntity1 = WeaTableEditUtil.getColumn(ConditionType.INPUT, SystemEnv.getHtmlLabelName(195, Util.getIntValue(paramUser.getLanguage() + "")), "updateId", "15%", 1);
/* 305 */     weaTableEditEntity1.setCom(arrayList);
/* 306 */     jSONObject1.put("importtype", weaTableEditEntity1);
/*     */     
/* 308 */     arrayList = new ArrayList<>();
/* 309 */     weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.INPUT, "1", "updateId", 120);
/* 310 */     weaTableEditComEntity.setViewAttr(1, false);
/* 311 */     hashMap = new HashMap<>();
/* 312 */     hashMap.put("hasBorder", Boolean.valueOf(false));
/* 313 */     weaTableEditComEntity.setOtherParams(hashMap);
/* 314 */     arrayList.add(weaTableEditComEntity);
/* 315 */     WeaTableEditEntity weaTableEditEntity2 = WeaTableEditUtil.getColumn(ConditionType.INPUT, SystemEnv.getHtmlLabelName(195, Util.getIntValue(paramUser.getLanguage() + "")), "updateId", "15%", 1);
/* 316 */     weaTableEditEntity2.setCom(arrayList);
/* 317 */     jSONObject1.put("updateId", weaTableEditEntity2);
/*     */     
/* 319 */     arrayList = new ArrayList<>();
/* 320 */     weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.INPUT, "1", "importBase", 120);
/* 321 */     weaTableEditComEntity.setViewAttr(1, false);
/* 322 */     hashMap = new HashMap<>();
/* 323 */     hashMap.put("hasBorder", Boolean.valueOf(false));
/* 324 */     weaTableEditComEntity.setOtherParams(hashMap);
/* 325 */     arrayList.add(weaTableEditComEntity);
/* 326 */     WeaTableEditEntity weaTableEditEntity3 = WeaTableEditUtil.getColumn(ConditionType.INPUT, SystemEnv.getHtmlLabelName(195, Util.getIntValue(paramUser.getLanguage() + "")), "importBase", "15%", 1);
/* 327 */     weaTableEditEntity3.setCom(arrayList);
/* 328 */     jSONObject1.put("importBase", weaTableEditEntity3);
/* 329 */     return jSONObject1;
/*     */   }
/*     */   
/*     */   public JSONObject getSelectFormImportTypeCells1(User paramUser, List<SearchConditionOption> paramList) {
/*     */     List list;
/* 334 */     JSONObject jSONObject1 = new JSONObject();
/* 335 */     JSONObject jSONObject2 = new JSONObject();
/* 336 */     JSONArray jSONArray = new JSONArray();
/*     */ 
/*     */     
/* 339 */     if (paramList != null && paramList.size() > 0) {
/* 340 */       list = (new ImportTypeService()).getImportType("0", paramUser, true);
/*     */     } else {
/* 342 */       list = (new ImportTypeService()).getImportType("1", paramUser, true);
/*     */     } 
/* 344 */     WeaTableEditComEntity weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.SELECT, "1", "importtype", 120, list);
/* 345 */     weaTableEditComEntity.setViewAttr(1);
/* 346 */     jSONArray.add(weaTableEditComEntity);
/* 347 */     jSONObject2.put("com", jSONArray);
/* 348 */     jSONObject1.put("importtype", jSONObject2);
/*     */     
/* 350 */     ArrayList<WeaTableEditComEntity> arrayList = new ArrayList();
/* 351 */     weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.INPUT, "1", "updateId", 120);
/* 352 */     weaTableEditComEntity.setViewAttr(1, false);
/* 353 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 354 */     hashMap.put("hasBorder", Boolean.valueOf(false));
/* 355 */     weaTableEditComEntity.setOtherParams(hashMap);
/* 356 */     arrayList.add(weaTableEditComEntity);
/* 357 */     WeaTableEditEntity weaTableEditEntity = WeaTableEditUtil.getColumn(ConditionType.INPUT, SystemEnv.getHtmlLabelName(195, Util.getIntValue(paramUser.getLanguage() + "")), "updateId", "15%", 1);
/* 358 */     weaTableEditEntity.setCom(arrayList);
/* 359 */     jSONObject1.put("updateId", weaTableEditEntity);
/*     */     
/* 361 */     jSONObject2 = new JSONObject();
/* 362 */     jSONArray = new JSONArray();
/* 363 */     weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.CHECKBOX, "1", "importBase", 120);
/* 364 */     JSONObject jSONObject3 = JSONObject.parseObject(JSONObject.toJSONString(weaTableEditComEntity));
/* 365 */     jSONObject3.put("display", "switch");
/* 366 */     jSONArray.add(jSONObject3);
/* 367 */     jSONObject2.put("com", jSONArray);
/* 368 */     jSONObject1.put("importBase", jSONObject2);
/*     */     
/* 370 */     return jSONObject1;
/*     */   }
/*     */   
/*     */   public JSONObject getSelectFormImportTypeCells2(User paramUser, List<SearchConditionOption> paramList) {
/*     */     List list;
/* 375 */     JSONObject jSONObject1 = new JSONObject();
/* 376 */     JSONObject jSONObject2 = new JSONObject();
/* 377 */     JSONArray jSONArray = new JSONArray();
/*     */ 
/*     */     
/* 380 */     if (paramList != null && paramList.size() > 0) {
/* 381 */       list = (new ImportTypeService()).getImportType("0", paramUser, true);
/*     */     } else {
/* 383 */       list = (new ImportTypeService()).getImportType("1", paramUser, true);
/*     */     } 
/* 385 */     WeaTableEditComEntity weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.SELECT, "1", "importtype", 120, list);
/* 386 */     weaTableEditComEntity.setViewAttr(1);
/* 387 */     jSONArray.add(weaTableEditComEntity);
/* 388 */     jSONObject2.put("com", jSONArray);
/* 389 */     jSONObject1.put("importtype", jSONObject2);
/*     */     
/* 391 */     jSONObject2 = new JSONObject();
/* 392 */     jSONArray = new JSONArray();
/* 393 */     if (paramList == null) {
/* 394 */       weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.SELECT, "1", "updateId", 120);
/*     */     } else {
/* 396 */       ArrayList<SearchConditionOption> arrayList = new ArrayList();
/*     */       
/*     */       try {
/* 399 */         for (byte b = 0; b < paramList.size(); b++) {
/* 400 */           SearchConditionOption searchConditionOption = ((SearchConditionOption)paramList.get(b)).clone();
/* 401 */           searchConditionOption.setSelected(false);
/* 402 */           arrayList.add(searchConditionOption);
/*     */         } 
/* 404 */       } catch (CloneNotSupportedException cloneNotSupportedException) {
/* 405 */         this.log.error(cloneNotSupportedException.getMessage(), cloneNotSupportedException);
/* 406 */         arrayList = new ArrayList<>();
/*     */       } 
/* 408 */       weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.SELECT, "1", "updateId", 120, arrayList);
/*     */     } 
/* 410 */     weaTableEditComEntity.setViewAttr(1);
/* 411 */     JSONObject jSONObject3 = new JSONObject();
/* 412 */     jSONObject3 = JSONObject.parseObject(JSONObject.toJSONString(weaTableEditComEntity));
/* 413 */     jSONArray.add(jSONObject3);
/* 414 */     jSONObject2.put("com", jSONArray);
/* 415 */     jSONObject1.put("updateId", jSONObject2);
/*     */     
/* 417 */     jSONObject2 = new JSONObject();
/* 418 */     jSONArray = new JSONArray();
/* 419 */     weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.CHECKBOX, "1", "importBase", 120);
/* 420 */     jSONObject3 = JSONObject.parseObject(JSONObject.toJSONString(weaTableEditComEntity));
/* 421 */     jSONObject3.put("display", "switch");
/* 422 */     jSONArray.add(jSONObject3);
/* 423 */     jSONObject2.put("com", jSONArray);
/* 424 */     jSONObject1.put("importBase", jSONObject2);
/*     */     
/* 426 */     return jSONObject1;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/systemExpAndImp/impl/DocSelectFrom.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */