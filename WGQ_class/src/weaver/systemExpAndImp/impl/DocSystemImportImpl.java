/*     */ package weaver.systemExpAndImp.impl;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONArray;
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.api.integration.util.RecordSetObj;
/*     */ import com.engine.hrm.cmd.matrix.biz.MatrixMaintColComInfo;
/*     */ import com.engine.hrm.cmd.matrix.biz.MatrixMaintComInfo;
/*     */ import com.engine.hrm.cmd.matrix.biz.MatrixMaintConditionComInfo;
/*     */ import com.engine.hrm.cmd.matrix.biz.MatrixMaintMemberComInfo;
/*     */ import com.engine.systemExpAndImp.selectform.SelectFormUtil;
/*     */ import com.engine.systemExpAndImp.sysimport.AbstractSystemImport;
/*     */ import com.engine.workflow.cmd.workflowImport.GetWorkflowImportListCmd;
/*     */ import java.io.InputStream;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.HashSet;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.backup.beans.ExceptionBean;
/*     */ import weaver.backup.beans.TableBean;
/*     */ import weaver.backup.logging.Logger;
/*     */ import weaver.backup.services.FutureService;
/*     */ import weaver.backup.services.ImportService;
/*     */ import weaver.backup.services.ImportTypeService;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.file.ImageFileManager;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.city.CityComInfo;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.companyvirtual.CompanyVirtualComInfo;
/*     */ import weaver.hrm.companyvirtual.DepartmentVirtualComInfo;
/*     */ import weaver.hrm.companyvirtual.SubCompanyVirtualComInfo;
/*     */ import weaver.hrm.country.CountryComInfo;
/*     */ import weaver.hrm.finance.BankComInfo;
/*     */ import weaver.hrm.job.JobActivitiesComInfo;
/*     */ import weaver.hrm.job.JobCallComInfo;
/*     */ import weaver.hrm.job.JobGroupsComInfo;
/*     */ import weaver.hrm.job.JobTitlesComInfo;
/*     */ import weaver.hrm.location.LocationComInfo;
/*     */ import weaver.hrm.province.ProvinceComInfo;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.hrm.roles.RolesComInfo;
/*     */ import weaver.systemExpAndImp.logging.LoggerFactory;
/*     */ import weaver.systemExpAndImp.moduleSetting.ModuleSettingManager;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class DocSystemImportImpl
/*     */   extends AbstractSystemImport
/*     */ {
/*  55 */   private Logger log = LoggerFactory.getLogger(getClass());
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected void doStandardImportOperation() {
/*  61 */     String str1 = Util.null2String(this.params.get("datas"));
/*  62 */     String str2 = Util.null2String(this.params.get("typeid"));
/*  63 */     JSONArray jSONArray1 = JSONArray.parseArray(str1);
/*  64 */     JSONObject jSONObject1 = null;
/*     */ 
/*     */ 
/*     */     
/*  68 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  69 */     ArrayList arrayList = new ArrayList();
/*     */     
/*  71 */     JSONObject jSONObject2 = new JSONObject();
/*  72 */     JSONObject jSONObject3 = new JSONObject();
/*  73 */     String str3 = "";
/*  74 */     JSONArray jSONArray2 = new JSONArray();
/*  75 */     JSONObject jSONObject4 = new JSONObject();
/*  76 */     HashSet<String> hashSet = new HashSet();
/*  77 */     ArrayList<String> arrayList1 = new ArrayList();
/*  78 */     for (byte b = 0; jSONArray1 != null && b < jSONArray1.size(); b++) {
/*     */       
/*  80 */       getProgressCom().setIndex(b + 1 + this.importIndex);
/*  81 */       jSONObject1 = jSONArray1.getJSONObject(b);
/*  82 */       str3 = Util.null2String(jSONObject1.getString("type"));
/*  83 */       getProgressCom().setModule(str3);
/*  84 */       String str4 = Util.null2String(jSONObject1.getString("name"));
/*  85 */       int i = Util.getIntValue(Util.null2String(jSONObject1.getString("fileid")));
/*  86 */       String str5 = getFileName(i);
/*     */ 
/*     */       
/*  89 */       String str6 = Util.null2String(jSONObject1.getString("importtype"));
/*  90 */       String str7 = "";
/*  91 */       if (str6.equals("0")) {
/*  92 */         str7 = Util.null2String(jSONObject1.getString("updateId"));
/*     */       }
/*     */       
/*  95 */       InputStream inputStream = ImageFileManager.getInputStreamById(i);
/*  96 */       ImportService importService = new ImportService();
/*  97 */       importService.setLogger(LoggerFactory.getLogger(ImportService.class));
/*  98 */       importService.setUser(this.user);
/*  99 */       importService.setType(str3);
/*     */ 
/*     */       
/* 102 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 103 */       String str8 = "";
/* 104 */       this.log.info("导入模块：" + str3);
/* 105 */       if (str6.equals("0")) {
/* 106 */         importService.setImportType("updateDatas");
/* 107 */         str8 = SystemEnv.getHtmlLabelName(17744, Util.getIntValue(Util.getIntValue("" + this.user.getLanguage(), 7) + ""));
/*     */ 
/*     */         
/* 110 */         importService.setGroupValue(str7);
/* 111 */         this.log.info("导入类型：更新，被更新的id值：" + str7);
/* 112 */         hashMap1.put("id", str7);
/* 113 */         if (!arrayList1.contains(str7)) {
/* 114 */           arrayList1.add(str7);
/*     */         }
/*     */       } else {
/* 117 */         importService.setImportType("insertDatas");
/* 118 */         str8 = SystemEnv.getHtmlLabelName(1421, Util.getIntValue(Util.getIntValue("" + this.user.getLanguage(), 7) + ""));
/* 119 */         this.log.info("导入类型：新增");
/*     */       } 
/* 121 */       hashMap1.put("type", str3);
/* 122 */       importService.setParam(hashMap1);
/* 123 */       importService.setParams(this.params);
/*     */ 
/*     */ 
/*     */       
/* 127 */       importService.setPreCheck(false);
/*     */       
/* 129 */       int j = Util.getIntValue(Util.null2String(jSONObject1.getString("importBase")), 0);
/* 130 */       if (j != 1) {
/* 131 */         importService.addIgnoreBaseData("hrm");
/* 132 */         this.log.info("是否导入基础数据：否");
/*     */       } else {
/* 134 */         this.log.info("是否导入基础数据：是");
/*     */       } 
/* 136 */       importService.setSystemImport(true);
/*     */       
/* 138 */       importService.importByStream(inputStream);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 148 */       hashSet.addAll(importService.getRemoveCacheInfos());
/* 149 */       Map map = importService.getMsgMap();
/* 150 */       GetWorkflowImportListCmd getWorkflowImportListCmd = new GetWorkflowImportListCmd(map, this.user);
/* 151 */       Map<String, ArrayList<HashMap<Object, Object>>> map1 = getWorkflowImportListCmd.getImportListInfosNew(str4);
/*     */       
/* 153 */       jSONObject4 = jSONObject1;
/* 154 */       ExceptionBean exceptionBean = importService.getExceptionBean();
/* 155 */       if (exceptionBean != null && exceptionBean.getDetail() != null) {
/* 156 */         jSONObject4.put("importResult", Integer.valueOf(0));
/* 157 */         TableBean tableBean = exceptionBean.getTable();
/* 158 */         HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 159 */         ArrayList<HashMap<Object, Object>> arrayList2 = new ArrayList();
/* 160 */         hashMap2.put("key", Integer.valueOf(0));
/* 161 */         if (tableBean != null) {
/* 162 */           hashMap2.put("id", tableBean.getKey());
/* 163 */           hashMap2.put("tablename", tableBean.getTableName());
/* 164 */           hashMap2.put("desc", tableBean.getDesc());
/*     */         } 
/* 166 */         hashMap2.put("msgname", str8);
/* 167 */         hashMap2.put("status", exceptionBean.getDetail());
/* 168 */         hashMap2.put("exceptionMsg", exceptionBean.getTitle());
/* 169 */         hashMap2.put("exceptionDetail", exceptionBean.getDetail());
/* 170 */         arrayList2.add(hashMap2);
/* 171 */         map1.put("dataSource", arrayList2);
/* 172 */         map1.put("name", str4);
/*     */       } else {
/* 174 */         jSONObject4.put("importResult", Integer.valueOf(1));
/*     */         
/* 176 */         saveImportLog(importService, str5, jSONObject1);
/*     */       } 
/* 178 */       jSONObject2.put(i + "", map1);
/* 179 */       jSONArray2.add(jSONObject4);
/*     */     } 
/*     */     
/* 182 */     JSONObject jSONObject5 = new JSONObject();
/* 183 */     jSONObject5.put("title", (new ModuleSettingManager()).getModuleNameWithParentById(str3, this.user.getLanguage()));
/* 184 */     jSONObject5.put("id", str2);
/* 185 */     jSONObject5.put("columns", (new SelectFormUtil(this.user)).getImportResultCols(str3, (new ImportTypeService()).getSystemOptions(arrayList1, str3)));
/* 186 */     jSONObject5.put("values", jSONArray2);
/* 187 */     jSONObject5.put("result", jSONObject2);
/* 188 */     getProgressCom().addResultInfo(jSONObject5);
/*     */     
/* 190 */     removeCache(hashSet);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected void saveImportLog(ImportService paramImportService, String paramString, JSONObject paramJSONObject) {
/* 202 */     String str1 = Util.null2String(paramJSONObject.getString("importtype"));
/*     */     
/* 204 */     if (str1.equals("0")) {
/* 205 */       str1 = "update";
/*     */     } else {
/* 207 */       str1 = "add";
/*     */     } 
/*     */     
/* 210 */     String str2 = Util.null2String(paramJSONObject.getString("type"));
/*     */     
/* 212 */     RecordSetObj recordSetObj = new RecordSetObj();
/* 213 */     String str3 = "";
/* 214 */     recordSetObj.executeQuery("select selectid from workflow_exchange_module where id = ?", new Object[] { str2 });
/* 215 */     if (recordSetObj.next()) {
/* 216 */       str3 = Util.null2String(recordSetObj.getString("selectid"));
/*     */     }
/*     */     
/* 219 */     recordSetObj.executeQuery("select tablename,primarykey from workflow_exchange where eid = ? and type = ?", new Object[] { str3, str2 });
/* 220 */     String str4 = "";
/* 221 */     String str5 = "";
/* 222 */     if (recordSetObj.next()) {
/* 223 */       str4 = Util.null2String(recordSetObj.getString("tablename"));
/* 224 */       str5 = Util.null2String(recordSetObj.getString("primarykey"));
/*     */     } 
/*     */     
/* 227 */     ArrayList<String> arrayList = paramImportService.getNewValue(str4, str5);
/* 228 */     String str6 = (arrayList.size() > 0) ? arrayList.get(0) : "0";
/*     */ 
/*     */     
/* 231 */     String str7 = Util.null2String(this.params.get("param_ip"));
/*     */     
/* 233 */     int i = this.user.getUID();
/*     */     
/* 235 */     String str8 = paramImportService.getImportDateTime();
/*     */     
/* 237 */     String str9 = "insert into workflow_importlog(workflowid, clientip, operator, operatetime, issamesystem, importtype, isimportbase, iscreateform, importfilename,type) values(?,?,?,?,?,?,?,?,?,?)";
/* 238 */     RecordSet recordSet = new RecordSet();
/* 239 */     recordSet.executeUpdate(str9, new Object[] { str6, str7, Integer.valueOf(i), str8, "", str1, "", "", paramString, str2 });
/*     */     
/* 241 */     str9 = "select max(id) from workflow_importlog";
/* 242 */     recordSet.executeQuery(str9, new Object[0]);
/* 243 */     String str10 = "";
/* 244 */     if (recordSet.next()) {
/* 245 */       str10 = recordSet.getString(1);
/*     */     }
/*     */     
/* 248 */     List list = paramImportService.getImportLog().getLogs();
/* 249 */     if (list != null && list.size() > 0)
/*     */     {
/* 251 */       saveImportLogDetail(list, str10);
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void removeCache(final HashSet<String> removeCacheInfos) {
/* 259 */     this.log.info("开始清除缓存：系统导入");
/* 260 */     Thread thread = new Thread()
/*     */       {
/*     */         public void run() {
/* 263 */           FutureService futureService = new FutureService(DocSystemImportImpl.this.log, 5L);
/* 264 */           if (removeCacheInfos.contains("Cache_Hrm_CompanyVirtual")) {
/* 265 */             futureService.doFuture(CompanyVirtualComInfo.class.getName(), "removeCompanyCache");
/*     */           }
/* 267 */           if (removeCacheInfos.contains("Cache_Hrm_Subcompany")) {
/* 268 */             futureService.doFuture(SubCompanyComInfo.class.getName(), "removeCompanyCache");
/*     */           }
/* 270 */           if (removeCacheInfos.contains("Cache_Hrm_SubcompanyVirtual")) {
/* 271 */             futureService.doFuture(SubCompanyVirtualComInfo.class.getName(), "removeSubCompanyCache");
/*     */           }
/* 273 */           if (removeCacheInfos.contains("Cache_Hrm_Department")) {
/* 274 */             futureService.doFuture(DepartmentComInfo.class.getName(), "removeCompanyCache");
/*     */           }
/* 276 */           if (removeCacheInfos.contains("Cache_Hrm_DepartmentVirtual")) {
/* 277 */             futureService.doFuture(DepartmentVirtualComInfo.class.getName(), "removeDepartmentCache");
/*     */           }
/* 279 */           if (removeCacheInfos.contains("Cache_Hrm_Resource")) {
/* 280 */             futureService.doFuture(ResourceComInfo.class.getName(), "removeResourceCache");
/*     */           }
/* 282 */           if (removeCacheInfos.contains("Cache_Hrm_Jobtitles")) {
/* 283 */             futureService.doFuture(JobTitlesComInfo.class.getName(), "removeJobTitlesCache");
/*     */           }
/* 285 */           if (removeCacheInfos.contains("Cache_Hrm_Roles")) {
/* 286 */             futureService.doFuture(RolesComInfo.class.getName(), "removeRolesCache");
/*     */           }
/* 288 */           if (removeCacheInfos.contains("Cache_Hrm_Jobactivities")) {
/* 289 */             futureService.doFuture(JobActivitiesComInfo.class.getName(), "removeJobActivitiesCache");
/*     */           }
/* 291 */           if (removeCacheInfos.contains("Cache_Hrm_Location")) {
/* 292 */             futureService.doFuture(LocationComInfo.class.getName(), "removeLocationCache");
/*     */           }
/* 294 */           if (removeCacheInfos.contains("Cache_Hrm_City")) {
/* 295 */             futureService.doFuture(CityComInfo.class.getName(), "removeCityCache");
/*     */           }
/* 297 */           if (removeCacheInfos.contains("Cache_Hrm_Province")) {
/* 298 */             futureService.doFuture(ProvinceComInfo.class.getName(), "removeProvinceCache");
/*     */           }
/* 300 */           if (removeCacheInfos.contains("Cache_Hrm_Country")) {
/* 301 */             futureService.doFuture(CountryComInfo.class.getName(), "removeCountryCache");
/*     */           }
/* 303 */           if (removeCacheInfos.contains("Cache_Hrm_Jobgroups")) {
/* 304 */             futureService.doFuture(JobGroupsComInfo.class.getName(), "removeCache");
/*     */           }
/* 306 */           if (removeCacheInfos.contains("Cache_Hrm_Jobcall")) {
/* 307 */             futureService.doFuture(JobCallComInfo.class.getName(), "removeJobCallCache");
/*     */           }
/* 309 */           if (removeCacheInfos.contains("Cache_Hrm_Bank")) {
/* 310 */             futureService.doFuture(BankComInfo.class.getName(), "removeBankCache");
/*     */           }
/* 312 */           if (removeCacheInfos.contains("Cache_Hrm_Matrix")) {
/* 313 */             futureService.doFuture(MatrixMaintComInfo.class.getName(), "removeCache");
/* 314 */             futureService.doFuture(MatrixMaintColComInfo.class.getName(), "removeCache");
/* 315 */             futureService.doFuture(MatrixMaintConditionComInfo.class.getName(), "removeCache");
/* 316 */             futureService.doFuture(MatrixMaintMemberComInfo.class.getName(), "removeCache");
/*     */           } 
/* 318 */           futureService.doFuture("weaver.docs.category.MainCategoryComInfo", "removeMainCategoryCache");
/* 319 */           futureService.doFuture("weaver.docs.category.SubCategoryComInfo", "removeMainCategoryCache");
/* 320 */           futureService.doFuture("weaver.docs.category.SecCategoryComInfo", "removeMainCategoryCache");
/* 321 */           futureService.doFuture("weaver.docs.category.DocTreelistComInfo", "removeGetDocListInfordCache");
/*     */         }
/*     */       };
/* 324 */     thread.start();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/systemExpAndImp/impl/DocSystemImportImpl.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */