/*     */ package weaver.systemExpAndImp.impl;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONArray;
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.api.integration.util.RecordSetObj;
/*     */ import com.engine.hrm.cmd.matrix.biz.MatrixMaintColComInfo;
/*     */ import com.engine.hrm.cmd.matrix.biz.MatrixMaintComInfo;
/*     */ import com.engine.hrm.cmd.matrix.biz.MatrixMaintConditionComInfo;
/*     */ import com.engine.hrm.cmd.matrix.biz.MatrixMaintMemberComInfo;
/*     */ import com.engine.systemExpAndImp.selectform.SelectFormUtil;
/*     */ import com.engine.systemExpAndImp.sysimport.AbstractSystemImport;
/*     */ import com.engine.workflow.cmd.workflowImport.GetWorkflowImportListCmd;
/*     */ import java.io.InputStream;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.HashSet;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.backup.beans.ExceptionBean;
/*     */ import weaver.backup.beans.TableBean;
/*     */ import weaver.backup.logging.Logger;
/*     */ import weaver.backup.services.FutureService;
/*     */ import weaver.backup.services.ImportService;
/*     */ import weaver.backup.services.ImportTypeService;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.file.ImageFileManager;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.city.CityComInfo;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.companyvirtual.CompanyVirtualComInfo;
/*     */ import weaver.hrm.companyvirtual.DepartmentVirtualComInfo;
/*     */ import weaver.hrm.companyvirtual.SubCompanyVirtualComInfo;
/*     */ import weaver.hrm.country.CountryComInfo;
/*     */ import weaver.hrm.finance.BankComInfo;
/*     */ import weaver.hrm.job.JobActivitiesComInfo;
/*     */ import weaver.hrm.job.JobCallComInfo;
/*     */ import weaver.hrm.job.JobGroupsComInfo;
/*     */ import weaver.hrm.job.JobTitlesComInfo;
/*     */ import weaver.hrm.location.LocationComInfo;
/*     */ import weaver.hrm.province.ProvinceComInfo;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.hrm.roles.RolesComInfo;
/*     */ import weaver.systemExpAndImp.logging.LoggerFactory;
/*     */ import weaver.systemExpAndImp.moduleSetting.ModuleSettingManager;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ public class MatrixSystemImportImpl extends AbstractSystemImport {
/*  49 */   private Logger log = LoggerFactory.getLogger(getClass());
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected void doStandardImportOperation() {
/*  55 */     String str1 = Util.null2String(this.params.get("datas"));
/*  56 */     String str2 = Util.null2String(this.params.get("typeid"));
/*  57 */     JSONArray jSONArray1 = JSONArray.parseArray(str1);
/*  58 */     JSONObject jSONObject1 = null;
/*     */ 
/*     */ 
/*     */     
/*  62 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  63 */     ArrayList arrayList = new ArrayList();
/*     */     
/*  65 */     JSONObject jSONObject2 = new JSONObject();
/*  66 */     JSONObject jSONObject3 = new JSONObject();
/*  67 */     String str3 = "";
/*  68 */     JSONArray jSONArray2 = new JSONArray();
/*  69 */     JSONObject jSONObject4 = new JSONObject();
/*  70 */     HashSet<String> hashSet = new HashSet();
/*  71 */     ArrayList<String> arrayList1 = new ArrayList();
/*  72 */     for (byte b = 0; jSONArray1 != null && b < jSONArray1.size(); b++) {
/*     */       
/*  74 */       getProgressCom().setIndex(b + 1 + this.importIndex);
/*  75 */       jSONObject1 = jSONArray1.getJSONObject(b);
/*  76 */       str3 = Util.null2String(jSONObject1.getString("type"));
/*  77 */       getProgressCom().setModule(str3);
/*  78 */       String str4 = Util.null2String(jSONObject1.getString("name"));
/*  79 */       int i = Util.getIntValue(Util.null2String(jSONObject1.getString("fileid")));
/*  80 */       String str5 = getFileName(i);
/*     */ 
/*     */       
/*  83 */       String str6 = Util.null2String(jSONObject1.getString("importtype"));
/*  84 */       String str7 = "";
/*  85 */       if (str6.equals("0")) {
/*  86 */         str7 = Util.null2String(jSONObject1.getString("updateId"));
/*     */       }
/*     */       
/*  89 */       InputStream inputStream = ImageFileManager.getInputStreamById(i);
/*  90 */       ImportService importService = new ImportService();
/*  91 */       importService.setLogger(LoggerFactory.getLogger(ImportService.class));
/*  92 */       importService.setUser(this.user);
/*  93 */       importService.setType(str3);
/*     */ 
/*     */       
/*  96 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  97 */       String str8 = "";
/*  98 */       this.log.info("导入模块：" + str3);
/*  99 */       if (str6.equals("0")) {
/* 100 */         importService.setImportType("updateDatas");
/* 101 */         str8 = SystemEnv.getHtmlLabelName(17744, Util.getIntValue(Util.getIntValue("" + this.user.getLanguage(), 7) + ""));
/*     */         
/* 103 */         importService.setGroupValue(str7);
/* 104 */         hashMap1.put("id", str7);
/* 105 */         this.log.info("导入类型：更新，被更新的id值：" + str7);
/* 106 */         if (!arrayList1.contains(str7)) {
/* 107 */           arrayList1.add(str7);
/*     */         }
/*     */       } else {
/* 110 */         importService.setImportType("insertDatas");
/* 111 */         str8 = SystemEnv.getHtmlLabelName(1421, Util.getIntValue(Util.getIntValue("" + this.user.getLanguage(), 7) + ""));
/* 112 */         this.log.info("导入类型：新增");
/*     */       } 
/* 114 */       hashMap1.put("type", str3);
/* 115 */       importService.setParam(hashMap1);
/* 116 */       importService.setParams(this.params);
/*     */ 
/*     */ 
/*     */       
/* 120 */       importService.setPreCheck(false);
/*     */       
/* 122 */       int j = Util.getIntValue(Util.null2String(jSONObject1.getString("importBase")), 0);
/* 123 */       if (j != 1) {
/* 124 */         importService.addIgnoreBaseData("hrm");
/* 125 */         this.log.info("是否导入基础数据：否");
/*     */       } else {
/* 127 */         this.log.info("是否导入基础数据：是");
/*     */       } 
/*     */       
/* 130 */       String str9 = Util.null2String(jSONObject1.getString("importIntegration"));
/*     */       
/* 132 */       if (!"1".equals(str9)) {
/* 133 */         String str = "select eid from workflow_exchange where subtype = 'integration' and type = 'hrm_matrix'";
/* 134 */         RecordSet recordSet = new RecordSet();
/* 135 */         recordSet.executeQuery(str, new Object[0]);
/* 136 */         while (recordSet.next()) {
/* 137 */           importService.addIgnoreTable(recordSet.getString("eid"));
/*     */         }
/* 139 */         this.log.info("是否导入集成数据：否");
/*     */       } else {
/* 141 */         this.log.info("是否导入集成数据：是");
/*     */       } 
/* 143 */       importService.setSystemImport(true);
/*     */       
/* 145 */       importService.importByStream(inputStream);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 155 */       hashSet.addAll(importService.getRemoveCacheInfos());
/* 156 */       Map map = importService.getMsgMap();
/* 157 */       GetWorkflowImportListCmd getWorkflowImportListCmd = new GetWorkflowImportListCmd(map, this.user);
/* 158 */       Map<String, ArrayList<HashMap<Object, Object>>> map1 = getWorkflowImportListCmd.getImportListInfosNew(str4);
/*     */       
/* 160 */       jSONObject4 = jSONObject1;
/* 161 */       ExceptionBean exceptionBean = importService.getExceptionBean();
/* 162 */       if (exceptionBean != null && exceptionBean.getDetail() != null) {
/* 163 */         jSONObject4.put("importResult", Integer.valueOf(0));
/* 164 */         TableBean tableBean = exceptionBean.getTable();
/* 165 */         HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 166 */         ArrayList<HashMap<Object, Object>> arrayList2 = new ArrayList();
/* 167 */         hashMap2.put("key", Integer.valueOf(0));
/* 168 */         if (tableBean != null) {
/* 169 */           hashMap2.put("id", tableBean.getKey());
/* 170 */           hashMap2.put("tablename", tableBean.getTableName());
/* 171 */           hashMap2.put("desc", tableBean.getDesc());
/*     */         } 
/* 173 */         hashMap2.put("msgname", str8);
/* 174 */         hashMap2.put("status", exceptionBean.getDetail());
/* 175 */         hashMap2.put("exceptionMsg", exceptionBean.getTitle());
/* 176 */         hashMap2.put("exceptionDetail", exceptionBean.getDetail());
/* 177 */         arrayList2.add(hashMap2);
/* 178 */         map1.put("dataSource", arrayList2);
/* 179 */         map1.put("name", str4);
/*     */       } else {
/* 181 */         jSONObject4.put("importResult", Integer.valueOf(1));
/*     */         
/* 183 */         saveImportLog(importService, str5, jSONObject1);
/*     */       } 
/* 185 */       jSONObject2.put(i + "", map1);
/* 186 */       jSONArray2.add(jSONObject4);
/*     */     } 
/*     */     
/* 189 */     JSONObject jSONObject5 = new JSONObject();
/* 190 */     jSONObject5.put("title", (new ModuleSettingManager()).getModuleNameWithParentById(str3, this.user.getLanguage()));
/* 191 */     jSONObject5.put("id", str2);
/* 192 */     jSONObject5.put("columns", (new SelectFormUtil(this.user)).getImportResultCols(str3, (new ImportTypeService()).getSystemOptions(arrayList1, str3)));
/* 193 */     jSONObject5.put("values", jSONArray2);
/* 194 */     jSONObject5.put("result", jSONObject2);
/* 195 */     getProgressCom().addResultInfo(jSONObject5);
/*     */     
/* 197 */     removeCache(hashSet);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected void saveImportLog(ImportService paramImportService, String paramString, JSONObject paramJSONObject) {
/* 210 */     String str1 = Util.null2String(paramJSONObject.getString("importtype"));
/*     */     
/* 212 */     if (str1.equals("0")) {
/* 213 */       str1 = "update";
/*     */     } else {
/* 215 */       str1 = "add";
/*     */     } 
/*     */     
/* 218 */     String str2 = Util.null2String(paramJSONObject.getString("type"));
/*     */     
/* 220 */     RecordSetObj recordSetObj = new RecordSetObj();
/* 221 */     String str3 = "";
/* 222 */     recordSetObj.executeQuery("select selectid from workflow_exchange_module where id = ?", new Object[] { str2 });
/* 223 */     if (recordSetObj.next()) {
/* 224 */       str3 = Util.null2String(recordSetObj.getString("selectid"));
/*     */     }
/*     */     
/* 227 */     ArrayList<String> arrayList = paramImportService.getNewValue("MATRIXINFO", "id");
/* 228 */     String str4 = (arrayList.size() > 0) ? arrayList.get(0) : "0";
/*     */ 
/*     */     
/* 231 */     String str5 = Util.null2String(this.params.get("param_ip"));
/*     */     
/* 233 */     int i = this.user.getUID();
/*     */     
/* 235 */     String str6 = paramImportService.getImportDateTime();
/*     */     
/* 237 */     String str7 = "insert into workflow_importlog(workflowid, clientip, operator, operatetime, issamesystem, importtype, isimportbase, iscreateform, importfilename,type) values(?,?,?,?,?,?,?,?,?,?)";
/* 238 */     RecordSet recordSet = new RecordSet();
/* 239 */     recordSet.executeUpdate(str7, new Object[] { str4, str5, Integer.valueOf(i), str6, "", str1, "", "", paramString, str2 });
/*     */     
/* 241 */     str7 = "select max(id) from workflow_importlog";
/* 242 */     recordSet.executeQuery(str7, new Object[0]);
/* 243 */     String str8 = "";
/* 244 */     if (recordSet.next()) {
/* 245 */       str8 = recordSet.getString(1);
/*     */     }
/*     */     
/* 248 */     List list = paramImportService.getImportLog().getLogs();
/* 249 */     if (list != null && list.size() > 0)
/*     */     {
/* 251 */       saveImportLogDetail(list, str8);
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void removeCache(final HashSet<String> removeCacheInfos) {
/* 259 */     this.log.info("开始清除缓存：系统导入");
/* 260 */     Thread thread = new Thread()
/*     */       {
/*     */         public void run() {
/* 263 */           FutureService futureService = new FutureService(MatrixSystemImportImpl.this.log, 5L);
/*     */           
/* 265 */           if (removeCacheInfos.contains("Cache_Hrm_CompanyVirtual")) {
/* 266 */             futureService.doFuture(CompanyVirtualComInfo.class.getName(), "removeCompanyCache");
/*     */           }
/* 268 */           if (removeCacheInfos.contains("Cache_Hrm_Subcompany")) {
/* 269 */             futureService.doFuture(SubCompanyComInfo.class.getName(), "removeCompanyCache");
/*     */           }
/* 271 */           if (removeCacheInfos.contains("Cache_Hrm_SubcompanyVirtual")) {
/* 272 */             futureService.doFuture(SubCompanyVirtualComInfo.class.getName(), "removeSubCompanyCache");
/*     */           }
/* 274 */           if (removeCacheInfos.contains("Cache_Hrm_Department")) {
/* 275 */             futureService.doFuture(DepartmentComInfo.class.getName(), "removeCompanyCache");
/*     */           }
/* 277 */           if (removeCacheInfos.contains("Cache_Hrm_DepartmentVirtual")) {
/* 278 */             futureService.doFuture(DepartmentVirtualComInfo.class.getName(), "removeDepartmentCache");
/*     */           }
/* 280 */           if (removeCacheInfos.contains("Cache_Hrm_Resource")) {
/* 281 */             futureService.doFuture(ResourceComInfo.class.getName(), "removeResourceCache");
/*     */           }
/* 283 */           if (removeCacheInfos.contains("Cache_Hrm_Jobtitles")) {
/* 284 */             futureService.doFuture(JobTitlesComInfo.class.getName(), "removeJobTitlesCache");
/*     */           }
/* 286 */           if (removeCacheInfos.contains("Cache_Hrm_Roles")) {
/* 287 */             futureService.doFuture(RolesComInfo.class.getName(), "removeRolesCache");
/*     */           }
/* 289 */           if (removeCacheInfos.contains("Cache_Hrm_Jobactivities")) {
/* 290 */             futureService.doFuture(JobActivitiesComInfo.class.getName(), "removeJobActivitiesCache");
/*     */           }
/* 292 */           if (removeCacheInfos.contains("Cache_Hrm_Location")) {
/* 293 */             futureService.doFuture(LocationComInfo.class.getName(), "removeLocationCache");
/*     */           }
/* 295 */           if (removeCacheInfos.contains("Cache_Hrm_City")) {
/* 296 */             futureService.doFuture(CityComInfo.class.getName(), "removeCityCache");
/*     */           }
/* 298 */           if (removeCacheInfos.contains("Cache_Hrm_Province")) {
/* 299 */             futureService.doFuture(ProvinceComInfo.class.getName(), "removeProvinceCache");
/*     */           }
/* 301 */           if (removeCacheInfos.contains("Cache_Hrm_Country")) {
/* 302 */             futureService.doFuture(CountryComInfo.class.getName(), "removeCountryCache");
/*     */           }
/* 304 */           if (removeCacheInfos.contains("Cache_Hrm_Jobgroups")) {
/* 305 */             futureService.doFuture(JobGroupsComInfo.class.getName(), "removeCache");
/*     */           }
/* 307 */           if (removeCacheInfos.contains("Cache_Hrm_Jobcall")) {
/* 308 */             futureService.doFuture(JobCallComInfo.class.getName(), "removeJobCallCache");
/*     */           }
/* 310 */           if (removeCacheInfos.contains("Cache_Hrm_Bank")) {
/* 311 */             futureService.doFuture(BankComInfo.class.getName(), "removeBankCache");
/*     */           }
/* 313 */           if (removeCacheInfos.contains("Cache_Hrm_Matrix")) {
/* 314 */             futureService.doFuture(MatrixMaintComInfo.class.getName(), "removeCache");
/* 315 */             futureService.doFuture(MatrixMaintColComInfo.class.getName(), "removeCache");
/* 316 */             futureService.doFuture(MatrixMaintConditionComInfo.class.getName(), "removeCache");
/* 317 */             futureService.doFuture(MatrixMaintMemberComInfo.class.getName(), "removeCache");
/*     */           } 
/* 319 */           futureService.doFuture("com.engine.hrm.cmd.matrix.biz.MatrixinfoComInfo", "removeCache");
/* 320 */           futureService.doFuture("com.engine.hrm.cmd.matrix.biz.MatrixFieldsComInfo", "removeCache");
/* 321 */           futureService.doFuture("com.engine.hrm.cmd.matrix.listnew.MatrixUtilToolCmd", "refreshMatrixCominfo");
/*     */         }
/*     */       };
/* 324 */     thread.start();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/systemExpAndImp/impl/MatrixSystemImportImpl.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */