/*     */ package weaver.systemExpAndImp.impl;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONArray;
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.engine.common.entity.BizLogContext;
/*     */ import com.engine.hrm.cmd.matrix.biz.MatrixMaintColComInfo;
/*     */ import com.engine.hrm.cmd.matrix.biz.MatrixMaintComInfo;
/*     */ import com.engine.hrm.cmd.matrix.biz.MatrixMaintConditionComInfo;
/*     */ import com.engine.hrm.cmd.matrix.biz.MatrixMaintMemberComInfo;
/*     */ import com.engine.systemExpAndImp.selectform.SelectFormUtil;
/*     */ import com.engine.workflow.cmd.workflowImport.GetWorkflowImportListCmd;
/*     */ import java.io.InputStream;
/*     */ import java.text.SimpleDateFormat;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.HashSet;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.backup.beans.ExceptionBean;
/*     */ import weaver.backup.beans.TableBean;
/*     */ import weaver.backup.logging.Logger;
/*     */ import weaver.backup.services.FutureService;
/*     */ import weaver.backup.services.ImportService;
/*     */ import weaver.backup.services.ImportTypeService;
/*     */ import weaver.backup.utils.ProgressCom;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.file.ImageFileManager;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.hrm.city.CityComInfo;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.companyvirtual.CompanyVirtualComInfo;
/*     */ import weaver.hrm.companyvirtual.DepartmentVirtualComInfo;
/*     */ import weaver.hrm.companyvirtual.SubCompanyVirtualComInfo;
/*     */ import weaver.hrm.country.CountryComInfo;
/*     */ import weaver.hrm.finance.BankComInfo;
/*     */ import weaver.hrm.job.JobActivitiesComInfo;
/*     */ import weaver.hrm.job.JobCallComInfo;
/*     */ import weaver.hrm.job.JobGroupsComInfo;
/*     */ import weaver.hrm.job.JobTitlesComInfo;
/*     */ import weaver.hrm.location.LocationComInfo;
/*     */ import weaver.hrm.province.ProvinceComInfo;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.hrm.roles.RolesComInfo;
/*     */ import weaver.systemExpAndImp.itf.ISystemImport;
/*     */ import weaver.systemExpAndImp.logging.LoggerFactory;
/*     */ import weaver.systemExpAndImp.moduleSetting.ModuleSettingManager;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ public class WorkflowSystemImportImpl
/*     */   implements ISystemImport
/*     */ {
/*     */   private User user;
/*     */   private Map<String, Object> params;
/*     */   private ProgressCom progressCom;
/*  57 */   private Logger log = LoggerFactory.getLogger(getClass());
/*     */   
/*     */   private boolean isImportBase = false;
/*     */   
/*     */   private boolean isImportIntegration = false;
/*  62 */   public int count = 0;
/*  63 */   public int importIndex = 0;
/*     */ 
/*     */   
/*     */   public Map<String, Object> doImport(Map<String, Object> paramMap, User paramUser) {
/*  67 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  68 */     this.user = paramUser;
/*  69 */     this.params = paramMap;
/*     */     
/*  71 */     String str1 = Util.null2String(paramMap.get("datas"));
/*  72 */     JSONArray jSONArray = JSONArray.parseArray(str1);
/*  73 */     JSONObject jSONObject = null;
/*  74 */     String str2 = "";
/*  75 */     byte b = 0; if (jSONArray != null && b < jSONArray.size()) {
/*  76 */       jSONObject = jSONArray.getJSONObject(b);
/*  77 */       str2 = Util.null2String(jSONObject.getString("type"));
/*     */     } 
/*     */     
/*  80 */     doWorkflowImportOperation();
/*  81 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void doWorkflowImportOperation() {
/*  91 */     String str1 = Util.null2String(this.params.get("datas"));
/*  92 */     String str2 = Util.null2String(this.params.get("typeid"));
/*  93 */     JSONArray jSONArray1 = JSONArray.parseArray(str1);
/*  94 */     JSONObject jSONObject1 = null;
/*     */ 
/*     */     
/*  97 */     String str3 = "";
/*  98 */     String str4 = "";
/*  99 */     String str5 = "";
/* 100 */     Map map1 = null;
/* 101 */     Map map2 = null;
/* 102 */     Map map3 = null;
/* 103 */     Map<String, ArrayList<HashMap<Object, Object>>> map = null;
/*     */     
/* 105 */     ArrayList arrayList = new ArrayList();
/*     */ 
/*     */     
/* 108 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 109 */     JSONObject jSONObject2 = new JSONObject();
/* 110 */     JSONObject jSONObject3 = new JSONObject();
/* 111 */     String str6 = "";
/* 112 */     JSONArray jSONArray2 = new JSONArray();
/* 113 */     JSONObject jSONObject4 = new JSONObject();
/* 114 */     HashSet<String> hashSet = new HashSet();
/* 115 */     ArrayList<String> arrayList1 = new ArrayList();
/* 116 */     for (byte b = 0; jSONArray1 != null && b < jSONArray1.size(); b++) {
/* 117 */       getProgressCom().setIndex(b + 1 + this.importIndex);
/* 118 */       jSONObject1 = jSONArray1.getJSONObject(b);
/* 119 */       str6 = Util.null2String(jSONObject1.getString("type"));
/* 120 */       getProgressCom().setModule(str6);
/* 121 */       String str7 = Util.null2String(jSONObject1.getString("name"));
/* 122 */       int i = Util.getIntValue(Util.null2String(jSONObject1.getString("fileid")));
/* 123 */       String str8 = getFielName(i);
/*     */       
/* 125 */       String str9 = Util.null2String(jSONObject1.getString("importtype"));
/* 126 */       int j = 0;
/* 127 */       if (str9.equals("0")) {
/* 128 */         j = Util.getIntValue(Util.null2String(jSONObject1.getString("updateId")), -1);
/*     */       }
/*     */       
/* 131 */       InputStream inputStream = ImageFileManager.getInputStreamById(i);
/* 132 */       ImportService importService = new ImportService();
/* 133 */       importService.setLogger(LoggerFactory.getLogger(ImportService.class));
/* 134 */       importService.setUser(this.user);
/* 135 */       importService.setType("workflow");
/*     */       
/* 137 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 138 */       String str10 = "";
/* 139 */       this.log.info("导入模块：workflow");
/* 140 */       if (str9.equals("0")) {
/* 141 */         importService.setImportType("updateDatas");
/* 142 */         str10 = SystemEnv.getHtmlLabelName(17744, Util.getIntValue(Util.getIntValue("" + this.user.getLanguage(), 7) + ""));
/*     */         
/* 144 */         importService.setGroupValue("" + j);
/* 145 */         hashMap1.put("id", Integer.valueOf(j));
/* 146 */         this.log.info("导入类型：更新，被更新的id值：" + j);
/* 147 */         if (!arrayList1.contains(Integer.valueOf(j))) {
/* 148 */           arrayList1.add(j + "");
/*     */         }
/*     */       } else {
/* 151 */         importService.setImportType("insertDatas");
/* 152 */         str10 = SystemEnv.getHtmlLabelName(1421, Util.getIntValue(Util.getIntValue("" + this.user.getLanguage(), 7) + ""));
/* 153 */         hashMap1.put("import_column_condition_tablename", "workflow_base.id");
/* 154 */         this.log.info("导入类型：新增");
/*     */       } 
/* 156 */       hashMap1.put("type", "workflow");
/* 157 */       importService.setParam(hashMap1);
/* 158 */       importService.setParams(this.params);
/*     */       
/* 160 */       importService.setSystemType("0");
/*     */       
/* 162 */       int k = Util.getIntValue(Util.null2String(this.params.get("preCheck")), 1);
/*     */       
/* 164 */       importService.setPreCheck(false);
/*     */       
/* 166 */       int m = Util.getIntValue(Util.null2String(jSONObject1.getString("importBase")), 0);
/* 167 */       if (m != 1) {
/* 168 */         importService.addIgnoreBaseData("hrm");
/* 169 */         importService.addIgnoreBaseData("doc");
/* 170 */         importService.addIgnoreBaseData("matrix");
/* 171 */         this.log.info("是否导入基础数据：否");
/*     */       } else {
/* 173 */         this.isImportBase = true;
/* 174 */         this.log.info("是否导入基础数据：是");
/*     */       } 
/*     */       
/* 177 */       String str11 = Util.null2String(jSONObject1.getString("importIntegration"));
/*     */       
/* 179 */       if (!"1".equals(str11)) {
/* 180 */         String str = "select eid from workflow_exchange where subtype = 'integration' and type = 'workflow'";
/* 181 */         RecordSet recordSet = new RecordSet();
/* 182 */         recordSet.executeQuery(str, new Object[0]);
/* 183 */         while (recordSet.next()) {
/* 184 */           importService.addIgnoreTable(recordSet.getString("eid"));
/*     */         }
/* 186 */         this.log.info("是否导入集成数据：否");
/*     */       } else {
/* 188 */         this.isImportIntegration = true;
/* 189 */         this.log.info("是否导入集成数据：是");
/*     */       } 
/*     */ 
/*     */       
/* 193 */       int n = Util.getIntValue(Util.null2String(jSONObject1.getString("createForm")), 1);
/* 194 */       if (n != 1) {
/*     */         
/* 196 */         importService.tableExistCondition.put("workflow_bill".toLowerCase(), "uuid");
/*     */         
/* 198 */         importService.tableExistCondition.put("workflow_billfield".toLowerCase(), "uuid");
/*     */         
/* 200 */         importService.tableExistCondition.put("workflow_billdetailtable".toLowerCase(), "uuid");
/*     */         
/* 202 */         importService.tableExistCondition.put("workflow_deptAbbr".toLowerCase(), "workflowid==-1&&isbill==1?uuid");
/*     */         
/* 204 */         importService.tableExistCondition.put("workflow_subComAbbr".toLowerCase(), "workflowid==-1&&isbill==1?uuid");
/*     */         
/* 206 */         importService.tableExistCondition.put("workflow_shortNameSetting".toLowerCase(), "workflowid==-1&&isbill==1?uuid");
/*     */         
/* 208 */         importService.tableExistCondition.put("workflow_formfield_group".toLowerCase(), "formid<0?uuid");
/*     */         
/* 210 */         importService.tableExistCondition.put("workflow_systemfield_group".toLowerCase(), "formid<0?uuid;isbill==1?uuid");
/*     */         
/* 212 */         importService.tableExistCondition.put("workflow_formdetailinfo".toLowerCase(), "formid<0?uuid");
/*     */         
/* 214 */         importService.tableExistCondition.put("workflow_code".toLowerCase(), "flowid==-1&&isbill==1?uuid");
/*     */         
/* 216 */         importService.tableExistCondition.put("workflow_codeseq".toLowerCase(), "workflowid==-1&&isbill==1?uuid");
/*     */         
/* 218 */         importService.tableExistCondition.put("workflow_codeseqreserved".toLowerCase(), "$Parent$workflowid==-1&&$Parent$isbill==1?uuid");
/* 219 */         this.log.info("是否创建新表单：否");
/*     */       } else {
/* 221 */         this.log.info("是否创建新表单：是");
/*     */       } 
/*     */       
/* 224 */       importService.setClientIP(Util.null2String(this.params.get("param_ip")));
/* 225 */       this.log.info("客户端IP：" + Util.null2String(this.params.get("param_ip")));
/* 226 */       importService.setSystemImport(true);
/*     */       
/* 228 */       importService.importByStream(inputStream);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 238 */       hashSet.addAll(importService.getRemoveCacheInfos());
/*     */       
/* 240 */       ArrayList<String> arrayList2 = importService.getNewValue("workflow_base", "id");
/* 241 */       ArrayList<String> arrayList3 = importService.getNewValue("workflow_base", "formid");
/* 242 */       ArrayList<String> arrayList4 = importService.getNewValue("workflow_base", "isbill");
/* 243 */       str3 = (arrayList2.size() > 0) ? arrayList2.get(0) : "";
/* 244 */       str4 = (arrayList3.size() > 0) ? arrayList3.get(0) : "";
/* 245 */       str5 = (arrayList4.size() > 0) ? arrayList4.get(0) : "";
/* 246 */       map3 = importService.getMsgMap();
/*     */ 
/*     */       
/* 249 */       map1 = importService.getFieldIds();
/*     */       
/* 251 */       map2 = importService.getFieldIds("workflow_nodebase", "id");
/*     */       
/* 253 */       hashMap.put("isImportBase", importService.isHaveBase() ? "1" : "0");
/* 254 */       GetWorkflowImportListCmd getWorkflowImportListCmd = new GetWorkflowImportListCmd(map3, this.user);
/* 255 */       map = getWorkflowImportListCmd.getImportListInfosNew(str7);
/*     */ 
/*     */       
/* 258 */       jSONObject4 = jSONObject1;
/* 259 */       ExceptionBean exceptionBean = importService.getExceptionBean();
/* 260 */       if (exceptionBean != null && exceptionBean.getDetail() != null) {
/* 261 */         jSONObject4.put("importResult", Integer.valueOf(0));
/* 262 */         TableBean tableBean = exceptionBean.getTable();
/* 263 */         if (tableBean != null) {
/* 264 */           HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 265 */           ArrayList<HashMap<Object, Object>> arrayList5 = new ArrayList();
/* 266 */           hashMap2.put("key", Integer.valueOf(0));
/* 267 */           hashMap2.put("id", tableBean.getKey());
/* 268 */           hashMap2.put("tablename", tableBean.getTableName());
/* 269 */           hashMap2.put("msgname", str10);
/* 270 */           hashMap2.put("desc", tableBean.getDesc());
/* 271 */           hashMap2.put("status", exceptionBean.getDetail());
/* 272 */           hashMap2.put("exceptionMsg", exceptionBean.getTitle());
/* 273 */           hashMap2.put("exceptionDetail", exceptionBean.getDetail());
/* 274 */           arrayList5.add(hashMap2);
/* 275 */           map.put("dataSource", arrayList5);
/*     */         } 
/* 277 */         map.put("name", str7);
/*     */       } else {
/* 279 */         jSONObject4.put("importResult", Integer.valueOf(1));
/*     */         
/* 281 */         saveImportLog(importService, str8, jSONObject1);
/*     */       } 
/* 283 */       jSONObject2.put(i + "", map);
/* 284 */       jSONArray2.add(jSONObject4);
/*     */     } 
/*     */     
/* 287 */     JSONObject jSONObject5 = new JSONObject();
/* 288 */     jSONObject5.put("title", (new ModuleSettingManager()).getModuleNameWithParentById(str6, this.user.getLanguage()));
/* 289 */     jSONObject5.put("id", str2);
/* 290 */     jSONObject5.put("columns", (new SelectFormUtil(this.user)).getImportResultCols(str6, (new ImportTypeService()).getSystemOptions(arrayList1, str6)));
/* 291 */     jSONObject5.put("values", jSONArray2);
/* 292 */     jSONObject5.put("result", jSONObject2);
/* 293 */     getProgressCom().addResultInfo(jSONObject5);
/*     */     
/* 295 */     removeCache(hashSet);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void saveImportLog(ImportService paramImportService, String paramString, JSONObject paramJSONObject) {
/* 305 */     ArrayList<String> arrayList = paramImportService.getNewValue("workflow_base", "id");
/* 306 */     String str1 = (arrayList.size() > 0) ? arrayList.get(0) : "";
/*     */     
/* 308 */     String str2 = Util.null2String(this.params.get("param_ip"));
/*     */     
/* 310 */     int i = this.user.getUID();
/*     */     
/* 312 */     String str3 = paramImportService.getImportDateTime();
/*     */     
/* 314 */     String str4 = Util.null2String(this.params.get("type"));
/* 315 */     if ("0".equals(str4)) {
/* 316 */       str4 = "y";
/*     */     } else {
/* 318 */       str4 = "n";
/*     */     } 
/* 320 */     String str5 = Util.null2String(paramJSONObject.getString("type"));
/*     */     
/* 322 */     String str6 = Util.null2String(paramJSONObject.getString("importtype"));
/* 323 */     if (str6.equals("0")) {
/* 324 */       str6 = "update";
/*     */     } else {
/* 326 */       str6 = "add";
/*     */     } 
/*     */     
/* 329 */     int j = Util.getIntValue(Util.null2String(paramJSONObject.getString("importBase")), 0);
/* 330 */     String str7 = "y";
/* 331 */     if (j != 1 || !str4.equals("y")) {
/* 332 */       str7 = "n";
/*     */     }
/*     */     
/* 335 */     int k = Util.getIntValue(Util.null2String(paramJSONObject.getString("createForm")), 1);
/* 336 */     String str8 = "y";
/* 337 */     if (k != 1) {
/* 338 */       str8 = "n";
/*     */     }
/* 340 */     String str9 = "insert into workflow_importlog(workflowid, clientip, operator, operatetime, issamesystem, importtype, isimportbase, iscreateform, importfilename, type) values(?,?,?,?,?,?,?,?,?,?)";
/* 341 */     RecordSet recordSet = new RecordSet();
/* 342 */     recordSet.executeUpdate(str9, new Object[] { str1, str2, Integer.valueOf(i), str3, str4, str6, str7, str8, paramString, str5 });
/*     */     
/* 344 */     str9 = "select max(id) from workflow_importlog";
/* 345 */     recordSet.executeQuery(str9, new Object[0]);
/* 346 */     String str10 = "";
/* 347 */     if (recordSet.next()) {
/* 348 */       str10 = recordSet.getString(1);
/*     */     }
/*     */     
/* 351 */     String str11 = "";
/* 352 */     List<BizLogContext> list = paramImportService.getImportLog().getLogs();
/* 353 */     if (list != null && list.size() > 0)
/*     */     {
/* 355 */       saveImportLogDetail(list, str10);
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void saveImportLogDetail(List<BizLogContext> paramList, String paramString) {
/* 366 */     RecordSet recordSet = new RecordSet();
/* 367 */     String str = "insert into workflow_importlog_detail(mainid, operatetime, logtype, logTypeLabel, logsmalltype, logSmallTypeLabel, belongtype, belongTypeLabel) values(?,?,?,?,?,?,?,?)";
/*     */     
/* 369 */     SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
/* 370 */     for (byte b = 0; paramList != null && b < paramList.size(); b++) {
/* 371 */       BizLogContext bizLogContext = paramList.get(b);
/* 372 */       String str1 = simpleDateFormat.format(bizLogContext.getDateObject());
/* 373 */       int i = 0;
/* 374 */       int j = 0;
/* 375 */       if (bizLogContext.getLogType() != null) {
/* 376 */         i = bizLogContext.getLogType().getCode();
/* 377 */         j = bizLogContext.getLogType().getLableId();
/*     */       } 
/* 379 */       int k = 0;
/* 380 */       int m = 0;
/* 381 */       if (bizLogContext.getLogSmallType() != null) {
/* 382 */         k = bizLogContext.getLogSmallType().getCode();
/* 383 */         m = bizLogContext.getLogSmallType().getLableId();
/*     */       } 
/* 385 */       int n = 0;
/* 386 */       int i1 = 0;
/* 387 */       if (bizLogContext.getBelongType() != null) {
/* 388 */         n = bizLogContext.getBelongType().getCode();
/* 389 */         i1 = bizLogContext.getBelongType().getLableId();
/*     */       } 
/* 391 */       recordSet.executeUpdate(str, new Object[] { paramString, str1, Integer.valueOf(i), Integer.valueOf(j), Integer.valueOf(k), Integer.valueOf(m), Integer.valueOf(n), Integer.valueOf(i1) });
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getFielName(int paramInt) {
/* 403 */     String str1 = "";
/* 404 */     RecordSet recordSet = new RecordSet();
/* 405 */     String str2 = "select t1.imageFileId,t1.imageFileName,t1.imageFileType,t1.imageFileUsed,t1.fileRealPath,t1.isZip,t1.isencrypt,t1.filesize,t1.downloads,t1.miniimgpath,t1.imgsize,t1.isFTP,t1.FTPConfigId,t1.isaesencrypt,t1.aescode,t1.tokenKey,t1.storageStatus,t1.comefrom,t2.imagefilename as realname  from ImageFile t1 left join DocImageFile t2 on t1.imagefileid = t2.imagefileid where t1.imagefileid =  " + paramInt;
/* 406 */     recordSet.executeSql(str2);
/* 407 */     if (recordSet.next()) {
/* 408 */       str1 = Util.null2String(recordSet.getString("realname"));
/* 409 */       if (str1.equals("")) {
/* 410 */         str1 = Util.null2String(recordSet.getString("imageFileName"));
/*     */       }
/*     */     } 
/* 413 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private boolean isSystemFileType(String paramString) {
/* 423 */     if (paramString == null || paramString.length() == 0) {
/* 424 */       return false;
/*     */     }
/* 426 */     if (paramString.toLowerCase().endsWith(".esf")) {
/* 427 */       return true;
/*     */     }
/* 429 */     return false;
/*     */   }
/*     */   
/*     */   public ProgressCom getProgressCom() {
/* 433 */     if (this.progressCom == null) {
/* 434 */       this.progressCom = new ProgressCom(this.user, "SYSTEM_IMPORT_PROGRESS_FLAG");
/*     */     }
/* 436 */     return this.progressCom;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void removeCache(final HashSet<String> removeCacheInfos) {
/* 443 */     this.log.info("开始清除缓存：系统导入");
/* 444 */     Thread thread = new Thread()
/*     */       {
/*     */         public void run() {
/* 447 */           FutureService futureService = new FutureService(WorkflowSystemImportImpl.this.log, 5L);
/* 448 */           futureService.doFuture("weaver.workflow.workflow.WorkflowAllComInfo", "removeWorkflowCache");
/* 449 */           futureService.doFuture("weaver.workflow.workflow.WorkflowComInfo", "removeWorkflowCache");
/* 450 */           futureService.doFuture("weaver.workflow.workflow.TestWorkflowComInfo", "removeWorkflowCache");
/* 451 */           futureService.doFuture("weaver.workflow.field.FieldComInfo", "removeFieldCache");
/* 452 */           futureService.doFuture("weaver.hrm.cachecenter.bean.WorkflowRecSimpleComInfo", "removeCache");
/* 453 */           futureService.doFuture("weaver.hrm.cachecenter.bean.WorkflowAdvancedComInfo", "removeCache");
/* 454 */           futureService.doFuture("weaver.workflow.workflow.WorkflowBillComInfo", "removeWorkflowBillCache");
/* 455 */           futureService.doFuture("weaver.workflow.workflow.WorkflowNodeComInfo", "removeNodeCache");
/*     */           
/* 457 */           if (removeCacheInfos.contains("Cache_Hrm_CompanyVirtual")) {
/* 458 */             futureService.doFuture(CompanyVirtualComInfo.class.getName(), "removeCompanyCache");
/*     */           }
/* 460 */           if (removeCacheInfos.contains("Cache_Hrm_Subcompany")) {
/* 461 */             futureService.doFuture(SubCompanyComInfo.class.getName(), "removeCompanyCache");
/*     */           }
/* 463 */           if (removeCacheInfos.contains("Cache_Hrm_SubcompanyVirtual")) {
/* 464 */             futureService.doFuture(SubCompanyVirtualComInfo.class.getName(), "removeSubCompanyCache");
/*     */           }
/* 466 */           if (removeCacheInfos.contains("Cache_Hrm_Department")) {
/* 467 */             futureService.doFuture(DepartmentComInfo.class.getName(), "removeCompanyCache");
/*     */           }
/* 469 */           if (removeCacheInfos.contains("Cache_Hrm_DepartmentVirtual")) {
/* 470 */             futureService.doFuture(DepartmentVirtualComInfo.class.getName(), "removeDepartmentCache");
/*     */           }
/* 472 */           if (removeCacheInfos.contains("Cache_Hrm_Resource")) {
/* 473 */             futureService.doFuture(ResourceComInfo.class.getName(), "removeResourceCache");
/*     */           }
/* 475 */           if (removeCacheInfos.contains("Cache_Hrm_Jobtitles")) {
/* 476 */             futureService.doFuture(JobTitlesComInfo.class.getName(), "removeJobTitlesCache");
/*     */           }
/* 478 */           if (removeCacheInfos.contains("Cache_Hrm_Roles")) {
/* 479 */             futureService.doFuture(RolesComInfo.class.getName(), "removeRolesCache");
/*     */           }
/* 481 */           if (removeCacheInfos.contains("Cache_Hrm_Jobactivities")) {
/* 482 */             futureService.doFuture(JobActivitiesComInfo.class.getName(), "removeJobActivitiesCache");
/*     */           }
/* 484 */           if (removeCacheInfos.contains("Cache_Hrm_Location")) {
/* 485 */             futureService.doFuture(LocationComInfo.class.getName(), "removeLocationCache");
/*     */           }
/* 487 */           if (removeCacheInfos.contains("Cache_Hrm_City")) {
/* 488 */             futureService.doFuture(CityComInfo.class.getName(), "removeCityCache");
/*     */           }
/* 490 */           if (removeCacheInfos.contains("Cache_Hrm_Province")) {
/* 491 */             futureService.doFuture(ProvinceComInfo.class.getName(), "removeProvinceCache");
/*     */           }
/* 493 */           if (removeCacheInfos.contains("Cache_Hrm_Country")) {
/* 494 */             futureService.doFuture(CountryComInfo.class.getName(), "removeCountryCache");
/*     */           }
/* 496 */           if (removeCacheInfos.contains("Cache_Hrm_Jobgroups")) {
/* 497 */             futureService.doFuture(JobGroupsComInfo.class.getName(), "removeCache");
/*     */           }
/* 499 */           if (removeCacheInfos.contains("Cache_Hrm_Jobcall")) {
/* 500 */             futureService.doFuture(JobCallComInfo.class.getName(), "removeJobCallCache");
/*     */           }
/* 502 */           if (removeCacheInfos.contains("Cache_Hrm_Bank")) {
/* 503 */             futureService.doFuture(BankComInfo.class.getName(), "removeBankCache");
/*     */           }
/* 505 */           if (removeCacheInfos.contains("Cache_Hrm_Matrix")) {
/* 506 */             futureService.doFuture(MatrixMaintComInfo.class.getName(), "removeCache");
/* 507 */             futureService.doFuture(MatrixMaintColComInfo.class.getName(), "removeCache");
/* 508 */             futureService.doFuture(MatrixMaintConditionComInfo.class.getName(), "removeCache");
/* 509 */             futureService.doFuture(MatrixMaintMemberComInfo.class.getName(), "removeCache");
/*     */           } 
/* 511 */           if (WorkflowSystemImportImpl.this.isImportIntegration) {
/*     */             
/* 513 */             futureService.doFuture("weaver.interfaces.cache.impl.IntegrationCache4Browser", "loadCache");
/* 514 */             futureService.doFuture("weaver.interfaces.cache.impl.IntegrationCache4DataSource", "loadCache");
/* 515 */             futureService.doFuture("weaver.interfaces.cache.impl.IntegrationCache4Action", "loadCache");
/*     */           } 
/*     */         }
/*     */       };
/* 519 */     thread.start();
/*     */   }
/*     */   
/*     */   public void setCount(int paramInt) {
/* 523 */     this.count = paramInt;
/*     */   }
/*     */   public void setImportIndex(int paramInt) {
/* 526 */     this.importIndex = paramInt;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/systemExpAndImp/impl/WorkflowSystemImportImpl.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */