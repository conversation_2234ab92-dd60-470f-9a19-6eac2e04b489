/*     */ package weaver.systemExpAndImp.impl;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONArray;
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.engine.integration.util.WeaTableEditUtil;
/*     */ import com.engine.workflow.entity.WeaTableEditComEntity;
/*     */ import com.engine.workflow.entity.WeaTableEditEntity;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import weaver.backup.logging.Logger;
/*     */ import weaver.backup.services.ImportTypeService;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systemExpAndImp.itf.ISelectForm;
/*     */ import weaver.systemExpAndImp.logging.LoggerFactory;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ApplicationSelectForm
/*     */   implements ISelectForm
/*     */ {
/*     */   private String type;
/*  32 */   private Logger log = LoggerFactory.getLogger(getClass());
/*     */   
/*     */   public ApplicationSelectForm(String paramString) {
/*  35 */     this.type = paramString;
/*     */   }
/*     */ 
/*     */   
/*     */   public List<Object> getSelectFormColumns(User paramUser, List<SearchConditionOption> paramList) {
/*  40 */     return getSelectFormColumns(paramUser, paramList, null);
/*     */   }
/*     */ 
/*     */   
/*     */   public List<Object> getSelectFormColumns(User paramUser, List<SearchConditionOption> paramList, JSONArray paramJSONArray) {
/*  45 */     ArrayList<JSONObject> arrayList = new ArrayList();
/*     */     
/*  47 */     ArrayList<WeaTableEditComEntity> arrayList1 = new ArrayList();
/*     */ 
/*     */     
/*  50 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  51 */     WeaTableEditEntity weaTableEditEntity1 = WeaTableEditUtil.getColumn(ConditionType.CHECKBOX, SystemEnv.getHtmlLabelName(129432, Util.getIntValue(paramUser.getLanguage() + "")), "isImport", "15%", 2, hashMap1);
/*  52 */     JSONObject jSONObject = new JSONObject();
/*  53 */     jSONObject = JSONObject.parseObject(JSONObject.toJSONString(weaTableEditEntity1));
/*  54 */     jSONObject.getJSONArray("com").getJSONObject(0).put("display", "switch");
/*  55 */     arrayList.add(jSONObject);
/*     */ 
/*     */     
/*  58 */     WeaTableEditComEntity weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.INPUT, "1", "name", 120);
/*  59 */     weaTableEditComEntity.setViewAttr(1, true);
/*  60 */     hashMap1 = new HashMap<>();
/*  61 */     hashMap1.put("hasBorder", Boolean.valueOf(false));
/*  62 */     weaTableEditComEntity.setOtherParams(hashMap1);
/*  63 */     arrayList1.add(weaTableEditComEntity);
/*  64 */     WeaTableEditEntity weaTableEditEntity2 = WeaTableEditUtil.getColumn(ConditionType.INPUT, SystemEnv.getHtmlLabelName(195, Util.getIntValue(paramUser.getLanguage() + "")), "name", "15%", 1);
/*  65 */     weaTableEditEntity2.setCom(arrayList1);
/*  66 */     arrayList.add(weaTableEditEntity2);
/*     */ 
/*     */     
/*  69 */     ArrayList<SearchConditionOption> arrayList2 = new ArrayList();
/*     */     
/*  71 */     SearchConditionOption searchConditionOption1 = new SearchConditionOption();
/*  72 */     searchConditionOption1.setKey("2");
/*  73 */     searchConditionOption1.setShowname(SystemEnv.getHtmlLabelName(129294, Util.getIntValue(paramUser.getLanguage() + "")));
/*     */     
/*  75 */     SearchConditionOption searchConditionOption2 = new SearchConditionOption();
/*  76 */     searchConditionOption2.setKey("1");
/*  77 */     searchConditionOption2.setShowname(SystemEnv.getHtmlLabelName(1421, Util.getIntValue(paramUser.getLanguage() + "")));
/*     */     
/*  79 */     SearchConditionOption searchConditionOption3 = new SearchConditionOption();
/*  80 */     searchConditionOption3.setKey("0");
/*  81 */     searchConditionOption3.setShowname(SystemEnv.getHtmlLabelName(17744, Util.getIntValue(paramUser.getLanguage() + "")));
/*  82 */     searchConditionOption3.setSelected(true);
/*     */     
/*  84 */     arrayList2.add(searchConditionOption1);
/*  85 */     arrayList2.add(searchConditionOption2);
/*  86 */     arrayList2.add(searchConditionOption3);
/*  87 */     WeaTableEditEntity weaTableEditEntity3 = WeaTableEditUtil.getColumn(ConditionType.SELECT, SystemEnv.getHtmlLabelName(24863, Util.getIntValue(paramUser.getLanguage() + "")), "importtype", "15%", 2, arrayList2);
/*  88 */     jSONObject = new JSONObject();
/*  89 */     jSONObject = JSONObject.parseObject(JSONObject.toJSONString(weaTableEditEntity3));
/*  90 */     jSONObject.getJSONArray("com").getJSONObject(0).put("viewAttr", Integer.valueOf(1));
/*  91 */     jSONObject.getJSONArray("com").getJSONObject(0).put("hasBorder", Boolean.valueOf(false));
/*  92 */     jSONObject.getJSONArray("com").getJSONObject(0).put("otherParams", "");
/*  93 */     arrayList.add(jSONObject);
/*     */ 
/*     */     
/*  96 */     weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.INPUT, "1", "updateId", 120);
/*  97 */     weaTableEditComEntity.setViewAttr(1);
/*  98 */     arrayList1 = new ArrayList<>();
/*  99 */     arrayList1.add(weaTableEditComEntity);
/* 100 */     WeaTableEditEntity weaTableEditEntity4 = WeaTableEditUtil.getColumn(ConditionType.INPUT, SystemEnv.getHtmlLabelName(523786, Util.getIntValue(paramUser.getLanguage() + "")), "updateId", "25%", 1);
/* 101 */     weaTableEditEntity4.setCom(arrayList1);
/* 102 */     arrayList.add(weaTableEditEntity4);
/*     */ 
/*     */     
/* 105 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*     */     
/* 107 */     hashMap2.put("helpfulTip", SystemEnv.getHtmlLabelName(525839, Util.getIntValue(paramUser.getLanguage() + "")));
/* 108 */     weaTableEditEntity1 = WeaTableEditUtil.getColumn(ConditionType.CHECKBOX, SystemEnv.getHtmlLabelName(507298, Util.getIntValue(paramUser.getLanguage() + "")), "importBase", "15%", 2, hashMap2);
/* 109 */     jSONObject = new JSONObject();
/* 110 */     jSONObject = JSONObject.parseObject(JSONObject.toJSONString(weaTableEditEntity1));
/* 111 */     jSONObject.getJSONArray("com").getJSONObject(0).put("display", "switch");
/* 112 */     arrayList.add(jSONObject);
/*     */     
/* 114 */     return (List)arrayList;
/*     */   }
/*     */ 
/*     */   
/*     */   public List<Object> getImportResultColumns(User paramUser, List<SearchConditionOption> paramList) {
/* 119 */     ArrayList<WeaTableEditEntity> arrayList = new ArrayList();
/*     */     
/* 121 */     ArrayList<WeaTableEditComEntity> arrayList1 = new ArrayList();
/*     */     
/* 123 */     WeaTableEditComEntity weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.INPUT, "1", "name", 120);
/* 124 */     weaTableEditComEntity.setViewAttr(1, true);
/* 125 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 126 */     hashMap1.put("hasBorder", Boolean.valueOf(false));
/* 127 */     weaTableEditComEntity.setOtherParams(hashMap1);
/* 128 */     arrayList1.add(weaTableEditComEntity);
/* 129 */     WeaTableEditEntity weaTableEditEntity1 = WeaTableEditUtil.getColumn(ConditionType.INPUT, SystemEnv.getHtmlLabelName(195, Util.getIntValue(paramUser.getLanguage() + "")), "name", "15%", 1);
/* 130 */     weaTableEditEntity1.setCom(arrayList1);
/* 131 */     arrayList.add(weaTableEditEntity1);
/*     */ 
/*     */     
/* 134 */     ArrayList<SearchConditionOption> arrayList2 = new ArrayList();
/* 135 */     SearchConditionOption searchConditionOption1 = new SearchConditionOption();
/* 136 */     searchConditionOption1.setKey("2");
/* 137 */     searchConditionOption1.setShowname(SystemEnv.getHtmlLabelName(129294, Util.getIntValue(paramUser.getLanguage() + "")));
/* 138 */     SearchConditionOption searchConditionOption2 = new SearchConditionOption();
/* 139 */     searchConditionOption2.setKey("1");
/* 140 */     searchConditionOption2.setShowname(SystemEnv.getHtmlLabelName(1421, Util.getIntValue(paramUser.getLanguage() + "")));
/* 141 */     SearchConditionOption searchConditionOption3 = new SearchConditionOption();
/* 142 */     searchConditionOption3.setKey("0");
/* 143 */     searchConditionOption3.setShowname(SystemEnv.getHtmlLabelName(17744, Util.getIntValue(paramUser.getLanguage() + "")));
/* 144 */     searchConditionOption3.setSelected(true);
/* 145 */     arrayList2.add(searchConditionOption1);
/* 146 */     arrayList2.add(searchConditionOption2);
/* 147 */     arrayList2.add(searchConditionOption3);
/* 148 */     WeaTableEditEntity weaTableEditEntity2 = WeaTableEditUtil.getColumn(ConditionType.SELECT, SystemEnv.getHtmlLabelName(24863, Util.getIntValue(paramUser.getLanguage() + "")), "importtype", "15%", 1, arrayList2);
/* 149 */     JSONObject jSONObject = JSONObject.parseObject(JSONObject.toJSONString(weaTableEditEntity2));
/* 150 */     jSONObject.getJSONArray("com").getJSONObject(0).put("viewAttr", Integer.valueOf(1));
/* 151 */     jSONObject.getJSONArray("com").getJSONObject(0).put("hasBorder", Boolean.valueOf(false));
/* 152 */     jSONObject.getJSONArray("com").getJSONObject(0).put("otherParams", "");
/* 153 */     arrayList.add(jSONObject);
/*     */ 
/*     */     
/* 156 */     weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.INPUT, "1", "updateId", 120);
/* 157 */     weaTableEditComEntity.setViewAttr(1);
/* 158 */     arrayList1 = new ArrayList<>();
/* 159 */     arrayList1.add(weaTableEditComEntity);
/* 160 */     WeaTableEditEntity weaTableEditEntity3 = WeaTableEditUtil.getColumn(ConditionType.INPUT, SystemEnv.getHtmlLabelName(523786, Util.getIntValue(paramUser.getLanguage() + "")), "updateId", "25%", 1);
/* 161 */     weaTableEditEntity3.setCom(arrayList1);
/* 162 */     arrayList.add(weaTableEditEntity3);
/*     */ 
/*     */     
/* 165 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*     */     
/* 167 */     hashMap2.put("helpfulTip", SystemEnv.getHtmlLabelName(525839, Util.getIntValue(paramUser.getLanguage() + "")));
/* 168 */     WeaTableEditEntity weaTableEditEntity4 = WeaTableEditUtil.getColumn(ConditionType.CHECKBOX, SystemEnv.getHtmlLabelName(507298, Util.getIntValue(paramUser.getLanguage() + "")), "importBase", "15%", 1, hashMap2);
/* 169 */     jSONObject = new JSONObject();
/* 170 */     jSONObject = JSONObject.parseObject(JSONObject.toJSONString(weaTableEditEntity4));
/* 171 */     jSONObject.getJSONArray("com").getJSONObject(0).put("display", "switch");
/* 172 */     arrayList.add(jSONObject);
/*     */     
/* 174 */     return (List)arrayList;
/*     */   }
/*     */ 
/*     */   
/*     */   public JSONObject getSelectFormOnlyReadColumns(User paramUser, List<SearchConditionOption> paramList) {
/* 179 */     ImportTypeService importTypeService = new ImportTypeService();
/* 180 */     JSONObject jSONObject1 = new JSONObject();
/*     */     
/* 182 */     JSONObject jSONObject3 = new JSONObject();
/* 183 */     JSONArray jSONArray = new JSONArray();
/*     */ 
/*     */ 
/*     */     
/* 187 */     jSONObject3 = new JSONObject();
/* 188 */     jSONArray = new JSONArray();
/* 189 */     WeaTableEditComEntity weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.CHECKBOX, "1", "isImport", 120);
/* 190 */     JSONObject jSONObject2 = JSONObject.parseObject(JSONObject.toJSONString(weaTableEditComEntity));
/* 191 */     jSONObject2.put("display", "switch");
/* 192 */     jSONObject2.put("viewAttr", Integer.valueOf(1));
/* 193 */     jSONArray.add(jSONObject2);
/* 194 */     jSONObject3.put("com", jSONArray);
/* 195 */     jSONObject1.put("isImport", jSONObject3);
/*     */     
/* 197 */     jSONObject3 = new JSONObject();
/* 198 */     jSONArray = new JSONArray();
/* 199 */     weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.SELECT, "1", "importtype", 120, importTypeService.getImportType("0", paramUser, true));
/* 200 */     weaTableEditComEntity.setViewAttr(1);
/* 201 */     jSONArray.add(weaTableEditComEntity);
/* 202 */     jSONObject3.put("com", jSONArray);
/* 203 */     jSONObject1.put("importtype", jSONObject3);
/*     */ 
/*     */     
/* 206 */     jSONObject3 = new JSONObject();
/* 207 */     jSONArray = new JSONArray();
/* 208 */     weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.CHECKBOX, "1", "importBase", 120);
/* 209 */     jSONObject2 = JSONObject.parseObject(JSONObject.toJSONString(weaTableEditComEntity));
/* 210 */     jSONObject2.put("display", "switch");
/* 211 */     jSONObject2.put("viewAttr", Integer.valueOf(1));
/* 212 */     jSONArray.add(jSONObject2);
/* 213 */     jSONObject3.put("com", jSONArray);
/* 214 */     jSONObject1.put("importBase", jSONObject3);
/* 215 */     return jSONObject1;
/*     */   }
/*     */ 
/*     */   
/*     */   public JSONObject getSelectFormImportTypeCells0(User paramUser, List<SearchConditionOption> paramList) {
/* 220 */     JSONObject jSONObject1 = new JSONObject();
/* 221 */     JSONObject jSONObject2 = new JSONObject();
/* 222 */     JSONArray jSONArray = new JSONArray();
/* 223 */     ArrayList<WeaTableEditComEntity> arrayList = new ArrayList();
/*     */     
/* 225 */     WeaTableEditComEntity weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.INPUT, "1", "importtype", 120);
/* 226 */     weaTableEditComEntity.setViewAttr(1, false);
/* 227 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 228 */     hashMap.put("hasBorder", Boolean.valueOf(false));
/* 229 */     weaTableEditComEntity.setOtherParams(hashMap);
/* 230 */     arrayList.add(weaTableEditComEntity);
/* 231 */     WeaTableEditEntity weaTableEditEntity1 = WeaTableEditUtil.getColumn(ConditionType.INPUT, SystemEnv.getHtmlLabelName(195, Util.getIntValue(paramUser.getLanguage() + "")), "importtype", "15%", 1);
/* 232 */     weaTableEditEntity1.setCom(arrayList);
/* 233 */     jSONObject1.put("importtype", weaTableEditEntity1);
/*     */     
/* 235 */     arrayList = new ArrayList<>();
/* 236 */     weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.INPUT, "1", "updateId", 120);
/* 237 */     weaTableEditComEntity.setViewAttr(1, false);
/* 238 */     hashMap = new HashMap<>();
/* 239 */     hashMap.put("hasBorder", Boolean.valueOf(false));
/* 240 */     weaTableEditComEntity.setOtherParams(hashMap);
/* 241 */     arrayList.add(weaTableEditComEntity);
/* 242 */     WeaTableEditEntity weaTableEditEntity2 = WeaTableEditUtil.getColumn(ConditionType.INPUT, SystemEnv.getHtmlLabelName(195, Util.getIntValue(paramUser.getLanguage() + "")), "updateId", "15%", 1);
/* 243 */     weaTableEditEntity2.setCom(arrayList);
/* 244 */     jSONObject1.put("updateId", weaTableEditEntity2);
/*     */     
/* 246 */     arrayList = new ArrayList<>();
/* 247 */     weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.INPUT, "1", "importBase", 120);
/* 248 */     weaTableEditComEntity.setViewAttr(1, false);
/* 249 */     hashMap = new HashMap<>();
/* 250 */     hashMap.put("hasBorder", Boolean.valueOf(false));
/* 251 */     weaTableEditComEntity.setOtherParams(hashMap);
/* 252 */     arrayList.add(weaTableEditComEntity);
/* 253 */     WeaTableEditEntity weaTableEditEntity3 = WeaTableEditUtil.getColumn(ConditionType.INPUT, SystemEnv.getHtmlLabelName(195, Util.getIntValue(paramUser.getLanguage() + "")), "importBase", "15%", 1);
/* 254 */     weaTableEditEntity3.setCom(arrayList);
/* 255 */     jSONObject1.put("importBase", weaTableEditEntity3);
/* 256 */     return jSONObject1;
/*     */   }
/*     */   
/*     */   public JSONObject getSelectFormImportTypeCells1(User paramUser, List<SearchConditionOption> paramList) {
/*     */     List list;
/* 261 */     JSONObject jSONObject1 = new JSONObject();
/* 262 */     JSONObject jSONObject2 = new JSONObject();
/* 263 */     JSONArray jSONArray = new JSONArray();
/*     */ 
/*     */     
/* 266 */     if (paramList != null) {
/* 267 */       list = (new ImportTypeService()).getImportType("0", paramUser, true);
/*     */     } else {
/* 269 */       list = (new ImportTypeService()).getImportType("1", paramUser, true);
/*     */     } 
/* 271 */     WeaTableEditComEntity weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.SELECT, "1", "importtype", 120, list);
/* 272 */     weaTableEditComEntity.setViewAttr(1);
/* 273 */     jSONArray.add(weaTableEditComEntity);
/* 274 */     jSONObject2.put("com", jSONArray);
/* 275 */     jSONObject1.put("importtype", jSONObject2);
/*     */     
/* 277 */     ArrayList<WeaTableEditComEntity> arrayList = new ArrayList();
/* 278 */     weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.INPUT, "1", "updateId", 120);
/* 279 */     weaTableEditComEntity.setViewAttr(1, false);
/* 280 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 281 */     hashMap.put("hasBorder", Boolean.valueOf(false));
/* 282 */     weaTableEditComEntity.setOtherParams(hashMap);
/* 283 */     arrayList.add(weaTableEditComEntity);
/* 284 */     WeaTableEditEntity weaTableEditEntity = WeaTableEditUtil.getColumn(ConditionType.INPUT, SystemEnv.getHtmlLabelName(195, Util.getIntValue(paramUser.getLanguage() + "")), "updateId", "15%", 1);
/* 285 */     weaTableEditEntity.setCom(arrayList);
/* 286 */     jSONObject1.put("updateId", weaTableEditEntity);
/*     */     
/* 288 */     jSONObject2 = new JSONObject();
/* 289 */     jSONArray = new JSONArray();
/* 290 */     weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.CHECKBOX, "1", "importBase", 120);
/* 291 */     JSONObject jSONObject3 = JSONObject.parseObject(JSONObject.toJSONString(weaTableEditComEntity));
/* 292 */     jSONObject3.put("display", "switch");
/* 293 */     jSONArray.add(jSONObject3);
/* 294 */     jSONObject2.put("com", jSONArray);
/* 295 */     jSONObject1.put("importBase", jSONObject2);
/*     */     
/* 297 */     return jSONObject1;
/*     */   }
/*     */   
/*     */   public JSONObject getSelectFormImportTypeCells2(User paramUser, List<SearchConditionOption> paramList) {
/*     */     List list;
/* 302 */     JSONObject jSONObject1 = new JSONObject();
/* 303 */     JSONObject jSONObject2 = new JSONObject();
/* 304 */     JSONArray jSONArray = new JSONArray();
/*     */ 
/*     */     
/* 307 */     if (paramList != null) {
/* 308 */       list = (new ImportTypeService()).getImportType("0", paramUser, true);
/*     */     } else {
/* 310 */       list = (new ImportTypeService()).getImportType("1", paramUser, true);
/*     */     } 
/* 312 */     WeaTableEditComEntity weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.SELECT, "1", "importtype", 120, list);
/* 313 */     weaTableEditComEntity.setViewAttr(1);
/* 314 */     jSONArray.add(weaTableEditComEntity);
/* 315 */     jSONObject2.put("com", jSONArray);
/* 316 */     jSONObject1.put("importtype", jSONObject2);
/*     */     
/* 318 */     jSONObject2 = new JSONObject();
/* 319 */     jSONArray = new JSONArray();
/* 320 */     weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.INPUT, "1", "updateId", 120);
/* 321 */     weaTableEditComEntity.setViewAttr(1);
/* 322 */     JSONObject jSONObject3 = new JSONObject();
/* 323 */     jSONObject3 = JSONObject.parseObject(JSONObject.toJSONString(weaTableEditComEntity));
/* 324 */     jSONArray.add(jSONObject3);
/* 325 */     jSONObject2.put("com", jSONArray);
/* 326 */     jSONObject1.put("updateId", jSONObject2);
/*     */     
/* 328 */     jSONObject2 = new JSONObject();
/* 329 */     jSONArray = new JSONArray();
/* 330 */     weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.CHECKBOX, "1", "importBase", 120);
/* 331 */     jSONObject3 = JSONObject.parseObject(JSONObject.toJSONString(weaTableEditComEntity));
/* 332 */     jSONObject3.put("display", "switch");
/* 333 */     jSONArray.add(jSONObject3);
/* 334 */     jSONObject2.put("com", jSONArray);
/* 335 */     jSONObject1.put("importBase", jSONObject2);
/*     */     
/* 337 */     return jSONObject1;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/systemExpAndImp/impl/ApplicationSelectForm.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */