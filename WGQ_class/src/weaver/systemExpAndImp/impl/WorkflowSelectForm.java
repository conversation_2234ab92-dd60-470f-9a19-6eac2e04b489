/*     */ package weaver.systemExpAndImp.impl;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONArray;
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.engine.integration.util.WeaTableEditUtil;
/*     */ import com.engine.workflow.entity.WeaTableEditComEntity;
/*     */ import com.engine.workflow.entity.WeaTableEditEntity;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import weaver.backup.logging.Logger;
/*     */ import weaver.backup.services.ImportTypeService;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systemExpAndImp.itf.ISelectForm;
/*     */ import weaver.systemExpAndImp.logging.LoggerFactory;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class WorkflowSelectForm
/*     */   implements ISelectForm
/*     */ {
/*  29 */   private Logger log = LoggerFactory.getLogger(getClass());
/*     */ 
/*     */   
/*     */   public List<Object> getSelectFormColumns(User paramUser, List<SearchConditionOption> paramList) {
/*  33 */     return getSelectFormColumns(paramUser, paramList, null);
/*     */   }
/*     */ 
/*     */   
/*     */   public List<Object> getSelectFormColumns(User paramUser, List<SearchConditionOption> paramList, JSONArray paramJSONArray) {
/*  38 */     ArrayList<JSONObject> arrayList = new ArrayList();
/*     */ 
/*     */     
/*  41 */     ArrayList<WeaTableEditComEntity> arrayList1 = new ArrayList();
/*     */     
/*  43 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  44 */     WeaTableEditEntity weaTableEditEntity1 = WeaTableEditUtil.getColumn(ConditionType.CHECKBOX, SystemEnv.getHtmlLabelName(129432, Util.getIntValue(paramUser.getLanguage() + "")), "isImport", "15%", 2, hashMap1);
/*  45 */     JSONObject jSONObject = new JSONObject();
/*  46 */     jSONObject = JSONObject.parseObject(JSONObject.toJSONString(weaTableEditEntity1));
/*  47 */     jSONObject.getJSONArray("com").getJSONObject(0).put("display", "switch");
/*  48 */     arrayList.add(jSONObject);
/*     */     
/*  50 */     WeaTableEditComEntity weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.INPUT, "1", "name", 120);
/*  51 */     weaTableEditComEntity.setViewAttr(1, true);
/*  52 */     hashMap1 = new HashMap<>();
/*  53 */     hashMap1.put("hasBorder", Boolean.valueOf(false));
/*  54 */     weaTableEditComEntity.setOtherParams(hashMap1);
/*  55 */     arrayList1.add(weaTableEditComEntity);
/*     */ 
/*     */     
/*  58 */     WeaTableEditEntity weaTableEditEntity2 = WeaTableEditUtil.getColumn(ConditionType.INPUT, SystemEnv.getHtmlLabelName(195, Util.getIntValue(paramUser.getLanguage() + "")), "name", "15%", 1);
/*  59 */     weaTableEditEntity2.setCom(arrayList1);
/*  60 */     arrayList.add(weaTableEditEntity2);
/*  61 */     jSONObject = new JSONObject();
/*     */ 
/*     */ 
/*     */     
/*  65 */     ArrayList<SearchConditionOption> arrayList2 = new ArrayList();
/*  66 */     SearchConditionOption searchConditionOption1 = new SearchConditionOption();
/*  67 */     searchConditionOption1.setKey("2");
/*  68 */     searchConditionOption1.setShowname(SystemEnv.getHtmlLabelName(129294, Util.getIntValue(paramUser.getLanguage() + "")));
/*     */     
/*  70 */     SearchConditionOption searchConditionOption2 = new SearchConditionOption();
/*  71 */     searchConditionOption2.setKey("1");
/*  72 */     searchConditionOption2.setShowname(SystemEnv.getHtmlLabelName(1421, Util.getIntValue(paramUser.getLanguage() + "")));
/*     */     
/*  74 */     SearchConditionOption searchConditionOption3 = new SearchConditionOption();
/*  75 */     searchConditionOption3.setKey("0");
/*  76 */     searchConditionOption3.setShowname(SystemEnv.getHtmlLabelName(17744, Util.getIntValue(paramUser.getLanguage() + "")));
/*     */     
/*  78 */     arrayList2.add(searchConditionOption1);
/*  79 */     arrayList2.add(searchConditionOption2);
/*  80 */     arrayList2.add(searchConditionOption3);
/*  81 */     WeaTableEditEntity weaTableEditEntity4 = WeaTableEditUtil.getColumn(ConditionType.SELECT, SystemEnv.getHtmlLabelName(24863, Util.getIntValue(paramUser.getLanguage() + "")), "importtype", "15%", 1, arrayList2);
/*  82 */     jSONObject = new JSONObject();
/*  83 */     jSONObject = JSONObject.parseObject(JSONObject.toJSONString(weaTableEditEntity4));
/*  84 */     jSONObject.getJSONArray("com").getJSONObject(0).put("viewAttr", Integer.valueOf(1));
/*  85 */     jSONObject.getJSONArray("com").getJSONObject(0).put("hasBorder", Boolean.valueOf(false));
/*  86 */     jSONObject.getJSONArray("com").getJSONObject(0).put("otherParams", "");
/*  87 */     arrayList.add(jSONObject);
/*     */ 
/*     */     
/*  90 */     if (paramList == null) {
/*  91 */       weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.SELECT, "1", "updateId", 120);
/*     */     } else {
/*  93 */       ArrayList<SearchConditionOption> arrayList3 = new ArrayList();
/*     */       
/*     */       try {
/*  96 */         for (byte b = 0; b < paramList.size(); b++) {
/*  97 */           SearchConditionOption searchConditionOption = ((SearchConditionOption)paramList.get(b)).clone();
/*  98 */           searchConditionOption.setSelected(false);
/*  99 */           arrayList3.add(searchConditionOption);
/*     */         } 
/* 101 */       } catch (CloneNotSupportedException cloneNotSupportedException) {
/* 102 */         this.log.error(cloneNotSupportedException.getMessage(), cloneNotSupportedException);
/* 103 */         arrayList3 = new ArrayList<>();
/*     */       } 
/* 105 */       weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.SELECT, "1", "updateId", 120, arrayList3);
/*     */     } 
/* 107 */     weaTableEditComEntity.setViewAttr(1);
/* 108 */     arrayList1 = new ArrayList<>();
/* 109 */     arrayList1.add(weaTableEditComEntity);
/* 110 */     WeaTableEditEntity weaTableEditEntity5 = WeaTableEditUtil.getColumn(ConditionType.SELECT, SystemEnv.getHtmlLabelName(523786, Util.getIntValue(paramUser.getLanguage() + "")), "updateId", "25%", 1);
/* 111 */     weaTableEditEntity5.setCom(arrayList1);
/* 112 */     arrayList.add(weaTableEditEntity5);
/*     */ 
/*     */     
/* 115 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*     */ 
/*     */     
/* 118 */     hashMap2.put("helpfulTip", SystemEnv.getHtmlLabelName(10000177, Util.getIntValue(paramUser.getLanguage() + "")));
/* 119 */     WeaTableEditEntity weaTableEditEntity3 = WeaTableEditUtil.getColumn(ConditionType.CHECKBOX, SystemEnv.getHtmlLabelName(507297, Util.getIntValue(paramUser.getLanguage() + "")), "createForm", "15%", 2, hashMap2);
/* 120 */     jSONObject = JSONObject.parseObject(JSONObject.toJSONString(weaTableEditEntity3));
/* 121 */     jSONObject.getJSONArray("com").getJSONObject(0).put("display", "switch");
/* 122 */     arrayList.add(jSONObject);
/*     */ 
/*     */     
/* 125 */     HashMap<Object, Object> hashMap3 = new HashMap<>();
/*     */     
/* 127 */     hashMap3.put("helpfulTip", SystemEnv.getHtmlLabelName(509466, Util.getIntValue(paramUser.getLanguage() + "")));
/* 128 */     weaTableEditEntity3 = WeaTableEditUtil.getColumn(ConditionType.CHECKBOX, SystemEnv.getHtmlLabelName(507298, Util.getIntValue(paramUser.getLanguage() + "")), "importBase", "15%", 2, hashMap3);
/* 129 */     jSONObject = JSONObject.parseObject(JSONObject.toJSONString(weaTableEditEntity3));
/* 130 */     jSONObject.getJSONArray("com").getJSONObject(0).put("display", "switch");
/* 131 */     arrayList.add(jSONObject);
/*     */ 
/*     */     
/* 134 */     HashMap<Object, Object> hashMap4 = new HashMap<>();
/*     */     
/* 136 */     hashMap4.put("helpfulTip", SystemEnv.getHtmlLabelName(514433, Util.getIntValue(paramUser.getLanguage() + "")));
/* 137 */     weaTableEditEntity3 = WeaTableEditUtil.getColumn(ConditionType.CHECKBOX, SystemEnv.getHtmlLabelName(514424, Util.getIntValue(paramUser.getLanguage() + "")), "importIntegration", "15%", 2, hashMap4);
/* 138 */     jSONObject = JSONObject.parseObject(JSONObject.toJSONString(weaTableEditEntity3));
/* 139 */     jSONObject.getJSONArray("com").getJSONObject(0).put("display", "switch");
/* 140 */     arrayList.add(jSONObject);
/*     */     
/* 142 */     return (List)arrayList;
/*     */   }
/*     */ 
/*     */   
/*     */   public List<Object> getImportResultColumns(User paramUser, List<SearchConditionOption> paramList) {
/* 147 */     ArrayList<WeaTableEditEntity> arrayList = new ArrayList();
/*     */ 
/*     */ 
/*     */     
/* 151 */     ArrayList<WeaTableEditComEntity> arrayList1 = new ArrayList();
/* 152 */     WeaTableEditComEntity weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.INPUT, "1", "name", 120);
/* 153 */     weaTableEditComEntity.setViewAttr(1, true);
/* 154 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 155 */     hashMap1.put("hasBorder", Boolean.valueOf(false));
/* 156 */     weaTableEditComEntity.setOtherParams(hashMap1);
/* 157 */     arrayList1.add(weaTableEditComEntity);
/* 158 */     WeaTableEditEntity weaTableEditEntity1 = WeaTableEditUtil.getColumn(ConditionType.INPUT, SystemEnv.getHtmlLabelName(195, Util.getIntValue(paramUser.getLanguage() + "")), "name", "15%", 1);
/* 159 */     weaTableEditEntity1.setCom(arrayList1);
/* 160 */     arrayList.add(weaTableEditEntity1);
/*     */ 
/*     */     
/* 163 */     ArrayList<SearchConditionOption> arrayList2 = new ArrayList();
/* 164 */     SearchConditionOption searchConditionOption1 = new SearchConditionOption();
/* 165 */     searchConditionOption1.setKey("2");
/* 166 */     searchConditionOption1.setShowname(SystemEnv.getHtmlLabelName(129294, Util.getIntValue(paramUser.getLanguage() + "")));
/* 167 */     SearchConditionOption searchConditionOption2 = new SearchConditionOption();
/* 168 */     searchConditionOption2.setKey("1");
/* 169 */     searchConditionOption2.setShowname(SystemEnv.getHtmlLabelName(1421, Util.getIntValue(paramUser.getLanguage() + "")));
/* 170 */     SearchConditionOption searchConditionOption3 = new SearchConditionOption();
/* 171 */     searchConditionOption3.setKey("0");
/* 172 */     searchConditionOption3.setShowname(SystemEnv.getHtmlLabelName(17744, Util.getIntValue(paramUser.getLanguage() + "")));
/* 173 */     arrayList2.add(searchConditionOption1);
/* 174 */     arrayList2.add(searchConditionOption2);
/* 175 */     arrayList2.add(searchConditionOption3);
/* 176 */     WeaTableEditEntity weaTableEditEntity2 = WeaTableEditUtil.getColumn(ConditionType.SELECT, SystemEnv.getHtmlLabelName(24863, Util.getIntValue(paramUser.getLanguage() + "")), "importtype", "15%", 1, arrayList2);
/* 177 */     JSONObject jSONObject = new JSONObject();
/* 178 */     jSONObject = JSONObject.parseObject(JSONObject.toJSONString(weaTableEditEntity2));
/* 179 */     jSONObject.getJSONArray("com").getJSONObject(0).put("viewAttr", Integer.valueOf(1));
/* 180 */     jSONObject.getJSONArray("com").getJSONObject(0).put("hasBorder", Boolean.valueOf(false));
/* 181 */     jSONObject.getJSONArray("com").getJSONObject(0).put("otherParams", "");
/* 182 */     arrayList.add(jSONObject);
/*     */ 
/*     */     
/* 185 */     if (paramList == null) {
/* 186 */       weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.SELECT, "1", "updateId", 120);
/*     */     } else {
/* 188 */       ArrayList<SearchConditionOption> arrayList3 = new ArrayList();
/*     */       
/*     */       try {
/* 191 */         for (byte b = 0; b < paramList.size(); b++) {
/* 192 */           SearchConditionOption searchConditionOption = ((SearchConditionOption)paramList.get(b)).clone();
/* 193 */           searchConditionOption.setSelected(false);
/* 194 */           arrayList3.add(searchConditionOption);
/*     */         } 
/* 196 */       } catch (CloneNotSupportedException cloneNotSupportedException) {
/* 197 */         this.log.error(cloneNotSupportedException.getMessage(), cloneNotSupportedException);
/* 198 */         arrayList3 = new ArrayList<>();
/*     */       } 
/* 200 */       weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.SELECT, "1", "updateId", 120, arrayList3);
/*     */     } 
/* 202 */     weaTableEditComEntity.setViewAttr(1);
/* 203 */     arrayList1 = new ArrayList<>();
/* 204 */     arrayList1.add(weaTableEditComEntity);
/* 205 */     WeaTableEditEntity weaTableEditEntity3 = WeaTableEditUtil.getColumn(ConditionType.SELECT, SystemEnv.getHtmlLabelName(523786, Util.getIntValue(paramUser.getLanguage() + "")), "updateId", "25%", 1);
/* 206 */     weaTableEditEntity3.setCom(arrayList1);
/* 207 */     arrayList.add(weaTableEditEntity3);
/*     */ 
/*     */     
/* 210 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*     */ 
/*     */ 
/*     */     
/* 214 */     arrayList.add(WeaTableEditUtil.getColumn(ConditionType.CHECKBOX, SystemEnv.getHtmlLabelName(507297, Util.getIntValue(paramUser.getLanguage() + "")), "createForm", "15%", 1, hashMap2));
/*     */ 
/*     */     
/* 217 */     HashMap<Object, Object> hashMap3 = new HashMap<>();
/*     */     
/* 219 */     hashMap3.put("helpfulTip", SystemEnv.getHtmlLabelName(509466, Util.getIntValue(paramUser.getLanguage() + "")));
/* 220 */     arrayList.add(WeaTableEditUtil.getColumn(ConditionType.CHECKBOX, SystemEnv.getHtmlLabelName(507298, Util.getIntValue(paramUser.getLanguage() + "")), "importBase", "15%", 1, hashMap3));
/*     */ 
/*     */     
/* 223 */     HashMap<Object, Object> hashMap4 = new HashMap<>();
/*     */     
/* 225 */     hashMap4.put("helpfulTip", SystemEnv.getHtmlLabelName(514433, Util.getIntValue(paramUser.getLanguage() + "")));
/* 226 */     arrayList.add(WeaTableEditUtil.getColumn(ConditionType.CHECKBOX, SystemEnv.getHtmlLabelName(514424, Util.getIntValue(paramUser.getLanguage() + "")), "importIntegration", "15%", 1, hashMap4));
/*     */     
/* 228 */     return (List)arrayList;
/*     */   }
/*     */   
/*     */   public JSONObject getSelectFormOnlyReadColumns(User paramUser, List<SearchConditionOption> paramList) {
/*     */     List list;
/* 233 */     ImportTypeService importTypeService = new ImportTypeService();
/* 234 */     JSONObject jSONObject1 = new JSONObject();
/* 235 */     JSONObject jSONObject2 = new JSONObject();
/* 236 */     JSONArray jSONArray = new JSONArray();
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 241 */     jSONObject2 = new JSONObject();
/* 242 */     jSONArray = new JSONArray();
/* 243 */     WeaTableEditComEntity weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.CHECKBOX, "1", "isImport", 120);
/* 244 */     JSONObject jSONObject3 = JSONObject.parseObject(JSONObject.toJSONString(weaTableEditComEntity));
/* 245 */     jSONObject3.put("display", "switch");
/* 246 */     jSONObject3.put("viewAttr", Integer.valueOf(1));
/* 247 */     jSONArray.add(jSONObject3);
/* 248 */     jSONObject2.put("com", jSONArray);
/* 249 */     jSONObject1.put("isImport", jSONObject2);
/*     */     
/* 251 */     jSONObject2 = new JSONObject();
/*     */     
/* 253 */     if (paramList != null && paramList.size() > 0) {
/* 254 */       list = (new ImportTypeService()).getImportType("0", paramUser, true);
/*     */     } else {
/* 256 */       list = (new ImportTypeService()).getImportType("1", paramUser, true);
/*     */     } 
/* 258 */     jSONArray = new JSONArray();
/* 259 */     weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.SELECT, "1", "importtype", 120, list);
/* 260 */     weaTableEditComEntity.setViewAttr(1);
/* 261 */     jSONArray.add(weaTableEditComEntity);
/* 262 */     jSONObject2.put("com", jSONArray);
/* 263 */     jSONObject1.put("importtype", jSONObject2);
/*     */ 
/*     */     
/* 266 */     jSONObject2 = new JSONObject();
/* 267 */     jSONArray = new JSONArray();
/* 268 */     if (paramList == null) {
/* 269 */       weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.SELECT, "1", "updateId", 120);
/*     */     } else {
/* 271 */       ArrayList<SearchConditionOption> arrayList = new ArrayList();
/*     */       
/*     */       try {
/* 274 */         for (byte b = 0; b < paramList.size(); b++) {
/* 275 */           SearchConditionOption searchConditionOption = ((SearchConditionOption)paramList.get(b)).clone();
/* 276 */           searchConditionOption.setSelected(false);
/* 277 */           arrayList.add(searchConditionOption);
/*     */         } 
/* 279 */       } catch (CloneNotSupportedException cloneNotSupportedException) {
/* 280 */         this.log.error(cloneNotSupportedException.getMessage(), cloneNotSupportedException);
/* 281 */         arrayList = new ArrayList<>();
/*     */       } 
/* 283 */       weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.SELECT, "1", "updateId", 120, arrayList);
/*     */     } 
/* 285 */     weaTableEditComEntity.setViewAttr(1);
/* 286 */     jSONArray.add(weaTableEditComEntity);
/* 287 */     jSONObject2.put("com", jSONArray);
/* 288 */     jSONObject1.put("updateId", jSONObject2);
/*     */ 
/*     */     
/* 291 */     jSONObject2 = new JSONObject();
/* 292 */     jSONArray = new JSONArray();
/* 293 */     weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.CHECKBOX, "1", "createForm", 120);
/* 294 */     jSONObject3 = JSONObject.parseObject(JSONObject.toJSONString(weaTableEditComEntity));
/* 295 */     jSONObject3.put("display", "switch");
/* 296 */     jSONObject3.put("viewAttr", Integer.valueOf(1));
/* 297 */     jSONArray.add(jSONObject3);
/* 298 */     jSONObject2.put("com", jSONArray);
/* 299 */     jSONObject1.put("createForm", jSONObject2);
/*     */ 
/*     */     
/* 302 */     jSONObject2 = new JSONObject();
/* 303 */     jSONArray = new JSONArray();
/* 304 */     weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.CHECKBOX, "1", "importBase", 120);
/* 305 */     jSONObject3 = JSONObject.parseObject(JSONObject.toJSONString(weaTableEditComEntity));
/* 306 */     jSONObject3.put("display", "switch");
/* 307 */     jSONObject3.put("viewAttr", Integer.valueOf(1));
/* 308 */     jSONArray.add(jSONObject3);
/* 309 */     jSONObject2.put("com", jSONArray);
/* 310 */     jSONObject1.put("importBase", jSONObject2);
/*     */ 
/*     */     
/* 313 */     jSONObject2 = new JSONObject();
/* 314 */     jSONArray = new JSONArray();
/* 315 */     weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.CHECKBOX, "1", "importIntegration", 120);
/* 316 */     jSONObject3 = JSONObject.parseObject(JSONObject.toJSONString(weaTableEditComEntity));
/* 317 */     jSONObject3.put("display", "switch");
/* 318 */     jSONObject3.put("viewAttr", Integer.valueOf(1));
/* 319 */     jSONArray.add(jSONObject3);
/* 320 */     jSONObject2.put("com", jSONArray);
/* 321 */     jSONObject1.put("importIntegration", jSONObject2);
/* 322 */     return jSONObject1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONObject getSelectFormImportTypeCells0(User paramUser, List<SearchConditionOption> paramList) {
/* 333 */     JSONObject jSONObject1 = new JSONObject();
/* 334 */     JSONObject jSONObject2 = new JSONObject();
/* 335 */     JSONArray jSONArray = new JSONArray();
/* 336 */     ArrayList<WeaTableEditComEntity> arrayList = new ArrayList();
/*     */     
/* 338 */     WeaTableEditComEntity weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.INPUT, "1", "importtype", 120);
/* 339 */     weaTableEditComEntity.setViewAttr(1, false);
/* 340 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 341 */     hashMap.put("hasBorder", Boolean.valueOf(false));
/* 342 */     weaTableEditComEntity.setOtherParams(hashMap);
/* 343 */     arrayList.add(weaTableEditComEntity);
/* 344 */     WeaTableEditEntity weaTableEditEntity1 = WeaTableEditUtil.getColumn(ConditionType.INPUT, SystemEnv.getHtmlLabelName(195, Util.getIntValue(paramUser.getLanguage() + "")), "importtype", "15%", 1);
/* 345 */     weaTableEditEntity1.setCom(arrayList);
/* 346 */     jSONObject1.put("importtype", weaTableEditEntity1);
/*     */     
/* 348 */     arrayList = new ArrayList<>();
/* 349 */     weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.INPUT, "1", "updateId", 120);
/* 350 */     weaTableEditComEntity.setViewAttr(1, false);
/* 351 */     hashMap = new HashMap<>();
/* 352 */     hashMap.put("hasBorder", Boolean.valueOf(false));
/* 353 */     weaTableEditComEntity.setOtherParams(hashMap);
/* 354 */     arrayList.add(weaTableEditComEntity);
/* 355 */     WeaTableEditEntity weaTableEditEntity2 = WeaTableEditUtil.getColumn(ConditionType.INPUT, SystemEnv.getHtmlLabelName(195, Util.getIntValue(paramUser.getLanguage() + "")), "updateId", "15%", 1);
/* 356 */     weaTableEditEntity2.setCom(arrayList);
/* 357 */     jSONObject1.put("updateId", weaTableEditEntity2);
/*     */     
/* 359 */     arrayList = new ArrayList<>();
/* 360 */     weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.INPUT, "1", "createForm", 120);
/* 361 */     weaTableEditComEntity.setViewAttr(1, false);
/* 362 */     hashMap = new HashMap<>();
/* 363 */     hashMap.put("hasBorder", Boolean.valueOf(false));
/* 364 */     weaTableEditComEntity.setOtherParams(hashMap);
/* 365 */     arrayList.add(weaTableEditComEntity);
/* 366 */     WeaTableEditEntity weaTableEditEntity3 = WeaTableEditUtil.getColumn(ConditionType.INPUT, SystemEnv.getHtmlLabelName(195, Util.getIntValue(paramUser.getLanguage() + "")), "createForm", "15%", 1);
/* 367 */     weaTableEditEntity3.setCom(arrayList);
/* 368 */     jSONObject1.put("createForm", weaTableEditEntity3);
/*     */     
/* 370 */     arrayList = new ArrayList<>();
/* 371 */     weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.INPUT, "1", "importBase", 120);
/* 372 */     weaTableEditComEntity.setViewAttr(1, false);
/* 373 */     hashMap = new HashMap<>();
/* 374 */     hashMap.put("hasBorder", Boolean.valueOf(false));
/* 375 */     weaTableEditComEntity.setOtherParams(hashMap);
/* 376 */     arrayList.add(weaTableEditComEntity);
/* 377 */     WeaTableEditEntity weaTableEditEntity4 = WeaTableEditUtil.getColumn(ConditionType.INPUT, SystemEnv.getHtmlLabelName(195, Util.getIntValue(paramUser.getLanguage() + "")), "importBase", "15%", 1);
/* 378 */     weaTableEditEntity4.setCom(arrayList);
/* 379 */     jSONObject1.put("importBase", weaTableEditEntity4);
/*     */     
/* 381 */     arrayList = new ArrayList<>();
/* 382 */     weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.INPUT, "1", "importIntegration", 120);
/* 383 */     weaTableEditComEntity.setViewAttr(1, false);
/* 384 */     hashMap = new HashMap<>();
/* 385 */     hashMap.put("hasBorder", Boolean.valueOf(false));
/* 386 */     weaTableEditComEntity.setOtherParams(hashMap);
/* 387 */     arrayList.add(weaTableEditComEntity);
/* 388 */     WeaTableEditEntity weaTableEditEntity5 = WeaTableEditUtil.getColumn(ConditionType.INPUT, SystemEnv.getHtmlLabelName(195, Util.getIntValue(paramUser.getLanguage() + "")), "importIntegration", "15%", 1);
/* 389 */     weaTableEditEntity5.setCom(arrayList);
/* 390 */     jSONObject1.put("importIntegration", weaTableEditEntity5);
/* 391 */     return jSONObject1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONObject getSelectFormImportTypeCells1(User paramUser, List<SearchConditionOption> paramList) {
/*     */     List list;
/* 402 */     JSONObject jSONObject1 = new JSONObject();
/* 403 */     JSONObject jSONObject2 = new JSONObject();
/* 404 */     JSONArray jSONArray = new JSONArray();
/*     */ 
/*     */     
/* 407 */     if (paramList != null && paramList.size() > 0) {
/* 408 */       list = (new ImportTypeService()).getImportType("0", paramUser, true);
/*     */     } else {
/* 410 */       list = (new ImportTypeService()).getImportType("1", paramUser, true);
/*     */     } 
/* 412 */     WeaTableEditComEntity weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.SELECT, "1", "importtype", 120, list);
/* 413 */     weaTableEditComEntity.setViewAttr(1);
/* 414 */     jSONArray.add(weaTableEditComEntity);
/* 415 */     jSONObject2.put("com", jSONArray);
/* 416 */     jSONObject1.put("importtype", jSONObject2);
/*     */     
/* 418 */     ArrayList<WeaTableEditComEntity> arrayList = new ArrayList();
/* 419 */     weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.INPUT, "1", "updateId", 120);
/* 420 */     weaTableEditComEntity.setViewAttr(1, false);
/* 421 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 422 */     hashMap.put("hasBorder", Boolean.valueOf(false));
/* 423 */     weaTableEditComEntity.setOtherParams(hashMap);
/* 424 */     arrayList.add(weaTableEditComEntity);
/* 425 */     WeaTableEditEntity weaTableEditEntity = WeaTableEditUtil.getColumn(ConditionType.INPUT, SystemEnv.getHtmlLabelName(195, Util.getIntValue(paramUser.getLanguage() + "")), "updateId", "15%", 1);
/* 426 */     weaTableEditEntity.setCom(arrayList);
/* 427 */     jSONObject1.put("updateId", weaTableEditEntity);
/* 428 */     return jSONObject1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONObject getSelectFormImportTypeCells2(User paramUser, List<SearchConditionOption> paramList) {
/*     */     List list;
/* 439 */     JSONObject jSONObject1 = new JSONObject();
/* 440 */     JSONObject jSONObject2 = new JSONObject();
/* 441 */     JSONArray jSONArray = new JSONArray();
/*     */ 
/*     */     
/* 444 */     if (paramList != null && paramList.size() > 0) {
/* 445 */       list = (new ImportTypeService()).getImportType("0", paramUser, true);
/*     */     } else {
/* 447 */       list = (new ImportTypeService()).getImportType("1", paramUser, true);
/*     */     } 
/*     */     
/* 450 */     WeaTableEditComEntity weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.SELECT, "1", "importtype", 120, list);
/* 451 */     weaTableEditComEntity.setViewAttr(1);
/* 452 */     jSONArray.add(weaTableEditComEntity);
/* 453 */     jSONObject2.put("com", jSONArray);
/* 454 */     jSONObject1.put("importtype", jSONObject2);
/*     */     
/* 456 */     jSONObject2 = new JSONObject();
/* 457 */     jSONArray = new JSONArray();
/* 458 */     if (paramList == null) {
/* 459 */       weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.SELECT, "1", "updateId", 120);
/*     */     } else {
/* 461 */       ArrayList<SearchConditionOption> arrayList1 = new ArrayList();
/*     */       
/*     */       try {
/* 464 */         for (byte b = 0; b < paramList.size(); b++) {
/* 465 */           SearchConditionOption searchConditionOption = ((SearchConditionOption)paramList.get(b)).clone();
/* 466 */           searchConditionOption.setSelected(false);
/* 467 */           arrayList1.add(searchConditionOption);
/*     */         } 
/* 469 */       } catch (CloneNotSupportedException cloneNotSupportedException) {
/* 470 */         this.log.error(cloneNotSupportedException.getMessage(), cloneNotSupportedException);
/* 471 */         arrayList1 = new ArrayList<>();
/*     */       } 
/* 473 */       weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.SELECT, "1", "updateId", 120, arrayList1);
/*     */     } 
/* 475 */     weaTableEditComEntity.setViewAttr(1);
/* 476 */     JSONObject jSONObject3 = new JSONObject();
/* 477 */     jSONObject3 = JSONObject.parseObject(JSONObject.toJSONString(weaTableEditComEntity));
/* 478 */     jSONArray.add(jSONObject3);
/* 479 */     jSONObject2.put("com", jSONArray);
/* 480 */     jSONObject1.put("updateId", jSONObject2);
/*     */     
/* 482 */     ArrayList<WeaTableEditComEntity> arrayList = new ArrayList();
/* 483 */     weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.INPUT, "1", "createForm", 120);
/* 484 */     weaTableEditComEntity.setViewAttr(1, false);
/* 485 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 486 */     hashMap.put("hasBorder", Boolean.valueOf(false));
/* 487 */     weaTableEditComEntity.setOtherParams(hashMap);
/* 488 */     arrayList.add(weaTableEditComEntity);
/* 489 */     WeaTableEditEntity weaTableEditEntity = WeaTableEditUtil.getColumn(ConditionType.INPUT, SystemEnv.getHtmlLabelName(507297, Util.getIntValue(paramUser.getLanguage() + "")), "createForm", "15%", 1);
/* 490 */     weaTableEditEntity.setCom(arrayList);
/* 491 */     jSONObject1.put("createForm", weaTableEditEntity);
/* 492 */     return jSONObject1;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/systemExpAndImp/impl/WorkflowSelectForm.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */