/*     */ package weaver.systemExpAndImp.impl;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONArray;
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import com.api.browser.bean.SearchConditionOption;
/*     */ import com.api.browser.util.ConditionType;
/*     */ import com.engine.integration.util.WeaTableEditUtil;
/*     */ import com.engine.workflow.entity.WeaTableEditComEntity;
/*     */ import com.engine.workflow.entity.WeaTableEditEntity;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import weaver.backup.logging.Logger;
/*     */ import weaver.backup.services.ImportTypeService;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systemExpAndImp.itf.ISelectForm;
/*     */ import weaver.systemExpAndImp.logging.LoggerFactory;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class MatrixSelectFrom
/*     */   implements ISelectForm
/*     */ {
/*     */   private String type;
/*  35 */   private Logger log = LoggerFactory.getLogger(getClass());
/*     */   
/*     */   public MatrixSelectFrom(String paramString) {
/*  38 */     this.type = paramString;
/*     */   }
/*     */ 
/*     */   
/*     */   public List<Object> getSelectFormColumns(User paramUser, List<SearchConditionOption> paramList) {
/*  43 */     return getSelectFormColumns(paramUser, paramList, null);
/*     */   }
/*     */ 
/*     */   
/*     */   public List<Object> getSelectFormColumns(User paramUser, List<SearchConditionOption> paramList, JSONArray paramJSONArray) {
/*  48 */     ArrayList<JSONObject> arrayList = new ArrayList();
/*     */     
/*  50 */     ArrayList<WeaTableEditComEntity> arrayList1 = new ArrayList();
/*     */ 
/*     */     
/*  53 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  54 */     WeaTableEditEntity weaTableEditEntity1 = WeaTableEditUtil.getColumn(ConditionType.CHECKBOX, SystemEnv.getHtmlLabelName(129432, Util.getIntValue(paramUser.getLanguage() + "")), "isImport", "15%", 2, hashMap1);
/*  55 */     JSONObject jSONObject = new JSONObject();
/*  56 */     jSONObject = JSONObject.parseObject(JSONObject.toJSONString(weaTableEditEntity1));
/*  57 */     jSONObject.getJSONArray("com").getJSONObject(0).put("display", "switch");
/*  58 */     arrayList.add(jSONObject);
/*     */ 
/*     */     
/*  61 */     WeaTableEditComEntity weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.INPUT, "1", "name", 120);
/*  62 */     weaTableEditComEntity.setViewAttr(1, true);
/*  63 */     hashMap1 = new HashMap<>();
/*  64 */     hashMap1.put("hasBorder", Boolean.valueOf(false));
/*  65 */     weaTableEditComEntity.setOtherParams(hashMap1);
/*  66 */     arrayList1.add(weaTableEditComEntity);
/*  67 */     WeaTableEditEntity weaTableEditEntity2 = WeaTableEditUtil.getColumn(ConditionType.INPUT, SystemEnv.getHtmlLabelName(195, Util.getIntValue(paramUser.getLanguage() + "")), "name", "15%", 1);
/*  68 */     weaTableEditEntity2.setCom(arrayList1);
/*  69 */     arrayList.add(weaTableEditEntity2);
/*     */ 
/*     */     
/*  72 */     ArrayList<SearchConditionOption> arrayList2 = new ArrayList();
/*  73 */     SearchConditionOption searchConditionOption1 = new SearchConditionOption();
/*  74 */     searchConditionOption1.setKey("2");
/*  75 */     searchConditionOption1.setShowname(SystemEnv.getHtmlLabelName(129294, Util.getIntValue(paramUser.getLanguage() + "")));
/*  76 */     searchConditionOption1.setSelected(true);
/*     */     
/*  78 */     SearchConditionOption searchConditionOption2 = new SearchConditionOption();
/*  79 */     searchConditionOption2.setKey("1");
/*  80 */     searchConditionOption2.setShowname(SystemEnv.getHtmlLabelName(1421, Util.getIntValue(paramUser.getLanguage() + "")));
/*     */     
/*  82 */     SearchConditionOption searchConditionOption3 = new SearchConditionOption();
/*  83 */     searchConditionOption3.setKey("0");
/*  84 */     searchConditionOption3.setShowname(SystemEnv.getHtmlLabelName(17744, Util.getIntValue(paramUser.getLanguage() + "")));
/*     */     
/*  86 */     arrayList2.add(searchConditionOption1);
/*  87 */     arrayList2.add(searchConditionOption2);
/*  88 */     arrayList2.add(searchConditionOption3);
/*  89 */     WeaTableEditEntity weaTableEditEntity3 = WeaTableEditUtil.getColumn(ConditionType.SELECT, SystemEnv.getHtmlLabelName(24863, Util.getIntValue(paramUser.getLanguage() + "")), "importtype", "15%", 2, arrayList2);
/*  90 */     jSONObject = new JSONObject();
/*  91 */     jSONObject = JSONObject.parseObject(JSONObject.toJSONString(weaTableEditEntity3));
/*  92 */     jSONObject.getJSONArray("com").getJSONObject(0).put("viewAttr", Integer.valueOf(1));
/*  93 */     jSONObject.getJSONArray("com").getJSONObject(0).put("hasBorder", Boolean.valueOf(false));
/*  94 */     jSONObject.getJSONArray("com").getJSONObject(0).put("otherParams", "");
/*  95 */     arrayList.add(jSONObject);
/*     */ 
/*     */     
/*  98 */     if (paramList == null) {
/*  99 */       weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.SELECT, "1", "updateId", 120);
/*     */     } else {
/* 101 */       ArrayList<SearchConditionOption> arrayList3 = new ArrayList();
/*     */       
/*     */       try {
/* 104 */         for (byte b = 0; b < paramList.size(); b++) {
/* 105 */           SearchConditionOption searchConditionOption = ((SearchConditionOption)paramList.get(b)).clone();
/* 106 */           searchConditionOption.setSelected(false);
/* 107 */           arrayList3.add(searchConditionOption);
/*     */         } 
/* 109 */       } catch (CloneNotSupportedException cloneNotSupportedException) {
/* 110 */         this.log.error(cloneNotSupportedException.getMessage(), cloneNotSupportedException);
/* 111 */         arrayList3 = new ArrayList<>();
/*     */       } 
/* 113 */       weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.SELECT, "1", "updateId", 120, arrayList3);
/*     */     } 
/* 115 */     weaTableEditComEntity.setViewAttr(1);
/* 116 */     arrayList1 = new ArrayList<>();
/* 117 */     arrayList1.add(weaTableEditComEntity);
/* 118 */     WeaTableEditEntity weaTableEditEntity4 = WeaTableEditUtil.getColumn(ConditionType.SELECT, SystemEnv.getHtmlLabelName(523786, Util.getIntValue(paramUser.getLanguage() + "")), "updateId", "25%", 1);
/* 119 */     weaTableEditEntity4.setCom(arrayList1);
/* 120 */     arrayList.add(weaTableEditEntity4);
/*     */ 
/*     */     
/* 123 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*     */     
/* 125 */     hashMap2.put("helpfulTip", SystemEnv.getHtmlLabelName(525839, Util.getIntValue(paramUser.getLanguage() + "")));
/* 126 */     WeaTableEditEntity weaTableEditEntity5 = WeaTableEditUtil.getColumn(ConditionType.CHECKBOX, SystemEnv.getHtmlLabelName(507298, Util.getIntValue(paramUser.getLanguage() + "")), "importBase", "15%", 2, hashMap2);
/* 127 */     weaTableEditEntity5.getCom().get(0);
/* 128 */     jSONObject = new JSONObject();
/* 129 */     jSONObject = JSONObject.parseObject(JSONObject.toJSONString(weaTableEditEntity5));
/* 130 */     jSONObject.getJSONArray("com").getJSONObject(0).put("display", "switch");
/* 131 */     arrayList.add(jSONObject);
/*     */ 
/*     */     
/* 134 */     HashMap<Object, Object> hashMap3 = new HashMap<>();
/*     */     
/* 136 */     hashMap3.put("helpfulTip", SystemEnv.getHtmlLabelName(530885, Util.getIntValue(paramUser.getLanguage() + "")));
/* 137 */     WeaTableEditEntity weaTableEditEntity6 = WeaTableEditUtil.getColumn(ConditionType.CHECKBOX, SystemEnv.getHtmlLabelName(514424, Util.getIntValue(paramUser.getLanguage() + "")), "importIntegration", "15%", 2, hashMap3);
/* 138 */     jSONObject = new JSONObject();
/* 139 */     jSONObject = JSONObject.parseObject(JSONObject.toJSONString(weaTableEditEntity6));
/* 140 */     jSONObject.getJSONArray("com").getJSONObject(0).put("display", "switch");
/* 141 */     arrayList.add(jSONObject);
/*     */     
/* 143 */     return (List)arrayList;
/*     */   }
/*     */ 
/*     */   
/*     */   public List<Object> getImportResultColumns(User paramUser, List<SearchConditionOption> paramList) {
/* 148 */     ArrayList<WeaTableEditEntity> arrayList = new ArrayList();
/*     */     
/* 150 */     ArrayList<WeaTableEditComEntity> arrayList1 = new ArrayList();
/*     */     
/* 152 */     WeaTableEditComEntity weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.INPUT, "1", "name", 120);
/* 153 */     weaTableEditComEntity.setViewAttr(1, true);
/* 154 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 155 */     hashMap1.put("hasBorder", Boolean.valueOf(false));
/* 156 */     weaTableEditComEntity.setOtherParams(hashMap1);
/* 157 */     arrayList1.add(weaTableEditComEntity);
/* 158 */     WeaTableEditEntity weaTableEditEntity1 = WeaTableEditUtil.getColumn(ConditionType.INPUT, SystemEnv.getHtmlLabelName(195, Util.getIntValue(paramUser.getLanguage() + "")), "name", "15%", 1);
/* 159 */     weaTableEditEntity1.setCom(arrayList1);
/* 160 */     arrayList.add(weaTableEditEntity1);
/*     */ 
/*     */     
/* 163 */     ArrayList<SearchConditionOption> arrayList2 = new ArrayList();
/* 164 */     SearchConditionOption searchConditionOption1 = new SearchConditionOption();
/* 165 */     searchConditionOption1.setKey("2");
/* 166 */     searchConditionOption1.setShowname(SystemEnv.getHtmlLabelName(129294, Util.getIntValue(paramUser.getLanguage() + "")));
/* 167 */     searchConditionOption1.setSelected(true);
/* 168 */     SearchConditionOption searchConditionOption2 = new SearchConditionOption();
/* 169 */     searchConditionOption2.setKey("1");
/* 170 */     searchConditionOption2.setShowname(SystemEnv.getHtmlLabelName(1421, Util.getIntValue(paramUser.getLanguage() + "")));
/* 171 */     SearchConditionOption searchConditionOption3 = new SearchConditionOption();
/* 172 */     searchConditionOption3.setKey("0");
/* 173 */     searchConditionOption3.setShowname(SystemEnv.getHtmlLabelName(17744, Util.getIntValue(paramUser.getLanguage() + "")));
/* 174 */     arrayList2.add(searchConditionOption1);
/* 175 */     arrayList2.add(searchConditionOption2);
/* 176 */     arrayList2.add(searchConditionOption3);
/* 177 */     WeaTableEditEntity weaTableEditEntity2 = WeaTableEditUtil.getColumn(ConditionType.SELECT, SystemEnv.getHtmlLabelName(24863, Util.getIntValue(paramUser.getLanguage() + "")), "importtype", "15%", 1, arrayList2);
/* 178 */     JSONObject jSONObject = new JSONObject();
/* 179 */     jSONObject = JSONObject.parseObject(JSONObject.toJSONString(weaTableEditEntity2));
/* 180 */     jSONObject.getJSONArray("com").getJSONObject(0).put("viewAttr", Integer.valueOf(1));
/* 181 */     jSONObject.getJSONArray("com").getJSONObject(0).put("hasBorder", Boolean.valueOf(false));
/* 182 */     jSONObject.getJSONArray("com").getJSONObject(0).put("otherParams", "");
/* 183 */     arrayList.add(jSONObject);
/*     */ 
/*     */     
/* 186 */     if (paramList == null) {
/* 187 */       weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.SELECT, "1", "updateId", 120);
/*     */     } else {
/* 189 */       ArrayList<SearchConditionOption> arrayList3 = new ArrayList();
/*     */       
/*     */       try {
/* 192 */         for (byte b = 0; b < paramList.size(); b++) {
/* 193 */           SearchConditionOption searchConditionOption = ((SearchConditionOption)paramList.get(b)).clone();
/* 194 */           searchConditionOption.setSelected(false);
/* 195 */           arrayList3.add(searchConditionOption);
/*     */         } 
/* 197 */       } catch (CloneNotSupportedException cloneNotSupportedException) {
/* 198 */         this.log.error(cloneNotSupportedException.getMessage(), cloneNotSupportedException);
/* 199 */         arrayList3 = new ArrayList<>();
/*     */       } 
/* 201 */       weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.SELECT, "1", "updateId", 120, arrayList3);
/*     */     } 
/* 203 */     weaTableEditComEntity.setViewAttr(1);
/* 204 */     arrayList1 = new ArrayList<>();
/* 205 */     arrayList1.add(weaTableEditComEntity);
/* 206 */     WeaTableEditEntity weaTableEditEntity3 = WeaTableEditUtil.getColumn(ConditionType.SELECT, SystemEnv.getHtmlLabelName(523786, Util.getIntValue(paramUser.getLanguage() + "")), "updateId", "25%", 1);
/* 207 */     weaTableEditEntity3.setCom(arrayList1);
/* 208 */     arrayList.add(weaTableEditEntity3);
/*     */ 
/*     */     
/* 211 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*     */     
/* 213 */     hashMap2.put("helpfulTip", SystemEnv.getHtmlLabelName(525839, Util.getIntValue(paramUser.getLanguage() + "")));
/* 214 */     WeaTableEditEntity weaTableEditEntity4 = WeaTableEditUtil.getColumn(ConditionType.CHECKBOX, SystemEnv.getHtmlLabelName(507298, Util.getIntValue(paramUser.getLanguage() + "")), "importBase", "15%", 1, hashMap2);
/* 215 */     weaTableEditEntity4.getCom().get(0);
/* 216 */     jSONObject = new JSONObject();
/* 217 */     jSONObject = JSONObject.parseObject(JSONObject.toJSONString(weaTableEditEntity4));
/* 218 */     jSONObject.getJSONArray("com").getJSONObject(0).put("display", "switch");
/* 219 */     arrayList.add(jSONObject);
/*     */ 
/*     */     
/* 222 */     HashMap<Object, Object> hashMap3 = new HashMap<>();
/*     */     
/* 224 */     hashMap3.put("helpfulTip", SystemEnv.getHtmlLabelName(525906, Util.getIntValue(paramUser.getLanguage() + "")));
/* 225 */     WeaTableEditEntity weaTableEditEntity5 = WeaTableEditUtil.getColumn(ConditionType.CHECKBOX, SystemEnv.getHtmlLabelName(514424, Util.getIntValue(paramUser.getLanguage() + "")), "importIntegration", "15%", 1, hashMap3);
/* 226 */     jSONObject = new JSONObject();
/* 227 */     jSONObject = JSONObject.parseObject(JSONObject.toJSONString(weaTableEditEntity5));
/* 228 */     jSONObject.getJSONArray("com").getJSONObject(0).put("display", "switch");
/* 229 */     arrayList.add(jSONObject);
/*     */     
/* 231 */     return (List)arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONObject getSelectFormOnlyReadColumns(User paramUser, List<SearchConditionOption> paramList) {
/* 237 */     ImportTypeService importTypeService = new ImportTypeService();
/* 238 */     JSONObject jSONObject1 = new JSONObject();
/* 239 */     JSONObject jSONObject2 = new JSONObject();
/* 240 */     JSONArray jSONArray = new JSONArray();
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 245 */     jSONObject2 = new JSONObject();
/* 246 */     jSONArray = new JSONArray();
/* 247 */     WeaTableEditComEntity weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.CHECKBOX, "1", "isImport", 120);
/* 248 */     JSONObject jSONObject3 = JSONObject.parseObject(JSONObject.toJSONString(weaTableEditComEntity));
/* 249 */     jSONObject3.put("display", "switch");
/* 250 */     jSONObject3.put("viewAttr", Integer.valueOf(1));
/* 251 */     jSONArray.add(jSONObject3);
/* 252 */     jSONObject2.put("com", jSONArray);
/* 253 */     jSONObject1.put("isImport", jSONObject2);
/*     */     
/* 255 */     jSONObject2 = new JSONObject();
/* 256 */     jSONArray = new JSONArray();
/* 257 */     weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.SELECT, "1", "importtype", 120, importTypeService.getImportType("0", paramUser, true));
/* 258 */     weaTableEditComEntity.setViewAttr(1);
/* 259 */     jSONArray.add(weaTableEditComEntity);
/* 260 */     jSONObject2.put("com", jSONArray);
/* 261 */     jSONObject1.put("importtype", jSONObject2);
/*     */     
/* 263 */     jSONObject2 = new JSONObject();
/* 264 */     jSONArray = new JSONArray();
/* 265 */     if (paramList == null) {
/* 266 */       weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.SELECT, "1", "updateId", 120);
/*     */     } else {
/* 268 */       ArrayList<SearchConditionOption> arrayList = new ArrayList();
/*     */       
/*     */       try {
/* 271 */         for (byte b = 0; b < paramList.size(); b++) {
/* 272 */           SearchConditionOption searchConditionOption = ((SearchConditionOption)paramList.get(b)).clone();
/* 273 */           searchConditionOption.setSelected(false);
/* 274 */           arrayList.add(searchConditionOption);
/*     */         } 
/* 276 */       } catch (CloneNotSupportedException cloneNotSupportedException) {
/* 277 */         this.log.error(cloneNotSupportedException.getMessage(), cloneNotSupportedException);
/* 278 */         arrayList = new ArrayList<>();
/*     */       } 
/* 280 */       weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.SELECT, "1", "updateId", 120, arrayList);
/*     */     } 
/* 282 */     weaTableEditComEntity.setViewAttr(1);
/* 283 */     jSONArray.add(weaTableEditComEntity);
/* 284 */     jSONObject2.put("com", jSONArray);
/* 285 */     jSONObject1.put("updateId", jSONObject2);
/*     */ 
/*     */     
/* 288 */     jSONObject2 = new JSONObject();
/* 289 */     jSONArray = new JSONArray();
/* 290 */     weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.CHECKBOX, "1", "importBase", 120);
/* 291 */     jSONObject3 = JSONObject.parseObject(JSONObject.toJSONString(weaTableEditComEntity));
/* 292 */     jSONObject3.put("display", "switch");
/* 293 */     jSONObject3.put("viewAttr", Integer.valueOf(1));
/* 294 */     jSONArray.add(jSONObject3);
/* 295 */     jSONObject2.put("com", jSONArray);
/* 296 */     jSONObject1.put("importBase", jSONObject2);
/*     */ 
/*     */     
/* 299 */     jSONObject2 = new JSONObject();
/* 300 */     jSONArray = new JSONArray();
/* 301 */     weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.CHECKBOX, "1", "importIntegration", 120);
/* 302 */     jSONObject3 = JSONObject.parseObject(JSONObject.toJSONString(weaTableEditComEntity));
/* 303 */     jSONObject3.put("display", "switch");
/* 304 */     jSONObject3.put("viewAttr", Integer.valueOf(1));
/* 305 */     jSONArray.add(jSONObject3);
/* 306 */     jSONObject2.put("com", jSONArray);
/* 307 */     jSONObject1.put("importIntegration", jSONObject2);
/* 308 */     return jSONObject1;
/*     */   }
/*     */ 
/*     */   
/*     */   public JSONObject getSelectFormImportTypeCells0(User paramUser, List<SearchConditionOption> paramList) {
/* 313 */     JSONObject jSONObject1 = new JSONObject();
/* 314 */     JSONObject jSONObject2 = new JSONObject();
/* 315 */     JSONArray jSONArray = new JSONArray();
/* 316 */     ArrayList<WeaTableEditComEntity> arrayList = new ArrayList();
/*     */     
/* 318 */     WeaTableEditComEntity weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.INPUT, "1", "importtype", 120);
/* 319 */     weaTableEditComEntity.setViewAttr(1, false);
/* 320 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 321 */     hashMap.put("hasBorder", Boolean.valueOf(false));
/* 322 */     weaTableEditComEntity.setOtherParams(hashMap);
/* 323 */     arrayList.add(weaTableEditComEntity);
/* 324 */     WeaTableEditEntity weaTableEditEntity1 = WeaTableEditUtil.getColumn(ConditionType.INPUT, SystemEnv.getHtmlLabelName(195, Util.getIntValue(paramUser.getLanguage() + "")), "updateId", "15%", 1);
/* 325 */     weaTableEditEntity1.setCom(arrayList);
/* 326 */     jSONObject1.put("importtype", weaTableEditEntity1);
/*     */     
/* 328 */     arrayList = new ArrayList<>();
/* 329 */     weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.INPUT, "1", "updateId", 120);
/* 330 */     weaTableEditComEntity.setViewAttr(1, false);
/* 331 */     hashMap = new HashMap<>();
/* 332 */     hashMap.put("hasBorder", Boolean.valueOf(false));
/* 333 */     weaTableEditComEntity.setOtherParams(hashMap);
/* 334 */     arrayList.add(weaTableEditComEntity);
/* 335 */     WeaTableEditEntity weaTableEditEntity2 = WeaTableEditUtil.getColumn(ConditionType.INPUT, SystemEnv.getHtmlLabelName(195, Util.getIntValue(paramUser.getLanguage() + "")), "updateId", "15%", 1);
/* 336 */     weaTableEditEntity2.setCom(arrayList);
/* 337 */     jSONObject1.put("updateId", weaTableEditEntity2);
/*     */ 
/*     */     
/* 340 */     arrayList = new ArrayList<>();
/* 341 */     weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.INPUT, "1", "importBase", 120);
/* 342 */     weaTableEditComEntity.setViewAttr(1, false);
/* 343 */     hashMap = new HashMap<>();
/* 344 */     hashMap.put("hasBorder", Boolean.valueOf(false));
/* 345 */     weaTableEditComEntity.setOtherParams(hashMap);
/* 346 */     arrayList.add(weaTableEditComEntity);
/* 347 */     WeaTableEditEntity weaTableEditEntity3 = WeaTableEditUtil.getColumn(ConditionType.INPUT, SystemEnv.getHtmlLabelName(195, Util.getIntValue(paramUser.getLanguage() + "")), "importBase", "15%", 1);
/* 348 */     weaTableEditEntity3.setCom(arrayList);
/* 349 */     jSONObject1.put("importBase", weaTableEditEntity3);
/*     */ 
/*     */     
/* 352 */     arrayList = new ArrayList<>();
/* 353 */     weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.INPUT, "1", "importIntegration", 120);
/* 354 */     weaTableEditComEntity.setViewAttr(1, false);
/* 355 */     hashMap = new HashMap<>();
/* 356 */     hashMap.put("hasBorder", Boolean.valueOf(false));
/* 357 */     weaTableEditComEntity.setOtherParams(hashMap);
/* 358 */     arrayList.add(weaTableEditComEntity);
/* 359 */     WeaTableEditEntity weaTableEditEntity4 = WeaTableEditUtil.getColumn(ConditionType.INPUT, SystemEnv.getHtmlLabelName(195, Util.getIntValue(paramUser.getLanguage() + "")), "importIntegration", "15%", 1);
/* 360 */     weaTableEditEntity4.setCom(arrayList);
/* 361 */     jSONObject1.put("importIntegration", weaTableEditEntity4);
/*     */     
/* 363 */     return jSONObject1;
/*     */   }
/*     */   
/*     */   public JSONObject getSelectFormImportTypeCells1(User paramUser, List<SearchConditionOption> paramList) {
/*     */     List list;
/* 368 */     JSONObject jSONObject1 = new JSONObject();
/* 369 */     JSONObject jSONObject2 = new JSONObject();
/* 370 */     JSONArray jSONArray = new JSONArray();
/*     */ 
/*     */     
/* 373 */     if (paramList != null && paramList.size() > 0) {
/* 374 */       list = (new ImportTypeService()).getImportType("0", paramUser, true);
/*     */     } else {
/* 376 */       list = (new ImportTypeService()).getImportType("1", paramUser, true);
/*     */     } 
/* 378 */     WeaTableEditComEntity weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.SELECT, "1", "importtype", 120, list);
/* 379 */     weaTableEditComEntity.setViewAttr(1);
/* 380 */     jSONArray.add(weaTableEditComEntity);
/* 381 */     jSONObject2.put("com", jSONArray);
/* 382 */     jSONObject1.put("importtype", jSONObject2);
/*     */ 
/*     */     
/* 385 */     ArrayList<WeaTableEditComEntity> arrayList = new ArrayList();
/* 386 */     arrayList = new ArrayList();
/* 387 */     weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.INPUT, "1", "updateId", 120);
/* 388 */     weaTableEditComEntity.setViewAttr(1, false);
/* 389 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 390 */     hashMap.put("hasBorder", Boolean.valueOf(false));
/* 391 */     weaTableEditComEntity.setOtherParams(hashMap);
/* 392 */     arrayList.add(weaTableEditComEntity);
/* 393 */     WeaTableEditEntity weaTableEditEntity = WeaTableEditUtil.getColumn(ConditionType.INPUT, SystemEnv.getHtmlLabelName(195, Util.getIntValue(paramUser.getLanguage() + "")), "updateId", "15%", 1);
/* 394 */     weaTableEditEntity.setCom(arrayList);
/* 395 */     jSONObject1.put("updateId", weaTableEditEntity);
/*     */ 
/*     */     
/* 398 */     jSONObject2 = new JSONObject();
/* 399 */     jSONArray = new JSONArray();
/* 400 */     weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.CHECKBOX, "1", "importBase", 120);
/* 401 */     JSONObject jSONObject3 = JSONObject.parseObject(JSONObject.toJSONString(weaTableEditComEntity));
/* 402 */     jSONObject3.put("display", "switch");
/* 403 */     jSONArray.add(jSONObject3);
/* 404 */     jSONObject2.put("com", jSONArray);
/* 405 */     jSONObject1.put("importBase", jSONObject2);
/*     */ 
/*     */     
/* 408 */     jSONObject2 = new JSONObject();
/* 409 */     jSONArray = new JSONArray();
/* 410 */     weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.CHECKBOX, "1", "importIntegration", 120);
/* 411 */     jSONObject3 = JSONObject.parseObject(JSONObject.toJSONString(weaTableEditComEntity));
/* 412 */     jSONObject3.put("display", "switch");
/* 413 */     jSONArray.add(jSONObject3);
/* 414 */     jSONObject2.put("com", jSONArray);
/* 415 */     jSONObject1.put("importIntegration", jSONObject2);
/*     */     
/* 417 */     return jSONObject1;
/*     */   }
/*     */   
/*     */   public JSONObject getSelectFormImportTypeCells2(User paramUser, List<SearchConditionOption> paramList) {
/*     */     List list;
/* 422 */     JSONObject jSONObject1 = new JSONObject();
/* 423 */     JSONObject jSONObject2 = new JSONObject();
/* 424 */     JSONArray jSONArray = new JSONArray();
/*     */ 
/*     */     
/* 427 */     if (paramList != null && paramList.size() > 0) {
/* 428 */       list = (new ImportTypeService()).getImportType("0", paramUser, true);
/*     */     } else {
/* 430 */       list = (new ImportTypeService()).getImportType("1", paramUser, true);
/*     */     } 
/* 432 */     WeaTableEditComEntity weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.SELECT, "1", "importtype", 120, list);
/* 433 */     weaTableEditComEntity.setViewAttr(1);
/* 434 */     jSONArray.add(weaTableEditComEntity);
/* 435 */     jSONObject2.put("com", jSONArray);
/* 436 */     jSONObject1.put("importtype", jSONObject2);
/*     */ 
/*     */     
/* 439 */     jSONObject2 = new JSONObject();
/* 440 */     jSONArray = new JSONArray();
/* 441 */     if (paramList == null) {
/* 442 */       weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.SELECT, "1", "updateId", 120);
/*     */     } else {
/* 444 */       ArrayList<SearchConditionOption> arrayList = new ArrayList();
/*     */       
/*     */       try {
/* 447 */         for (byte b = 0; b < paramList.size(); b++) {
/* 448 */           SearchConditionOption searchConditionOption = ((SearchConditionOption)paramList.get(b)).clone();
/* 449 */           searchConditionOption.setSelected(false);
/* 450 */           arrayList.add(searchConditionOption);
/*     */         } 
/* 452 */       } catch (CloneNotSupportedException cloneNotSupportedException) {
/* 453 */         this.log.error(cloneNotSupportedException.getMessage(), cloneNotSupportedException);
/* 454 */         arrayList = new ArrayList<>();
/*     */       } 
/* 456 */       weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.SELECT, "1", "updateId", 120, arrayList);
/*     */     } 
/* 458 */     weaTableEditComEntity.setViewAttr(1);
/* 459 */     JSONObject jSONObject3 = new JSONObject();
/* 460 */     jSONObject3 = JSONObject.parseObject(JSONObject.toJSONString(weaTableEditComEntity));
/* 461 */     jSONArray.add(jSONObject3);
/* 462 */     jSONObject2.put("com", jSONArray);
/* 463 */     jSONObject1.put("updateId", jSONObject2);
/*     */ 
/*     */     
/* 466 */     jSONObject2 = new JSONObject();
/* 467 */     jSONArray = new JSONArray();
/* 468 */     weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.CHECKBOX, "1", "importBase", 120);
/* 469 */     jSONObject3 = JSONObject.parseObject(JSONObject.toJSONString(weaTableEditComEntity));
/* 470 */     jSONObject3.put("display", "switch");
/* 471 */     jSONArray.add(jSONObject3);
/* 472 */     jSONObject2.put("com", jSONArray);
/* 473 */     jSONObject1.put("importBase", jSONObject2);
/*     */ 
/*     */     
/* 476 */     jSONObject2 = new JSONObject();
/* 477 */     jSONArray = new JSONArray();
/* 478 */     weaTableEditComEntity = new WeaTableEditComEntity("", ConditionType.CHECKBOX, "1", "importIntegration", 120);
/* 479 */     jSONObject3 = JSONObject.parseObject(JSONObject.toJSONString(weaTableEditComEntity));
/* 480 */     jSONObject3.put("display", "switch");
/* 481 */     jSONArray.add(jSONObject3);
/* 482 */     jSONObject2.put("com", jSONArray);
/* 483 */     jSONObject1.put("importIntegration", jSONObject2);
/*     */     
/* 485 */     return jSONObject1;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/systemExpAndImp/impl/MatrixSelectFrom.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */