/*     */ package weaver.templetecheck;
/*     */ 
/*     */ import java.util.HashSet;
/*     */ import java.util.LinkedList;
/*     */ import java.util.Set;
/*     */ import java.util.StringTokenizer;
/*     */ 
/*     */ public class SqlFormatUtils {
/*   9 */   private static final Set<String> BEGIN_CLAUSES = new HashSet<>();
/*  10 */   private static final Set<String> END_CLAUSES = new HashSet<>();
/*  11 */   private static final Set<String> LOGICAL = new HashSet<>();
/*  12 */   private static final Set<String> QUANTIFIERS = new HashSet<>();
/*  13 */   private static final Set<String> DML = new HashSet<>();
/*  14 */   private static final Set<String> MISC = new HashSet<>();
/*     */   static final String indentString = "    ";
/*     */   static final String initial = "\n    ";
/*     */   
/*     */   public static void main(String[] paramArrayOfString) {
/*  19 */     System.out.println(format("CREATE TABLE workflow_form ( requestid integer NOT NULL , billformid integer NULL , billid integer NULL , totaltime number(10, 3) NULL , department integer NULL , relatedcustomer integer NULL , relatedresource integer NULL , relateddocument integer NULL , relatedrequest integer NULL , userdepartment integer NULL , startrailwaystation integer NULL , subject varchar2 (200)  , hotellevel integer NULL , integer1 integer NULL , desc1 varchar2 (100)  , desc2 varchar2 (100)  , desc3 varchar2 (100)  , desc4 varchar2 (100)  , desc5 varchar2 (100)  , desc6 varchar2 (100)  , desc7 varchar2 (100)  , integer2 integer NULL , reception_important integer NULL , check2 char (1)  , check3 char (1)  , check4 char (1)  , textvalue1 varchar2(4000)  , textvalue2 varchar2(4000) , textvalue3 varchar2(4000)  , textvalue4 varchar2(4000)  , textvalue5 varchar2(4000)  , textvalue6 varchar2(4000)  , textvalue7 varchar2(4000)  , softwaregetway integer NULL , decimalvalue1 number(10, 3) NULL , decimalvalue2 number(10, 3) NULL , manager integer NULL , jobtitle integer NULL , jobtitle2 integer NULL , document integer NULL , Customer integer NULL , Project integer NULL , resource_n integer NULL , item integer NULL , request integer NULL , mutiresource varchar2(4000)  , muticustomer varchar2(4000)  , remark varchar2(4000)  , description varchar2 (100)  , begindate char (10)  , begintime char (5)  , enddate char (10)  , endtime char (5)  , totaldays integer NULL , check1 char (1) , amount number(10, 3) NULL , startairport integer NULL , airways integer NULL , payoptions integer NULL , expresstype integer NULL , jtgj integer NULL , absencetype integer NULL , zc integer NULL , zczl integer NULL , fwcp integer NULL , muticareer varchar2(4000)  , date1 char (10)  , date2 char (10)  , date3 char (10)  , date4 char (10)  , date5 char (10) , date6 char (10)  , time1 char (5)  , time2 char (5)  , time3 char (5)  , time4 char (5)  , time5 char (5)  , time6 char (5)  , resource1 integer NULL , date_n char (10)  , relatmeeting integer NULL , itservice integer NULL , cplx integer NULL , gzlx integer NULL )"));
/*     */   }
/*     */   public static String format(String paramString) {
/*  22 */     return (new FormatProcess(paramString)).perform().trim();
/*     */   }
/*     */   
/*     */   static {
/*  26 */     BEGIN_CLAUSES.add("left");
/*  27 */     BEGIN_CLAUSES.add("right");
/*  28 */     BEGIN_CLAUSES.add("inner");
/*  29 */     BEGIN_CLAUSES.add("outer");
/*  30 */     BEGIN_CLAUSES.add("group");
/*  31 */     BEGIN_CLAUSES.add("order");
/*     */     
/*  33 */     END_CLAUSES.add("where");
/*  34 */     END_CLAUSES.add("set");
/*  35 */     END_CLAUSES.add("having");
/*  36 */     END_CLAUSES.add("join");
/*  37 */     END_CLAUSES.add("from");
/*  38 */     END_CLAUSES.add("by");
/*  39 */     END_CLAUSES.add("join");
/*  40 */     END_CLAUSES.add("into");
/*  41 */     END_CLAUSES.add("union");
/*     */     
/*  43 */     LOGICAL.add("and");
/*  44 */     LOGICAL.add("or");
/*  45 */     LOGICAL.add("when");
/*  46 */     LOGICAL.add("else");
/*  47 */     LOGICAL.add("end");
/*     */     
/*  49 */     QUANTIFIERS.add("in");
/*  50 */     QUANTIFIERS.add("all");
/*  51 */     QUANTIFIERS.add("exists");
/*  52 */     QUANTIFIERS.add("some");
/*  53 */     QUANTIFIERS.add("any");
/*     */     
/*  55 */     DML.add("insert");
/*  56 */     DML.add("update");
/*  57 */     DML.add("delete");
/*     */     
/*  59 */     MISC.add("select");
/*  60 */     MISC.add("on");
/*     */   }
/*     */   
/*     */   private static class FormatProcess
/*     */   {
/*     */     boolean beginLine = true;
/*     */     boolean afterBeginBeforeEnd = false;
/*     */     boolean afterByOrSetOrFromOrSelect = false;
/*     */     boolean afterValues = false;
/*     */     boolean afterOn = false;
/*     */     boolean afterBetween = false;
/*     */     boolean afterInsert = false;
/*  72 */     int inFunction = 0;
/*  73 */     int parensSinceSelect = 0;
/*  74 */     private LinkedList<Integer> parenCounts = new LinkedList<>();
/*  75 */     private LinkedList<Boolean> afterByOrFromOrSelects = new LinkedList<>();
/*     */     
/*  77 */     int indent = 1;
/*     */     
/*  79 */     StringBuffer result = new StringBuffer();
/*     */     StringTokenizer tokens;
/*     */     String lastToken;
/*     */     String token;
/*     */     String lcToken;
/*     */     
/*     */     public FormatProcess(String param1String) {
/*  86 */       this.tokens = new StringTokenizer(param1String, "()+*/-=<>'`\"[], \n\r\f\t", true);
/*     */     }
/*     */     
/*     */     public String perform() {
/*  90 */       this.result.append("NewLineSymbol    ");
/*     */       
/*  92 */       while (this.tokens.hasMoreTokens()) {
/*  93 */         this.token = this.tokens.nextToken();
/*  94 */         this.lcToken = this.token.toLowerCase();
/*     */         
/*  96 */         if ("'".equals(this.token)) {
/*     */           String str;
/*     */           do {
/*  99 */             str = this.tokens.nextToken();
/* 100 */             this.token += str;
/* 101 */           } while (!"'".equals(str) && this.tokens.hasMoreTokens());
/* 102 */         } else if ("\"".equals(this.token)) {
/*     */           String str;
/*     */           do {
/* 105 */             str = this.tokens.nextToken();
/* 106 */             this.token += str;
/* 107 */           } while (!"\"".equals(str));
/*     */         } 
/*     */         
/* 110 */         if (this.afterByOrSetOrFromOrSelect && ",".equals(this.token)) {
/* 111 */           commaAfterByOrFromOrSelect();
/* 112 */         } else if (this.afterOn && ",".equals(this.token)) {
/* 113 */           commaAfterOn();
/* 114 */         } else if ("(".equals(this.token)) {
/* 115 */           openParen();
/* 116 */         } else if (")".equals(this.token)) {
/* 117 */           closeParen();
/* 118 */         } else if (SqlFormatUtils.BEGIN_CLAUSES.contains(this.lcToken)) {
/* 119 */           beginNewClause();
/* 120 */         } else if (SqlFormatUtils.END_CLAUSES.contains(this.lcToken)) {
/* 121 */           endNewClause();
/* 122 */         } else if ("select".equals(this.lcToken)) {
/* 123 */           select();
/* 124 */         } else if (SqlFormatUtils.DML.contains(this.lcToken)) {
/* 125 */           updateOrInsertOrDelete();
/* 126 */         } else if ("values".equals(this.lcToken)) {
/* 127 */           values();
/* 128 */         } else if ("on".equals(this.lcToken)) {
/* 129 */           on();
/* 130 */         } else if (this.afterBetween && this.lcToken.equals("and")) {
/* 131 */           misc();
/* 132 */           this.afterBetween = false;
/* 133 */         } else if (SqlFormatUtils.LOGICAL.contains(this.lcToken)) {
/* 134 */           logical();
/* 135 */         } else if (isWhitespace(this.token)) {
/* 136 */           white();
/*     */         } else {
/* 138 */           misc();
/*     */         } 
/*     */         
/* 141 */         if (!isWhitespace(this.token)) {
/* 142 */           this.lastToken = this.lcToken;
/*     */         }
/*     */       } 
/*     */       
/* 146 */       return this.result.toString();
/*     */     }
/*     */     
/*     */     private void commaAfterOn() {
/* 150 */       out();
/* 151 */       this.indent--;
/* 152 */       newline();
/* 153 */       this.afterOn = false;
/* 154 */       this.afterByOrSetOrFromOrSelect = true;
/*     */     }
/*     */     
/*     */     private void commaAfterByOrFromOrSelect() {
/* 158 */       out();
/* 159 */       newline();
/*     */     }
/*     */     
/*     */     private void logical() {
/* 163 */       if ("end".equals(this.lcToken)) {
/* 164 */         this.indent--;
/*     */       }
/* 166 */       newline();
/* 167 */       out();
/* 168 */       this.beginLine = false;
/*     */     }
/*     */     
/*     */     private void on() {
/* 172 */       this.indent++;
/* 173 */       this.afterOn = true;
/* 174 */       newline();
/* 175 */       out();
/* 176 */       this.beginLine = false;
/*     */     }
/*     */     
/*     */     private void misc() {
/* 180 */       out();
/* 181 */       if ("between".equals(this.lcToken)) {
/* 182 */         this.afterBetween = true;
/*     */       }
/* 184 */       if (this.afterInsert) {
/* 185 */         newline();
/* 186 */         this.afterInsert = false;
/*     */       } else {
/* 188 */         this.beginLine = false;
/* 189 */         if ("case".equals(this.lcToken))
/* 190 */           this.indent++; 
/*     */       } 
/*     */     }
/*     */     
/*     */     private void white() {
/* 195 */       if (!this.beginLine)
/* 196 */         this.result.append(" "); 
/*     */     }
/*     */     
/*     */     private void updateOrInsertOrDelete() {
/* 200 */       out();
/* 201 */       this.indent++;
/* 202 */       this.beginLine = false;
/* 203 */       if ("update".equals(this.lcToken)) {
/* 204 */         newline();
/*     */       }
/* 206 */       if ("insert".equals(this.lcToken))
/* 207 */         this.afterInsert = true; 
/*     */     }
/*     */     
/*     */     private void select() {
/* 211 */       out();
/* 212 */       this.indent++;
/* 213 */       newline();
/* 214 */       this.parenCounts.addLast(new Integer(this.parensSinceSelect));
/* 215 */       this.afterByOrFromOrSelects.addLast(Boolean.valueOf(this.afterByOrSetOrFromOrSelect));
/* 216 */       this.parensSinceSelect = 0;
/* 217 */       this.afterByOrSetOrFromOrSelect = true;
/*     */     }
/*     */     
/*     */     private void out() {
/* 221 */       this.result.append(this.token);
/*     */     }
/*     */     
/*     */     private void endNewClause() {
/* 225 */       if (!this.afterBeginBeforeEnd) {
/* 226 */         this.indent--;
/* 227 */         if (this.afterOn) {
/* 228 */           this.indent--;
/* 229 */           this.afterOn = false;
/*     */         } 
/* 231 */         newline();
/*     */       } 
/* 233 */       out();
/* 234 */       if (!"union".equals(this.lcToken)) {
/* 235 */         this.indent++;
/*     */       }
/* 237 */       newline();
/* 238 */       this.afterBeginBeforeEnd = false;
/* 239 */       this
/* 240 */         .afterByOrSetOrFromOrSelect = ("by".equals(this.lcToken) || "set".equals(this.lcToken) || "from".equals(this.lcToken));
/*     */     }
/*     */     
/*     */     private void beginNewClause() {
/* 244 */       if (!this.afterBeginBeforeEnd) {
/* 245 */         if (this.afterOn) {
/* 246 */           this.indent--;
/* 247 */           this.afterOn = false;
/*     */         } 
/* 249 */         this.indent--;
/* 250 */         newline();
/*     */       } 
/* 252 */       out();
/* 253 */       this.beginLine = false;
/* 254 */       this.afterBeginBeforeEnd = true;
/*     */     }
/*     */     
/*     */     private void values() {
/* 258 */       this.indent--;
/* 259 */       newline();
/* 260 */       out();
/* 261 */       this.indent++;
/* 262 */       newline();
/* 263 */       this.afterValues = true;
/*     */     }
/*     */     
/*     */     private void closeParen() {
/* 267 */       this.parensSinceSelect--;
/* 268 */       if (this.parensSinceSelect < 0) {
/* 269 */         this.indent--;
/* 270 */         this.parensSinceSelect = ((Integer)this.parenCounts.removeLast()).intValue();
/* 271 */         this.afterByOrSetOrFromOrSelect = ((Boolean)this.afterByOrFromOrSelects.removeLast()).booleanValue();
/*     */       } 
/* 273 */       if (this.inFunction > 0) {
/* 274 */         this.inFunction--;
/* 275 */         out();
/*     */       } else {
/* 277 */         if (!this.afterByOrSetOrFromOrSelect) {
/* 278 */           this.indent--;
/* 279 */           newline();
/*     */         } 
/* 281 */         out();
/*     */       } 
/* 283 */       this.beginLine = false;
/*     */     }
/*     */     
/*     */     private void openParen() {
/* 287 */       if (isFunctionName(this.lastToken) || this.inFunction > 0) {
/* 288 */         this.inFunction++;
/*     */       }
/* 290 */       this.beginLine = false;
/* 291 */       if (this.inFunction > 0) {
/* 292 */         out();
/*     */       } else {
/* 294 */         out();
/* 295 */         if (!this.afterByOrSetOrFromOrSelect) {
/* 296 */           this.indent++;
/* 297 */           newline();
/* 298 */           this.beginLine = true;
/*     */         } 
/*     */       } 
/* 301 */       this.parensSinceSelect++;
/*     */     }
/*     */     
/*     */     private static boolean isFunctionName(String param1String) {
/* 305 */       char c = param1String.charAt(0);
/* 306 */       boolean bool = (Character.isJavaIdentifierStart(c) || '"' == c) ? true : false;
/* 307 */       return (bool && !SqlFormatUtils.LOGICAL.contains(param1String) && 
/* 308 */         !SqlFormatUtils.END_CLAUSES.contains(param1String) && 
/* 309 */         !SqlFormatUtils.QUANTIFIERS.contains(param1String) && !SqlFormatUtils.DML.contains(param1String) && 
/* 310 */         !SqlFormatUtils.MISC.contains(param1String));
/*     */     }
/*     */     
/*     */     private static boolean isWhitespace(String param1String) {
/* 314 */       return (" \n\r\f\t".indexOf(param1String) >= 0);
/*     */     }
/*     */     
/*     */     private void newline() {
/* 318 */       this.result.append("NewLineSymbol");
/* 319 */       for (byte b = 0; b < this.indent; b++) {
/* 320 */         this.result.append("    ");
/*     */       }
/* 322 */       this.beginLine = true;
/*     */     }
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/SqlFormatUtils.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */