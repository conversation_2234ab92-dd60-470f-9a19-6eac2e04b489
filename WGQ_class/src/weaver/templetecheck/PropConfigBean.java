/*    */ package weaver.templetecheck;
/*    */ 
/*    */ import java.util.HashMap;
/*    */ 
/*    */ public class PropConfigBean {
/*    */   public String filepath;
/*    */   public String from;
/*    */   public String note;
/*    */   private HashMap<String, String> content;
/*    */   private HashMap<String, String> needCheckMap;
/*    */   
/*    */   public PropConfigBean(String paramString1, String paramString2, HashMap<String, String> paramHashMap, String paramString3) {
/* 13 */     this.from = paramString1;
/* 14 */     this.filepath = paramString2;
/* 15 */     this.content = paramHashMap;
/* 16 */     this.note = paramString3;
/* 17 */     this.needCheckMap = new HashMap<>();
/*    */   }
/*    */   public PropConfigBean(String paramString1, String paramString2, HashMap<String, String> paramHashMap1, HashMap<String, String> paramHashMap2, String paramString3) {
/* 20 */     this.from = paramString1;
/* 21 */     this.filepath = paramString2;
/* 22 */     this.content = paramHashMap1;
/* 23 */     this.note = paramString3;
/* 24 */     this.needCheckMap = paramHashMap2;
/*    */   }
/*    */   
/*    */   public HashMap<String, String> getContent() {
/* 28 */     return this.content;
/*    */   }
/*    */   
/*    */   public void setContent(HashMap<String, String> paramHashMap) {
/* 32 */     this.content = paramHashMap;
/*    */   }
/*    */   
/*    */   public HashMap<String, String> getNeedCheckMap() {
/* 36 */     return this.needCheckMap;
/*    */   }
/*    */   
/*    */   public void setNeedCheckMap(HashMap<String, String> paramHashMap) {
/* 40 */     this.needCheckMap = paramHashMap;
/*    */   }
/*    */   public String getFilepath() {
/* 43 */     return this.filepath;
/*    */   }
/*    */   
/*    */   public void setFilepath(String paramString) {
/* 47 */     this.filepath = paramString;
/*    */   }
/*    */   
/*    */   public String getFrom() {
/* 51 */     return this.from;
/*    */   }
/*    */   
/*    */   public void setFrom(String paramString) {
/* 55 */     this.from = paramString;
/*    */   }
/*    */   public String getNote() {
/* 58 */     return this.note;
/*    */   }
/*    */   
/*    */   public void setNote(String paramString) {
/* 62 */     this.note = paramString;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/PropConfigBean.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */