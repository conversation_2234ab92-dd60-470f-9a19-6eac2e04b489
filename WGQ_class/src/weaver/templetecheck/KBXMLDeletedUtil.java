/*    */ package weaver.templetecheck;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.List;
/*    */ import org.dom4j.Document;
/*    */ import org.dom4j.Element;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.GCONST;
/*    */ 
/*    */ 
/*    */ public class KBXMLDeletedUtil
/*    */ {
/* 14 */   private static ArrayList<HashMap<String, String>> kbXMLDeletedList = null;
/*    */ 
/*    */   
/*    */   public KBXMLDeletedUtil() {
/* 18 */     if (kbXMLDeletedList == null) {
/* 19 */       getKBXMLDeleted();
/*    */     }
/*    */   }
/*    */   
/*    */   public static ArrayList<HashMap<String, String>> getKbXMLDeletedList() {
/* 24 */     return kbXMLDeletedList;
/*    */   }
/*    */   
/*    */   public static void setKbXMLDeletedList(ArrayList<HashMap<String, String>> paramArrayList) {
/* 28 */     kbXMLDeletedList = paramArrayList;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void getKBXMLDeleted() {
/* 36 */     String str = GCONST.getRootPath() + "templetecheck/xml/deleteconfig.xml";
/* 37 */     ReadXml readXml = new ReadXml();
/* 38 */     Document document = readXml.read(str);
/* 39 */     KBVersionCompare kBVersionCompare = new KBVersionCompare();
/*    */     
/* 41 */     kbXMLDeletedList = new ArrayList<>();
/* 42 */     if (document != null) {
/* 43 */       RecordSet recordSet = new RecordSet();
/* 44 */       String str1 = "";
/* 45 */       recordSet.executeQuery("select cversion from license", new Object[0]);
/* 46 */       if (recordSet.next()) {
/* 47 */         str1 = recordSet.getString("cversion");
/*    */       }
/*    */       
/* 50 */       Element element = document.getRootElement();
/* 51 */       List list = element.elements();
/* 52 */       if (list != null)
/* 53 */         for (Element element1 : list) {
/* 54 */           HashMap<Object, Object> hashMap = new HashMap<>();
/* 55 */           String str2 = element1.elementTextTrim("filepath");
/* 56 */           String str3 = element1.elementTextTrim("key");
/* 57 */           String str4 = element1.elementTextTrim("xpath");
/* 58 */           String str5 = element1.elementTextTrim("KBVersion");
/* 59 */           String str6 = element1.elementTextTrim("content");
/*    */ 
/*    */           
/* 62 */           int i = kBVersionCompare.compareVersion(str5, str1);
/* 63 */           if (i >= 0) {
/*    */             
/* 65 */             hashMap.put("filepath", str2);
/* 66 */             hashMap.put("key", str3);
/* 67 */             hashMap.put("xpath", str4);
/* 68 */             hashMap.put("KBVersion", str5);
/* 69 */             hashMap.put("content", str6);
/*    */             
/* 71 */             kbXMLDeletedList.add(hashMap);
/*    */           } 
/*    */         }  
/*    */     } 
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/KBXMLDeletedUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */