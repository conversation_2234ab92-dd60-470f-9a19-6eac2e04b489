/*      */ package weaver.templetecheck;
/*      */ import java.io.File;
/*      */ import java.io.FileOutputStream;
/*      */ import java.text.SimpleDateFormat;
/*      */ import java.util.ArrayList;
/*      */ import java.util.Comparator;
/*      */ import java.util.Date;
/*      */ import java.util.HashMap;
/*      */ import java.util.HashSet;
/*      */ import java.util.Iterator;
/*      */ import java.util.List;
/*      */ import java.util.Map;
/*      */ import java.util.Set;
/*      */ import java.util.regex.Matcher;
/*      */ import java.util.regex.Pattern;
/*      */ import org.apache.poi.hssf.usermodel.HSSFRichTextString;
/*      */ import org.dom4j.Document;
/*      */ import org.dom4j.Element;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.conn.RecordSetTrans;
/*      */ import weaver.general.BaseBean;
/*      */ import weaver.general.GCONST;
/*      */ import weaver.general.ThreadVarLanguage;
/*      */ import weaver.general.Util;
/*      */ import weaver.hrm.User;
/*      */ import weaver.systeminfo.SystemEnv;
/*      */ import weaver.upgradetool.UpgradeFileUtil;
/*      */ 
/*      */ public class ConfigOperation extends BaseBean {
/*   30 */   SimpleDateFormat timeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
/*   31 */   SimpleDateFormat ymd = new SimpleDateFormat("yyyy-MM-dd");
/*   32 */   SimpleDateFormat hm = new SimpleDateFormat("HH:mm");
/*   33 */   CheckConfigFile checkConfigFile = new CheckConfigFile();
/*   34 */   ConfigBakUtil fileBakUtil = new ConfigBakUtil();
/*   35 */   UpgradeFileUtil fileUtil = new UpgradeFileUtil();
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<Map<String, String>> getConfigFileList(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*   47 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*   48 */     int i = Util.getIntValue(paramMap.get("pageSize"));
/*   49 */     int j = Util.getIntValue(paramMap.get("pageIndex"));
/*   50 */     String str1 = Util.null2String(paramMap.get("filename"));
/*   51 */     String str2 = Util.null2String(paramMap.get("fileinfo"));
/*   52 */     String str3 = Util.null2String(paramMap.get("kbversion"));
/*   53 */     String str4 = Util.null2String(paramMap.get("sysversion"));
/*   54 */     String str5 = "where a.isdelete=0 ";
/*      */     
/*   56 */     if (!str1.equals("")) {
/*   57 */       str5 = str5 + "and lower(a.filename) like '%" + str1.toLowerCase() + "%' ";
/*      */     }
/*   59 */     if (!str2.equals("")) {
/*   60 */       str5 = str5 + "and lower(a.fileinfo) like '%" + str2.toLowerCase() + "%' ";
/*      */     }
/*      */     
/*   63 */     if (!str3.equals("")) {
/*   64 */       str5 = str5 + "and lower(a.kbversion) like '%" + str3.toLowerCase() + "%' ";
/*      */     }
/*   66 */     if (!str4.equals("")) {
/*   67 */       str5 = str5 + "and lower(b.sysversion) like '%" + str4.toLowerCase() + "%' ";
/*      */     }
/*   69 */     String str6 = this.checkConfigFile.getCurrentUsedFileIds();
/*   70 */     if (!str6.equals("")) {
/*   71 */       str5 = str5 + " and a.id in(" + str6 + ")";
/*      */     }
/*      */     
/*   74 */     RecordSet recordSet = new RecordSet();
/*   75 */     ReadXml readXml = new ReadXml();
/*   76 */     PropertiesUtil propertiesUtil = new PropertiesUtil();
/*   77 */     String str7 = "";
/*   78 */     Document document = null;
/*   79 */     File file = null;
/*      */     
/*   81 */     String str8 = "select a.id,a.filename,a.filetype,a.filepath,a.fileinfo,a.kbversion,a.labelid,b.sysversion, CASE WHEN a.qcnumber = '' or a.qcnumber is null THEN '-' WHEN a.qcnumber = null THEN '-' ELSE a.qcnumber END as qcnumber from configFileManager a left join CustomerKBVersion b on a.kbversion = b.name " + str5 + " order by  CASE WHEN a.labelid = '' or a.labelid is null THEN '0'  ELSE '1' END  asc,a.labelid desc,a.id desc ";
/*      */ 
/*      */     
/*   84 */     recordSet.execute(str8);
/*      */     
/*   86 */     while (recordSet.next()) {
/*   87 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*   88 */       hashMap.put("id", Util.null2String(recordSet.getString("id")));
/*   89 */       hashMap.put("filename", Util.null2String(recordSet.getString("filename")));
/*   90 */       hashMap.put("filepath", Util.null2String(recordSet.getString("filepath")));
/*   91 */       hashMap.put("fileinfo", Util.null2String(recordSet.getString("fileinfo")));
/*   92 */       hashMap.put("kbversion", Util.null2String(recordSet.getString("kbversion")));
/*   93 */       hashMap.put("labelid", Util.null2String(recordSet.getString("labelid")));
/*      */       
/*   95 */       hashMap.put("sysversion", Util.null2String(recordSet.getString("sysversion")));
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  101 */       hashMap.put("filetype", Util.null2String(recordSet.getString("filetype")));
/*  102 */       hashMap.put("qcnumber", Util.null2String(recordSet.getString("qcnumber")));
/*  103 */       hashMap.put("isSystemConfig", Util.null2String(recordSet.getString("labelid")).equals("") ? (SystemEnv.getHtmlLabelName(30587, ThreadVarLanguage.getLang()) + "") : (SystemEnv.getHtmlLabelName(163, ThreadVarLanguage.getLang()) + ""));
/*      */       
/*  105 */       arrayList.add(hashMap);
/*      */     } 
/*      */ 
/*      */ 
/*      */     
/*  110 */     sortByVersion((List)arrayList);
/*      */     
/*  112 */     for (byte b = 0; b < arrayList.size(); b++) {
/*      */       
/*  114 */       Map<String, String> map = arrayList.get(b);
/*      */       
/*  116 */       if (i <= 0 || j <= 0 || (b >= i * (j - 1) && b <= i * j)) {
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  121 */         HashMap<Object, Object> hashMap = new HashMap<>();
/*  122 */         hashMap.put("checktype", Util.null2String((String)map.get("filetype")));
/*  123 */         hashMap.put("ids", Util.null2String((String)map.get("id")));
/*  124 */         hashMap.put("requisite", "1");
/*  125 */         ArrayList<ConfigDetail> arrayList1 = this.checkConfigFile.getConfigDetailList((Map)hashMap);
/*  126 */         boolean bool = true;
/*      */ 
/*      */ 
/*      */         
/*  130 */         if (arrayList1 != null && arrayList1.size() > 0) {
/*  131 */           for (ConfigDetail configDetail : arrayList1) {
/*  132 */             if (configDetail != null) {
/*  133 */               String str9 = configDetail.getType();
/*  134 */               String str10 = configDetail.getFilepath();
/*  135 */               boolean bool1 = str7.equals(str10);
/*  136 */               boolean bool2 = false;
/*  137 */               String str11 = String.valueOf(configDetail.getDetailid());
/*      */               
/*  139 */               if ("1".equals(str9)) {
/*  140 */                 String str12 = configDetail.getKeyname();
/*  141 */                 String str13 = configDetail.getValue();
/*  142 */                 file = new File(this.fileUtil.getPath(GCONST.getRootPath() + str10));
/*  143 */                 if (!file.exists()) {
/*  144 */                   bool2 = false;
/*      */                 } else {
/*  146 */                   if (!bool1) {
/*  147 */                     String str14 = str10;
/*  148 */                     if (str10.startsWith("\\")) {
/*  149 */                       str10 = str10.substring(1, str10.length());
/*      */                     }
/*  151 */                     propertiesUtil = new PropertiesUtil();
/*  152 */                     propertiesUtil.load(GCONST.getRootPath() + str10);
/*      */                     
/*  154 */                     str7 = str14;
/*      */                   } 
/*  156 */                   String str = this.checkConfigFile.checkPropertiesFile(propertiesUtil, str10, str12, str13, str11, str6);
/*  157 */                   if ("ok".equals(str) || "older".equals(str)) {
/*  158 */                     bool2 = true;
/*      */                   }
/*      */                 } 
/*      */               } else {
/*      */ 
/*      */                 
/*      */                 try {
/*  165 */                   String str = str10;
/*  166 */                   if (!bool1) {
/*  167 */                     if (str10.startsWith("\\")) {
/*  168 */                       str10 = str10.substring(1, str10.length());
/*      */                     }
/*  170 */                     file = new File(this.fileUtil.getPath(GCONST.getRootPath() + str10));
/*  171 */                     str7 = str;
/*  172 */                     readXml = new ReadXml();
/*  173 */                     document = readXml.read(GCONST.getRootPath() + str10);
/*      */                   } 
/*      */                   
/*  176 */                   if (null != file) {
/*  177 */                     String str12 = configDetail.getXpath();
/*  178 */                     String str13 = configDetail.getValue();
/*  179 */                     if (str13 != null && document != null) {
/*  180 */                       String str14 = this.checkConfigFile.checkXmlFile(str12, document, str13, str11, str6);
/*  181 */                       if ("ok".equals(str14) || "older".equals(str14)) {
/*  182 */                         bool2 = true;
/*      */                       }
/*      */                     } else {
/*  185 */                       bool2 = false;
/*      */                     } 
/*      */                   } 
/*  188 */                 } catch (Exception exception) {
/*  189 */                   bool2 = false;
/*  190 */                   exception.printStackTrace();
/*      */                 } 
/*      */               } 
/*  193 */               if (!bool2) {
/*  194 */                 bool = bool2;
/*      */                 
/*      */                 break;
/*      */               } 
/*      */             } 
/*      */           } 
/*      */         }
/*  201 */         map.put("isconfiged", bool ? (SystemEnv.getHtmlLabelName(163, ThreadVarLanguage.getLang()) + "") : (SystemEnv.getHtmlLabelName(30587, ThreadVarLanguage.getLang()) + ""));
/*      */       } 
/*      */     } 
/*  204 */     return (List)arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean checkConfigIsConfig(String paramString) {
/*  215 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  216 */     boolean bool = true;
/*  217 */     String str1 = "where a.isdelete=0 ";
/*      */     
/*  219 */     if (paramString == null || "".equals(paramString)) {
/*  220 */       return true;
/*      */     }
/*  222 */     if (paramString.startsWith(",")) {
/*  223 */       paramString = paramString.substring(1);
/*      */     }
/*      */     
/*  226 */     if (paramString.endsWith(",")) {
/*  227 */       paramString = paramString.substring(0, paramString.length() - 1);
/*      */     }
/*      */     
/*  230 */     String str2 = paramString;
/*  231 */     if (!str2.equals("")) {
/*  232 */       str1 = str1 + " and a.id in(" + str2 + ")";
/*      */     }
/*      */     
/*  235 */     RecordSet recordSet = new RecordSet();
/*  236 */     ReadXml readXml = new ReadXml();
/*  237 */     PropertiesUtil propertiesUtil = new PropertiesUtil();
/*  238 */     String str3 = "";
/*  239 */     Document document = null;
/*  240 */     File file = null;
/*      */     
/*  242 */     String str4 = "select a.id,a.filename,a.filetype,a.filepath,a.fileinfo,a.kbversion,a.labelid,b.sysversion, CASE WHEN a.qcnumber = '' or a.qcnumber is null THEN '-' WHEN a.qcnumber = null THEN '-' ELSE a.qcnumber END as qcnumber from configFileManager a left join CustomerKBVersion b on a.kbversion = b.name " + str1 + " order by  CASE WHEN a.labelid = '' or a.labelid is null THEN '0'  ELSE '1' END  asc,a.labelid desc,a.id desc ";
/*      */ 
/*      */     
/*  245 */     recordSet.execute(str4);
/*      */     
/*  247 */     while (recordSet.next()) {
/*  248 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*  249 */       hashMap.put("id", Util.null2String(recordSet.getString("id")));
/*  250 */       hashMap.put("filename", Util.null2String(recordSet.getString("filename")));
/*  251 */       hashMap.put("filepath", Util.null2String(recordSet.getString("filepath")));
/*  252 */       hashMap.put("fileinfo", Util.null2String(recordSet.getString("fileinfo")));
/*  253 */       hashMap.put("kbversion", Util.null2String(recordSet.getString("kbversion")));
/*  254 */       hashMap.put("labelid", Util.null2String(recordSet.getString("labelid")));
/*      */       
/*  256 */       hashMap.put("sysversion", Util.null2String(recordSet.getString("sysversion")));
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  262 */       hashMap.put("filetype", Util.null2String(recordSet.getString("filetype")));
/*  263 */       hashMap.put("qcnumber", Util.null2String(recordSet.getString("qcnumber")));
/*  264 */       hashMap.put("isSystemConfig", Util.null2String(recordSet.getString("labelid")).equals("") ? "否" : "是");
/*      */       
/*  266 */       arrayList.add(hashMap);
/*      */     } 
/*      */ 
/*      */ 
/*      */     
/*  271 */     sortByVersion((List)arrayList);
/*      */     
/*  273 */     for (byte b = 0; b < arrayList.size(); b++) {
/*      */       
/*  275 */       Map map = arrayList.get(b);
/*      */       
/*  277 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*  278 */       hashMap.put("checktype", Util.null2String((String)map.get("filetype")));
/*  279 */       hashMap.put("ids", Util.null2String((String)map.get("id")));
/*  280 */       hashMap.put("requisite", "1");
/*  281 */       ArrayList<ConfigDetail> arrayList1 = this.checkConfigFile.getConfigDetailList((Map)hashMap);
/*  282 */       boolean bool1 = true;
/*      */ 
/*      */       
/*  285 */       if (arrayList1 != null && arrayList1.size() > 0) {
/*  286 */         for (ConfigDetail configDetail : arrayList1) {
/*  287 */           if (configDetail != null) {
/*  288 */             String str5 = configDetail.getType();
/*  289 */             String str6 = configDetail.getFilepath();
/*  290 */             boolean bool2 = str3.equals(str6);
/*  291 */             boolean bool3 = false;
/*  292 */             String str7 = String.valueOf(configDetail.getDetailid());
/*      */             
/*  294 */             if ("1".equals(str5)) {
/*  295 */               String str8 = configDetail.getKeyname();
/*  296 */               String str9 = configDetail.getValue();
/*  297 */               file = new File(this.fileUtil.getPath(GCONST.getRootPath() + str6));
/*  298 */               if (!file.exists()) {
/*  299 */                 bool3 = false;
/*  300 */                 bool = false;
/*      */                 break;
/*      */               } 
/*  303 */               if (!bool2) {
/*  304 */                 String str = str6;
/*  305 */                 if (str6.startsWith("\\")) {
/*  306 */                   str6 = str6.substring(1, str6.length());
/*      */                 }
/*  308 */                 propertiesUtil = new PropertiesUtil();
/*  309 */                 propertiesUtil.load(GCONST.getRootPath() + str6);
/*      */                 
/*  311 */                 str3 = str;
/*      */               } 
/*  313 */               String str10 = this.checkConfigFile.checkPropertiesFile(propertiesUtil, str6, str8, str9, str7, str2);
/*  314 */               if ("ok".equals(str10) || "older".equals(str10)) {
/*  315 */                 bool3 = true;
/*      */               }
/*      */             } else {
/*      */ 
/*      */               
/*      */               try {
/*      */                 
/*  322 */                 String str = str6;
/*  323 */                 if (!bool2) {
/*  324 */                   if (str6.startsWith("\\")) {
/*  325 */                     str6 = str6.substring(1, str6.length());
/*      */                   }
/*  327 */                   file = new File(this.fileUtil.getPath(GCONST.getRootPath() + str6));
/*  328 */                   str3 = str;
/*  329 */                   readXml = new ReadXml();
/*  330 */                   document = readXml.read(GCONST.getRootPath() + str6);
/*      */                 } 
/*      */                 
/*  333 */                 if (null != file) {
/*  334 */                   String str8 = configDetail.getXpath();
/*  335 */                   String str9 = configDetail.getValue();
/*  336 */                   if (str9 != null && document != null) {
/*  337 */                     String str10 = this.checkConfigFile.checkXmlFile(str8, document, str9, str7, str2);
/*  338 */                     if ("ok".equals(str10) || "older".equals(str10)) {
/*  339 */                       bool3 = true;
/*      */                     }
/*      */                   } else {
/*  342 */                     bool3 = false;
/*  343 */                     bool = false;
/*      */                     break;
/*      */                   } 
/*      */                 } 
/*  347 */               } catch (Exception exception) {
/*  348 */                 exception.printStackTrace();
/*  349 */                 bool3 = false;
/*  350 */                 bool = false;
/*      */                 break;
/*      */               } 
/*      */             } 
/*  354 */             if (!bool3) {
/*  355 */               bool1 = bool3;
/*  356 */               bool = false;
/*      */               break;
/*      */             } 
/*      */           } 
/*      */         } 
/*      */       }
/*  362 */       if (!bool) {
/*      */         break;
/*      */       }
/*      */     } 
/*  366 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void sortByVersion(List<Map<String, String>> paramList) {
/*  375 */     if (paramList != null) {
/*  376 */       final KBVersionCompare compare = new KBVersionCompare();
/*      */       
/*  378 */       Collections.sort(paramList, new Comparator<Map<String, String>>()
/*      */           {
/*      */             public int compare(Map<String, String> param1Map1, Map<String, String> param1Map2) {
/*  381 */               String str1 = (String)param1Map1.get("sysversion") + "+" + (String)param1Map1.get("kbversion");
/*  382 */               String str2 = (String)param1Map2.get("sysversion") + "+" + (String)param1Map2.get("kbversion");
/*      */               
/*  384 */               if (param1Map1.get("sysversion") == null || "null".equals(param1Map1.get("sysversion"))) {
/*  385 */                 return 1;
/*      */               }
/*      */               
/*  388 */               if (param1Map2.get("sysversion") == null || "null".equals(param1Map2.get("sysversion"))) {
/*  389 */                 return -1;
/*      */               }
/*      */               
/*  392 */               return compare.compareVersion(str1, str2);
/*      */             }
/*      */           });
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getConfigDetail(String paramString1, String paramString2) {
/*  406 */     RecordSet recordSet = new RecordSet();
/*  407 */     String str = "";
/*  408 */     StringBuilder stringBuilder = new StringBuilder();
/*  409 */     if ("1".equals(paramString2)) {
/*  410 */       str = "select * from configPropertiesFile where (isdelete='0' or isdelete is null ) and configfileid = " + paramString1;
/*  411 */       recordSet.execute(str);
/*  412 */       while (recordSet.next()) {
/*      */ 
/*      */ 
/*      */         
/*  416 */         String str1 = Util.null2String(recordSet.getString("attrname"));
/*  417 */         String str2 = Util.null2String(recordSet.getString("attrvalue"));
/*  418 */         String str3 = Util.null2String(recordSet.getString("attrnotes"));
/*  419 */         String str4 = Util.null2String(recordSet.getString("issystem"));
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  424 */         String str5 = "1".equals(str4) ? ("==== " + SystemEnv.getHtmlLabelName(524816, ThreadVarLanguage.getLang())) : "";
/*  425 */         stringBuilder.append(new HSSFRichTextString(str3 + "  " + str5 + "\r\n"));
/*  426 */         stringBuilder.append(new HSSFRichTextString(str1 + " : " + str2 + "\r\n\r\n"));
/*      */       } 
/*  428 */     } else if ("2".equals(paramString2)) {
/*  429 */       str = "select * from configXmlFile where (isdelete ='0' or isdelete is null) and configfileid = " + paramString1;
/*  430 */       recordSet.execute(str);
/*  431 */       while (recordSet.next()) {
/*      */ 
/*      */ 
/*      */         
/*  435 */         String str1 = Util.null2String(recordSet.getString("attrvalue"));
/*  436 */         String str2 = Util.null2String(recordSet.getString("attrnotes"));
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  441 */         String str3 = Util.null2String(recordSet.getString("issystem"));
/*  442 */         String str4 = "1".equals(str3) ? ("==== " + SystemEnv.getHtmlLabelName(524816, ThreadVarLanguage.getLang())) : "";
/*  443 */         stringBuilder.append(new HSSFRichTextString(str2 + "  " + str4 + "\r\n"));
/*  444 */         stringBuilder.append(new HSSFRichTextString(str1 + "\r\n\r\n"));
/*      */       } 
/*      */     } 
/*  447 */     return stringBuilder.toString();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean saveProperties(String paramString, String[] paramArrayOfString1, String[] paramArrayOfString2, String[] paramArrayOfString3, String[] paramArrayOfString4, String[] paramArrayOfString5) {
/*  461 */     if (paramString.equals("") || paramString == null || paramArrayOfString1.length <= 0) {
/*  462 */       return false;
/*      */     }
/*  464 */     String str = "";
/*  465 */     for (String str1 : paramArrayOfString1) {
/*  466 */       if (!str1.equals("")) {
/*  467 */         str = str + (str.equals("") ? str1 : ("," + str1));
/*      */       }
/*      */     } 
/*  470 */     RecordSetTrans recordSetTrans = new RecordSetTrans();
/*  471 */     recordSetTrans.setAutoCommit(false);
/*      */     
/*      */     try {
/*  474 */       if (!str.equals("")) {
/*  475 */         recordSetTrans.execute("delete from configPropertiesFile where issystem = 0 and configfileid=" + paramString + " and id not in (" + str + ")");
/*      */       } else {
/*  477 */         recordSetTrans.execute("delete from configPropertiesFile where issystem = 0 and  configfileid=" + paramString);
/*      */       } 
/*      */       
/*  480 */       for (byte b = 0; b < paramArrayOfString1.length; b++) {
/*  481 */         String str1 = "";
/*  482 */         String str2 = (paramArrayOfString2[b] == null) ? "" : paramArrayOfString2[b].replace("'", "''");
/*  483 */         String str3 = (paramArrayOfString3[b] == null) ? "" : paramArrayOfString3[b].replaceAll("'", "''");
/*  484 */         String str4 = (paramArrayOfString4[b] == null) ? "" : paramArrayOfString4[b].replaceAll("'", "''");
/*  485 */         Date date = new Date();
/*  486 */         if (paramArrayOfString1[b] == null || paramArrayOfString1[b].equals("")) {
/*  487 */           str1 = "insert into configPropertiesFile  (configfileid,attrname,attrvalue,attrnotes,createdate,createtime,issystem,requisite,isdelete) values ('" + paramString + "','" + str2 + "','" + str3 + "','" + str4 + "','" + this.ymd.format(date) + "','" + this.hm.format(date) + "',0," + paramArrayOfString5[b] + ",0)";
/*      */         }
/*      */         else {
/*      */           
/*  491 */           str1 = "update configPropertiesFile set attrname='" + str2 + "',attrvalue='" + str3 + "',attrnotes='" + str4 + "',createdate= '" + this.ymd.format(date) + "',createtime= '" + this.hm.format(date) + "',requisite='" + paramArrayOfString5[b] + "' where id = " + paramArrayOfString1[b] + " and (issystem!=1 or (issystem=1 and requisite = 0))";
/*      */         } 
/*      */         
/*  494 */         if (!str1.equals("")) {
/*  495 */           recordSetTrans.execute(str1);
/*      */         }
/*      */       } 
/*  498 */     } catch (Exception exception) {
/*  499 */       exception.printStackTrace();
/*  500 */       recordSetTrans.rollback();
/*  501 */       return false;
/*      */     } 
/*  503 */     recordSetTrans.commit();
/*  504 */     return true;
/*      */   }
/*      */ 
/*      */   
/*      */   public boolean checkXmlValue(String paramString) {
/*      */     try {
/*  510 */       paramString = "<myroot>" + paramString + "</myroot>";
/*  511 */       Document document = this.checkConfigFile.Str2Document(paramString);
/*  512 */       if (document == null) {
/*  513 */         return false;
/*      */       }
/*  515 */     } catch (Exception exception) {
/*  516 */       exception.printStackTrace();
/*  517 */       return false;
/*      */     } 
/*  519 */     return true;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean saveXml(String paramString, String[] paramArrayOfString1, String[] paramArrayOfString2, String[] paramArrayOfString3, String[] paramArrayOfString4, String[] paramArrayOfString5) {
/*  532 */     if (paramString.equals("") || paramString == null) {
/*  533 */       return false;
/*      */     }
/*      */     
/*  536 */     if (paramArrayOfString1.length <= 0) {
/*  537 */       return false;
/*      */     }
/*  539 */     String str = "";
/*  540 */     for (String str1 : paramArrayOfString1) {
/*  541 */       if (str1 != null && !str1.equals("")) {
/*  542 */         str = str + (str.equals("") ? str1 : ("," + str1));
/*      */       }
/*      */     } 
/*  545 */     RecordSetTrans recordSetTrans = new RecordSetTrans();
/*  546 */     recordSetTrans.setAutoCommit(false);
/*      */     
/*      */     try {
/*  549 */       if (str != null && !str.equals("")) {
/*  550 */         recordSetTrans.execute("delete from configXmlFile where issystem = 0 and  configfileid=" + paramString + " and id not in (" + str + ")");
/*      */       } else {
/*  552 */         recordSetTrans.execute("delete from configXmlFile where issystem = 0 and  configfileid=" + paramString);
/*      */       } 
/*      */       
/*  555 */       for (byte b = 0; b < paramArrayOfString1.length; b++) {
/*  556 */         String str1 = "";
/*  557 */         String str2 = (paramArrayOfString3[b] == null) ? "" : paramArrayOfString3[b].replace("'", "''");
/*  558 */         String str3 = (paramArrayOfString4[b] == null) ? "" : paramArrayOfString4[b].replace("'", "''");
/*  559 */         String str4 = (paramArrayOfString2[b] == null) ? "" : paramArrayOfString2[b].replace("'", "''");
/*  560 */         Date date = new Date();
/*  561 */         if (paramArrayOfString1[b] == null || paramArrayOfString1[b].equals("")) {
/*  562 */           str1 = "insert into configXmlFile  (configfileid,attrvalue,xpath,attrnotes,createdate,createtime,issystem,requisite,isdelete) values ('" + paramString + "','" + str4 + "','" + str2 + "','" + str3 + "','" + this.ymd.format(date) + "','" + this.hm.format(date) + "',0," + paramArrayOfString5[b] + ",'0')";
/*      */         
/*      */         }
/*      */         else {
/*      */ 
/*      */           
/*  568 */           str1 = "update configXmlFile set attrvalue='" + str4 + "',xpath='" + str2 + "',attrnotes='" + str3 + "',createdate= '" + this.ymd.format(date) + "',createtime= '" + this.hm.format(date) + "',requisite='" + paramArrayOfString5[b] + "'where issystem = 0 and id = " + paramArrayOfString1[b];
/*      */         } 
/*      */ 
/*      */ 
/*      */         
/*  573 */         Document document = this.checkConfigFile.Str2Document("<myroot>" + paramArrayOfString2[b] + "</myroot>");
/*      */ 
/*      */ 
/*      */         
/*  577 */         if (!str1.equals("")) {
/*  578 */           recordSetTrans.execute(str1);
/*      */         }
/*      */       } 
/*  581 */     } catch (Exception exception) {
/*  582 */       exception.printStackTrace();
/*  583 */       recordSetTrans.rollback();
/*  584 */       return false;
/*      */     } 
/*  586 */     recordSetTrans.commit();
/*  587 */     return true;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getDelIds(String paramString) {
/*  598 */     String str1 = "";
/*  599 */     if (paramString == null || paramString.equals("") || paramString.replaceAll(",", "").equals("")) {
/*  600 */       return "";
/*      */     }
/*  602 */     if (paramString.endsWith(",")) {
/*  603 */       paramString = paramString.substring(0, paramString.length() - 1);
/*      */     }
/*  605 */     RecordSet recordSet = new RecordSet();
/*  606 */     String str2 = "select * from configFileManager where isdelete=0 and id in(" + paramString + ") and (labelid is null or labelid='' or labelid = 0)";
/*  607 */     recordSet.execute(str2);
/*  608 */     while (recordSet.next()) {
/*  609 */       str1 = str1 + Util.null2String(recordSet.getString("id")) + ",";
/*      */     }
/*  611 */     return str1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean deleteConfig(String paramString) {
/*  621 */     if (paramString == null || paramString.equals("") || paramString.replaceAll(",", "").equals("")) {
/*  622 */       return false;
/*      */     }
/*  624 */     if (paramString.substring(0, 1).equals(",")) {
/*  625 */       paramString = paramString.substring(1, paramString.length() + 1);
/*      */     }
/*  627 */     if (paramString.endsWith(",")) {
/*  628 */       paramString = paramString.substring(0, paramString.length() - 1);
/*      */     }
/*  630 */     writeLog("需要删除的manager id:" + paramString);
/*  631 */     List<String> list = Arrays.asList(paramString.split(","));
/*  632 */     if (list == null)
/*  633 */       return false; 
/*  634 */     RecordSetTrans recordSetTrans = new RecordSetTrans();
/*  635 */     recordSetTrans.setAutoCommit(false);
/*      */     try {
/*  637 */       for (String str1 : list) {
/*  638 */         String str2 = "select * from configFileManager where isdelete=0 and id =" + str1;
/*  639 */         recordSetTrans.execute(str2);
/*  640 */         if (recordSetTrans.next()) {
/*  641 */           String str3 = Util.null2String(recordSetTrans.getString("labelid"));
/*  642 */           int i = Util.getIntValue(recordSetTrans.getString("filetype"));
/*  643 */           if (!str3.replace(" ", "").equals("") && !str3.equals("0")) {
/*  644 */             writeLog("系统必须配置的文件无法删除！");
/*      */             
/*      */             continue;
/*      */           } 
/*  648 */           String str4 = "";
/*  649 */           if (i == 0) {
/*  650 */             str4 = "delete from configPropertiesFile where configfileid = " + str1;
/*  651 */           } else if (i == 1) {
/*  652 */             str4 = "delete from configXmlFile where configfileid = " + str1;
/*      */           } 
/*      */           
/*  655 */           if (!str4.equals("")) {
/*  656 */             recordSetTrans.execute(str4);
/*      */           }
/*  658 */           recordSetTrans.execute("delete from configFileManager where isdelete=0 and id =" + str1);
/*      */         } 
/*      */       } 
/*  661 */       recordSetTrans.commit();
/*  662 */       return true;
/*  663 */     } catch (Exception exception) {
/*  664 */       recordSetTrans.rollback();
/*  665 */       exception.printStackTrace();
/*  666 */       return false;
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean deletePropDetail(String paramString) {
/*  677 */     if (paramString == null || paramString.equals("") || paramString.replaceAll(",", "").equals("")) {
/*  678 */       return false;
/*      */     }
/*      */     
/*  681 */     RecordSet recordSet = new RecordSet();
/*  682 */     String str = "delete from configPropertiesFile where issystem = 0 and  configfileid = " + paramString;
/*      */     try {
/*  684 */       return recordSet.execute(str);
/*  685 */     } catch (Exception exception) {
/*  686 */       exception.printStackTrace();
/*  687 */       return false;
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean deleteXmlDetail(String paramString) {
/*  697 */     if (paramString == null || paramString.equals("") || paramString.replaceAll(",", "").equals("")) {
/*  698 */       return false;
/*      */     }
/*      */     
/*  701 */     RecordSet recordSet = new RecordSet();
/*  702 */     String str = "delete from configXmlFile where issystem = 0 and  configfileid = " + paramString;
/*      */     try {
/*  704 */       return recordSet.execute(str);
/*  705 */     } catch (Exception exception) {
/*  706 */       exception.printStackTrace();
/*  707 */       return false;
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean addConfig(String paramString1, String paramString2, String paramString3, String paramString4, int paramInt, String paramString5) {
/*  723 */     if (paramString1.equals("") || paramString2.equals("")) {
/*  724 */       return false;
/*      */     }
/*  726 */     paramString3 = paramString3.replace("'", "''");
/*  727 */     Date date = new Date();
/*  728 */     String str1 = this.ymd.format(date);
/*  729 */     String str2 = this.hm.format(date);
/*      */     try {
/*  731 */       RecordSet recordSet = new RecordSet();
/*  732 */       recordSet.execute("select * from license");
/*  733 */       if (recordSet.next()) {
/*  734 */         String str3 = Util.null2String(recordSet.getString("cversion"));
/*  735 */         String str4 = "";
/*  736 */         String str5 = "";
/*  737 */         if (str3.contains("+")) {
/*  738 */           str4 = str3.substring(str3.indexOf("+") + 1, str3.length());
/*  739 */           str5 = str3.substring(0, str3.indexOf("+"));
/*  740 */         } else if (!str3.equals("")) {
/*  741 */           str4 = str3;
/*  742 */           str5 = str3;
/*      */         } 
/*      */         
/*  745 */         if (paramString4.equals(str4)) {
/*      */           
/*  747 */           RecordSet recordSet1 = new RecordSet();
/*  748 */           recordSet1.execute("select * from CustomerKBVersion where name='" + str4 + "'");
/*  749 */           if (!recordSet1.next()) {
/*  750 */             recordSet1.execute("insert into CustomerKBVersion (name,sysversion) values('" + str4 + "','" + str5 + "')");
/*      */           }
/*  752 */           recordSet1.execute("select * from CustomerSysVersion where name='" + str5 + "'");
/*  753 */           if (!recordSet1.next()) {
/*  754 */             recordSet1.execute("insert into CustomerSysVersion (name) values('" + str5 + "')");
/*      */           }
/*      */         } 
/*      */       } 
/*      */       
/*  759 */       String str = "insert into configFileManager (filename,filetype,filepath,kbversion,fileinfo,createdate,createtime,isdelete) values('" + paramString1 + "'," + paramInt + ",'" + paramString2 + "','" + paramString4 + "','" + paramString3 + "','" + str1 + "','" + str2 + "','0')";
/*  760 */       return recordSet.execute(str);
/*  761 */     } catch (Exception exception) {
/*  762 */       exception.printStackTrace();
/*  763 */       return false;
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean editConfig(int paramInt1, String paramString1, String paramString2, String paramString3, String paramString4, int paramInt2, String paramString5) {
/*  782 */     if (paramString1.equals("") || paramString2.equals("") || paramString3.equals("")) {
/*  783 */       return false;
/*      */     }
/*  785 */     paramString3 = paramString3.replace("'", "''");
/*  786 */     Date date = new Date();
/*  787 */     String str1 = this.ymd.format(date);
/*  788 */     String str2 = this.hm.format(date);
/*      */     try {
/*  790 */       RecordSet recordSet = new RecordSet();
/*      */       
/*  792 */       String str = "update configFileManager set filename='" + paramString1 + "',filetype=" + paramInt2 + ",filepath='" + paramString2 + "',kbversion='" + paramString4 + "',fileinfo='" + paramString3 + "',createdate='" + str1 + "',createtime='" + str2 + "' where id =" + paramInt1;
/*      */       
/*  794 */       return recordSet.execute(str);
/*  795 */     } catch (Exception exception) {
/*  796 */       exception.printStackTrace();
/*  797 */       return false;
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String oneKeyConfig(String paramString, HashSet<String> paramHashSet) {
/*  809 */     RecordSet recordSet = new RecordSet();
/*  810 */     if (paramHashSet == null) {
/*  811 */       paramHashSet = new HashSet<>();
/*      */     }
/*      */     
/*  814 */     ConfigSpecialHandle configSpecialHandle = new ConfigSpecialHandle();
/*  815 */     configSpecialHandle.deleteXMLConfig_DB();
/*  816 */     String str1 = "select a.id,a.filename,a.filetype,a.filepath,a.fileinfo,a.kbversion,a.labelid,a.qcnumber,case when a.filename='web.xml' then 1 else 2 end as temporder  from configFileManager a  where a.isdelete=0 and  a.id in(" + paramString + ") order by temporder desc";
/*      */ 
/*      */     
/*  819 */     recordSet.execute(str1);
/*  820 */     String str2 = "";
/*  821 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  822 */     ReadXml readXml = new ReadXml();
/*  823 */     FileOutputStream fileOutputStream = null;
/*      */     
/*  825 */     while (recordSet.next()) {
/*      */       
/*  827 */       String str3 = Util.null2String(recordSet.getString("filename"));
/*  828 */       String str4 = Util.null2String(recordSet.getString("filepath"));
/*  829 */       if (str4 != null) {
/*  830 */         str4 = str4.replace("\\", "/");
/*      */       }
/*      */       
/*  833 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  834 */       hashMap1.put("checktype", Util.null2String(recordSet.getString("filetype")));
/*  835 */       hashMap1.put("ids", Util.null2String(recordSet.getString("id")));
/*      */       
/*  837 */       hashMap1.put("requisite", "1");
/*  838 */       ArrayList<ConfigDetail> arrayList = this.checkConfigFile.getConfigDetailList((Map)hashMap1);
/*      */       
/*  840 */       String str5 = "";
/*  841 */       PropertiesUtil propertiesUtil = new PropertiesUtil();
/*  842 */       if (arrayList == null || arrayList.size() <= 0) {
/*      */         continue;
/*      */       }
/*      */       
/*  846 */       String str6 = GCONST.getRootPath() + str4;
/*  847 */       String str7 = this.fileBakUtil.getBakRootPath() + str4;
/*  848 */       this.fileBakUtil.copyFile(str6, str7);
/*  849 */       String str8 = this.checkConfigFile.getCurrentUsedFileIds();
/*      */       
/*  851 */       for (byte b = 0; b < arrayList.size(); b++) {
/*  852 */         ConfigDetail configDetail = arrayList.get(b);
/*  853 */         Document document = null;
/*  854 */         File file = null;
/*      */         
/*  856 */         XMLWriter xMLWriter = null;
/*  857 */         boolean bool = true;
/*  858 */         RecordSet recordSet1 = new RecordSet();
/*      */       } 
/*      */     } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/* 1003 */     Set set = hashMap.keySet();
/* 1004 */     Iterator<String> iterator = set.iterator();
/* 1005 */     selectXmlNodeUtil selectXmlNodeUtil = new selectXmlNodeUtil();
/*      */     
/* 1007 */     while (iterator.hasNext()) {
/* 1008 */       String str3 = "";
/* 1009 */       String str4 = iterator.next();
/* 1010 */       if (str4.indexOf("/") > -1) {
/* 1011 */         int i = str4.lastIndexOf("/") + 1;
/* 1012 */         str3 = str4.substring(i);
/*      */       } else {
/* 1014 */         str3 = str4;
/*      */       } 
/*      */       
/* 1017 */       if (paramHashSet != null && paramHashSet.contains(str3)) {
/* 1018 */         Document document = (Document)hashMap.get(str4);
/* 1019 */         configSpecialHandle.deleteXMLConfig_File(document, str4);
/*      */         
/* 1021 */         document = selectXmlNodeUtil.sortElement(document);
/*      */         
/* 1023 */         selectXmlNodeUtil.writeXml(this.fileUtil.getPath(GCONST.getRootPath() + str4), document);
/*      */       } 
/*      */     } 
/*      */ 
/*      */     
/* 1028 */     hashMap = new HashMap<>();
/* 1029 */     if (!str2.equals("") && str2.endsWith(",")) {
/* 1030 */       str2 = str2.substring(0, str2.length() - 1);
/*      */     }
/*      */     
/* 1033 */     return str2;
/*      */   }
/*      */ 
/*      */   
/*      */   public String getConfigColor(String paramString1, String paramString2) {
/* 1038 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/* 1039 */     String str1 = "";
/* 1040 */     String str2 = "";
/* 1041 */     if (arrayOfString.length == 2) {
/* 1042 */       str1 = arrayOfString[0];
/* 1043 */       str2 = arrayOfString[1];
/*      */     } 
/* 1045 */     if (!"".equals(str1) && str1.equals(SystemEnv.getHtmlLabelName(30587, ThreadVarLanguage.getLang()) + ""))
/*      */     {
/* 1047 */       str2 = "<span style='color:red'>" + str2 + "</span>";
/*      */     }
/* 1049 */     return str2;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void generateOrderRule(String paramString1, String paramString2, String paramString3) {
/* 1060 */     writeLog("###开始生成配置顺序规则...###");
/* 1061 */     PropertiesUtil propertiesUtil = new PropertiesUtil();
/* 1062 */     String str1 = GCONST.getRootPath() + "templetecheck" + File.separatorChar + "xml" + File.separatorChar + "condition.properties";
/* 1063 */     propertiesUtil.load(str1);
/* 1064 */     String str2 = getPattern(paramString3);
/* 1065 */     Pattern pattern = Pattern.compile(str2);
/*      */     
/* 1067 */     Matcher matcher = pattern.matcher(paramString3);
/* 1068 */     String str3 = "";
/* 1069 */     if (matcher.find()) {
/* 1070 */       str3 = matcher.group(1);
/*      */     }
/*      */ 
/*      */     
/* 1074 */     XMLUtil xMLUtil = new XMLUtil();
/* 1075 */     Document document = xMLUtil.Str2Document("<myroot>" + paramString2 + "</myroot>");
/* 1076 */     List<Element> list = document.getRootElement().elements();
/* 1077 */     String str4 = "";
/* 1078 */     if (propertiesUtil.containsKey(str3)) {
/* 1079 */       str4 = propertiesUtil.get(str3);
/*      */     }
/* 1081 */     for (byte b = 0; b < list.size(); b++) {
/* 1082 */       str2 = getPattern(((Element)list.get(b)).asXML());
/*      */       
/* 1084 */       pattern = Pattern.compile(str2);
/*      */       
/* 1086 */       matcher = pattern.matcher(((Element)list.get(b)).asXML());
/*      */       
/* 1088 */       while (matcher.find()) {
/* 1089 */         String str = matcher.group(1);
/* 1090 */         if ("pre".equals(paramString1)) {
/* 1091 */           if (!str4.contains("previous@" + str))
/* 1092 */             str4 = str4 + ",previous@" + str; 
/*      */           continue;
/*      */         } 
/* 1095 */         if (!str4.contains("after@" + str)) {
/* 1096 */           str4 = str4 + ",after@" + str;
/*      */         }
/*      */       } 
/*      */     } 
/*      */ 
/*      */ 
/*      */     
/* 1103 */     if (str4.startsWith(",")) {
/* 1104 */       str4 = str4.substring(1);
/*      */     }
/* 1106 */     propertiesUtil.put(str3, str4);
/*      */ 
/*      */     
/* 1109 */     propertiesUtil.store(str1);
/*      */     
/* 1111 */     writeLog("###生成配置顺序规则完成...###");
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getPattern(String paramString) {
/* 1121 */     String str1 = "<filter-name>(.*?)</filter-name>";
/* 1122 */     String str2 = getElementType(paramString);
/* 1123 */     if ("filter".equals(str2)) {
/* 1124 */       str1 = "<filter-name>(.*?)</filter-name>";
/* 1125 */     } else if ("servlet".equals(str2)) {
/* 1126 */       str1 = "<servlet-name>(.*?)</servlet-name>";
/* 1127 */     } else if ("listener".equals(str2)) {
/* 1128 */       str1 = "<listener-class>(.*?)</listener-class>";
/*      */     } 
/*      */     
/* 1131 */     return str1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String isFilterOrServlet(String paramString) {
/* 1141 */     String str = "";
/* 1142 */     if (paramString.indexOf("listener-class") > -1) {
/* 1143 */       str = "listener";
/* 1144 */     } else if (paramString.indexOf("filter-name") > -1) {
/* 1145 */       str = "filter";
/* 1146 */     } else if (paramString.indexOf("servlet-name") > -1) {
/* 1147 */       str = "servlet";
/*      */     } 
/* 1149 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getElementType(String paramString) {
/* 1159 */     return isFilterOrServlet(paramString);
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/ConfigOperation.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */