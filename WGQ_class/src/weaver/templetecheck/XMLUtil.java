/*     */ package weaver.templetecheck;
/*     */ import java.io.File;
/*     */ import java.io.FileOutputStream;
/*     */ import java.io.FileWriter;
/*     */ import java.io.IOException;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Iterator;
/*     */ import java.util.LinkedHashMap;
/*     */ import java.util.List;
/*     */ import java.util.regex.Matcher;
/*     */ import java.util.regex.Pattern;
/*     */ import org.dom4j.Attribute;
/*     */ import org.dom4j.Document;
/*     */ import org.dom4j.Element;
/*     */ import org.dom4j.io.OutputFormat;
/*     */ import org.dom4j.io.SAXReader;
/*     */ import org.dom4j.io.XMLWriter;
/*     */ import org.json.JSONArray;
/*     */ import org.json.JSONException;
/*     */ import org.json.JSONObject;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ public class XMLUtil {
/*  25 */   private JSONArray nodes = new JSONArray();
/*     */   private ArrayList<NodeElement> nodesArr;
/*  27 */   private HashMap<String, Element> nodemap = new HashMap<>();
/*     */   private Document doc;
/*     */   private ArrayList<String> xpathes;
/*  30 */   private String fileEncode = "";
/*  31 */   private String xmlpath = "C:\\Users\\<USER>\\Desktop\\test.xml";
/*  32 */   UpgradeFileUtil fileUtil = new UpgradeFileUtil();
/*     */   public XMLUtil(String paramString) {
/*     */     try {
/*  35 */       this.xmlpath = paramString;
/*  36 */       ReadXml readXml = new ReadXml();
/*  37 */       this.doc = readXml.read(this.xmlpath);
/*  38 */       this.fileEncode = readXml.getFileEncode();
/*  39 */     } catch (Exception exception) {
/*  40 */       exception.printStackTrace();
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public XMLUtil() {}
/*     */ 
/*     */   
/*     */   public String getXMLString() {
/*  50 */     if (this.doc != null) {
/*  51 */       return this.doc.asXML();
/*     */     }
/*  53 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public Document String2XML(String paramString) {
/*  58 */     this.doc = Str2Document(paramString);
/*  59 */     return this.doc;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public ArrayList<NodeElement> xml2ArrayList() {
/*     */     try {
/*  67 */       if (this.doc == null) {
/*  68 */         return null;
/*     */       }
/*  70 */       Element element = this.doc.getRootElement();
/*  71 */       this.nodesArr = getNameNode_list(element);
/*     */       
/*  73 */       return this.nodesArr;
/*  74 */     } catch (Exception exception) {
/*  75 */       exception.printStackTrace();
/*  76 */       return null;
/*     */     } 
/*     */   }
/*     */   
/*     */   public JSONArray xml2Json(String paramString1, String paramString2) {
/*     */     try {
/*  82 */       if (this.doc == null) {
/*  83 */         return null;
/*     */       }
/*  85 */       Element element = this.doc.getRootElement();
/*  86 */       getNameNode_json(element, paramString2);
/*  87 */       return this.nodes;
/*  88 */     } catch (Exception exception) {
/*  89 */       exception.printStackTrace();
/*  90 */       return null;
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public JSONArray getElementByParentId(String paramString) {
/*  96 */     JSONArray jSONArray = new JSONArray();
/*     */     try {
/*  98 */       if (this.doc == null) {
/*  99 */         return null;
/*     */       }
/* 101 */       Element element = this.doc.getRootElement();
/*     */       
/* 103 */       JSONObject jSONObject = new JSONObject();
/* 104 */       this.nodes.put(jSONObject);
/* 105 */       this.nodemap.put("root", element);
/* 106 */       jSONObject.put("id", "root");
/* 107 */       jSONObject.put("parentId", "0");
/*     */       
/* 109 */       String str1 = element.getName();
/* 110 */       String str2 = getAttrs(element);
/* 111 */       if ("".equals(str2)) {
/* 112 */         jSONObject.put("name", str1);
/*     */       } else {
/* 114 */         jSONObject.put("name", str1 + "    " + SystemEnv.getHtmlLabelName(10004225, ThreadVarLanguage.getLang()) + "" + str2);
/*     */       } 
/* 116 */       jSONObject.put("isParent", true);
/* 117 */       jSONObject.put("canEdit", true);
/* 118 */       jSONObject.put("xpath", element.getUniquePath());
/* 119 */       getNameNode_json(element, "root");
/*     */ 
/*     */       
/* 122 */       for (byte b = 0; b < this.nodes.length(); b++) {
/* 123 */         JSONObject jSONObject1 = (JSONObject)this.nodes.get(b);
/* 124 */         String str = (String)jSONObject1.get("parentId");
/* 125 */         if (paramString.equals(str));
/*     */ 
/*     */         
/* 128 */         jSONArray.put(jSONObject1);
/*     */       } 
/* 130 */     } catch (JSONException jSONException) {
/* 131 */       jSONException.printStackTrace();
/* 132 */       jSONArray = null;
/*     */     } 
/* 134 */     return jSONArray;
/*     */   }
/*     */   
/*     */   public JSONArray getNameNode_json(Element paramElement, String paramString) {
/*     */     try {
/* 139 */       List list = paramElement.elements();
/* 140 */       byte b = 1;
/* 141 */       for (Iterator<Element> iterator = list.iterator(); iterator.hasNext(); ) {
/* 142 */         String str1 = "";
/* 143 */         if ("0".equals(paramString)) {
/* 144 */           str1 = "" + b;
/*     */         } else {
/* 146 */           str1 = paramString + "_" + b;
/*     */         } 
/*     */         
/* 149 */         Element element = iterator.next();
/* 150 */         List list1 = element.elements();
/*     */         
/* 152 */         JSONObject jSONObject = new JSONObject();
/* 153 */         this.nodes.put(jSONObject);
/* 154 */         jSONObject.put("id", str1);
/* 155 */         jSONObject.put("parentId", paramString);
/* 156 */         String str2 = getAttrs(element);
/* 157 */         if ("".equals(str2)) {
/* 158 */           jSONObject.put("name", element.getName());
/*     */         } else {
/* 160 */           jSONObject.put("name", element.getName() + "    " + SystemEnv.getHtmlLabelName(10004225, ThreadVarLanguage.getLang()) + "" + str2);
/*     */         } 
/*     */ 
/*     */         
/* 164 */         jSONObject.put("canEdit", true);
/* 165 */         jSONObject.put("xpath", element.getUniquePath());
/* 166 */         if (list1.size() > 0) {
/* 167 */           jSONObject.put("isParent", true);
/* 168 */           this.nodemap.put(str1 + "", element);
/* 169 */           getNameNode_json(element, str1);
/*     */         } else {
/* 171 */           String str = element.getTextTrim();
/*     */           
/* 173 */           if (str == null) {
/* 174 */             jSONObject.put("isParent", false);
/*     */           } else {
/*     */             
/* 177 */             jSONObject.put("isParent", true);
/* 178 */             jSONObject.put("content", str);
/* 179 */             if (str != null && !"".equals(str)) {
/* 180 */               JSONObject jSONObject1 = new JSONObject();
/* 181 */               jSONObject1.put("id", str1 + "_content");
/* 182 */               jSONObject1.put("isParent", false);
/* 183 */               jSONObject1.put("parentId", str1);
/* 184 */               jSONObject1.put("canEdit", true);
/* 185 */               jSONObject1.put("name", "  " + SystemEnv.getHtmlLabelName(10004235, ThreadVarLanguage.getLang()) + "" + str);
/* 186 */               jSONObject1.put("xpath", element.getUniquePath());
/*     */               
/* 188 */               this.nodes.put(jSONObject1);
/*     */             } 
/*     */ 
/*     */             
/* 192 */             this.nodemap.put(str1 + "_content", element);
/*     */           } 
/*     */         } 
/*     */         
/* 196 */         b++;
/*     */       } 
/* 198 */       return this.nodes;
/* 199 */     } catch (Exception exception) {
/* 200 */       exception.printStackTrace();
/*     */       
/* 202 */       return null;
/*     */     } 
/*     */   }
/*     */   public Element getNodeById(Element paramElement, String paramString1, String paramString2) {
/* 206 */     Element element = null;
/*     */     try {
/* 208 */       List list = paramElement.elements();
/* 209 */       byte b = 1;
/* 210 */       for (Iterator<Element> iterator = list.iterator(); iterator.hasNext(); ) {
/* 211 */         String str = "";
/* 212 */         if ("0".equals(paramString1)) {
/* 213 */           str = "" + b;
/*     */         } else {
/* 215 */           str = paramString1 + "_" + b;
/*     */         } 
/*     */         
/* 218 */         Element element1 = iterator.next();
/* 219 */         List list1 = element1.elements();
/*     */         
/* 221 */         if (str.equals(paramString2)) {
/* 222 */           element = element1;
/*     */           break;
/*     */         } 
/* 225 */         if (list1.size() > 0) {
/* 226 */           element = getNodeById(element1, str, paramString2);
/* 227 */           if (element != null) {
/*     */             break;
/*     */           }
/*     */         } else {
/*     */           
/* 232 */           String str1 = str + "_content";
/* 233 */           if (str1.equals(paramString2)) {
/* 234 */             element = element1;
/*     */             break;
/*     */           } 
/*     */         } 
/* 238 */         b++;
/*     */       }
/*     */     
/* 241 */     } catch (Exception exception) {
/* 242 */       exception.printStackTrace();
/* 243 */       return null;
/*     */     } 
/* 245 */     return element;
/*     */   }
/*     */ 
/*     */   
/*     */   public ArrayList<NodeElement> getNameNode_list(Element paramElement) {
/*     */     try {
/* 251 */       ArrayList<NodeElement> arrayList = new ArrayList();
/* 252 */       List list = paramElement.elements();
/* 253 */       for (Element element : list) {
/*     */         
/* 255 */         List list1 = element.elements();
/*     */         
/* 257 */         NodeElement nodeElement = new NodeElement();
/* 258 */         arrayList.add(nodeElement);
/* 259 */         nodeElement.setTabname(element.getName());
/* 260 */         nodeElement.setElement(element);
/* 261 */         setAttrs_list(element, nodeElement);
/*     */         
/* 263 */         if (list1.size() > 0) {
/* 264 */           nodeElement.setChildren(getNameNode_list(element)); continue;
/*     */         } 
/* 266 */         nodeElement.setContent(element.getTextTrim());
/*     */       } 
/*     */ 
/*     */       
/* 270 */       return arrayList;
/* 271 */     } catch (Exception exception) {
/* 272 */       exception.printStackTrace();
/*     */       
/* 274 */       return null;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getAttrs(Element paramElement) throws JSONException {
/* 283 */     String str = "";
/* 284 */     List<Attribute> list = paramElement.attributes();
/* 285 */     JSONObject jSONObject = new JSONObject();
/* 286 */     LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/* 287 */     for (byte b = 0; b < list.size(); b++) {
/* 288 */       Attribute attribute = list.get(b);
/* 289 */       String str1 = attribute.getName();
/* 290 */       String str2 = attribute.getValue();
/* 291 */       str = str + "    " + str1 + "=" + str2;
/*     */     } 
/* 293 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONObject getAttrsJson(Element paramElement) throws JSONException {
/* 302 */     String str = "";
/* 303 */     List<Attribute> list = paramElement.attributes();
/* 304 */     JSONObject jSONObject = new JSONObject();
/* 305 */     LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/* 306 */     for (byte b = 0; b < list.size(); b++) {
/* 307 */       Attribute attribute = list.get(b);
/* 308 */       String str1 = attribute.getName();
/* 309 */       String str2 = attribute.getValue();
/* 310 */       jSONObject.put(str1, str2);
/*     */     } 
/* 312 */     return jSONObject;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setAttrs_list(Element paramElement, NodeElement paramNodeElement) {
/* 321 */     List<String> list = paramElement.attributes();
/* 322 */     LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/* 323 */     for (byte b = 0; b < list.size(); b++) {
/* 324 */       String str1 = list.get(b);
/* 325 */       String str2 = paramElement.attributeValue(str1);
/* 326 */       linkedHashMap.put(str1, str2);
/*     */     } 
/* 328 */     paramNodeElement.setNodeattrs((LinkedHashMap)linkedHashMap);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setAttrs_json(Element paramElement, JSONObject paramJSONObject) throws JSONException {
/* 337 */     List<String> list = paramElement.attributes();
/* 338 */     JSONObject jSONObject = new JSONObject();
/* 339 */     LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/* 340 */     for (byte b = 0; b < list.size(); b++) {
/* 341 */       String str1 = list.get(b);
/* 342 */       String str2 = paramElement.attributeValue(str1);
/* 343 */       jSONObject.put(str1, str2);
/*     */     } 
/* 345 */     paramJSONObject.put("attributes", jSONObject);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Element getNodeByXpath(String paramString) {
/* 355 */     Element element = this.doc.getRootElement();
/* 356 */     return (Element)element.selectSingleNode(paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String removeNodeByXpath(String paramString) throws IOException {
/* 366 */     String str = "";
/*     */     try {
/* 368 */       int i = paramString.lastIndexOf("/");
/* 369 */       String str1 = paramString.substring(0, i);
/*     */       
/* 371 */       Element element1 = this.doc.getRootElement();
/* 372 */       Element element2 = (Element)element1.selectSingleNode(str1);
/* 373 */       Element element3 = (Element)element2.selectSingleNode(paramString);
/*     */       
/* 375 */       element2.remove(element3);
/*     */       
/* 377 */       writeXml();
/*     */       
/* 379 */       str = "{\"status\":\"ok\"}";
/*     */     }
/* 381 */     catch (Exception exception) {
/* 382 */       exception.printStackTrace();
/* 383 */       str = "{\"status\":\"no\"}";
/*     */     } 
/* 385 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String updateXml(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6) {
/* 398 */     String str = "";
/*     */     try {
/* 400 */       Element element1 = this.doc.getRootElement();
/* 401 */       Element element2 = (Element)element1.selectSingleNode(paramString2);
/* 402 */       String[] arrayOfString1 = paramString4.split(",");
/* 403 */       String[] arrayOfString2 = paramString3.split(",");
/*     */       
/* 405 */       if ("edit".equals(paramString6)) {
/* 406 */         element2.clearContent();
/*     */ 
/*     */         
/* 409 */         if ("".equals(paramString4)) {
/* 410 */           List<Attribute> list1 = element2.attributes();
/* 411 */           for (byte b = 0; b < list1.size(); b++) {
/* 412 */             element2.remove(list1.get(b));
/*     */           }
/*     */         } else {
/* 415 */           for (byte b = 0; b < arrayOfString1.length; b++) {
/*     */             
/* 417 */             if (arrayOfString2[b] != null && !"".equals(arrayOfString2[b])) {
/*     */ 
/*     */ 
/*     */ 
/*     */               
/* 422 */               String str1 = arrayOfString1[b].replace("\"", "").replace("'", "").replace("<", "").replace(">", "");
/* 423 */               String str2 = arrayOfString2[b].replace("\"", "").replace("'", "").replace("'", "").replace("<", "").replace(">", "");
/* 424 */               Attribute attribute = element2.attribute(str2);
/* 425 */               if (attribute == null) {
/* 426 */                 element2.addAttribute(str2, str1);
/*     */               } else {
/* 428 */                 attribute.setValue(str1);
/*     */               } 
/*     */             } 
/*     */           } 
/*     */         } 
/*     */         
/* 434 */         paramString5 = "<myroot>" + paramString5 + "</myroot>";
/* 435 */         Document document = Str2Document(paramString5);
/* 436 */         Element element = document.getRootElement();
/* 437 */         List<Element> list = element.elements();
/* 438 */         if (list.size() > 0) {
/* 439 */           for (byte b = 0; b < list.size(); b++) {
/* 440 */             Element element3 = list.get(b);
/* 441 */             Document document1 = Str2Document(element3.asXML());
/* 442 */             Element element4 = document1.getRootElement();
/* 443 */             element2.add(element4);
/*     */           } 
/*     */         } else {
/* 446 */           element2.setText(element.getTextTrim());
/*     */         } 
/*     */ 
/*     */         
/* 450 */         writeXml();
/* 451 */         str = "{\"status\":\"ok\"}";
/*     */       }
/*     */       else {
/*     */         
/* 455 */         Document document1 = Str2Document("<myroot><" + paramString1 + ">" + paramString5 + "</" + paramString1 + "></myroot>");
/* 456 */         Element element3 = document1.getRootElement();
/* 457 */         List<Element> list = element3.elements();
/*     */         
/* 459 */         Element element4 = list.get(0);
/* 460 */         Document document2 = Str2Document(element4.asXML());
/* 461 */         Element element5 = document2.getRootElement();
/*     */         
/* 463 */         if (arrayOfString2 != null && arrayOfString2.length > 0) {
/* 464 */           for (byte b = 0; b < arrayOfString2.length; b++) {
/* 465 */             if (!"".equals(arrayOfString2[b]) && arrayOfString2[b] != null && arrayOfString1[b] != null) {
/* 466 */               element5.addAttribute(arrayOfString2[b], arrayOfString1[b]);
/*     */             }
/*     */           } 
/*     */         }
/* 470 */         element2.add(element5);
/*     */         
/* 472 */         writeXml();
/* 473 */         str = "{\"status\":\"ok\"}";
/*     */       } 
/* 475 */     } catch (Exception exception) {
/* 476 */       exception.printStackTrace();
/* 477 */       str = "{\"status\":\"no\"}";
/*     */     } 
/*     */     
/* 480 */     return str;
/*     */   }
/*     */ 
/*     */   
/*     */   public void writeXml() {
/* 485 */     FileWriter fileWriter = null;
/* 486 */     XMLWriter xMLWriter = null;
/*     */     try {
/* 488 */       fileWriter = new FileWriter(this.fileUtil.getPath(this.xmlpath));
/* 489 */       OutputFormat outputFormat = OutputFormat.createPrettyPrint();
/* 490 */       outputFormat.setEncoding(this.fileEncode);
/* 491 */       outputFormat.setIndent(true);
/* 492 */       outputFormat.setIndent("   ");
/* 493 */       xMLWriter = new XMLWriter(fileWriter, outputFormat);
/* 494 */       xMLWriter.write(this.doc);
/*     */       
/* 496 */       fileWriter.flush();
/* 497 */       xMLWriter.flush();
/* 498 */       if (fileWriter != null) {
/* 499 */         fileWriter.close();
/*     */       }
/* 501 */       if (xMLWriter != null)
/*     */       {
/* 503 */         xMLWriter.close();
/*     */       }
/* 505 */     } catch (Exception exception) {
/* 506 */       exception.printStackTrace();
/*     */     } finally {
/* 508 */       if (fileWriter != null) {
/*     */         try {
/* 510 */           fileWriter.close();
/* 511 */         } catch (IOException iOException) {
/* 512 */           iOException.printStackTrace();
/*     */         } 
/*     */       }
/* 515 */       if (xMLWriter != null) {
/*     */         try {
/* 517 */           xMLWriter.close();
/* 518 */         } catch (IOException iOException) {
/* 519 */           iOException.printStackTrace();
/*     */         } 
/*     */       }
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public ArrayList<String> getXPath(String paramString, ArrayList<NodeElement> paramArrayList) {
/* 528 */     for (byte b = 0; b < paramArrayList.size(); b++) {
/* 529 */       NodeElement nodeElement = paramArrayList.get(b);
/* 530 */       ArrayList<NodeElement> arrayList = nodeElement.getChildren();
/* 531 */       if (arrayList.size() > 0) {
/* 532 */         getXPath(paramString, arrayList);
/*     */       } else {
/* 534 */         String str = nodeElement.getContent();
/* 535 */         if (str != null && str.equals(paramString)) {
/* 536 */           String str1 = nodeElement.getElement().getPath() + "[text()='" + str + "']";
/*     */           
/* 538 */           this.xpathes.add(str1);
/*     */         } 
/*     */       } 
/*     */     } 
/* 542 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String addXml(File paramFile, Document paramDocument) {
/* 551 */     if (paramFile != null) {
/* 552 */       if (paramFile.exists()) {
/* 553 */         return "error";
/*     */       }
/* 555 */       FileOutputStream fileOutputStream = null;
/* 556 */       XMLWriter xMLWriter = null;
/*     */       
/*     */       try {
/* 559 */         String str = paramFile.getPath();
/* 560 */         str = str.substring(0, str.lastIndexOf(File.separatorChar));
/* 561 */         File file = new File(this.fileUtil.getPath(str));
/* 562 */         if (!file.exists()) {
/* 563 */           file.mkdirs();
/*     */         }
/*     */         
/* 566 */         paramFile.createNewFile();
/* 567 */         fileOutputStream = new FileOutputStream(paramFile);
/* 568 */         OutputFormat outputFormat = OutputFormat.createPrettyPrint();
/* 569 */         outputFormat.setEncoding("UTF-8");
/* 570 */         outputFormat.setIndent(true);
/* 571 */         outputFormat.setIndent("   ");
/* 572 */         xMLWriter = new XMLWriter(fileOutputStream, outputFormat);
/* 573 */         xMLWriter.write(paramDocument);
/* 574 */         fileOutputStream.flush();
/* 575 */         xMLWriter.flush();
/* 576 */         if (fileOutputStream != null) {
/* 577 */           fileOutputStream.close();
/*     */         }
/* 579 */         if (xMLWriter != null)
/*     */         {
/* 581 */           xMLWriter.close();
/*     */         }
/* 583 */       } catch (Exception exception) {
/* 584 */         exception.printStackTrace();
/*     */       } finally {
/* 586 */         if (fileOutputStream != null) {
/*     */           try {
/* 588 */             fileOutputStream.close();
/* 589 */           } catch (IOException iOException) {
/* 590 */             iOException.printStackTrace();
/*     */           } 
/*     */         }
/* 593 */         if (xMLWriter != null) {
/*     */           try {
/* 595 */             xMLWriter.close();
/* 596 */           } catch (IOException iOException) {
/* 597 */             iOException.printStackTrace();
/*     */           } 
/*     */         }
/*     */       } 
/*     */     } else {
/*     */       
/* 603 */       return "error";
/*     */     } 
/*     */     
/* 606 */     return "ok";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Document Str2Document(String paramString) {
/* 616 */     SAXReader sAXReader = new SAXReader();
/* 617 */     sAXReader.setEntityResolver(new IgnoreDTDEntityResolver());
/* 618 */     Document document = null;
/*     */     try {
/* 620 */       document = sAXReader.read(new ByteArrayInputStream(paramString.getBytes("UTF-8")));
/* 621 */     } catch (Exception exception) {
/*     */       
/* 623 */       exception.printStackTrace();
/*     */     } 
/* 625 */     return document;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String generateXPathByContent(String paramString) {
/* 633 */     String str1 = getElementType(paramString);
/* 634 */     String str2 = "";
/* 635 */     String str3 = "";
/* 636 */     String str4 = getPattern(paramString);
/*     */     
/* 638 */     Pattern pattern = Pattern.compile(str4);
/*     */     
/* 640 */     Matcher matcher = pattern.matcher(paramString);
/*     */     
/* 642 */     if (matcher.find()) {
/*     */       
/* 644 */       str3 = matcher.group(1);
/* 645 */       if ("servlet".equals(str1)) {
/* 646 */         str2 = "/web-app/servlet/servlet-name[text()='" + str3 + "']/parent::*";
/* 647 */       } else if ("filter".equals(str1)) {
/* 648 */         str2 = "/web-app/filter/filter-name[text()='" + str3 + "']/parent::*";
/* 649 */       } else if ("listener".equals(str1)) {
/* 650 */         str2 = "/web-app/listener/listener-class[text()='" + str3 + "']/parent::*";
/* 651 */       } else if ("env-entry".equals(str1)) {
/* 652 */         str2 = "/web-app/env-entry/env-entry-name[text()='" + str3 + "']/parent::*";
/*     */       } 
/*     */     } 
/* 655 */     return str2;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getPattern(String paramString) {
/* 664 */     String str1 = "<filter-name>(.*?)</filter-name>";
/* 665 */     String str2 = getElementType(paramString);
/* 666 */     if ("filter".equals(str2)) {
/* 667 */       str1 = "<filter-name>(.*?)</filter-name>";
/* 668 */     } else if ("servlet".equals(str2)) {
/* 669 */       str1 = "<servlet-name>(.*?)</servlet-name>";
/* 670 */     } else if ("listener".equals(str2)) {
/* 671 */       str1 = "<listener-class>(.*?)</listener-class>";
/* 672 */     } else if ("env-entry".equals(str2)) {
/* 673 */       str1 = "<env-entry-name>(.*?)</env-entry-name>";
/*     */     } 
/*     */     
/* 676 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String isFilterOrServlet(String paramString) {
/* 685 */     String str = "";
/* 686 */     if (paramString.indexOf("env-entry") > -1) {
/* 687 */       str = "env-entry";
/* 688 */     } else if (paramString.indexOf("listener-class") > -1) {
/* 689 */       str = "listener";
/* 690 */     } else if (paramString.indexOf("filter-name") > -1) {
/* 691 */       str = "filter";
/* 692 */     } else if (paramString.indexOf("servlet-name") > -1) {
/* 693 */       str = "servlet";
/*     */     } 
/* 695 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getElementType(String paramString) {
/* 704 */     return isFilterOrServlet(paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getElementName(String paramString) {
/* 713 */     String str1 = getPattern(paramString);
/* 714 */     Pattern pattern = Pattern.compile(str1);
/*     */     
/* 716 */     Matcher matcher = pattern.matcher(paramString);
/* 717 */     String str2 = "";
/* 718 */     if (matcher.find()) {
/* 719 */       str2 = matcher.group(1);
/*     */     }
/*     */     
/* 722 */     return str2;
/*     */   }
/*     */ 
/*     */   
/*     */   public class NodeElement
/*     */   {
/*     */     private ArrayList<NodeElement> children;
/*     */     
/*     */     private Element element;
/*     */     private LinkedHashMap<String, String> nodeattrs;
/*     */     private String content;
/*     */     private String tabname;
/*     */     
/*     */     public LinkedHashMap<String, String> getNodeattrs() {
/* 736 */       return this.nodeattrs;
/*     */     }
/*     */     public void setNodeattrs(LinkedHashMap<String, String> param1LinkedHashMap) {
/* 739 */       this.nodeattrs = param1LinkedHashMap;
/*     */     }
/*     */     public String getContent() {
/* 742 */       return this.content;
/*     */     }
/*     */     public void setContent(String param1String) {
/* 745 */       this.content = param1String;
/*     */     }
/*     */     public Element getElement() {
/* 748 */       return this.element;
/*     */     }
/*     */     public void setElement(Element param1Element) {
/* 751 */       this.element = param1Element;
/*     */     }
/*     */     public ArrayList<NodeElement> getChildren() {
/* 754 */       return this.children;
/*     */     }
/*     */     public void setChildren(ArrayList<NodeElement> param1ArrayList) {
/* 757 */       this.children = param1ArrayList;
/*     */     }
/*     */     public String getTabname() {
/* 760 */       return this.tabname;
/*     */     }
/*     */     public void setTabname(String param1String) {
/* 763 */       this.tabname = param1String;
/*     */     }
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/XMLUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */