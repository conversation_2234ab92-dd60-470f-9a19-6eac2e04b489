/*    */ package weaver.templetecheck;
/*    */ 
/*    */ public class ConfigDetail
/*    */ {
/*    */   private String filepath;
/*    */   private String keyname;
/*    */   private String value;
/*    */   private String notes;
/*    */   private String type;
/*    */   private int detailid;
/*    */   private String xpath;
/*    */   private String requisite;
/*    */   private String filename;
/*    */   
/*    */   public String getNotes() {
/* 16 */     return this.notes;
/*    */   }
/*    */   public void setNotes(String paramString) {
/* 19 */     this.notes = paramString;
/*    */   }
/*    */   
/*    */   public String getFilename() {
/* 23 */     return this.filename;
/*    */   }
/*    */   public void setFilename(String paramString) {
/* 26 */     this.filename = paramString;
/*    */   }
/*    */   public String getRequisite() {
/* 29 */     return this.requisite;
/*    */   }
/*    */   public void setRequisite(String paramString) {
/* 32 */     this.requisite = paramString;
/*    */   }
/*    */   public String getXpath() {
/* 35 */     return this.xpath;
/*    */   }
/*    */   public void setXpath(String paramString) {
/* 38 */     this.xpath = paramString;
/*    */   }
/*    */   public int getDetailid() {
/* 41 */     return this.detailid;
/*    */   }
/*    */   public void setDetailid(int paramInt) {
/* 44 */     this.detailid = paramInt;
/*    */   }
/*    */   public String getKeyname() {
/* 47 */     return this.keyname;
/*    */   }
/*    */   public void setKeyname(String paramString) {
/* 50 */     this.keyname = paramString;
/*    */   }
/*    */   
/*    */   public String getFilepath() {
/* 54 */     return this.filepath;
/*    */   }
/*    */   public void setFilepath(String paramString) {
/* 57 */     this.filepath = paramString;
/*    */   }
/*    */   public String getValue() {
/* 60 */     return this.value;
/*    */   }
/*    */   public void setValue(String paramString) {
/* 63 */     this.value = paramString;
/*    */   }
/*    */   public String getType() {
/* 66 */     return this.type;
/*    */   }
/*    */   public void setType(String paramString) {
/* 69 */     this.type = paramString;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/ConfigDetail.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */