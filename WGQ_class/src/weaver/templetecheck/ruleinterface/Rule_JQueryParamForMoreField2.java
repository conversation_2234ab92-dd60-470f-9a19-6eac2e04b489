/*    */ package weaver.templetecheck.ruleinterface;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import java.util.regex.Matcher;
/*    */ import java.util.regex.Pattern;
/*    */ import weaver.general.BaseBean;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class Rule_JQueryParamForMoreField2
/*    */   extends CheckRuleInterface
/*    */ {
/*    */   public List<String> checkRule(String paramString) {
/* 21 */     Map<String, Object> map = analyseStr(paramString, false);
/* 22 */     return (List)map.get("checkresult");
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public String replaceRule(String paramString) {
/* 28 */     Map<String, Object> map = analyseStr(paramString, true);
/* 29 */     return (String)map.get("replaceresult");
/*    */   }
/*    */ 
/*    */   
/*    */   private Map<String, Object> analyseStr(String paramString, boolean paramBoolean) {
/* 34 */     ArrayList<String> arrayList = new ArrayList();
/* 35 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */ 
/*    */ 
/*    */     
/*    */     try {
/* 40 */       Map<String, String> map = findStrByRegex(paramString, "\\$\\(.*?\\)\\.val\\((.*?)\\)(;?)");
/* 41 */       String str = map.get("transferredsource");
/* 42 */       for (String str1 : map.keySet()) {
/* 43 */         if (!str1.contains("27C6F7EACF39D0F36DBD516866B7776E"))
/* 44 */           continue;  String str2 = map.get(str1);
/* 45 */         Pattern pattern = Pattern.compile("(?i)\\$\\((.*?,.*?)\\).val\\((.*?)\\)(;?)");
/* 46 */         Matcher matcher = pattern.matcher(str2);
/* 47 */         if (matcher.find()) {
/* 48 */           arrayList.add(matcher.group());
/* 49 */           if (paramBoolean) {
/* 50 */             String[] arrayOfString = matcher.group(1).split(",");
/* 51 */             String str3 = matcher.group(2);
/* 52 */             String str4 = "";
/* 53 */             for (String str6 : arrayOfString) {
/* 54 */               if (!"".equals(str6.trim())) {
/* 55 */                 if (!str6.trim().startsWith("'") && !str6.trim().startsWith("\"")) {
/* 56 */                   str6 = "\"" + str6;
/*    */                 } else {
/* 58 */                   str6 = "\"" + str6.substring(1);
/*    */                 } 
/*    */                 
/* 61 */                 if (!str6.trim().endsWith("'") && !str6.trim().endsWith("\"")) {
/* 62 */                   str6 = str6 + "\"";
/*    */                 } else {
/* 64 */                   str6 = str6.substring(0, str6.length() - 1) + "\"";
/*    */                 } 
/* 66 */                 str4 = str4 + "WfForm.changeFieldValue(" + str6.replace("#", "") + "," + str3 + ");\n";
/*    */               } 
/*    */             } 
/* 69 */             String str5 = matcher.replaceAll(str4);
/* 70 */             str = str.replace(str1, str5);
/*    */           }  continue;
/*    */         } 
/* 73 */         if (paramBoolean) {
/* 74 */           str = str.replace(str1, str2);
/*    */         }
/*    */       } 
/*    */       
/* 78 */       hashMap.put("checkresult", arrayList);
/* 79 */       hashMap.put("replaceresult", str);
/* 80 */     } catch (Exception exception) {
/* 81 */       (new BaseBean()).writeLog(getClass().getName() + "" + exception.getMessage() + exception);
/* 82 */       hashMap.put("checkresult", arrayList);
/* 83 */       hashMap.put("replaceresult", paramString);
/*    */     } 
/* 85 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/ruleinterface/Rule_JQueryParamForMoreField2.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */