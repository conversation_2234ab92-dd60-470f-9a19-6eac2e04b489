/*     */ package weaver.templetecheck.ruleinterface;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.general.BaseBean;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class Rule_JQueryFieldHide
/*     */   extends CheckRuleInterface
/*     */ {
/*     */   public List<String> checkRule(String paramString) {
/*  19 */     Map<String, Object> map = analyseStr(paramString, false);
/*  20 */     return (List)map.get("checkresult");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String replaceRule(String paramString) {
/*  27 */     Map<String, Object> map = analyseStr(paramString, true);
/*  28 */     return (String)map.get("replaceresult");
/*     */   }
/*     */ 
/*     */   
/*     */   private Map<String, Object> analyseStr(String paramString, boolean paramBoolean) {
/*  33 */     ArrayList<String> arrayList = new ArrayList();
/*  34 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*     */     try {
/*  37 */       Map<String, String> map = findStrByRegex(paramString, "(?i)(jQuery|\\$)\\((.*?field.*?)\\).hide\\(\\s?\\)(;?)");
/*  38 */       String str = map.get("transferredsource");
/*  39 */       for (String str1 : map.keySet()) {
/*  40 */         if (!str1.contains("27C6F7EACF39D0F36DBD516866B7776E"))
/*  41 */           continue;  String str2 = map.get(str1);
/*  42 */         arrayList.add(str2);
/*  43 */         if (paramBoolean) {
/*  44 */           String str3 = "";
/*  45 */           String str4 = "";
/*     */           try {
/*  47 */             str3 = str2.substring(str2.indexOf("(") + 1, str2.indexOf(")"));
/*  48 */             str4 = str2.substring(str2.lastIndexOf("(") + 1, str2.lastIndexOf(")"));
/*  49 */             if (str3.contains("+")) {
/*  50 */               str = str.replace(str1, str2);
/*     */               continue;
/*     */             } 
/*  53 */           } catch (Exception exception) {
/*     */             continue;
/*     */           } 
/*  56 */           String[] arrayOfString = str3.split(",");
/*  57 */           String str5 = str4;
/*  58 */           String str6 = "";
/*  59 */           for (String str7 : arrayOfString) {
/*  60 */             if (!"".equals(str7.trim())) {
/*  61 */               if (!str7.trim().startsWith("'") && !str7.trim().startsWith("\"")) {
/*  62 */                 str7 = "\"" + str7;
/*     */               } else {
/*  64 */                 str7 = "\"" + str7.substring(1);
/*     */               } 
/*     */               
/*  67 */               if (!str7.trim().endsWith("'") && !str7.trim().endsWith("\"")) {
/*  68 */                 str7 = str7 + "\"";
/*     */               } else {
/*  70 */                 str7 = str7.substring(0, str7.length() - 1) + "\"";
/*     */               } 
/*  72 */               str6 = str6 + "jQuery(" + str7.replace("#", ".wf-input-") + ").hide();\n";
/*     */             } 
/*     */           } 
/*  75 */           str = str.replace(str1, str6); continue;
/*     */         } 
/*  77 */         if (paramBoolean) {
/*  78 */           str = str.replace(str1, str2);
/*     */         }
/*     */       } 
/*     */       
/*  82 */       hashMap.put("checkresult", arrayList);
/*  83 */       hashMap.put("replaceresult", str);
/*  84 */     } catch (Exception exception) {
/*  85 */       (new BaseBean()).writeLog(getClass().getName() + "" + exception.getMessage() + exception);
/*  86 */       hashMap.put("checkresult", arrayList);
/*  87 */       hashMap.put("replaceresult", paramString);
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 134 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/ruleinterface/Rule_JQueryFieldHide.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */