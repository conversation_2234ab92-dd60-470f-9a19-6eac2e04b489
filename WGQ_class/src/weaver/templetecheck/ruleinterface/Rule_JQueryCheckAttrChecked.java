/*    */ package weaver.templetecheck.ruleinterface;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import weaver.general.BaseBean;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class Rule_JQueryCheckAttrChecked
/*    */   extends CheckRuleInterface
/*    */ {
/*    */   public List<String> checkRule(String paramString) {
/* 21 */     Map<String, Object> map = analyseStr(paramString, false);
/* 22 */     return (List)map.get("checkresult");
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String replaceRule(String paramString) {
/* 30 */     return paramString;
/*    */   }
/*    */   
/*    */   private Map<String, Object> analyseStr(String paramString, boolean paramBoolean) {
/* 34 */     ArrayList<String> arrayList = new ArrayList();
/* 35 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */ 
/*    */ 
/*    */     
/*    */     try {
/* 40 */       Map<String, String> map = findStrByRegex(paramString, "(?i)(\\$|jQuery)\\((\\\"|')#field\\d+(\\\"|')\\).attr\\((\\\"|')checked(\\\"|')\\)");
/* 41 */       String str = map.get("transferredsource");
/* 42 */       for (String str1 : map.keySet()) {
/* 43 */         if (!str1.contains("27C6F7EACF39D0F36DBD516866B7776E"))
/* 44 */           continue;  String str2 = map.get(str1);
/* 45 */         arrayList.add(str2);
/*    */       } 
/* 47 */       hashMap.put("checkresult", arrayList);
/* 48 */       hashMap.put("replaceresult", str);
/* 49 */     } catch (Exception exception) {
/* 50 */       (new BaseBean()).writeLog(getClass().getName() + "" + exception.getMessage() + exception);
/* 51 */       hashMap.put("checkresult", arrayList);
/* 52 */       hashMap.put("replaceresult", "");
/*    */     } 
/* 54 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/ruleinterface/Rule_JQueryCheckAttrChecked.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */