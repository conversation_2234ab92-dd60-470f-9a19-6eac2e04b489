/*    */ package weaver.templetecheck.ruleinterface;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import weaver.general.BaseBean;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class Rule_Wea
/*    */   extends CheckRuleInterface
/*    */ {
/*    */   public List<String> checkRule(String paramString) {
/* 19 */     Map<String, Object> map = analyseStr(paramString, false);
/* 20 */     return (List)map.get("checkresult");
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public String replaceRule(String paramString) {
/* 26 */     return paramString;
/*    */   }
/*    */   
/*    */   private Map<String, Object> analyseStr(String paramString, boolean paramBoolean) {
/* 30 */     ArrayList<String> arrayList = new ArrayList();
/* 31 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */     
/*    */     try {
/* 34 */       Map<String, String> map = findStrByTag(paramString, "<wea:", "<", ">", "<wea:".indexOf("<"), true);
/* 35 */       String str = map.get("transferredsource");
/* 36 */       for (String str1 : map.keySet()) {
/* 37 */         if (!str1.contains("27C6F7EACF39D0F36DBD516866B7776E"))
/* 38 */           continue;  String str2 = map.get(str1);
/*    */         
/* 40 */         if (str2 != null && !"".equals(str2)) {
/* 41 */           arrayList.add(str2); continue;
/*    */         } 
/* 43 */         if (paramBoolean) {
/* 44 */           str = str.replace(str1, str2);
/*    */         }
/*    */       } 
/*    */       
/* 48 */       hashMap.put("checkresult", arrayList);
/* 49 */       hashMap.put("replaceresult", str);
/* 50 */     } catch (Exception exception) {
/* 51 */       (new BaseBean()).writeLog(getClass().getName() + "" + exception.getMessage() + exception);
/* 52 */       hashMap.put("checkresult", arrayList);
/* 53 */       hashMap.put("replaceresult", "");
/*    */     } 
/* 55 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/ruleinterface/Rule_Wea.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */