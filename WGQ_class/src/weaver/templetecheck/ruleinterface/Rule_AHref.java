/*    */ package weaver.templetecheck.ruleinterface;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import java.util.regex.Matcher;
/*    */ import java.util.regex.Pattern;
/*    */ import weaver.general.BaseBean;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class Rule_AHref
/*    */   extends CheckRuleInterface
/*    */ {
/*    */   public List<String> checkRule(String paramString) {
/* 21 */     Map<String, Object> map = analyseStr(paramString, false);
/* 22 */     return (List)map.get("checkresult");
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public String replaceRule(String paramString) {
/* 28 */     Map<String, Object> map = analyseStr(paramString, true);
/* 29 */     return (String)map.get("replaceresult");
/*    */   }
/*    */ 
/*    */   
/*    */   private Map<String, Object> analyseStr(String paramString, boolean paramBoolean) {
/* 34 */     ArrayList<String> arrayList = new ArrayList();
/* 35 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */ 
/*    */ 
/*    */     
/*    */     try {
/* 40 */       Map<String, String> map = findStrByTag(paramString, "<a", "<", ">", 0, true);
/* 41 */       String str = map.get("transferredsource");
/* 42 */       for (String str1 : map.keySet()) {
/* 43 */         if (!str1.contains("27C6F7EACF39D0F36DBD516866B7776E"))
/* 44 */           continue;  String str2 = map.get(str1);
/* 45 */         Pattern pattern = Pattern.compile("(?i)(\\<a.*?href.*?)#(.*?>)");
/* 46 */         Matcher matcher = pattern.matcher(str2);
/* 47 */         if (matcher.find()) {
/* 48 */           arrayList.add(matcher.group());
/* 49 */           if (paramBoolean) {
/* 50 */             String str3 = matcher.replaceAll("$1javascript:return false;$2");
/* 51 */             str = str.replace(str1, str3);
/*    */           }  continue;
/*    */         } 
/* 54 */         if (paramBoolean) {
/* 55 */           str = str.replace(str1, str2);
/*    */         }
/*    */       } 
/*    */       
/* 59 */       hashMap.put("checkresult", arrayList);
/* 60 */       hashMap.put("replaceresult", str);
/* 61 */     } catch (Exception exception) {
/* 62 */       (new BaseBean()).writeLog(getClass().getName() + "" + exception.getMessage() + exception);
/* 63 */       hashMap.put("checkresult", arrayList);
/* 64 */       hashMap.put("replaceresult", "");
/*    */     } 
/* 66 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/ruleinterface/Rule_AHref.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */