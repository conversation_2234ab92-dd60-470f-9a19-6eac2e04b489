/*    */ package weaver.templetecheck.ruleinterface;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.List;
/*    */ import java.util.regex.Matcher;
/*    */ import java.util.regex.Pattern;
/*    */ import weaver.general.BaseBean;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class Rule_BindPropertyChange
/*    */   extends CheckRuleInterface
/*    */ {
/*    */   public List<String> checkRule(String paramString) {
/*    */     try {
/* 23 */       ArrayList<String> arrayList = new ArrayList();
/* 24 */       Pattern pattern = null;
/* 25 */       Matcher matcher = null;
/*    */       try {
/* 27 */         pattern = Pattern.compile("(?i)(\\.)bind\\(.*?propertychange.*?,.*?function.*?\\(\\)");
/* 28 */         matcher = pattern.matcher(paramString);
/*    */       }
/* 30 */       catch (Exception exception) {
/* 31 */         (new BaseBean()).writeLog(exception.toString());
/*    */       } 
/* 33 */       while (matcher.find()) {
/* 34 */         arrayList.add(matcher.group());
/*    */       }
/* 36 */       (new BaseBean()).writeLog("======================调用接口weaver.templetecheck.ruleinterface.Rule_BindPropertyChange的checkRule方法成功");
/* 37 */       return arrayList;
/* 38 */     } catch (Exception exception) {
/* 39 */       exception.printStackTrace();
/*    */       
/* 41 */       (new BaseBean()).writeLog("======================调用接口weaver.templetecheck.ruleinterface.Rule_BindPropertyChange的checkRule方法成功");
/* 42 */       return null;
/*    */     } 
/*    */   }
/*    */   
/*    */   public String replaceRule(String paramString) {
/* 47 */     Pattern pattern = null;
/* 48 */     Matcher matcher = null;
/*    */     try {
/* 50 */       pattern = Pattern.compile("(?i)(\\.)bind\\(.*?propertychange.*?,.*?function.*?\\(\\)");
/* 51 */       matcher = pattern.matcher(paramString);
/*    */     }
/* 53 */     catch (Exception exception) {
/* 54 */       (new BaseBean()).writeLog(exception.toString());
/*    */     } 
/* 56 */     while (matcher.find()) {
/* 57 */       paramString = matcher.replaceAll(".bindPropertyChange(function()");
/*    */     }
/* 59 */     (new BaseBean()).writeLog("======================调用接口weaver.templetecheck.ruleinterface.Rule_BindPropertyChange的replaceRule方法成功");
/* 60 */     return paramString;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/ruleinterface/Rule_BindPropertyChange.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */