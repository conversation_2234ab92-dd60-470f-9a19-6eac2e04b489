/*    */ package weaver.templetecheck.ruleinterface;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.List;
/*    */ import java.util.regex.Matcher;
/*    */ import java.util.regex.Pattern;
/*    */ import weaver.general.BaseBean;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class Rule_ClassCalendar
/*    */   extends CheckRuleInterface
/*    */ {
/*    */   public List<String> checkRule(String paramString) {
/*    */     try {
/* 22 */       ArrayList<String> arrayList = new ArrayList();
/* 23 */       Pattern pattern = null;
/* 24 */       Matcher matcher = null;
/*    */       try {
/* 26 */         pattern = Pattern.compile("(class.*?)(calendar|CALENDAR)(.*?>)");
/* 27 */         matcher = pattern.matcher(paramString);
/*    */       }
/* 29 */       catch (Exception exception) {
/* 30 */         (new BaseBean()).writeLog(exception.getMessage() + exception);
/*    */       } 
/* 32 */       while (matcher.find()) {
/* 33 */         arrayList.add(matcher.group());
/*    */       }
/*    */       
/* 36 */       (new BaseBean()).writeLog("======================调用接口weaver.templetecheck.ruleinterface.Rule_ClassCalendar的checkRule方法成功");
/* 37 */       return arrayList;
/* 38 */     } catch (Exception exception) {
/* 39 */       exception.printStackTrace();
/*    */       
/* 41 */       (new BaseBean()).writeLog("======================调用接口weaver.templetecheck.ruleinterface.Rule_ClassCalendar的checkRule方法成功");
/* 42 */       return null;
/*    */     } 
/*    */   }
/*    */   
/*    */   public String replaceRule(String paramString) {
/* 47 */     Pattern pattern = null;
/* 48 */     Matcher matcher = null;
/*    */     try {
/* 50 */       pattern = Pattern.compile("(class.*?)(calendar|CALENDAR)(.*?>)");
/* 51 */       matcher = pattern.matcher(paramString);
/*    */     }
/* 53 */     catch (Exception exception) {
/* 54 */       (new BaseBean()).writeLog(exception.getMessage() + exception);
/*    */     } 
/* 56 */     while (matcher.find()) {
/* 57 */       paramString = matcher.replaceAll("$1Calendar$3");
/*    */     }
/* 59 */     (new BaseBean()).writeLog("======================调用接口weaver.templetecheck.ruleinterface.Rule_ClassCalendar的replaceRule方法成功");
/* 60 */     return paramString;
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public static String getFunctionBody(String paramString, int paramInt) {
/* 66 */     String str1 = paramString.substring(paramInt, paramString.length());
/* 67 */     String str2 = "";
/* 68 */     int i = str1.indexOf("<");
/* 69 */     int j = 0;
/* 70 */     boolean bool = false;
/* 71 */     if (i == -1) {
/* 72 */       return "";
/*    */     }
/*    */     
/* 75 */     int k = 1;
/* 76 */     for (j = i + 1; j < str1.length(); j++) {
/* 77 */       if (str1.charAt(j) == '<') {
/* 78 */         k++;
/* 79 */       } else if (str1.charAt(j) == '>') {
/* 80 */         k--;
/* 81 */       }  if (k == 0) {
/* 82 */         bool = true;
/*    */         
/*    */         break;
/*    */       } 
/*    */     } 
/* 87 */     k = j + paramInt + 1;
/* 88 */     if (bool) {
/* 89 */       str2 = paramString.substring(paramInt, k);
/*    */     }
/* 91 */     return str2;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/ruleinterface/Rule_ClassCalendar.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */