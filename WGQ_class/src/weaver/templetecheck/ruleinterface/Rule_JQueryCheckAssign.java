/*    */ package weaver.templetecheck.ruleinterface;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import weaver.general.BaseBean;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class Rule_JQueryCheckAssign
/*    */   extends CheckRuleInterface
/*    */ {
/*    */   public List<String> checkRule(String paramString) {
/* 21 */     Map<String, Object> map = analyseStr(paramString, false);
/* 22 */     return (List)map.get("checkresult");
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String replaceRule(String paramString) {
/* 30 */     return paramString;
/*    */   }
/*    */   
/*    */   private Map<String, Object> analyseStr(String paramString, boolean paramBoolean) {
/* 34 */     ArrayList<String> arrayList = new ArrayList();
/* 35 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */ 
/*    */     
/*    */     try {
/* 39 */       Map<String, String> map = findStrByRegex(paramString, "(?i)(jQuery|\\$)\\((\\\"|')#field\\d+(\\\"|')\\).next\\(.*?\\).*?jNiceChecked(\\\"|')\\)");
/* 40 */       String str = map.get("transferredsource");
/* 41 */       for (String str1 : map.keySet()) {
/* 42 */         if (!str1.contains("27C6F7EACF39D0F36DBD516866B7776E"))
/* 43 */           continue;  String str2 = map.get(str1);
/* 44 */         arrayList.add(str2);
/*    */       } 
/* 46 */       hashMap.put("checkresult", arrayList);
/* 47 */       hashMap.put("replaceresult", str);
/* 48 */     } catch (Exception exception) {
/* 49 */       (new BaseBean()).writeLog(getClass().getName() + "" + exception.getMessage() + exception);
/* 50 */       hashMap.put("checkresult", arrayList);
/* 51 */       hashMap.put("replaceresult", "");
/*    */     } 
/* 53 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/ruleinterface/Rule_JQueryCheckAssign.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */