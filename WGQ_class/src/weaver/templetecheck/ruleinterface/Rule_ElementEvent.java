/*     */ package weaver.templetecheck.ruleinterface;
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import java.io.ByteArrayInputStream;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Iterator;
/*     */ import java.util.LinkedHashMap;
/*     */ import java.util.List;
/*     */ import java.util.Set;
/*     */ import java.util.regex.Matcher;
/*     */ import java.util.regex.Pattern;
/*     */ import org.dom4j.Attribute;
/*     */ import org.dom4j.Document;
/*     */ import org.dom4j.Element;
/*     */ import org.dom4j.io.SAXReader;
/*     */ import weaver.general.BaseBean;
/*     */ 
/*     */ public class Rule_ElementEvent extends CheckRuleInterface {
/*  18 */   private BaseBean baseBean = new BaseBean();
/*     */   
/*  20 */   private String event = "onclick|onblur|onchange|onfocus|onkeydown|onkeypress|onkeyup";
/*  21 */   private String pattern = "(?i)(<[^>]*?(" + this.event + ").*?[^%]>)";
/*  22 */   private String sqoMark = "#sqo#";
/*  23 */   private String singleSqoMark = "'";
/*  24 */   private String javaTagStart = "#javaTagStart#";
/*  25 */   private String javaTagEnd = "#javaTagEnd#";
/*     */ 
/*     */ 
/*     */   
/*     */   public List<String> checkRule(String paramString) {
/*  30 */     ArrayList<String> arrayList = new ArrayList();
/*     */     try {
/*  32 */       Pattern pattern = Pattern.compile(this.pattern);
/*  33 */       Matcher matcher = pattern.matcher(paramString);
/*  34 */       String str1 = "";
/*  35 */       String str2 = "";
/*  36 */       Element element = null;
/*  37 */       Document document = null;
/*  38 */       JSONObject jSONObject1 = null;
/*  39 */       JSONObject jSONObject2 = null;
/*  40 */       while (matcher.find()) {
/*     */ 
/*     */         
/*  43 */         str1 = paramString.substring(0, matcher.start());
/*  44 */         str1 = str1.trim();
/*  45 */         str1 = str1.replace("&nbsp;", "");
/*  46 */         if (str1.endsWith("\"")) {
/*     */           continue;
/*     */         }
/*  49 */         str2 = matcher.group();
/*     */         
/*  51 */         String str = "";
/*     */         try {
/*  53 */           document = string2Document(str2);
/*  54 */           element = document.getRootElement();
/*  55 */           jSONObject1 = getAttrsJson(element);
/*  56 */           jSONObject2 = (JSONObject)jSONObject1.get("event");
/*  57 */           Set set = jSONObject2.keySet();
/*  58 */           Iterator<String> iterator = set.iterator();
/*     */           
/*  60 */           while (iterator.hasNext()) {
/*  61 */             str = iterator.next();
/*  62 */             String str3 = (String)jSONObject1.get("id");
/*  63 */             String str4 = "";
/*  64 */             if (str3.indexOf("$field") > -1) {
/*  65 */               str3 = str3.replace("$", "");
/*  66 */               str4 = getEventBindingHead("1", str, str3);
/*     */             } else {
/*     */               
/*  69 */               str = str.replaceFirst("on", "");
/*  70 */               str4 = getEventBindingHead("0", str, str3);
/*     */             } 
/*     */ 
/*     */ 
/*     */             
/*  75 */             if (paramString.indexOf(str4) > -1) {
/*     */               continue;
/*     */             }
/*     */             
/*  79 */             arrayList.add(str2);
/*     */           } 
/*  81 */         } catch (Exception exception) {
/*  82 */           exception.printStackTrace();
/*     */         }
/*     */       
/*     */       } 
/*  86 */     } catch (Exception exception) {
/*  87 */       exception.printStackTrace();
/*     */     } 
/*  89 */     return arrayList;
/*     */   }
/*     */ 
/*     */   
/*     */   public String replaceRule(String paramString) {
/*     */     try {
/*  95 */       Pattern pattern = Pattern.compile(this.pattern);
/*  96 */       Matcher matcher = pattern.matcher(paramString);
/*  97 */       Element element = null;
/*  98 */       Document document = null;
/*  99 */       String str1 = "";
/* 100 */       JSONObject jSONObject1 = new JSONObject();
/* 101 */       ArrayList<JSONObject> arrayList = new ArrayList();
/* 102 */       String str2 = "";
/* 103 */       while (matcher.find()) {
/*     */ 
/*     */ 
/*     */         
/* 107 */         str2 = paramString.substring(0, matcher.start());
/* 108 */         str2 = str2.trim();
/* 109 */         str2 = str2.replace("&nbsp;", "");
/* 110 */         if (str2.endsWith("\"")) {
/*     */           continue;
/*     */         }
/* 113 */         str1 = matcher.group();
/*     */         try {
/* 115 */           document = string2Document(str1);
/* 116 */           element = document.getRootElement();
/* 117 */           jSONObject1 = getAttrsJson(element);
/* 118 */           jSONObject1.put("matchcontent", str1);
/*     */           
/* 120 */           arrayList.add(jSONObject1);
/* 121 */         } catch (Exception exception) {
/* 122 */           this.baseBean.writeLog("###Rule_ElementEvent error:" + exception.toString());
/* 123 */           exception.printStackTrace();
/*     */         } 
/*     */       } 
/*     */ 
/*     */ 
/*     */       
/* 129 */       String str3 = "";
/* 130 */       String str4 = "";
/* 131 */       byte b1 = 0;
/* 132 */       JSONObject jSONObject2 = new JSONObject();
/* 133 */       String str5 = "";
/* 134 */       StringBuffer stringBuffer = new StringBuffer();
/* 135 */       stringBuffer.append("\r\n");
/* 136 */       stringBuffer.append("<!--e9 filechecktool auto add -->\r\n");
/* 137 */       stringBuffer.append("<script type=\"text/javascript\">\r\n");
/* 138 */       stringBuffer.append("jQuery(document).ready(function(){\r\n");
/*     */       
/* 140 */       String str6 = paramString.replace(" ", "");
/* 141 */       for (byte b2 = 0; b2 < arrayList.size(); b2++) {
/* 142 */         boolean bool = false;
/* 143 */         jSONObject1 = arrayList.get(b2);
/* 144 */         str1 = (String)jSONObject1.get("matchcontent");
/*     */         
/* 146 */         if (jSONObject1.containsKey("id")) {
/* 147 */           str3 = (String)jSONObject1.get("id");
/* 148 */           if (str3.indexOf("$field") > -1) {
/* 149 */             bool = true;
/*     */           } else {
/* 151 */             bool = false;
/*     */           } 
/*     */         } else {
/*     */           
/* 155 */           str3 = "defineid" + b2;
/* 156 */           String str = str1.replaceFirst(" ", " id=\"" + str3 + "\" ");
/* 157 */           paramString = paramString.replace(str1, str);
/*     */         } 
/* 159 */         jSONObject2 = (JSONObject)jSONObject1.get("event");
/* 160 */         Set set = jSONObject2.keySet();
/* 161 */         Iterator<String> iterator = set.iterator();
/* 162 */         while (iterator.hasNext()) {
/* 163 */           str4 = iterator.next();
/* 164 */           if (!bool) {
/* 165 */             str5 = (String)jSONObject2.get(str4);
/* 166 */             str4 = str4.replaceFirst("on", "");
/*     */             
/* 168 */             String str7 = getEventBindingHead("0", str4, str3);
/*     */             
/* 170 */             if (str6.indexOf(str7) > -1) {
/*     */               continue;
/*     */             }
/* 173 */             if (stringBuffer.toString().indexOf(str7) > -1) {
/*     */               continue;
/*     */             }
/*     */ 
/*     */             
/* 178 */             stringBuffer.append(str7 + "\r\n");
/* 179 */             stringBuffer.append("    ").append(str5).append(";\r\n");
/* 180 */             stringBuffer.append("});\r\n");
/* 181 */             b1++; continue;
/*     */           } 
/* 183 */           str5 = (String)jSONObject2.get(str4);
/* 184 */           str3 = str3.replace("$", "");
/* 185 */           String str = getEventBindingHead("1", str4, str3);
/*     */ 
/*     */ 
/*     */           
/* 189 */           if (str6.indexOf(str) > -1) {
/*     */             continue;
/*     */           }
/* 192 */           if (stringBuffer.toString().indexOf(str) > -1) {
/*     */             continue;
/*     */           }
/* 195 */           str = str + "function(){";
/* 196 */           stringBuffer.append(str + "\r\n");
/* 197 */           if (str5.indexOf("\\") > -1) {
/* 198 */             str5.replace("\\", "");
/*     */           }
/* 200 */           stringBuffer.append("    ").append(str5).append(";\r\n");
/* 201 */           stringBuffer.append("});\r\n");
/* 202 */           b1++;
/*     */         } 
/*     */       } 
/*     */ 
/*     */       
/* 207 */       stringBuffer.append("});\r\n");
/* 208 */       stringBuffer.append("</script>\r\n");
/*     */       
/* 210 */       stringBuffer.append("<!--e9 filechecktool auto add -->");
/* 211 */       if (b1 > 0)
/*     */       {
/* 213 */         if (paramString.matches("(?i)(</head>)")) {
/* 214 */           paramString = paramString.replaceFirst("(?i)(</head>)", "$0" + stringBuffer.toString());
/*     */         } else {
/* 216 */           paramString = paramString + stringBuffer.toString();
/*     */         } 
/*     */         
/* 219 */         paramString = paramString.replace(this.sqoMark, "\"");
/* 220 */         paramString = paramString.replace(this.javaTagStart, "<%");
/* 221 */         paramString = paramString.replace(this.javaTagEnd, "%>");
/*     */       }
/*     */     
/* 224 */     } catch (Exception exception) {
/* 225 */       exception.printStackTrace();
/*     */     } 
/* 227 */     return paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONObject getAttrsJson(Element paramElement) throws JSONException {
/* 237 */     String str = "";
/* 238 */     List<Attribute> list = paramElement.attributes();
/* 239 */     JSONObject jSONObject1 = new JSONObject();
/* 240 */     JSONObject jSONObject2 = new JSONObject();
/* 241 */     LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/* 242 */     for (byte b = 0; b < list.size(); b++) {
/* 243 */       Attribute attribute = list.get(b);
/* 244 */       String str1 = attribute.getName();
/* 245 */       String str2 = attribute.getValue();
/* 246 */       str1 = str1.toLowerCase();
/* 247 */       if (str1.startsWith("on")) {
/* 248 */         if (jSONObject1.get("event") == null) {
/* 249 */           jSONObject2 = new JSONObject();
/*     */         } else {
/* 251 */           jSONObject2 = (JSONObject)jSONObject1.get("event");
/*     */         } 
/* 253 */         if (this.event.indexOf(str1.toLowerCase()) > -1)
/*     */         {
/*     */           
/* 256 */           jSONObject2.put(str1, str2);
/*     */         }
/*     */         
/* 259 */         jSONObject1.put("event", jSONObject2);
/*     */       } 
/* 261 */       jSONObject1.put(str1, str2);
/*     */     } 
/* 263 */     return jSONObject1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Document string2Document(String paramString) {
/* 273 */     SAXReader sAXReader = new SAXReader();
/* 274 */     sAXReader.setEntityResolver((EntityResolver)new IgnoreDTDEntityResolver());
/* 275 */     Document document = null;
/*     */     try {
/* 277 */       String str = paramString.substring(paramString.indexOf("<") + 1, paramString.indexOf(" "));
/* 278 */       if (paramString.endsWith("/>")) {
/* 279 */         paramString = paramString.replace("/>", ">");
/*     */       }
/*     */       
/* 282 */       paramString = paramString + "</" + str + ">";
/*     */ 
/*     */       
/* 285 */       paramString = paramString.replace("\\\"", this.sqoMark);
/* 286 */       paramString = paramString.replace("<%", this.javaTagStart);
/* 287 */       paramString = paramString.replace("%>", this.javaTagEnd);
/* 288 */       document = sAXReader.read(new ByteArrayInputStream(paramString.getBytes("UTF-8")));
/* 289 */     } catch (Exception exception) {
/* 290 */       exception.printStackTrace();
/*     */     } 
/* 292 */     return document;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getEventBindingHead(String paramString1, String paramString2, String paramString3) {
/* 301 */     if ("1".equals(paramString1)) {
/* 302 */       return "WfForm.bindFieldAction(\"" + paramString2 + "\",\"" + paramString3 + "\",";
/*     */     }
/* 304 */     return "jQuery(\"#" + paramString3 + "\")." + paramString2 + "(function(){";
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/ruleinterface/Rule_ElementEvent.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */