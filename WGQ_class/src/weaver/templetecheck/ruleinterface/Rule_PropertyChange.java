/*     */ package weaver.templetecheck.ruleinterface;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.regex.Matcher;
/*     */ import java.util.regex.Pattern;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.general.BaseBean;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class Rule_PropertyChange
/*     */   extends CheckRuleInterface
/*     */ {
/*     */   public List<String> checkRule(String paramString) {
/*  21 */     String str1 = "(?i)(\\.)bind\\(.*?propertychange.*?\\)";
/*  22 */     String str2 = "(?i)(\\.)bind\\(.*?propertychange.*?,.*?function.*?\\(\\)";
/*     */     
/*  24 */     String[] arrayOfString = { str1, str2 };
/*  25 */     for (String str : arrayOfString) {
/*     */       
/*     */       try {
/*  28 */         ArrayList<String> arrayList = new ArrayList();
/*  29 */         Pattern pattern = null;
/*  30 */         Matcher matcher = null;
/*     */         try {
/*  32 */           pattern = Pattern.compile(str);
/*  33 */           matcher = pattern.matcher(paramString);
/*     */         }
/*  35 */         catch (Exception exception) {
/*  36 */           (new BaseBean()).writeLog(exception.toString());
/*     */         } 
/*  38 */         while (matcher.find()) {
/*  39 */           arrayList.add(matcher.group());
/*     */         }
/*  41 */         (new BaseBean()).writeLog("======================调用接口的方法weaver.templetecheck.ruleinterface.Rule_PropertyChange的checkRule成功");
/*     */         
/*  43 */         return arrayList;
/*  44 */       } catch (Exception exception) {
/*  45 */         exception.printStackTrace();
/*     */       } 
/*     */     } 
/*  48 */     (new BaseBean()).writeLog("======================调用接口的方法weaver.templetecheck.ruleinterface.Rule_PropertyChange的checkRule成功");
/*     */     
/*  50 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String replaceRule(String paramString) {
/*  58 */     String str1 = "document.getElementById\\(.*?\\).bind\\(.*?propertychange.*?\\)";
/*  59 */     String str2 = "\\$\\(.*?#.*?\\).bind\\(.*?propertychange.*?\\)";
/*     */     
/*  61 */     String str3 = "(?i)jQuery\\(.*?#.*?\\)\\.bind\\(.*?propertychange.*?\\)";
/*     */     
/*  63 */     Pattern pattern1 = Pattern.compile(str1);
/*  64 */     Pattern pattern2 = Pattern.compile(str2);
/*  65 */     Pattern pattern3 = Pattern.compile(str3);
/*  66 */     Matcher matcher1 = pattern1.matcher(paramString);
/*  67 */     Matcher matcher2 = pattern2.matcher(paramString);
/*  68 */     Matcher matcher3 = pattern3.matcher(paramString);
/*  69 */     boolean bool = false;
/*     */     
/*  71 */     while (matcher1.find()) {
/*  72 */       String str4 = matcher1.group().replaceFirst("document.getElementById\\(\"", "");
/*  73 */       String str5 = str4.replaceAll("\\(", "");
/*  74 */       str5 = str5.substring(str5.indexOf(",") + 1, str5.length() - 1).trim();
/*  75 */       str4 = str4.substring(0, str4.indexOf("\""));
/*  76 */       if (str4.contains("field")) {
/*  77 */         String str = "";
/*     */         
/*  79 */         if (str5.contains("function")) {
/*  80 */           str = getFunctionBody(paramString, "document.getElementById(\"" + str4 + "\")).bind(\"propertychange\",function(){");
/*     */         } else {
/*  82 */           str = str5 + "()";
/*     */         } 
/*  84 */         if (StringUtil.isNotNull(str))
/*  85 */           paramString = replaceFormField(paramString, str4, str); 
/*     */         continue;
/*     */       } 
/*  88 */       bool = true;
/*     */     } 
/*     */ 
/*     */     
/*  92 */     while (matcher2.find()) {
/*  93 */       String str4 = matcher2.group().replaceFirst("\\$\\(\"#", "");
/*     */       
/*  95 */       String str5 = str4.substring(str4.indexOf(",") + 1, str4.length() - 1).trim();
/*  96 */       str4 = str4.substring(0, str4.indexOf("\""));
/*     */       
/*  98 */       if (str4.contains("field")) {
/*  99 */         String str = "";
/* 100 */         if (str5.contains("function")) {
/*     */           
/* 102 */           str = getFunctionBody(paramString, matcher2.group() + "{");
/*     */         } else {
/* 104 */           str = str5 + "()";
/*     */         } 
/* 106 */         if (StringUtil.isNotNull(str)) {
/* 107 */           paramString = replaceFormField(paramString, str4, str);
/*     */         }
/*     */         continue;
/*     */       } 
/* 111 */       bool = true;
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 116 */     while (matcher3.find()) {
/* 117 */       String str4 = matcher3.group().replaceFirst("jQuery\\(\"#", "");
/* 118 */       String str5 = str4.replaceAll("\\(", "");
/* 119 */       str5 = str5.substring(str5.indexOf(",") + 1, str5.length() - 1).trim();
/* 120 */       str4 = str4.substring(0, str4.indexOf("\""));
/* 121 */       if (str4.contains("field")) {
/* 122 */         String str = "";
/* 123 */         if (str5.contains("function")) {
/* 124 */           str = getFunctionBody(paramString, matcher3.group() + "{");
/*     */         } else {
/* 126 */           str = str5 + "()";
/*     */         } 
/* 128 */         if (StringUtil.isNotNull(str))
/* 129 */           paramString = replaceFormField(paramString, str4, str); 
/*     */         continue;
/*     */       } 
/* 132 */       bool = true;
/*     */     } 
/*     */ 
/*     */     
/* 136 */     if (bool) {
/* 137 */       paramString = replaceNotFormField(paramString);
/*     */     }
/* 139 */     return paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getFunctionBody(String paramString1, String paramString2) {
/* 151 */     Rule_PropertyChange rule_PropertyChange = new Rule_PropertyChange();
/* 152 */     Map<String, String> map = rule_PropertyChange.findStrByTag(paramString1, paramString2, "{", "}", paramString2.indexOf("{"), true);
/* 153 */     for (String str : map.keySet()) {
/* 154 */       if (str.contains("27C6F7EACF39D0F36DBD516866B7776E")) {
/* 155 */         String str1 = map.get(str);
/* 156 */         str1 = str1.substring(str1.indexOf("function()"), str1.length());
/* 157 */         return str1;
/*     */       } 
/*     */     } 
/* 160 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static String replaceJQueryGetFiledVal(String paramString) {
/* 170 */     String str = "(\\s*jQuery\\s*)(\\(\\s*\"#field).*?(\\s*\\).val\\s*\\(\\s*\\))";
/* 171 */     Matcher matcher = null;
/* 172 */     Pattern pattern = null;
/* 173 */     pattern = Pattern.compile(str);
/* 174 */     matcher = pattern.matcher(paramString);
/* 175 */     while (matcher.find()) {
/* 176 */       String str1 = matcher.group();
/* 177 */       String str2 = str1.replaceAll("jQuery", "WfForm.getFieldValue").replaceFirst("#", "").replaceAll(".val\\(\\)", "");
/* 178 */       str1 = str1.replaceAll("\\(", ".").replaceAll("\\)", ".");
/* 179 */       paramString = paramString.replaceAll(str1, str2);
/*     */     } 
/* 181 */     return paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static String replaceFormField(String paramString1, String paramString2, String paramString3) {
/* 188 */     String[] arrayOfString = paramString1.split("</SCRIPT>|</script>");
/* 189 */     paramString3 = replaceJQueryGetFiledVal(paramString3);
/* 190 */     StringBuffer stringBuffer1 = new StringBuffer(" \n <SCRIPT> \n ");
/* 191 */     stringBuffer1.append("jQuery(document).ready(function(){ \n");
/* 192 */     stringBuffer1.append("   WfForm.bindFieldChangeEvent(" + paramString2 + "," + paramString3 + "); \n ); \n </SCRIPT> ");
/*     */ 
/*     */ 
/*     */     
/* 196 */     String str = stringBuffer1.toString();
/* 197 */     if (paramString1.contains(str)) {
/* 198 */       return paramString1;
/*     */     }
/* 200 */     StringBuffer stringBuffer2 = new StringBuffer();
/* 201 */     if (arrayOfString.length > 0) {
/* 202 */       for (byte b = 0; b < arrayOfString.length; b++) {
/*     */         
/* 204 */         if (b == arrayOfString.length - 1) {
/* 205 */           stringBuffer2.append(arrayOfString[b] + "\n" + str);
/*     */         } else {
/* 207 */           stringBuffer2.append(arrayOfString[b] + " </SCRIPT>\n");
/*     */         } 
/*     */       } 
/*     */     }
/* 211 */     (new BaseBean()).writeLog("======================调用接口weaver.templetecheck.ruleinterface.Rule_PropertyChangeReplace中替换表单字段，replaceFormField()函数");
/* 212 */     return stringBuffer2.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static String replaceNotFormField(String paramString) {
/* 221 */     String str1 = "bind\\(.*?propertychange.*?,";
/* 222 */     String str2 = "bindPropertyChange(";
/*     */     
/* 224 */     Pattern pattern = null;
/* 225 */     Matcher matcher = null;
/*     */     try {
/* 227 */       pattern = Pattern.compile(str1);
/* 228 */       matcher = pattern.matcher(paramString);
/*     */     }
/* 230 */     catch (Exception exception) {
/* 231 */       (new BaseBean()).writeLog(exception.toString());
/*     */     } 
/* 233 */     while (matcher.find()) {
/* 234 */       paramString = matcher.replaceAll(str2);
/*     */     }
/* 236 */     (new BaseBean()).writeLog("======================调用接口weaver.templetecheck.ruleinterface.Rule_PropertyChangeReplace中替换非表单字段，replaceNotFormField()函数");
/* 237 */     return paramString;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/ruleinterface/Rule_PropertyChange.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */