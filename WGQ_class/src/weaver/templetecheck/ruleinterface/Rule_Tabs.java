/*    */ package weaver.templetecheck.ruleinterface;
/*    */ 
/*    */ import com.weaver.general.BaseBean;
/*    */ import java.util.ArrayList;
/*    */ import java.util.List;
/*    */ import java.util.regex.Matcher;
/*    */ import java.util.regex.Pattern;
/*    */ import weaver.general.BaseBean;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class Rule_Tabs
/*    */   extends CheckRuleInterface
/*    */ {
/*    */   public List<String> checkRule(String paramString) {
/* 20 */     ArrayList<String> arrayList = new ArrayList();
/*    */     try {
/* 22 */       Pattern pattern = null;
/* 23 */       Matcher matcher = null;
/*    */       try {
/* 25 */         pattern = Pattern.compile("(?i)(\\$|jQuery).*?\\.Tabs\\s*?\\(\\s*?\\{[\\s\\S]*?}\\s*?\\)\\s*?;");
/* 26 */         matcher = pattern.matcher(paramString);
/* 27 */       } catch (Exception exception) {
/* 28 */         (new BaseBean()).writeLog(exception.toString());
/* 29 */         exception.printStackTrace();
/*    */       } 
/* 31 */       while (matcher.find()) {
/* 32 */         arrayList.add(matcher.group());
/*    */       }
/* 34 */       (new BaseBean()).writeLog("======================调用接口的方法weaver.templetecheck.ruleinterface.Rule_Tabs的checkRule成功");
/* 35 */       return arrayList;
/* 36 */     } catch (Exception exception) {
/* 37 */       exception.printStackTrace();
/*    */       
/* 39 */       (new BaseBean()).writeLog("======================调用接口的方法weaver.templetecheck.ruleinterface.Rule_Tabs的checkRule失败");
/* 40 */       return null;
/*    */     } 
/*    */   }
/*    */   
/*    */   public String replaceRule(String paramString) {
/* 45 */     return paramString;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/ruleinterface/Rule_Tabs.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */