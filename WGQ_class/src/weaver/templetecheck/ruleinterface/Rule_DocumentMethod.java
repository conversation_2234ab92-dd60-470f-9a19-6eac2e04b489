/*    */ package weaver.templetecheck.ruleinterface;
/*    */ import java.util.HashMap;
/*    */ import java.util.Iterator;
/*    */ import java.util.Map;
/*    */ import java.util.Set;
/*    */ 
/*    */ public class Rule_DocumentMethod extends CheckRuleInterface {
/*  8 */   String pattern = "document.write\\(";
/*    */ 
/*    */ 
/*    */   
/*    */   public List<String> checkRule(String paramString) {
/* 13 */     ArrayList<String> arrayList = new ArrayList();
/*    */     try {
/* 15 */       String str = "document.write(";
/* 16 */       HashMap hashMap = (HashMap)findStrByTag(paramString, str, "(", ")", str.indexOf("("), true);
/* 17 */       Set set = hashMap.entrySet();
/* 18 */       Iterator<Map.Entry> iterator = set.iterator();
/* 19 */       while (iterator.hasNext()) {
/* 20 */         Map.Entry entry = iterator.next();
/* 21 */         String str1 = (String)entry.getKey();
/* 22 */         if (str1.indexOf("transferredsource") > -1) {
/*    */           continue;
/*    */         }
/* 25 */         String str2 = str1.substring(str1.indexOf("_") + 1, str1.length());
/* 26 */         int i = Integer.valueOf(str2).intValue();
/* 27 */         boolean bool = isInAnnotation(paramString, i);
/* 28 */         if (!bool) {
/* 29 */           arrayList.add(entry.getValue());
/*    */         }
/*    */       }
/*    */     
/* 33 */     } catch (Exception exception) {
/* 34 */       exception.printStackTrace();
/*    */     } 
/* 36 */     return arrayList;
/*    */   }
/*    */ 
/*    */   
/*    */   public String replaceRule(String paramString) {
/*    */     try {
/* 42 */       String str1 = "document.write(";
/* 43 */       HashMap hashMap = (HashMap)findStrByTag(paramString, str1, "(", ")", str1.indexOf("("), true);
/* 44 */       Set set = hashMap.entrySet();
/* 45 */       Iterator<Map.Entry> iterator = set.iterator();
/* 46 */       String str2 = "";
/* 47 */       String str3 = "";
/* 48 */       int i = 0;
/* 49 */       while (iterator.hasNext()) {
/* 50 */         Map.Entry entry = iterator.next();
/* 51 */         String str4 = (String)entry.getKey();
/* 52 */         String str5 = (String)entry.getValue();
/* 53 */         String str6 = this.notesStart + str5 + this.notesEnd;
/* 54 */         if (str4.indexOf("transferredsource") > -1) {
/*    */           continue;
/*    */         }
/* 57 */         String str7 = str4.substring(str4.indexOf("_") + 1, str4.length());
/* 58 */         int j = Integer.valueOf(str7).intValue();
/* 59 */         boolean bool = isInAnnotation(paramString, j + i);
/* 60 */         if (!bool) {
/* 61 */           str2 = paramString.substring(0, j + i);
/* 62 */           str3 = paramString.substring(j + i, paramString.length());
/* 63 */           str3 = replaceFirst(str3, (String)entry.getValue(), str6);
/* 64 */           paramString = str2 + str3;
/* 65 */           i = i + this.notesStart.length() + this.notesEnd.length();
/*    */         }
/*    */       
/*    */       } 
/* 69 */     } catch (Exception exception) {
/* 70 */       exception.printStackTrace();
/*    */     } 
/* 72 */     return paramString;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/ruleinterface/Rule_DocumentMethod.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */