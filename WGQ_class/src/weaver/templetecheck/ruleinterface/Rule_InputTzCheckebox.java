/*    */ package weaver.templetecheck.ruleinterface;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import java.util.regex.Matcher;
/*    */ import java.util.regex.Pattern;
/*    */ import weaver.general.BaseBean;
/*    */ 
/*    */ 
/*    */ public class Rule_InputTzCheckebox
/*    */   extends CheckRuleInterface
/*    */ {
/*    */   public List<String> checkRule(String paramString) {
/* 16 */     Map<String, Object> map = analyseStr(paramString, false);
/* 17 */     return (List)map.get("checkresult");
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public String replaceRule(String paramString) {
/* 23 */     return paramString;
/*    */   }
/*    */   
/*    */   private Map<String, Object> analyseStr(String paramString, boolean paramBoolean) {
/* 27 */     ArrayList<String> arrayList = new ArrayList();
/* 28 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */ 
/*    */ 
/*    */     
/*    */     try {
/* 33 */       Map<String, String> map = findStrByTag(paramString, "<input", "<", ">", "<input".indexOf("<"), true);
/* 34 */       String str = map.get("transferredsource");
/* 35 */       for (String str1 : map.keySet()) {
/* 36 */         if (!str1.contains("27C6F7EACF39D0F36DBD516866B7776E"))
/* 37 */           continue;  String str2 = map.get(str1);
/* 38 */         String str3 = str2.replaceAll("%>", "[!!!!!!]");
/* 39 */         Pattern pattern = Pattern.compile("<input[\\s\\S]*?tzCheckbox=.{0,5}true[\\s\\S]*?>");
/* 40 */         Matcher matcher = pattern.matcher(str3);
/* 41 */         if (matcher.find()) {
/* 42 */           arrayList.add(matcher.group().replaceAll("\\[!!!!!!\\]", "%>")); continue;
/*    */         } 
/* 44 */         if (paramBoolean) {
/* 45 */           str = str.replace(str1, str2);
/*    */         }
/*    */       } 
/*    */       
/* 49 */       hashMap.put("checkresult", arrayList);
/* 50 */       hashMap.put("replaceresult", str);
/* 51 */     } catch (Exception exception) {
/* 52 */       (new BaseBean()).writeLog(getClass().getName() + "" + exception.getMessage() + exception);
/* 53 */       hashMap.put("checkresult", arrayList);
/* 54 */       hashMap.put("replaceresult", "");
/*    */     } 
/* 56 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/ruleinterface/Rule_InputTzCheckebox.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */