/*     */ package weaver.templetecheck.ruleinterface;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ import java.util.regex.Matcher;
/*     */ import java.util.regex.Pattern;
/*     */ import weaver.general.BaseBean;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class Rule_ClassTableSTYLE7
/*     */   extends CheckRuleInterface
/*     */ {
/*     */   public List<String> checkRule(String paramString) {
/*     */     try {
/*  21 */       ArrayList<String> arrayList = new ArrayList();
/*  22 */       for (byte b = 0; b < paramString.length(); b++) {
/*  23 */         if (paramString.regionMatches(true, b, "<table", 0, "<table".length())) {
/*  24 */           String str = getFunctionBody(paramString, b);
/*  25 */           if (!checkContentPass(str)) {
/*  26 */             arrayList.add(str);
/*     */           }
/*     */         } 
/*     */       } 
/*  30 */       (new BaseBean()).writeLog("======================调用接口weaver.templetecheck.ruleinterface.Rule_ClassTableSTYLE7的checkRule方法成功");
/*  31 */       return arrayList;
/*  32 */     } catch (Exception exception) {
/*  33 */       exception.printStackTrace();
/*     */       
/*  35 */       (new BaseBean()).writeLog("======================调用接口weaver.templetecheck.ruleinterface.Rule_ClassTableSTYLE7的checkRule方法成功");
/*  36 */       return null;
/*     */     } 
/*     */   }
/*     */   
/*     */   public String replaceRule(String paramString) {
/*  41 */     Pattern pattern = null;
/*  42 */     Matcher matcher = null;
/*     */     try {
/*  44 */       pattern = Pattern.compile("(?i)(class.*?)table.*?STYLE7");
/*  45 */       matcher = pattern.matcher(paramString);
/*     */     }
/*  47 */     catch (Exception exception) {
/*  48 */       (new BaseBean()).writeLog(exception.getMessage() + exception);
/*     */     } 
/*  50 */     while (matcher.find()) {
/*  51 */       paramString = matcher.replaceAll("$1STYLE7");
/*     */     }
/*  53 */     (new BaseBean()).writeLog("======================调用接口weaver.templetecheck.ruleinterface.Rule_ClassTableSTYLE7的replaceRule方法成功");
/*  54 */     return paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getFunctionBody(String paramString, int paramInt) {
/*  60 */     String str1 = paramString.substring(paramInt, paramString.length());
/*  61 */     String str2 = "";
/*  62 */     int i = str1.indexOf("<");
/*  63 */     int j = 0;
/*  64 */     boolean bool = false;
/*  65 */     if (i == -1) {
/*  66 */       return "";
/*     */     }
/*     */     
/*  69 */     int k = 1;
/*  70 */     for (j = i + 1; j < str1.length(); j++) {
/*  71 */       if (str1.charAt(j) == '<') {
/*  72 */         k++;
/*  73 */       } else if (str1.charAt(j) == '>') {
/*  74 */         k--;
/*  75 */       }  if (k == 0) {
/*  76 */         bool = true;
/*     */         
/*     */         break;
/*     */       } 
/*     */     } 
/*  81 */     k = j + paramInt + 1;
/*  82 */     if (bool) {
/*  83 */       str2 = paramString.substring(paramInt, k);
/*     */     }
/*  85 */     return str2;
/*     */   }
/*     */ 
/*     */   
/*     */   private static boolean checkContentPass(String paramString) {
/*  90 */     boolean bool = true;
/*  91 */     Pattern pattern = null;
/*  92 */     Matcher matcher = null;
/*     */     try {
/*  94 */       pattern = Pattern.compile("(?i)(class.*?)table.*?STYLE7");
/*  95 */       matcher = pattern.matcher(paramString);
/*     */     }
/*  97 */     catch (Exception exception) {
/*  98 */       (new BaseBean()).writeLog(exception.getMessage() + exception);
/*     */     } 
/* 100 */     if (matcher.find()) {
/* 101 */       return false;
/*     */     }
/* 103 */     return bool;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/ruleinterface/Rule_ClassTableSTYLE7.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */