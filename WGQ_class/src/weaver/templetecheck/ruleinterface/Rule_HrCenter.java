/*     */ package weaver.templetecheck.ruleinterface;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ import java.util.regex.Matcher;
/*     */ import java.util.regex.Pattern;
/*     */ import weaver.general.BaseBean;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class Rule_HrCenter
/*     */   extends CheckRuleInterface
/*     */ {
/*     */   public List<String> checkRule(String paramString) {
/*     */     try {
/*  22 */       ArrayList<String> arrayList = new ArrayList();
/*  23 */       for (byte b = 0; b < paramString.length(); b++) {
/*  24 */         if (paramString.regionMatches(true, b, "<hr", 0, "<hr".length())) {
/*  25 */           String str = getFunctionBody(paramString, b);
/*  26 */           if (!checkContentPass(str)) {
/*  27 */             arrayList.add(str);
/*     */           }
/*     */         } 
/*     */       } 
/*  31 */       (new BaseBean()).writeLog("======================调用接口的方法weaver.templetecheck.ruleinterface.Rule_HrCenter的checkRule成功");
/*  32 */       return arrayList;
/*  33 */     } catch (Exception exception) {
/*  34 */       exception.printStackTrace();
/*     */       
/*  36 */       (new BaseBean()).writeLog("======================调用接口的方法weaver.templetecheck.ruleinterface.Rule_HrCenter的checkRule成功");
/*  37 */       return null;
/*     */     } 
/*     */   }
/*     */   
/*     */   public String replaceRule(String paramString) {
/*  42 */     Pattern pattern = null;
/*  43 */     Matcher matcher = null;
/*     */     try {
/*  45 */       pattern = Pattern.compile("(?i)(<hr.*?)(text-align[ ]*:[ ]*center[ ]*[;]?)(.*?>)");
/*  46 */       matcher = pattern.matcher(paramString);
/*     */     }
/*  48 */     catch (Exception exception) {
/*  49 */       (new BaseBean()).writeLog(exception.getMessage() + exception);
/*     */     } 
/*  51 */     while (matcher.find()) {
/*  52 */       paramString = matcher.replaceAll("$1$3");
/*     */     }
/*  54 */     (new BaseBean()).writeLog("======================调用接口的方法weaver.templetecheck.ruleinterface.Rule_HrCenter的replaceRule成功");
/*  55 */     return paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getFunctionBody(String paramString, int paramInt) {
/*  61 */     String str1 = paramString.substring(paramInt, paramString.length());
/*  62 */     String str2 = "";
/*  63 */     int i = str1.indexOf("<");
/*  64 */     int j = 0;
/*  65 */     boolean bool = false;
/*  66 */     if (i == -1) {
/*  67 */       return "";
/*     */     }
/*     */     
/*  70 */     int k = 1;
/*  71 */     for (j = i + 1; j < str1.length(); j++) {
/*  72 */       if (str1.charAt(j) == '<') {
/*  73 */         k++;
/*  74 */       } else if (str1.charAt(j) == '>') {
/*  75 */         k--;
/*  76 */       }  if (k == 0) {
/*  77 */         bool = true;
/*     */         
/*     */         break;
/*     */       } 
/*     */     } 
/*  82 */     k = j + paramInt + 1;
/*  83 */     if (bool) {
/*  84 */       str2 = paramString.substring(paramInt, k);
/*     */     }
/*  86 */     return str2;
/*     */   }
/*     */ 
/*     */   
/*     */   private static boolean checkContentPass(String paramString) {
/*  91 */     boolean bool = true;
/*  92 */     Pattern pattern = null;
/*  93 */     Matcher matcher = null;
/*     */     try {
/*  95 */       pattern = Pattern.compile("(?i)(<hr.*?)(text-align[ ]*:[ ]*center[ ]*[;]?)(.*?>)");
/*  96 */       matcher = pattern.matcher(paramString);
/*     */     }
/*  98 */     catch (Exception exception) {
/*  99 */       (new BaseBean()).writeLog(exception.getMessage() + exception);
/*     */     } 
/* 101 */     if (matcher.find()) {
/* 102 */       return false;
/*     */     }
/* 104 */     return bool;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/ruleinterface/Rule_HrCenter.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */