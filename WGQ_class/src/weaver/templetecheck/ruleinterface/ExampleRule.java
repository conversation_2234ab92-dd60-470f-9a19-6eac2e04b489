/*    */ package weaver.templetecheck.ruleinterface;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.Date;
/*    */ import java.util.List;
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.general.ThreadVarLanguage;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ExampleRule
/*    */   extends CheckRuleInterface
/*    */ {
/*    */   public List<String> checkRule(String paramString) {
/* 20 */     ArrayList<String> arrayList = new ArrayList();
/* 21 */     (new BaseBean()).writeLog("======================调用接口weaver.templetecheck.ruleinterface.ExampleRule的checkRule方法成功");
/* 22 */     if (paramString.trim().length() == 0) {
/* 23 */       return arrayList;
/*    */     }
/*    */     
/*    */     try {
/* 27 */       while (paramString.indexOf("weaver-test-wenc") > -1) {
/* 28 */         arrayList.add("" + SystemEnv.getHtmlLabelName(10004224, ThreadVarLanguage.getLang()) + "weaver-test-wenc" + SystemEnv.getHtmlLabelName(27903, ThreadVarLanguage.getLang()) + "");
/* 29 */         paramString = paramString.substring(paramString.indexOf("weaver-test-wenc") + "weaver-test-wenc".length(), paramString.length());
/*    */       } 
/* 31 */       return arrayList;
/* 32 */     } catch (Exception exception) {
/* 33 */       exception.printStackTrace();
/*    */       
/* 35 */       return null;
/*    */     } 
/*    */   }
/*    */   
/*    */   public String replaceRule(String paramString) {
/* 40 */     (new BaseBean()).writeLog("======================调用接口weaver.templetecheck.ruleinterface.ExampleRule的方法replaceRule成功");
/*    */     
/* 42 */     if (paramString.indexOf("weaver-test-wenc") > -1) {
/* 43 */       paramString = paramString.replace("weaver-test-wenc", "weaver-test-success-wenc-" + (new Date()).toString());
/*    */     }
/*    */     
/* 46 */     if (paramString.trim().length() == 0) return null; 
/* 47 */     (new BaseBean()).writeLog("filestring=======" + paramString);
/* 48 */     return paramString;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/ruleinterface/ExampleRule.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */