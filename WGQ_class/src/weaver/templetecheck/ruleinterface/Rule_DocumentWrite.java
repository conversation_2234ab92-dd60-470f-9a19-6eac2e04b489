/*    */ package weaver.templetecheck.ruleinterface;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.List;
/*    */ import java.util.regex.Matcher;
/*    */ import java.util.regex.Pattern;
/*    */ import weaver.general.BaseBean;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class Rule_DocumentWrite
/*    */   extends CheckRuleInterface
/*    */ {
/*    */   public List<String> checkRule(String paramString) {
/*    */     try {
/* 19 */       ArrayList<String> arrayList = new ArrayList();
/* 20 */       Pattern pattern = null;
/* 21 */       Matcher matcher = null;
/*    */       try {
/* 23 */         pattern = Pattern.compile("document(.?\\))?\\.(write\\(.*?\\)|getElementById\\(.*?\\))");
/* 24 */         matcher = pattern.matcher(paramString);
/*    */       }
/* 26 */       catch (Exception exception) {
/* 27 */         (new BaseBean()).writeLog(exception.getMessage() + exception);
/*    */       } 
/* 29 */       while (matcher.find()) {
/* 30 */         arrayList.add(matcher.group());
/*    */       }
/* 32 */       (new BaseBean()).writeLog("======================调用接口的方法weaver.templetecheck.ruleinterface.Rule_DocumentWrite的checkRule成功");
/* 33 */       return arrayList;
/* 34 */     } catch (Exception exception) {
/* 35 */       exception.printStackTrace();
/*    */       
/* 37 */       (new BaseBean()).writeLog("======================调用接口的方法weaver.templetecheck.ruleinterface.Rule_DocumentWrite的checkRule失败");
/* 38 */       return null;
/*    */     } 
/*    */   }
/*    */   
/*    */   public String replaceRule(String paramString) {
/* 43 */     return paramString;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/ruleinterface/Rule_DocumentWrite.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */