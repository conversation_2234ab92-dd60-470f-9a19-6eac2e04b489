/*    */ package weaver.templetecheck.ruleinterface;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.List;
/*    */ import weaver.general.BaseBean;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class Rule_DeleterowReturn
/*    */   extends CheckRuleInterface
/*    */ {
/*    */   public List<String> checkRule(String paramString) {
/*    */     try {
/* 19 */       paramString = paramString.replaceAll(" +", " ");
/*    */       
/* 21 */       ArrayList<String> arrayList = new ArrayList();
/* 22 */       for (byte b = 0; b < paramString.length(); b++) {
/* 23 */         if (paramString.regionMatches(true, b, "function deleterow", 0, "function deleterow".length())) {
/* 24 */           String str = getFunctionBody(paramString, b);
/* 25 */           if (!checkContentPass(str)) {
/* 26 */             arrayList.add(str);
/*    */           }
/*    */         } 
/*    */       } 
/* 30 */       (new BaseBean()).writeLog("======================调用接口的方法weaver.templetecheck.ruleinterface.Rule_DeleterowReturn的checkRule成功");
/* 31 */       return arrayList;
/* 32 */     } catch (Exception exception) {
/* 33 */       exception.printStackTrace();
/*    */       
/* 35 */       (new BaseBean()).writeLog("======================调用接口的方法weaver.templetecheck.ruleinterface.Rule_DeleterowReturn的checkRule成功");
/* 36 */       return null;
/*    */     } 
/*    */   }
/*    */ 
/*    */   
/*    */   public String replaceRule(String paramString) {
/* 42 */     return paramString;
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public static String getFunctionBody(String paramString, int paramInt) {
/* 48 */     String str1 = paramString.substring(paramInt, paramString.length());
/* 49 */     String str2 = "";
/* 50 */     int i = str1.indexOf("{");
/* 51 */     int j = 0;
/* 52 */     boolean bool = false;
/* 53 */     if (i == -1) {
/* 54 */       return "";
/*    */     }
/*    */     
/* 57 */     int k = 1;
/* 58 */     for (j = i + 1; j < str1.length(); j++) {
/* 59 */       if (str1.charAt(j) == '{') {
/* 60 */         k++;
/* 61 */       } else if (str1.charAt(j) == '}') {
/* 62 */         k--;
/* 63 */       }  if (k == 0) {
/* 64 */         bool = true;
/*    */         
/*    */         break;
/*    */       } 
/*    */     } 
/* 69 */     k = j + paramInt + 1;
/* 70 */     if (bool) {
/* 71 */       str2 = paramString.substring(paramInt, k);
/*    */     }
/* 73 */     return str2;
/*    */   }
/*    */ 
/*    */   
/*    */   private static boolean checkContentPass(String paramString) {
/* 78 */     boolean bool = true;
/* 79 */     if (paramString == null) return bool; 
/* 80 */     paramString = paramString.replaceAll(" +", " ").toLowerCase();
/* 81 */     if (!paramString.contains("return false")) bool = false; 
/* 82 */     return bool;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/ruleinterface/Rule_DeleterowReturn.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */