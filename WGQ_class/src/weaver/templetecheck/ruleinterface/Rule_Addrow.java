/*    */ package weaver.templetecheck.ruleinterface;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import java.util.regex.Matcher;
/*    */ import java.util.regex.Pattern;
/*    */ import weaver.general.BaseBean;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class Rule_Addrow
/*    */   extends CheckRuleInterface
/*    */ {
/*    */   public List<String> checkRule(String paramString) {
/* 21 */     Map<String, Object> map = analyseStr(paramString, false);
/* 22 */     return (List)map.get("checkresult");
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public String replaceRule(String paramString) {
/* 28 */     Map<String, Object> map = analyseStr(paramString, true);
/* 29 */     return (String)map.get("replaceresult");
/*    */   }
/*    */ 
/*    */   
/*    */   private Map<String, Object> analyseStr(String paramString, boolean paramBoolean) {
/* 34 */     ArrayList<String> arrayList = new ArrayList();
/* 35 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */ 
/*    */ 
/*    */     
/*    */     try {
/* 40 */       Map<String, String> map = findStrByRegex(paramString, "(?i)(<button.*?addrow(\\d+)\\s*?\\(\\s*?\\d\\s*?\\).*?(</button>|/>))");
/* 41 */       String str = map.get("transferredsource");
/* 42 */       for (String str1 : map.keySet()) {
/* 43 */         if (!str1.contains("27C6F7EACF39D0F36DBD516866B7776E"))
/* 44 */           continue;  String str2 = map.get(str1);
/* 45 */         String str3 = "";
/* 46 */         String str4 = "";
/* 47 */         boolean bool = true;
/*    */         
/*    */         try {
/* 50 */           Pattern pattern = Pattern.compile("(?i)addrow(\\d+)");
/* 51 */           Matcher matcher = pattern.matcher(str2);
/* 52 */           if (matcher.find()) {
/* 53 */             str3 = matcher.group(1);
/*    */ 
/*    */ 
/*    */ 
/*    */             
/* 58 */             pattern = Pattern.compile("(?i)id=\\s*?(('\\$addbutton" + str3 + "\\$')|(\"\\$addbutton" + str3 + "\\$\"))");
/* 59 */             matcher = pattern.matcher(str2);
/* 60 */             if (!matcher.find()) {
/* 61 */               str4 = str4 + " id=\"$addbutton" + str3 + "$\" ";
/* 62 */               bool = false;
/*    */             } 
/*    */             
/* 65 */             pattern = Pattern.compile("(?i)name=\\s*?(('addbutton" + str3 + "')|(\"addbutton" + str3 + "\"))");
/* 66 */             matcher = pattern.matcher(str2);
/* 67 */             if (!matcher.find()) {
/* 68 */               bool = false;
/* 69 */               str4 = str4 + " name=\"addbutton" + str3 + "\" ";
/*    */             } 
/*    */ 
/*    */ 
/*    */             
/* 74 */             if (!bool) {
/* 75 */               arrayList.add(str2);
/*    */             }
/* 77 */             if (!bool && paramBoolean) {
/* 78 */               str = str.replace(str1, str2.replaceAll("(?i)<button", "<button " + str4.replace("$", "\\$"))); continue;
/*    */             } 
/* 80 */             str = str.replace(str1, str2);
/*    */           } 
/*    */         } catch (Exception exception) {}
/* 83 */       }  hashMap.put("checkresult", arrayList);
/* 84 */       hashMap.put("replaceresult", str);
/* 85 */     } catch (Exception exception) {
/* 86 */       (new BaseBean()).writeLog(getClass().getName() + "" + exception.getMessage() + exception);
/* 87 */       hashMap.put("checkresult", arrayList);
/* 88 */       hashMap.put("replaceresult", paramString);
/*    */     } 
/* 90 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/ruleinterface/Rule_Addrow.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */