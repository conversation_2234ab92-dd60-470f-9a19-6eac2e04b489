/*     */ package weaver.templetecheck.ruleinterface;
/*     */ 
/*     */ import java.util.HashMap;
/*     */ import java.util.LinkedHashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.regex.Matcher;
/*     */ import java.util.regex.Pattern;
/*     */ import weaver.general.BaseBean;
/*     */ 
/*     */ 
/*     */ 
/*     */ public abstract class CheckRuleInterface
/*     */ {
/*     */   public static final String TAG = "27C6F7EACF39D0F36DBD516866B7776E";
/*     */   public static final String TAGRND = "END";
/*  17 */   String notesStart = "/*#e9 filechecktool auto annotation#\r\n";
/*  18 */   String notesEnd = "\r\n#e9 filechecktool auto annotation#*/";
/*  19 */   String notes = "//#e9 filechecktool auto annotation# ";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   abstract List<String> checkRule(String paramString);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   abstract String replaceRule(String paramString);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   Map<String, String> findStrByRegex(String paramString1, String paramString2) {
/*  40 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  41 */     String str = paramString1;
/*     */ 
/*     */     
/*     */     try {
/*  45 */       Pattern pattern = Pattern.compile(paramString2);
/*  46 */       Matcher matcher = pattern.matcher(paramString1);
/*  47 */       byte b = 0;
/*  48 */       while (matcher.find()) {
/*  49 */         b++;
/*  50 */         hashMap.put("27C6F7EACF39D0F36DBD516866B7776E_" + b + "_" + "END", matcher.group());
/*  51 */         str = replaceFirst(str, matcher.group(), "27C6F7EACF39D0F36DBD516866B7776E_" + b + "_" + "END");
/*     */       } 
/*  53 */     } catch (Exception exception) {
/*  54 */       exception.printStackTrace();
/*     */     } 
/*  56 */     hashMap.put("transferredsource", str);
/*  57 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   Map<String, String> findStrByTag(String paramString1, String paramString2, String paramString3, String paramString4, int paramInt, boolean paramBoolean) {
/*  71 */     LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/*     */     
/*     */     try {
/*  74 */       if (!paramString2.substring(paramInt, paramInt + paramString3.length()).equalsIgnoreCase(paramString3) || (paramBoolean && !paramString2.substring(paramInt, paramInt + paramString3.length()).equals(paramString3))) {
/*  75 */         (new BaseBean()).writeLog(getClass().getName() + "===调用接口的方法findStrByTag传递参数不合法：匹配的字符串tagStr:" + paramString2 + ",开始标签starttag：" + paramString3 + ",结束标签endtag：" + paramString4 + "，希望从tagStr的角标tagindex:" + paramInt + "开始匹配");
/*  76 */         return (Map)linkedHashMap;
/*     */       } 
/*  78 */     } catch (Exception exception) {
/*  79 */       (new BaseBean()).writeLog(getClass().getName() + "===调用接口的方法findStrByTag传递参数不合法：匹配的字符串tagStr:" + paramString2 + ",开始标签starttag：" + paramString3 + ",结束标签endtag：" + paramString4 + "，希望从tagStr的角标tagindex:" + paramInt + "开始匹配");
/*  80 */       return (Map)linkedHashMap;
/*     */     } 
/*     */     
/*  83 */     String str = "";
/*  84 */     for (int i = 0; i < paramString1.length(); i++) {
/*  85 */       if (paramString1.regionMatches(paramBoolean, i, paramString2, 0, paramString2.length()))
/*  86 */       { int j = getTagEndIndex(paramString1, i, paramString3, paramString4, paramInt);
/*  87 */         if (j == -1) {
/*  88 */           str = str + paramString1.charAt(i);
/*     */         } else {
/*     */           
/*  91 */           linkedHashMap.put("27C6F7EACF39D0F36DBD516866B7776E_" + i + "_" + "END", paramString1.substring(i, j));
/*  92 */           str = str + "27C6F7EACF39D0F36DBD516866B7776E_" + i + "_" + "END";
/*  93 */           i = j - 1;
/*     */         }  }
/*  95 */       else { str = str + paramString1.charAt(i); }
/*     */     
/*     */     } 
/*  98 */     linkedHashMap.put("transferredsource", str);
/*  99 */     return (Map)linkedHashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private int getTagEndIndex(String paramString1, int paramInt1, String paramString2, String paramString3, int paramInt2) {
/* 112 */     String str = paramString1.substring(paramInt1, paramString1.length());
/* 113 */     int i = paramInt2;
/*     */     
/* 115 */     int k = -1;
/* 116 */     boolean bool = false;
/* 117 */     boolean bool1 = false;
/*     */     
/* 119 */     byte b = 1; int j;
/* 120 */     for (j = i + paramString2.length(); j < str.length(); j++) {
/* 121 */       if (j + paramString2.length() <= str.length() && str.substring(j, j + paramString2.length()).equalsIgnoreCase(paramString2)) {
/* 122 */         b++;
/* 123 */       } else if (j + paramString3.length() <= str.length() && (str.substring(j, j + paramString3.length()).equalsIgnoreCase(paramString3) || str.substring(j, j + paramString3.length() + 1).equalsIgnoreCase(paramString3 + ";"))) {
/* 124 */         bool1 = str.substring(j, j + paramString3.length() + 1).equalsIgnoreCase(paramString3 + ";");
/* 125 */         b--;
/*     */       } 
/* 127 */       if (b == 0) {
/* 128 */         bool = true;
/*     */         break;
/*     */       } 
/*     */     } 
/* 132 */     if (bool) {
/* 133 */       k = j + paramInt1 + paramString3.length();
/* 134 */       if (bool1) {
/* 135 */         k++;
/*     */       }
/*     */     } 
/* 138 */     return k;
/*     */   }
/*     */ 
/*     */   
/*     */   public static String replaceFirst(String paramString1, String paramString2, String paramString3) {
/* 143 */     return replace(paramString1, paramString2, paramString3, 1);
/*     */   }
/*     */ 
/*     */   
/*     */   public static String replace(String paramString1, String paramString2, String paramString3, int paramInt) {
/* 148 */     if (!isEmpty(paramString1) && !isEmpty(paramString2) && paramString3 != null && paramInt != 0) {
/* 149 */       int i = 0;
/* 150 */       int j = paramString1.indexOf(paramString2, i);
/* 151 */       if (j == -1) {
/* 152 */         return paramString1;
/*     */       }
/* 154 */       int k = paramString2.length();
/* 155 */       int m = paramString3.length() - k;
/* 156 */       m = (m < 0) ? 0 : m;
/* 157 */       m *= (paramInt < 0) ? 16 : ((paramInt > 64) ? 64 : paramInt);
/*     */       
/*     */       StringBuffer stringBuffer;
/* 160 */       for (stringBuffer = new StringBuffer(paramString1.length() + m); j != -1; j = paramString1.indexOf(paramString2, i)) {
/* 161 */         stringBuffer.append(paramString1.substring(i, j)).append(paramString3);
/* 162 */         i = j + k;
/* 163 */         paramInt--;
/* 164 */         if (paramInt == 0) {
/*     */           break;
/*     */         }
/*     */       } 
/*     */       
/* 169 */       stringBuffer.append(paramString1.substring(i));
/* 170 */       return stringBuffer.toString();
/*     */     } 
/*     */     
/* 173 */     return paramString1;
/*     */   }
/*     */ 
/*     */   
/*     */   public static int countMatches(String paramString1, String paramString2) {
/* 178 */     if (!isEmpty(paramString1) && !isEmpty(paramString2)) {
/* 179 */       byte b = 0;
/*     */       
/* 181 */       for (int i = 0; (i = paramString1.indexOf(paramString2, i)) != -1; i += paramString2.length()) {
/* 182 */         b++;
/*     */       }
/*     */       
/* 185 */       return b;
/*     */     } 
/* 187 */     return 0;
/*     */   }
/*     */ 
/*     */   
/*     */   public static boolean isEmpty(String paramString) {
/* 192 */     return (paramString == null || paramString.length() == 0);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isInAnnotation(String paramString, int paramInt) {
/* 202 */     paramString = paramString.substring(0, paramInt);
/* 203 */     int i = paramString.lastIndexOf("*/");
/* 204 */     int j = paramString.lastIndexOf("/*");
/* 205 */     if (i < j) {
/* 206 */       return true;
/*     */     }
/* 208 */     int k = paramString.lastIndexOf("//");
/* 209 */     if (k > -1) {
/* 210 */       paramString = paramString.substring(k, paramInt);
/*     */       
/* 212 */       if (paramString.indexOf("\n") == -1) {
/* 213 */         return true;
/*     */       }
/*     */     } 
/* 216 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String delAnnotation1(String paramString) {
/* 225 */     paramString = paramString.replaceAll("/\\*[\\s\\S]*?\\*/", "");
/*     */     
/* 227 */     return paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String delAnnotation2(String paramString) {
/* 235 */     paramString = paramString.replaceAll("//.*?", "");
/*     */     
/* 237 */     return paramString;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/ruleinterface/CheckRuleInterface.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */