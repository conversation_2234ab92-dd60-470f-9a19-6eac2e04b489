/*    */ package weaver.templetecheck.ruleinterface;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.List;
/*    */ import java.util.regex.Matcher;
/*    */ import java.util.regex.Pattern;
/*    */ import weaver.general.BaseBean;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class Rule_ClassListShort
/*    */   extends CheckRuleInterface
/*    */ {
/*    */   public List<String> checkRule(String paramString) {
/*    */     try {
/* 22 */       ArrayList<String> arrayList = new ArrayList();
/* 23 */       Pattern pattern = null;
/* 24 */       Matcher matcher = null;
/*    */       try {
/* 26 */         pattern = Pattern.compile("(class.*?)(listshort|LISTSHORT)(.*?>)");
/* 27 */         matcher = pattern.matcher(paramString);
/*    */       }
/* 29 */       catch (Exception exception) {
/* 30 */         (new BaseBean()).writeLog(exception.getMessage() + exception);
/*    */       } 
/* 32 */       while (matcher.find()) {
/* 33 */         arrayList.add(matcher.group());
/*    */       }
/* 35 */       (new BaseBean()).writeLog("======================调用接口weaver.templetecheck.ruleinterface.Rule_ClassListShort的checkRule方法成功");
/* 36 */       return arrayList;
/* 37 */     } catch (Exception exception) {
/* 38 */       exception.printStackTrace();
/*    */       
/* 40 */       (new BaseBean()).writeLog("======================调用接口weaver.templetecheck.ruleinterface.Rule_ClassListShort的checkRule方法成功");
/* 41 */       return null;
/*    */     } 
/*    */   }
/*    */   
/*    */   public String replaceRule(String paramString) {
/* 46 */     Pattern pattern = null;
/* 47 */     Matcher matcher = null;
/*    */     try {
/* 49 */       pattern = Pattern.compile("(class.*?)(listshort|LISTSHORT)(.*?>)");
/* 50 */       matcher = pattern.matcher(paramString);
/*    */     }
/* 52 */     catch (Exception exception) {
/* 53 */       (new BaseBean()).writeLog(exception.getMessage() + exception);
/*    */     } 
/* 55 */     while (matcher.find()) {
/* 56 */       paramString = matcher.replaceAll("$1ListShort$3");
/*    */     }
/* 58 */     (new BaseBean()).writeLog("======================调用接口weaver.templetecheck.ruleinterface.Rule_ClassListShort的replaceRule方法成功");
/* 59 */     return paramString;
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public static String getFunctionBody(String paramString, int paramInt) {
/* 65 */     String str1 = paramString.substring(paramInt, paramString.length());
/* 66 */     String str2 = "";
/* 67 */     int i = str1.indexOf("<");
/* 68 */     int j = 0;
/* 69 */     boolean bool = false;
/* 70 */     if (i == -1) {
/* 71 */       return "";
/*    */     }
/*    */     
/* 74 */     int k = 1;
/* 75 */     for (j = i + 1; j < str1.length(); j++) {
/* 76 */       if (str1.charAt(j) == '<') {
/* 77 */         k++;
/* 78 */       } else if (str1.charAt(j) == '>') {
/* 79 */         k--;
/* 80 */       }  if (k == 0) {
/* 81 */         bool = true;
/*    */         
/*    */         break;
/*    */       } 
/*    */     } 
/* 86 */     k = j + paramInt + 1;
/* 87 */     if (bool) {
/* 88 */       str2 = paramString.substring(paramInt, k);
/*    */     }
/* 90 */     return str2;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/ruleinterface/Rule_ClassListShort.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */