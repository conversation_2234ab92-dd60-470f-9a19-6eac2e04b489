/*    */ package weaver.templetecheck.ruleinterface;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.List;
/*    */ import java.util.regex.Matcher;
/*    */ import java.util.regex.Pattern;
/*    */ import weaver.general.BaseBean;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class Rule_BeautySelect
/*    */   extends CheckRuleInterface
/*    */ {
/*    */   public List<String> checkRule(String paramString) {
/*    */     try {
/* 25 */       String str = paramString.replaceAll("//.*?", "").replaceAll("/\\*[\\s\\S]*?\\*/", "");
/* 26 */       ArrayList<String> arrayList = new ArrayList();
/* 27 */       Pattern pattern = null;
/* 28 */       Matcher matcher = null;
/*    */       try {
/* 30 */         pattern = Pattern.compile("(__jNiceNamespace__\\.)?(beautySelect.?\\(.*?\\)).?;");
/* 31 */         matcher = pattern.matcher(str);
/* 32 */       } catch (Exception exception) {
/* 33 */         (new BaseBean()).writeLog(exception.getMessage() + exception);
/*    */       } 
/* 35 */       while (matcher.find()) {
/* 36 */         arrayList.add(matcher.group());
/*    */       }
/* 38 */       (new BaseBean()).writeLog("======================调用接口的方法weaver.templetecheck.ruleinterface.Rule_BeautySelect的checkRule成功");
/* 39 */       return arrayList;
/* 40 */     } catch (Exception exception) {
/* 41 */       exception.printStackTrace();
/*    */       
/* 43 */       (new BaseBean()).writeLog("======================调用接口的方法weaver.templetecheck.ruleinterface.Rule_BeautySelect的checkRule失败");
/* 44 */       return null;
/*    */     } 
/*    */   }
/*    */   
/*    */   public String replaceRule(String paramString) {
/* 49 */     Pattern pattern = null;
/* 50 */     Matcher matcher = null;
/*    */     try {
/* 52 */       pattern = Pattern.compile("(?i)(__jNiceNamespace__\\.)?(beautySelect.?\\(.*?\\)).?;");
/* 53 */       matcher = pattern.matcher(paramString);
/* 54 */       int i = 0;
/* 55 */       while (matcher.find()) {
/* 56 */         if (!isInAnnotation(paramString, matcher.start() + i)) {
/* 57 */           String str1 = "/*" + this.notes + matcher.group().replace("*/", "").replace("/*", "") + "*/";
/* 58 */           i -= matcher.group().length() - (this.notes + str1).length();
/* 59 */           String str2 = paramString.substring(0, matcher.start());
/* 60 */           String str3 = paramString.substring(matcher.start());
/* 61 */           str3 = replaceFirst(str3, matcher.group(), str1);
/*    */           
/* 63 */           paramString = str2 + str3;
/*    */         } 
/*    */       } 
/* 66 */       (new BaseBean()).writeLog("======================调用接口的方法weaver.templetecheck.ruleinterface.Rule_BeautySelect的replaceRule成功");
/* 67 */     } catch (Exception exception) {
/* 68 */       (new BaseBean()).writeLog("======================调用接口的方法weaver.templetecheck.ruleinterface.Rule_BeautySelect的replaceRule失败");
/* 69 */       (new BaseBean()).writeLog(exception.getMessage() + exception);
/*    */     } 
/* 71 */     return paramString;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/ruleinterface/Rule_BeautySelect.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */