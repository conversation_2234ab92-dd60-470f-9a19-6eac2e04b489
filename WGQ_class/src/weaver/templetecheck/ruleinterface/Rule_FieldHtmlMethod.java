/*    */ package weaver.templetecheck.ruleinterface;
/*    */ import java.util.ArrayList;
/*    */ import java.util.Iterator;
/*    */ import java.util.Map;
/*    */ import java.util.regex.Matcher;
/*    */ import java.util.regex.Pattern;
/*    */ 
/*    */ public class Rule_FieldHtmlMethod extends CheckRuleInterface {
/*  9 */   String pattern = "(?i)(jQuery|\\$)\\(['\"]#?field.*?\\).html\\(";
/*    */ 
/*    */ 
/*    */   
/*    */   public List<String> checkRule(String paramString) {
/* 14 */     ArrayList<String> arrayList = new ArrayList();
/*    */     try {
/* 16 */       Pattern pattern = Pattern.compile(this.pattern);
/* 17 */       Matcher matcher = pattern.matcher(paramString);
/* 18 */       boolean bool = false;
/* 19 */       while (matcher.find()) {
/* 20 */         bool = isInAnnotation(paramString, matcher.start());
/* 21 */         if (!bool) {
/* 22 */           arrayList.add(matcher.group());
/*    */         }
/*    */       }
/*    */     
/* 26 */     } catch (Exception exception) {
/* 27 */       exception.printStackTrace();
/*    */     } 
/* 29 */     return arrayList;
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public String replaceRule(String paramString) {
/*    */     try {
/* 36 */       Pattern pattern = Pattern.compile(this.pattern);
/* 37 */       Matcher matcher = pattern.matcher(paramString);
/* 38 */       boolean bool = false;
/* 39 */       String str1 = "";
/* 40 */       String str2 = "";
/* 41 */       int i = 0;
/* 42 */       while (matcher.find()) {
/* 43 */         String str = matcher.group();
/* 44 */         HashMap hashMap = (HashMap)findStrByTag(paramString, str, "(", ")", str.lastIndexOf("("), true);
/* 45 */         Set set = hashMap.entrySet();
/* 46 */         Iterator<Map.Entry> iterator = set.iterator();
/*    */         
/* 48 */         i = 0;
/*    */         
/* 50 */         while (iterator.hasNext()) {
/* 51 */           Map.Entry entry = iterator.next();
/* 52 */           String str3 = (String)entry.getKey();
/* 53 */           String str4 = (String)entry.getValue();
/* 54 */           String str5 = this.notesStart + str4 + this.notesEnd;
/* 55 */           if (str3.indexOf("transferredsource") > -1) {
/*    */             continue;
/*    */           }
/* 58 */           String str6 = str3.substring(str3.indexOf("_") + 1, str3.length() - "_END".length());
/* 59 */           int j = Integer.valueOf(str6).intValue();
/* 60 */           bool = isInAnnotation(paramString, j + i);
/* 61 */           if (!bool) {
/* 62 */             str1 = paramString.substring(0, j + i);
/* 63 */             str2 = paramString.substring(j + i, paramString.length());
/* 64 */             str2 = replaceFirst(str2, (String)entry.getValue(), str5);
/* 65 */             paramString = str1 + str2;
/* 66 */             i = i + this.notesStart.length() + this.notesEnd.length();
/*    */ 
/*    */           
/*    */           }
/*    */ 
/*    */ 
/*    */         
/*    */         }
/*    */ 
/*    */ 
/*    */       
/*    */       }
/*    */ 
/*    */ 
/*    */     
/*    */     }
/* 82 */     catch (Exception exception) {
/* 83 */       exception.printStackTrace();
/*    */     } 
/* 85 */     return paramString;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/ruleinterface/Rule_FieldHtmlMethod.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */