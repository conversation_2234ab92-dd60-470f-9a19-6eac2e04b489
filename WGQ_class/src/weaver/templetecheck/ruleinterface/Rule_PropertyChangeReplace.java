/*     */ package weaver.templetecheck.ruleinterface;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.regex.Matcher;
/*     */ import java.util.regex.Pattern;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.general.BaseBean;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class Rule_PropertyChangeReplace
/*     */   extends CheckRuleInterface
/*     */ {
/*     */   public List<String> checkRule(String paramString) {
/*  21 */     String str1 = "(?i)(\\.)bind\\(.propertychange.*?\\)\\.";
/*  22 */     String str2 = "(?i)(\\.)bind\\(.*?propertychange.*?,.*?function.*?\\(\\)";
/*     */     
/*  24 */     String[] arrayOfString = { str1, str2 };
/*  25 */     for (String str : arrayOfString) {
/*     */       
/*     */       try {
/*  28 */         ArrayList<String> arrayList = new ArrayList();
/*  29 */         Pattern pattern = null;
/*  30 */         Matcher matcher = null;
/*     */         try {
/*  32 */           pattern = Pattern.compile(str);
/*  33 */           matcher = pattern.matcher(paramString);
/*     */         }
/*  35 */         catch (Exception exception) {
/*  36 */           (new BaseBean()).writeLog(exception.toString());
/*     */         } 
/*  38 */         while (matcher.find()) {
/*  39 */           arrayList.add(matcher.group());
/*     */         }
/*  41 */         (new BaseBean()).writeLog("======================调用接口的方法weaver.templetecheck.ruleinterface.Rule_PropertyChange的checkRule成功");
/*     */         
/*  43 */         return arrayList;
/*  44 */       } catch (Exception exception) {
/*  45 */         exception.printStackTrace();
/*     */       } 
/*     */     } 
/*  48 */     (new BaseBean()).writeLog("======================调用接口的方法weaver.templetecheck.ruleinterface.Rule_PropertyChange的checkRule成功");
/*     */     
/*  50 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getFunctionBody(String paramString1, String paramString2) {
/*  63 */     Rule_PropertyChangeReplace rule_PropertyChangeReplace = new Rule_PropertyChangeReplace();
/*  64 */     Map<String, String> map = rule_PropertyChangeReplace.findStrByTag(paramString1, paramString2, "{", "}", paramString2.indexOf("{"), true);
/*  65 */     for (String str : map.keySet()) {
/*  66 */       if (str.contains("27C6F7EACF39D0F36DBD516866B7776E")) {
/*  67 */         String str1 = map.get(str);
/*  68 */         str1 = str1.substring(str1.indexOf("function()"), str1.length());
/*  69 */         return str1;
/*     */       } 
/*     */     } 
/*  72 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String replaceRule(String paramString) {
/*  80 */     String str1 = "document.getElementById\\(.*?\\).bind\\(.*?propertychange.*?\\)";
/*  81 */     String str2 = "\\$\\(.*?#.*?\\).bind\\(.*?propertychange.*?\\)";
/*  82 */     String str3 = "jQuery\\(.*?#.*?\\)\\.bind\\(.*?propertychange.*?\\)";
/*     */     
/*  84 */     Pattern pattern1 = Pattern.compile(str1);
/*  85 */     Pattern pattern2 = Pattern.compile(str2);
/*  86 */     Pattern pattern3 = Pattern.compile(str3);
/*  87 */     Matcher matcher1 = pattern1.matcher(paramString);
/*  88 */     Matcher matcher2 = pattern2.matcher(paramString);
/*  89 */     Matcher matcher3 = pattern3.matcher(paramString);
/*     */     
/*  91 */     while (matcher1.find()) {
/*  92 */       String str4 = matcher1.group().replaceFirst("document.getElementById\\(\"", "");
/*  93 */       String str5 = str4.replaceAll("\\(", "");
/*  94 */       str5 = str5.substring(str5.indexOf(",") + 1, str5.length() - 1).trim();
/*  95 */       str4 = str4.substring(0, str4.indexOf("\""));
/*  96 */       if (str4.contains("field")) {
/*  97 */         String str = "";
/*     */         
/*  99 */         if (str5.contains("function")) {
/* 100 */           str = getFunctionBody(paramString, "document.getElementById(\"" + str4 + "\")).bind(\"propertychange\",function(){");
/*     */         } else {
/* 102 */           str = str5 + "()";
/*     */         } 
/* 104 */         if (StringUtil.isNotNull(str))
/* 105 */           paramString = replaceFormField(paramString, str4, str); 
/*     */         continue;
/*     */       } 
/* 108 */       paramString = replaceNotFormField(paramString);
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 113 */     while (matcher2.find()) {
/* 114 */       String str4 = matcher2.group().replaceFirst("\\$\\(\"#", "");
/*     */       
/* 116 */       String str5 = str4.substring(str4.indexOf(",") + 1, str4.length() - 1).trim();
/* 117 */       str4 = str4.substring(0, str4.indexOf("\""));
/*     */       
/* 119 */       if (str4.contains("field")) {
/* 120 */         String str = "";
/* 121 */         if (str5.contains("function")) {
/*     */           
/* 123 */           str = getFunctionBody(paramString, matcher2.group() + "{");
/*     */         } else {
/* 125 */           str = str5 + "()";
/*     */         } 
/* 127 */         paramString = replaceFormField(paramString, str4, str); continue;
/*     */       } 
/* 129 */       paramString = replaceNotFormField(paramString);
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 134 */     while (matcher3.find()) {
/* 135 */       String str4 = matcher3.group().replaceFirst("jQuery\\(\"#", "");
/* 136 */       String str5 = str4.replaceAll("\\(", "");
/* 137 */       str5 = str5.substring(str5.indexOf(",") + 1, str5.length() - 1).trim();
/* 138 */       str4 = str4.substring(0, str4.indexOf("\""));
/* 139 */       if (str4.contains("field")) {
/* 140 */         String str = "";
/* 141 */         if (str5.contains("function")) {
/* 142 */           str = getFunctionBody(paramString, matcher3.group() + "{");
/*     */         } else {
/* 144 */           str = str5 + "()";
/*     */         } 
/* 146 */         paramString = replaceFormField(paramString, str4, str); continue;
/*     */       } 
/* 148 */       paramString = replaceNotFormField(paramString);
/*     */     } 
/*     */ 
/*     */     
/* 152 */     return paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static String replaceFormField(String paramString1, String paramString2, String paramString3) {
/* 159 */     String[] arrayOfString = paramString1.split("</SCRIPT>|</script>");
/* 160 */     StringBuffer stringBuffer1 = new StringBuffer(" \n <SCRIPT> \n ");
/* 161 */     stringBuffer1.append("jQuery(document).ready(function(){ \n");
/* 162 */     stringBuffer1.append("   WfForm.bindFieldChangeEvent(" + paramString2 + "," + paramString3 + "); \n ); \n </SCRIPT> ");
/* 163 */     String str = stringBuffer1.toString();
/* 164 */     StringBuffer stringBuffer2 = new StringBuffer();
/* 165 */     if (arrayOfString.length > 0) {
/* 166 */       for (byte b = 0; b < arrayOfString.length; b++) {
/*     */         
/* 168 */         if (b == arrayOfString.length - 1) {
/* 169 */           stringBuffer2.append(arrayOfString[b] + "\n </script> \n" + str);
/*     */         } else {
/* 171 */           stringBuffer2.append(arrayOfString[b] + " \n </SCRIPT>");
/*     */         } 
/*     */       } 
/*     */     }
/* 175 */     (new BaseBean()).writeLog("======================调用接口weaver.templetecheck.ruleinterface.Rule_PropertyChangeReplace中替换表单字段，replaceFormField()函数");
/* 176 */     return stringBuffer2.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static String replaceNotFormField(String paramString) {
/* 185 */     String str1 = "bind\\(.*?propertychange.*?,";
/* 186 */     String str2 = "bindPropertyChange(";
/*     */     
/* 188 */     Pattern pattern = null;
/* 189 */     Matcher matcher = null;
/*     */     try {
/* 191 */       pattern = Pattern.compile(str1);
/* 192 */       matcher = pattern.matcher(paramString);
/*     */     }
/* 194 */     catch (Exception exception) {
/* 195 */       (new BaseBean()).writeLog(exception.toString());
/*     */     } 
/* 197 */     while (matcher.find()) {
/* 198 */       paramString = matcher.replaceAll(str2);
/*     */     }
/* 200 */     (new BaseBean()).writeLog("======================调用接口weaver.templetecheck.ruleinterface.Rule_PropertyChangeReplace中替换非表单字段，replaceNotFormField()函数");
/* 201 */     return paramString;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/ruleinterface/Rule_PropertyChangeReplace.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */