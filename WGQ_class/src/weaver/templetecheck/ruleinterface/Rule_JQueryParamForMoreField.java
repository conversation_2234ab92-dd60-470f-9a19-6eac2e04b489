/*    */ package weaver.templetecheck.ruleinterface;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import weaver.general.BaseBean;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class Rule_JQueryParamForMoreField
/*    */   extends CheckRuleInterface
/*    */ {
/*    */   public List<String> checkRule(String paramString) {
/* 19 */     Map<String, Object> map = analyseStr(paramString, false);
/* 20 */     return (List)map.get("checkresult");
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public String replaceRule(String paramString) {
/* 26 */     Map<String, Object> map = analyseStr(paramString, true);
/* 27 */     return (String)map.get("replaceresult");
/*    */   }
/*    */ 
/*    */   
/*    */   private Map<String, Object> analyseStr(String paramString, boolean paramBoolean) {
/* 32 */     ArrayList<String> arrayList = new ArrayList();
/* 33 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */     
/*    */     try {
/* 36 */       Map<String, String> map = findStrByRegex(paramString, "(?i)(jQuery|\\$)\\((.*?,.*?)\\).val\\((.*?)\\)(;?)");
/* 37 */       String str = map.get("transferredsource");
/* 38 */       for (String str1 : map.keySet()) {
/* 39 */         if (!str1.contains("27C6F7EACF39D0F36DBD516866B7776E"))
/* 40 */           continue;  String str2 = map.get(str1);
/* 41 */         arrayList.add(str2);
/* 42 */         if (paramBoolean) {
/* 43 */           String str3 = "";
/* 44 */           String str4 = "";
/*    */           try {
/* 46 */             str3 = str2.substring(str2.indexOf("(") + 1, str2.indexOf(")"));
/* 47 */             str4 = str2.substring(str2.lastIndexOf("(") + 1, str2.lastIndexOf(")"));
/* 48 */             if (str3.contains("+")) {
/* 49 */               str = str.replace(str1, str2);
/*    */               continue;
/*    */             } 
/* 52 */           } catch (Exception exception) {
/*    */             continue;
/*    */           } 
/* 55 */           String[] arrayOfString = str3.split(",");
/* 56 */           String str5 = str4;
/* 57 */           String str6 = "";
/* 58 */           for (String str7 : arrayOfString) {
/* 59 */             if (!"".equals(str7.trim())) {
/* 60 */               if (!str7.trim().startsWith("'") && !str7.trim().startsWith("\"")) {
/* 61 */                 str7 = "\"" + str7;
/*    */               } else {
/* 63 */                 str7 = "\"" + str7.substring(1);
/*    */               } 
/*    */               
/* 66 */               if (!str7.trim().endsWith("'") && !str7.trim().endsWith("\"")) {
/* 67 */                 str7 = str7 + "\"";
/*    */               } else {
/* 69 */                 str7 = str7.substring(0, str7.length() - 1) + "\"";
/*    */               } 
/* 71 */               str6 = str6 + "jQuery(" + str7 + ").val(" + str5 + ");\n";
/*    */             } 
/*    */           } 
/* 74 */           str = str.replace(str1, str6); continue;
/*    */         } 
/* 76 */         if (paramBoolean) {
/* 77 */           str = str.replace(str1, str2);
/*    */         }
/*    */       } 
/*    */       
/* 81 */       hashMap.put("checkresult", arrayList);
/* 82 */       hashMap.put("replaceresult", str);
/* 83 */     } catch (Exception exception) {
/* 84 */       (new BaseBean()).writeLog(getClass().getName() + "" + exception.getMessage() + exception);
/* 85 */       hashMap.put("checkresult", arrayList);
/* 86 */       hashMap.put("replaceresult", paramString);
/*    */     } 
/* 88 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/ruleinterface/Rule_JQueryParamForMoreField.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */