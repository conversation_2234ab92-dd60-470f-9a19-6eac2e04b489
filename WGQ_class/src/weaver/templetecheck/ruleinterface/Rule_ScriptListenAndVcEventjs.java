/*    */ package weaver.templetecheck.ruleinterface;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.List;
/*    */ import java.util.regex.Matcher;
/*    */ import java.util.regex.Pattern;
/*    */ import weaver.general.BaseBean;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class Rule_ScriptListenAndVcEventjs
/*    */   extends CheckRuleInterface
/*    */ {
/*    */   public List<String> checkRule(String paramString) {
/*    */     try {
/* 22 */       ArrayList<String> arrayList = new ArrayList();
/* 23 */       Pattern pattern = null;
/* 24 */       Matcher matcher = null;
/*    */       try {
/* 26 */         pattern = Pattern.compile("((?i)(<script).*?src=.?(/wui/common/jquery/plugin/Listener_wev8.js|/js/workflow/VCEventHandle_wev8.js).*?(/>|>.?</script>|>))");
/* 27 */         matcher = pattern.matcher(paramString);
/*    */       }
/* 29 */       catch (Exception exception) {
/* 30 */         (new BaseBean()).writeLog(exception.getMessage() + exception);
/*    */       } 
/* 32 */       while (matcher.find()) {
/* 33 */         arrayList.add(matcher.group());
/*    */       }
/* 35 */       (new BaseBean()).writeLog("======================调用接口的方法weaver.templetecheck.ruleinterface.Rule_ScriptListenAndVcEventjs的checkRule成功");
/* 36 */       return arrayList;
/* 37 */     } catch (Exception exception) {
/* 38 */       exception.printStackTrace();
/*    */       
/* 40 */       (new BaseBean()).writeLog("======================调用接口的方法weaver.templetecheck.ruleinterface.Rule_ScriptListenAndVcEventjs的checkRule失败");
/* 41 */       return null;
/*    */     } 
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String replaceRule(String paramString) {
/*    */     try {
/* 52 */       String str = paramString;
/* 53 */       Pattern pattern = null;
/* 54 */       Matcher matcher = null;
/*    */       try {
/* 56 */         pattern = Pattern.compile("((?i)(<script).*?src=.?(/wui/common/jquery/plugin/Listener_wev8.js|/js/workflow/VCEventHandle_wev8.js).*?(/>|>.?</script>|>))");
/* 57 */         matcher = pattern.matcher(paramString);
/*    */       }
/* 59 */       catch (Exception exception) {
/* 60 */         (new BaseBean()).writeLog(exception.getMessage() + exception);
/*    */       } 
/* 62 */       while (matcher.find()) {
/* 63 */         str = matcher.replaceAll("");
/*    */       }
/* 65 */       (new BaseBean()).writeLog("======================调用接口的方法weaver.templetecheck.ruleinterface.Rule_ScriptListenAndVcEventjs的replaceRule成功");
/* 66 */       return str;
/* 67 */     } catch (Exception exception) {
/* 68 */       exception.printStackTrace();
/*    */       
/* 70 */       (new BaseBean()).writeLog("======================调用接口的方法weaver.templetecheck.ruleinterface.Rule_ScriptListenAndVcEventjs的replaceRule失败");
/* 71 */       return "";
/*    */     } 
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/ruleinterface/Rule_ScriptListenAndVcEventjs.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */