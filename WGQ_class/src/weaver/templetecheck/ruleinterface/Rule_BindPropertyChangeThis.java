/*    */ package weaver.templetecheck.ruleinterface;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.List;
/*    */ import weaver.general.BaseBean;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class Rule_BindPropertyChangeThis
/*    */   extends CheckRuleInterface
/*    */ {
/*    */   public List<String> checkRule(String paramString) {
/*    */     try {
/* 19 */       ArrayList<String> arrayList = new ArrayList();
/* 20 */       for (byte b = 0; b < paramString.length(); b++) {
/* 21 */         if (paramString.regionMatches(true, b, ".bindPropertyChange", 0, ".bindPropertyChange".length())) {
/* 22 */           String str = getFunctionBody(paramString, b);
/* 23 */           if (!checkContentPass(str)) {
/* 24 */             arrayList.add(str);
/*    */           }
/*    */         } 
/*    */       } 
/* 28 */       (new BaseBean()).writeLog("======================调用接口weaver.templetecheck.ruleinterface.Rule_BindPropertyChangeThis的checkRule方法成功");
/* 29 */       return arrayList;
/* 30 */     } catch (Exception exception) {
/* 31 */       exception.printStackTrace();
/*    */       
/* 33 */       (new BaseBean()).writeLog("======================调用接口weaver.templetecheck.ruleinterface.Rule_BindPropertyChangeThis的checkRule方法成功");
/* 34 */       return null;
/*    */     } 
/*    */   }
/*    */   
/*    */   public String replaceRule(String paramString) {
/* 39 */     return paramString;
/*    */   }
/*    */ 
/*    */   
/*    */   public static String getFunctionBody(String paramString, int paramInt) {
/* 44 */     String str1 = paramString.substring(paramInt, paramString.length());
/* 45 */     String str2 = "";
/* 46 */     int i = str1.indexOf("(");
/* 47 */     int j = 0;
/* 48 */     boolean bool = false;
/* 49 */     if (i == -1) {
/* 50 */       return "";
/*    */     }
/*    */     
/* 53 */     int k = 1;
/* 54 */     for (j = i + 1; j < str1.length(); j++) {
/* 55 */       if (str1.charAt(j) == '(') {
/* 56 */         k++;
/* 57 */       } else if (str1.charAt(j) == ')') {
/* 58 */         k--;
/* 59 */       }  if (k == 0) {
/* 60 */         bool = true;
/*    */         
/*    */         break;
/*    */       } 
/*    */     } 
/* 65 */     k = j + paramInt + 1;
/* 66 */     if (bool) {
/* 67 */       str2 = paramString.substring(paramInt, k);
/*    */     }
/* 69 */     return str2;
/*    */   }
/*    */ 
/*    */   
/*    */   private static boolean checkContentPass(String paramString) {
/* 74 */     boolean bool = true;
/* 75 */     if (paramString.toLowerCase().indexOf("function") > 0) {
/* 76 */       paramString = paramString.substring(paramString.toLowerCase().indexOf("function") + "function".length(), paramString.length());
/* 77 */       int i = paramString.indexOf("(");
/* 78 */       int j = 0;
/* 79 */       if (i == -1) {
/* 80 */         return true;
/*    */       }
/*    */       
/* 83 */       byte b = 1;
/* 84 */       for (j = i + 1; j < paramString.length(); j++) {
/* 85 */         if (paramString.charAt(j) == '(') {
/* 86 */           b++;
/* 87 */         } else if (paramString.charAt(j) == ')') {
/* 88 */           b--;
/* 89 */         }  if (b == 0) {
/* 90 */           String str = paramString.substring(i + 1, j);
/* 91 */           if (str.equalsIgnoreCase("this")) {
/* 92 */             bool = false;
/*    */           }
/*    */           
/*    */           break;
/*    */         } 
/*    */       } 
/*    */     } 
/* 99 */     return bool;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/ruleinterface/Rule_BindPropertyChangeThis.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */