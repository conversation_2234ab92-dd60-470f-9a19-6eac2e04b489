/*    */ package weaver.templetecheck.ruleinterface;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import java.util.regex.Matcher;
/*    */ import java.util.regex.Pattern;
/*    */ import weaver.general.BaseBean;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class Rule_Viewtype
/*    */   extends CheckRuleInterface
/*    */ {
/*    */   public List<String> checkRule(String paramString) {
/* 21 */     Map<String, Object> map = analyseStr(paramString);
/* 22 */     return (List)map.get("checkresult");
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String replaceRule(String paramString) {
/* 31 */     return paramString;
/*    */   }
/*    */   
/*    */   private Map<String, Object> analyseStr(String paramString) {
/* 35 */     ArrayList<String> arrayList = new ArrayList();
/* 36 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */ 
/*    */ 
/*    */     
/*    */     try {
/* 41 */       Map<String, String> map = findStrByTag(paramString, "<brow:browser", "<", ">", 0, true);
/* 42 */       String str = map.get("transferredsource");
/* 43 */       for (String str1 : map.keySet()) {
/* 44 */         if (!str1.contains("27C6F7EACF39D0F36DBD516866B7776E"))
/* 45 */           continue;  String str2 = map.get(str1);
/* 46 */         Pattern pattern = Pattern.compile("(?i)\\<brow:browser.*?viewType\\s*?=");
/* 47 */         Matcher matcher = pattern.matcher(str2);
/* 48 */         while (matcher.find()) {
/* 49 */           arrayList.add(matcher.group());
/*    */         }
/*    */       } 
/* 52 */       hashMap.put("checkresult", arrayList);
/* 53 */       hashMap.put("replaceresult", str);
/* 54 */     } catch (Exception exception) {
/* 55 */       (new BaseBean()).writeLog(getClass().getName() + "" + exception.getMessage() + exception);
/* 56 */       hashMap.put("checkresult", arrayList);
/* 57 */       hashMap.put("replaceresult", "");
/*    */     } 
/* 59 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/ruleinterface/Rule_Viewtype.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */