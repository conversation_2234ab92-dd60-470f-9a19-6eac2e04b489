/*    */ package weaver.templetecheck.ruleinterface;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import java.util.regex.Matcher;
/*    */ import java.util.regex.Pattern;
/*    */ import weaver.general.BaseBean;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class Rule_BindPropertyChangeJQuery
/*    */   extends CheckRuleInterface
/*    */ {
/*    */   public List<String> checkRule(String paramString) {
/* 22 */     Map<String, Object> map = analyseStr(paramString, false);
/* 23 */     return (List)map.get("checkresult");
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public String replaceRule(String paramString) {
/* 29 */     Map<String, Object> map = analyseStr(paramString, true);
/* 30 */     return (String)map.get("replaceresult");
/*    */   }
/*    */ 
/*    */   
/*    */   private Map<String, Object> analyseStr(String paramString, boolean paramBoolean) {
/* 35 */     ArrayList<String> arrayList = new ArrayList();
/* 36 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */ 
/*    */ 
/*    */     
/*    */     try {
/* 41 */       Map<String, String> map = findStrByTag(paramString, ".bindPropertyChange(", "(", ")", ".bindPropertyChange(".indexOf("("), true);
/* 42 */       String str = map.get("transferredsource");
/* 43 */       for (String str1 : map.keySet()) {
/* 44 */         if (!str1.contains("27C6F7EACF39D0F36DBD516866B7776E"))
/* 45 */           continue;  String str2 = map.get(str1);
/* 46 */         Pattern pattern = Pattern.compile("(?i)jQuery\\((.*?#.*?)\\)\\.val\\(\\)");
/* 47 */         Matcher matcher = pattern.matcher(str2);
/* 48 */         if (matcher.find()) {
/* 49 */           arrayList.add(matcher.group());
/* 50 */           if (paramBoolean) {
/* 51 */             String str3 = matcher.replaceAll("WfForm.getFieldValue($1)").replace("#", "");
/* 52 */             str = str.replace(str1, str3);
/*    */           }  continue;
/*    */         } 
/* 55 */         if (paramBoolean) {
/* 56 */           str = str.replace(str1, str2);
/*    */         }
/*    */       } 
/*    */       
/* 60 */       hashMap.put("checkresult", arrayList);
/* 61 */       hashMap.put("replaceresult", str);
/* 62 */     } catch (Exception exception) {
/* 63 */       (new BaseBean()).writeLog(getClass().getName() + "" + exception.getMessage() + exception);
/* 64 */       hashMap.put("checkresult", arrayList);
/* 65 */       hashMap.put("replaceresult", "");
/*    */     } 
/* 67 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/ruleinterface/Rule_BindPropertyChangeJQuery.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */