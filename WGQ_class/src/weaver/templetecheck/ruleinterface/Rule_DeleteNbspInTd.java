/*    */ package weaver.templetecheck.ruleinterface;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import java.util.regex.Matcher;
/*    */ import java.util.regex.Pattern;
/*    */ import weaver.general.BaseBean;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class Rule_DeleteNbspInTd
/*    */   extends CheckRuleInterface
/*    */ {
/*    */   public List<String> checkRule(String paramString) {
/* 20 */     Map<String, Object> map = analyseStr5(paramString, false);
/* 21 */     return (List)map.get("checkresult");
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String replaceRule(String paramString) {
/* 28 */     Map<String, Object> map = analyseStr5(paramString, true);
/* 29 */     return (String)map.get("replaceresult");
/*    */   }
/*    */ 
/*    */   
/*    */   private Map<String, Object> analyseStr5(String paramString, boolean paramBoolean) {
/* 34 */     ArrayList<String> arrayList = new ArrayList();
/* 35 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */ 
/*    */ 
/*    */     
/*    */     try {
/* 40 */       Map<String, String> map = findStrByRegex(paramString, "(?i)(<td[^<]*?>[\\s]*)((&nbsp;)+)<");
/* 41 */       String str = map.get("transferredsource");
/* 42 */       for (String str1 : map.keySet()) {
/* 43 */         if (!str1.contains("27C6F7EACF39D0F36DBD516866B7776E"))
/* 44 */           continue;  String str2 = map.get(str1);
/* 45 */         Pattern pattern = Pattern.compile("(?i)(<td[^<]*?>[\\s]*)((&nbsp;)+)<");
/* 46 */         Matcher matcher = pattern.matcher(str2);
/* 47 */         if (matcher.find()) {
/* 48 */           String str3 = matcher.group();
/* 49 */           arrayList.add(str3);
/* 50 */           String str4 = matcher.group(2);
/* 51 */           int i = matcher.start(2);
/* 52 */           if (paramBoolean) {
/* 53 */             String str5 = str2;
/* 54 */             str2 = str5.substring(0, i) + str5.substring(i + str4.length());
/* 55 */             str = str.replace(str1, str2);
/*    */           }  continue;
/*    */         } 
/* 58 */         if (paramBoolean) {
/* 59 */           str = str.replace(str1, str2);
/*    */         }
/*    */       } 
/*    */       
/* 63 */       hashMap.put("checkresult", arrayList);
/* 64 */       hashMap.put("replaceresult", str);
/* 65 */     } catch (Exception exception) {
/* 66 */       (new BaseBean()).writeLog(getClass().getName() + "" + exception.getMessage() + exception);
/* 67 */       hashMap.put("checkresult", arrayList);
/* 68 */       hashMap.put("replaceresult", "");
/*    */     } 
/* 70 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/ruleinterface/Rule_DeleteNbspInTd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */