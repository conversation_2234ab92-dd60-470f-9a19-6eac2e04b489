/*     */ package weaver.templetecheck.logcat;
/*     */ import java.sql.Connection;
/*     */ import java.sql.ResultSet;
/*     */ import java.sql.Statement;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ public class LogCatThread extends Thread {
/*  11 */   public static String msg = "";
/*  12 */   public static int status = 0;
/*  13 */   private ArrayList<String> logfilelist = null;
/*     */   public LogCatThread(ArrayList<String> paramArrayList) {
/*  15 */     this.logfilelist = paramArrayList;
/*     */   }
/*     */   
/*     */   public void run() {
/*  19 */     Statement statement = null;
/*  20 */     Connection connection = null;
/*  21 */     ResultSet resultSet = null;
/*  22 */     BaseBean baseBean = new BaseBean();
/*     */     try {
/*  24 */       Class.forName("oracle.jdbc.OracleDriver");
/*  25 */       connection = DriverManager.getConnection(baseBean.getPropValue("weaver", "ecology.url"), baseBean.getPropValue("weaver", "ecology.user"), baseBean.getPropValue("weaver", "ecology.password"));
/*  26 */       if (connection == null) {
/*  27 */         msg = "" + SystemEnv.getHtmlLabelName(10004216, ThreadVarLanguage.getLang()) + "";
/*  28 */         baseBean.writeLog(msg);
/*     */       } 
/*  30 */       statement = connection.createStatement();
/*  31 */       byte b = 1;
/*  32 */       status = 1;
/*  33 */       for (String str1 : this.logfilelist) {
/*  34 */         msg = "" + SystemEnv.getHtmlLabelName(10004217, ThreadVarLanguage.getLang()) + "" + b + "" + SystemEnv.getHtmlLabelName(10004218, ThreadVarLanguage.getLang()) + "," + SystemEnv.getHtmlLabelName(23631, ThreadVarLanguage.getLang()) + "" + this.logfilelist.size() + "" + SystemEnv.getHtmlLabelName(10004218, ThreadVarLanguage.getLang()) + "";
/*  35 */         String str2 = "begin dbms_logmnr.add_logfile('" + str1 + "',dbms_logmnr.NEW);dbms_logmnr.start_logmnr(Options =>dbms_logmnr.DICT_FROM_ONLINE_CATALOG); end;";
/*     */ 
/*     */ 
/*     */         
/*  39 */         baseBean.writeLog("=========开始执行挖去日志存储过程语句:" + str2);
/*  40 */         int i = statement.executeUpdate(str2);
/*  41 */         System.out.println("============挖日志完成,结果为:" + i);
/*     */         
/*  43 */         resultSet = statement.executeQuery(" select sql_redo from v$logmnr_contents where seg_owner='ECOLOGY9'");
/*  44 */         if (resultSet.next()) {
/*  45 */           if (!judgeExistTable("logcat")) {
/*  46 */             statement.executeUpdate("create table logcat as select * from v$logmnr_contents where seg_owner='ECOLOGY9'");
/*     */           } else {
/*  48 */             statement.executeUpdate("insert into logcat select * from v$logmnr_contents where seg_owner='ECOLOGY9'");
/*     */           } 
/*  50 */           baseBean.writeLog("挖日志结束,挖到了数据,name:" + str1);
/*     */         } else {
/*  52 */           baseBean.writeLog("挖日志结束,没有挖到数据" + str1);
/*     */         } 
/*     */         
/*  55 */         int j = statement.executeUpdate("begin dbms_logmnr.end_logmnr; end;");
/*  56 */         baseBean.writeLog("清空结果,name:" + str1 + "===resust" + j);
/*  57 */         b++;
/*     */       } 
/*  59 */       msg = "" + SystemEnv.getHtmlLabelName(10004219, ThreadVarLanguage.getLang()) + "";
/*  60 */     } catch (Exception exception) {
/*  61 */       baseBean.writeLog("挖日志出现异常");
/*  62 */       exception.printStackTrace();
/*  63 */       status = 2;
/*     */     } finally {
/*     */       try {
/*  66 */         if (resultSet != null) resultSet.close(); 
/*  67 */         if (statement != null) statement.close(); 
/*  68 */         if (connection != null) connection.close(); 
/*  69 */       } catch (Exception exception) {
/*  70 */         exception.printStackTrace();
/*     */       } 
/*     */     } 
/*  73 */     status = 3;
/*     */   }
/*     */ 
/*     */   
/*     */   public static boolean judgeExistTable(String paramString) throws Exception {
/*  78 */     BaseBean baseBean = new BaseBean();
/*  79 */     if (paramString == null || paramString.length() == 0) {
/*  80 */       return false;
/*     */     }
/*  82 */     boolean bool = false;
/*  83 */     RecordSet recordSet = new RecordSet();
/*  84 */     boolean bool1 = "oracle".equalsIgnoreCase(recordSet.getDBType());
/*  85 */     boolean bool2 = "db2".equalsIgnoreCase(recordSet.getDBType());
/*     */     try {
/*  87 */       String str = "";
/*  88 */       if (bool1) {
/*  89 */         str = "select 1 from user_tables where table_name = upper('" + paramString + "')";
/*  90 */       } else if (bool2) {
/*  91 */         str = "select 1 from SYSIBM.SYSTABLES where lower(name) = lower('" + paramString + "')";
/*  92 */       } else if (recordSet.getDBType().equals("mysql")) {
/*  93 */         str = "select 1 from information_schema.Tables where LOWER(Table_Name)=LOWER('" + paramString + "') ";
/*  94 */       } else if (recordSet.getDBType().equals("postgresql")) {
/*  95 */         str = "select 1 from information_schema.Tables where LOWER(Table_Name)=LOWER('" + paramString + "') ";
/*     */       } else {
/*  97 */         str = "select 1 a from sysobjects where name = '" + paramString + "' and objectproperty(id, 'IsUserTable') = 1";
/*  98 */       }  baseBean.writeLog("  judgeExistTable,sql:" + str);
/*  99 */       recordSet.execute(str);
/* 100 */       if (recordSet.next())
/* 101 */         bool = true; 
/* 102 */     } catch (Exception exception) {
/* 103 */       exception.printStackTrace();
/*     */       
/* 105 */       throw new Exception("判断表" + paramString + "是否存在出现异常", exception);
/*     */     } finally {}
/*     */     
/* 108 */     baseBean.writeLog("  judgeExistTable,exist:" + bool);
/* 109 */     return bool;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/logcat/LogCatThread.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */