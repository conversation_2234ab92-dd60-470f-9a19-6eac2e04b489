/*     */ package weaver.templetecheck;
/*     */ import java.io.BufferedReader;
/*     */ import java.io.ByteArrayOutputStream;
/*     */ import java.io.File;
/*     */ import java.io.FileInputStream;
/*     */ import java.io.FileNotFoundException;
/*     */ import java.io.FileOutputStream;
/*     */ import java.io.IOException;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Set;
/*     */ import org.apache.tools.zip.ZipEntry;
/*     */ import org.apache.tools.zip.ZipOutputStream;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.GCONST;
/*     */ 
/*     */ public class TempFilesDownLoadUtils {
/*  19 */   private static int BUFFER = 4096;
/*  20 */   private static byte[] B_ARRAY = new byte[BUFFER];
/*     */   
/*     */   public static void getFileByHtmllayOutId(String paramString) {
/*  23 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  24 */     String str1 = " select id,htmllayoutid from templeteFilesDownload where verifycode ='" + paramString + "' ";
/*  25 */     RecordSet recordSet = new RecordSet();
/*     */     
/*  27 */     String str2 = "";
/*  28 */     recordSet.executeQuery(str1, new Object[0]);
/*  29 */     ArrayList<String> arrayList = new ArrayList();
/*  30 */     while (recordSet.next()) {
/*  31 */       str2 = recordSet.getString("htmllayoutid");
/*  32 */       arrayList.add(str2);
/*     */     } 
/*     */     
/*  35 */     for (String str : arrayList) {
/*  36 */       getString(str, (HashMap)hashMap);
/*     */     }
/*     */     
/*  39 */     if (hashMap.size() > 0) {
/*  40 */       Set set = hashMap.keySet();
/*  41 */       for (String str3 : set) {
/*  42 */         String str4 = (String)hashMap.get(str3);
/*  43 */         getHtmlAccordingPath(str3.replaceAll("\"|'", "").replaceAll(" ", ""), str4);
/*     */       } 
/*     */     } 
/*     */     
/*  47 */     (new BaseBean()).writeLog(" TempFilesDownLoadUtils.getFileByHtmllayOutId 下载引入的js,css,jsp文件 完成");
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private static void getString(String paramString, HashMap<String, String> paramHashMap) {
/*  53 */     ExcelSecurity excelSecurity = new ExcelSecurity();
/*  54 */     RecordSet recordSet = new RecordSet();
/*  55 */     String str = " select syspath,version,scripts from workflow_nodehtmllayout where id ='" + paramString + "'";
/*  56 */     recordSet.execute(str);
/*  57 */     if (recordSet.next()) {
/*  58 */       String str1 = Util.null2String(recordSet.getString("scripts"));
/*  59 */       String str2 = recordSet.getString("syspath");
/*  60 */       String str3 = recordSet.getString("version");
/*  61 */       if (StringUtil.isNotNull(str3) && "2".equalsIgnoreCase(str3)) {
/*     */         
/*  63 */         String str4 = "";
/*  64 */         if (!"".equals(str1)) {
/*  65 */           str4 = excelSecurity.decode(str1);
/*     */         }
/*  67 */         Set<String> set = checkRule(str4);
/*  68 */         for (String str5 : set) {
/*  69 */           paramHashMap.put(str5, "");
/*     */         }
/*  71 */       } else if (StringUtil.isNotNull(str2)) {
/*     */         
/*  73 */         File file = new File(str2);
/*  74 */         if (file.isFile()) {
/*  75 */           String str4 = acquireHtmlToString(file);
/*  76 */           Set<String> set = checkRule(str4);
/*  77 */           for (String str5 : set) {
/*  78 */             paramHashMap.put(str5, str2);
/*     */           }
/*     */         } 
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Set<String> checkRule(String paramString) {
/*  89 */     String str1 = "('|\")^?((\\.|\\.\\.|/)([a-zA-Z0-9_$]*))*([a-zA-Z0-9_$]*)(.jsp)";
/*     */     
/*  91 */     String str2 = "('|\")^?((\\.|\\.\\.|/)([a-zA-Z0-9_$-]*))*([a-zA-Z0-9_$-]*)(\\.css)";
/*     */     
/*  93 */     String str3 = "(\"|')^?((\\.|\\.\\.|/)([a-zA-Z0-9_$-]*))*([a-zA-Z0-9_$-]*)(\\.js)('|\"|\\s*)";
/*     */     
/*  95 */     String[] arrayOfString = { str2, str1, str3 };
/*     */     
/*     */     try {
/*  98 */       HashSet<String> hashSet = new HashSet();
/*  99 */       for (String str : arrayOfString) {
/* 100 */         Pattern pattern = null;
/* 101 */         Matcher matcher = null;
/*     */         try {
/* 103 */           pattern = Pattern.compile(str);
/* 104 */           matcher = pattern.matcher(paramString);
/*     */         }
/* 106 */         catch (Exception exception) {
/* 107 */           (new BaseBean()).writeLog(exception.toString());
/*     */         } 
/* 109 */         while (matcher.find()) {
/* 110 */           hashSet.add(matcher.group());
/*     */         }
/*     */       } 
/* 113 */       return hashSet;
/* 114 */     } catch (Exception exception) {
/* 115 */       exception.printStackTrace();
/*     */       
/* 117 */       return null;
/*     */     } 
/*     */   }
/*     */   
/*     */   public static void getHtmlAccordingPath(String paramString1, String paramString2) {
/* 122 */     if (!paramString1.contains("/")) {
/*     */       return;
/*     */     }
/* 125 */     String[] arrayOfString = paramString1.split("/");
/*     */     
/* 127 */     if (!paramString1.startsWith(".")) {
/* 128 */       absoluteFilePathDownLoad(arrayOfString);
/*     */     } else {
/*     */       
/* 131 */       relatePostionDownLoad(paramString2, arrayOfString);
/*     */     } 
/*     */   }
/*     */   
/*     */   private static void relatePostionDownLoad(String paramString, String[] paramArrayOfString) {
/* 136 */     if (paramArrayOfString[0].equals(".") || StringUtil.isNull(paramString)) {
/*     */       return;
/*     */     }
/*     */     
/* 140 */     String str1 = paramString.toLowerCase();
/* 141 */     if (str1.contains(".js") || str1.contains(".jsp") || str1.contains(".css") || str1.contains(".html")) {
/* 142 */       if (paramString.contains("\\")) {
/* 143 */         paramString = paramString.substring(0, paramString.lastIndexOf("\\"));
/* 144 */       } else if (paramString.contains("/")) {
/* 145 */         paramString = paramString.substring(0, paramString.lastIndexOf("/"));
/*     */       } 
/*     */     }
/* 148 */     File file1 = new File(paramString);
/* 149 */     String str2 = "";
/* 150 */     for (String str : paramArrayOfString) {
/* 151 */       if (!StringUtil.isNull(str))
/*     */       {
/*     */         
/* 154 */         if ("..".equals(str)) {
/* 155 */           str2 = file1.getParent();
/* 156 */           file1 = new File(str2);
/*     */         } else {
/* 158 */           str2 = str2 + File.separator + str;
/*     */         }  } 
/*     */     } 
/* 161 */     File file2 = new File(str2);
/*     */     
/* 163 */     if (!file2.isFile()) {
/*     */       return;
/*     */     }
/* 166 */     String str3 = GCONST.getRootPath();
/* 167 */     if (str2.contains(str3)) {
/* 168 */       str2 = str2.substring(str3.length());
/*     */     } else {
/*     */       return;
/*     */     } 
/* 172 */     String[] arrayOfString = null;
/* 173 */     if (str2.contains("\\")) {
/* 174 */       arrayOfString = str2.split("\\\\");
/* 175 */     } else if (str2.contains("/")) {
/* 176 */       arrayOfString = str2.split("/");
/*     */     } 
/* 178 */     str3 = str3 + "templetecheck" + File.separator + "downloadpackage" + File.separator + "ecology";
/* 179 */     for (String str4 : arrayOfString) {
/* 180 */       String str5 = str4.toLowerCase();
/* 181 */       if (!str5.contains(".js") && !str5.contains(".jsp") && !str5.contains(".css") && !str5.contains(".html")) {
/* 182 */         str3 = str3 + File.separator + str4;
/*     */       }
/*     */     } 
/*     */     
/* 186 */     File file3 = new File(str3);
/* 187 */     if (!file3.exists()) {
/*     */       try {
/* 189 */         file3.mkdirs();
/* 190 */       } catch (Exception exception) {
/* 191 */         exception.printStackTrace();
/*     */       } 
/*     */     }
/* 194 */     downLoadHtmlFileToSyspath(file2, str3);
/*     */   }
/*     */ 
/*     */   
/*     */   private static void absoluteFilePathDownLoad(String[] paramArrayOfString) {
/* 199 */     String str1 = "";
/* 200 */     String str2 = "";
/*     */     
/* 202 */     File file1 = new File("");
/* 203 */     str1 = GCONST.getRootPath();
/*     */     
/* 205 */     if (StringUtil.isNotNull(str1)) {
/* 206 */       for (String str : paramArrayOfString) {
/* 207 */         if (StringUtil.isNotNull(str)) {
/* 208 */           str1 = str1 + File.separator + str;
/* 209 */           String str4 = str.toLowerCase();
/* 210 */           if (!str4.contains(".jsp") && !str4.contains(".css") && !str4.contains(".js")) {
/* 211 */             str2 = str2 + File.separator + str;
/*     */           }
/*     */         } 
/*     */       } 
/*     */     }
/* 216 */     File file2 = new File(str1);
/* 217 */     if (!file2.isFile()) {
/*     */       return;
/*     */     }
/* 220 */     String str3 = GCONST.getRootPath() + "templetecheck" + File.separator + "downloadpackage" + File.separator + "ecology" + str2;
/* 221 */     File file3 = new File(str3);
/* 222 */     if (!file3.exists()) {
/*     */       try {
/* 224 */         file3.mkdirs();
/* 225 */       } catch (Exception exception) {
/* 226 */         exception.printStackTrace();
/*     */       } 
/*     */     }
/* 229 */     downLoadHtmlFileToSyspath(file2, str3);
/*     */   }
/*     */ 
/*     */   
/*     */   public static void downLoadHtmlFileToSyspath(File paramFile, String paramString) {
/* 234 */     FileInputStream fileInputStream = null;
/*     */     
/*     */     try {
/* 237 */       fileInputStream = new FileInputStream(paramFile);
/* 238 */     } catch (FileNotFoundException fileNotFoundException) {
/* 239 */       fileNotFoundException.printStackTrace();
/*     */     } 
/* 241 */     if (null == fileInputStream) {
/*     */       return;
/*     */     }
/* 244 */     byte[] arrayOfByte = new byte[0];
/*     */     try {
/* 246 */       arrayOfByte = readInputStream(fileInputStream);
/* 247 */     } catch (IOException iOException) {
/* 248 */       iOException.printStackTrace();
/*     */     } 
/*     */     
/* 251 */     File file = new File(paramString + File.separator + paramFile.getName());
/*     */ 
/*     */     
/* 254 */     FileOutputStream fileOutputStream = null;
/*     */     try {
/* 256 */       fileOutputStream = new FileOutputStream(file);
/*     */       
/* 258 */       fileOutputStream.write(arrayOfByte);
/* 259 */     } catch (FileNotFoundException fileNotFoundException) {
/* 260 */       fileNotFoundException.printStackTrace();
/* 261 */     } catch (IOException iOException) {
/*     */       
/* 263 */       iOException.printStackTrace();
/*     */     } finally {
/* 265 */       if (null != fileOutputStream) {
/*     */         try {
/* 267 */           fileOutputStream.flush();
/* 268 */           fileOutputStream.close();
/* 269 */         } catch (IOException iOException) {
/* 270 */           iOException.printStackTrace();
/*     */         } 
/*     */       }
/*     */       try {
/* 274 */         fileInputStream.close();
/* 275 */       } catch (IOException iOException) {
/* 276 */         iOException.printStackTrace();
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static final byte[] readInputStream(InputStream paramInputStream) throws IOException {
/* 284 */     byte[] arrayOfByte = new byte[1024];
/* 285 */     int i = 0;
/* 286 */     ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
/* 287 */     while ((i = paramInputStream.read(arrayOfByte)) != -1) {
/* 288 */       byteArrayOutputStream.write(arrayOfByte, 0, i);
/*     */     }
/*     */     try {
/* 291 */       byteArrayOutputStream.close();
/* 292 */     } catch (IOException iOException) {
/*     */       
/* 294 */       iOException.printStackTrace();
/*     */     } 
/* 296 */     return byteArrayOutputStream.toByteArray();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String acquireHtmlToString(File paramFile) {
/* 303 */     StringBuffer stringBuffer = new StringBuffer();
/* 304 */     String str = null;
/* 305 */     BufferedReader bufferedReader = null;
/*     */     try {
/* 307 */       bufferedReader = new BufferedReader(new FileReader(paramFile));
/* 308 */       while ((str = bufferedReader.readLine()) != null) {
/* 309 */         stringBuffer.append(str.trim());
/*     */       }
/* 311 */     } catch (FileNotFoundException fileNotFoundException) {
/* 312 */       fileNotFoundException.printStackTrace();
/* 313 */     } catch (IOException iOException) {
/* 314 */       iOException.printStackTrace();
/*     */     } finally {
/* 316 */       if (null != bufferedReader) {
/*     */         try {
/* 318 */           bufferedReader.close();
/* 319 */         } catch (IOException iOException) {
/* 320 */           iOException.printStackTrace();
/*     */         } 
/*     */       }
/*     */     } 
/* 324 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void delAllFile(File paramFile) {
/* 332 */     if (!paramFile.isDirectory()) {
/* 333 */       paramFile.delete();
/*     */     } else {
/* 335 */       File[] arrayOfFile = paramFile.listFiles();
/*     */ 
/*     */       
/* 338 */       if (arrayOfFile.length == 0) {
/* 339 */         paramFile.delete();
/*     */         
/*     */         return;
/*     */       } 
/*     */       
/* 344 */       for (File file : arrayOfFile) {
/* 345 */         if (file.isDirectory()) {
/* 346 */           delAllFile(file);
/*     */         } else {
/* 348 */           file.delete();
/*     */         } 
/*     */       } 
/*     */ 
/*     */       
/* 353 */       paramFile.delete();
/*     */     } 
/*     */   }
/*     */   
/*     */   public static void delteFilePackageZip() {
/* 358 */     String str = GCONST.getRootPath() + "templetecheck" + File.separator + "downloadpackage";
/* 359 */     File file = new File(str);
/* 360 */     if (file.isDirectory()) {
/* 361 */       File[] arrayOfFile = file.listFiles();
/* 362 */       if (null != arrayOfFile && arrayOfFile.length > 0) {
/* 363 */         for (File file1 : arrayOfFile) {
/* 364 */           if (file1.exists()) {
/* 365 */             delAllFile(file1);
/*     */           }
/*     */         } 
/*     */       }
/*     */     } 
/*     */   }
/*     */   
/*     */   private static ZipOutputStream getZipOutputStream(String paramString) {
/* 373 */     paramString = paramString.endsWith(".zip") ? paramString : (paramString + ".zip");
/*     */     
/* 375 */     FileOutputStream fileOutputStream = null;
/*     */     try {
/* 377 */       File file = new File(paramString);
/* 378 */       if (!file.getParentFile().exists())
/* 379 */         file.getParentFile().mkdirs(); 
/* 380 */       fileOutputStream = new FileOutputStream(paramString);
/* 381 */     } catch (FileNotFoundException fileNotFoundException) {
/* 382 */       fileNotFoundException.printStackTrace();
/*     */     } 
/* 384 */     BufferedOutputStream bufferedOutputStream = new BufferedOutputStream(fileOutputStream);
/*     */     
/* 386 */     return new ZipOutputStream(bufferedOutputStream);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void execute(String paramString1, String paramString2) {
/* 393 */     File file = new File(paramString1);
/* 394 */     String str = paramString1.substring(paramString1.lastIndexOf("" + File.separatorChar) + 1);
/* 395 */     ZipOutputStream zipOutputStream = getZipOutputStream(paramString2);
/* 396 */     zipPack(zipOutputStream, file, str);
/*     */     try {
/* 398 */       if (null != zipOutputStream) {
/* 399 */         zipOutputStream.close();
/*     */       }
/* 401 */     } catch (IOException iOException) {
/* 402 */       iOException.printStackTrace();
/*     */     } 
/*     */   }
/*     */   
/*     */   private static void zipPack(ZipOutputStream paramZipOutputStream, File paramFile, String paramString) {
/* 407 */     if (paramFile.isDirectory()) {
/*     */       
/* 409 */       packFolder(paramZipOutputStream, paramFile, paramString);
/*     */     } else {
/*     */       
/* 412 */       packFile(paramZipOutputStream, paramFile, paramString);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static void packFolder(ZipOutputStream paramZipOutputStream, File paramFile, String paramString) {
/* 421 */     File[] arrayOfFile = paramFile.listFiles();
/*     */     
/* 423 */     paramString = (paramString.length() == 0) ? "" : (paramString + "" + File.separatorChar);
/* 424 */     for (File file : arrayOfFile) {
/* 425 */       zipPack(paramZipOutputStream, file, paramString + file.getName());
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static void packFile(ZipOutputStream paramZipOutputStream, File paramFile, String paramString) {
/* 434 */     ZipEntry zipEntry = new ZipEntry(paramString);
/*     */ 
/*     */     
/* 437 */     zipEntry.setSize(paramFile.length());
/* 438 */     zipEntry.setTime(paramFile.lastModified());
/*     */     try {
/* 440 */       paramZipOutputStream.putNextEntry(zipEntry);
/* 441 */     } catch (IOException iOException) {
/* 442 */       iOException.printStackTrace();
/*     */     } 
/* 444 */     FileInputStream fileInputStream = null;
/*     */     try {
/* 446 */       fileInputStream = new FileInputStream(paramFile);
/* 447 */     } catch (FileNotFoundException fileNotFoundException) {
/* 448 */       fileNotFoundException.printStackTrace();
/*     */     } 
/* 450 */     int i = 0;
/*     */     
/*     */     try {
/* 453 */       while ((i = fileInputStream.read(B_ARRAY, 0, BUFFER)) != -1) {
/* 454 */         paramZipOutputStream.write(B_ARRAY, 0, i);
/*     */       }
/* 456 */     } catch (IOException iOException) {
/* 457 */       iOException.printStackTrace();
/* 458 */     } catch (NullPointerException nullPointerException) {
/* 459 */       System.err
/* 460 */         .println("NullPointerException info ======= [FileInputStream is null]");
/*     */     } finally {
/*     */       try {
/* 463 */         if (null != fileInputStream) {
/* 464 */           fileInputStream.close();
/*     */         }
/* 466 */         if (null != paramZipOutputStream) {
/* 467 */           paramZipOutputStream.closeEntry();
/*     */         }
/* 469 */       } catch (IOException iOException) {}
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/TempFilesDownLoadUtils.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */