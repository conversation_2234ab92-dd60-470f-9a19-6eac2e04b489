/*      */ package weaver.templetecheck;
/*      */ 
/*      */ import java.io.BufferedReader;
/*      */ import java.io.BufferedWriter;
/*      */ import java.io.ByteArrayInputStream;
/*      */ import java.io.File;
/*      */ import java.io.FileInputStream;
/*      */ import java.io.FileOutputStream;
/*      */ import java.io.IOException;
/*      */ import java.io.InputStreamReader;
/*      */ import java.io.OutputStreamWriter;
/*      */ import java.io.UnsupportedEncodingException;
/*      */ import java.lang.reflect.Method;
/*      */ import java.net.URLDecoder;
/*      */ import java.util.ArrayList;
/*      */ import java.util.Arrays;
/*      */ import java.util.Collections;
/*      */ import java.util.Comparator;
/*      */ import java.util.Date;
/*      */ import java.util.HashMap;
/*      */ import java.util.HashSet;
/*      */ import java.util.Iterator;
/*      */ import java.util.LinkedHashSet;
/*      */ import java.util.List;
/*      */ import java.util.Map;
/*      */ import java.util.Set;
/*      */ import java.util.regex.Matcher;
/*      */ import java.util.regex.Pattern;
/*      */ import javax.servlet.http.HttpServletRequest;
/*      */ import javax.servlet.http.HttpServletResponse;
/*      */ import org.apache.commons.io.FileUtils;
/*      */ import org.dom4j.Document;
/*      */ import org.dom4j.Element;
/*      */ import org.dom4j.io.SAXReader;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.conn.RecordSetTrans;
/*      */ import weaver.file.Prop;
/*      */ import weaver.general.BaseBean;
/*      */ import weaver.general.ThreadVarLanguage;
/*      */ import weaver.general.Util;
/*      */ import weaver.hrm.User;
/*      */ import weaver.systeminfo.SystemEnv;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ public class MatchUtil
/*      */ {
/*   52 */   private String tabtype = "";
/*   53 */   private String ishtml = "";
/*      */   private String sysversion;
/*   55 */   private String status = "0";
/*   56 */   private static HashMap<String, ArrayList<HashMap<String, String>>> matchResult = new HashMap<>();
/*   57 */   private static List<Map<String, String>> result = new ArrayList<>();
/*      */   
/*   59 */   private Set<String> filesets = new LinkedHashSet<>();
/*   60 */   private List<String> matchruletype = new ArrayList<>();
/*   61 */   FileUtil fileUtil = new FileUtil();
/*      */ 
/*      */   
/*   64 */   private static String thetype = "";
/*   65 */   private static String thetabtype = "";
/*   66 */   private static String theishtml = "";
/*   67 */   private static String theruleid = "";
/*   68 */   private static String thename = "";
/*   69 */   private static String thedescription = "";
/*   70 */   private static String thestatus = "";
/*      */ 
/*      */   
/*      */   public List<Map<String, String>> getMatchResult(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*   74 */     String str1 = Util.null2String(paramMap.get("type"));
/*   75 */     this.tabtype = Util.null2String(paramMap.get("tabtype"));
/*   76 */     this.ishtml = Util.null2String(paramMap.get("ishtml"));
/*   77 */     String str2 = Util.null2String(paramMap.get("ruleid"));
/*   78 */     String str3 = Util.null2String(changeStr4(paramMap.get("name")));
/*   79 */     String str4 = Util.null2String(changeStr4(paramMap.get("description")));
/*   80 */     this.status = Util.null2String(paramMap.get("status"));
/*      */     
/*   82 */     String str5 = Util.null2String(paramMap.get("pageIndex"));
/*      */ 
/*      */     
/*   85 */     String str6 = Util.null2String(paramMap.get("match2excel"));
/*   86 */     if ("match2excel".equals(str6)) {
/*   87 */       if ("0".equals(this.ishtml) || "4".equals(this.ishtml) || "5".equals(this.ishtml)) {
/*   88 */         return getMatchResultFromDB();
/*      */       }
/*   90 */       return result;
/*      */     } 
/*      */ 
/*      */     
/*   94 */     if (thetype.equals(str1) && thetabtype.equals(this.tabtype) && theishtml.equals(this.ishtml) && theruleid
/*   95 */       .equals(str2) && thename.equals(str3) && thedescription.equals(str4) && thestatus
/*   96 */       .equals(this.status) && !"1".equals(str5)) {
/*      */       
/*   98 */       if ("0".equals(this.ishtml) || "4".equals(this.ishtml) || "5".equals(this.ishtml)) {
/*   99 */         (new BaseBean()).writeLog("=====返回之前的数据库内容" + this.ishtml);
/*  100 */         return getMatchResultFromDB();
/*      */       } 
/*  102 */       if (result.size() > 0) {
/*  103 */         (new BaseBean()).writeLog("=====返回之前的缓存内容" + this.ishtml);
/*  104 */         return result;
/*      */       } 
/*  106 */       result = new ArrayList<>();
/*      */     }
/*      */     else {
/*      */       
/*  110 */       (new BaseBean()).writeLog("=====清空所有内容");
/*  111 */       result = new ArrayList<>();
/*  112 */       if ("0".equals(this.ishtml) || "4".equals(this.ishtml) || "5".equals(this.ishtml)) {
/*  113 */         clearMatchResultFromDB();
/*      */       }
/*      */     } 
/*      */     
/*  117 */     matchResult = new HashMap<>();
/*  118 */     this.filesets = new LinkedHashSet<>();
/*      */ 
/*      */ 
/*      */     
/*  122 */     thetype = str1;
/*  123 */     thetabtype = this.tabtype;
/*  124 */     theishtml = this.ishtml;
/*  125 */     theruleid = str2;
/*  126 */     thename = str3;
/*  127 */     thedescription = str4;
/*  128 */     thestatus = this.status;
/*      */     
/*  130 */     RecordSet recordSet = new RecordSet();
/*      */     try {
/*      */       List<Map<String, String>> list;
/*  133 */       CheckUtil checkUtil = new CheckUtil("0");
/*  134 */       if (null != str2 && !"".equals(str2)) {
/*  135 */         list = checkUtil.getRuleById(this.tabtype, str2, str3, str4);
/*      */       } else {
/*  137 */         list = checkUtil.getAllFileRules(this.tabtype, str3, str4);
/*      */       } 
/*      */       
/*  140 */       RulePath rulePath = new RulePath();
/*  141 */       String str = Util.null2String(rulePath.getpath(this.tabtype));
/*  142 */       if ("4".equals(this.ishtml)) {
/*  143 */         ArrayList arrayList = new ArrayList();
/*  144 */         ArrayList<String> arrayList1 = new ArrayList();
/*  145 */         recordSet.execute("select id,pagecontent from AppHomePage WHERE pagecontent is not NULL");
/*  146 */         while (recordSet.next())
/*      */         {
/*  148 */           arrayList1.add(recordSet.getString("id"));
/*      */         }
/*      */         
/*  151 */         matchMoblieModeHtml(list, arrayList1, result);
/*      */       } else {
/*      */         
/*  154 */         ArrayList<String> arrayList = new ArrayList();
/*  155 */         if ("0".equals(this.ishtml)) {
/*  156 */           recordSet.execute("select syspath from workflow_nodehtmllayout WHERE syspath is not NULL");
/*  157 */           while (recordSet.next()) {
/*  158 */             arrayList.add(recordSet.getString("syspath"));
/*      */           }
/*  160 */         } else if ("5".equals(this.ishtml)) {
/*  161 */           recordSet.execute("select syspath from modehtmllayout WHERE syspath is not NULL");
/*  162 */           while (recordSet.next()) {
/*  163 */             arrayList.add(recordSet.getString("syspath"));
/*      */           }
/*      */         } else {
/*  166 */           String[] arrayOfString = str.split(",");
/*  167 */           for (byte b = 0; b < arrayOfString.length; b++) {
/*  168 */             arrayList.add(arrayOfString[b]);
/*      */           }
/*      */         } 
/*  171 */         if ("1".equals(this.ishtml) || "2".equals(this.ishtml)) {
/*  172 */           recordSet.execute("select cversion from license");
/*  173 */           if (recordSet.next()) {
/*  174 */             this.sysversion = recordSet.getString("cversion");
/*      */           }
/*      */         } 
/*      */         
/*  178 */         matchpath(arrayList, list, result);
/*  179 */         (new BaseBean()).writeLog("###check result size :" + result.size());
/*      */       }
/*      */     
/*  182 */     } catch (Exception exception) {
/*  183 */       exception.printStackTrace();
/*      */     } 
/*      */ 
/*      */     
/*  187 */     setMatchResultToDB(result);
/*  188 */     if ("0".equals(this.ishtml) || "4".equals(this.ishtml) || "5".equals(this.ishtml))
/*      */     {
/*  190 */       return getMatchResultFromDB();
/*      */     }
/*  192 */     return result;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void matchpath(ArrayList<String> paramArrayList, List<Map<String, String>> paramList1, List<Map<String, String>> paramList2) throws Exception {
/*  204 */     (new BaseBean()).writeLog("文件规则检测开始result.size()：" + paramList2.size());
/*  205 */     int i = 0;
/*  206 */     if (paramArrayList.size() == 1) {
/*  207 */       File file = new File(paramArrayList.get(0));
/*  208 */       if (file.isDirectory()) {
/*  209 */         File[] arrayOfFile = file.listFiles();
/*  210 */         i = arrayOfFile.length;
/*      */       } else {
/*  212 */         i = 1;
/*      */       } 
/*      */     } 
/*  215 */     (new BaseBean()).writeLog("需要检测的文件个数为：" + i);
/*  216 */     (new BaseBean()).writeLog("需要检测的规则个数为：" + paramList1.size());
/*  217 */     for (byte b = 0; b < paramArrayList.size(); b++) {
/*  218 */       (new BaseBean()).writeLog("开始检测文件：" + this.fileUtil.getPath(paramArrayList.get(b)));
/*  219 */       long l = (new Date()).getTime();
/*  220 */       matchFile(new File(this.fileUtil.getPath(paramArrayList.get(b))), paramList1, paramList2);
/*  221 */       (new BaseBean()).writeLog("已经检测文件：" + this.fileUtil.getPath(paramArrayList.get(b)) + "结束,检测耗时：" + ((new Date()).getTime() - l) + "毫秒");
/*      */     } 
/*  223 */     (new BaseBean()).writeLog("文件规则检测结束result.size()：" + paramList2.size());
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void matchFile(File paramFile, List<Map<String, String>> paramList1, List<Map<String, String>> paramList2) throws Exception {
/*  235 */     if (paramFile.exists()) {
/*  236 */       Object object = null;
/*  237 */       if (paramFile.isFile()) {
/*  238 */         match(paramFile, paramList1, paramList2);
/*      */       } else {
/*  240 */         File[] arrayOfFile = paramFile.listFiles();
/*  241 */         if (arrayOfFile.length > 0) {
/*  242 */           for (byte b = 0; b < arrayOfFile.length; b++) {
/*  243 */             (new BaseBean()).writeLog("开始检测文件：" + this.fileUtil.getPath(arrayOfFile[b].getPath()));
/*  244 */             String str = this.fileUtil.getPath(arrayOfFile[b].getPath());
/*  245 */             if (arrayOfFile[b].isFile() && !getMatchRuleType().contains(str.substring(str.lastIndexOf(".") + 1, str.length()))) {
/*  246 */               (new BaseBean()).writeLog("已经检测文件：" + this.fileUtil.getPath(arrayOfFile[b].getPath()) + "结束,该文件类型不在检测范围内，不做处理");
/*      */             } else {
/*      */               
/*  249 */               long l = (new Date()).getTime();
/*  250 */               matchFile(arrayOfFile[b], paramList1, paramList2);
/*  251 */               (new BaseBean()).writeLog("已经检测文件：" + this.fileUtil.getPath(arrayOfFile[b].getPath()) + "结束,检测耗时：" + ((new Date()).getTime() - l) + "毫秒");
/*      */             } 
/*      */           } 
/*      */         }
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void match(File paramFile, List<Map<String, String>> paramList1, List<Map<String, String>> paramList2) throws Exception {
/*  266 */     RecordSet recordSet = new RecordSet();
/*  267 */     String str1 = "";
/*  268 */     String str2 = "";
/*  269 */     Document document = null;
/*  270 */     if ("0".equals(this.ishtml)) {
/*  271 */       recordSet.execute("select t3.workflowname as workflowname,t2.nodename as nodename from workflow_nodehtmllayout t,workflow_nodebase t2,workflow_base t3 WHERE t.syspath like '%" + paramFile.getName() + "%' and t.nodeid=t2.id and t.workflowid=t3.id");
/*  272 */       if (recordSet.next()) {
/*  273 */         str1 = recordSet.getString("workflowname");
/*  274 */         str2 = recordSet.getString("nodename");
/*      */       } 
/*      */     } 
/*  277 */     if ("5".equals(this.ishtml)) {
/*  278 */       recordSet.execute("select t.layoutname as layoutname,t2.modename as modename from modehtmllayout t,modeinfo t2 WHERE t.syspath like '%" + paramFile.getName() + "%' and t.modeid=t2.id");
/*  279 */       if (recordSet.next()) {
/*  280 */         str1 = recordSet.getString("modename");
/*  281 */         str2 = recordSet.getString("layoutname");
/*      */       } 
/*      */     } 
/*      */     
/*  285 */     String str3 = "GBK";
/*  286 */     boolean bool = FileCharsetDetector.check(paramFile);
/*  287 */     if (bool) {
/*  288 */       str3 = "UTF-8";
/*      */     } else {
/*  290 */       str3 = "GBK";
/*      */     } 
/*  292 */     String str4 = "";
/*  293 */     if ("1".equals(this.ishtml) || "2".equals(this.ishtml)) {
/*      */ 
/*      */       
/*  296 */       if (paramFile.length() > 0L) {
/*  297 */         ReadXml readXml = new ReadXml();
/*  298 */         document = readXml.read(paramFile.getPath());
/*      */       
/*      */       }
/*      */ 
/*      */     
/*      */     }
/*      */     else {
/*      */ 
/*      */       
/*  307 */       InputStreamReader inputStreamReader = new InputStreamReader(new FileInputStream(paramFile), str3);
/*      */       
/*  309 */       BufferedReader bufferedReader = new BufferedReader(inputStreamReader);
/*  310 */       String str = null;
/*  311 */       boolean bool1 = true;
/*  312 */       StringBuffer stringBuffer = new StringBuffer();
/*  313 */       while ((str = bufferedReader.readLine()) != null) {
/*  314 */         stringBuffer.append(str).append("\r\n");
/*      */       }
/*  316 */       str4 = stringBuffer.toString();
/*  317 */       bufferedReader.close();
/*  318 */       inputStreamReader.close();
/*      */     } 
/*      */ 
/*      */     
/*  322 */     HashSet<String> hashSet = new HashSet();
/*  323 */     for (byte b = 0; b < paramList1.size(); b++) {
/*  324 */       Map map = paramList1.get(b);
/*      */       
/*  326 */       String str5 = (String)map.get("flageid");
/*  327 */       String str6 = (String)map.get("name");
/*  328 */       String str7 = (String)map.get("desc");
/*  329 */       String str8 = (String)map.get("content");
/*  330 */       String str9 = (String)map.get("replacecontent");
/*  331 */       String str10 = (String)map.get("version");
/*  332 */       String str11 = (String)map.get("xpath");
/*  333 */       String str12 = (String)map.get("requisite");
/*  334 */       String str13 = (String)map.get("ruletype");
/*      */ 
/*      */       
/*  337 */       if (("1".equals(this.ishtml) || "2".equals(this.ishtml)) && str10 != null && !"".equals(str10)) {
/*  338 */         String[] arrayOfString = this.sysversion.split("\\+");
/*  339 */         if (arrayOfString.length == 2) {
/*  340 */           String str14 = arrayOfString[0];
/*  341 */           String str15 = arrayOfString[1].replace("KB", "");
/*      */           
/*  343 */           if (str10.indexOf("KB") < 0) {
/*  344 */             if (!str14.equals(str10)) {
/*      */               continue;
/*      */             }
/*      */           } else {
/*  348 */             String str = str10.replace("KB", "");
/*      */             
/*  350 */             long l1 = Long.valueOf(str15).longValue();
/*  351 */             long l2 = Long.valueOf(str).longValue();
/*  352 */             if (l1 < l2) {
/*      */               continue;
/*      */             }
/*      */           } 
/*      */         } else {
/*      */           
/*  358 */           String str14 = str10.replace("KB", "");
/*  359 */           String str15 = arrayOfString[0];
/*      */           
/*  361 */           if (str15.compareTo(str14) < 0) {
/*      */             continue;
/*      */           }
/*      */         } 
/*      */       } 
/*      */       
/*  367 */       if ("1".equals(this.ishtml) || "2".equals(this.ishtml)) {
/*      */         
/*  369 */         str11 = changeStr3(str11);
/*  370 */         HashMap<Object, Object> hashMap = new HashMap<>();
/*  371 */         hashMap.put("ruleid", str5);
/*  372 */         hashMap.put("name", changeStr3(str6));
/*  373 */         hashMap.put("desc", changeStr3(str7));
/*  374 */         hashMap.put("content", changeStr3(str8));
/*  375 */         hashMap.put("file", paramFile.getPath());
/*  376 */         hashMap.put("version", str10);
/*  377 */         hashMap.put("xpath", str11);
/*  378 */         hashMap.put("requisite", changeStr3(str12));
/*      */ 
/*      */         
/*  381 */         if (str11 != null && !"".equals(str11) && hashSet.contains(str11)) {
/*  382 */           hashMap.put("replacecontent", changeStr3(str9));
/*  383 */           hashMap.put("status", "" + SystemEnv.getHtmlLabelName(10004220, ThreadVarLanguage.getLang()) + "");
/*  384 */           if ("8".equals(this.status) || "0".equals(this.status)) {
/*  385 */             paramList2.add(hashMap);
/*      */             continue;
/*      */           } 
/*      */         } else {
/*  389 */           hashSet.add(str11);
/*      */         } 
/*      */         
/*  392 */         hashMap.put("replacecontent", changeStr3(str9));
/*      */ 
/*      */         
/*  395 */         String str = checkXmlFile(str11, document, str9);
/*  396 */         if ("ok".equals(str)) {
/*  397 */           hashMap.put("status", "" + SystemEnv.getHtmlLabelName(10004085, ThreadVarLanguage.getLang()) + "");
/*  398 */           if ("1".equals(this.status) || "0".equals(this.status)) {
/*  399 */             paramList2.add(hashMap);
/*      */           }
/*  401 */         } else if ("multierror".equals(str)) {
/*  402 */           hashMap.put("status", "" + SystemEnv.getHtmlLabelName(10004088, ThreadVarLanguage.getLang()) + "");
/*      */           
/*  404 */           if ("5".equals(this.status) || "0".equals(this.status)) {
/*  405 */             paramList2.add(hashMap);
/*      */           }
/*  407 */         } else if ("xpatherror".equals(str)) {
/*  408 */           hashMap.put("status", "" + SystemEnv.getHtmlLabelName(10004087, ThreadVarLanguage.getLang()) + "");
/*      */           
/*  410 */           if ("4".equals(this.status) || "0".equals(this.status)) {
/*  411 */             paramList2.add(hashMap);
/*      */           }
/*  413 */         } else if ("diff".equals(str)) {
/*  414 */           hashMap.put("status", "" + SystemEnv.getHtmlLabelName(10004086, ThreadVarLanguage.getLang()) + "");
/*      */           
/*  416 */           if ("3".equals(this.status) || "0".equals(this.status)) {
/*  417 */             paramList2.add(hashMap);
/*      */           }
/*  419 */         } else if ("xpathorcontenterror".equals(str)) {
/*  420 */           hashMap.put("status", "" + SystemEnv.getHtmlLabelName(10004090, ThreadVarLanguage.getLang()) + "");
/*      */           
/*  422 */           if ("0".equals(this.status) || "6".equals(this.status)) {
/*  423 */             paramList2.add(hashMap);
/*      */           }
/*  425 */         } else if ("contenterror".equals(str)) {
/*  426 */           hashMap.put("status", "" + SystemEnv.getHtmlLabelName(10004089, ThreadVarLanguage.getLang()) + "XML" + SystemEnv.getHtmlLabelName(15196, ThreadVarLanguage.getLang()) + "");
/*      */           
/*  428 */           if ("0".equals(this.status) || "7".equals(this.status)) {
/*  429 */             paramList2.add(hashMap);
/*      */           }
/*      */         } else {
/*  432 */           hashMap.put("status", "" + SystemEnv.getHtmlLabelName(129337, ThreadVarLanguage.getLang()) + "");
/*      */           
/*  434 */           if ("2".equals(this.status) || "0".equals(this.status)) {
/*  435 */             paramList2.add(hashMap);
/*      */           }
/*      */         } 
/*      */ 
/*      */         
/*  440 */         if (str11 != null && !"".equals(str11)) {
/*  441 */           List<Element> list = document.selectNodes(changeStr3(str11));
/*      */           
/*  443 */           if (list.size() == 1)
/*      */           {
/*  445 */             if ("ok".equals(str)) {
/*  446 */               hashMap.put("localconfig", changeStr3(str9));
/*      */             } else {
/*  448 */               hashMap.put("localconfig", changeStr3(((Element)list.get(0)).asXML()));
/*      */             } 
/*      */           }
/*      */         } else {
/*      */           
/*  453 */           hashMap.put("localconfig", "");
/*      */         } 
/*      */         continue;
/*      */       } 
/*  457 */       if ("1".equals(str13)) {
/*      */         try {
/*  459 */           Class<?> clazz = Class.forName(str8);
/*  460 */           Method method = clazz.getMethod("checkRule", new Class[] { String.class });
/*  461 */           List list = (List)method.invoke(clazz.newInstance(), new Object[] { paramFile.getPath() });
/*  462 */           for (String str14 : list) {
/*  463 */             HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  464 */             hashMap1.put("ruleid", str5);
/*  465 */             hashMap1.put("name", changeStr3(str6));
/*  466 */             hashMap1.put("desc", changeStr3(str7));
/*  467 */             hashMap1.put("workflowname", changeStr3(str1));
/*  468 */             hashMap1.put("nodename", changeStr3(str2));
/*  469 */             hashMap1.put("content", changeStr3(str8));
/*  470 */             String str15 = changeStr3(str9);
/*  471 */             if ("".equals(str15)) {
/*  472 */               str15 = "" + SystemEnv.getHtmlLabelName(10004221, ThreadVarLanguage.getLang()) + "";
/*      */             }
/*  474 */             hashMap1.put("replacecontent", str15);
/*      */             
/*  476 */             String str16 = paramFile.getPath();
/*  477 */             str16 = str16.replaceAll("\\\\", "/");
/*  478 */             hashMap1.put("file", str16);
/*      */             
/*  480 */             hashMap1.put("version", str10);
/*  481 */             hashMap1.put("matchcontent", changeStr3(str14));
/*  482 */             hashMap1.put("line", "");
/*      */             
/*  484 */             if (matchResult.containsKey(str16)) {
/*  485 */               ArrayList<HashMap<Object, Object>> arrayList = (ArrayList)matchResult.get(str16);
/*  486 */               arrayList.add(hashMap1);
/*      */             } else {
/*  488 */               ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  489 */               arrayList.add(hashMap1);
/*  490 */               matchResult.put(str16, arrayList);
/*      */             } 
/*  492 */             HashMap<Object, Object> hashMap2 = new HashMap<>();
/*      */             
/*  494 */             if (this.filesets.contains(str16)) {
/*      */               continue;
/*      */             }
/*  497 */             this.filesets.add(str16);
/*  498 */             hashMap2.put("file", str16);
/*  499 */             hashMap2.put("workflowname", changeStr3(str1));
/*  500 */             hashMap2.put("nodename", changeStr3(str2));
/*  501 */             hashMap2.put("detail", "<a href='javascript:showdetail(\"" + str16 + "\",\"" + str5 + "\",0)'>" + SystemEnv.getHtmlLabelName(22045, ThreadVarLanguage.getLang()) + "</a>");
/*  502 */             paramList2.add(hashMap2);
/*      */           }
/*      */         
/*  505 */         } catch (Exception exception) {
/*  506 */           exception.printStackTrace();
/*  507 */           (new BaseBean()).writeLog("请检查接口" + str8 + "是否正确!!!错误信息为：" + exception.toString());
/*      */         }
/*      */       
/*      */       }
/*      */       else {
/*      */         
/*  513 */         Pattern pattern = null;
/*      */ 
/*      */         
/*  516 */         pattern = Pattern.compile(changeStr2(str8));
/*  517 */         Matcher matcher = pattern.matcher(str4);
/*  518 */         while (matcher.find()) {
/*  519 */           HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  520 */           hashMap1.put("ruleid", str5);
/*  521 */           hashMap1.put("name", changeStr3(str6));
/*  522 */           hashMap1.put("desc", changeStr3(str7));
/*  523 */           hashMap1.put("workflowname", changeStr3(str1));
/*  524 */           hashMap1.put("nodename", changeStr3(str2));
/*  525 */           hashMap1.put("content", changeStr3(str8));
/*  526 */           String str14 = changeStr3(str9);
/*  527 */           if ("".equals(str14)) {
/*  528 */             str14 = "" + SystemEnv.getHtmlLabelName(10004221, ThreadVarLanguage.getLang()) + "";
/*      */           }
/*  530 */           hashMap1.put("replacecontent", "" + str14);
/*      */           
/*  532 */           String str15 = paramFile.getPath();
/*  533 */           str15 = str15.replaceAll("\\\\", "/");
/*  534 */           hashMap1.put("file", str15);
/*      */           
/*  536 */           hashMap1.put("version", str10);
/*  537 */           hashMap1.put("matchcontent", changeStr3(matcher.group()));
/*  538 */           hashMap1.put("line", "");
/*      */           
/*  540 */           if (matchResult.containsKey(str15)) {
/*  541 */             ArrayList<HashMap<Object, Object>> arrayList = (ArrayList)matchResult.get(str15);
/*  542 */             arrayList.add(hashMap1);
/*      */           } else {
/*  544 */             ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  545 */             arrayList.add(hashMap1);
/*  546 */             matchResult.put(str15, arrayList);
/*      */           } 
/*  548 */           HashMap<Object, Object> hashMap2 = new HashMap<>();
/*      */ 
/*      */           
/*  551 */           if (this.filesets.contains(str15)) {
/*      */             continue;
/*      */           }
/*  554 */           this.filesets.add(str15);
/*  555 */           hashMap2.put("file", str15);
/*      */           
/*  557 */           if (("0".equals(this.ishtml) || "5".equals(this.ishtml)) && (
/*  558 */             "".equals(str1) || str1 == null)) {
/*      */             continue;
/*      */           }
/*      */           
/*  562 */           hashMap2.put("workflowname", changeStr3(str1));
/*  563 */           hashMap2.put("nodename", changeStr3(str2));
/*  564 */           hashMap2.put("detail", "<a href='javascript:showdetail(\"" + str15 + "\",\"" + str5 + "\",0)'>" + SystemEnv.getHtmlLabelName(22045, ThreadVarLanguage.getLang()) + "</a>");
/*  565 */           paramList2.add(hashMap2);
/*      */         } 
/*      */       } 
/*      */       continue;
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void matchMoblieModeHtml(List<Map<String, String>> paramList1, ArrayList<String> paramArrayList, List<Map<String, String>> paramList2) {
/*  577 */     RecordSet recordSet = new RecordSet();
/*  578 */     String str1 = "";
/*  579 */     String str2 = "";
/*  580 */     String str3 = "";
/*  581 */     String str4 = "";
/*  582 */     for (byte b = 0; b < paramArrayList.size(); b++) {
/*  583 */       str4 = paramArrayList.get(b);
/*  584 */       recordSet.execute("select t2.appname as appname,t1.pagecontent as pagecontent,t1.pagename as pagename from AppHomePage t1,mobileAppBaseInfo t2 where t1.appid=t2.id and t1.id='" + str4 + "'");
/*  585 */       if (recordSet.next()) {
/*  586 */         str1 = recordSet.getString("pagename");
/*  587 */         str2 = recordSet.getString("appname");
/*  588 */         str3 = recordSet.getString("pagecontent");
/*      */       } 
/*      */       
/*  591 */       for (byte b1 = 0; b1 < paramList1.size(); b1++) {
/*  592 */         Map map = paramList1.get(b1);
/*      */         
/*  594 */         String str5 = (String)map.get("flageid");
/*  595 */         String str6 = (String)map.get("name");
/*  596 */         String str7 = (String)map.get("desc");
/*  597 */         String str8 = (String)map.get("content");
/*  598 */         String str9 = (String)map.get("replacecontent");
/*  599 */         Pattern pattern = null;
/*      */         
/*  601 */         pattern = Pattern.compile(changeStr2(str8));
/*  602 */         Matcher matcher = pattern.matcher(str3);
/*  603 */         while (matcher.find()) {
/*  604 */           HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  605 */           hashMap1.put("ruleid", str5);
/*  606 */           hashMap1.put("name", changeStr3(str6));
/*  607 */           hashMap1.put("desc", changeStr3(str7));
/*  608 */           hashMap1.put("workflowname", changeStr3(str2));
/*  609 */           hashMap1.put("nodename", changeStr3(str1));
/*  610 */           String str = changeStr3(str9);
/*  611 */           if ("".equals(str)) {
/*  612 */             str = "" + SystemEnv.getHtmlLabelName(10004221, ThreadVarLanguage.getLang()) + "";
/*      */           }
/*  614 */           hashMap1.put("replacecontent", "" + str);
/*      */           
/*  616 */           hashMap1.put("matchcontent", changeStr3(matcher.group()));
/*  617 */           hashMap1.put("line", "");
/*      */ 
/*      */           
/*  620 */           if (matchResult.containsKey(str4)) {
/*  621 */             ArrayList<HashMap<Object, Object>> arrayList = (ArrayList)matchResult.get(str4);
/*  622 */             arrayList.add(hashMap1);
/*      */           } else {
/*  624 */             ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  625 */             arrayList.add(hashMap1);
/*  626 */             matchResult.put(str4, arrayList);
/*      */           } 
/*  628 */           HashMap<Object, Object> hashMap2 = new HashMap<>();
/*      */ 
/*      */           
/*  631 */           if (this.filesets.contains(str4)) {
/*      */             continue;
/*      */           }
/*  634 */           this.filesets.add(str4);
/*  635 */           hashMap2.put("file", "" + str4);
/*  636 */           hashMap2.put("workflowname", changeStr3(str2));
/*  637 */           hashMap2.put("nodename", changeStr3(str1));
/*  638 */           hashMap2.put("detail", "<a href='javascript:showdetail(\"" + str4 + "\",\"" + str5 + "\",1)'>" + SystemEnv.getHtmlLabelName(22045, ThreadVarLanguage.getLang()) + "</a>");
/*  639 */           paramList2.add(hashMap2);
/*      */         } 
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean replaceContent(String paramString1, String paramString2, String paramString3) {
/*      */     try {
/*  655 */       replace(paramString1, paramString2, paramString3);
/*  656 */     } catch (Exception exception) {
/*  657 */       exception.printStackTrace();
/*  658 */       return false;
/*      */     } 
/*  660 */     return true;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void replace(String paramString1, String paramString2, String paramString3) throws IOException {
/*      */     List<Map<String, String>> list;
/*  671 */     RecordSet recordSet = new RecordSet();
/*  672 */     Pattern pattern = null;
/*  673 */     ArrayList<Pattern> arrayList = new ArrayList();
/*  674 */     CheckUtil checkUtil = new CheckUtil();
/*      */ 
/*      */     
/*  677 */     if (paramString2 != null && !"".equals(paramString2)) {
/*  678 */       list = checkUtil.getRuleById(paramString1, paramString2, "", "");
/*      */     } else {
/*  680 */       list = checkUtil.getAllFileRules(paramString1, "", "");
/*      */     } 
/*      */     
/*  683 */     ArrayList<String> arrayList1 = new ArrayList();
/*  684 */     RulePath rulePath = new RulePath();
/*  685 */     String str = Util.null2String(rulePath.getpath(paramString1));
/*  686 */     String[] arrayOfString1 = str.split(",");
/*      */     
/*  688 */     if ("0".equals(paramString3)) {
/*  689 */       recordSet.execute("select syspath from workflow_nodehtmllayout WHERE syspath is not NULL ");
/*  690 */       while (recordSet.next()) {
/*  691 */         arrayList1.add(recordSet.getString("syspath"));
/*      */       }
/*      */     } 
/*      */     
/*  695 */     if ("5".equals(this.ishtml)) {
/*  696 */       recordSet.execute("select syspath from modehtmllayout WHERE syspath is not NULL ");
/*  697 */       while (recordSet.next()) {
/*  698 */         arrayList1.add(recordSet.getString("syspath"));
/*      */       }
/*      */     } 
/*      */     
/*  702 */     for (byte b1 = 0; b1 < arrayOfString1.length; b1++) {
/*  703 */       arrayList1.add(arrayOfString1[b1]);
/*      */     }
/*      */     
/*  706 */     String[] arrayOfString2 = new String[list.size()]; byte b2;
/*  707 */     for (b2 = 0; b2 < list.size(); b2++) {
/*  708 */       Map map = list.get(b2);
/*  709 */       String str1 = (String)map.get("content");
/*  710 */       String str2 = (String)map.get("replacecontent");
/*      */ 
/*      */ 
/*      */       
/*  714 */       pattern = Pattern.compile(changeStr2(str1));
/*  715 */       arrayList.add(pattern);
/*      */       
/*  717 */       arrayOfString2[b2] = changeStr2(str2.replaceAll("\\\\n", "ssssssss,@"));
/*      */     } 
/*      */     
/*  720 */     for (b2 = 0; b2 < arrayList1.size(); b2++) {
/*      */       
/*  722 */       File file = new File(this.fileUtil.getPath(arrayList1.get(b2)));
/*  723 */       String str1 = "";
/*  724 */       if (file.exists()) {
/*  725 */         replaceFile(file, arrayList, arrayOfString2);
/*      */       }
/*  727 */       if (file.isFile()) {
/*  728 */         String str2 = file.getPath();
/*  729 */         str1 = str2.substring(0, str2.lastIndexOf("" + File.separatorChar));
/*  730 */         str1 = str1 + "_cbak" + File.separatorChar;
/*      */       } else {
/*  732 */         str1 = file.getPath() + "_cbak";
/*      */       } 
/*      */       
/*  735 */       deleteFile(new File(str1));
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean replaceContent(String paramString1, String paramString2, String paramString3, String paramString4) {
/*      */     try {
/*  751 */       RecordSet recordSet = new RecordSet();
/*  752 */       Pattern pattern = null;
/*  753 */       ArrayList<Pattern> arrayList = new ArrayList();
/*  754 */       ArrayList<String> arrayList1 = new ArrayList();
/*  755 */       CheckUtil checkUtil = new CheckUtil();
/*  756 */       ArrayList<String> arrayList2 = new ArrayList();
/*      */ 
/*      */       
/*  759 */       String str = "";
/*  760 */       LinkedHashSet<String> linkedHashSet = new LinkedHashSet();
/*  761 */       if ("4".equals(paramString4)) {
/*  762 */         Set<String> set = matchResult.keySet();
/*  763 */         Iterator<String> iterator = set.iterator();
/*  764 */         while (iterator.hasNext()) {
/*  765 */           String str1 = iterator.next();
/*  766 */           ArrayList arrayList3 = matchResult.get(str1);
/*  767 */           Iterator<HashMap> iterator1 = arrayList3.iterator();
/*  768 */           while (iterator1.hasNext()) {
/*  769 */             HashMap hashMap = iterator1.next();
/*  770 */             String str2 = (String)hashMap.get("ruleid");
/*  771 */             linkedHashSet.add(str2);
/*      */           } 
/*      */         } 
/*      */ 
/*      */         
/*  776 */         if ("0".equals(paramString3)) {
/*  777 */           recordSet.execute("select syspath from workflow_nodehtmllayout WHERE syspath is not NULL ");
/*  778 */           while (recordSet.next()) {
/*  779 */             arrayList2.add(recordSet.getString("syspath"));
/*      */           }
/*  781 */         } else if ("5".equals(paramString3)) {
/*  782 */           recordSet.execute("select syspath from modehtmllayout WHERE syspath is not NULL ");
/*  783 */           while (recordSet.next()) {
/*  784 */             arrayList2.add(recordSet.getString("syspath"));
/*      */           }
/*      */         } else {
/*      */           
/*  788 */           RulePath rulePath = new RulePath();
/*  789 */           String str1 = Util.null2String(rulePath.getpath(paramString1));
/*  790 */           String[] arrayOfString1 = str1.split(",");
/*  791 */           for (byte b1 = 0; b1 < arrayOfString1.length; b1++) {
/*  792 */             arrayList2.add(arrayOfString1[b1]);
/*      */           
/*      */           }
/*      */         }
/*      */       
/*      */       }
/*      */       else {
/*      */         
/*  800 */         arrayList2.add(paramString2);
/*  801 */         ArrayList arrayList3 = matchResult.get(paramString2);
/*      */         
/*  803 */         Iterator<HashMap> iterator = arrayList3.iterator();
/*  804 */         while (iterator.hasNext()) {
/*  805 */           HashMap hashMap = iterator.next();
/*  806 */           String str1 = (String)hashMap.get("ruleid");
/*  807 */           linkedHashSet.add(str1);
/*      */         } 
/*      */       } 
/*      */       
/*  811 */       if (linkedHashSet != null) {
/*  812 */         Iterator<String> iterator = linkedHashSet.iterator();
/*  813 */         while (iterator.hasNext()) {
/*  814 */           String str1 = iterator.next();
/*  815 */           str = str + str1 + ",";
/*      */         } 
/*      */       } 
/*      */ 
/*      */ 
/*      */       
/*  821 */       List<Map<String, String>> list = new ArrayList();
/*  822 */       if (str != null && !"".equals(str)) {
/*  823 */         list = checkUtil.getRuleById(paramString1, str, "", "");
/*      */       }
/*      */       
/*  826 */       String[] arrayOfString = new String[list.size()]; byte b;
/*  827 */       for (b = 0; b < list.size(); b++) {
/*  828 */         Map map = list.get(b);
/*  829 */         String str1 = (String)map.get("content");
/*  830 */         String str2 = (String)map.get("replacecontent");
/*      */ 
/*      */ 
/*      */         
/*  834 */         String str3 = (String)map.get("ruletype");
/*  835 */         if ("1".equals(str3)) {
/*      */           
/*  837 */           arrayList1.add(str1);
/*      */         } else {
/*  839 */           pattern = Pattern.compile(changeStr2(str1));
/*  840 */           arrayList.add(pattern);
/*      */           
/*  842 */           arrayOfString[b] = changeStr2(str2.replaceAll("\\\\n", "ssssssss,@"));
/*      */         } 
/*      */       } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  870 */       for (b = 0; b < arrayList2.size(); b++) {
/*      */ 
/*      */         
/*  873 */         File file = new File(this.fileUtil.getPath(arrayList2.get(b)));
/*  874 */         String str1 = "";
/*  875 */         if (file.exists()) {
/*      */           
/*  877 */           if (arrayList1 != null && !arrayList1.isEmpty()) {
/*  878 */             replaceFileWithClassRule(file, arrayList1);
/*      */           }
/*  880 */           replaceFile(file, arrayList, arrayOfString);
/*      */         } 
/*      */         
/*  883 */         if (file.isFile()) {
/*  884 */           String str2 = file.getPath();
/*  885 */           str1 = str2.substring(0, str2.lastIndexOf("" + File.separatorChar));
/*  886 */           str1 = str1 + "_cbak" + File.separatorChar;
/*      */         } else {
/*  888 */           str1 = file.getPath() + "_cbak";
/*      */         } 
/*  890 */         (new BaseBean()).writeLog("删除备份文件：" + this.fileUtil.getPath(str1));
/*      */         
/*  892 */         deleteFile(new File(this.fileUtil.getPath(str1)));
/*      */       } 
/*  894 */     } catch (Exception exception) {
/*  895 */       (new BaseBean()).writeLog(exception.toString());
/*  896 */       exception.printStackTrace();
/*  897 */       return false;
/*      */     } 
/*      */     
/*  900 */     return true;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean replaceContent_MobileMode(String paramString1, String paramString2, String paramString3, String paramString4) {
/*  912 */     RecordSet recordSet = new RecordSet();
/*  913 */     RecordSetTrans recordSetTrans = new RecordSetTrans();
/*      */     try {
/*  915 */       Pattern pattern = null;
/*  916 */       ArrayList<Pattern> arrayList = new ArrayList();
/*  917 */       CheckUtil checkUtil = new CheckUtil();
/*  918 */       ArrayList<String> arrayList1 = new ArrayList();
/*  919 */       ArrayList<String> arrayList2 = new ArrayList();
/*      */ 
/*      */       
/*  922 */       String str = "";
/*  923 */       LinkedHashSet<String> linkedHashSet = new LinkedHashSet();
/*  924 */       if ("4".equals(paramString4)) {
/*  925 */         Set<String> set = matchResult.keySet();
/*  926 */         Iterator<String> iterator = set.iterator();
/*  927 */         String str1 = "";
/*  928 */         while (iterator.hasNext()) {
/*  929 */           String str2 = iterator.next();
/*  930 */           str1 = str1 + str2 + ",";
/*  931 */           ArrayList arrayList3 = matchResult.get(str2);
/*  932 */           Iterator<HashMap> iterator1 = arrayList3.iterator();
/*  933 */           while (iterator1.hasNext()) {
/*  934 */             HashMap hashMap = iterator1.next();
/*  935 */             String str3 = (String)hashMap.get("ruleid");
/*  936 */             linkedHashSet.add(str3);
/*      */           } 
/*      */         } 
/*  939 */         if (str1.endsWith(",")) {
/*  940 */           str1 = str1.substring(0, str1.length() - 1);
/*      */         }
/*  942 */         recordSet.executeSql("select id,pagecontent from apphomepage where id in(" + str1 + ")");
/*  943 */         while (recordSet.next()) {
/*  944 */           arrayList1.add(recordSet.getString("pagecontent"));
/*  945 */           arrayList2.add(recordSet.getString("id"));
/*      */         } 
/*      */       } else {
/*      */         
/*  949 */         recordSet.executeSql("select pagecontent from apphomepage where id ='" + paramString2 + "'");
/*  950 */         if (recordSet.next()) {
/*  951 */           arrayList1.add(recordSet.getString("pagecontent"));
/*  952 */           arrayList2.add(paramString2);
/*      */         } 
/*      */ 
/*      */         
/*  956 */         ArrayList arrayList3 = matchResult.get(paramString2);
/*  957 */         Iterator<HashMap> iterator = arrayList3.iterator();
/*  958 */         while (iterator.hasNext()) {
/*  959 */           HashMap hashMap = iterator.next();
/*  960 */           String str1 = (String)hashMap.get("ruleid");
/*  961 */           linkedHashSet.add(str1);
/*      */         } 
/*      */       } 
/*      */       
/*  965 */       if (linkedHashSet != null) {
/*  966 */         Iterator<String> iterator = linkedHashSet.iterator();
/*  967 */         while (iterator.hasNext()) {
/*  968 */           String str1 = iterator.next();
/*  969 */           str = str + str1 + ",";
/*      */         } 
/*      */       } 
/*      */ 
/*      */ 
/*      */       
/*  975 */       List<Map<String, String>> list = new ArrayList();
/*  976 */       if (str != null && !"".equals(str)) {
/*  977 */         list = checkUtil.getRuleById(paramString1, str, "", "");
/*      */       }
/*      */       
/*  980 */       String[] arrayOfString = new String[list.size()]; byte b;
/*  981 */       for (b = 0; b < list.size(); b++) {
/*  982 */         Map map = list.get(b);
/*  983 */         String str1 = (String)map.get("content");
/*  984 */         String str2 = (String)map.get("replacecontent");
/*      */ 
/*      */ 
/*      */         
/*  988 */         pattern = Pattern.compile(changeStr2(str1));
/*  989 */         arrayList.add(pattern);
/*  990 */         arrayOfString[b] = changeStr2(str2);
/*      */       } 
/*      */       
/*  993 */       recordSetTrans.setAutoCommit(false);
/*  994 */       for (b = 0; b < arrayList1.size(); b++) {
/*      */         
/*  996 */         String str1 = arrayList1.get(b);
/*  997 */         if (str1 != null && !"".equals(str1)) {
/*  998 */           str1 = replace_MobileMode(str1, arrayList, arrayOfString);
/*  999 */           recordSetTrans.executeSql("update apphomepage set pagecontent = '" + str1 + "' where id='" + (String)arrayList2.get(b) + "'");
/*      */         } 
/*      */       } 
/*      */       
/* 1003 */       recordSetTrans.commit();
/* 1004 */       recordSetTrans.setAutoCommit(true);
/* 1005 */     } catch (Exception exception) {
/* 1006 */       exception.printStackTrace();
/* 1007 */       recordSetTrans.setAutoCommit(true);
/* 1008 */       recordSetTrans.rollback();
/* 1009 */       return false;
/*      */     } 
/*      */     
/* 1012 */     return true;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean replaceContentWithPageid(String paramString1, String paramString2, String paramString3, String paramString4) {
/* 1025 */     RecordSet recordSet = new RecordSet();
/* 1026 */     RecordSetTrans recordSetTrans = new RecordSetTrans();
/*      */     try {
/* 1028 */       Pattern pattern = null;
/* 1029 */       ArrayList<Pattern> arrayList = new ArrayList();
/* 1030 */       CheckUtil checkUtil = new CheckUtil();
/* 1031 */       ArrayList<String> arrayList1 = new ArrayList();
/* 1032 */       recordSet.executeSql("select pagecontent from apphomepage where id='" + paramString4 + "'");
/* 1033 */       while (recordSet.next()) {
/* 1034 */         arrayList1.add(recordSet.getString("pagecontent"));
/*      */       }
/*      */       
/* 1037 */       List<Map<String, String>> list = new ArrayList();
/* 1038 */       if (paramString2 != null && !"".equals(paramString2)) {
/* 1039 */         list = checkUtil.getRuleById(paramString1, paramString2, "", "");
/*      */       }
/*      */       
/* 1042 */       String[] arrayOfString = new String[list.size()]; byte b;
/* 1043 */       for (b = 0; b < list.size(); b++) {
/* 1044 */         Map map = list.get(b);
/* 1045 */         String str1 = (String)map.get("content");
/* 1046 */         String str2 = (String)map.get("replacecontent");
/*      */ 
/*      */ 
/*      */         
/* 1050 */         pattern = Pattern.compile(changeStr2(str1));
/* 1051 */         arrayList.add(pattern);
/* 1052 */         arrayOfString[b] = changeStr2(str2);
/*      */       } 
/*      */       
/* 1055 */       recordSetTrans.setAutoCommit(false);
/* 1056 */       for (b = 0; b < arrayList1.size(); b++) {
/*      */         
/* 1058 */         String str = arrayList1.get(b);
/* 1059 */         if (str != null && !"".equals(str)) {
/* 1060 */           str = replace_MobileMode(str, arrayList, arrayOfString);
/* 1061 */           recordSetTrans.executeSql("update apphomepage set pagecontent = '" + str + "' where id='" + paramString4 + "'");
/*      */         } 
/*      */       } 
/*      */       
/* 1065 */       recordSetTrans.commit();
/* 1066 */       recordSetTrans.setAutoCommit(true);
/*      */       
/* 1068 */       removeMatchResult(paramString4, paramString2);
/* 1069 */     } catch (Exception exception) {
/* 1070 */       exception.printStackTrace();
/* 1071 */       recordSetTrans.setAutoCommit(true);
/* 1072 */       recordSetTrans.rollback();
/* 1073 */       return false;
/*      */     } 
/* 1075 */     return true;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String replace_MobileMode(String paramString, ArrayList<Pattern> paramArrayList, String[] paramArrayOfString) {
/* 1084 */     Matcher matcher = null;
/* 1085 */     for (byte b = 0; b < paramArrayList.size(); b++) {
/* 1086 */       Pattern pattern = paramArrayList.get(b);
/* 1087 */       String str = paramArrayOfString[b];
/* 1088 */       matcher = pattern.matcher(paramString);
/* 1089 */       while (matcher.find() && str != null && !"".equals(str))
/*      */       {
/* 1091 */         paramString = matcher.replaceAll(str);
/*      */       }
/*      */     } 
/* 1094 */     return paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private boolean deleteFile(File paramFile) {
/* 1106 */     if (paramFile.isDirectory()) {
/* 1107 */       String[] arrayOfString = paramFile.list();
/*      */       
/* 1109 */       for (byte b = 0; b < arrayOfString.length; b++) {
/* 1110 */         boolean bool = deleteFile(new File(paramFile, this.fileUtil.getPath(arrayOfString[b])));
/* 1111 */         if (!bool) {
/* 1112 */           return false;
/*      */         }
/*      */       } 
/*      */     } 
/*      */     
/* 1117 */     return paramFile.delete();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String replaceFile(File paramFile, ArrayList<Pattern> paramArrayList, String[] paramArrayOfString) {
/* 1127 */     String str = "";
/* 1128 */     if (paramFile.isFile()) {
/* 1129 */       String str1 = paramFile.getPath();
/* 1130 */       (new BaseBean()).writeLog("正则规则替换，开始替换文件：" + this.fileUtil.getPath(str1));
/* 1131 */       String str2 = this.fileUtil.getPath(paramFile.getPath());
/* 1132 */       if (!getMatchRuleType().contains(str2.substring(str2.lastIndexOf(".") + 1, str2.length()))) {
/* 1133 */         (new BaseBean()).writeLog("跳过该文件：" + str2 + ",该文件类型不在检测替换范围内，不做处理");
/* 1134 */         return null;
/*      */       } 
/* 1136 */       long l = (new Date()).getTime();
/*      */       
/* 1138 */       if (str1.indexOf("vssver") > -1) {
/* 1139 */         return null;
/*      */       }
/* 1141 */       String str3 = paramFile.getName();
/* 1142 */       str = str1.substring(0, str1.lastIndexOf("" + File.separatorChar));
/* 1143 */       str = str + "_cbak" + File.separatorChar;
/* 1144 */       String str4 = str + str3;
/*      */       
/* 1146 */       String str5 = "GBK";
/*      */       
/* 1148 */       OutputStreamWriter outputStreamWriter = null;
/* 1149 */       BufferedWriter bufferedWriter = null;
/* 1150 */       String str6 = null;
/* 1151 */       BufferedReader bufferedReader = null;
/* 1152 */       InputStreamReader inputStreamReader = null;
/* 1153 */       File file = null;
/*      */       try {
/* 1155 */         boolean bool = FileCharsetDetector.check(paramFile);
/* 1156 */         if (bool) {
/* 1157 */           str5 = "UTF-8";
/*      */         } else {
/* 1159 */           str5 = "GBK";
/*      */         } 
/*      */         
/* 1162 */         inputStreamReader = new InputStreamReader(new FileInputStream(paramFile), str5);
/* 1163 */         bufferedReader = new BufferedReader(inputStreamReader);
/* 1164 */         File file1 = new File(this.fileUtil.getPath(str));
/* 1165 */         if (!file1.exists()) {
/* 1166 */           file1.mkdirs();
/*      */         }
/* 1168 */         file = new File(this.fileUtil.getPath(str4));
/* 1169 */         if (!file.exists())
/*      */         {
/* 1171 */           file.createNewFile();
/*      */         }
/*      */         
/* 1174 */         file.setWritable(true);
/* 1175 */         paramFile.setWritable(true);
/*      */         
/* 1177 */         outputStreamWriter = new OutputStreamWriter(new FileOutputStream(this.fileUtil.getPath(str4)), str5);
/* 1178 */         bufferedWriter = new BufferedWriter(outputStreamWriter);
/*      */         
/* 1180 */         String str7 = "";
/* 1181 */         StringBuffer stringBuffer = new StringBuffer();
/* 1182 */         while ((str6 = bufferedReader.readLine()) != null) {
/* 1183 */           stringBuffer.append(str6).append("\r\n");
/*      */         }
/* 1185 */         Matcher matcher = null;
/* 1186 */         str7 = stringBuffer.toString();
/*      */         
/* 1188 */         for (byte b = 0; b < paramArrayList.size(); b++) {
/* 1189 */           Pattern pattern = paramArrayList.get(b);
/* 1190 */           String str8 = paramArrayOfString[b];
/*      */           
/* 1192 */           matcher = pattern.matcher(str7);
/* 1193 */           while (matcher.find() && str8 != null && !"".equals(str8))
/*      */           {
/* 1195 */             str7 = matcher.replaceAll(str8).replaceAll("ssssssss,@", "\r\n");
/*      */           }
/*      */         } 
/* 1198 */         bufferedWriter.write(str7, 0, str7.length());
/*      */       }
/* 1200 */       catch (Exception exception) {
/* 1201 */         exception.printStackTrace();
/*      */       } finally {
/* 1203 */         str6 = null;
/*      */         try {
/* 1205 */           bufferedWriter.flush();
/* 1206 */           bufferedWriter.close();
/*      */           
/* 1208 */           outputStreamWriter.close();
/*      */           
/* 1210 */           bufferedReader.close();
/* 1211 */           inputStreamReader.close();
/* 1212 */         } catch (IOException iOException) {
/* 1213 */           iOException.printStackTrace();
/*      */         } 
/*      */       } 
/*      */ 
/*      */ 
/*      */       
/* 1219 */       if (file != null && paramFile != null) {
/*      */         try {
/* 1221 */           FileUtils.copyFile(file, paramFile);
/* 1222 */         } catch (IOException iOException) {
/* 1223 */           iOException.printStackTrace();
/*      */         } 
/*      */       }
/* 1226 */       (new BaseBean()).writeLog("正则规则替换，已经替换文件：" + this.fileUtil.getPath(str1) + "结束,检测耗时：" + ((new Date()).getTime() - l) + "毫秒");
/* 1227 */     } else if (paramFile.isDirectory()) {
/* 1228 */       File[] arrayOfFile = paramFile.listFiles();
/* 1229 */       if (arrayOfFile.length > 0) {
/* 1230 */         for (byte b = 0; b < arrayOfFile.length; b++) {
/* 1231 */           replaceFile(arrayOfFile[b], paramArrayList, paramArrayOfString);
/*      */         }
/*      */       }
/*      */       
/* 1235 */       deleteFile(new File(this.fileUtil.getPath(paramFile.getPath() + "_cbak")));
/*      */     } else {
/*      */ 
/*      */       
/*      */       try {
/*      */ 
/*      */ 
/*      */ 
/*      */         
/* 1244 */         (new BaseBean()).writeLog("错误文件路径：" + paramFile.getAbsolutePath());
/* 1245 */       } catch (Exception exception) {
/* 1246 */         (new BaseBean()).writeLog("文件异常直接跳过" + exception.toString());
/*      */       } 
/*      */     } 
/* 1249 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String replaceFileWithClassRule(File paramFile, ArrayList<String> paramArrayList) {
/* 1261 */     String str = "";
/* 1262 */     if (paramFile.isFile()) {
/* 1263 */       String str1 = paramFile.getPath();
/* 1264 */       (new BaseBean()).writeLog("类规则替换，开始替换文件：" + this.fileUtil.getPath(str1));
/* 1265 */       String str2 = this.fileUtil.getPath(paramFile.getPath());
/* 1266 */       if (!getMatchRuleType().contains(str2.substring(str2.lastIndexOf(".") + 1, str2.length()))) {
/* 1267 */         (new BaseBean()).writeLog("跳过该文件：" + str2 + ",该文件类型不在检测替换范围内，不做处理");
/* 1268 */         return null;
/*      */       } 
/* 1270 */       long l = (new Date()).getTime();
/*      */       
/* 1272 */       if (str1.indexOf("vssver") > -1) {
/* 1273 */         return null;
/*      */       }
/* 1275 */       String str3 = paramFile.getName();
/* 1276 */       str = str1.substring(0, str1.lastIndexOf("" + File.separatorChar));
/* 1277 */       str = str + "_cbak" + File.separatorChar;
/* 1278 */       String str4 = str + str3;
/*      */       
/* 1280 */       String str5 = "GBK";
/*      */       
/* 1282 */       OutputStreamWriter outputStreamWriter = null;
/* 1283 */       BufferedWriter bufferedWriter = null;
/* 1284 */       String str6 = null;
/* 1285 */       BufferedReader bufferedReader = null;
/* 1286 */       InputStreamReader inputStreamReader = null;
/* 1287 */       File file = null;
/*      */       try {
/* 1289 */         str5 = FileCharsetDetector.check(paramFile) ? "UTF-8" : "GBK";
/* 1290 */         inputStreamReader = new InputStreamReader(new FileInputStream(paramFile), str5);
/* 1291 */         bufferedReader = new BufferedReader(inputStreamReader);
/* 1292 */         File file1 = new File(this.fileUtil.getPath(str));
/* 1293 */         if (!file1.exists()) {
/* 1294 */           file1.mkdirs();
/*      */         }
/* 1296 */         file = new File(this.fileUtil.getPath(str4));
/* 1297 */         if (!file.exists()) {
/* 1298 */           file.createNewFile();
/*      */         }
/*      */         
/* 1301 */         file.setWritable(true);
/* 1302 */         paramFile.setWritable(true);
/*      */         
/* 1304 */         outputStreamWriter = new OutputStreamWriter(new FileOutputStream(this.fileUtil.getPath(str4)), str5);
/* 1305 */         bufferedWriter = new BufferedWriter(outputStreamWriter);
/*      */         
/* 1307 */         StringBuffer stringBuffer = new StringBuffer();
/* 1308 */         while ((str6 = bufferedReader.readLine()) != null) {
/* 1309 */           stringBuffer.append(str6).append("\r\n");
/*      */         }
/* 1311 */         bufferedWriter.write(stringBuffer.toString(), 0, stringBuffer.toString().length());
/* 1312 */         bufferedWriter.flush();
/*      */         
/* 1314 */         for (String str7 : paramArrayList) {
/*      */           try {
/* 1316 */             Class<?> clazz = Class.forName(str7);
/* 1317 */             Method method = clazz.getMethod("replaceRule", new Class[] { String.class });
/* 1318 */             method.invoke(clazz.newInstance(), new Object[] { file.getPath() });
/* 1319 */           } catch (Exception exception) {
/* 1320 */             exception.printStackTrace();
/* 1321 */             (new BaseBean()).writeLog("请检查接口rulepath是否正确!!!");
/* 1322 */             return "";
/*      */           }
/*      */         
/*      */         } 
/* 1326 */       } catch (Exception exception) {
/* 1327 */         exception.printStackTrace();
/* 1328 */         return "";
/*      */       } finally {
/* 1330 */         str6 = null;
/*      */         try {
/* 1332 */           bufferedWriter.flush();
/* 1333 */           bufferedWriter.close();
/*      */           
/* 1335 */           outputStreamWriter.close();
/*      */           
/* 1337 */           bufferedReader.close();
/* 1338 */           inputStreamReader.close();
/* 1339 */         } catch (IOException iOException) {
/* 1340 */           iOException.printStackTrace();
/*      */         } 
/*      */       } 
/*      */       
/* 1344 */       if (file != null && paramFile != null) {
/*      */         try {
/* 1346 */           FileUtils.copyFile(file, paramFile);
/* 1347 */         } catch (IOException iOException) {
/* 1348 */           iOException.printStackTrace();
/*      */         } 
/*      */       }
/* 1351 */       (new BaseBean()).writeLog("类规则替换，已经替换文件：" + this.fileUtil.getPath(str1) + "结束,检测耗时：" + ((new Date()).getTime() - l) + "毫秒");
/* 1352 */     } else if (paramFile.isDirectory()) {
/* 1353 */       File[] arrayOfFile = paramFile.listFiles();
/* 1354 */       if (arrayOfFile.length > 0) {
/* 1355 */         for (byte b = 0; b < arrayOfFile.length; b++) {
/* 1356 */           replaceFileWithClassRule(arrayOfFile[b], paramArrayList);
/*      */         }
/*      */       }
/*      */       
/* 1360 */       deleteFile(new File(this.fileUtil.getPath(paramFile.getPath() + "_cbak")));
/*      */     } else {
/*      */       try {
/* 1363 */         (new BaseBean()).writeLog("错误文件路径：" + paramFile.getAbsolutePath());
/* 1364 */       } catch (Exception exception) {
/* 1365 */         (new BaseBean()).writeLog("错误文件异常，直接跳过");
/*      */       } 
/*      */     } 
/* 1368 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean replaceContentWithPath(String paramString1, String paramString2, String paramString3, String paramString4) {
/*      */     try {
/*      */       List<Map<String, String>> list;
/* 1382 */       RecordSet recordSet = new RecordSet();
/* 1383 */       Pattern pattern = null;
/* 1384 */       ArrayList<Pattern> arrayList = new ArrayList();
/* 1385 */       ArrayList<String> arrayList1 = new ArrayList();
/* 1386 */       CheckUtil checkUtil = new CheckUtil();
/*      */ 
/*      */       
/* 1389 */       if (paramString2 != null && !"".equals(paramString2)) {
/* 1390 */         list = checkUtil.getRuleById(paramString1, paramString2, "", "");
/*      */       } else {
/* 1392 */         list = checkUtil.getAllFileRules(paramString1, "", "");
/*      */       } 
/*      */ 
/*      */       
/* 1396 */       ArrayList<String> arrayList2 = new ArrayList();
/* 1397 */       RulePath rulePath = new RulePath();
/*      */       
/* 1399 */       arrayList2.add(paramString4);
/*      */ 
/*      */       
/* 1402 */       String[] arrayOfString = new String[list.size()]; byte b;
/* 1403 */       for (b = 0; b < list.size(); b++) {
/* 1404 */         Map map = list.get(b);
/* 1405 */         String str1 = (String)map.get("content");
/* 1406 */         String str2 = (String)map.get("replacecontent");
/*      */ 
/*      */ 
/*      */         
/* 1410 */         String str3 = (String)map.get("ruletype");
/* 1411 */         if ("1".equals(str3)) {
/* 1412 */           arrayList1.add(str1);
/*      */         } else {
/* 1414 */           pattern = Pattern.compile(changeStr2(str1));
/* 1415 */           arrayList.add(pattern);
/*      */           
/* 1417 */           arrayOfString[b] = changeStr2(str2.replaceAll("\\\\n", "ssssssss,@"));
/*      */         } 
/*      */       } 
/*      */       
/* 1421 */       for (b = 0; b < arrayList2.size(); b++) {
/*      */         
/* 1423 */         File file = new File(this.fileUtil.getPath(arrayList2.get(b)));
/* 1424 */         String str = "";
/* 1425 */         if (file.exists()) {
/* 1426 */           replaceFileWithClassRule(file, arrayList1);
/*      */         }
/* 1428 */         if (file.isFile()) {
/* 1429 */           String str1 = file.getPath();
/* 1430 */           str = str1.substring(0, str1.lastIndexOf("" + File.separatorChar));
/* 1431 */           str = str + "_cbak" + File.separatorChar;
/*      */         } else {
/* 1433 */           str = file.getPath() + "_cbak";
/*      */         } 
/*      */         
/* 1436 */         deleteFile(new File(this.fileUtil.getPath(str)));
/*      */       } 
/*      */ 
/*      */       
/* 1440 */       for (b = 0; b < arrayList2.size(); b++) {
/*      */         
/* 1442 */         File file = new File(this.fileUtil.getPath(arrayList2.get(b)));
/* 1443 */         String str = "";
/* 1444 */         if (file.exists()) {
/* 1445 */           replaceFile(file, arrayList, arrayOfString);
/*      */         }
/* 1447 */         if (file.isFile()) {
/* 1448 */           String str1 = file.getPath();
/* 1449 */           str = str1.substring(0, str1.lastIndexOf("" + File.separatorChar));
/* 1450 */           str = str + "_cbak" + File.separatorChar;
/*      */         } else {
/* 1452 */           str = file.getPath() + "_cbak";
/*      */         } 
/*      */         
/* 1455 */         deleteFile(new File(this.fileUtil.getPath(str)));
/*      */       } 
/*      */ 
/*      */       
/* 1459 */       removeMatchResult(paramString4, paramString2);
/*      */     }
/* 1461 */     catch (Exception exception) {
/* 1462 */       exception.printStackTrace();
/* 1463 */       return false;
/*      */     } 
/* 1465 */     return true;
/*      */   }
/*      */ 
/*      */   
/*      */   public void removeMatchResult(String paramString1, String paramString2) {
/* 1470 */     if (matchResult != null) {
/* 1471 */       List<String> list = null;
/* 1472 */       if (paramString2 != null) {
/* 1473 */         String[] arrayOfString = paramString2.split(",");
/* 1474 */         list = Arrays.asList(arrayOfString);
/*      */       } 
/*      */       
/* 1477 */       ArrayList arrayList = matchResult.get(paramString1);
/* 1478 */       Iterator<HashMap> iterator = arrayList.iterator();
/* 1479 */       while (iterator.hasNext()) {
/* 1480 */         HashMap hashMap = iterator.next();
/* 1481 */         String str1 = (String)hashMap.get("ruleid");
/*      */         
/* 1483 */         String str2 = (String)hashMap.get("replacecontent");
/* 1484 */         boolean bool = (str2 == null || "".equals(str2) || str2.equals("无法自动替换，请手动修改")) ? false : true;
/* 1485 */         if (list != null && list.contains(str1) && bool) {
/* 1486 */           iterator.remove();
/*      */         }
/*      */       } 
/*      */     } 
/*      */   }
/*      */   
/*      */   public ArrayList<HashMap<String, String>> getMatchResultDetail(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 1493 */     ArrayList<HashMap> arrayList1 = new ArrayList();
/* 1494 */     String str1 = paramMap.get("filepath");
/* 1495 */     String str2 = paramMap.get("type");
/* 1496 */     String str3 = paramMap.get("pageid");
/* 1497 */     String str4 = paramMap.get("name");
/* 1498 */     String str5 = paramMap.get("description");
/*      */     
/* 1500 */     ArrayList<HashMap> arrayList2 = null;
/*      */     
/*      */     try {
/* 1503 */       if ("1".equals(str2)) {
/* 1504 */         arrayList2 = (ArrayList)matchResult.get(str3);
/*      */       } else {
/* 1506 */         str1 = URLDecoder.decode(str1, "UTF-8");
/*      */         
/* 1508 */         arrayList2 = (ArrayList)matchResult.get(str1);
/*      */       } 
/*      */       
/* 1511 */       for (byte b = 0; arrayList2 != null && b < arrayList2.size(); b++) {
/* 1512 */         HashMap hashMap = arrayList2.get(b);
/* 1513 */         String str6 = (String)hashMap.get("name");
/* 1514 */         String str7 = (String)hashMap.get("desc");
/* 1515 */         if (str4 == null || "".equals(str4) || 
/* 1516 */           str6.indexOf(str4) >= 0)
/*      */         {
/*      */ 
/*      */           
/* 1520 */           if (str5 == null || "".equals(str5) || 
/* 1521 */             str7.indexOf(str5) >= 0)
/*      */           {
/*      */ 
/*      */             
/* 1525 */             arrayList1.add(hashMap); } 
/*      */         }
/*      */       } 
/* 1528 */     } catch (UnsupportedEncodingException unsupportedEncodingException) {
/* 1529 */       unsupportedEncodingException.printStackTrace();
/*      */     } 
/*      */     
/* 1532 */     return (ArrayList)arrayList1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean checkXmlFile(String paramString1, String paramString2) {
/* 1543 */     paramString2 = changeStr2(paramString2);
/* 1544 */     paramString1 = changeStr2(paramString1);
/* 1545 */     boolean bool = true;
/*      */ 
/*      */     
/* 1548 */     paramString1 = replaceNotesAndWhitespace(paramString1);
/*      */     
/*      */     try {
/* 1551 */       paramString2 = paramString2.replaceAll("\\\\r|\\\\n", "");
/* 1552 */       paramString1 = paramString1.replaceAll("\\\\r|\\\\n", "");
/* 1553 */       Element element = Str2Document("<myroot>" + paramString2 + "</myroot>").getRootElement();
/* 1554 */       List<Element> list = element.elements();
/*      */       
/* 1556 */       int i = list.size();
/* 1557 */       if (i > 0) {
/* 1558 */         for (byte b = 0; b < list.size(); b++) {
/* 1559 */           Element element1 = list.get(b);
/* 1560 */           String str = element1.asXML();
/* 1561 */           str = replaceNotesAndWhitespace(str);
/*      */ 
/*      */           
/* 1564 */           if (!paramString1.contains(str))
/*      */           {
/* 1566 */             bool = checkElementByLine(element1, paramString1);
/* 1567 */             if (!bool) {
/*      */               break;
/*      */             }
/*      */           }
/*      */         
/*      */         } 
/*      */       } else {
/*      */         
/* 1575 */         String str = element.getTextTrim();
/* 1576 */         str = replaceNotesAndWhitespace(str);
/*      */         
/* 1578 */         if (!paramString1.contains(str))
/*      */         {
/* 1580 */           bool = checkElementByLine(element, paramString1);
/* 1581 */           return bool;
/*      */         }
/*      */       
/*      */       } 
/* 1585 */     } catch (Exception exception) {
/* 1586 */       exception.printStackTrace();
/*      */     } 
/*      */ 
/*      */     
/* 1590 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String replaceNotesAndWhitespace(String paramString) {
/* 1599 */     if (paramString != null) {
/* 1600 */       paramString = paramString.replaceAll("(?s)<\\!\\-\\-.+?\\-\\->", "");
/* 1601 */       paramString = paramString.replaceAll("xmlns=\"\"", "");
/* 1602 */       paramString = paramString.replaceAll("\n|\r", "").replaceAll(">\\s+", ">").replaceAll("\\s+<", "<").replaceAll("\\s", "");
/*      */     } 
/*      */     
/* 1605 */     return paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String checkXmlFile(String paramString1, Document paramDocument, String paramString2) {
/*      */     try {
/* 1616 */       if (paramString1 != null && !"".equals(paramString1)) {
/* 1617 */         paramString1 = changeStr2(paramString1);
/*      */ 
/*      */         
/* 1620 */         List<Element> list = paramDocument.selectNodes(paramString1);
/*      */         
/* 1622 */         String str = checkXpathAndContent(paramString1, paramDocument, paramString2);
/* 1623 */         if (str != null) {
/* 1624 */           return str;
/*      */         }
/*      */ 
/*      */         
/* 1628 */         if (list.size() == 1) {
/*      */           
/* 1630 */           Element element1 = list.get(0);
/* 1631 */           Element element2 = element1.getParent();
/* 1632 */           if (element2 == null) {
/* 1633 */             element2 = paramDocument.getRootElement();
/*      */           }
/*      */           
/* 1636 */           String str1 = element2.asXML();
/*      */ 
/*      */ 
/*      */ 
/*      */           
/* 1641 */           boolean bool1 = checkXmlFile(str1, paramString2);
/* 1642 */           if (bool1)
/*      */           {
/* 1644 */             return "ok";
/*      */           }
/*      */           
/* 1647 */           return "diff";
/*      */         } 
/*      */         
/* 1650 */         if (list.size() > 1) {
/* 1651 */           return "multierror";
/*      */         }
/*      */         
/* 1654 */         boolean bool = checkXmlFile(paramDocument.asXML(), paramString2);
/* 1655 */         if (bool) {
/* 1656 */           return "ok";
/*      */         }
/* 1658 */         return "no";
/*      */       } 
/*      */ 
/*      */ 
/*      */       
/* 1663 */       return "no";
/*      */     
/*      */     }
/* 1666 */     catch (Exception exception) {
/* 1667 */       exception.printStackTrace();
/* 1668 */       return "xpatherror";
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String checkXpathAndContent(String paramString1, Document paramDocument, String paramString2) {
/* 1681 */     String str = null;
/* 1682 */     if (paramString1 == null || "".equals(paramString1)) {
/* 1683 */       return null;
/*      */     }
/* 1685 */     List<Element> list = paramDocument.selectNodes(paramString1);
/* 1686 */     if (list.size() == 1) {
/*      */       
/* 1688 */       Element element1 = list.get(0);
/* 1689 */       Element element2 = element1.getParent();
/* 1690 */       if (element2 == null) {
/* 1691 */         element2 = paramDocument.getRootElement();
/*      */       }
/*      */       
/* 1694 */       Element element3 = getElement(paramString2);
/* 1695 */       if (element3 == null) {
/* 1696 */         return "contenterror";
/*      */       }
/* 1698 */       List<Element> list1 = element3.elements();
/* 1699 */       ArrayList<String> arrayList = new ArrayList();
/* 1700 */       for (byte b1 = 0; b1 < list1.size(); b1++) {
/* 1701 */         Element element4 = list1.get(b1);
/* 1702 */         Element element5 = element4.createCopy();
/* 1703 */         element2.add(element5);
/* 1704 */         String str1 = element5.getUniquePath();
/* 1705 */         arrayList.add(str1);
/*      */       } 
/*      */ 
/*      */ 
/*      */       
/* 1710 */       List list2 = paramDocument.selectNodes(paramString1);
/*      */       
/* 1712 */       if (list2.size() == 1 || list2.size() == 0) {
/* 1713 */         str = "xpathorcontenterror";
/*      */       }
/*      */ 
/*      */       
/* 1717 */       Collections.sort(arrayList, new Comparator<String>()
/*      */           {
/*      */             public int compare(String param1String1, String param1String2) {
/* 1720 */               if (param1String1.compareTo(param1String2) > 0) {
/* 1721 */                 return -1;
/*      */               }
/* 1723 */               return 1;
/*      */             }
/*      */           });
/*      */       
/* 1727 */       for (byte b2 = 0; b2 < arrayList.size(); b2++) {
/* 1728 */         Element element = (Element)paramDocument.selectSingleNode(arrayList.get(b2));
/* 1729 */         if (element != null) {
/* 1730 */           Element element4 = element.getParent();
/* 1731 */           element4.remove(element);
/*      */         } 
/*      */       } 
/* 1734 */       return str;
/*      */     } 
/* 1736 */     if (list.size() == 0) {
/*      */       
/* 1738 */       Element element = getElement(paramString2);
/* 1739 */       List<Element> list1 = element.elements();
/* 1740 */       if (list1.size() > 0) {
/* 1741 */         String str1 = ((Element)list1.get(0)).getName();
/*      */         
/* 1743 */         String str2 = getNodeByXpath(paramString1, paramDocument, str1);
/* 1744 */         if ("".equals(str2)) {
/* 1745 */           return "xpatherror";
/*      */         }
/* 1747 */         Element element1 = (Element)paramDocument.selectSingleNode(str2);
/*      */         
/* 1749 */         if (element1 == null) {
/* 1750 */           return "xpatherror";
/*      */         }
/* 1752 */         ArrayList<String> arrayList = new ArrayList();
/*      */         
/* 1754 */         for (byte b1 = 0; b1 < list1.size(); b1++) {
/* 1755 */           Element element2 = list1.get(b1);
/* 1756 */           Element element3 = element2.createCopy();
/* 1757 */           element1.add(element3);
/* 1758 */           String str3 = element3.getUniquePath();
/* 1759 */           arrayList.add(str3);
/*      */         } 
/*      */ 
/*      */ 
/*      */         
/* 1764 */         List list2 = paramDocument.selectNodes(paramString1);
/* 1765 */         if (list2.size() == 0) {
/* 1766 */           str = "xpathorcontenterror";
/*      */         }
/*      */ 
/*      */         
/* 1770 */         Collections.sort(arrayList, new Comparator<String>()
/*      */             {
/*      */               public int compare(String param1String1, String param1String2) {
/* 1773 */                 if (param1String1.compareTo(param1String2) > 0) {
/* 1774 */                   return -1;
/*      */                 }
/* 1776 */                 return 1;
/*      */               }
/*      */             });
/*      */         
/* 1780 */         for (byte b2 = 0; b2 < arrayList.size(); b2++) {
/* 1781 */           Element element2 = (Element)paramDocument.selectSingleNode(arrayList.get(b2));
/* 1782 */           if (element2 != null) {
/* 1783 */             Element element3 = element2.getParent();
/* 1784 */             element3.remove(element2);
/*      */           } 
/*      */         } 
/* 1787 */         return str;
/*      */       } 
/*      */     } 
/* 1790 */     return null;
/*      */   }
/*      */   
/*      */   public static HashMap<String, ArrayList<HashMap<String, String>>> getMatchResult() {
/* 1794 */     return matchResult;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getNodeByXpath(String paramString1, Document paramDocument, String paramString2) {
/* 1805 */     if (paramString1 == null || "".equals(paramString1)) {
/* 1806 */       return paramString1;
/*      */     }
/* 1808 */     int i = paramString1.lastIndexOf("/");
/* 1809 */     paramString1 = paramString1.substring(0, i);
/*      */     
/*      */     try {
/* 1812 */       List<Element> list = paramDocument.selectNodes(paramString1);
/* 1813 */       if (list.size() > 0) {
/* 1814 */         String str = ((Element)list.get(0)).getName();
/*      */         
/* 1816 */         if (paramString2 != null && !"".equals(paramString2) && 
/* 1817 */           str != null) {
/* 1818 */           Object object = null;
/*      */           
/* 1820 */           if (paramString2.equals(str)) {
/* 1821 */             int j = paramString1.lastIndexOf("/");
/* 1822 */             paramString1 = paramString1.substring(0, j);
/*      */           } 
/*      */         } 
/*      */         
/* 1826 */         return paramString1;
/*      */       } 
/* 1828 */       return getNodeByXpath(paramString1, paramDocument, paramString2);
/*      */     }
/* 1830 */     catch (Exception exception) {
/*      */       
/* 1832 */       return getNodeByXpath(paramString1, paramDocument, paramString2);
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void sortByVersion(List<Map<String, String>> paramList) {
/* 1841 */     if (paramList != null)
/*      */     {
/* 1843 */       Collections.sort(paramList, new Comparator<Map<String, String>>()
/*      */           {
/*      */             public int compare(Map<String, String> param1Map1, Map<String, String> param1Map2) {
/* 1846 */               String str1 = param1Map1.get("version");
/* 1847 */               String str2 = param1Map2.get("version");
/* 1848 */               return MatchUtil.this.compareVersion(str1, str2);
/*      */             }
/*      */           });
/*      */     }
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Element getElement(String paramString) {
/* 1860 */     Element element = null;
/* 1861 */     paramString = changeStr2(paramString);
/* 1862 */     Document document = Str2Document("<myroot>" + paramString + "</myroot>");
/* 1863 */     if (document != null) {
/* 1864 */       element = document.getRootElement();
/*      */     }
/*      */     
/* 1867 */     return element;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Document Str2Document(String paramString) {
/* 1877 */     SAXReader sAXReader = new SAXReader();
/* 1878 */     sAXReader.setEntityResolver(new IgnoreDTDEntityResolver());
/* 1879 */     Document document = null;
/*      */     try {
/* 1881 */       document = sAXReader.read(new ByteArrayInputStream(paramString.getBytes("UTF-8")));
/* 1882 */     } catch (Exception exception) {
/*      */       
/* 1884 */       exception.printStackTrace();
/*      */     } 
/* 1886 */     return document;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void clearCache() {
/* 1893 */     matchResult = new HashMap<>();
/* 1894 */     result = new ArrayList<>();
/* 1895 */     clearMatchResultFromDB();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int compareVersion(String paramString1, String paramString2) {
/* 1906 */     if (paramString1 == null || "".equals(paramString1)) {
/* 1907 */       return 1;
/*      */     }
/* 1909 */     if (paramString2 == null || "".equals(paramString2)) {
/* 1910 */       return -1;
/*      */     }
/*      */     
/* 1913 */     if (paramString1.indexOf("KB") > -1 && paramString2.indexOf("KB") > -1) {
/* 1914 */       if (paramString1.compareTo(paramString2) > 0)
/* 1915 */         return -1; 
/* 1916 */       if (paramString1.compareTo(paramString2) == 0) {
/* 1917 */         return 0;
/*      */       }
/* 1919 */       return 1;
/* 1920 */     }  if (paramString1.indexOf("KB") > -1 && paramString2.indexOf("KB") < 0)
/* 1921 */       return -1; 
/* 1922 */     if (paramString1.indexOf("KB") < 0 && paramString2.indexOf("KB") > -1) {
/* 1923 */       return 1;
/*      */     }
/* 1925 */     return 0;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String changeStr(String paramString) {
/* 1935 */     if (paramString == null) {
/* 1936 */       return paramString;
/*      */     }
/* 1938 */     paramString = paramString.replaceAll("<", "&lt;");
/* 1939 */     paramString = paramString.replaceAll(">", "&gt;");
/*      */     
/* 1941 */     paramString = paramString.replaceAll("\"", "&quot;");
/* 1942 */     paramString = paramString.replaceAll(" ", "&nbsp;");
/* 1943 */     paramString = paramString.replaceAll("'", "&apos;");
/* 1944 */     return paramString;
/*      */   }
/*      */   
/*      */   public String changeStr2(String paramString) {
/* 1948 */     if (paramString == null) {
/* 1949 */       return paramString;
/*      */     }
/*      */     
/* 1952 */     paramString = paramString.replaceAll("&lt;", "<");
/* 1953 */     paramString = paramString.replaceAll("&gt;", ">");
/*      */     
/* 1955 */     paramString = paramString.replaceAll("&nbsp;", " ");
/*      */     
/* 1957 */     paramString = paramString.replaceAll("&quot;", "\"");
/* 1958 */     paramString = paramString.replaceAll("&apos;", "'");
/* 1959 */     return paramString;
/*      */   }
/*      */   
/*      */   public String changeStr3(String paramString) {
/* 1963 */     if (paramString != null) {
/* 1964 */       paramString = paramString.replaceAll("&amp;", "&");
/*      */       
/* 1966 */       paramString = paramString.replaceAll("&nbsp;", " ");
/*      */       
/* 1968 */       paramString = paramString.replaceAll("&quot;", "\"");
/* 1969 */       paramString = paramString.replaceAll("&apos;", "'");
/* 1970 */       paramString = paramString.replaceAll("&124;", "\\|");
/* 1971 */       paramString = paramString.replaceAll("&94;", "\\^");
/* 1972 */       paramString = paramString.replaceAll("\\\\n", "");
/* 1973 */       paramString = paramString.replaceAll("<", "&lt;");
/* 1974 */       paramString = paramString.replaceAll(">", "&gt;");
/*      */     } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/* 1986 */     return paramString;
/*      */   }
/*      */   private String changeStr4(String paramString) {
/* 1989 */     if (paramString != null) {
/* 1990 */       paramString = paramString.replace("<", "&lt;");
/* 1991 */       paramString = paramString.replace(">", "&gt;");
/*      */     } 
/* 1993 */     return paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean checkElementByLine(Element paramElement, String paramString) {
/* 1999 */     boolean bool = true;
/*      */     
/* 2001 */     String str = paramElement.asXML();
/* 2002 */     if (str.indexOf("init-param") > -1) {
/* 2003 */       if (str != null) {
/* 2004 */         String[] arrayOfString = str.split(">");
/* 2005 */         for (byte b = 0; b < arrayOfString.length; b++) {
/* 2006 */           String str1 = arrayOfString[b];
/* 2007 */           str1 = str1 + ">";
/* 2008 */           str1 = str1.replaceAll("xmlns=\"\"", "");
/* 2009 */           str1 = str1.replaceAll("\n|\r", "").replaceAll(">\\s+", ">").replaceAll("\\s+<", "<").replaceAll("\\s", "");
/* 2010 */           if (!paramString.contains(str1)) {
/* 2011 */             bool = false;
/*      */             break;
/*      */           } 
/*      */         } 
/*      */       } 
/*      */     } else {
/* 2017 */       bool = false;
/*      */     } 
/* 2019 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<Map<String, String>> getMatchResultFromDB() {
/* 2027 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 2028 */     RecordSet recordSet = new RecordSet();
/* 2029 */     String str = " select filepath,workflowname,nodename,detail from templetecheck_matchresult ";
/* 2030 */     recordSet.execute(str);
/* 2031 */     while (recordSet.next()) {
/* 2032 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 2033 */       hashMap.put("workflowname", recordSet.getString("workflowname"));
/* 2034 */       hashMap.put("nodename", recordSet.getString("nodename"));
/* 2035 */       hashMap.put("detail", recordSet.getString("detail"));
/* 2036 */       hashMap.put("file", recordSet.getString("filepath"));
/* 2037 */       arrayList.add(hashMap);
/*      */     } 
/* 2039 */     return (List)arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setMatchResultToDB(List<Map<String, String>> paramList) {
/* 2046 */     RecordSet recordSet = new RecordSet();
/* 2047 */     String str = "";
/* 2048 */     for (Map<String, String> map : paramList) {
/* 2049 */       if (map == null) {
/*      */         continue;
/*      */       }
/* 2052 */       String str1 = (String)map.get("detail");
/* 2053 */       if (str1 == null) {
/* 2054 */         str1 = "";
/*      */       }
/*      */       
/* 2057 */       str = "insert into templetecheck_matchresult (filepath,workflowname,nodename,detail) values('" + (String)map.get("file") + "','" + (String)map.get("workflowname") + "','" + (String)map.get("nodename") + "','" + str1.replace("'", "''") + "') ";
/* 2058 */       recordSet.execute(str);
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void clearMatchResultFromDB() {
/* 2068 */     RecordSet recordSet = new RecordSet();
/* 2069 */     String str = " delete from templetecheck_matchresult ";
/* 2070 */     recordSet.execute(str);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<String> getMatchRuleType() {
/* 2078 */     if (this.matchruletype == null || this.matchruletype.size() == 0) {
/* 2079 */       String str = Util.null2String(Prop.getPropValue("matchRuleType", "matchRuleType"));
/* 2080 */       if (str.trim().equals("")) {
/* 2081 */         this.matchruletype.add("html");
/*      */       } else {
/* 2083 */         this.matchruletype = Arrays.asList(str.split(","));
/*      */       } 
/*      */     } 
/* 2086 */     return this.matchruletype;
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/MatchUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */