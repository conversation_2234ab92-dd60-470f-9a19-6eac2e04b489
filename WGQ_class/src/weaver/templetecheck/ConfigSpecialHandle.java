/*     */ package weaver.templetecheck;
/*     */ 
/*     */ import java.io.File;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.HashSet;
/*     */ import java.util.Iterator;
/*     */ import java.util.Set;
/*     */ import org.dom4j.Document;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.TimeUtil;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ConfigSpecialHandle
/*     */   extends BaseBean
/*     */ {
/*     */   public void deleteXMLConfig_DB() {
/*  24 */     KBXMLDeletedUtil kBXMLDeletedUtil = new KBXMLDeletedUtil();
/*  25 */     ResinXmlConfigUtil resinXmlConfigUtil = new ResinXmlConfigUtil();
/*  26 */     ArrayList<HashMap<String, String>> arrayList1 = ResinXmlConfigUtil.getResinXmlConfig();
/*  27 */     ArrayList<HashMap<String, String>> arrayList2 = KBXMLDeletedUtil.getKbXMLDeletedList();
/*     */ 
/*     */     
/*  30 */     if (arrayList1 != null && arrayList1.size() > 0) {
/*  31 */       deleteXMLConfig_DB(arrayList1);
/*     */     }
/*     */     
/*  34 */     if (arrayList2 != null && arrayList2.size() > 0) {
/*  35 */       deleteXMLConfig_DB(arrayList2);
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void deleteXMLConfig_File() {
/*  44 */     KBXMLDeletedUtil kBXMLDeletedUtil = new KBXMLDeletedUtil();
/*  45 */     ResinXmlConfigUtil resinXmlConfigUtil = new ResinXmlConfigUtil();
/*     */     
/*  47 */     ArrayList<HashMap<String, String>> arrayList1 = ResinXmlConfigUtil.getResinXmlConfig();
/*  48 */     ArrayList<HashMap<String, String>> arrayList2 = KBXMLDeletedUtil.getKbXMLDeletedList();
/*     */ 
/*     */     
/*  51 */     if (arrayList1 != null && arrayList1.size() > 0) {
/*  52 */       deleteXMLConfig_File(arrayList1);
/*     */     }
/*     */     
/*  55 */     if (arrayList2 != null && arrayList2.size() > 0) {
/*  56 */       deleteXMLConfig_File(arrayList2);
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void deleteXMLConfig_File(Document paramDocument, String paramString) {
/*  64 */     KBXMLDeletedUtil kBXMLDeletedUtil = new KBXMLDeletedUtil();
/*  65 */     ResinXmlConfigUtil resinXmlConfigUtil = new ResinXmlConfigUtil();
/*     */     
/*  67 */     ArrayList<HashMap<String, String>> arrayList1 = ResinXmlConfigUtil.getResinXmlConfig();
/*  68 */     ArrayList<HashMap<String, String>> arrayList2 = KBXMLDeletedUtil.getKbXMLDeletedList();
/*     */ 
/*     */     
/*  71 */     if (arrayList1 != null && arrayList1.size() > 0) {
/*  72 */       deleteXMLConfig_File(arrayList1, paramDocument, paramString);
/*     */     }
/*     */     
/*  75 */     if (arrayList2 != null && arrayList2.size() > 0) {
/*  76 */       deleteXMLConfig_File(arrayList2, paramDocument, paramString);
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void deleteXMLConfig() {
/*  87 */     KBXMLDeletedUtil kBXMLDeletedUtil = new KBXMLDeletedUtil();
/*  88 */     ResinXmlConfigUtil resinXmlConfigUtil = new ResinXmlConfigUtil();
/*  89 */     ArrayList<HashMap<String, String>> arrayList1 = ResinXmlConfigUtil.getResinXmlConfig();
/*  90 */     ArrayList<HashMap<String, String>> arrayList2 = KBXMLDeletedUtil.getKbXMLDeletedList();
/*     */ 
/*     */     
/*  93 */     if (arrayList1 != null && arrayList1.size() > 0) {
/*  94 */       deleteXMLConfig_DB(arrayList1);
/*  95 */       deleteXMLConfig_File(arrayList1);
/*     */     } 
/*  97 */     if (arrayList2 != null && arrayList2.size() > 0) {
/*  98 */       deleteXMLConfig_DB(arrayList2);
/*  99 */       deleteXMLConfig_File(arrayList2);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void deleteXMLConfig_DB(ArrayList<HashMap<String, String>> paramArrayList) {
/* 108 */     RecordSet recordSet = new RecordSet();
/* 109 */     BaseBean baseBean = new BaseBean();
/* 110 */     if (paramArrayList != null) {
/* 111 */       CheckUtil checkUtil = new CheckUtil();
/* 112 */       String str = "";
/* 113 */       for (byte b = 0; b < paramArrayList.size(); b++) {
/* 114 */         HashMap hashMap = paramArrayList.get(b);
/* 115 */         String str1 = (String)hashMap.get("filepath");
/* 116 */         String str2 = (String)hashMap.get("key");
/* 117 */         String str3 = (String)hashMap.get("xpath");
/* 118 */         str3 = str3.replaceAll("'", "''");
/*     */         
/* 120 */         recordSet.executeQuery("select * from configxmlfile t1 left join configfilemanager t2 on t2.id=t1.configfileid where t1.isdelete='0' and t1.xpath = '" + str3 + "' and (t2.filepath  = '" + str1.replace("/", "\\") + "'  or  t2.filepath  = '" + str1.replace("\\", "/") + "')", new Object[0]);
/* 121 */         HashSet<String> hashSet = new HashSet();
/*     */         
/* 123 */         while (recordSet.next()) {
/* 124 */           String str4 = recordSet.getString("id");
/* 125 */           String str5 = recordSet.getString("configfileid");
/* 126 */           hashSet.add(str5);
/*     */           
/* 128 */           recordSet.executeUpdate("update configxmlfile set isdelete='1' where id = ?", new Object[] { str4 });
/* 129 */           baseBean.writeLog("ConfigSpecialHandle...delete from configxmlfile where id =" + str4);
/* 130 */           if (str.indexOf(str2) < 0) {
/* 131 */             str = str + str2 + ",";
/*     */           }
/*     */         } 
/*     */ 
/*     */ 
/*     */         
/* 137 */         Iterator<String> iterator = hashSet.iterator();
/* 138 */         while (iterator.hasNext()) {
/* 139 */           String str4 = iterator.next();
/* 140 */           recordSet.executeQuery("select count(*) as count from configxmlfile where isdelete='0' and configfileid = ?", new Object[] { str4 });
/* 141 */           recordSet.next();
/* 142 */           int i = recordSet.getInt("count");
/*     */           
/* 144 */           if (i == 0) {
/* 145 */             recordSet.executeUpdate("update configfilemanager set isdelete='1' where id = ?", new Object[] { str4 });
/* 146 */             baseBean.writeLog("ConfigSpecialHandle...delete from configfilemanager where id = " + str4);
/*     */           } 
/*     */         } 
/*     */       } 
/*     */ 
/*     */ 
/*     */       
/* 153 */       if (str != null && !"".equals(str)) {
/* 154 */         checkUtil.deleteRule("," + str, "checkwebxml");
/* 155 */         baseBean.writeLog("ConfigSpecialHandle...delete from rule.xml  " + str);
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void deleteXMLConfig_File(ArrayList<HashMap<String, String>> paramArrayList) {
/* 165 */     if (paramArrayList != null) {
/* 166 */       selectXmlNodeUtil selectXmlNodeUtil = new selectXmlNodeUtil();
/* 167 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*     */       
/* 169 */       for (byte b = 0; b < paramArrayList.size(); b++) {
/* 170 */         HashMap hashMap1 = paramArrayList.get(b);
/* 171 */         String str1 = (String)hashMap1.get("filepath");
/* 172 */         String str2 = GCONST.getRootPath() + str1;
/* 173 */         Document document = null;
/* 174 */         if (hashMap.get(str1) == null) {
/* 175 */           ReadXml readXml = new ReadXml();
/* 176 */           File file = new File(str2);
/* 177 */           if (file.exists()) {
/* 178 */             document = readXml.read(str2);
/*     */           }
/*     */         } else {
/*     */           
/* 182 */           document = (Document)hashMap.get(str1);
/*     */         } 
/*     */         
/* 185 */         if (document != null) {
/* 186 */           String str3 = (String)hashMap1.get("xpath");
/* 187 */           String str4 = (String)hashMap1.get("content");
/* 188 */           if (str3 == null || "".equals(str3)) {
/* 189 */             selectXmlNodeUtil.deleteConfigByContent(document, str4);
/*     */           } else {
/* 191 */             selectXmlNodeUtil.deleteConfig(document, str4, str2, str3);
/*     */             
/* 193 */             selectXmlNodeUtil.deleteConfigByContent(document, str4);
/*     */           } 
/* 195 */           writeLog("deleteXMLConfig_File by list--file " + str2 + ",xpath:" + str3);
/* 196 */           hashMap.put(str1, document);
/*     */         } else {
/* 198 */           writeLog("document is null");
/*     */         } 
/*     */       } 
/*     */ 
/*     */       
/* 203 */       Set set = hashMap.keySet();
/* 204 */       Iterator<String> iterator = set.iterator();
/* 205 */       while (iterator.hasNext()) {
/* 206 */         String str1 = iterator.next();
/* 207 */         Document document = (Document)hashMap.get(str1);
/* 208 */         String str2 = GCONST.getRootPath() + str1;
/* 209 */         File file = new File(str2);
/* 210 */         if (file.exists()) {
/* 211 */           selectXmlNodeUtil.writeXml(str2, document);
/*     */         }
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void deleteXMLConfig_File(ArrayList<HashMap<String, String>> paramArrayList, Document paramDocument, String paramString) {
/* 222 */     if (paramArrayList != null) {
/* 223 */       selectXmlNodeUtil selectXmlNodeUtil = new selectXmlNodeUtil();
/*     */       
/* 225 */       for (byte b = 0; b < paramArrayList.size(); b++) {
/* 226 */         HashMap hashMap = paramArrayList.get(b);
/* 227 */         String str1 = (String)hashMap.get("filepath");
/* 228 */         paramString = paramString.replace("\\", "/");
/* 229 */         str1 = str1.replace("\\", "/");
/* 230 */         if (!paramString.equals(str1)) {
/*     */           continue;
/*     */         }
/* 233 */         String str2 = GCONST.getRootPath() + str1;
/*     */         
/* 235 */         if (paramDocument != null) {
/* 236 */           String str3 = (String)hashMap.get("xpath");
/* 237 */           String str4 = (String)hashMap.get("content");
/*     */ 
/*     */ 
/*     */           
/* 241 */           if (str3.indexOf("CreateBarCode") > -1) {
/* 242 */             String str = "<servlet-class>weaver.cpt.barcode.BarCodeServlet</servlet-class>";
/* 243 */             if (!selectXmlNodeUtil.checkXmlFile(paramDocument.asXML(), str)) {
/*     */               continue;
/*     */             }
/*     */           } 
/*     */ 
/*     */           
/* 249 */           if (str3 == null || "".equals(str3)) {
/* 250 */             selectXmlNodeUtil.deleteConfigByContent(paramDocument, str4);
/*     */           } else {
/* 252 */             selectXmlNodeUtil.deleteConfig(paramDocument, str4, str2, str3);
/*     */             
/* 254 */             selectXmlNodeUtil.deleteConfigByContent(paramDocument, str4);
/*     */           } 
/* 256 */           writeLog("deleteXMLConfig_File with path-delete file " + str2 + ",xpath:" + str3); continue;
/*     */         } 
/* 258 */         writeLog("document is null");
/*     */         continue;
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void adjustConfig() {
/* 283 */     RecordSet recordSet = new RecordSet();
/* 284 */     recordSet.executeUpdate("update configXmlFile set isdelete='1' where attrvalue like '%jsp-config%'", new Object[0]);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 290 */     recordSet.executeQuery("select * from configXmlFile where attrvalue like '%/page/interfaces/*.jsp%'", new Object[0]);
/* 291 */     while (recordSet.next()) {
/* 292 */       String str1 = recordSet.getString("id");
/* 293 */       String str2 = recordSet.getString("attrvalue");
/* 294 */       if (str2 != null) {
/* 295 */         str2 = str2.replace("/page/interfaces/*.jsp", "/page/interfaces/*");
/*     */       }
/*     */       
/* 298 */       recordSet.executeUpdate("update configXmlFile set attrvalue=? where id=?", new Object[] { str2, str1 });
/*     */     } 
/*     */ 
/*     */     
/* 302 */     recordSet.executeQuery("select * from configXmlFile  where attrvalue like '%display-name%'", new Object[0]);
/* 303 */     while (recordSet.next()) {
/* 304 */       String str1 = recordSet.getString("id");
/* 305 */       String str2 = recordSet.getString("attrvalue");
/* 306 */       if (str2 != null) {
/* 307 */         str2 = str2.replaceAll("<display-name>[\\s\\S]*?</display-name>", "");
/*     */       }
/* 309 */       recordSet.executeUpdate("update configXmlFile set attrvalue=? where id=?", new Object[] { str2, str1 });
/*     */     } 
/*     */     
/* 312 */     recordSet.executeQuery("SELECT * FROM configFileManager  where filepath like '%classbean%'", new Object[0]);
/* 313 */     while (recordSet.next()) {
/* 314 */       String str1 = recordSet.getString("id");
/* 315 */       String str2 = recordSet.getString("filepath");
/* 316 */       if (str2 != null) {
/* 317 */         str2 = str2.replaceAll("classbean", "WEB-INF/classes");
/*     */       }
/* 319 */       recordSet.executeUpdate("update configFileManager set filepath=? where id=?", new Object[] { str2, str1 });
/*     */     } 
/*     */ 
/*     */     
/* 323 */     addFileCheckFileter();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void addFileCheckFileter() {
/* 330 */     RecordSet recordSet = new RecordSet();
/* 331 */     String str1 = TimeUtil.getCurrentDateString();
/* 332 */     String str2 = TimeUtil.getOnlyCurrentTimeString();
/* 333 */     String str3 = "FileCheckFilter";
/* 334 */     recordSet.executeQuery("select 1 from configFileManager where fileinfo = ? ", new Object[] { str3 });
/* 335 */     if (!recordSet.next())
/*     */     {
/* 337 */       recordSet.executeUpdate("insert into configFileManager(labelid,filetype,configtype,filename,filepath,qcnumber,fileinfo,kbversion,isdelete,createdate,createtime) values(?,?,?,?,?,?,?,?,?,?,?)", new Object[] { "0", "2", "1", "web.xml", "/WEB-INF/web.xml", "0", str3, "9.00.2003.00", "0", str1, str2 });
/*     */     }
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 343 */     String str4 = "";
/* 344 */     String str5 = "<filter>\n<filter-name>FileCheckFilter</filter-name>\n<filter-class>wscheck.FileCheckFilter</filter-class>\n</filter>\n<filter-mapping>\n<filter-name>FileCheckFilter</filter-name>\n<url-pattern>*.jsp</url-pattern>\n</filter-mapping>\n<filter-mapping>\n<filter-name>FileCheckFilter</filter-name>\n<url-pattern>*.html</url-pattern>\n</filter-mapping>\n<filter-mapping>\n<filter-name>FileCheckFilter</filter-name>\n<url-pattern>/api/*</url-pattern>\n</filter-mapping>";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 361 */     String str6 = "/web-app/filter/filter-name[text()='FileCheckFilter']/parent::*";
/* 362 */     recordSet.executeQuery("select id from configFileManager where fileinfo = ?", new Object[] { str3 });
/* 363 */     if (recordSet.next()) {
/* 364 */       str4 = recordSet.getString("id");
/*     */     }
/*     */     
/* 367 */     recordSet.executeQuery("select 1 from configXmlFile where attrnotes = ? ", new Object[] { str3 });
/* 368 */     if (!recordSet.next())
/*     */     {
/* 370 */       recordSet.executeUpdate("insert into configXmlFile(configfileid,xmldetailid,attrvalue,xpath,attrnotes,createdate,createtime,issystem,requisite,isdelete) values(?,?,?,?,?,?,?,?,?,?)", new Object[] { str4, "0", str5, str6, str3, str1, str2, "0", "1", "0" });
/*     */     }
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/ConfigSpecialHandle.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */