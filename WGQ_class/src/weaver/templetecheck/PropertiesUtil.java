/*     */ package weaver.templetecheck;
/*     */ 
/*     */ import java.io.BufferedReader;
/*     */ import java.io.BufferedWriter;
/*     */ import java.io.File;
/*     */ import java.io.FileInputStream;
/*     */ import java.io.FileOutputStream;
/*     */ import java.io.IOException;
/*     */ import java.io.InputStreamReader;
/*     */ import java.io.OutputStreamWriter;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.regex.Pattern;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.upgradetool.UpgradeFileUtil;
/*     */ 
/*     */ 
/*     */ public class PropertiesUtil
/*     */   extends BaseBean
/*     */ {
/*  27 */   private List<String> keys = new ArrayList<>();
/*     */ 
/*     */   
/*  30 */   private Map<String, String> valueMap = new HashMap<>();
/*     */   
/*  32 */   private Map<String, String> notesMap = new HashMap<>();
/*     */   
/*  34 */   public String filepath = "";
/*     */   
/*  36 */   UpgradeFileUtil fileUtil = new UpgradeFileUtil();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getPropertyVal(String paramString) {
/*  49 */     return Util.null2String(this.valueMap.get(paramString));
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public String getPropertyNotes(String paramString) {
/*  55 */     return Util.null2String(this.notesMap.get(paramString));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void put(String paramString1, String paramString2) {
/*  65 */     if (this.keys.contains(paramString1))
/*     */     {
/*  67 */       this.keys.remove(paramString1);
/*     */     }
/*  69 */     this.keys.add(paramString1);
/*  70 */     this.valueMap.put(paramString1, paramString2);
/*     */   }
/*     */ 
/*     */   
/*     */   public void put(String paramString1, String paramString2, String paramString3) {
/*  75 */     if (!this.keys.contains(paramString1))
/*     */     {
/*  77 */       this.keys.add(paramString1);
/*     */     }
/*  79 */     this.valueMap.put(paramString1, paramString2);
/*     */ 
/*     */     
/*  82 */     if (paramString3.indexOf("\n") > -1) {
/*  83 */       paramString3 = paramString3.replaceAll("\n", "\r\n");
/*     */       
/*  85 */       this.notesMap.put(paramString1, paramString3);
/*     */     } else {
/*  87 */       this.notesMap.put(paramString1, paramString3);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean containsKey(String paramString) {
/*  98 */     return this.keys.contains(paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String get(String paramString) {
/* 107 */     return Util.null2String(this.valueMap.get(paramString));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void remove(String paramString) {
/* 116 */     this.keys.remove(paramString);
/* 117 */     this.valueMap.remove(paramString);
/* 118 */     this.notesMap.remove(paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<String> getKeys(String paramString) {
/* 126 */     Pattern pattern = Pattern.compile(paramString);
/* 127 */     ArrayList<String> arrayList = new ArrayList();
/* 128 */     for (String str : this.keys) {
/* 129 */       if (pattern.matcher(str).matches()) {
/* 130 */         arrayList.add(str);
/*     */       }
/*     */     } 
/* 133 */     return arrayList;
/*     */   }
/*     */ 
/*     */   
/*     */   public List<String> getKeys() {
/* 138 */     return this.keys;
/*     */   }
/*     */ 
/*     */   
/*     */   public String toString() {
/* 143 */     return this.valueMap.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public synchronized void load(String paramString) {
/* 150 */     FileInputStream fileInputStream = null;
/* 151 */     InputStreamReader inputStreamReader = null;
/* 152 */     this.filepath = paramString;
/*     */     try {
/* 154 */       fileInputStream = new FileInputStream(this.fileUtil.getPath(paramString));
/*     */       
/* 156 */       inputStreamReader = new InputStreamReader(fileInputStream, "GBK");
/* 157 */       BufferedReader bufferedReader = new BufferedReader(inputStreamReader);
/* 158 */       ArrayList<String> arrayList = new ArrayList();
/*     */       String str;
/* 160 */       while ((str = bufferedReader.readLine()) != null) {
/* 161 */         arrayList.add(str);
/*     */       }
/* 163 */       bufferedReader.close();
/* 164 */       inputStreamReader.close();
/*     */ 
/*     */       
/* 167 */       StringBuffer stringBuffer = new StringBuffer();
/* 168 */       for (String str1 : arrayList) {
/* 169 */         if (str1 == null || "".equals(str1.trim())) {
/*     */           continue;
/*     */         }
/* 172 */         if (str1.trim().startsWith("#") || str1.trim().startsWith("<!--")) {
/* 173 */           stringBuffer.append(str1).append("\r\n"); continue;
/*     */         } 
/* 175 */         if (str1.indexOf("=") > -1 && !str1.trim().startsWith("{")) {
/* 176 */           String str4 = str1.substring(0, str1.indexOf("=")).trim();
/* 177 */           String str5 = str1.substring(str1.indexOf("=") + 1).trim();
/* 178 */           String str6 = str5;
/* 179 */           String str7 = "";
/*     */           
/* 181 */           if (str5.indexOf("#") > -1) {
/* 182 */             str6 = str5.substring(0, str1.indexOf("#"));
/* 183 */             str7 = str5.substring(str1.indexOf("#") + 1);
/* 184 */             stringBuffer.append(str7).append("\r\n");
/*     */           } 
/* 186 */           this.keys.add(str4);
/* 187 */           this.valueMap.put(str4, str6);
/* 188 */           if (stringBuffer.length() > 0) {
/* 189 */             this.notesMap.put(str4, stringBuffer.toString());
/* 190 */             stringBuffer = new StringBuffer();
/*     */           } 
/*     */           continue;
/*     */         } 
/* 194 */         if (this.keys.size() == 0) {
/* 195 */           this.keys.add(str1); continue;
/*     */         } 
/* 197 */         int i = this.keys.size() - 1;
/* 198 */         String str2 = this.keys.get(i);
/* 199 */         String str3 = this.valueMap.get(str2);
/* 200 */         StringBuffer stringBuffer1 = new StringBuffer(str3);
/* 201 */         stringBuffer1.append("\r\n").append(str1);
/* 202 */         this.valueMap.put(str2, stringBuffer1.toString());
/*     */ 
/*     */       
/*     */       }
/*     */ 
/*     */     
/*     */     }
/* 209 */     catch (Exception exception) {
/* 210 */       writeLog("文件加载失败：" + paramString);
/*     */     } finally {
/* 212 */       if (fileInputStream != null) {
/*     */         try {
/* 214 */           fileInputStream.close();
/* 215 */         } catch (IOException iOException) {
/* 216 */           iOException.printStackTrace();
/*     */         } 
/*     */       }
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public synchronized boolean store(String paramString) {
/* 228 */     BufferedWriter bufferedWriter = null;
/* 229 */     boolean bool = false;
/*     */     try {
/* 231 */       List<String> list = this.keys;
/* 232 */       File file = new File(this.fileUtil.getPath(paramString));
/*     */       
/* 234 */       if (!file.exists()) {
/* 235 */         return false;
/*     */       }
/*     */ 
/*     */       
/* 239 */       if (!file.canWrite()) {
/* 240 */         file.setWritable(true);
/*     */       }
/* 242 */       bufferedWriter = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(file), "GBK"));
/* 243 */       for (byte b = 0; b < list.size(); b++) {
/*     */         
/* 245 */         String str1 = Util.null2String(list.get(b));
/* 246 */         String str2 = str1;
/* 247 */         if (!"".equals(str1)) {
/* 248 */           str2 = str2 + "=" + Util.null2String(this.valueMap.get(str1));
/*     */         }
/*     */         
/* 251 */         if (this.notesMap.get(str1) != null) {
/* 252 */           bufferedWriter.write(this.notesMap.get(str1));
/*     */           
/* 254 */           bufferedWriter.newLine();
/*     */         } 
/*     */         
/* 257 */         bufferedWriter.write(str2);
/* 258 */         bufferedWriter.newLine();
/*     */       } 
/* 260 */       bufferedWriter.flush();
/* 261 */       bool = true;
/* 262 */     } catch (Exception exception) {
/* 263 */       bool = false;
/* 264 */       exception.printStackTrace();
/* 265 */       writeLog("文件保存失败：" + paramString);
/*     */     } finally {
/* 267 */       if (bufferedWriter != null) {
/*     */         try {
/* 269 */           bufferedWriter.close();
/* 270 */         } catch (IOException iOException) {}
/*     */       }
/*     */     } 
/*     */ 
/*     */     
/* 275 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public synchronized String saveFile(String paramString1, String paramString2) {
/* 282 */     File file = new File(this.fileUtil.getPath(paramString1));
/*     */     
/*     */     try {
/* 285 */       paramString1 = file.getPath();
/*     */       
/* 287 */       String str = paramString1.substring(0, paramString1.lastIndexOf(File.separatorChar));
/* 288 */       File file1 = new File(this.fileUtil.getPath(str));
/* 289 */       if (!file1.exists()) {
/* 290 */         file1.mkdirs();
/*     */       }
/*     */       
/* 293 */       if (!file.exists()) {
/* 294 */         file.createNewFile();
/*     */       } else {
/* 296 */         return "error";
/*     */       } 
/*     */ 
/*     */       
/* 300 */       String[] arrayOfString = paramString2.split("\n");
/*     */       
/* 302 */       StringBuffer stringBuffer = new StringBuffer();
/*     */       
/* 304 */       for (byte b = 0; b < arrayOfString.length; b++) {
/* 305 */         String str1 = arrayOfString[b];
/* 306 */         if (str1 != null && !"".equals(str1.trim()))
/*     */         {
/*     */           
/* 309 */           if (str1.trim().startsWith("#") || str1.trim().startsWith("<!--")) {
/* 310 */             stringBuffer.append(str1).append("\r\n");
/*     */           }
/* 312 */           else if (str1.indexOf("=") > -1) {
/* 313 */             String str2 = str1.substring(0, str1.indexOf("=")).trim();
/* 314 */             String str3 = str1.substring(str1.indexOf("=") + 1).trim();
/* 315 */             String str4 = str3;
/* 316 */             String str5 = "";
/*     */             
/* 318 */             if (str3.indexOf("#") > -1) {
/* 319 */               str4 = str3.substring(0, str1.indexOf("#"));
/* 320 */               str5 = str3.substring(str1.indexOf("#") + 1);
/* 321 */               stringBuffer.append(str5).append("\r\n");
/*     */             } 
/* 323 */             this.keys.add(str2);
/* 324 */             this.valueMap.put(str2, str4);
/* 325 */             if (stringBuffer.length() > 0) {
/* 326 */               this.notesMap.put(str2, stringBuffer.toString());
/* 327 */               stringBuffer = new StringBuffer();
/*     */             } 
/*     */           } else {
/*     */             
/* 331 */             this.keys.add(str1);
/*     */           } 
/*     */         }
/*     */       } 
/*     */ 
/*     */       
/* 337 */       store(paramString1);
/* 338 */       return "ok";
/* 339 */     } catch (IOException iOException) {
/*     */       
/* 341 */       iOException.printStackTrace();
/* 342 */       return "error";
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<Map<String, String>> getPropList(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 354 */     ArrayList<Map<String, String>> arrayList = new ArrayList();
/* 355 */     String str1 = paramMap.get("fpath");
/* 356 */     String str2 = paramMap.get("excludestatus");
/*     */     
/* 358 */     if ("false".equals(str2)) {
/* 359 */       return arrayList;
/*     */     }
/* 361 */     str1 = str1.replace("#", ":");
/*     */ 
/*     */     
/* 364 */     if (str1 != null) {
/* 365 */       load(str1);
/*     */     }
/* 367 */     for (byte b = 0; b < this.keys.size(); b++) {
/* 368 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 369 */       String str3 = this.keys.get(b);
/* 370 */       String str4 = this.valueMap.get(str3);
/* 371 */       String str5 = this.notesMap.get(str3);
/* 372 */       hashMap.put("attrname", str3);
/* 373 */       if (str4 != null) {
/* 374 */         hashMap.put("attrvalue", str4);
/*     */       }
/* 376 */       if (str5 != null) {
/* 377 */         hashMap.put("attrnotes", str5);
/*     */       }
/* 379 */       arrayList.add(hashMap);
/*     */     } 
/* 381 */     return arrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/PropertiesUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */