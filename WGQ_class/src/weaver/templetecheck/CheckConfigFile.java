/*     */ package weaver.templetecheck;
/*     */ 
/*     */ import java.io.ByteArrayInputStream;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Collections;
/*     */ import java.util.Comparator;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import org.dom4j.Document;
/*     */ import org.dom4j.DocumentException;
/*     */ import org.dom4j.Element;
/*     */ import org.dom4j.io.SAXReader;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ public class CheckConfigFile
/*     */ {
/*  25 */   ReadXml readXml = new ReadXml();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<Map<String, String>> getMatchResult(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  37 */     String str1 = paramMap.get("filetype");
/*  38 */     String str2 = Util.null2String(paramMap.get("status")).equals("") ? "0" : Util.null2String(paramMap.get("status"));
/*  39 */     String str3 = Util.null2String(paramMap.get("ids"));
/*  40 */     String str4 = Util.null2String(paramMap.get("filename"));
/*  41 */     String str5 = Util.null2String(paramMap.get("attrname"));
/*  42 */     String str6 = Util.null2String(paramMap.get("attrvalue"));
/*     */     
/*  44 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  45 */     hashMap.put("checktype", str1);
/*  46 */     hashMap.put("status", str2);
/*  47 */     hashMap.put("ids", str3);
/*  48 */     hashMap.put("filename", str4);
/*  49 */     hashMap.put("attrname", str5);
/*  50 */     hashMap.put("attrvalue", str6);
/*  51 */     ArrayList<ConfigDetail> arrayList = getConfigDetailList((Map)hashMap);
/*  52 */     ArrayList<Map<String, String>> arrayList1 = new ArrayList();
/*  53 */     if (arrayList.size() <= 0) {
/*  54 */       return arrayList1;
/*     */     }
/*  56 */     String str7 = "";
/*  57 */     PropertiesUtil propertiesUtil = new PropertiesUtil();
/*  58 */     String str8 = getCurrentUsedFileIds();
/*  59 */     for (byte b = 0; b < arrayList.size(); b++) {
/*  60 */       ConfigDetail configDetail = arrayList.get(b);
/*  61 */       if (configDetail != null) {
/*  62 */         String str9 = configDetail.getType();
/*  63 */         String str10 = configDetail.getFilepath();
/*  64 */         String str11 = configDetail.getRequisite();
/*  65 */         String str12 = String.valueOf(configDetail.getDetailid());
/*  66 */         boolean bool = str7.equals(str10);
/*  67 */         HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  68 */         if ("1".equals(str9)) {
/*  69 */           String str13 = configDetail.getKeyname();
/*  70 */           String str14 = configDetail.getValue();
/*  71 */           if (!bool) {
/*  72 */             propertiesUtil = new PropertiesUtil();
/*  73 */             propertiesUtil.load(GCONST.getRootPath() + str10);
/*  74 */             str7 = str10;
/*     */           } 
/*  76 */           String str15 = checkPropertiesFile(propertiesUtil, str10, str13, str14, str12, str8);
/*  77 */           String str16 = getLocalPropertiesValue(propertiesUtil, str10, str13, str14);
/*  78 */           hashMap1.put("detailid", str12);
/*  79 */           hashMap1.put("filetype", "Properties");
/*  80 */           hashMap1.put("filepath", str10);
/*  81 */           hashMap1.put("attrname", str13);
/*  82 */           hashMap1.put("attrvalue", str14);
/*  83 */           hashMap1.put("localvalue", changeStr(str16));
/*  84 */           hashMap1.put("status", getPropStatusName(str15).get("status"));
/*  85 */           hashMap1.put("statusname", getPropStatusName(str15).get("statusName"));
/*  86 */           hashMap1.put("requisite", str11.equals("1") ? (SystemEnv.getHtmlLabelName(163, ThreadVarLanguage.getLang()) + "") : (SystemEnv.getHtmlLabelName(25105, ThreadVarLanguage.getLang()) + ""));
/*     */         } else {
/*     */           
/*  89 */           String str13 = configDetail.getValue();
/*  90 */           String str14 = configDetail.getXpath();
/*  91 */           Document document = null;
/*     */           try {
/*  93 */             document = this.readXml.read(GCONST.getRootPath() + str10);
/*  94 */           } catch (Exception exception) {
/*  95 */             exception.printStackTrace();
/*     */           } 
/*  97 */           String str15 = configDetail.getValue();
/*     */           
/*  99 */           String str16 = checkXmlFile(str14, document, str15, str12, str8);
/* 100 */           hashMap1.put("detailid", "" + str12);
/* 101 */           hashMap1.put("filetype", "XML");
/* 102 */           hashMap1.put("filepath", str10);
/* 103 */           hashMap1.put("attrname", "-");
/* 104 */           hashMap1.put("attrvalue", changeStr(str13));
/* 105 */           hashMap1.put("status", getXmlStatusName(str16).get("status"));
/* 106 */           hashMap1.put("statusname", getXmlStatusName(str16).get("statusName"));
/* 107 */           hashMap1.put("requisite", str11.equals("1") ? (SystemEnv.getHtmlLabelName(163, ThreadVarLanguage.getLang()) + "") : (SystemEnv.getHtmlLabelName(25105, ThreadVarLanguage.getLang()) + ""));
/*     */         } 
/*     */ 
/*     */         
/* 111 */         if (((String)hashMap1.get("status")).equals(str2) || str2.equals("0")) {
/* 112 */           arrayList1.add(hashMap1);
/*     */         }
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 132 */     return arrayList1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getLocalPropertiesValue(PropertiesUtil paramPropertiesUtil, String paramString1, String paramString2, String paramString3) {
/* 145 */     String str = "";
/* 146 */     if (paramString2 != null) {
/* 147 */       str = Util.null2String(paramPropertiesUtil.getPropertyVal(paramString2));
/*     */     }
/* 149 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean checkXmlFile(String paramString1, String paramString2) {
/* 161 */     boolean bool = true;
/*     */ 
/*     */     
/* 164 */     paramString1 = replaceNotesAndWhitespace(paramString1);
/*     */     
/*     */     try {
/* 167 */       paramString2 = paramString2.replaceAll("\\\\r|\\\\n", "");
/* 168 */       paramString1 = paramString1.replaceAll("\\\\r|\\\\n", "");
/* 169 */       Element element = Str2Document("<myroot>" + paramString2 + "</myroot>").getRootElement();
/* 170 */       List<Element> list = element.elements();
/*     */       
/* 172 */       int i = list.size();
/* 173 */       if (i > 0) {
/* 174 */         for (byte b = 0; b < list.size(); b++) {
/* 175 */           Element element1 = list.get(b);
/* 176 */           String str = element1.asXML();
/* 177 */           str = replaceNotesAndWhitespace(str);
/*     */ 
/*     */           
/* 180 */           if (!paramString1.contains(str)) {
/* 181 */             bool = false;
/* 182 */             return bool;
/*     */           } 
/*     */         } 
/*     */       } else {
/* 186 */         String str = element.getTextTrim();
/* 187 */         str = replaceNotesAndWhitespace(str);
/*     */         
/* 189 */         if (!paramString1.contains(str)) {
/* 190 */           bool = false;
/* 191 */           return bool;
/*     */         }
/*     */       
/*     */       } 
/* 195 */     } catch (Exception exception) {
/* 196 */       exception.printStackTrace();
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 201 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String replaceNotesAndWhitespace(String paramString) {
/* 211 */     if (paramString != null) {
/* 212 */       paramString = paramString.replaceAll("(?s)<\\!\\-\\-.+?\\-\\->", "");
/* 213 */       paramString = paramString.replaceAll("xmlns=\"\"", "");
/* 214 */       paramString = paramString.replaceAll("\n|\r", "").replaceAll(">\\s+", ">").replaceAll("\\s+<", "<").replaceAll("\\s", "");
/*     */     } 
/*     */     
/* 217 */     return paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String checkPropertiesFile(PropertiesUtil paramPropertiesUtil, String paramString1, String paramString2, String paramString3, String paramString4, String paramString5) {
/* 231 */     if (paramString2 == null || paramString2.trim().equals("") || paramString3 == null || paramString3.trim().equals("")) {
/* 232 */       return "keyorcontenterror";
/*     */     }
/* 234 */     if (paramString4 != null && !paramString4.equals("")) {
/* 235 */       RecordSet recordSet1 = new RecordSet();
/* 236 */       paramString2 = paramString2.trim().replaceAll("'+", "'").replaceAll("'", "''");
/*     */ 
/*     */       
/* 239 */       String str1 = " where (a.isdelete = '0' or a.isdelete is null) and b.isdelete=0 and a.attrname='" + paramString2 + "' and b.filepath = (select filepath from configFileManager where isdelete=0 and id = (select configfileid from configPropertiesFile where id =" + paramString4 + " )) " + (paramString5.equals("") ? "" : (" and b.id in(" + paramString5 + ")"));
/*     */ 
/*     */       
/* 242 */       String str2 = "select a.id,b.kbversion,c.sysversion from configPropertiesFile a left join configFileManager b on a.configfileid = b.id LEFT JOIN CustomerKBVersion c ON c.name = b.kbversion " + str1 + " order by c.sysversion desc,b.kbversion desc,a.id desc";
/*     */       
/* 244 */       recordSet1.execute(str2);
/* 245 */       if (recordSet1.next()) {
/* 246 */         String str3 = Util.null2String(recordSet1.getString("id"));
/* 247 */         if (!str3.equals(paramString4)) {
/* 248 */           return "older";
/*     */         }
/*     */       } 
/*     */     } 
/* 252 */     List<String> list = paramPropertiesUtil.getKeys();
/* 253 */     byte b = 0;
/* 254 */     for (String str1 : list) {
/* 255 */       if (str1.trim().equals(paramString2.trim()) && b < 2) {
/* 256 */         b++;
/*     */       }
/*     */     } 
/* 259 */     if (b > 1) {
/* 260 */       return "multierror";
/*     */     }
/*     */     
/* 263 */     String str = Util.null2String(paramPropertiesUtil.getPropertyVal(paramString2));
/* 264 */     if (str == null || str.trim().equals("")) {
/* 265 */       if (b == 0) {
/* 266 */         return "no";
/*     */       }
/* 268 */       return "diff";
/*     */     } 
/*     */ 
/*     */     
/* 272 */     RecordSet recordSet = new RecordSet();
/* 273 */     recordSet.execute("select needcheck from configPropertiesFile where id='" + paramString4 + "'");
/* 274 */     if (recordSet.next()) {
/* 275 */       String str1 = recordSet.getString("needcheck");
/* 276 */       if ("0".equals(str1)) {
/* 277 */         return "ok";
/*     */       }
/*     */     } 
/*     */ 
/*     */     
/* 282 */     if (paramString3.trim().equals(str.trim())) {
/* 283 */       return "ok";
/*     */     }
/* 285 */     return "diff";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String checkXmlFile(String paramString1, Document paramDocument, String paramString2, String paramString3, String paramString4) {
/*     */     try {
/* 301 */       if (paramString3 != null && !paramString3.equals("")) {
/* 302 */         RecordSet recordSet = new RecordSet();
/* 303 */         String str1 = paramString1.replaceAll("'+", "'").replaceAll("'", "''");
/*     */ 
/*     */         
/* 306 */         String str2 = " where(a.isdelete = '0' or  a.isdelete is null) and b.isdelete=0 and a.xpath='" + str1 + "' and b.filepath = (select filepath from configFileManager where isdelete=0 and id = (select configfileid from configXmlFile where id =" + paramString3 + " ))" + (paramString4.equals("") ? "" : (" and b.id in(" + paramString4 + ")"));
/* 307 */         String str3 = "select a.id,b.kbversion,c.sysversion from configXmlFile a left join configFileManager b on a.configfileid = b.id LEFT JOIN CustomerKBVersion c ON c.name = b.kbversion " + str2 + " order by c.sysversion desc,b.kbversion desc,a.id desc";
/* 308 */         recordSet.execute(str3);
/* 309 */         if (recordSet.next()) {
/* 310 */           String str = Util.null2String(recordSet.getString("id"));
/* 311 */           if (!str.equals(paramString3)) {
/* 312 */             return "older";
/*     */           }
/*     */         } 
/*     */       } 
/*     */       
/* 317 */       if (paramString1 != null && !"".equals(paramString1)) {
/* 318 */         List<Element> list = paramDocument.selectNodes(paramString1);
/* 319 */         String str = checkXpathAndContent(paramString1, paramDocument, paramString2);
/* 320 */         if (str != null) {
/* 321 */           return str;
/*     */         }
/*     */ 
/*     */         
/* 325 */         if (list.size() == 1) {
/*     */ 
/*     */           
/* 328 */           Element element1 = list.get(0);
/* 329 */           Element element2 = element1.getParent();
/* 330 */           if (element2 == null) {
/* 331 */             element2 = paramDocument.getRootElement();
/*     */           }
/*     */           
/* 334 */           String str1 = element2.asXML();
/*     */ 
/*     */ 
/*     */           
/* 338 */           selectXmlNodeUtil selectXmlNodeUtil1 = new selectXmlNodeUtil();
/* 339 */           boolean bool1 = selectXmlNodeUtil1.checkXmlFile(str1, paramString2);
/* 340 */           if (bool1)
/*     */           {
/* 342 */             return "ok";
/*     */           }
/* 344 */           return "diff";
/*     */         } 
/*     */         
/* 347 */         if (list.size() > 1)
/*     */         {
/* 349 */           return "multierror";
/*     */         }
/* 351 */         selectXmlNodeUtil selectXmlNodeUtil = new selectXmlNodeUtil();
/* 352 */         boolean bool = selectXmlNodeUtil.checkXmlFile(paramDocument.asXML(), paramString2);
/* 353 */         if (bool) {
/* 354 */           return "ok";
/*     */         }
/*     */         
/* 357 */         return "no";
/*     */       } 
/*     */ 
/*     */       
/* 361 */       return "no";
/*     */     
/*     */     }
/* 364 */     catch (Exception exception) {
/* 365 */       exception.printStackTrace();
/* 366 */       return "xpatherror";
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String checkXpathAndContent(String paramString1, Document paramDocument, String paramString2) {
/* 380 */     Document document = (Document)paramDocument.clone();
/* 381 */     String str = null;
/* 382 */     if (paramString1 == null || "".equals(paramString1)) {
/* 383 */       return null;
/*     */     }
/* 385 */     List<Element> list = document.selectNodes(paramString1);
/* 386 */     if (list.size() == 1) {
/*     */       
/* 388 */       Element element1 = list.get(0);
/* 389 */       Element element2 = element1.getParent();
/* 390 */       if (element2 == null) {
/* 391 */         element2 = document.getRootElement();
/*     */       }
/*     */       
/* 394 */       Element element3 = getElement(paramString2);
/* 395 */       if (element3 == null) {
/* 396 */         return "contenterror";
/*     */       }
/* 398 */       List<Element> list1 = element3.elements();
/* 399 */       ArrayList<String> arrayList = new ArrayList();
/* 400 */       for (byte b1 = 0; b1 < list1.size(); b1++) {
/* 401 */         Element element4 = list1.get(b1);
/* 402 */         Element element5 = element4.createCopy();
/* 403 */         element2.add(element5);
/* 404 */         String str1 = element5.getUniquePath();
/* 405 */         arrayList.add(str1);
/*     */       } 
/*     */ 
/*     */ 
/*     */       
/* 410 */       List list2 = document.selectNodes(paramString1);
/* 411 */       if (list2.size() == 1 || list2.size() == 0) {
/* 412 */         str = "xpathorcontenterror";
/*     */       }
/*     */       
/* 415 */       Collections.sort(arrayList, new Comparator<String>()
/*     */           {
/*     */             public int compare(String param1String1, String param1String2) {
/* 418 */               if (param1String1.compareTo(param1String2) > 0) {
/* 419 */                 return -1;
/*     */               }
/* 421 */               return 1;
/*     */             }
/*     */           });
/*     */       
/* 425 */       for (byte b2 = 0; b2 < arrayList.size(); b2++) {
/* 426 */         Element element = (Element)document.selectSingleNode(arrayList.get(b2));
/* 427 */         if (element != null) {
/* 428 */           Element element4 = element.getParent();
/* 429 */           element4.remove(element);
/*     */         } 
/*     */       } 
/* 432 */       return str;
/* 433 */     }  if (list.size() == 0) {
/*     */       
/* 435 */       Element element = getElement(paramString2);
/* 436 */       List<Element> list1 = element.elements();
/* 437 */       if (list1.size() > 0) {
/* 438 */         String str1 = ((Element)list1.get(0)).getName();
/*     */         
/* 440 */         String str2 = getNodeByXpath(paramString1, document, str1);
/* 441 */         if ("".equals(str2)) {
/* 442 */           return "xpatherror";
/*     */         }
/* 444 */         Element element1 = (Element)document.selectSingleNode(str2);
/*     */         
/* 446 */         if (element1 == null) {
/* 447 */           return "xpatherror";
/*     */         }
/* 449 */         ArrayList<String> arrayList = new ArrayList();
/*     */         
/* 451 */         for (byte b1 = 0; b1 < list1.size(); b1++) {
/* 452 */           Element element2 = list1.get(b1);
/* 453 */           Element element3 = element2.createCopy();
/* 454 */           element1.add(element3);
/* 455 */           String str3 = element3.getUniquePath();
/* 456 */           arrayList.add(str3);
/*     */         } 
/*     */ 
/*     */ 
/*     */         
/* 461 */         List list2 = document.selectNodes(paramString1);
/* 462 */         if (list2.size() == 0) {
/* 463 */           str = "xpathorcontenterror";
/*     */         }
/*     */         
/* 466 */         Collections.sort(arrayList, new Comparator<String>()
/*     */             {
/*     */               public int compare(String param1String1, String param1String2) {
/* 469 */                 if (param1String1.compareTo(param1String2) > 0) {
/* 470 */                   return -1;
/*     */                 }
/* 472 */                 return 1;
/*     */               }
/*     */             });
/*     */         
/* 476 */         for (byte b2 = 0; b2 < arrayList.size(); b2++) {
/* 477 */           Element element2 = (Element)document.selectSingleNode(arrayList.get(b2));
/* 478 */           if (element2 != null) {
/* 479 */             Element element3 = element2.getParent();
/* 480 */             element3.remove(element2);
/*     */           } 
/*     */         } 
/* 483 */         return str;
/*     */       } 
/*     */     } 
/* 486 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Element getElement(String paramString) {
/* 496 */     Element element = null;
/* 497 */     Document document = Str2Document("<myroot>" + paramString + "</myroot>");
/* 498 */     if (document != null) {
/* 499 */       element = document.getRootElement();
/*     */     }
/*     */     
/* 502 */     return element;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Document Str2Document(String paramString) {
/* 513 */     SAXReader sAXReader = new SAXReader();
/* 514 */     sAXReader.setEntityResolver(new IgnoreDTDEntityResolver());
/* 515 */     Document document = null;
/*     */     try {
/* 517 */       document = sAXReader.read(new ByteArrayInputStream(paramString.getBytes()));
/* 518 */     } catch (DocumentException documentException) {
/*     */       
/* 520 */       documentException.printStackTrace();
/*     */     } 
/* 522 */     return document;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getNodeByXpath(String paramString1, Document paramDocument, String paramString2) {
/* 534 */     if (paramString1 == null || "".equals(paramString1)) {
/* 535 */       return paramString1;
/*     */     }
/* 537 */     int i = paramString1.lastIndexOf("/");
/* 538 */     paramString1 = paramString1.substring(0, i);
/*     */     
/*     */     try {
/* 541 */       List<Element> list = paramDocument.selectNodes(paramString1);
/* 542 */       if (list.size() > 0) {
/* 543 */         String str = ((Element)list.get(0)).getName();
/*     */         
/* 545 */         if (paramString2 != null && !"".equals(paramString2) && 
/* 546 */           str != null) {
/* 547 */           Object object = null;
/*     */           
/* 549 */           if (paramString2.equals(str)) {
/* 550 */             int j = paramString1.lastIndexOf("/");
/* 551 */             paramString1 = paramString1.substring(0, j);
/*     */           } 
/*     */         } 
/*     */         
/* 555 */         return paramString1;
/*     */       } 
/* 557 */       return getNodeByXpath(paramString1, paramDocument, paramString2);
/*     */     }
/* 559 */     catch (Exception exception) {
/*     */       
/* 561 */       return getNodeByXpath(paramString1, paramDocument, paramString2);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public ArrayList<ConfigDetail> getConfigDetailList(Map<String, String> paramMap) {
/* 571 */     ArrayList<ConfigDetail> arrayList = new ArrayList();
/* 572 */     RecordSet recordSet = new RecordSet();
/* 573 */     String str1 = Util.null2String(paramMap.get("checktype"));
/* 574 */     String str2 = Util.null2String(paramMap.get("ids"));
/* 575 */     String str3 = Util.null2String(paramMap.get("filename"));
/* 576 */     String str4 = Util.null2String(paramMap.get("attrname"));
/* 577 */     String str5 = Util.null2String(paramMap.get("attrvalue"));
/* 578 */     String str6 = Util.null2String(paramMap.get("requisite"));
/* 579 */     if (str2 == null || str2.equals("")) {
/* 580 */       return arrayList;
/*     */     }
/* 582 */     if (str2.endsWith(",")) {
/* 583 */       str2 = str2.substring(0, str2.length() - 1);
/*     */     }
/* 585 */     if (str1.equals("")) {
/* 586 */       recordSet.execute("select filetype from configFileManager where id=" + str2.split(",")[0]);
/* 587 */       if (recordSet.next()) {
/* 588 */         str1 = recordSet.getString("filetype");
/*     */       }
/*     */     } 
/*     */     
/* 592 */     if ("0".equals(str1) || "1".equals(str1)) {
/* 593 */       String str = " select a.id,a.attrname,a.requisite,a.attrvalue,a.attrnotes,b.filepath,b.filename  from configPropertiesFile a,configFileManager b where (a.isdelete = '0' or  a.isdelete is null) and b.isdelete=0 and a.configfileid = b.id and b.id in(" + str2 + ") ";
/* 594 */       if (!"".equals(str3)) {
/* 595 */         str = str + " and b.filename like '%" + str3 + "%' ";
/*     */       }
/* 597 */       if (!"".equals(str4)) {
/* 598 */         str = str + " and a.attrname like '%" + str4 + "%' ";
/*     */       }
/*     */       
/* 601 */       if (!"".equals(str5)) {
/* 602 */         str = str + " and a.attrvalue like '%" + str5 + "%' ";
/*     */       }
/*     */       
/* 605 */       if (!"".equals(str6)) {
/* 606 */         str = str + " and a.requisite='1' ";
/*     */       }
/*     */       
/* 609 */       str = str + " and a.attrname is not null " + (recordSet.getDBType().equals("oracle") ? "" : "and attrname<>'' ") + " order by b.labelid desc, a.propdetailid desc";
/*     */       
/* 611 */       recordSet.executeSql(str);
/* 612 */       while (recordSet.next()) {
/* 613 */         String str7 = recordSet.getString("attrname");
/* 614 */         String str8 = recordSet.getString("attrvalue");
/* 615 */         String str9 = recordSet.getString("attrnotes");
/* 616 */         String str10 = recordSet.getString("filepath");
/* 617 */         String str11 = recordSet.getString("filename");
/*     */         
/* 619 */         int i = recordSet.getInt("id");
/* 620 */         ConfigDetail configDetail = new ConfigDetail();
/* 621 */         configDetail.setFilepath(str10);
/* 622 */         configDetail.setKeyname(str7);
/* 623 */         configDetail.setValue(str8);
/* 624 */         configDetail.setNotes(str9);
/* 625 */         configDetail.setFilename(str11);
/* 626 */         configDetail.setType("1");
/* 627 */         configDetail.setDetailid(i);
/* 628 */         configDetail.setRequisite(recordSet.getString("requisite"));
/* 629 */         arrayList.add(configDetail);
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 634 */     if ("0".equals(str1) || "2".equals(str1)) {
/* 635 */       String str = " select a.id,a.attrvalue,a.xpath,a.requisite,b.filepath,b.filename from configXmlFile a,configFileManager b where (a.isdelete = '0' or  a.isdelete is null) and b.isdelete=0 and a.configfileid = b.id and b.id in(" + str2 + ")";
/* 636 */       if (!"".equals(str3)) {
/* 637 */         str = str + " and b.filename like '%" + str3 + "%' ";
/*     */       }
/* 639 */       if (!"".equals(str5)) {
/* 640 */         str = str + " and a.attrvalue like '%" + str5 + "%' ";
/*     */       }
/* 642 */       if (!"".equals(str6)) {
/* 643 */         str = str + " and a.requisite='1' ";
/*     */       }
/* 645 */       str = str + " and a.attrvalue is not null  order by  b.labelid desc, a.xmldetailid desc";
/* 646 */       recordSet.executeSql(str);
/*     */       
/* 648 */       while (recordSet.next()) {
/* 649 */         String str7 = recordSet.getString("attrvalue");
/* 650 */         String str8 = recordSet.getString("xpath");
/* 651 */         String str9 = recordSet.getString("filepath");
/* 652 */         String str10 = recordSet.getString("filename");
/*     */         
/* 654 */         int i = recordSet.getInt("id");
/*     */         
/* 656 */         ConfigDetail configDetail = new ConfigDetail();
/* 657 */         configDetail.setFilepath(str9);
/* 658 */         configDetail.setValue(str7);
/* 659 */         configDetail.setXpath(str8);
/* 660 */         configDetail.setType("2");
/* 661 */         configDetail.setDetailid(i);
/* 662 */         configDetail.setFilename(str10);
/* 663 */         configDetail.setRequisite(recordSet.getString("requisite"));
/* 664 */         arrayList.add(configDetail);
/*     */       } 
/*     */     } 
/*     */     
/* 668 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, String> getDiffIds(String paramString) {
/* 676 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 677 */     if (paramString.equals("") || paramString.replaceAll(",", "").equals("")) {
/* 678 */       return (Map)hashMap;
/*     */     }
/* 680 */     if (paramString.endsWith(",")) {
/* 681 */       paramString = paramString.substring(0, paramString.length() - 1);
/*     */     }
/*     */     
/* 684 */     RecordSet recordSet = new RecordSet();
/* 685 */     String str1 = "";
/* 686 */     String str2 = "";
/* 687 */     String str3 = "select id from configFileManager where isdelete=0 and filetype = 2 and id in(" + paramString + ")";
/* 688 */     String str4 = "select id from configFileManager where isdelete=0 and filetype = 1 and id in(" + paramString + ")";
/* 689 */     recordSet.execute(str3);
/* 690 */     while (recordSet.next()) {
/* 691 */       str1 = str1 + recordSet.getString("id") + ",";
/*     */     }
/*     */     
/* 694 */     recordSet.execute(str4);
/* 695 */     while (recordSet.next()) {
/* 696 */       str2 = str2 + recordSet.getString("id") + ",";
/*     */     }
/*     */     
/* 699 */     if (str1.endsWith(",")) {
/* 700 */       str1 = str1.substring(0, str1.length() - 1);
/*     */     }
/* 702 */     if (str2.endsWith(",")) {
/* 703 */       str2 = str2.substring(0, str2.length() - 1);
/*     */     }
/* 705 */     hashMap.put("1", str2);
/* 706 */     hashMap.put("2", str1);
/* 707 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   public String changeStr(String paramString) {
/* 711 */     paramString = paramString.replaceAll("<", "&lt;");
/* 712 */     paramString = paramString.replaceAll(">", "&gt;");
/*     */     
/* 714 */     paramString = paramString.replaceAll("\"", "&quot;");
/* 715 */     paramString = paramString.replaceAll("'", "&apos;");
/* 716 */     paramString = paramString.replaceAll(" ", "&nbsp;");
/* 717 */     paramString = paramString.replaceAll("\n", "<br/>");
/* 718 */     return paramString;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, String> getXmlStatusName(String paramString) {
/* 723 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 724 */     if ("ok".equals(paramString)) {
/* 725 */       hashMap.put("status", "1");
/* 726 */       hashMap.put("statusName", "" + SystemEnv.getHtmlLabelName(10004085, ThreadVarLanguage.getLang()) + "");
/* 727 */     } else if ("diff".equals(paramString)) {
/* 728 */       hashMap.put("status", "3");
/* 729 */       hashMap.put("statusName", "" + SystemEnv.getHtmlLabelName(10004086, ThreadVarLanguage.getLang()) + "");
/* 730 */     } else if ("xpatherror".equals(paramString)) {
/* 731 */       hashMap.put("status", "4");
/* 732 */       hashMap.put("statusName", "" + SystemEnv.getHtmlLabelName(10004087, ThreadVarLanguage.getLang()) + "");
/* 733 */     } else if ("multierror".equals(paramString)) {
/* 734 */       hashMap.put("status", "5");
/* 735 */       hashMap.put("statusName", "xpath" + SystemEnv.getHtmlLabelName(10004088, ThreadVarLanguage.getLang()) + "");
/* 736 */     } else if ("contenterror".equals(paramString)) {
/* 737 */       hashMap.put("status", "6");
/* 738 */       hashMap.put("statusName", "" + SystemEnv.getHtmlLabelName(10004089, ThreadVarLanguage.getLang()) + "XML" + SystemEnv.getHtmlLabelName(15196, ThreadVarLanguage.getLang()) + "");
/* 739 */     } else if ("xpathorcontenterror".equals(paramString)) {
/* 740 */       hashMap.put("status", "7");
/* 741 */       hashMap.put("statusName", "" + SystemEnv.getHtmlLabelName(10004090, ThreadVarLanguage.getLang()) + "");
/* 742 */     } else if ("older".equals(paramString)) {
/* 743 */       hashMap.put("status", "8");
/* 744 */       hashMap.put("statusName", "" + SystemEnv.getHtmlLabelName(10004091, ThreadVarLanguage.getLang()) + "");
/*     */     } else {
/* 746 */       hashMap.put("status", "2");
/* 747 */       hashMap.put("statusName", "" + SystemEnv.getHtmlLabelName(129337, ThreadVarLanguage.getLang()) + "");
/*     */     } 
/* 749 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, String> getPropStatusName(String paramString) {
/* 754 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 755 */     if ("ok".equals(paramString)) {
/* 756 */       hashMap.put("status", "1");
/* 757 */       hashMap.put("statusName", "" + SystemEnv.getHtmlLabelName(10004085, ThreadVarLanguage.getLang()) + "");
/* 758 */     } else if ("diff".equals(paramString)) {
/* 759 */       hashMap.put("status", "3");
/* 760 */       hashMap.put("statusName", "" + SystemEnv.getHtmlLabelName(10004086, ThreadVarLanguage.getLang()) + "");
/* 761 */     } else if ("multierror".equals(paramString)) {
/* 762 */       hashMap.put("status", "5");
/* 763 */       hashMap.put("statusName", "" + SystemEnv.getHtmlLabelName(10004088, ThreadVarLanguage.getLang()) + "");
/* 764 */     } else if ("keyorcontenterror".equals(paramString)) {
/* 765 */       hashMap.put("status", "7");
/* 766 */       hashMap.put("statusName", "" + SystemEnv.getHtmlLabelName(10004090, ThreadVarLanguage.getLang()) + "");
/* 767 */     } else if ("older".equals(paramString)) {
/* 768 */       hashMap.put("status", "8");
/* 769 */       hashMap.put("statusName", "" + SystemEnv.getHtmlLabelName(10004091, ThreadVarLanguage.getLang()) + "");
/*     */     } else {
/* 771 */       hashMap.put("status", "2");
/* 772 */       hashMap.put("statusName", "" + SystemEnv.getHtmlLabelName(129337, ThreadVarLanguage.getLang()) + "");
/*     */     } 
/* 774 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   public String getConfigColor(String paramString1, String paramString2) {
/* 778 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/* 779 */     String str1 = "";
/* 780 */     String str2 = "";
/* 781 */     if (arrayOfString.length == 2) {
/* 782 */       str1 = arrayOfString[0];
/* 783 */       str2 = arrayOfString[1];
/*     */     } 
/*     */     
/* 786 */     if ((!"".equals(str1) && str1.equals(SystemEnv.getHtmlLabelName(10004085, ThreadVarLanguage.getLang()) + "")) || str1.equals(SystemEnv.getHtmlLabelName(10004091, ThreadVarLanguage.getLang()) + "")) {
/*     */       
/* 788 */       str2 = "<span style='color:green'>" + str2 + "</span>";
/* 789 */     } else if (!"".equals(str1) && str1.equals(SystemEnv.getHtmlLabelName(129337, ThreadVarLanguage.getLang()) + "")) {
/*     */       
/* 791 */       str2 = "<span style='color:red'>" + str2 + "</span>";
/*     */     } else {
/*     */       
/* 794 */       str2 = "<span style='color:#FFCC00'>" + str2 + "</span>";
/*     */     } 
/* 796 */     return str2;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getCurrentUsedFileIds() {
/* 801 */     String str1 = "";
/* 802 */     RecordSet recordSet1 = new RecordSet();
/* 803 */     String str2 = "select * from ConfigFileManager  ";
/* 804 */     String str3 = "where isdelete=0 ";
/*     */     
/* 806 */     RecordSet recordSet2 = new RecordSet();
/* 807 */     String str4 = "";
/* 808 */     String str5 = "";
/* 809 */     recordSet2.execute("select * from license");
/* 810 */     if (recordSet2.next()) {
/* 811 */       String str = Util.null2String(recordSet2.getString("cversion"));
/* 812 */       if (str.contains("+")) {
/* 813 */         str4 = str.substring(str.indexOf("+") + 1, str.length());
/* 814 */       } else if (!str.equals("")) {
/* 815 */         str5 = str;
/*     */       } 
/*     */     } 
/* 818 */     if (!str4.equals("")) {
/* 819 */       RecordSet recordSet = new RecordSet();
/* 820 */       String str = "select max(sysversion) as name from CustomerKBVersion where LOWER(name) <= '" + str4.toLowerCase() + "' and LOWER(name) like '%kb%'";
/* 821 */       recordSet.execute(str);
/* 822 */       if (recordSet.next()) {
/* 823 */         str3 = str3 + " and ((lower(kbversion)<= '" + str4.toLowerCase() + "' and lower(kbversion) like '%kb%' ) or (lower(kbversion) <='" + recordSet.getString("name").toLowerCase() + "' and lower(kbversion) not like '%kb%'))";
/*     */       } else {
/* 825 */         str3 = str3 + " and (lower(kbversion)<= '" + str4.toLowerCase() + "' and lower(kbversion) like '%kb%' )";
/*     */       } 
/* 827 */     } else if (!str5.equals("")) {
/* 828 */       RecordSet recordSet = new RecordSet();
/* 829 */       String str = "select max(name) as name from CustomerKBVersion where sysversion in(select name from CustomerSysVersion where name <'" + str5 + "') and lower(name) like '%kb%' ";
/* 830 */       recordSet.execute(str);
/* 831 */       if (recordSet.next()) {
/* 832 */         str3 = str3 + " and ((lower(kbversion)<='" + recordSet.getString("name").toLowerCase() + "' and lower(kbversion) like '%kb%') or (kbversion <='" + str5 + "' and lower(kbversion) not like '%kb%') )";
/*     */       }
/*     */     } 
/*     */ 
/*     */     
/* 837 */     str2 = str2 + str3;
/* 838 */     recordSet1.execute(str2);
/* 839 */     while (recordSet1.next()) {
/* 840 */       str1 = str1 + recordSet1.getString("id") + ",";
/*     */     }
/* 842 */     if (str1.endsWith(",")) {
/* 843 */       str1 = str1.substring(0, str1.length() - 1);
/*     */     }
/* 845 */     return str1;
/*     */   }
/*     */   
/*     */   public String getUpdatetime(String paramString1, String paramString2) {
/* 849 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/* 850 */     String str1 = "";
/* 851 */     String str2 = "";
/* 852 */     if (arrayOfString.length == 2) {
/* 853 */       str1 = arrayOfString[0];
/* 854 */       str2 = arrayOfString[1];
/*     */     } 
/* 856 */     if ("".equals(str1) || "".equals(str2)) {
/* 857 */       return "";
/*     */     }
/* 859 */     if (str2 != null && str2.equalsIgnoreCase("xml")) {
/* 860 */       str2 = "2";
/*     */     } else {
/* 862 */       str2 = "1";
/*     */     } 
/* 864 */     RecordSet recordSet = new RecordSet();
/* 865 */     String str3 = "select max(updatetime) as updatetime from autoConfigLog where configdetailid=" + str1 + " and configtype=" + str2;
/* 866 */     recordSet.execute(str3);
/* 867 */     if (recordSet.next()) {
/* 868 */       return Util.null2String(recordSet.getString("updatetime"));
/*     */     }
/* 870 */     return "";
/*     */   }
/*     */ 
/*     */   
/*     */   public String getLocalvalue(String paramString) {
/* 875 */     return paramString.replaceAll("\\\\\r", "").replaceAll("\\\\\n", "").replaceAll("\\\\\r\n", "").replaceAll("<br/>", "");
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/CheckConfigFile.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */