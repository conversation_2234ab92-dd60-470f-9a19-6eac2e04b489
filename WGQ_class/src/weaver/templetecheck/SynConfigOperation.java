/*     */ package weaver.templetecheck;
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import java.text.SimpleDateFormat;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Date;
/*     */ import java.util.HashMap;
/*     */ import java.util.Iterator;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.Set;
/*     */ import java.util.concurrent.CopyOnWriteArrayList;
/*     */ import java.util.regex.Matcher;
/*     */ import org.dom4j.Element;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.TimeUtil;
/*     */ import wscheck.MD5Coder;
/*     */ 
/*     */ public class SynConfigOperation extends BaseBean {
/*  19 */   private int fileid = 0;
/*     */   
/*  21 */   private String configmainids = "";
/*  22 */   private String elementName = "";
/*  23 */   private String elementXpath = "";
/*  24 */   private String note = "syn node config";
/*  25 */   private String action = "";
/*  26 */   private String detailNote = "";
/*     */   
/*     */   private boolean finishCheck = false;
/*  29 */   String fromtype = "";
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONObject syn(String paramString, Map<String, Object> paramMap) {
/*  34 */     this.fromtype = paramString;
/*  35 */     JSONObject jSONObject = new JSONObject();
/*  36 */     jSONObject = checkNode();
/*  37 */     if (jSONObject.get("status").equals("failure")) {
/*  38 */       writeLog("###节点校验未通过...###");
/*  39 */       writeLog("###节点校验未通过..." + jSONObject.toJSONString());
/*  40 */       return jSONObject;
/*     */     } 
/*  42 */     writeLog("###开始同步配置...###");
/*  43 */     this.action = (String)paramMap.get("action");
/*     */     
/*  45 */     generateRule(paramMap);
/*  46 */     configOp(paramString, paramMap);
/*     */     
/*  48 */     jSONObject = synNode(paramString);
/*     */     
/*  50 */     writeLog("###配置同步结束...###");
/*     */     
/*  52 */     return jSONObject;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONObject synBatch(String paramString, ArrayList<HashMap> paramArrayList) {
/*  62 */     JSONObject jSONObject = new JSONObject();
/*  63 */     this.fromtype = paramString;
/*  64 */     jSONObject = checkNode();
/*  65 */     if (jSONObject.get("status").equals("failure")) {
/*  66 */       writeLog("###节点校验未通过...###");
/*  67 */       writeLog("###节点校验未通过..." + jSONObject.toJSONString());
/*  68 */       return jSONObject;
/*     */     } 
/*  70 */     writeLog("###开始批量同步配置...###");
/*  71 */     this.action = "batch";
/*     */     
/*  73 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  74 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*     */     
/*  76 */     for (byte b = 0; b < paramArrayList.size(); b++) {
/*  77 */       Map<String, String> map = paramArrayList.get(b);
/*  78 */       String str1 = (String)map.get("filepath");
/*  79 */       String str2 = (String)map.get("action");
/*     */ 
/*     */       
/*  82 */       if ("jc".equals(paramString)) {
/*  83 */         map.put("filepath", "/WEB-INF/web.xml");
/*  84 */         map.put("note", "integration auto config");
/*     */       } 
/*     */       
/*  87 */       if ("delete".equals(str2)) {
/*  88 */         updateElements(str1, (Map)map, (Map)hashMap1);
/*     */       } else {
/*  90 */         updateElements(str1, (Map)map, (Map)hashMap2);
/*     */       } 
/*     */       
/*  93 */       generateRule((Map)map);
/*     */     } 
/*     */     
/*  96 */     if ("jc".equals(paramString)) {
/*  97 */       this.note = "integration auto config";
/*     */     }
/*     */ 
/*     */     
/* 101 */     inserRreorganizeMap2DB((Map)hashMap1);
/* 102 */     inserRreorganizeMap2DB((Map)hashMap2);
/*     */ 
/*     */     
/* 105 */     jSONObject = synNode(paramString);
/* 106 */     writeLog("###批量配置同步结束...###");
/* 107 */     return jSONObject;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void inserRreorganizeMap2DB(Map<String, Object> paramMap) {
/* 115 */     Set<String> set = paramMap.keySet();
/* 116 */     Iterator<String> iterator = set.iterator();
/* 117 */     while (iterator.hasNext()) {
/* 118 */       String str = iterator.next();
/* 119 */       HashMap<String, Object> hashMap = (HashMap)paramMap.get(str);
/* 120 */       insertXML2DB(hashMap);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void updateElements(String paramString, Map<String, Object> paramMap1, Map<String, Object> paramMap2) {
/* 131 */     if (paramMap2.containsKey(paramString)) {
/* 132 */       Map<String, String> map = (Map)paramMap2.get(paramString);
/*     */       
/* 134 */       String str1 = getElementContent((List<Element>)map.get("elements"));
/* 135 */       Map map1 = (Map)paramMap1.get(paramString);
/* 136 */       String str2 = getElementContent((List<Element>)map1.get("elements"));
/* 137 */       map.put("elements", str2 + "\r\n" + str1);
/*     */     } else {
/* 139 */       paramMap2.put(paramString, paramMap1);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void configOp(String paramString, Map<String, Object> paramMap) {
/* 149 */     String str = (String)paramMap.get("filepath");
/* 150 */     this.action = (String)paramMap.get("action");
/* 151 */     if (str.endsWith(".xml")) {
/*     */       
/* 153 */       if ("jc".equals(paramString)) {
/* 154 */         paramMap.put("filepath", "/WEB-INF/web.xml");
/*     */         
/* 156 */         this.note = "integration auto config";
/*     */       } 
/*     */ 
/*     */       
/* 160 */       insertXML2DB(paramMap);
/*     */     } else {
/* 162 */       insertProp2DB(paramMap);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void generateRule(Map<String, Object> paramMap) {
/* 173 */     String str1 = (String)paramMap.get("filepath");
/* 174 */     if (!str1.endsWith(".xml")) {
/*     */       return;
/*     */     }
/* 177 */     List<Element> list = (List)paramMap.get("elements");
/* 178 */     String str2 = (String)paramMap.get("action");
/* 179 */     Element element1 = (Element)paramMap.get("pre-condition");
/* 180 */     Element element2 = (Element)paramMap.get("aft-condition");
/* 181 */     String str3 = getElementContent(list);
/*     */     
/* 183 */     if ("add".equals(str2)) {
/* 184 */       String str = "";
/* 185 */       if (element2 != null) {
/* 186 */         generateOrderRule("after", str3, element2.asXML());
/*     */       }
/* 188 */       if (element1 != null) {
/* 189 */         generateOrderRule("pre", str3, element1.asXML());
/*     */       }
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void updateXMLDel(ConcurrentHashMap<String, Object> paramConcurrentHashMap) {
/* 218 */     String str = GCONST.getRootPath() + "templetecheck/xml/deleteconfig.xml";
/* 219 */     XMLUtil xMLUtil = new XMLUtil(str);
/* 220 */     if ("delete".equals(this.action)) {
/* 221 */       StringBuffer stringBuffer1 = new StringBuffer();
/* 222 */       stringBuffer1.append("<key>" + this.elementName + "</key>");
/* 223 */       stringBuffer1.append("<filepath>" + paramConcurrentHashMap.get("filepath") + "</filepath>");
/* 224 */       RecordSet recordSet = new RecordSet();
/* 225 */       recordSet.executeQuery("select cversion from license", new Object[0]);
/* 226 */       String str1 = "";
/* 227 */       if (recordSet.next()) {
/* 228 */         str1 = recordSet.getString("cversion");
/*     */       }
/* 230 */       stringBuffer1.append("<KBVersion>" + str1 + "</KBVersion>");
/* 231 */       stringBuffer1.append("<xpath>" + this.elementXpath + "</xpath>");
/*     */       
/* 233 */       List<Element> list = (List)paramConcurrentHashMap.get("elements");
/* 234 */       StringBuffer stringBuffer2 = new StringBuffer();
/* 235 */       for (byte b = 0; b < list.size(); b++) {
/* 236 */         stringBuffer2.append(((Element)list.get(b)).asXML());
/* 237 */         stringBuffer2.append("\r\n");
/*     */       } 
/* 239 */       stringBuffer1.append("<content>\r\n<![CDATA[\r\n" + stringBuffer2
/*     */           
/* 241 */           .toString() + "]]>\r\n</content>");
/*     */ 
/*     */ 
/*     */       
/* 245 */       xMLUtil.updateXml("element", "/elements", "", "", stringBuffer1.toString(), "add");
/*     */     } else {
/*     */       
/*     */       try {
/* 249 */         xMLUtil.removeNodeByXpath("/elements/element/key[text()='" + this.elementName + "']/parent::*");
/* 250 */       } catch (IOException iOException) {
/* 251 */         iOException.printStackTrace();
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void generateOrderRule(String paramString1, String paramString2, String paramString3) {
/* 266 */     writeLog("###开始生成配置顺序规则...###");
/*     */     
/* 268 */     JSONObject jSONObject = new JSONObject();
/* 269 */     if (this.detailNote == null || !"".equals(this.detailNote));
/*     */ 
/*     */     
/* 272 */     XMLUtil xMLUtil = new XMLUtil();
/* 273 */     PropertiesUtil propertiesUtil = new PropertiesUtil();
/* 274 */     String str1 = GCONST.getRootPath() + "templetecheck" + File.separatorChar + "xml" + File.separatorChar + "condition.properties";
/* 275 */     propertiesUtil.load(str1);
/* 276 */     String str2 = xMLUtil.getElementName(paramString3);
/* 277 */     String str3 = null;
/* 278 */     String str4 = "";
/* 279 */     String str5 = "";
/*     */     
/* 281 */     Document document = xMLUtil.Str2Document("<myroot>" + paramString2 + "</myroot>");
/* 282 */     List<Element> list = document.getRootElement().elements();
/* 283 */     if (list != null && list.size() > 0) {
/*     */       
/* 285 */       str4 = xMLUtil.getElementName(((Element)list.get(0)).asXML());
/* 286 */       List<String> list1 = propertiesUtil.getKeys();
/* 287 */       for (byte b = 0; b < list1.size(); b++) {
/* 288 */         String str = list1.get(b);
/* 289 */         if (str4.indexOf(str) > -1) {
/* 290 */           str3 = propertiesUtil.getPropertyVal(str);
/* 291 */           writeLog("###开始生成配置顺序规则...conditionConfig：" + str3 + "###");
/*     */           
/*     */           break;
/*     */         } 
/*     */       } 
/*     */     } 
/* 297 */     if (str3 != null && !"".equals(str3)) {
/* 298 */       if ("jc".equals(this.fromtype) && 
/* 299 */         str3 != null && !str3.contains("encodingFilter")) {
/* 300 */         str3 = str3 + ",after@encodingFilter";
/*     */       }
/*     */       
/* 303 */       jSONObject.put(str4, str3);
/*     */     
/*     */     }
/* 306 */     else if ("pre".equals(paramString1)) {
/* 307 */       str5 = "previous@" + str2;
/* 308 */       jSONObject.put(str4, str5);
/*     */     } else {
/* 310 */       str5 = "after@" + str2;
/* 311 */       jSONObject.put(str4, str5);
/*     */     } 
/*     */ 
/*     */     
/* 315 */     this.detailNote = jSONObject.toString();
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 320 */     writeLog("###生成配置顺序规则完成...###");
/*     */   }
/*     */   
/*     */   public void insertProp2DB(Map<String, Object> paramMap) {
/* 324 */     String str1 = (String)paramMap.get("filepath");
/* 325 */     String str2 = (String)paramMap.get("note");
/* 326 */     String str3 = (String)paramMap.get("action");
/* 327 */     String str4 = "0";
/* 328 */     String str5 = this.detailNote;
/* 329 */     if (str1 == null) {
/*     */       return;
/*     */     }
/* 332 */     if ("delete".equals(str3)) {
/* 333 */       str4 = "1";
/* 334 */       str5 = "";
/*     */     } 
/*     */     
/* 337 */     String str6 = insertMainTable(str1, str2, str4);
/* 338 */     if ("".equals(str6) || str6 == null) {
/* 339 */       writeLog("###配置插入数据库失败...###");
/*     */       return;
/*     */     } 
/* 342 */     String str7 = TimeUtil.getCurrentDateString();
/* 343 */     String str8 = TimeUtil.getOnlyCurrentTimeString();
/* 344 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 346 */     HashMap hashMap1 = (HashMap)paramMap.get("content");
/* 347 */     HashMap hashMap2 = (HashMap)paramMap.get("needCheckMap");
/* 348 */     Set set = hashMap1.entrySet();
/* 349 */     Iterator<Map.Entry> iterator = set.iterator();
/* 350 */     while (iterator.hasNext()) {
/* 351 */       Map.Entry entry = iterator.next();
/* 352 */       String str9 = (String)entry.getKey();
/* 353 */       String str10 = (String)entry.getValue();
/* 354 */       String str11 = (String)hashMap2.get(str9);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 361 */       recordSet.executeQuery("select 1 from configPropertiesFile where attrname=? and configfileid=?", new Object[] { str9, str6 });
/* 362 */       if (recordSet.next()) {
/* 363 */         recordSet.executeUpdate("update configPropertiesFile set configfileid=?,propdetailid=?,attrname =?,attrvalue =?,attrnotes =?,createdate =?,createtime =?,issystem =?,requisite =?,isdelete =?,needcheck=? where configfileid=? and attrname=?", new Object[] { str6, "0", str9, str10, "", str7, str8, "0", "0", "0", str11, str6, str9 });
/*     */         
/*     */         continue;
/*     */       } 
/* 367 */       recordSet.executeUpdate("insert into configPropertiesFile(propdetailid,configfileid,attrname,attrvalue,attrnotes,createdate,createtime,issystem,requisite,isdelete,needcheck) values(?,?,?,?,?,?,?,?,?,?,?)", new Object[] { "0", str6, str9, str10, "", str7, str8, "0", "0", "0", str11 });
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String insertMainTable(String paramString1, String paramString2, String paramString3) {
/* 382 */     if (paramString1 == null) {
/* 383 */       return "";
/*     */     }
/* 385 */     String str1 = "";
/* 386 */     char c = Util.getSeparator();
/* 387 */     RecordSet recordSet = new RecordSet();
/* 388 */     String str2 = "1";
/* 389 */     String str3 = "1";
/* 390 */     String str4 = "0";
/* 391 */     String str5 = "";
/* 392 */     String str6 = "web.xml";
/* 393 */     String str7 = TimeUtil.getCurrentDateString();
/* 394 */     String str8 = TimeUtil.getOnlyCurrentTimeString();
/*     */     
/* 396 */     if (paramString1.endsWith("properties")) {
/* 397 */       str2 = "1";
/*     */     } else {
/* 399 */       str2 = "2";
/*     */     } 
/* 401 */     if (paramString3 == null) {
/* 402 */       paramString3 = "0";
/*     */     }
/*     */     
/* 405 */     paramString1 = paramString1.replace("\\", "/");
/* 406 */     str6 = paramString1.substring(paramString1.lastIndexOf("/") + 1);
/* 407 */     if (paramString2 != null && !"".equals(paramString2)) {
/* 408 */       this.note = paramString2;
/*     */     }
/*     */     
/* 411 */     recordSet.executeQuery("select cversion from license", new Object[0]);
/* 412 */     if (recordSet.next()) {
/* 413 */       str5 = recordSet.getString("cversion");
/*     */     }
/* 415 */     recordSet.executeQuery("select 1 from CustomerKBVersion where name =?", new Object[] { str5 });
/* 416 */     if (!recordSet.next()) {
/* 417 */       recordSet.executeUpdate("insert into CustomerKBVersion(name,sysversion) values (?,'9.00')", new Object[] { str5 });
/*     */     }
/*     */ 
/*     */     
/* 421 */     ArrayList arrayList = new ArrayList();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 441 */     recordSet.executeQuery("select 1 from configFileManager where filename=? and fileinfo=? and filetype=? and isdelete =? ", new Object[] { str6, this.note, str2, paramString3 });
/* 442 */     writeLog("###主表：select 1 from configFileManager where filename='" + str6 + "' and fileinfo='" + this.note + "' and filetype='" + str2 + "' and isdelete ='" + paramString3 + "'");
/* 443 */     if (recordSet.next()) {
/* 444 */       recordSet.executeUpdate("update configFileManager set filetype =?,configtype =?,filename =?,filepath =?,qcnumber =?,fileinfo =?,kbversion =?,isdelete =?,labelid=? where filename=? and fileinfo=? and filetype=? and  isdelete =?", new Object[] { str2, str3, str6, paramString1, str4, this.note, str5, paramString3, "0", str6, this.note, str2, paramString3 });
/*     */     }
/*     */     else {
/*     */       
/* 448 */       recordSet.executeUpdate("insert into configFileManager(labelid,filetype,configtype,filename,filepath,qcnumber,fileinfo,kbversion,isdelete,createdate,createtime) values(?,?,?,?,?,?,?,?,?,?,?)", new Object[] { "0", str2, str3, str6, paramString1, str4, this.note, str5, paramString3, str7, str8 });
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 454 */     recordSet.executeQuery("select id from configFileManager where filename=? and fileinfo=? and isdelete =?", new Object[] { str6, this.note, paramString3 });
/* 455 */     if (recordSet.next()) {
/* 456 */       str1 = recordSet.getString("id");
/* 457 */       if (str1 != null && !"".equals(str1)) {
/* 458 */         this.configmainids += "," + str1;
/*     */       }
/*     */     } 
/*     */ 
/*     */     
/* 463 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void generateXPathByContent(String paramString) {
/* 471 */     writeLog("###配置内容：" + paramString);
/* 472 */     String str1 = getElementType(paramString);
/* 473 */     writeLog("###contenttype：" + str1);
/* 474 */     String str2 = getPattern(paramString);
/*     */     
/* 476 */     Pattern pattern = Pattern.compile(str2);
/*     */     
/* 478 */     Matcher matcher = pattern.matcher(paramString);
/*     */     
/* 480 */     if (matcher.find()) {
/*     */       
/* 482 */       this.elementName = matcher.group(1);
/* 483 */       writeLog("###elementName：" + this.elementName);
/* 484 */       if ("servlet".equals(str1)) {
/* 485 */         this.elementXpath = "/web-app/servlet/servlet-name[text()='" + this.elementName + "']/parent::*";
/* 486 */       } else if ("filter".equals(str1)) {
/* 487 */         this.elementXpath = "/web-app/filter/filter-name[text()='" + this.elementName + "']/parent::*";
/* 488 */       } else if ("listener".equals(str1)) {
/* 489 */         this.elementXpath = "/web-app/listener/listener-class[text()='" + this.elementName + "']/parent::*";
/* 490 */       } else if ("env-entry".equals(str1)) {
/* 491 */         this.elementXpath = "/web-app/env-entry/env-entry-name[text()='" + this.elementName + "']/parent::*";
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getElementContent(List<Element> paramList) {
/* 502 */     StringBuffer stringBuffer = new StringBuffer();
/* 503 */     for (byte b = 0; b < paramList.size(); b++) {
/* 504 */       stringBuffer.append(((Element)paramList.get(b)).asXML());
/* 505 */       stringBuffer.append("\r\n");
/*     */     } 
/* 507 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void insertXML2DB(Map<String, Object> paramMap) {
/* 515 */     String str1 = (String)paramMap.get("filepath");
/* 516 */     String str2 = (String)paramMap.get("action");
/* 517 */     String str3 = (String)paramMap.get("note");
/* 518 */     String str4 = "0";
/* 519 */     String str5 = this.detailNote;
/* 520 */     List<Element> list = (List)paramMap.get("elements");
/*     */     
/* 522 */     String str6 = getElementContent(list);
/* 523 */     if (str1 == null) {
/*     */       return;
/*     */     }
/* 526 */     if ("delete".equals(str2)) {
/* 527 */       str4 = "1";
/* 528 */       str5 = "";
/*     */     } 
/* 530 */     generateXPathByContent(str6);
/*     */     
/* 532 */     writeLog("###开始将配置插入到数据库...###");
/* 533 */     RecordSet recordSet = new RecordSet();
/* 534 */     String str7 = TimeUtil.getCurrentDateString();
/* 535 */     String str8 = TimeUtil.getOnlyCurrentTimeString();
/* 536 */     String str9 = insertMainTable(str1, str3, str4);
/* 537 */     if (str9 == null || "".equals(str9)) {
/* 538 */       writeLog("###配置插入数据库失败...###");
/*     */       
/*     */       return;
/*     */     } 
/* 542 */     if (!str1.endsWith("properties")) {
/*     */ 
/*     */       
/* 545 */       recordSet.executeQuery("select 1 from configXmlFile where configfileid=? and isdelete = ?", new Object[] { str9, str4 });
/* 546 */       writeLog("###明细表：select 1 from configXmlFile where xpath='" + this.elementXpath + "' and configfileid='" + str9 + "'and isdelete ='" + str4 + "'");
/* 547 */       if (recordSet.next()) {
/* 548 */         recordSet.executeUpdate("update configXmlFile set configfileid=?,xmldetailid=?,attrvalue =?,xpath =?,attrnotes =?,createdate =?,createtime =?,issystem =?,requisite =?,isdelete =? where configfileid=?", new Object[] { str9, "0", str6
/*     */               
/* 550 */               .trim(), this.elementXpath, str5, str7, str8, "0", "0", str4, str9 });
/*     */       } else {
/* 552 */         recordSet.executeUpdate("insert into configXmlFile(configfileid,xmldetailid,attrvalue,xpath,attrnotes,createdate,createtime,issystem,requisite,isdelete) values(?,?,?,?,?,?,?,?,?,?)", new Object[] { str9, "0", str6
/* 553 */               .trim(), this.elementXpath, str5, str7, str8, "0", "0", str4 });
/*     */       } 
/*     */     } 
/*     */     
/* 557 */     writeLog("###配置插入到数据库完成...###");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONObject synNode(String paramString) {
/* 564 */     RecordSet recordSet = new RecordSet();
/* 565 */     String str1 = "";
/* 566 */     JSONObject jSONObject = new JSONObject();
/* 567 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 568 */     CopyOnWriteArrayList<String> copyOnWriteArrayList1 = new CopyOnWriteArrayList();
/* 569 */     CopyOnWriteArrayList<String> copyOnWriteArrayList2 = new CopyOnWriteArrayList();
/*     */ 
/*     */ 
/*     */     
/* 573 */     Date date = new Date();
/* 574 */     SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
/* 575 */     String str2 = simpleDateFormat.format(date);
/* 576 */     recordSet.executeQuery("select * from autoConfigKey", new Object[0]);
/* 577 */     if (recordSet.next()) {
/* 578 */       str1 = recordSet.getString("synnode");
/*     */       
/* 580 */       if (!this.finishCheck) {
/* 581 */         recordSet.executeUpdate("update autoConfigKey set time=? where 1=1", new Object[] { str2 });
/*     */       } else {
/* 583 */         str2 = recordSet.getString("time");
/*     */       } 
/*     */     } else {
/*     */       
/* 587 */       recordSet.executeUpdate("insert into autoConfigKey(time) values (?)", new Object[] { str2 });
/*     */     } 
/*     */     
/* 590 */     if (this.configmainids != null && this.configmainids.startsWith(",")) {
/* 591 */       this.configmainids = this.configmainids.substring(1);
/*     */     }
/* 593 */     writeLog("###configmainids:" + this.configmainids);
/* 594 */     if (str1 != null && !"".equals(str1)) {
/* 595 */       recordSet.executeQuery("select * from clustersetting", new Object[0]);
/* 596 */       while (recordSet.next()) {
/* 597 */         hashMap.put(recordSet.getString("httpaddress"), "");
/*     */       }
/*     */       
/* 600 */       if (hashMap != null) {
/*     */         
/* 602 */         MD5Coder mD5Coder = new MD5Coder();
/* 603 */         String str3 = "weaVer" + str2;
/* 604 */         String str4 = mD5Coder.getMD5ofStr(str3);
/*     */         
/* 606 */         Set set = hashMap.keySet();
/* 607 */         Iterator<String> iterator = set.iterator();
/* 608 */         while (iterator.hasNext()) {
/* 609 */           String str5 = iterator.next();
/* 610 */           String str6 = str5.trim() + "/join/UpgradeInter4Monitor.jsp";
/* 611 */           HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 612 */           hashMap1.put("token", str4);
/* 613 */           hashMap1.put("operation", "synconfig");
/* 614 */           hashMap1.put("action", this.action);
/* 615 */           hashMap1.put("configtype", "2");
/* 616 */           hashMap1.put("token", str4);
/* 617 */           hashMap1.put("fromtype", paramString);
/* 618 */           hashMap1.put("ids", this.configmainids);
/*     */           
/* 620 */           String str7 = SynNodeSender.getInstance().sync(str6, (Map)hashMap1);
/* 621 */           if (str7 != null) {
/* 622 */             if (str7.indexOf("status") > -1) {
/* 623 */               JSONObject jSONObject1 = null;
/*     */               try {
/* 625 */                 jSONObject1 = JSONObject.parseObject(str7);
/* 626 */                 writeLog("###" + str5 + ":" + str7);
/* 627 */                 writeLog("###status:" + jSONObject1.get("status"));
/* 628 */                 String str = (String)jSONObject1.get("status");
/*     */                 
/* 630 */                 if ("noValidate".equals(str)) {
/* 631 */                   copyOnWriteArrayList1.add(str5);
/*     */                 }
/* 633 */                 if ("no".equals(str)) {
/* 634 */                   copyOnWriteArrayList2.add(str5);
/*     */                 }
/* 636 */               } catch (Exception exception) {
/* 637 */                 exception.printStackTrace();
/* 638 */                 copyOnWriteArrayList2.add(str5);
/*     */               }  continue;
/*     */             } 
/* 641 */             copyOnWriteArrayList2.add(str5);
/*     */             continue;
/*     */           } 
/* 644 */           copyOnWriteArrayList2.add(str5);
/*     */         } 
/*     */ 
/*     */ 
/*     */         
/* 649 */         if (copyOnWriteArrayList1.size() != 0 || copyOnWriteArrayList2.size() != 0) {
/* 650 */           jSONObject.put("status", "failure");
/* 651 */           if (copyOnWriteArrayList1.size() != 0) {
/* 652 */             jSONObject.put("noValNode", String.join(",", (Iterable)copyOnWriteArrayList1));
/*     */           } else {
/* 654 */             jSONObject.put("noValNode", "");
/*     */           } 
/* 656 */           if (copyOnWriteArrayList2.size() != 0) {
/* 657 */             jSONObject.put("errorNode", String.join(",", (Iterable)copyOnWriteArrayList2));
/*     */           } else {
/* 659 */             jSONObject.put("errorNode", "");
/*     */           } 
/*     */         } 
/*     */       } 
/*     */     } else {
/* 664 */       SystemAutoConfig systemAutoConfig = new SystemAutoConfig();
/* 665 */       if ("delete".equals(this.action)) {
/* 666 */         systemAutoConfig.deleteXMLConfig(this.configmainids);
/* 667 */         jSONObject.put("status", "success");
/* 668 */       } else if ("add".equals(this.action)) {
/*     */         
/* 670 */         String str = systemAutoConfig.synConfig(this.configmainids, paramString);
/* 671 */         jSONObject = JSONObject.parseObject(str);
/* 672 */       } else if ("batch".equals(this.action)) {
/* 673 */         String str = systemAutoConfig.synConfig(this.configmainids, paramString);
/* 674 */         jSONObject = JSONObject.parseObject(str);
/*     */       } 
/*     */     } 
/*     */     
/* 678 */     return jSONObject;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONObject synNodeConfig(String paramString) {
/* 686 */     RecordSet recordSet = new RecordSet();
/* 687 */     String str1 = "";
/* 688 */     JSONObject jSONObject = new JSONObject();
/* 689 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 690 */     CopyOnWriteArrayList<String> copyOnWriteArrayList1 = new CopyOnWriteArrayList();
/* 691 */     CopyOnWriteArrayList<String> copyOnWriteArrayList2 = new CopyOnWriteArrayList();
/* 692 */     this.action = "batch";
/*     */ 
/*     */     
/* 695 */     Date date = new Date();
/* 696 */     SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
/* 697 */     String str2 = simpleDateFormat.format(date);
/* 698 */     recordSet.executeQuery("select synnode from autoConfigKey", new Object[0]);
/* 699 */     if (recordSet.next()) {
/* 700 */       str1 = recordSet.getString("synnode");
/* 701 */       recordSet.executeUpdate("update autoConfigKey set time=? where 1=1", new Object[] { str2 });
/*     */     } else {
/* 703 */       recordSet.executeUpdate("insert into autoConfigKey(time) values (?)", new Object[] { str2 });
/*     */     } 
/*     */     
/* 706 */     if (paramString != null && paramString.startsWith(",")) {
/* 707 */       paramString = paramString.substring(1);
/*     */     }
/* 709 */     writeLog("###synnode configmainids:" + paramString + "########");
/* 710 */     if (str1 != null && !"".equals(str1)) {
/* 711 */       recordSet.executeQuery("select * from clustersetting", new Object[0]);
/* 712 */       while (recordSet.next()) {
/* 713 */         hashMap.put(recordSet.getString("httpaddress"), "");
/*     */       }
/*     */       
/* 716 */       if (hashMap != null) {
/*     */         
/* 718 */         MD5Coder mD5Coder = new MD5Coder();
/* 719 */         String str3 = "weaVer" + str2;
/* 720 */         String str4 = mD5Coder.getMD5ofStr(str3);
/*     */         
/* 722 */         Set set = hashMap.keySet();
/* 723 */         Iterator<String> iterator = set.iterator();
/* 724 */         while (iterator.hasNext()) {
/* 725 */           String str5 = iterator.next();
/* 726 */           String str6 = str5.trim() + "/join/UpgradeInter4Monitor.jsp";
/* 727 */           HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 728 */           hashMap1.put("token", str4);
/* 729 */           hashMap1.put("operation", "synconfig");
/* 730 */           hashMap1.put("action", this.action);
/* 731 */           hashMap1.put("configtype", "2");
/* 732 */           hashMap1.put("token", str4);
/* 733 */           hashMap1.put("fromtype", "synconfig");
/* 734 */           hashMap1.put("ids", paramString);
/*     */           
/* 736 */           String str7 = SynNodeSender.getInstance().sync(str6, (Map)hashMap1);
/* 737 */           if (str7 != null) {
/* 738 */             if (str7.indexOf("status") > -1) {
/* 739 */               JSONObject jSONObject1 = null;
/*     */               try {
/* 741 */                 jSONObject1 = JSONObject.parseObject(str7);
/* 742 */                 writeLog("###" + str5 + ":" + str7);
/* 743 */                 writeLog("###status:" + jSONObject1.get("status"));
/* 744 */                 String str = (String)jSONObject1.get("status");
/*     */                 
/* 746 */                 if ("noValidate".equals(str)) {
/* 747 */                   copyOnWriteArrayList1.add(str5);
/*     */                 }
/* 749 */                 if ("no".equals(str)) {
/* 750 */                   copyOnWriteArrayList2.add(str5);
/*     */                 }
/* 752 */               } catch (Exception exception) {
/* 753 */                 exception.printStackTrace();
/* 754 */                 copyOnWriteArrayList2.add(str5);
/*     */               }  continue;
/*     */             } 
/* 757 */             copyOnWriteArrayList2.add(str5);
/*     */             continue;
/*     */           } 
/* 760 */           copyOnWriteArrayList2.add(str5);
/*     */         } 
/*     */ 
/*     */ 
/*     */         
/* 765 */         if (copyOnWriteArrayList1.size() != 0 || copyOnWriteArrayList2.size() != 0) {
/* 766 */           jSONObject.put("status", "failure");
/* 767 */           if (copyOnWriteArrayList1.size() != 0) {
/* 768 */             jSONObject.put("noValNode", String.join(",", (Iterable)copyOnWriteArrayList1));
/*     */           } else {
/* 770 */             jSONObject.put("noValNode", "");
/*     */           } 
/* 772 */           if (copyOnWriteArrayList2.size() != 0) {
/* 773 */             jSONObject.put("errorNode", String.join(",", (Iterable)copyOnWriteArrayList2));
/*     */           } else {
/* 775 */             jSONObject.put("errorNode", "");
/*     */           } 
/*     */         } 
/*     */       } 
/*     */     } 
/* 780 */     return jSONObject;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONObject checkNode() {
/* 788 */     writeLog("###开始校验接口###");
/* 789 */     JSONObject jSONObject = new JSONObject();
/* 790 */     String str1 = "";
/* 791 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 792 */     CopyOnWriteArrayList<String> copyOnWriteArrayList1 = new CopyOnWriteArrayList();
/* 793 */     CopyOnWriteArrayList<String> copyOnWriteArrayList2 = new CopyOnWriteArrayList();
/*     */     
/* 795 */     Date date = new Date();
/* 796 */     SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
/* 797 */     String str2 = simpleDateFormat.format(date);
/* 798 */     RecordSet recordSet = new RecordSet();
/* 799 */     recordSet.executeQuery("select synnode from autoConfigKey", new Object[0]);
/* 800 */     if (recordSet.next()) {
/* 801 */       str1 = recordSet.getString("synnode");
/* 802 */       recordSet.executeUpdate("update autoConfigKey set time=? where 1=1", new Object[] { str2 });
/*     */     } else {
/* 804 */       recordSet.executeUpdate("insert into autoConfigKey(time) values (?)", new Object[] { str2 });
/*     */     } 
/* 806 */     if (str1 != null && !"".equals(str1)) {
/* 807 */       recordSet.executeQuery("select * from clustersetting", new Object[0]);
/* 808 */       while (recordSet.next()) {
/* 809 */         hashMap.put(recordSet.getString("httpaddress"), "");
/*     */       }
/*     */       
/* 812 */       if (hashMap != null) {
/*     */         
/* 814 */         MD5Coder mD5Coder = new MD5Coder();
/* 815 */         String str3 = "weaVer" + str2;
/* 816 */         String str4 = mD5Coder.getMD5ofStr(str3);
/*     */         
/* 818 */         Set set = hashMap.keySet();
/* 819 */         Iterator<String> iterator = set.iterator();
/* 820 */         while (iterator.hasNext()) {
/* 821 */           String str5 = iterator.next();
/* 822 */           String str6 = str5.trim() + "/join/UpgradeInter4Monitor.jsp";
/* 823 */           HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 824 */           hashMap1.put("token", str4);
/* 825 */           hashMap1.put("operation", "synconfigcheck");
/*     */           
/* 827 */           String str7 = SynNodeSender.getInstance().sync(str6, (Map)hashMap1);
/* 828 */           if (str7 != null) {
/* 829 */             if (str7.indexOf("status") > -1) {
/* 830 */               JSONObject jSONObject1 = null;
/*     */               try {
/* 832 */                 jSONObject1 = JSONObject.parseObject(str7);
/* 833 */                 writeLog("###校验" + str5 + ":" + str7);
/* 834 */                 writeLog("###校验status:" + jSONObject1.get("status"));
/* 835 */                 String str = (String)jSONObject1.get("status");
/*     */                 
/* 837 */                 if ("noValidate".equals(str)) {
/* 838 */                   copyOnWriteArrayList1.add(str5);
/*     */                 }
/* 840 */                 if ("no".equals(str)) {
/* 841 */                   copyOnWriteArrayList2.add(str5);
/*     */                 }
/* 843 */               } catch (Exception exception) {
/* 844 */                 exception.printStackTrace();
/* 845 */                 copyOnWriteArrayList2.add(str5);
/*     */               } 
/*     */               continue;
/*     */             } 
/* 849 */             copyOnWriteArrayList2.add(str5);
/*     */             
/*     */             continue;
/*     */           } 
/*     */           
/* 854 */           copyOnWriteArrayList2.add(str5);
/*     */         } 
/*     */ 
/*     */ 
/*     */         
/* 859 */         if (copyOnWriteArrayList1.size() != 0 || copyOnWriteArrayList2.size() != 0) {
/* 860 */           jSONObject.put("status", "failure");
/* 861 */           if (copyOnWriteArrayList1.size() != 0) {
/* 862 */             jSONObject.put("noValNode", String.join(",", (Iterable)copyOnWriteArrayList1));
/*     */           } else {
/* 864 */             jSONObject.put("noValNode", "");
/*     */           } 
/* 866 */           if (copyOnWriteArrayList2.size() != 0) {
/* 867 */             jSONObject.put("errorNode", String.join(",", (Iterable)copyOnWriteArrayList2));
/*     */           } else {
/* 869 */             jSONObject.put("errorNode", "");
/*     */           } 
/*     */         } else {
/*     */           
/* 873 */           jSONObject.put("status", "success");
/* 874 */           this.finishCheck = true;
/*     */         } 
/*     */       } else {
/*     */         
/* 878 */         jSONObject.put("status", "failure");
/*     */       } 
/*     */     } else {
/*     */       
/* 882 */       jSONObject.put("status", "success");
/* 883 */       this.finishCheck = true;
/*     */     } 
/*     */ 
/*     */     
/* 887 */     return jSONObject;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getPattern(String paramString) {
/* 896 */     String str1 = "<filter-name>(.*?)</filter-name>";
/* 897 */     String str2 = getElementType(paramString);
/* 898 */     if ("filter".equals(str2)) {
/* 899 */       str1 = "<filter-name>(.*?)</filter-name>";
/* 900 */     } else if ("servlet".equals(str2)) {
/* 901 */       str1 = "<servlet-name>(.*?)</servlet-name>";
/* 902 */     } else if ("listener".equals(str2)) {
/* 903 */       str1 = "<listener-class>(.*?)</listener-class>";
/* 904 */     } else if ("env-entry".equals(str2)) {
/* 905 */       str1 = "<env-entry-name>(.*?)</env-entry-name>";
/*     */     } 
/*     */     
/* 908 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String isFilterOrServlet(String paramString) {
/* 917 */     String str = "";
/* 918 */     if (paramString.indexOf("env-entry") > -1) {
/* 919 */       str = "env-entry";
/* 920 */     } else if (paramString.indexOf("listener-class") > -1) {
/* 921 */       str = "listener";
/* 922 */     } else if (paramString.indexOf("filter-name") > -1) {
/* 923 */       str = "filter";
/* 924 */     } else if (paramString.indexOf("servlet-name") > -1) {
/* 925 */       str = "servlet";
/*     */     } 
/* 927 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getElementType(String paramString) {
/* 936 */     return isFilterOrServlet(paramString);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/SynConfigOperation.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */