/*    */ package weaver.templetecheck;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.List;
/*    */ import java.util.Properties;
/*    */ import org.dom4j.Document;
/*    */ import org.dom4j.Element;
/*    */ import weaver.general.GCONST;
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ResinXmlConfigUtil
/*    */ {
/*    */   private static boolean isResin = false;
/* 16 */   private static ArrayList<HashMap<String, String>> resinXmlConfig = null;
/*    */ 
/*    */   
/*    */   public ResinXmlConfigUtil() {
/* 20 */     if (resinXmlConfig == null) {
/* 21 */       checkResin();
/* 22 */       if (!isResin) {
/* 23 */         getResinOnly();
/*    */       }
/*    */     } 
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void checkResin() {
/* 32 */     Properties properties = System.getProperties();
/* 33 */     String str = properties.getProperty("resin.home");
/* 34 */     if (str == null) {
/* 35 */       str = properties.getProperty("RESIN_HOME");
/*    */     }
/*    */     
/* 38 */     if (str != null && !"".equals(str)) {
/* 39 */       isResin = true;
/*    */     } else {
/* 41 */       isResin = false;
/*    */     } 
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void getResinOnly() {
/* 50 */     String str = GCONST.getRootPath() + "templetecheck/xml/resinonly.xml";
/* 51 */     ReadXml readXml = new ReadXml();
/* 52 */     Document document = readXml.read(str);
/*    */     
/* 54 */     resinXmlConfig = new ArrayList<>();
/* 55 */     if (document != null) {
/* 56 */       Element element = document.getRootElement();
/* 57 */       List list = element.elements();
/*    */       
/* 59 */       if (list != null) {
/* 60 */         for (Element element1 : list) {
/* 61 */           HashMap<Object, Object> hashMap = new HashMap<>();
/* 62 */           String str1 = element1.elementTextTrim("filepath");
/* 63 */           String str2 = element1.elementTextTrim("key");
/* 64 */           String str3 = element1.elementTextTrim("xpath");
/* 65 */           String str4 = element1.elementText("content");
/*    */           
/* 67 */           hashMap.put("filepath", str1);
/* 68 */           hashMap.put("key", str2);
/* 69 */           hashMap.put("xpath", str3);
/* 70 */           hashMap.put("content", str4);
/* 71 */           resinXmlConfig.add(hashMap);
/*    */         } 
/*    */       }
/*    */     } 
/*    */   }
/*    */ 
/*    */   
/*    */   public static boolean isResin() {
/* 79 */     return isResin;
/*    */   }
/*    */   
/*    */   public static void setIsResin(boolean paramBoolean) {
/* 83 */     isResin = paramBoolean;
/*    */   }
/*    */   
/*    */   public static ArrayList<HashMap<String, String>> getResinXmlConfig() {
/* 87 */     return resinXmlConfig;
/*    */   }
/*    */   
/*    */   public static void setResinXmlConfig(ArrayList<HashMap<String, String>> paramArrayList) {
/* 91 */     resinXmlConfig = paramArrayList;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/ResinXmlConfigUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */