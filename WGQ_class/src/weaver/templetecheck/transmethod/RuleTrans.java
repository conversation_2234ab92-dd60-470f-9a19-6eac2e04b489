/*     */ package weaver.templetecheck.transmethod;
/*     */ import java.io.File;
/*     */ import java.text.SimpleDateFormat;
/*     */ import java.util.ArrayList;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ public class RuleTrans {
/*  11 */   SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
/*     */   
/*     */   public static String changeStr(String paramString) {
/*  14 */     if (paramString == null) {
/*  15 */       return paramString;
/*     */     }
/*  17 */     paramString = paramString.replace("\\", "\\\\\\\\");
/*  18 */     paramString = paramString.replace("\n", "\\\\n");
/*  19 */     paramString = paramString.replace("\t", "\\\\t");
/*  20 */     paramString = paramString.replace("\"", "\\\\\"");
/*  21 */     paramString = paramString.replace("$", "\\$");
/*  22 */     paramString = paramString.replace("/", "\\/");
/*  23 */     return paramString;
/*     */   }
/*     */   
/*     */   public static String excludeRexChar(String paramString) {
/*  27 */     if (paramString == null) {
/*  28 */       return paramString;
/*     */     }
/*     */ 
/*     */     
/*  32 */     paramString = paramString.replace("(", "\\(");
/*  33 */     paramString = paramString.replace(")", "\\)");
/*  34 */     paramString = paramString.replace("*", "\\*");
/*  35 */     paramString = paramString.replace("+", "\\+");
/*  36 */     paramString = paramString.replace(".", "\\.");
/*  37 */     paramString = paramString.replace("[", "\\[");
/*  38 */     paramString = paramString.replace("?", "\\?");
/*  39 */     paramString = paramString.replace("^", "\\^");
/*  40 */     paramString = paramString.replace("{", "\\{");
/*  41 */     paramString = paramString.replace("|", "\\|");
/*  42 */     paramString = paramString.replace("!", "\\!");
/*  43 */     return paramString;
/*     */   }
/*     */   
/*     */   public static String changeStr2(String paramString) {
/*  47 */     if (paramString == null) {
/*  48 */       return paramString;
/*     */     }
/*     */     
/*  51 */     paramString = paramString.replaceAll("&lt;", "<");
/*  52 */     paramString = paramString.replaceAll("&gt;", ">");
/*     */     
/*  54 */     paramString = paramString.replaceAll("&nbsp;", " ");
/*     */     
/*  56 */     paramString = paramString.replaceAll("&quot;", "\"");
/*  57 */     paramString = paramString.replaceAll("&apos;", "'");
/*  58 */     return paramString;
/*     */   }
/*     */   
/*     */   public static String changeStr3(String paramString) {
/*  62 */     if (paramString != null) {
/*  63 */       paramString = paramString.replaceAll("&amp;", "&");
/*  64 */       paramString = paramString.replaceAll("&nbsp;", " ");
/*  65 */       paramString = paramString.replaceAll("&quot;", "\"");
/*  66 */       paramString = paramString.replaceAll("&apos;", "'");
/*  67 */       paramString = paramString.replaceAll("&124;", "\\|");
/*  68 */       paramString = paramString.replaceAll("\\^", "&#94;");
/*  69 */       paramString = paramString.replaceAll("\\\\n", "");
/*  70 */       paramString = paramString.replaceAll("<", "&lt;");
/*  71 */       paramString = paramString.replaceAll(">", "&gt;");
/*  72 */       paramString = paramString.replaceAll("\"", "&quot;");
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  84 */     return paramString;
/*     */   }
/*     */   
/*     */   public String getContentType(String paramString) {
/*  88 */     if (paramString == null) return ""; 
/*  89 */     if ("0".equals(paramString))
/*  90 */       return "Html"; 
/*  91 */     if ("1".equals(paramString))
/*  92 */       return "Sql" + SystemEnv.getHtmlLabelName(127495, ThreadVarLanguage.getLang()) + ""; 
/*  93 */     if ("2".equals(paramString)) {
/*  94 */       return "Java" + SystemEnv.getHtmlLabelName(83001, ThreadVarLanguage.getLang()) + "";
/*     */     }
/*  96 */     return "";
/*     */   }
/*     */   
/*     */   public String showType(String paramString1, String paramString2) {
/* 100 */     paramString2 = Util.null2String(paramString2);
/* 101 */     if ("mobilemodeskin".equalsIgnoreCase(paramString2))
/* 102 */       return paramString1 + "(" + SystemEnv.getHtmlLabelName(84213, ThreadVarLanguage.getLang()) + ")"; 
/* 103 */     if ("mobilemodepage".equalsIgnoreCase(paramString2))
/* 104 */       return paramString1 + "(" + SystemEnv.getHtmlLabelName(22967, ThreadVarLanguage.getLang()) + ")"; 
/* 105 */     if (paramString2.indexOf("mobileexpendcomponent") >= 0) {
/* 106 */       return paramString1 + "(" + SystemEnv.getHtmlLabelName(18700, ThreadVarLanguage.getLang()) + ")";
/*     */     }
/* 108 */     return paramString1;
/*     */   }
/*     */   
/*     */   public String getRuleType(String paramString) {
/* 112 */     if ("1".equals(paramString))
/* 113 */       return "Java" + SystemEnv.getHtmlLabelName(83001, ThreadVarLanguage.getLang()) + ""; 
/* 114 */     if ("0".equals(paramString)) {
/* 115 */       return "" + SystemEnv.getHtmlLabelName(10004233, ThreadVarLanguage.getLang()) + "";
/*     */     }
/* 117 */     return paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public ArrayList<String> getOperatesForWorkflowCheckResultDetail(String paramString1, String paramString2) {
/* 126 */     ArrayList<String> arrayList = new ArrayList();
/* 127 */     arrayList.add("true");
/* 128 */     if (paramString2 != null && !"".equals(paramString2) && paramString2.indexOf("无法自动替换") >= 0) {
/* 129 */       arrayList.add("false");
/*     */     } else {
/* 131 */       arrayList.add("true");
/*     */     } 
/* 133 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public ArrayList<String> getOperatesForWorkflowCheckResult(String paramString1, String paramString2) {
/* 140 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/* 141 */     String str1 = "";
/* 142 */     String str2 = "";
/* 143 */     if (arrayOfString.length == 2) {
/* 144 */       str1 = arrayOfString[0];
/* 145 */       str2 = arrayOfString[1];
/*     */     } 
/* 147 */     String str3 = " where 1=1 ";
/* 148 */     if (!"".equals(formatIds(str1))) {
/* 149 */       str3 = str3 + " and nodehtmllayoutid=" + formatIds(str1);
/*     */     }
/* 151 */     if (!"".equals(formatIds(str2))) {
/* 152 */       str3 = str3 + " and ruleid in(" + formatIds(str2) + ") ";
/*     */     }
/* 154 */     ArrayList<String> arrayList = new ArrayList();
/* 155 */     arrayList.add("true");
/* 156 */     RecordSet recordSet = new RecordSet();
/* 157 */     recordSet.execute("select replacecontent from upgradecheckworkflowresult" + str3);
/* 158 */     boolean bool = true;
/* 159 */     while (recordSet.next()) {
/* 160 */       String str = recordSet.getString("replacecontent");
/* 161 */       if (str.indexOf("无法自动替换") <= -1) {
/* 162 */         bool = false;
/*     */       }
/*     */     } 
/* 165 */     if (!bool) {
/* 166 */       arrayList.add("true");
/*     */     } else {
/* 168 */       arrayList.add("false");
/*     */     } 
/* 170 */     arrayList.add("true");
/* 171 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public ArrayList<String> getOperatesForMobileModeCheckResult(String paramString1, String paramString2) {
/* 178 */     ArrayList<String> arrayList = new ArrayList();
/* 179 */     arrayList.add("true");
/* 180 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/* 181 */     String str1 = "";
/* 182 */     String str2 = "";
/* 183 */     if (arrayOfString.length == 2) {
/* 184 */       str1 = arrayOfString[0];
/* 185 */       str2 = arrayOfString[1];
/*     */     } 
/* 187 */     if ("".equals(str1) || "".equals(str2)) {
/* 188 */       arrayList.add("false");
/* 189 */       arrayList.add("true");
/* 190 */       return arrayList;
/*     */     } 
/* 192 */     RecordSet recordSet = new RecordSet();
/* 193 */     String str3 = "false";
/* 194 */     recordSet.executeQuery("select replacecontent from checkmobilemoderesult where nodehtmllayoutid='" + str1 + "' and detailtype='" + str2 + "'", new Object[0]);
/* 195 */     while (recordSet.next()) {
/* 196 */       if (Util.null2String(recordSet.getString("replacecontent")).indexOf("无法自动替换") < 0) {
/* 197 */         str3 = "true";
/*     */         break;
/*     */       } 
/*     */     } 
/* 201 */     arrayList.add(str3);
/* 202 */     arrayList.add("true");
/* 203 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public ArrayList<String> getOperatesForMobileModeCheckResultDetail(String paramString1, String paramString2) {
/* 210 */     ArrayList<String> arrayList = new ArrayList();
/* 211 */     arrayList.add("true");
/* 212 */     if (paramString2 != null && !"".equals(paramString2) && paramString2.indexOf("无法自动替换") >= 0) {
/* 213 */       arrayList.add("false");
/*     */     } else {
/* 215 */       arrayList.add("true");
/*     */     } 
/* 217 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public ArrayList<String> getOperatesForModeCheckResult(String paramString1, String paramString2) {
/* 224 */     ArrayList<String> arrayList = new ArrayList();
/* 225 */     arrayList.add("true");
/* 226 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/* 227 */     String str1 = "";
/* 228 */     String str2 = "";
/* 229 */     if (arrayOfString.length == 2) {
/* 230 */       str1 = arrayOfString[0];
/* 231 */       str2 = arrayOfString[1];
/*     */     } 
/* 233 */     if ("".equals(str1) || "".equals(str2)) {
/* 234 */       arrayList.add("false");
/* 235 */       arrayList.add("true");
/* 236 */       return arrayList;
/*     */     } 
/* 238 */     RecordSet recordSet = new RecordSet();
/* 239 */     String str3 = "false";
/* 240 */     recordSet.executeQuery("select replacecontent from upgradecheckmoderesult where nodehtmllayoutid='" + str1 + "' and detailtype='" + str2 + "'", new Object[0]);
/* 241 */     while (recordSet.next()) {
/* 242 */       if (Util.null2String(recordSet.getString("replacecontent")).indexOf("无法自动替换") < 0) {
/* 243 */         str3 = "true";
/*     */         break;
/*     */       } 
/*     */     } 
/* 247 */     arrayList.add(str3);
/* 248 */     arrayList.add("true");
/* 249 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public ArrayList<String> getOperatesForModeCheckResultDetail(String paramString1, String paramString2) {
/* 256 */     ArrayList<String> arrayList = new ArrayList();
/* 257 */     arrayList.add("true");
/* 258 */     if (paramString2 != null && !"".equals(paramString2) && paramString2.indexOf("无法自动替换") >= 0) {
/* 259 */       arrayList.add("false");
/*     */     } else {
/* 261 */       arrayList.add("true");
/*     */     } 
/* 263 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public ArrayList<String> getOperatesForSpecialFileCheckResultDetail(String paramString1, String paramString2) {
/* 272 */     ArrayList<String> arrayList = new ArrayList();
/* 273 */     if (paramString2 != null && !"".equals(paramString2) && paramString2.indexOf("无法自动替换") >= 0) {
/* 274 */       arrayList.add("false");
/*     */     } else {
/* 276 */       arrayList.add("true");
/*     */     } 
/* 278 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public ArrayList<String> getOperatesForSpecialFileCheckResult(String paramString1, String paramString2) {
/* 288 */     ArrayList<String> arrayList = new ArrayList();
/* 289 */     if ("".equals(paramString2)) {
/* 290 */       arrayList.add("false");
/* 291 */       arrayList.add("false");
/* 292 */       return arrayList;
/*     */     } 
/* 294 */     RecordSet recordSet = new RecordSet();
/* 295 */     String str = "false";
/* 296 */     recordSet.executeQuery("select replacecontent from upgradecheckresult where filepath='" + paramString2 + "' ", new Object[0]);
/* 297 */     while (recordSet.next()) {
/* 298 */       if (Util.null2String(recordSet.getString("replacecontent")).indexOf("无法自动替换") < 0) {
/* 299 */         str = "true";
/*     */         break;
/*     */       } 
/*     */     } 
/* 303 */     arrayList.add(str);
/* 304 */     arrayList.add("true");
/* 305 */     return arrayList;
/*     */   }
/*     */   
/*     */   public String showResourceContent(String paramString1, String paramString2) {
/* 309 */     String str = Util.null2String(paramString2).trim();
/* 310 */     return "<a style=\"color:blue\" href=\"#\" onclick='showContentDetail(" + str + ")'>" + SystemEnv.getHtmlLabelName(10004234, ThreadVarLanguage.getLang()) + "</a>";
/*     */   }
/*     */ 
/*     */   
/*     */   public String showLastModifyTime(String paramString1, String paramString2) {
/* 315 */     String str1 = "";
/* 316 */     String str2 = Util.null2String(paramString2).trim();
/* 317 */     if (!"".equals(paramString1.trim())) {
/* 318 */       str1 = paramString1;
/*     */     }
/* 320 */     if (!"".equals(str2.trim())) {
/* 321 */       File file = new File(str2);
/* 322 */       if (file.exists()) {
/* 323 */         String str = this.dateFormat.format(Long.valueOf(file.lastModified()));
/* 324 */         if ("".equals(str1) || str1.compareTo(str) < 0) {
/* 325 */           str1 = str;
/*     */         }
/*     */       } 
/*     */     } 
/* 329 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String formatIds(String paramString) {
/* 336 */     String str = Util.null2String(paramString).toLowerCase().replaceAll(",+", ",");
/* 337 */     if (str.startsWith(",")) {
/* 338 */       str = str.substring(1);
/*     */     }
/* 340 */     if (str.endsWith(",")) {
/* 341 */       str = str.substring(0, str.length() - 1);
/*     */     }
/* 343 */     return str.trim();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/transmethod/RuleTrans.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */