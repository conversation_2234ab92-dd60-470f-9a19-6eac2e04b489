/*    */ package weaver.templetecheck.transmethod;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ModeTrans
/*    */ {
/*    */   public String changeStr2(String paramString) {
/* 10 */     if (paramString == null) {
/* 11 */       return paramString;
/*    */     }
/*    */     
/* 14 */     paramString = paramString.replaceAll("&lt;", "<");
/* 15 */     paramString = paramString.replaceAll("&gt;", ">");
/*    */     
/* 17 */     paramString = paramString.replaceAll("&nbsp;", " ");
/*    */     
/* 19 */     paramString = paramString.replaceAll("&quot;", "\"");
/* 20 */     paramString = paramString.replaceAll("&apos;", "'");
/* 21 */     return paramString;
/*    */   }
/*    */   
/*    */   public String changeStr3(String paramString) {
/* 25 */     if (paramString != null) {
/* 26 */       paramString = paramString.replaceAll("&amp;", "&");
/* 27 */       paramString = paramString.replaceAll("&nbsp;", " ");
/* 28 */       paramString = paramString.replaceAll("&quot;", "\"");
/* 29 */       paramString = paramString.replaceAll("&apos;", "'");
/* 30 */       paramString = paramString.replaceAll("&124;", "\\|");
/* 31 */       paramString = paramString.replaceAll("\\^", "&#94;");
/* 32 */       paramString = paramString.replaceAll("\\\\n", "");
/* 33 */       paramString = paramString.replaceAll("<", "&lt;");
/* 34 */       paramString = paramString.replaceAll(">", "&gt;");
/*    */     } 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */     
/* 46 */     return paramString;
/*    */   }
/*    */   private String changeStr4(String paramString) {
/* 49 */     if (paramString != null) {
/* 50 */       paramString = paramString.replace("<", "&lt;");
/* 51 */       paramString = paramString.replace(">", "&gt;");
/*    */     } 
/* 53 */     return paramString;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/transmethod/ModeTrans.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */