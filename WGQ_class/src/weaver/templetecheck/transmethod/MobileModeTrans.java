/*    */ package weaver.templetecheck.transmethod;
/*    */ 
/*    */ import weaver.general.Util;
/*    */ 
/*    */ 
/*    */ public class MobileModeTrans
/*    */ {
/*    */   public String changeStr2(String paramString) {
/*  9 */     if (paramString == null) {
/* 10 */       return paramString;
/*    */     }
/*    */     
/* 13 */     paramString = paramString.replaceAll("&lt;", "<");
/* 14 */     paramString = paramString.replaceAll("&gt;", ">");
/*    */     
/* 16 */     paramString = paramString.replaceAll("&nbsp;", " ");
/*    */     
/* 18 */     paramString = paramString.replaceAll("&quot;", "\"");
/* 19 */     paramString = paramString.replaceAll("&apos;", "'");
/* 20 */     return paramString;
/*    */   }
/*    */   
/*    */   public String changeStr3(String paramString) {
/* 24 */     if (paramString != null) {
/* 25 */       paramString = paramString.replaceAll("&amp;", "&");
/*    */       
/* 27 */       paramString = paramString.replaceAll("&nbsp;", " ");
/*    */       
/* 29 */       paramString = paramString.replaceAll("&quot;", "\"");
/* 30 */       paramString = paramString.replaceAll("&apos;", "'");
/* 31 */       paramString = paramString.replaceAll("&124;", "\\|");
/* 32 */       paramString = paramString.replaceAll("&94;", "\\^");
/* 33 */       paramString = paramString.replaceAll("\\\\n", "");
/* 34 */       paramString = paramString.replaceAll("<", "&lt;");
/* 35 */       paramString = paramString.replaceAll(">", "&gt;");
/*    */     } 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */     
/* 47 */     return paramString;
/*    */   }
/*    */   private String changeStr4(String paramString) {
/* 50 */     if (paramString != null) {
/* 51 */       paramString = paramString.replace("<", "&lt;");
/* 52 */       paramString = paramString.replace(">", "&gt;");
/*    */     } 
/* 54 */     return paramString;
/*    */   }
/*    */   
/*    */   public String showType(String paramString1, String paramString2) {
/* 58 */     paramString2 = Util.null2String(paramString2);
/* 59 */     if ("mobilemodeskin".equalsIgnoreCase(paramString2))
/* 60 */       return paramString1 + "(皮肤)"; 
/* 61 */     if ("mobilemodepage".equalsIgnoreCase(paramString2))
/* 62 */       return paramString1 + "(页面)"; 
/* 63 */     if (paramString2.indexOf("mobileexpendcomponent") >= 0) {
/* 64 */       return paramString1 + "(插件)";
/*    */     }
/* 66 */     return paramString1;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/transmethod/MobileModeTrans.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */