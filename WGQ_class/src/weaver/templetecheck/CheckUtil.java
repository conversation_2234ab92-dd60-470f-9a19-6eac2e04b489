/*      */ package weaver.templetecheck;
/*      */ 
/*      */ import java.io.File;
/*      */ import java.io.FileOutputStream;
/*      */ import java.util.ArrayList;
/*      */ import java.util.Collections;
/*      */ import java.util.Comparator;
/*      */ import java.util.HashMap;
/*      */ import java.util.Iterator;
/*      */ import java.util.LinkedHashMap;
/*      */ import java.util.List;
/*      */ import java.util.Map;
/*      */ import javax.servlet.http.HttpServletRequest;
/*      */ import javax.servlet.http.HttpServletResponse;
/*      */ import org.jdom.Content;
/*      */ import org.jdom.Document;
/*      */ import org.jdom.Element;
/*      */ import org.jdom.input.SAXBuilder;
/*      */ import org.jdom.output.Format;
/*      */ import org.jdom.output.XMLOutputter;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.general.BaseBean;
/*      */ import weaver.general.GCONST;
/*      */ import weaver.general.ThreadVarLanguage;
/*      */ import weaver.hrm.User;
/*      */ import weaver.systeminfo.SystemEnv;
/*      */ 
/*      */ public class CheckUtil
/*      */   extends BaseBean {
/*   30 */   FileUtil fileUtil = new FileUtil();
/*      */   
/*   32 */   String filename = GCONST.getRootPath() + "templetecheck" + File.separatorChar + "rule.xml";
/*   33 */   public String rulesid = "";
/*   34 */   public LinkedHashMap<String, LinkedHashMap<String, Rule>> pointArrayList = new LinkedHashMap<>();
/*      */ 
/*      */   
/*      */   public Element rootNodeElement;
/*      */ 
/*      */   
/*      */   public CheckUtil() {
/*   41 */     this("0");
/*      */   }
/*      */   
/*      */   public CheckUtil(String paramString) {
/*   45 */     SAXBuilder sAXBuilder = new SAXBuilder();
/*   46 */     File file = new File(this.fileUtil.getPath(this.filename));
/*   47 */     if (!file.canWrite()) {
/*   48 */       file.setWritable(true);
/*      */     }
/*      */     
/*      */     try {
/*   52 */       Document document = sAXBuilder.build(file);
/*      */       
/*   54 */       this.rootNodeElement = document.getRootElement();
/*   55 */     } catch (Exception exception) {
/*   56 */       exception.printStackTrace();
/*      */     } 
/*   58 */     init();
/*      */   }
/*      */ 
/*      */   
/*      */   public void init() {
/*   63 */     this.rulesid = this.rootNodeElement.getAttributeValue("id");
/*      */     
/*   65 */     List list = this.rootNodeElement.getChildren("tabtype");
/*      */     
/*   67 */     for (Element element : list) {
/*      */       
/*   69 */       String str = element.getAttributeValue("id");
/*      */ 
/*      */       
/*   72 */       List<Element> list1 = element.getChildren();
/*   73 */       LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/*   74 */       for (byte b = 0; b < list1.size(); b++) {
/*   75 */         Element element1 = list1.get(b);
/*   76 */         Rule rule = new Rule();
/*      */         
/*   78 */         String str1 = element1.getAttributeValue("id");
/*   79 */         String str2 = element1.getChildTextTrim("name");
/*   80 */         String str3 = element1.getChildTextTrim("content");
/*   81 */         String str4 = element1.getChildTextTrim("desc");
/*   82 */         String str5 = element1.getChildTextTrim("replacecontent");
/*   83 */         String str6 = element1.getChildTextTrim("xpath");
/*   84 */         String str7 = element1.getChildTextTrim("requisite");
/*   85 */         String str8 = element1.getChildTextTrim("ruletype");
/*      */ 
/*      */         
/*   88 */         String str9 = element1.getChildTextTrim("version");
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*   97 */         rule.setFlageid(str1);
/*   98 */         rule.setName(str2);
/*   99 */         rule.setDescription(str4);
/*  100 */         rule.setContent(str3);
/*  101 */         rule.setReplacecontent(str5);
/*  102 */         rule.setVersion(str9);
/*  103 */         rule.setXpath(str6);
/*  104 */         rule.setRequisite(str7);
/*  105 */         rule.setRuletype(str8);
/*      */         
/*  107 */         linkedHashMap.put(str1, rule);
/*      */       } 
/*  109 */       this.pointArrayList.put(str, linkedHashMap);
/*      */     } 
/*      */   }
/*      */ 
/*      */   
/*      */   public boolean changeTab(String paramString) {
/*  115 */     Document document = new Document();
/*  116 */     Element element = new Element("rules");
/*  117 */     element.setAttribute("id", "rule");
/*  118 */     element.setAttribute("version", "1.0.0");
/*      */ 
/*      */ 
/*      */     
/*  122 */     Iterator<Map.Entry> iterator = this.pointArrayList.entrySet().iterator();
/*      */     
/*  124 */     while (iterator.hasNext()) {
/*  125 */       Map.Entry entry = iterator.next();
/*  126 */       String str = (String)entry.getKey();
/*  127 */       if (paramString.indexOf(str) <= -1) {
/*      */         continue;
/*      */       }
/*  130 */       Element element1 = new Element("tabtype");
/*  131 */       element1.setAttribute("id", "" + str);
/*      */       
/*  133 */       LinkedHashMap linkedHashMap = (LinkedHashMap)entry.getValue();
/*  134 */       Iterator<Map.Entry> iterator1 = linkedHashMap.entrySet().iterator();
/*  135 */       while (iterator1.hasNext()) {
/*  136 */         Map.Entry entry1 = iterator1.next();
/*  137 */         Element element2 = new Element("rule");
/*  138 */         String str1 = (String)entry1.getKey();
/*  139 */         element2.setAttribute("id", str1);
/*  140 */         Rule rule = (Rule)entry1.getValue();
/*      */         
/*  142 */         if (null != rule) {
/*  143 */           String str2 = rule.getName();
/*  144 */           Element element3 = new Element("name");
/*  145 */           element3.addContent(str2);
/*  146 */           element2.addContent((Content)element3);
/*      */           
/*  148 */           String str3 = rule.getDescription();
/*  149 */           Element element4 = new Element("desc");
/*  150 */           element4.addContent(str3);
/*  151 */           element2.addContent((Content)element4);
/*      */           
/*  153 */           String str4 = rule.getContent();
/*  154 */           Element element5 = new Element("content");
/*  155 */           element5.addContent(str4);
/*  156 */           element2.addContent((Content)element5);
/*      */           
/*  158 */           String str5 = rule.getReplacecontent();
/*  159 */           Element element6 = new Element("replacecontent");
/*  160 */           element6.addContent(str5);
/*  161 */           element2.addContent((Content)element6);
/*      */           
/*  163 */           String str6 = rule.getVersion();
/*  164 */           Element element7 = new Element("version");
/*  165 */           element7.addContent(str6);
/*  166 */           element2.addContent((Content)element7);
/*      */           
/*  168 */           String str7 = rule.getXpath();
/*  169 */           Element element8 = new Element("xpath");
/*  170 */           element8.addContent(str7);
/*  171 */           element2.addContent((Content)element8);
/*      */           
/*  173 */           String str8 = rule.getRequisite();
/*  174 */           Element element9 = new Element("requisite");
/*  175 */           element9.addContent(str8);
/*  176 */           element2.addContent((Content)element9);
/*      */         } 
/*      */ 
/*      */         
/*  180 */         element1.addContent((Content)element2);
/*      */       } 
/*  182 */       element.addContent((Content)element1);
/*      */     } 
/*      */     
/*  185 */     document.addContent((Content)element);
/*      */ 
/*      */     
/*      */     try {
/*  189 */       String str = GCONST.XML_UTF8;
/*      */       
/*  191 */       Format format = Format.getCompactFormat();
/*  192 */       format.setEncoding(str);
/*  193 */       format.setIndent("    ");
/*  194 */       XMLOutputter xMLOutputter = new XMLOutputter(format);
/*  195 */       FileOutputStream fileOutputStream = new FileOutputStream(this.fileUtil.getPath(this.filename));
/*  196 */       xMLOutputter.output(document, fileOutputStream);
/*  197 */       fileOutputStream.flush();
/*  198 */       fileOutputStream.close();
/*      */     
/*      */     }
/*  201 */     catch (Exception exception) {
/*  202 */       exception.printStackTrace();
/*  203 */       return false;
/*      */     } 
/*  205 */     return true;
/*      */   }
/*      */   
/*      */   public boolean deleteRule(String paramString1, String paramString2) {
/*  209 */     Document document = new Document();
/*  210 */     Element element = new Element("rules");
/*  211 */     element.setAttribute("id", "rule");
/*  212 */     element.setAttribute("version", "1.0.0");
/*      */ 
/*      */ 
/*      */     
/*  216 */     Iterator<Map.Entry> iterator = this.pointArrayList.entrySet().iterator();
/*  217 */     while (iterator.hasNext()) {
/*  218 */       Map.Entry entry = iterator.next();
/*  219 */       String str = (String)entry.getKey();
/*  220 */       Element element1 = new Element("tabtype");
/*  221 */       element1.setAttribute("id", "" + str);
/*      */       
/*  223 */       LinkedHashMap linkedHashMap = (LinkedHashMap)entry.getValue();
/*  224 */       Iterator<Map.Entry> iterator1 = linkedHashMap.entrySet().iterator();
/*  225 */       while (iterator1.hasNext()) {
/*  226 */         Map.Entry entry1 = iterator1.next();
/*  227 */         Element element2 = new Element("rule");
/*  228 */         String str1 = (String)entry1.getKey();
/*  229 */         element2.setAttribute("id", str1);
/*  230 */         Rule rule = (Rule)entry1.getValue();
/*  231 */         if (!"".equals(paramString1) && paramString1.indexOf("," + str1 + ",") > -1 && paramString2.equals(str)) {
/*      */           continue;
/*      */         }
/*      */         
/*  235 */         if (null != rule) {
/*  236 */           String str2 = rule.getName();
/*  237 */           Element element3 = new Element("name");
/*  238 */           element3.addContent(str2);
/*  239 */           element2.addContent((Content)element3);
/*      */           
/*  241 */           String str3 = rule.getDescription();
/*  242 */           Element element4 = new Element("desc");
/*  243 */           element4.addContent(str3);
/*  244 */           element2.addContent((Content)element4);
/*      */           
/*  246 */           String str4 = rule.getContent();
/*  247 */           Element element5 = new Element("content");
/*  248 */           element5.addContent(str4);
/*  249 */           element2.addContent((Content)element5);
/*      */           
/*  251 */           String str5 = rule.getReplacecontent();
/*  252 */           Element element6 = new Element("replacecontent");
/*  253 */           element6.addContent(str5);
/*  254 */           element2.addContent((Content)element6);
/*      */           
/*  256 */           String str6 = rule.getVersion();
/*  257 */           Element element7 = new Element("version");
/*  258 */           element7.addContent(str6);
/*  259 */           element2.addContent((Content)element7);
/*      */           
/*  261 */           String str7 = rule.getXpath();
/*  262 */           Element element8 = new Element("xpath");
/*  263 */           element8.addContent(str7);
/*  264 */           element2.addContent((Content)element8);
/*      */           
/*  266 */           String str8 = rule.getRequisite();
/*  267 */           Element element9 = new Element("requisite");
/*  268 */           element9.addContent(str8);
/*  269 */           element2.addContent((Content)element9);
/*      */         } 
/*      */         
/*  272 */         element1.addContent((Content)element2);
/*      */       } 
/*  274 */       element.addContent((Content)element1);
/*      */     } 
/*      */     
/*  277 */     document.addContent((Content)element);
/*      */ 
/*      */     
/*      */     try {
/*  281 */       String str = GCONST.XML_UTF8;
/*      */       
/*  283 */       Format format = Format.getCompactFormat();
/*  284 */       format.setEncoding(str);
/*  285 */       format.setIndent("    ");
/*  286 */       XMLOutputter xMLOutputter = new XMLOutputter(format);
/*  287 */       FileOutputStream fileOutputStream = new FileOutputStream(this.fileUtil.getPath(this.filename));
/*  288 */       xMLOutputter.output(document, fileOutputStream);
/*  289 */       fileOutputStream.flush();
/*  290 */       fileOutputStream.close();
/*      */     }
/*  292 */     catch (Exception exception) {
/*  293 */       exception.printStackTrace();
/*  294 */       return false;
/*      */     } 
/*  296 */     return true;
/*      */   }
/*      */   
/*      */   public boolean updateRule(Rule paramRule, String paramString) {
/*  300 */     Document document = new Document();
/*  301 */     Element element = new Element("rules");
/*  302 */     element.setAttribute("id", "rule");
/*  303 */     element.setAttribute("version", "1.0.0");
/*      */     
/*  305 */     ArrayList arrayList = new ArrayList();
/*  306 */     String str = "";
/*  307 */     if (paramRule != null) {
/*  308 */       str = paramRule.getFlageid();
/*      */     }
/*      */     
/*  311 */     Iterator<Map.Entry> iterator = this.pointArrayList.entrySet().iterator();
/*      */     
/*  313 */     while (iterator.hasNext()) {
/*  314 */       Map.Entry entry = iterator.next();
/*  315 */       String str1 = (String)entry.getKey();
/*  316 */       Element element1 = new Element("tabtype");
/*  317 */       element1.setAttribute("id", "" + str1);
/*      */       
/*  319 */       LinkedHashMap linkedHashMap = (LinkedHashMap)entry.getValue();
/*  320 */       Iterator<Map.Entry> iterator1 = linkedHashMap.entrySet().iterator();
/*  321 */       while (iterator1.hasNext()) {
/*  322 */         Map.Entry entry1 = iterator1.next();
/*  323 */         Element element2 = new Element("rule");
/*  324 */         String str2 = (String)entry1.getKey();
/*  325 */         element2.setAttribute("id", str2);
/*      */         
/*  327 */         Rule rule = (Rule)entry1.getValue();
/*  328 */         if (!"".equals(str) && str.equals(str2) && str1.equals(paramString)) {
/*  329 */           rule = paramRule;
/*      */         }
/*      */         
/*  332 */         if (null != rule) {
/*  333 */           String str3 = rule.getName();
/*  334 */           Element element3 = new Element("name");
/*  335 */           element3.addContent(str3);
/*  336 */           element2.addContent((Content)element3);
/*      */           
/*  338 */           String str4 = rule.getDescription();
/*  339 */           Element element4 = new Element("desc");
/*  340 */           element4.addContent(str4);
/*  341 */           element2.addContent((Content)element4);
/*      */           
/*  343 */           String str5 = rule.getContent();
/*  344 */           Element element5 = new Element("content");
/*  345 */           element5.addContent(str5);
/*  346 */           element2.addContent((Content)element5);
/*      */           
/*  348 */           String str6 = rule.getReplacecontent();
/*  349 */           Element element6 = new Element("replacecontent");
/*  350 */           element6.addContent(str6);
/*  351 */           element2.addContent((Content)element6);
/*      */           
/*  353 */           String str7 = rule.getVersion();
/*  354 */           Element element7 = new Element("version");
/*  355 */           element7.addContent(str7);
/*  356 */           element2.addContent((Content)element7);
/*      */           
/*  358 */           String str8 = rule.getXpath();
/*  359 */           Element element8 = new Element("xpath");
/*  360 */           element8.addContent(str8);
/*  361 */           element2.addContent((Content)element8);
/*      */           
/*  363 */           String str9 = rule.getRequisite();
/*  364 */           Element element9 = new Element("requisite");
/*  365 */           element9.addContent(str9);
/*  366 */           element2.addContent((Content)element9);
/*      */           
/*  368 */           String str10 = rule.getRuletype();
/*  369 */           Element element10 = new Element("ruletype");
/*  370 */           element10.addContent(str10);
/*  371 */           element2.addContent((Content)element10);
/*      */         } 
/*      */         
/*  374 */         element1.addContent((Content)element2);
/*      */       } 
/*      */       
/*  377 */       element.addContent((Content)element1);
/*      */     } 
/*      */     
/*  380 */     document.addContent((Content)element);
/*      */ 
/*      */     
/*      */     try {
/*  384 */       String str1 = GCONST.XML_UTF8;
/*      */       
/*  386 */       Format format = Format.getCompactFormat();
/*  387 */       format.setEncoding(str1);
/*  388 */       format.setIndent("    ");
/*  389 */       XMLOutputter xMLOutputter = new XMLOutputter(format);
/*  390 */       FileOutputStream fileOutputStream = new FileOutputStream(this.fileUtil.getPath(this.filename));
/*  391 */       xMLOutputter.output(document, fileOutputStream);
/*  392 */       fileOutputStream.flush();
/*  393 */       fileOutputStream.close();
/*      */     }
/*  395 */     catch (Exception exception) {
/*  396 */       exception.printStackTrace();
/*  397 */       return false;
/*      */     } 
/*  399 */     return true;
/*      */   }
/*      */   
/*      */   public boolean saveRule(Rule paramRule, String paramString) {
/*  403 */     Document document = new Document();
/*  404 */     Element element = new Element("rules");
/*  405 */     element.setAttribute("id", "rule");
/*  406 */     element.setAttribute("version", "1.0.0");
/*  407 */     ArrayList arrayList = new ArrayList();
/*      */     
/*  409 */     Iterator<Map.Entry> iterator = this.pointArrayList.entrySet().iterator();
/*      */     
/*  411 */     while (iterator.hasNext()) {
/*  412 */       Map.Entry entry = iterator.next();
/*  413 */       String str = (String)entry.getKey();
/*  414 */       Element element1 = new Element("tabtype");
/*  415 */       element1.setAttribute("id", "" + str);
/*      */       
/*  417 */       LinkedHashMap linkedHashMap = (LinkedHashMap)entry.getValue();
/*  418 */       Iterator<Map.Entry> iterator1 = linkedHashMap.entrySet().iterator();
/*      */       
/*  420 */       while (iterator1.hasNext()) {
/*  421 */         Map.Entry entry1 = iterator1.next();
/*  422 */         Element element2 = new Element("rule");
/*  423 */         String str1 = (String)entry1.getKey();
/*  424 */         element2.setAttribute("id", str1);
/*      */         
/*  426 */         Rule rule = (Rule)entry1.getValue();
/*  427 */         if (null != rule) {
/*  428 */           String str2 = rule.getName();
/*  429 */           Element element3 = new Element("name");
/*  430 */           element3.addContent(str2);
/*  431 */           element2.addContent((Content)element3);
/*      */           
/*  433 */           String str3 = rule.getDescription();
/*  434 */           Element element4 = new Element("desc");
/*  435 */           element4.addContent(str3);
/*  436 */           element2.addContent((Content)element4);
/*      */           
/*  438 */           String str4 = rule.getContent();
/*  439 */           Element element5 = new Element("content");
/*  440 */           element5.addContent(str4);
/*  441 */           element2.addContent((Content)element5);
/*      */           
/*  443 */           String str5 = rule.getReplacecontent();
/*  444 */           Element element6 = new Element("replacecontent");
/*  445 */           element6.addContent(str5);
/*  446 */           element2.addContent((Content)element6);
/*      */           
/*  448 */           String str6 = rule.getVersion();
/*  449 */           Element element7 = new Element("version");
/*  450 */           element7.addContent(str6);
/*  451 */           element2.addContent((Content)element7);
/*      */           
/*  453 */           String str7 = rule.getXpath();
/*  454 */           Element element8 = new Element("xpath");
/*  455 */           element8.addContent(str7);
/*  456 */           element2.addContent((Content)element8);
/*      */           
/*  458 */           String str8 = rule.getRequisite();
/*  459 */           Element element9 = new Element("requisite");
/*  460 */           element9.addContent(str8);
/*  461 */           element2.addContent((Content)element9);
/*      */           
/*  463 */           String str9 = rule.getRuletype();
/*  464 */           Element element10 = new Element("ruletype");
/*  465 */           element10.addContent(str9);
/*  466 */           element2.addContent((Content)element10);
/*      */         } 
/*      */         
/*  469 */         element1.addContent((Content)element2);
/*      */       } 
/*      */ 
/*      */ 
/*      */       
/*  474 */       if (str.equals(paramString) && 
/*  475 */         paramRule != null) {
/*  476 */         String str1 = paramRule.getFlageid();
/*  477 */         if (linkedHashMap.containsKey(str1)) {
/*  478 */           return false;
/*      */         }
/*  480 */         Element element2 = new Element("rule");
/*  481 */         element2.setAttribute("id", "" + paramRule.getFlageid());
/*      */         
/*  483 */         String str2 = paramRule.getName();
/*  484 */         Element element3 = new Element("name");
/*  485 */         element3.addContent(str2);
/*  486 */         element2.addContent((Content)element3);
/*      */         
/*  488 */         String str3 = paramRule.getDescription();
/*  489 */         Element element4 = new Element("desc");
/*  490 */         element4.addContent(str3);
/*  491 */         element2.addContent((Content)element4);
/*      */         
/*  493 */         String str4 = paramRule.getContent();
/*  494 */         Element element5 = new Element("content");
/*  495 */         element5.addContent(str4);
/*  496 */         element2.addContent((Content)element5);
/*      */         
/*  498 */         String str5 = paramRule.getReplacecontent();
/*  499 */         Element element6 = new Element("replacecontent");
/*  500 */         element6.addContent(str5);
/*  501 */         element2.addContent((Content)element6);
/*      */         
/*  503 */         String str6 = paramRule.getVersion();
/*  504 */         Element element7 = new Element("version");
/*  505 */         element7.addContent(str6);
/*  506 */         element2.addContent((Content)element7);
/*      */         
/*  508 */         String str7 = paramRule.getXpath();
/*  509 */         Element element8 = new Element("xpath");
/*  510 */         element8.addContent(str7);
/*  511 */         element2.addContent((Content)element8);
/*      */ 
/*      */         
/*  514 */         String str8 = paramRule.getRequisite();
/*  515 */         Element element9 = new Element("requisite");
/*  516 */         element9.addContent(str8);
/*  517 */         element2.addContent((Content)element9);
/*      */         
/*  519 */         String str9 = paramRule.getRuletype();
/*  520 */         Element element10 = new Element("ruletype");
/*  521 */         element10.addContent(str9);
/*  522 */         element2.addContent((Content)element10);
/*      */         
/*  524 */         element1.addContent((Content)element2);
/*      */       } 
/*      */       
/*  527 */       element.addContent((Content)element1);
/*      */     } 
/*      */ 
/*      */     
/*  531 */     if (paramRule != null && !this.pointArrayList.containsKey(paramString)) {
/*  532 */       Element element1 = new Element("tabtype");
/*  533 */       element1.setAttribute("id", "" + paramString);
/*  534 */       Element element2 = new Element("rule");
/*  535 */       element2.setAttribute("id", "" + paramRule.getFlageid());
/*  536 */       String str1 = paramRule.getName();
/*  537 */       Element element3 = new Element("name");
/*  538 */       element3.addContent(str1);
/*  539 */       element2.addContent((Content)element3);
/*      */       
/*  541 */       String str2 = paramRule.getDescription();
/*  542 */       Element element4 = new Element("desc");
/*  543 */       element4.addContent(str2);
/*  544 */       element2.addContent((Content)element4);
/*      */       
/*  546 */       String str3 = paramRule.getContent();
/*  547 */       Element element5 = new Element("content");
/*  548 */       element5.addContent(str3);
/*  549 */       element2.addContent((Content)element5);
/*      */       
/*  551 */       String str4 = paramRule.getReplacecontent();
/*  552 */       Element element6 = new Element("replacecontent");
/*  553 */       element6.addContent(str4);
/*  554 */       element2.addContent((Content)element6);
/*      */       
/*  556 */       String str5 = paramRule.getVersion();
/*  557 */       Element element7 = new Element("version");
/*  558 */       element7.addContent(str5);
/*  559 */       element2.addContent((Content)element7);
/*      */       
/*  561 */       String str6 = paramRule.getXpath();
/*  562 */       Element element8 = new Element("xpath");
/*  563 */       element8.addContent(str6);
/*  564 */       element2.addContent((Content)element8);
/*      */       
/*  566 */       String str7 = paramRule.getRequisite();
/*  567 */       Element element9 = new Element("requisite");
/*  568 */       element9.addContent(str7);
/*  569 */       element2.addContent((Content)element9);
/*      */ 
/*      */       
/*  572 */       element1.addContent((Content)element2);
/*  573 */       element.addContent((Content)element1);
/*      */     } 
/*  575 */     document.addContent((Content)element);
/*      */     
/*      */     try {
/*  578 */       String str = GCONST.XML_UTF8;
/*      */       
/*  580 */       Format format = Format.getCompactFormat();
/*  581 */       format.setEncoding(str);
/*  582 */       format.setIndent("    ");
/*  583 */       XMLOutputter xMLOutputter = new XMLOutputter(format);
/*  584 */       FileOutputStream fileOutputStream = new FileOutputStream(this.fileUtil.getPath(this.filename));
/*  585 */       xMLOutputter.output(document, fileOutputStream);
/*  586 */       fileOutputStream.flush();
/*  587 */       fileOutputStream.close();
/*      */     }
/*  589 */     catch (Exception exception) {
/*  590 */       exception.printStackTrace();
/*  591 */       return false;
/*      */     } 
/*  593 */     return true;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void savepath(String paramString1, String paramString2) {}
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<Map<String, String>> getRulesByCondition(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  661 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  662 */     File file = new File(this.fileUtil.getPath(this.filename));
/*      */     
/*  664 */     if (!file.exists()) {
/*  665 */       return null;
/*      */     }
/*  667 */     String str1 = changeStr4(paramMap.get("name"));
/*  668 */     String str2 = changeStr4(paramMap.get("description"));
/*  669 */     String str3 = changeStr4(paramMap.get("content"));
/*  670 */     String str4 = changeStr4(paramMap.get("replacecontent"));
/*  671 */     String str5 = paramMap.get("tabtype");
/*  672 */     String str6 = paramMap.get("ruleid");
/*      */ 
/*      */     
/*  675 */     Iterator<Map.Entry> iterator = ruleFilter(this.pointArrayList).entrySet().iterator();
/*      */     
/*  677 */     while (iterator.hasNext()) {
/*  678 */       Map.Entry entry = iterator.next();
/*  679 */       String str = (String)entry.getKey();
/*  680 */       Element element = new Element("tabtype");
/*  681 */       element.setAttribute("id", "" + str);
/*      */       
/*  683 */       if (!str.equals(str5)) {
/*      */         continue;
/*      */       }
/*  686 */       LinkedHashMap linkedHashMap = (LinkedHashMap)entry.getValue();
/*  687 */       Iterator<Map.Entry> iterator1 = linkedHashMap.entrySet().iterator();
/*  688 */       while (iterator1.hasNext()) {
/*  689 */         Map.Entry entry1 = iterator1.next();
/*  690 */         String str7 = (String)entry1.getKey();
/*  691 */         if (str6 != null && !"".equals(str6) && !str7.equals(str6)) {
/*      */           continue;
/*      */         }
/*      */         
/*  695 */         Rule rule = (Rule)entry1.getValue();
/*  696 */         HashMap<Object, Object> hashMap = new HashMap<>();
/*  697 */         if (str1 != null && !"".equals(str1) && 
/*  698 */           rule.getName() != null) {
/*  699 */           String str11 = replaceStr2(rule.getName());
/*  700 */           int i = str11.toLowerCase().indexOf(str1.toLowerCase());
/*  701 */           if (i < 0) {
/*      */             continue;
/*      */           }
/*      */         } 
/*      */ 
/*      */         
/*  707 */         if (str2 != null && !"".equals(str2) && 
/*  708 */           rule.getDescription() != null) {
/*  709 */           String str11 = replaceStr2(rule.getDescription());
/*  710 */           int i = str11.toLowerCase().indexOf(str2.toLowerCase());
/*  711 */           if (i < 0) {
/*      */             continue;
/*      */           }
/*      */         } 
/*      */         
/*  716 */         if (str3 != null && !"".equals(str3) && 
/*  717 */           rule.getContent() != null) {
/*  718 */           String str11 = replaceStr2(rule.getContent());
/*  719 */           int i = str11.toLowerCase().indexOf(str3.toLowerCase());
/*  720 */           if (i < 0) {
/*      */             continue;
/*      */           }
/*      */         } 
/*      */ 
/*      */         
/*  726 */         if (str4 != null && !"".equals(str4) && 
/*  727 */           rule.getReplacecontent() != null) {
/*  728 */           String str11 = replaceStr2(rule.getReplacecontent());
/*  729 */           int i = str11.toLowerCase().indexOf(str4.toLowerCase());
/*  730 */           if (i < 0) {
/*      */             continue;
/*      */           }
/*      */         } 
/*      */ 
/*      */         
/*  736 */         String str8 = rule.getVersion();
/*  737 */         if (str8 != null && !"".equals(str8)) {
/*  738 */           boolean bool = checkVersion(str8);
/*  739 */           if (!bool) {
/*      */             continue;
/*      */           }
/*      */         } 
/*      */         
/*  744 */         hashMap.put("pointid", str7);
/*  745 */         hashMap.put("name", changeStr3(rule.getName()));
/*  746 */         hashMap.put("flageid", rule.getFlageid());
/*  747 */         hashMap.put("desc", changeStr3(rule.getDescription()));
/*  748 */         hashMap.put("content", changeStr3(rule.getContent()));
/*  749 */         hashMap.put("xpath", changeStr3(rule.getXpath()));
/*  750 */         hashMap.put("requisite", changeStr3(rule.getRequisite()));
/*  751 */         hashMap.put("ruletype", changeStr3(rule.getRuletype()));
/*      */         
/*  753 */         String str9 = changeStr3(rule.getReplacecontent());
/*  754 */         String str10 = changeStr3(rule.getRuletype());
/*      */         
/*  756 */         if ("".equals(str9)) {
/*  757 */           if (str10 == null || str10.equals("0")) {
/*  758 */             str9 = "" + SystemEnv.getHtmlLabelName(129782, ThreadVarLanguage.getLang()) + "";
/*  759 */           } else if (str10.equals("1")) {
/*  760 */             str9 = "" + SystemEnv.getHtmlLabelName(10004092, ThreadVarLanguage.getLang()) + "";
/*      */           } 
/*      */         }
/*  763 */         hashMap.put("replacecontent", str9);
/*  764 */         hashMap.put("version", rule.getVersion());
/*  765 */         arrayList.add(hashMap);
/*      */       } 
/*      */     } 
/*  768 */     if (arrayList.size() > 1) {
/*  769 */       sortByVersion((List)arrayList);
/*      */     }
/*      */     
/*  772 */     return (List)arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public List<Map<String, String>> getAllFileRules(String paramString1, String paramString2, String paramString3) {
/*  778 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  779 */     File file = new File(this.fileUtil.getPath(this.filename));
/*      */     
/*  781 */     if (!file.exists()) {
/*  782 */       return null;
/*      */     }
/*      */     
/*  785 */     Iterator<Map.Entry> iterator = ruleFilter(this.pointArrayList).entrySet().iterator();
/*      */     
/*  787 */     while (iterator.hasNext()) {
/*  788 */       Map.Entry entry = iterator.next();
/*  789 */       String str = (String)entry.getKey();
/*      */       
/*  791 */       Element element = new Element("tabtype");
/*  792 */       element.setAttribute("id", "" + str);
/*      */       
/*  794 */       if (!str.equals(paramString1)) {
/*      */         continue;
/*      */       }
/*      */       
/*  798 */       LinkedHashMap linkedHashMap = (LinkedHashMap)entry.getValue();
/*  799 */       Iterator<Map.Entry> iterator1 = linkedHashMap.entrySet().iterator();
/*  800 */       while (iterator1.hasNext()) {
/*  801 */         Map.Entry entry1 = iterator1.next();
/*  802 */         String str1 = (String)entry1.getKey();
/*  803 */         Rule rule = (Rule)entry1.getValue();
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  809 */         if (paramString2 != null && !"".equals(paramString2)) {
/*  810 */           paramString2 = paramString2.toLowerCase();
/*  811 */           if (rule.getName().toLowerCase().indexOf(paramString2) < 0) {
/*      */             continue;
/*      */           }
/*      */         } 
/*      */         
/*  816 */         if (paramString3 != null && !"".equals(paramString3)) {
/*  817 */           paramString3 = paramString3.toLowerCase();
/*  818 */           if (rule.getDescription().toLowerCase().indexOf(paramString3) < 0) {
/*      */             continue;
/*      */           }
/*      */         } 
/*      */         
/*  823 */         HashMap<Object, Object> hashMap = new HashMap<>();
/*  824 */         hashMap.put("pointid", str1);
/*  825 */         hashMap.put("name", rule.getName());
/*  826 */         hashMap.put("flageid", rule.getFlageid());
/*  827 */         hashMap.put("desc", rule.getDescription());
/*  828 */         hashMap.put("content", rule.getContent());
/*  829 */         hashMap.put("replacecontent", rule.getReplacecontent());
/*  830 */         hashMap.put("version", rule.getVersion());
/*  831 */         hashMap.put("xpath", rule.getXpath());
/*  832 */         hashMap.put("requisite", rule.getRequisite());
/*  833 */         if (rule.getRuletype() != null) {
/*  834 */           hashMap.put("ruletype", rule.getRuletype().equals("") ? "0" : rule.getRuletype());
/*      */         } else {
/*  836 */           hashMap.put("ruletype", "0");
/*      */         } 
/*      */         
/*  839 */         arrayList.add(hashMap);
/*      */       } 
/*      */     } 
/*      */     
/*  843 */     if (arrayList.size() > 1) {
/*  844 */       sortByVersion((List)arrayList);
/*      */     }
/*      */     
/*  847 */     return (List)arrayList;
/*      */   }
/*      */   
/*      */   public List<Map<String, String>> getRuleById(String paramString1, String paramString2, String paramString3, String paramString4) {
/*  851 */     LinkedHashMap linkedHashMap = ruleFilter(this.pointArrayList).get(paramString1);
/*  852 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  853 */     String[] arrayOfString = paramString2.split(",");
/*  854 */     for (byte b = 0; b < arrayOfString.length; b++) {
/*  855 */       String str1 = arrayOfString[b];
/*  856 */       if ("".equals(str1)) {
/*      */         continue;
/*      */       }
/*  859 */       Rule rule = (Rule)linkedHashMap.get(str1);
/*      */       
/*  861 */       if (paramString3 != null && !"".equals(paramString3)) {
/*  862 */         paramString3 = paramString3.toLowerCase();
/*  863 */         if (rule.getName().toLowerCase().indexOf(paramString3) < 0) {
/*      */           continue;
/*      */         }
/*      */       } 
/*      */       
/*  868 */       if (paramString4 != null && !"".equals(paramString4)) {
/*  869 */         paramString4 = paramString4.toLowerCase();
/*  870 */         if (rule.getDescription().toLowerCase().indexOf(paramString4) < 0) {
/*      */           continue;
/*      */         }
/*      */       } 
/*      */       
/*  875 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*  876 */       hashMap.put("pointid", rule.getFlageid());
/*  877 */       hashMap.put("name", rule.getName());
/*  878 */       hashMap.put("flageid", rule.getFlageid());
/*  879 */       hashMap.put("desc", rule.getDescription());
/*  880 */       hashMap.put("content", rule.getContent());
/*  881 */       hashMap.put("replacecontent", rule.getReplacecontent());
/*  882 */       hashMap.put("version", rule.getVersion());
/*  883 */       hashMap.put("xpath", rule.getXpath());
/*  884 */       hashMap.put("requisite", rule.getRequisite());
/*  885 */       String str2 = rule.getRuletype();
/*  886 */       if (str2 == null) {
/*  887 */         hashMap.put("ruletype", "0");
/*      */       } else {
/*  889 */         hashMap.put("ruletype", rule.getRuletype().equals("") ? "0" : rule.getRuletype());
/*      */       } 
/*      */ 
/*      */       
/*  893 */       arrayList.add(hashMap);
/*      */       continue;
/*      */     } 
/*  896 */     if (arrayList.size() > 1) {
/*  897 */       sortByVersion((List)arrayList);
/*      */     }
/*  899 */     return (List)arrayList;
/*      */   }
/*      */   
/*      */   public Rule getRuleObjById(String paramString1, String paramString2) {
/*  903 */     LinkedHashMap linkedHashMap = ruleFilter(this.pointArrayList).get(paramString1);
/*      */     
/*  905 */     return (Rule)linkedHashMap.get(paramString2);
/*      */   }
/*      */ 
/*      */   
/*      */   public String getworkhtmlcheck() {
/*  910 */     String str = "";
/*  911 */     List list = this.rootNodeElement.getChildren("workhtmlcheck");
/*  912 */     if (list.size() > 0) {
/*  913 */       str = this.rootNodeElement.getChildTextTrim("workhtmlcheck");
/*      */     }
/*  915 */     return str;
/*      */   }
/*      */   
/*      */   public String changeStr(String paramString) {
/*  919 */     if (paramString == null) {
/*  920 */       return paramString;
/*      */     }
/*  922 */     paramString = paramString.replaceAll("<", "&lt;");
/*  923 */     paramString = paramString.replaceAll(">", "&gt;");
/*      */     
/*  925 */     paramString = paramString.replaceAll("\"", "&quot;");
/*  926 */     paramString = paramString.replaceAll("'", "&apos;");
/*  927 */     paramString = paramString.replaceAll(" ", "\\ ");
/*  928 */     paramString = paramString.replaceAll("\n", "\\\\n");
/*  929 */     return paramString;
/*      */   }
/*      */   
/*      */   public String changeStr2(String paramString) {
/*  933 */     if (paramString == null) {
/*  934 */       return paramString;
/*      */     }
/*  936 */     paramString = paramString.replace("\\", "\\\\\\\\");
/*  937 */     paramString = paramString.replaceAll("&lt;", "<");
/*  938 */     paramString = paramString.replaceAll("&gt;", ">");
/*      */     
/*  940 */     paramString = paramString.replaceAll("&quot;", "\\\\\"");
/*  941 */     paramString = paramString.replaceAll("&apos;", "\\\\\\\\'");
/*  942 */     paramString = paramString.replaceAll("&nbsp;", " ");
/*  943 */     return paramString;
/*      */   }
/*      */   
/*      */   public String changeStr3(String paramString) {
/*  947 */     if (paramString == null) {
/*  948 */       return paramString;
/*      */     }
/*  950 */     paramString = paramString.replaceAll("\\|", "&#124;");
/*  951 */     paramString = paramString.replaceAll("\\^", "&#94;");
/*      */     
/*  953 */     paramString = paramString.replaceAll("&apos;", "'");
/*  954 */     paramString = paramString.replaceAll("&nbsp;", " ");
/*  955 */     paramString = paramString.replaceAll("\\\\n", "");
/*      */ 
/*      */ 
/*      */     
/*  959 */     return paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String changeStr4(String paramString) {
/*  967 */     return paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String replaceStr(String paramString) {
/*  977 */     if (paramString != null) {
/*  978 */       paramString = paramString.replace("<", "");
/*  979 */       paramString = paramString.replace(">", "");
/*  980 */       paramString = paramString.replace("\"", "");
/*  981 */       paramString = paramString.replace("'", "");
/*  982 */       paramString = paramString.replace("\n", "");
/*      */     } 
/*  984 */     return paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String replaceStr2(String paramString) {
/*  994 */     if (paramString != null) {
/*  995 */       paramString = paramString.replaceAll("&lt;", "");
/*  996 */       paramString = paramString.replaceAll("&gt;", "");
/*      */       
/*  998 */       paramString = paramString.replaceAll("&quot;", "");
/*  999 */       paramString = paramString.replaceAll("&apos;", "");
/* 1000 */       paramString = paramString.replaceAll("\\\\n", "");
/* 1001 */       paramString = paramString.replaceAll("&nbsp;", " ");
/*      */     } 
/* 1003 */     return paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public LinkedHashMap<String, LinkedHashMap<String, Rule>> ruleFilter(LinkedHashMap<String, LinkedHashMap<String, Rule>> paramLinkedHashMap) {
/* 1013 */     LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/* 1014 */     linkedHashMap = (LinkedHashMap<Object, Object>)paramLinkedHashMap.clone();
/* 1015 */     Iterator<Map.Entry> iterator = linkedHashMap.entrySet().iterator();
/* 1016 */     while (iterator.hasNext()) {
/* 1017 */       Map.Entry entry = iterator.next();
/* 1018 */       String str = (String)entry.getKey();
/*      */       
/* 1020 */       LinkedHashMap linkedHashMap1 = (LinkedHashMap)entry.getValue();
/* 1021 */       Iterator<Map.Entry> iterator1 = linkedHashMap1.entrySet().iterator();
/* 1022 */       while (iterator1.hasNext()) {
/* 1023 */         Map.Entry entry1 = iterator1.next();
/* 1024 */         Element element = new Element("rule");
/* 1025 */         String str1 = (String)entry1.getKey();
/* 1026 */         element.setAttribute("id", str1);
/*      */         
/* 1028 */         Rule rule = (Rule)entry1.getValue();
/* 1029 */         String str2 = rule.getVersion();
/* 1030 */         if (str2 != null && !"".equals(str2)) {
/* 1031 */           boolean bool = checkVersion(str2);
/* 1032 */           if (!bool) {
/* 1033 */             iterator1.remove();
/*      */           }
/*      */         } 
/*      */       } 
/*      */     } 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/* 1042 */     return (LinkedHashMap)linkedHashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean checkVersion(String paramString) {
/* 1053 */     boolean bool = true;
/* 1054 */     RecordSet recordSet = new RecordSet();
/* 1055 */     String str = "";
/* 1056 */     recordSet.execute("select cversion from license");
/* 1057 */     if (recordSet.next()) {
/* 1058 */       str = recordSet.getString("cversion");
/*      */     }
/*      */     
/* 1061 */     KBVersionCompare kBVersionCompare = new KBVersionCompare();
/* 1062 */     int i = kBVersionCompare.compareVersion(str, paramString);
/* 1063 */     if (i <= 0) {
/* 1064 */       bool = true;
/*      */     } else {
/* 1066 */       bool = false;
/*      */     } 
/*      */     
/* 1069 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void sortByVersion(List<Map<String, String>> paramList) {
/* 1079 */     if (paramList != null) {
/* 1080 */       final KBVersionCompare compare = new KBVersionCompare();
/*      */       
/* 1082 */       Collections.sort(paramList, new Comparator<Map<String, String>>()
/*      */           {
/*      */             public int compare(Map<String, String> param1Map1, Map<String, String> param1Map2) {
/* 1085 */               String str1 = param1Map1.get("version");
/* 1086 */               String str2 = param1Map2.get("version");
/* 1087 */               return compare.compareVersion(str1, str2);
/*      */             }
/*      */           });
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int compareVersion(String paramString1, String paramString2) {
/* 1101 */     if ((paramString1 == null || "".equals(paramString1)) && (paramString2 == null || "".equals(paramString2))) {
/* 1102 */       return 0;
/*      */     }
/*      */     
/* 1105 */     if (paramString1 == null || "".equals(paramString1)) {
/* 1106 */       return 1;
/*      */     }
/* 1108 */     if (paramString2 == null || "".equals(paramString2)) {
/* 1109 */       return -1;
/*      */     }
/*      */     
/* 1112 */     if (paramString1.indexOf("KB") > -1 && paramString2.indexOf("KB") > -1) {
/* 1113 */       if (paramString1.compareTo(paramString2) > 0)
/* 1114 */         return -1; 
/* 1115 */       if (paramString1.compareTo(paramString2) == 0) {
/* 1116 */         return 0;
/*      */       }
/* 1118 */       return 1;
/* 1119 */     }  if (paramString1.indexOf("KB") > -1 && paramString2.indexOf("KB") < 0)
/* 1120 */       return -1; 
/* 1121 */     if (paramString1.indexOf("KB") < 0 && paramString2.indexOf("KB") > -1) {
/* 1122 */       return 1;
/*      */     }
/* 1124 */     return 0;
/*      */   }
/*      */ 
/*      */   
/*      */   public class Rule
/*      */   {
/* 1130 */     String name = "";
/* 1131 */     String description = "";
/* 1132 */     String content = "";
/* 1133 */     String replacecontent = "";
/* 1134 */     String flageid = "";
/* 1135 */     String version = "";
/* 1136 */     String xpath = "";
/* 1137 */     String requisite = "";
/* 1138 */     String ruletype = "";
/*      */     
/*      */     public String getRuletype() {
/* 1141 */       return this.ruletype;
/*      */     }
/*      */     
/*      */     public void setRuletype(String param1String) {
/* 1145 */       this.ruletype = param1String;
/*      */     }
/*      */     
/*      */     public String getRequisite() {
/* 1149 */       return this.requisite;
/*      */     }
/*      */     
/*      */     public void setRequisite(String param1String) {
/* 1153 */       this.requisite = param1String;
/*      */     }
/*      */     
/*      */     public String getXpath() {
/* 1157 */       return this.xpath;
/*      */     }
/*      */     
/*      */     public void setXpath(String param1String) {
/* 1161 */       this.xpath = param1String;
/*      */     }
/*      */     
/*      */     public String getVersion() {
/* 1165 */       return this.version;
/*      */     }
/*      */     
/*      */     public void setVersion(String param1String) {
/* 1169 */       this.version = param1String;
/*      */     }
/*      */     
/*      */     public String getFlageid() {
/* 1173 */       return this.flageid;
/*      */     }
/*      */     
/*      */     public void setFlageid(String param1String) {
/* 1177 */       this.flageid = param1String;
/*      */     }
/*      */     
/*      */     public String getName() {
/* 1181 */       return this.name;
/*      */     }
/*      */     
/*      */     public void setName(String param1String) {
/* 1185 */       this.name = param1String;
/*      */     }
/*      */     
/*      */     public String getDescription() {
/* 1189 */       return this.description;
/*      */     }
/*      */     
/*      */     public void setDescription(String param1String) {
/* 1193 */       this.description = param1String;
/*      */     }
/*      */     
/*      */     public String getContent() {
/* 1197 */       return this.content;
/*      */     }
/*      */     
/*      */     public void setContent(String param1String) {
/* 1201 */       this.content = param1String;
/*      */     }
/*      */     
/*      */     public String getReplacecontent() {
/* 1205 */       return this.replacecontent;
/*      */     }
/*      */     
/*      */     public void setReplacecontent(String param1String) {
/* 1209 */       this.replacecontent = param1String;
/*      */     }
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/CheckUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */