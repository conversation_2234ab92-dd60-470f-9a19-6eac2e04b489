/*    */ package weaver.templetecheck;
/*    */ import java.io.File;
/*    */ import java.io.FileInputStream;
/*    */ import java.io.FileOutputStream;
/*    */ import java.io.IOException;
/*    */ import java.nio.channels.FileChannel;
/*    */ import java.text.SimpleDateFormat;
/*    */ import java.util.Date;
/*    */ import weaver.upgradetool.UpgradeFileUtil;
/*    */ 
/*    */ public class ConfigBakUtil {
/* 12 */   public static String target = GCONST.getRootPath() + "config_ubak\\";
/* 13 */   public static SimpleDateFormat yyyyMMdd = new SimpleDateFormat("yyyyMMdd");
/* 14 */   public static SimpleDateFormat HHmmss = new SimpleDateFormat("HHmmss");
/* 15 */   UpgradeFileUtil fileUtil = new UpgradeFileUtil();
/*    */   
/*    */   public void copyFile(String paramString1, String paramString2) {
/* 18 */     File file1 = new File(this.fileUtil.getPath(paramString1));
/* 19 */     if (!file1.exists()) {
/*    */       return;
/*    */     }
/* 22 */     String str = paramString2.replaceAll("/", "\\\\");
/*    */     
/* 24 */     File file2 = new File(this.fileUtil.getPath(str));
/* 25 */     if (file2.exists()) {
/*    */       return;
/*    */     }
/*    */     
/* 29 */     if (str.lastIndexOf("\\") > 0) {
/* 30 */       String str1 = str.substring(0, str.lastIndexOf("\\"));
/* 31 */       File file = new File(this.fileUtil.getPath(str1));
/* 32 */       if (!file.exists()) {
/* 33 */         file.mkdirs();
/*    */       }
/*    */     } else {
/*    */       return;
/*    */     } 
/* 38 */     FileInputStream fileInputStream = null;
/* 39 */     FileOutputStream fileOutputStream = null;
/* 40 */     FileChannel fileChannel1 = null;
/* 41 */     FileChannel fileChannel2 = null;
/*    */     try {
/* 43 */       fileInputStream = new FileInputStream(this.fileUtil.getPath(paramString1));
/* 44 */       File file = new File(this.fileUtil.getPath(paramString2));
/* 45 */       if (!file.exists()) {
/* 46 */         String str1 = file.getParent();
/* 47 */         File file3 = new File(str1);
/* 48 */         if (!file3.exists()) {
/* 49 */           file3.mkdirs();
/*    */         }
/* 51 */         file.createNewFile();
/*    */       } 
/*    */       
/* 54 */       fileOutputStream = new FileOutputStream(this.fileUtil.getPath(paramString2));
/* 55 */       fileChannel1 = fileInputStream.getChannel();
/* 56 */       fileChannel2 = fileOutputStream.getChannel();
/* 57 */       fileChannel1.transferTo(0L, fileChannel1.size(), fileChannel2);
/* 58 */     } catch (IOException iOException) {
/* 59 */       iOException.printStackTrace();
/*    */     } finally {
/*    */       try {
/* 62 */         fileInputStream.close();
/* 63 */         fileChannel1.close();
/* 64 */         fileOutputStream.close();
/* 65 */         fileChannel2.close();
/* 66 */       } catch (IOException iOException) {
/*    */         
/* 68 */         iOException.printStackTrace();
/*    */       } 
/*    */     } 
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String getBakRootPath() {
/* 77 */     String str1 = target;
/* 78 */     Date date = new Date();
/* 79 */     String str2 = yyyyMMdd.format(date);
/* 80 */     String str3 = HHmmss.format(date);
/* 81 */     str1 = target + str2 + "\\" + str3 + "\\ecology\\";
/* 82 */     return str1;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/ConfigBakUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */