/*     */ package weaver.templetecheck;
/*     */ import java.io.FileOutputStream;
/*     */ import java.io.IOException;
/*     */ import org.dom4j.Document;
/*     */ import org.dom4j.io.OutputFormat;
/*     */ import org.dom4j.io.XMLWriter;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.upgradetool.UpgradeFileUtil;
/*     */ 
/*     */ public class XmlFileOperation {
/*  14 */   ConfigBakUtil fileBakUtil = new ConfigBakUtil();
/*  15 */   UpgradeFileUtil fileUtil = new UpgradeFileUtil();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String updateXmlConfig(String paramString1, String paramString2) {
/*  25 */     String str1 = "";
/*  26 */     String str2 = "";
/*  27 */     String str3 = "";
/*  28 */     FileOutputStream fileOutputStream = null;
/*  29 */     XMLWriter xMLWriter = null;
/*  30 */     ReadXml readXml = new ReadXml();
/*  31 */     String str4 = "1".equals(paramString2) ? (SystemEnv.getHtmlLabelName(524939, ThreadVarLanguage.getLang()) + "") : (SystemEnv.getHtmlLabelName(524940, ThreadVarLanguage.getLang()) + "");
/*  32 */     String str5 = "1".equals(paramString2) ? (SystemEnv.getHtmlLabelName(524933, ThreadVarLanguage.getLang()) + "") : (SystemEnv.getHtmlLabelName(524934, ThreadVarLanguage.getLang()) + "");
/*     */     try {
/*  34 */       RecordSet recordSet = new RecordSet();
/*  35 */       String str6 = "select a.attrvalue,a.xpath,b.filepath from configXmlFile a,configFileManager b where b.isdelete=0 and a.configfileid = b.id and a.id=" + paramString1 + " and a.attrvalue is not null  order by b.filepath";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/*  41 */       recordSet.execute(str6);
/*  42 */       if (recordSet.next()) {
/*  43 */         str2 = recordSet.getString("attrvalue");
/*  44 */         str1 = recordSet.getString("filepath");
/*  45 */         str3 = recordSet.getString("xpath").replace("'", "'");
/*     */       } else {
/*  47 */         return "{\"status\":\"no\",\"msg\":\"" + str4 + "\"}";
/*     */       } 
/*     */       
/*  50 */       selectXmlNodeUtil selectXmlNodeUtil = new selectXmlNodeUtil(GCONST.getRootPath() + str1);
/*  51 */       Document document = null;
/*     */       try {
/*  53 */         if (document == null) {
/*  54 */           document = readXml.read(GCONST.getRootPath() + str1);
/*     */         }
/*  56 */       } catch (Exception exception) {
/*  57 */         exception.printStackTrace();
/*     */       } 
/*     */       
/*  60 */       String str7 = this.fileBakUtil.getBakRootPath() + str1;
/*  61 */       String str8 = GCONST.getRootPath() + str1;
/*  62 */       this.fileBakUtil.copyFile(str8, str7);
/*     */       
/*  64 */       String str9 = "";
/*  65 */       str2 = selectXmlNodeUtil.changeStr2(str2);
/*  66 */       if (paramString2.equals("2")) {
/*  67 */         str9 = selectXmlNodeUtil.deleteConfig(str2, GCONST.getRootPath() + str1, str3);
/*     */       } else {
/*  69 */         str9 = selectXmlNodeUtil.autoConfig(str3, document, str2);
/*  70 */         if (document != null) {
/*  71 */           fileOutputStream = new FileOutputStream(this.fileUtil.getPath(GCONST.getRootPath() + str1));
/*  72 */           OutputFormat outputFormat = OutputFormat.createPrettyPrint();
/*  73 */           String str = readXml.getFileEncode().equals("") ? "UTF-8" : readXml.getFileEncode();
/*  74 */           outputFormat.setEncoding(str);
/*  75 */           outputFormat.setIndent(true);
/*  76 */           outputFormat.setIndent("    ");
/*  77 */           xMLWriter = new XMLWriter(fileOutputStream, outputFormat);
/*  78 */           xMLWriter.write(document);
/*     */         } 
/*     */       } 
/*     */       
/*  82 */       if ("ok".equals(str9)) {
/*  83 */         return "{\"status\":\"ok\",\"msg\":\"" + str5 + "\"}";
/*     */       }
/*  85 */       return "{\"status\":\"no\",\"msg\":\"" + str4 + "\"}";
/*     */     }
/*  87 */     catch (Exception exception) {
/*  88 */       exception.printStackTrace();
/*  89 */       return "{\"status\":\"no\",\"msg\":\"" + str4 + "\"}";
/*     */     } finally {
/*     */       try {
/*  92 */         if (xMLWriter != null) {
/*  93 */           xMLWriter.flush();
/*     */         }
/*  95 */         if (fileOutputStream != null) {
/*  96 */           fileOutputStream.flush();
/*     */         }
/*  98 */         if (xMLWriter != null) {
/*  99 */           xMLWriter.close();
/*     */         }
/* 101 */         if (fileOutputStream != null) {
/* 102 */           fileOutputStream.close();
/*     */         }
/* 104 */       } catch (IOException iOException) {
/* 105 */         iOException.printStackTrace();
/*     */       } 
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/XmlFileOperation.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */