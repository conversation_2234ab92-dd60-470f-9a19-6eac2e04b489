/*     */ package weaver.templetecheck;
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import java.io.File;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.HashSet;
/*     */ import java.util.Iterator;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import net.sf.json.JSONArray;
/*     */ import org.dom4j.Document;
/*     */ import org.dom4j.Element;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.upgradetool.UpgradeFileUtil;
/*     */ 
/*     */ public class SystemAutoConfig {
/*  21 */   private final Logger logger = Logger.getLogger(getClass().getName());
/*  22 */   CheckConfigFile checkConfigFile = new CheckConfigFile();
/*  23 */   ConfigOperation configOperation = new ConfigOperation();
/*  24 */   PropertiesFileOperation propOperation = new PropertiesFileOperation();
/*  25 */   XmlFileOperation xmlOperation = new XmlFileOperation();
/*  26 */   private String isSyn = "";
/*     */ 
/*     */   
/*     */   public String systemAutoConfig(String paramString1, String paramString2, String paramString3) {
/*  30 */     this.isSyn = paramString3;
/*  31 */     return systemAutoConfig(paramString1, paramString2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String systemAutoConfig(String paramString1, String paramString2) {
/*  38 */     BaseBean baseBean = new BaseBean();
/*     */ 
/*     */     
/*  41 */     ResinXmlConfigUtil resinXmlConfigUtil = new ResinXmlConfigUtil();
/*  42 */     boolean bool = ResinXmlConfigUtil.isResin();
/*     */     
/*  44 */     ConfigSpecialHandle configSpecialHandle = new ConfigSpecialHandle();
/*  45 */     if (!bool) {
/*  46 */       baseBean.writeLog("###非resin中间件调整配置。###");
/*  47 */       configSpecialHandle.adjustConfig();
/*     */     } 
/*     */     
/*  50 */     configSpecialHandle.deleteXMLConfig_DB();
/*     */     
/*  52 */     ConfigOperation configOperation = new ConfigOperation();
/*  53 */     ArrayList<Map<String, String>> arrayList = new ArrayList();
/*  54 */     UpgradeFileUtil upgradeFileUtil = new UpgradeFileUtil();
/*     */     
/*  56 */     String str1 = this.checkConfigFile.getCurrentUsedFileIds();
/*  57 */     String str2 = " a.isdelete=0 ";
/*  58 */     String str3 = "";
/*     */     
/*  60 */     if ("1".equals(paramString1)) {
/*  61 */       if (!str1.equals("")) {
/*  62 */         str2 = str2 + " and a.id in(" + str1 + ") ";
/*     */       }
/*  64 */       String str = "select a.id from configFileManager a  left join CustomerKBVersion b on a.kbversion = b.name where " + str2;
/*     */       
/*  66 */       RecordSet recordSet = new RecordSet();
/*  67 */       recordSet.execute(str);
/*  68 */       while (recordSet.next()) {
/*  69 */         str3 = str3 + Util.null2String(recordSet.getString("id")) + ",";
/*     */       }
/*     */       
/*  72 */       if (str3.equals("")) {
/*  73 */         baseBean.writeLog("weaver.templetecheck.SystemAutoConfig 无法获取记录的id");
/*  74 */         return JSONArray.fromObject(arrayList).toString();
/*     */       }
/*     */     
/*  77 */     } else if ("2".equals(paramString1)) {
/*  78 */       str3 = paramString2;
/*     */     } 
/*     */     
/*  81 */     if (str3.endsWith(",")) {
/*  82 */       str3 = str3.substring(0, str3.length() - 1);
/*     */     }
/*  84 */     baseBean.writeLog("###checkids:#" + str3);
/*     */     
/*  86 */     if ((str3.split(",")).length == 1) {
/*  87 */       RecordSet recordSet = new RecordSet();
/*  88 */       String str = "select filepath from configFileManager where isdelete=0 and id =" + str3;
/*  89 */       recordSet.execute(str);
/*  90 */       if (recordSet.next()) {
/*  91 */         String str6 = GCONST.getRootPath() + recordSet.getString("filepath");
/*  92 */         File file = new File(upgradeFileUtil.getPath(str6));
/*  93 */         if (!file.exists()) {
/*     */           
/*  95 */           getAutoConfigInfo(arrayList, str3, "2");
/*  96 */           this.logger.info("weaver.templetecheck.SystemAutoConfig 一键配置失败，本地找不到该文件");
/*  97 */           return JSONArray.fromObject(arrayList).toString();
/*     */         } 
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 103 */     String str4 = "";
/* 104 */     HashSet<String> hashSet = new HashSet();
/* 105 */     String str5 = configOperation.oneKeyConfig(str3, hashSet);
/* 106 */     boolean bool1 = false;
/*     */     
/* 108 */     if (hashSet.contains("web.xml")) {
/* 109 */       bool1 = true;
/*     */     }
/* 111 */     if (str5 == null || str5.equals("")) {
/* 112 */       if (bool1) {
/* 113 */         str4 = "{\"modifyxml\":\"y\",\"status\":\"ok\",\"msg\":\"" + SystemEnv.getHtmlLabelName(10004230, ThreadVarLanguage.getLang()) + "\"}";
/*     */       } else {
/* 115 */         str4 = "{\"status\":\"ok\",\"msg\":\"" + SystemEnv.getHtmlLabelName(10004230, ThreadVarLanguage.getLang()) + "\"}";
/*     */       }
/*     */     
/* 118 */     } else if (str3.indexOf(",") != -1) {
/* 119 */       if (bool1) {
/* 120 */         str4 = "{\"modifyxml\":\"y\",\"status\":\"no\",\"msg\":\"" + SystemEnv.getHtmlLabelName(524950, ThreadVarLanguage.getLang()) + (str5.split(",")).length + SystemEnv.getHtmlLabelName(524952, ThreadVarLanguage.getLang()) + "\\\"" + SystemEnv.getHtmlLabelName(524953, ThreadVarLanguage.getLang()) + "\\\"" + SystemEnv.getHtmlLabelName(10004232, ThreadVarLanguage.getLang()) + "\\n" + str5.replace(",", "<br>") + "\"}";
/*     */       } else {
/* 122 */         str4 = "{\"status\":\"no\",\"msg\":\"" + SystemEnv.getHtmlLabelName(524950, ThreadVarLanguage.getLang()) + (str5.split(",")).length + SystemEnv.getHtmlLabelName(524952, ThreadVarLanguage.getLang()) + "\\\"" + SystemEnv.getHtmlLabelName(524953, ThreadVarLanguage.getLang()) + "\\\"" + SystemEnv.getHtmlLabelName(10004232, ThreadVarLanguage.getLang()) + "\\n" + str5.replace(",", "<br>") + "\"}";
/*     */       }
/*     */     
/*     */     }
/* 126 */     else if (bool1) {
/* 127 */       str4 = "{\"modifyxml\":\"y\",\"status\":\"no\",\"msg\":\"" + SystemEnv.getHtmlLabelName(10004231, ThreadVarLanguage.getLang()) + "\\\"" + SystemEnv.getHtmlLabelName(502218, ThreadVarLanguage.getLang()) + "\\\"" + SystemEnv.getHtmlLabelName(10004232, ThreadVarLanguage.getLang()) + "\"}";
/*     */     } else {
/* 129 */       str4 = "{\"status\":\"no\",\"msg\":\"" + SystemEnv.getHtmlLabelName(10004231, ThreadVarLanguage.getLang()) + "\\\"" + SystemEnv.getHtmlLabelName(502218, ThreadVarLanguage.getLang()) + "\\\"" + SystemEnv.getHtmlLabelName(10004232, ThreadVarLanguage.getLang()) + "\"}";
/*     */     } 
/*     */ 
/*     */     
/* 133 */     return str4;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getSystemConfigMain(String paramString1, String paramString2) {
/* 138 */     ArrayList arrayList = new ArrayList();
/* 139 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 140 */     hashMap.put("pageSize", paramString1);
/* 141 */     hashMap.put("pageIndex", paramString2);
/* 142 */     List<Map<String, String>> list = this.configOperation.getConfigFileList(null, (Map)hashMap, null, null);
/* 143 */     return JSONArray.fromObject(list).toString();
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSystemConfigDetail(String paramString) {
/* 149 */     ArrayList arrayList = new ArrayList();
/* 150 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 151 */     hashMap.put("ids", paramString);
/* 152 */     List<Map<String, String>> list = this.checkConfigFile.getMatchResult(null, (Map)hashMap, null, null);
/* 153 */     return JSONArray.fromObject(list).toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String updatePropConfig(String paramString1, String paramString2) {
/* 164 */     return this.propOperation.updatePropConfig(paramString1, paramString2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String updateXmlConfig(String paramString1, String paramString2) {
/* 174 */     return this.xmlOperation.updateXmlConfig(paramString1, paramString2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List<Map<String, String>> getAutoConfigInfo(List<Map<String, String>> paramList, String paramString1, String paramString2) {
/* 186 */     if (paramList == null) {
/* 187 */       return new ArrayList<>();
/*     */     }
/* 189 */     if (paramString1 == null || paramString1.equals("")) {
/* 190 */       return paramList;
/*     */     }
/* 192 */     if (paramString2 == null || paramString2.equals("")) {
/* 193 */       return paramList;
/*     */     }
/* 195 */     String[] arrayOfString = paramString1.split(",");
/* 196 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 197 */     for (String str1 : arrayOfString) {
/* 198 */       hashMap.clear();
/* 199 */       String str2 = paramString2.equals("1") ? (SystemEnv.getHtmlLabelName(10004085, ThreadVarLanguage.getLang()) + "") : (paramString2.equals("2") ? (SystemEnv.getHtmlLabelName(129337, ThreadVarLanguage.getLang()) + "") : (SystemEnv.getHtmlLabelName(524963, ThreadVarLanguage.getLang()) + ""));
/* 200 */       hashMap.put(str1, str2);
/* 201 */       paramList.add(hashMap);
/*     */     } 
/*     */     
/* 204 */     return paramList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String configByMainIds(String paramString) {
/* 212 */     String str1 = this.configOperation.oneKeyConfig(paramString, null);
/* 213 */     JSONObject jSONObject = new JSONObject();
/* 214 */     String str2 = "";
/* 215 */     if (str1 == null || str1.equals("")) {
/* 216 */       str2 = "{\"status\":\"ok\"}";
/*     */     } else {
/* 218 */       str2 = "{\"status\":\"no\"}";
/*     */     } 
/* 220 */     this.logger.info("###jsonStr:" + str2);
/* 221 */     jSONObject = JSONObject.parseObject(str2);
/* 222 */     if ("ok".equals(jSONObject.get("status"))) {
/* 223 */       deleteXMLConfig(paramString);
/*     */     }
/*     */ 
/*     */     
/* 227 */     return str2;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String synConfig(String paramString1, String paramString2) {
/* 237 */     RecordSet recordSet = new RecordSet();
/* 238 */     recordSet.executeQuery("select * from configXmlFile a,configFileManager b where a.configfileid = b.id and a.isdelete='0' and b.id in (" + paramString1 + ") ", new Object[0]);
/* 239 */     while (recordSet.next()) {
/* 240 */       String str = recordSet.getString("attrnotes");
/*     */     }
/*     */ 
/*     */     
/* 244 */     if ("jc".equals(paramString2)) {
/*     */       
/* 246 */       BaseBean baseBean = new BaseBean();
/* 247 */       baseBean.writeLog("开始配置本地文件--fromtype：" + paramString2);
/* 248 */       XMLUtil xMLUtil = new XMLUtil();
/* 249 */       ReadXml readXml = new ReadXml();
/* 250 */       UpgradeFileUtil upgradeFileUtil = new UpgradeFileUtil();
/* 251 */       String str1 = GCONST.getRootPath() + "WEB-INF" + File.separatorChar + "web.xml";
/* 252 */       Document document1 = readXml.read(str1);
/* 253 */       Document document2 = (Document)document1.clone();
/*     */       
/* 255 */       baseBean.writeLog("###开始配置本地文件--删除开始");
/* 256 */       deleteXMLConfig(paramString1, document2);
/* 257 */       baseBean.writeLog("###开始配置本地文件--删除结束");
/* 258 */       selectXmlNodeUtil selectXmlNodeUtil = new selectXmlNodeUtil();
/* 259 */       Element element = document2.getRootElement();
/* 260 */       List list = element.elements();
/* 261 */       List<Element> list1 = element.elements();
/* 262 */       StringBuffer stringBuffer1 = new StringBuffer();
/* 263 */       recordSet.executeQuery("select * from configXmlFile a,configFileManager b where a.configfileid = b.id and a.isdelete='0' and b.id in (" + paramString1 + ") ", new Object[0]);
/* 264 */       while (recordSet.next()) {
/* 265 */         String str3 = recordSet.getString("xpath");
/* 266 */         String str4 = recordSet.getString("attrvalue");
/* 267 */         stringBuffer1.append(str4);
/* 268 */         stringBuffer1.append("\r\n");
/* 269 */         String str5 = Util.null2String(recordSet.getString("attrnotes"));
/* 270 */         baseBean.writeLog("###开始配置本地文件--condition：" + str5);
/*     */ 
/*     */         
/* 273 */         Document document = xMLUtil.Str2Document("<myroot>" + str4 + "</myroot>");
/* 274 */         Element element1 = document.getRootElement();
/* 275 */         List<Element> list2 = element1.elements();
/* 276 */         for (byte b = 0; b < list2.size(); b++) {
/* 277 */           String str = xMLUtil.generateXPathByContent(((Element)list2.get(b)).asXML());
/* 278 */           selectXmlNodeUtil.deleteConfig(document2, ((Element)list2.get(b)).asXML(), str1, str);
/* 279 */           baseBean.writeLog("###先删除本地的配置：" + str);
/*     */         } 
/* 281 */         baseBean.writeLog("###先删除本地的配置");
/* 282 */         element = document2.getRootElement();
/* 283 */         list = element.elements();
/* 284 */         list1 = element.elements();
/*     */         
/* 286 */         JSONObject jSONObject = JSONObject.parseObject(str5);
/* 287 */         boolean bool1 = false;
/* 288 */         int i = list1.size();
/* 289 */         if (jSONObject != null) {
/* 290 */           Set set = jSONObject.keySet();
/* 291 */           String str6 = xMLUtil.getElementName(((Element)list2.get(0)).asXML());
/* 292 */           String str7 = (String)jSONObject.get(str6);
/*     */           
/* 294 */           String[] arrayOfString = str7.split(",");
/*     */           
/* 296 */           for (byte b1 = 0; b1 < arrayOfString.length; b1++) {
/* 297 */             String str8 = arrayOfString[b1];
/* 298 */             String str9 = ((str8.split("@")).length > 1) ? str8.split("@")[1] : "";
/* 299 */             for (int j = list1.size() - 1; j > 0; j--) {
/* 300 */               Element element2 = list1.get(j);
/*     */ 
/*     */ 
/*     */               
/* 304 */               if (element2.asXML().indexOf(str9) > -1) {
/*     */                 
/* 306 */                 if (list1.size() == i || i < j) {
/* 307 */                   i = j + 1;
/*     */                 }
/* 309 */                 bool1 = true;
/*     */                 
/*     */                 break;
/*     */               } 
/*     */             } 
/* 314 */             if (bool1) {
/*     */               break;
/*     */             }
/*     */           } 
/*     */         } 
/*     */ 
/*     */ 
/*     */         
/* 322 */         if (!bool1) {
/* 323 */           i = 0;
/*     */         }
/*     */         
/* 326 */         baseBean.writeLog("开始配置本地文件--insertIndex：" + i);
/* 327 */         baseBean.writeLog("开始配置本地文件--elementList.size()：" + list1.size());
/*     */         
/* 329 */         if (i > -1) {
/* 330 */           for (byte b1 = 0; b1 < list2.size(); b1++) {
/* 331 */             baseBean.writeLog("开始配置本地文件--插入：" + (i + b1) + "===" + ((Element)list2.get(b1)).asXML());
/* 332 */             list1.add(i + b1, ((Element)list2.get(b1)).createCopy());
/*     */           } 
/*     */         }
/*     */       } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 349 */       StringBuffer stringBuffer2 = new StringBuffer();
/*     */       
/* 351 */       Iterator<Element> iterator = list1.iterator();
/* 352 */       while (iterator.hasNext()) {
/* 353 */         Element element1 = iterator.next();
/* 354 */         if (!"".equals(stringBuffer2.toString()) && stringBuffer2.toString().contains(element1.asXML())) {
/* 355 */           iterator.remove();
/*     */         }
/* 357 */         stringBuffer2.append(element1.asXML());
/* 358 */         stringBuffer2.append("\r\n");
/*     */       } 
/*     */ 
/*     */       
/* 362 */       String str2 = "";
/* 363 */       boolean bool = selectXmlNodeUtil.checkXmlFile(document2.asXML(), stringBuffer1.toString());
/* 364 */       if (bool) {
/* 365 */         str2 = "{\"status\":\"ok\"}";
/*     */       } else {
/* 367 */         str2 = "{\"status\":\"no\"}";
/*     */       } 
/*     */       
/* 370 */       selectXmlNodeUtil.writeXml(str1, document2);
/*     */       
/* 372 */       return str2;
/*     */     } 
/* 374 */     return configByMainIds(paramString1);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void generateOrderRule(String paramString) {
/* 387 */     PropertiesUtil propertiesUtil = new PropertiesUtil();
/* 388 */     String str = GCONST.getRootPath() + "templetecheck" + File.separatorChar + "xml" + File.separatorChar + "condition.properties";
/* 389 */     JSONObject jSONObject = new JSONObject();
/* 390 */     if (paramString != null && !"".equals(paramString)) {
/* 391 */       jSONObject = JSONObject.parseObject(paramString);
/*     */     }
/* 393 */     Set set = jSONObject.entrySet();
/* 394 */     Iterator<Map.Entry> iterator = set.iterator();
/* 395 */     propertiesUtil.load(str);
/* 396 */     while (iterator.hasNext()) {
/* 397 */       Map.Entry entry = iterator.next();
/*     */       
/* 399 */       propertiesUtil.put((String)entry.getKey(), (String)entry.getValue());
/*     */     } 
/*     */     
/* 402 */     propertiesUtil.store(str);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void deleteXMLConfig(String paramString) {
/* 411 */     RecordSet recordSet = new RecordSet();
/* 412 */     recordSet.executeQuery("select * from configXmlFile a,configFileManager b where a.configfileid = b.id and a.isdelete='1' and b.id in (" + paramString + ") ", new Object[0]);
/* 413 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 414 */     while (recordSet.next()) {
/* 415 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 416 */       arrayList.add(hashMap);
/* 417 */       String str = recordSet.getString("filepath");
/* 418 */       if (str != null && (str.startsWith("/") || str.startsWith("\\"))) {
/* 419 */         str = str.substring(1);
/*     */       }
/* 421 */       hashMap.put("filepath", str);
/* 422 */       hashMap.put("xpath", recordSet.getString("xpath"));
/* 423 */       hashMap.put("content", recordSet.getString("attrvalue"));
/*     */     } 
/*     */ 
/*     */     
/* 427 */     ConfigSpecialHandle configSpecialHandle = new ConfigSpecialHandle();
/* 428 */     configSpecialHandle.deleteXMLConfig_File((ArrayList)arrayList);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void deleteXMLConfig(String paramString, Document paramDocument) {
/* 436 */     RecordSet recordSet = new RecordSet();
/* 437 */     recordSet.executeQuery("select * from configXmlFile a,configFileManager b where a.configfileid = b.id and a.isdelete='1' and b.id in (" + paramString + ") ", new Object[0]);
/*     */     
/* 439 */     while (recordSet.next()) {
/* 440 */       ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 441 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 442 */       arrayList.add(hashMap);
/* 443 */       String str = recordSet.getString("filepath");
/* 444 */       if (str != null && (str.startsWith("/") || str.startsWith("\\"))) {
/* 445 */         str = str.substring(1);
/*     */       }
/* 447 */       hashMap.put("filepath", str);
/* 448 */       hashMap.put("xpath", recordSet.getString("xpath"));
/* 449 */       hashMap.put("content", recordSet.getString("attrvalue"));
/*     */       
/* 451 */       ConfigSpecialHandle configSpecialHandle = new ConfigSpecialHandle();
/* 452 */       configSpecialHandle.deleteXMLConfig_File((ArrayList)arrayList, paramDocument, str);
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/SystemAutoConfig.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */