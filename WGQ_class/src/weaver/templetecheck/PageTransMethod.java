/*     */ package weaver.templetecheck;
/*     */ 
/*     */ import java.io.File;
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import org.dom4j.Document;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class PageTransMethod
/*     */ {
/*  20 */   FileUtil fileUtil = new FileUtil();
/*     */   public ArrayList<String> getOpratePopedom(String paramString1, String paramString2) throws Exception {
/*  22 */     ArrayList<String> arrayList = new ArrayList();
/*     */     
/*  24 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/*     */     
/*  26 */     String str = "";
/*  27 */     if (arrayOfString.length == 2) {
/*  28 */       str = arrayOfString[1];
/*     */     }
/*  30 */     if ("已配置".equals(str)) {
/*  31 */       arrayList.add("false");
/*  32 */       arrayList.add("false");
/*  33 */       arrayList.add("true");
/*  34 */     } else if (str.indexOf("多个") >= 0) {
/*  35 */       arrayList.add("false");
/*  36 */       arrayList.add("false");
/*  37 */       arrayList.add("true");
/*  38 */     } else if ("与标准不一致".equals(str)) {
/*  39 */       arrayList.add("true");
/*  40 */       arrayList.add("false");
/*  41 */       arrayList.add("false");
/*  42 */     } else if ("xpath与配置内容不一致".equals(str) || str.indexOf("忽略") > -1 || "配置内容不符合XML格式".equals(str)) {
/*  43 */       arrayList.add("false");
/*  44 */       arrayList.add("false");
/*  45 */       arrayList.add("false");
/*     */     } else {
/*  47 */       arrayList.add("true");
/*  48 */       arrayList.add("true");
/*  49 */       arrayList.add("false");
/*     */     } 
/*  51 */     return arrayList;
/*     */   }
/*     */   
/*     */   public ArrayList<String> getOpratePopedom2(String paramString1, String paramString2) throws Exception {
/*  55 */     ArrayList<String> arrayList = new ArrayList();
/*     */     
/*  57 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/*     */     
/*  59 */     String str1 = "";
/*  60 */     String str2 = "";
/*  61 */     String str3 = "";
/*  62 */     if (arrayOfString.length == 3) {
/*  63 */       str1 = arrayOfString[0];
/*  64 */       str2 = arrayOfString[1];
/*  65 */       str3 = arrayOfString[2];
/*     */     } 
/*  67 */     if (str3.equalsIgnoreCase("properties")) {
/*  68 */       if ("1".equals(str2)) {
/*  69 */         arrayList.add("false");
/*  70 */         arrayList.add("true");
/*  71 */       } else if ("5".equals(str2) || "7".equals(str2) || "8".equals(str2)) {
/*  72 */         arrayList.add("false");
/*  73 */         arrayList.add("false");
/*     */       } else {
/*  75 */         arrayList.add("true");
/*  76 */         arrayList.add("false");
/*     */       }
/*     */     
/*  79 */     } else if ("1".equals(str2)) {
/*  80 */       arrayList.add("false");
/*  81 */       arrayList.add("false");
/*  82 */       arrayList.add("true");
/*  83 */     } else if ("4".equals(str2) || "5".equals(str2) || "6".equals(str2) || "7".equals(str2) || "8".equals(str2)) {
/*  84 */       arrayList.add("false");
/*  85 */       arrayList.add("false");
/*  86 */       arrayList.add("false");
/*     */     } else {
/*  88 */       arrayList.add("true");
/*  89 */       arrayList.add("true");
/*  90 */       arrayList.add("false");
/*     */     } 
/*     */ 
/*     */     
/*  94 */     return arrayList;
/*     */   }
/*     */   
/*     */   public ArrayList<String> getOpratePopedomForConfigManager(String paramString1, String paramString2) throws Exception {
/*  98 */     ArrayList<String> arrayList = new ArrayList();
/*  99 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/* 100 */     String str1 = "";
/* 101 */     String str2 = "";
/* 102 */     if (arrayOfString.length == 3) {
/* 103 */       str1 = arrayOfString[1];
/* 104 */       str2 = arrayOfString[2];
/*     */     } 
/*     */     
/* 107 */     arrayList.add("true");
/* 108 */     arrayList.add("true");
/* 109 */     arrayList.add("true");
/* 110 */     arrayList.add("true");
/* 111 */     if ((SystemEnv.getHtmlLabelName(25105, ThreadVarLanguage.getLang()) + "").equals(str1)) {
/* 112 */       arrayList.add("true");
/*     */     } else {
/* 114 */       arrayList.add("false");
/*     */     } 
/* 116 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getRequisite(String paramString) {
/* 125 */     if ("1".equals(paramString)) {
/* 126 */       return "" + SystemEnv.getHtmlLabelName(163, ThreadVarLanguage.getLang()) + "";
/*     */     }
/* 128 */     return "" + SystemEnv.getHtmlLabelName(25105, ThreadVarLanguage.getLang()) + "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getMatchRes(String paramString1, String paramString2) {
/* 139 */     String str = "";
/*     */     try {
/* 141 */       String str1 = "";
/* 142 */       MatchUtil matchUtil = new MatchUtil();
/* 143 */       CheckUtil checkUtil = new CheckUtil();
/* 144 */       KBVersionCompare kBVersionCompare = new KBVersionCompare();
/* 145 */       String str2 = "";
/* 146 */       Document document = null;
/* 147 */       String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/* 148 */       if (arrayOfString.length == 2) {
/* 149 */         str1 = arrayOfString[0];
/* 150 */         str2 = arrayOfString[1];
/* 151 */         ReadXml readXml = new ReadXml();
/* 152 */         document = readXml.read(str2);
/*     */       } 
/*     */       
/* 155 */       CheckUtil.Rule rule = checkUtil.getRuleObjById(str1, paramString1);
/* 156 */       String str3 = rule.getXpath();
/* 157 */       String str4 = rule.getVersion();
/*     */       
/* 159 */       boolean bool = false;
/* 160 */       List<Map<String, String>> list = checkUtil.getAllFileRules(str1, "", "");
/* 161 */       for (byte b = 0; b < list.size(); b++) {
/* 162 */         Map map = list.get(b);
/* 163 */         String str6 = (String)map.get("version");
/* 164 */         String str7 = (String)map.get("xpath");
/* 165 */         if (str7 != null && !"".equals(str7) && str7.equals(str3) && 
/* 166 */           str6 != null && !"".equals(str6) && !str6.equals(str4)) {
/* 167 */           int i = kBVersionCompare.compareVersion(str6, str4);
/* 168 */           if (i == -1) {
/* 169 */             bool = true;
/*     */             
/*     */             break;
/*     */           } 
/*     */         } 
/*     */       } 
/*     */       
/* 176 */       if (bool) {
/* 177 */         str = "<span style='color:green'>" + SystemEnv.getHtmlLabelName(10004222, ThreadVarLanguage.getLang()) + "</span>";
/* 178 */         return str;
/*     */       } 
/*     */ 
/*     */       
/* 182 */       String str5 = rule.getReplacecontent();
/*     */       
/* 184 */       if (document != null) {
/* 185 */         String str6 = matchUtil.checkXmlFile(str3, document, str5);
/* 186 */         if ("ok".equals(str6)) {
/* 187 */           str = "<span style='color:green'>" + SystemEnv.getHtmlLabelName(10004085, ThreadVarLanguage.getLang()) + "</span>";
/* 188 */         } else if ("multierror".equals(str6)) {
/* 189 */           str = "<span style='color:#FFCC00'>" + SystemEnv.getHtmlLabelName(10004088, ThreadVarLanguage.getLang()) + "</span>";
/* 190 */         } else if ("xpatherror".equals(str6)) {
/* 191 */           str = "<span style='color:#FFCC00'>" + SystemEnv.getHtmlLabelName(10004087, ThreadVarLanguage.getLang()) + "</span>";
/* 192 */         } else if ("diff".equals(str6)) {
/* 193 */           str = "<span style='color:#FFCC00'>" + SystemEnv.getHtmlLabelName(10004086, ThreadVarLanguage.getLang()) + "</span>";
/* 194 */         } else if ("xpathorcontenterror".equals(str6)) {
/* 195 */           str = "<span style='color:#FFCC00'>" + SystemEnv.getHtmlLabelName(10004090, ThreadVarLanguage.getLang()) + "</span>";
/* 196 */         } else if ("contenterror".equals(str6)) {
/* 197 */           str = "<span style='color:#FFCC00'>" + SystemEnv.getHtmlLabelName(10004089, ThreadVarLanguage.getLang()) + "XML" + SystemEnv.getHtmlLabelName(15196, ThreadVarLanguage.getLang()) + "</span>";
/*     */         } else {
/* 199 */           str = "<span style='color:red'>" + SystemEnv.getHtmlLabelName(129337, ThreadVarLanguage.getLang()) + "</span>";
/*     */         } 
/*     */       } 
/* 202 */     } catch (Exception exception) {
/* 203 */       exception.printStackTrace();
/*     */     } 
/* 205 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getFileCharset(String paramString) {
/* 213 */     String str = "";
/* 214 */     RecordSet recordSet = new RecordSet();
/*     */     try {
/* 216 */       String str1 = "";
/* 217 */       recordSet.executeSql("select custompage from workflow_base where id='" + paramString + "'");
/* 218 */       if (recordSet.next()) {
/* 219 */         str1 = Util.null2String(recordSet.getString("custompage"));
/* 220 */         String str2 = this.fileUtil.getPath(GCONST.getRootPath() + str1);
/*     */         
/* 222 */         if (str2.indexOf("?") > -1) {
/* 223 */           str2 = str2.substring(0, str2.indexOf("?"));
/*     */         }
/* 225 */         File file = new File(str2);
/*     */         
/* 227 */         if (file.exists()) {
/* 228 */           boolean bool = FileCharsetDetector.check(file);
/* 229 */           if (bool) {
/* 230 */             str = "UTF-8";
/*     */           } else {
/* 232 */             str = "GBK";
/*     */           } 
/*     */         } 
/*     */       } 
/* 236 */     } catch (Exception exception) {
/* 237 */       str = "";
/*     */     } 
/*     */     
/* 240 */     return str;
/*     */   }
/*     */   
/*     */   public ArrayList<String> getOpratePopedom3(String paramString1, String paramString2) throws Exception {
/* 244 */     ArrayList<String> arrayList = new ArrayList();
/*     */     
/* 246 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/*     */     
/* 248 */     String str = "";
/* 249 */     if (arrayOfString.length == 2) {
/* 250 */       str = arrayOfString[1];
/*     */     }
/* 252 */     if (str.indexOf("无法自动替换") >= 0) {
/* 253 */       arrayList.add("false");
/*     */     } else {
/* 255 */       arrayList.add("true");
/*     */     } 
/* 257 */     return arrayList;
/*     */   }
/*     */   
/*     */   public String getConfigColor(String paramString) {
/* 261 */     String str = "";
/* 262 */     if (!"".equals(paramString) && (paramString.equals(SystemEnv.getHtmlLabelName(10004085, ThreadVarLanguage.getLang()) + "") || paramString.equals(SystemEnv.getHtmlLabelName(10004220, ThreadVarLanguage.getLang()) + ""))) {
/* 263 */       str = "<span style='color:green'>" + paramString + "</span>";
/* 264 */     } else if (!"".equals(paramString) && paramString.equals(SystemEnv.getHtmlLabelName(129337, ThreadVarLanguage.getLang()) + "")) {
/* 265 */       str = "<span style='color:red'>" + paramString + "</span>";
/*     */     } else {
/* 267 */       str = "<span style='color:#FFCC00'>" + paramString + "</span>";
/*     */     } 
/* 269 */     return str;
/*     */   }
/*     */   
/*     */   public String getALink(String paramString) {
/* 273 */     String str = "";
/* 274 */     str = str + "<a href=\"javascript:changetoQCDetail(" + paramString + ");\">" + paramString + "</a>";
/* 275 */     return str;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getScript(String paramString) {
/* 280 */     RecordSet recordSet = new RecordSet();
/* 281 */     String str1 = "select custompage from WORKFLOW_BASE where id=" + paramString;
/* 282 */     recordSet.execute(str1);
/* 283 */     String str2 = "";
/* 284 */     if (recordSet.next()) {
/* 285 */       str2 = recordSet.getString("custompage");
/*     */     }
/* 287 */     String str3 = this.fileUtil.getPath(GCONST.getRootPath() + str2);
/*     */     
/* 289 */     if (str3.indexOf("?") > -1) {
/* 290 */       str3 = str3.substring(0, str3.indexOf("?"));
/*     */     }
/* 292 */     File file = new File(str3);
/* 293 */     String str4 = "" + SystemEnv.getHtmlLabelName(346, ThreadVarLanguage.getLang()) + "";
/* 294 */     if (file.exists()) {
/* 295 */       String str = this.fileUtil.readFile(file).toString();
/* 296 */       if (str.toLowerCase().indexOf("<script") < 0) {
/* 297 */         str4 = "" + SystemEnv.getHtmlLabelName(15507, ThreadVarLanguage.getLang()) + "";
/*     */       }
/*     */     } else {
/* 300 */       str4 = "" + SystemEnv.getHtmlLabelName(10004223, ThreadVarLanguage.getLang()) + "";
/* 301 */       (new BaseBean()).writeLog("jsp文件未找到：" + str3);
/*     */     } 
/* 303 */     return str4;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/PageTransMethod.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */