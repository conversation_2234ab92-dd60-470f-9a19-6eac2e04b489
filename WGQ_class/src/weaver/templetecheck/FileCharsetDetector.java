/*     */ package weaver.templetecheck;
/*     */ 
/*     */ import java.io.BufferedInputStream;
/*     */ import java.io.File;
/*     */ import java.io.FileInputStream;
/*     */ import java.io.FileNotFoundException;
/*     */ import java.io.IOException;
/*     */ import java.io.InputStream;
/*     */ import org.apache.commons.io.input.BOMInputStream;
/*     */ import org.mozilla.intl.chardet.nsDetector;
/*     */ import org.mozilla.intl.chardet.nsICharsetDetectionObserver;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FileCharsetDetector
/*     */ {
/*     */   private static String encoding;
/*     */   private static boolean found;
/*     */   private static nsDetector detector;
/*     */   private static nsICharsetDetectionObserver observer;
/*     */   
/*     */   enum Language
/*     */   {
/*  37 */     Japanese(1),
/*  38 */     Chinese(2),
/*  39 */     SimplifiedChinese(3),
/*  40 */     TraditionalChinese(4),
/*  41 */     Korean(5),
/*  42 */     DontKnow(6);
/*     */     
/*     */     private int hint;
/*     */     
/*     */     Language(int param1Int1) {
/*  47 */       this.hint = param1Int1;
/*     */     }
/*     */     
/*     */     public int getHint() {
/*  51 */       return this.hint;
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String checkEncoding(File paramFile) throws FileNotFoundException, IOException {
/*  66 */     return checkEncoding(paramFile, getNsdetector());
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String checkEncoding(File paramFile, Language paramLanguage) throws FileNotFoundException, IOException {
/*  82 */     return checkEncoding(paramFile, new nsDetector(paramLanguage.getHint()));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String checkEncoding(String paramString) throws FileNotFoundException, IOException {
/*  96 */     return checkEncoding(new File(paramString));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String checkEncoding(String paramString, Language paramLanguage) throws FileNotFoundException, IOException {
/* 112 */     return checkEncoding(new File(paramString), paramLanguage);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static String checkEncoding(File paramFile, nsDetector paramnsDetector) throws FileNotFoundException, IOException {
/* 127 */     paramnsDetector.Init(getCharsetDetectionObserver());
/*     */     
/* 129 */     if (isAscii(paramFile, paramnsDetector)) {
/* 130 */       encoding = "ASCII";
/* 131 */       found = true;
/*     */     } 
/*     */     
/* 134 */     if (!found) {
/* 135 */       String[] arrayOfString = paramnsDetector.getProbableCharsets();
/* 136 */       if (arrayOfString.length > 0) {
/* 137 */         encoding = arrayOfString[0];
/*     */       } else {
/* 139 */         return null;
/*     */       } 
/*     */     } 
/*     */     
/* 143 */     return encoding;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static boolean isAscii(File paramFile, nsDetector paramnsDetector) throws IOException {
/* 155 */     BufferedInputStream bufferedInputStream = null;
/* 156 */     BOMInputStream bOMInputStream = null;
/*     */     try {
/* 158 */       FileInputStream fileInputStream = new FileInputStream(paramFile);
/* 159 */       bOMInputStream = new BOMInputStream(fileInputStream);
/* 160 */       bufferedInputStream = new BufferedInputStream((InputStream)bOMInputStream);
/*     */       
/* 162 */       byte[] arrayOfByte = new byte[1024];
/*     */       
/* 164 */       boolean bool1 = false;
/* 165 */       boolean bool2 = true;
/*     */       int i;
/* 167 */       while ((i = bufferedInputStream.read(arrayOfByte)) != -1) {
/* 168 */         if (bool2)
/* 169 */           bool2 = paramnsDetector.isAscii(arrayOfByte, i); 
/* 170 */         if (!bool2 && !bool1) {
/* 171 */           bool1 = paramnsDetector.DoIt(arrayOfByte, i, false);
/*     */         }
/*     */       } 
/* 174 */       return bool2;
/* 175 */     } catch (Exception exception) {
/* 176 */       exception.printStackTrace();
/* 177 */       return false;
/*     */     } finally {
/* 179 */       paramnsDetector.DataEnd();
/* 180 */       if (null != bufferedInputStream) bufferedInputStream.close(); 
/* 181 */       if (null != bOMInputStream) bOMInputStream.close();
/*     */     
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static nsDetector getNsdetector() {
/* 190 */     if (null == detector) {
/* 191 */       detector = new nsDetector();
/*     */     } else {
/* 193 */       detector.Reset();
/*     */     } 
/* 195 */     return detector;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static nsICharsetDetectionObserver getCharsetDetectionObserver() {
/* 203 */     if (null == observer) {
/* 204 */       observer = new nsICharsetDetectionObserver() {
/*     */           public void Notify(String param1String) {
/* 206 */             FileCharsetDetector.found = true;
/* 207 */             FileCharsetDetector.encoding = param1String;
/*     */           }
/*     */         };
/*     */     }
/* 211 */     return observer;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean check(File paramFile) throws IOException {
/* 217 */     String str = checkEncoding(paramFile);
/* 218 */     if ("UTF-8".equals(str)) {
/* 219 */       return true;
/*     */     }
/* 221 */     return false;
/*     */   }
/*     */ 
/*     */   
/*     */   public static String getEncoding(File paramFile) throws IOException {
/* 226 */     return checkEncoding(paramFile);
/*     */   }
/*     */   
/*     */   static class StreamBuffer
/*     */   {
/*     */     final InputStream in;
/*     */     final byte[] buf;
/* 233 */     int pos = -1; int len;
/*     */     
/*     */     public StreamBuffer(InputStream param1InputStream, int param1Int) {
/* 236 */       this.in = param1InputStream;
/* 237 */       if (param1Int < 3) {
/* 238 */         param1Int = 3;
/*     */       }
/* 240 */       this.buf = new byte[param1Int];
/*     */     }
/*     */     public void redo() {
/* 243 */       this.pos = 0;
/*     */     }
/*     */     public int next() throws IOException {
/* 246 */       if (this.len > 0 || this.pos < 0) {
/* 247 */         if (++this.pos == this.len) {
/* 248 */           if ((this.len = this.in.read(this.buf)) == 0) {
/* 249 */             return -1;
/*     */           }
/* 251 */           this.pos = 0;
/*     */         } 
/* 253 */         return this.buf[this.pos] & 0xFF;
/*     */       } 
/* 255 */       return -1;
/*     */     }
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/FileCharsetDetector.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */