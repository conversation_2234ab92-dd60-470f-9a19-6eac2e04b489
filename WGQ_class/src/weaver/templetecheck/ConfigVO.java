/*    */ package weaver.templetecheck;
/*    */ 
/*    */ import java.util.List;
/*    */ import org.jdom.Element;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class ConfigVO
/*    */ {
/* 12 */   private String from = "jc";
/*    */   
/*    */   private String action;
/*    */   private Element beforeElement;
/*    */   private Element afterElement;
/*    */   
/*    */   public ConfigVO(String paramString1, String paramString2, Element paramElement1, Element paramElement2, List<Element> paramList) {
/* 19 */     this.from = paramString1;
/* 20 */     this.action = paramString2;
/* 21 */     this.beforeElement = paramElement1;
/* 22 */     this.afterElement = paramElement2;
/* 23 */     this.content = paramList;
/*    */   }
/*    */   private List<Element> content; public static final String ACTION_ADD = "add"; public static final String ACTION_DELETE = "delete";
/*    */   
/*    */   public ConfigVO() {}
/*    */   
/*    */   public String getFrom() {
/* 30 */     return this.from;
/*    */   }
/*    */   
/*    */   public void setFrom(String paramString) {
/* 34 */     this.from = paramString;
/*    */   }
/*    */   
/*    */   public String getAction() {
/* 38 */     return this.action;
/*    */   }
/*    */   
/*    */   public void setAction(String paramString) {
/* 42 */     this.action = paramString;
/*    */   }
/*    */   
/*    */   public Element getBeforeElement() {
/* 46 */     return this.beforeElement;
/*    */   }
/*    */   
/*    */   public void setBeforeElement(Element paramElement) {
/* 50 */     this.beforeElement = paramElement;
/*    */   }
/*    */   
/*    */   public Element getAfterElement() {
/* 54 */     return this.afterElement;
/*    */   }
/*    */   
/*    */   public void setAfterElement(Element paramElement) {
/* 58 */     this.afterElement = paramElement;
/*    */   }
/*    */   
/*    */   public List<Element> getContent() {
/* 62 */     return this.content;
/*    */   }
/*    */   
/*    */   public void setContent(List<Element> paramList) {
/* 66 */     this.content = paramList;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/ConfigVO.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */