/*    */ package weaver.templetecheck;
/*    */ 
/*    */ import java.io.File;
/*    */ import java.io.FilenameFilter;
/*    */ 
/*    */ public class FileFilter implements FilenameFilter {
/*    */   private String indexstrings;
/*    */   
/*    */   public FileFilter(String paramString) {
/* 10 */     this.indexstrings = paramString;
/*    */   }
/*    */   
/*    */   public boolean accept(File paramFile, String paramString) {
/* 14 */     String[] arrayOfString = this.indexstrings.split(",");
/* 15 */     for (byte b = 0; b < arrayOfString.length; ) {
/* 16 */       if (paramString.indexOf(arrayOfString[b]) >= 0) {
/*    */         b++; continue;
/*    */       } 
/* 19 */       return false;
/*    */     } 
/*    */     
/* 22 */     return true;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/FileFilter.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */