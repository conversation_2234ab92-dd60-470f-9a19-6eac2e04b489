/*     */ package weaver.templetecheck;
/*     */ import java.io.BufferedReader;
/*     */ import java.io.BufferedWriter;
/*     */ import java.io.File;
/*     */ import java.io.FileInputStream;
/*     */ import java.io.OutputStreamWriter;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ public class PropertiesFileOperation {
/*  14 */   public static ArrayList<String> importantfilelist = new ArrayList<>();
/*  15 */   UpgradeFileUtil fileUtil = new UpgradeFileUtil();
/*  16 */   ConfigBakUtil fileBakUtil = new ConfigBakUtil();
/*     */   
/*     */   public ArrayList<Element> elementlist;
/*     */ 
/*     */   
/*     */   private void init() {
/*  22 */     importantfilelist.add("weaver.properties");
/*     */   } public PropertiesFileOperation() {
/*  24 */     this.elementlist = new ArrayList<>();
/*     */     init();
/*     */   } public HashMap<String, File> getAllPropertiesFiles() {
/*  27 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  28 */     String str = GCONST.getRootPath() + "WEB-INF/prop/";
/*  29 */     File file = new File(this.fileUtil.getPath(str));
/*  30 */     if (file.exists()) {
/*  31 */       File[] arrayOfFile = file.listFiles();
/*  32 */       for (byte b = 0; b < arrayOfFile.length; b++) {
/*  33 */         File file1 = arrayOfFile[b];
/*  34 */         if (!importantfilelist.contains(file1.getName()))
/*     */         {
/*     */           
/*  37 */           hashMap.put("" + file1, file1);
/*     */         }
/*     */       } 
/*     */     } 
/*  41 */     return (HashMap)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public ArrayList<Element> load(File paramFile, String paramString) {
/*  47 */     synchronized (this) {
/*     */       
/*  49 */       FileInputStream fileInputStream = null;
/*     */       try {
/*  51 */         fileInputStream = new FileInputStream(this.fileUtil.getPath(paramFile.getPath()));
/*  52 */         InputStreamReader inputStreamReader = new InputStreamReader(fileInputStream, paramString);
/*  53 */         BufferedReader bufferedReader = new BufferedReader(inputStreamReader);
/*  54 */         String str = "";
/*     */         
/*  56 */         StringBuffer stringBuffer = new StringBuffer();
/*  57 */         while ((str = bufferedReader.readLine()) != null) {
/*  58 */           Element element = new Element();
/*  59 */           str = str.trim();
/*  60 */           if (str.startsWith("#")) {
/*     */             
/*  62 */             stringBuffer.append(stringBuffer);
/*  63 */             stringBuffer.append("\n\r"); continue;
/*     */           } 
/*  65 */           String str1 = stringBuffer.toString();
/*  66 */           stringBuffer = new StringBuffer();
/*     */           
/*  68 */           if (str.indexOf("=") > -1) {
/*  69 */             String[] arrayOfString = str.split("=");
/*  70 */             String str2 = "";
/*  71 */             String str3 = "";
/*  72 */             String str4 = arrayOfString[0];
/*  73 */             if (str4 != null) {
/*  74 */               str2 = arrayOfString[0];
/*     */ 
/*     */ 
/*     */               
/*  78 */               element.setKey(str2);
/*  79 */               str4 = arrayOfString[1];
/*  80 */               if (str4 != null) {
/*  81 */                 str3 = arrayOfString[1].trim();
/*     */               } else {
/*  83 */                 str3 = null;
/*     */               } 
/*  85 */               element.setValue(str3);
/*     */               
/*  87 */               this.elementlist.add(element);
/*     */             }  continue;
/*  89 */           }  element.setKey(str);
/*  90 */           element.setComment(str1);
/*  91 */           this.elementlist.add(element);
/*     */         } 
/*     */ 
/*     */ 
/*     */         
/*  96 */         if (bufferedReader != null) {
/*  97 */           bufferedReader.close();
/*     */         }
/*  99 */         if (inputStreamReader != null) {
/* 100 */           inputStreamReader.close();
/*     */         }
/*     */       }
/* 103 */       catch (Exception exception) {
/* 104 */         exception.printStackTrace();
/*     */       } 
/* 106 */       return this.elementlist;
/*     */     } 
/*     */   }
/*     */   
/*     */   public String getValueByKey(File paramFile, String paramString1, String paramString2) {
/* 111 */     synchronized (this) {
/* 112 */       String str = "";
/*     */       try {
/* 114 */         FileInputStream fileInputStream = new FileInputStream(paramFile);
/*     */       }
/* 116 */       catch (FileNotFoundException fileNotFoundException) {
/* 117 */         fileNotFoundException.printStackTrace();
/*     */       } 
/*     */       
/* 120 */       return str;
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public void store(File paramFile, String paramString, ArrayList<Element> paramArrayList) {
/* 126 */     synchronized (this) {
/* 127 */       FileOutputStream fileOutputStream = null;
/*     */       try {
/* 129 */         fileOutputStream = new FileOutputStream(paramFile);
/* 130 */         OutputStreamWriter outputStreamWriter = new OutputStreamWriter(fileOutputStream, paramString);
/* 131 */         BufferedWriter bufferedWriter = new BufferedWriter(outputStreamWriter);
/* 132 */         for (byte b = 0; b < paramArrayList.size(); b++) {
/* 133 */           Element element = paramArrayList.get(b);
/* 134 */           String str1 = element.getKey();
/* 135 */           String str2 = Util.null2String(element.getValue());
/* 136 */           String str3 = element.getComment();
/*     */           
/* 138 */           if (str3 != null && !"".equals(str3)) {
/* 139 */             str3 = str3.trim();
/* 140 */             if (str3.startsWith("#")) {
/* 141 */               str3 = "#" + str3;
/*     */             }
/* 143 */             str3 = str3.replaceAll("\n\r", "\n\r#");
/* 144 */             writeComments(bufferedWriter, str3);
/*     */           } 
/*     */           
/* 147 */           if (str1 != null && !"".equals(str1)) {
/* 148 */             bufferedWriter.write(str1 + "=" + str2);
/*     */           }
/* 150 */           bufferedWriter.newLine();
/*     */         } 
/* 152 */         if (bufferedWriter != null) {
/* 153 */           bufferedWriter.flush();
/* 154 */           bufferedWriter.close();
/*     */         } 
/* 156 */         if (outputStreamWriter != null) {
/* 157 */           outputStreamWriter.flush();
/* 158 */           outputStreamWriter.close();
/*     */         }
/*     */       
/* 161 */       } catch (Exception exception) {
/* 162 */         exception.printStackTrace();
/*     */       } finally {}
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void check() {
/* 170 */     String str = "";
/* 171 */     RecordSet recordSet = new RecordSet();
/* 172 */     recordSet.execute("");
/* 173 */     while (recordSet.next()) {
/* 174 */       String str1 = recordSet.getString("key");
/* 175 */       String str2 = recordSet.getString("value");
/*     */     } 
/* 177 */     ArrayList<Element> arrayList = load(new File(this.fileUtil.getPath(str)), "");
/*     */   }
/*     */ 
/*     */   
/*     */   private void writeComments(BufferedWriter paramBufferedWriter, String paramString) throws IOException {
/* 182 */     int i = paramString.length();
/* 183 */     byte b = 0;
/* 184 */     int j = 0;
/* 185 */     char[] arrayOfChar = new char[6];
/* 186 */     arrayOfChar[0] = '\\';
/* 187 */     arrayOfChar[1] = 'u';
/* 188 */     while (b < i) {
/* 189 */       char c = paramString.charAt(b);
/* 190 */       if (c > 'ÿ' || c == '\n' || c == '\r') {
/* 191 */         if (j != b)
/* 192 */           paramBufferedWriter.write(paramString.substring(j, b)); 
/* 193 */         if (c > 'ÿ') {
/* 194 */           arrayOfChar[2] = toHex(c >> 12 & 0xF);
/* 195 */           arrayOfChar[3] = toHex(c >> 8 & 0xF);
/* 196 */           arrayOfChar[4] = toHex(c >> 4 & 0xF);
/* 197 */           arrayOfChar[5] = toHex(c & 0xF);
/* 198 */           paramBufferedWriter.write(new String(arrayOfChar));
/*     */         } else {
/* 200 */           paramBufferedWriter.newLine();
/* 201 */           if (c == '\r' && b != i - 1 && paramString.charAt(b + 1) == '\n') {
/* 202 */             b++;
/*     */           }
/* 204 */           if (b == i - 1 || (paramString.charAt(b + 1) != '#' && paramString.charAt(b + 1) != '!'))
/* 205 */             paramBufferedWriter.write("#"); 
/*     */         } 
/* 207 */         j = b + 1;
/*     */       } 
/* 209 */       b++;
/*     */     } 
/* 211 */     if (j != b)
/* 212 */       paramBufferedWriter.write(paramString.substring(j, b)); 
/* 213 */     paramBufferedWriter.newLine();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String updatePropConfig(String paramString1, String paramString2) {
/* 223 */     RecordSet recordSet = new RecordSet();
/* 224 */     String str1 = "";
/* 225 */     String str2 = "";
/* 226 */     String str3 = "";
/* 227 */     String str4 = "";
/* 228 */     String str5 = "";
/* 229 */     String str6 = "select a.attrname,a.attrvalue,a.attrnotes,b.filepath  from configPropertiesFile a,configFileManager b where (a.isdelete = '0' or  a.isdelete is null) and b.isdelete=0 and  a.configfileid = b.id and  a.id=" + paramString1 + " and a.attrname is not null  order by b.filepath";
/* 230 */     recordSet.execute(str6);
/* 231 */     if (recordSet.next()) {
/* 232 */       str1 = recordSet.getString("attrname");
/* 233 */       str2 = recordSet.getString("attrvalue");
/* 234 */       str3 = recordSet.getString("attrnotes");
/* 235 */       str4 = recordSet.getString("filepath");
/* 236 */       str5 = str4;
/* 237 */       str4 = GCONST.getRootPath() + str4;
/*     */     } 
/*     */     
/* 240 */     PropertiesUtil propertiesUtil = new PropertiesUtil();
/* 241 */     propertiesUtil.load(str4);
/* 242 */     String str7 = str3.equals("") ? "" : ("#" + str3);
/* 243 */     if ("0".equals(paramString2)) {
/* 244 */       propertiesUtil.put(str1, str2, str7);
/* 245 */     } else if ("1".equals(paramString2)) {
/* 246 */       propertiesUtil.put(str1, str2, str7);
/* 247 */     } else if ("2".equals(paramString2)) {
/* 248 */       String[] arrayOfString = str1.split(",");
/* 249 */       for (byte b = 0; b < arrayOfString.length; b++) {
/* 250 */         propertiesUtil.remove(arrayOfString[b]);
/*     */       }
/*     */     } 
/*     */     
/* 254 */     String str8 = GCONST.getRootPath() + str5;
/* 255 */     String str9 = this.fileBakUtil.getBakRootPath() + str5;
/* 256 */     this.fileBakUtil.copyFile(str8, str9);
/*     */     
/* 258 */     boolean bool = propertiesUtil.store(str4);
/* 259 */     if (bool) {
/*     */       
/* 261 */       String str = "0".equals(paramString2) ? (SystemEnv.getHtmlLabelName(524932, ThreadVarLanguage.getLang()) + "") : ("1".equals(paramString2) ? (SystemEnv.getHtmlLabelName(524933, ThreadVarLanguage.getLang()) + "") : (SystemEnv.getHtmlLabelName(524934, ThreadVarLanguage.getLang()) + ""));
/* 262 */       return "{\"status\":\"ok\",\"msg\":\"" + str + "\"}";
/*     */     } 
/*     */     
/* 265 */     String str10 = "0".equals(paramString2) ? (SystemEnv.getHtmlLabelName(524938, ThreadVarLanguage.getLang()) + "") : ("1".equals(paramString2) ? (SystemEnv.getHtmlLabelName(524939, ThreadVarLanguage.getLang()) + "") : (SystemEnv.getHtmlLabelName(524940, ThreadVarLanguage.getLang()) + ""));
/* 266 */     return "{\"status\":\"error\",\"msg\":\"" + str10 + "\"}";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static char toHex(int paramInt) {
/* 275 */     return hexDigit[paramInt & 0xF];
/*     */   }
/*     */ 
/*     */   
/* 279 */   private static final char[] hexDigit = new char[] { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F' };
/*     */   
/*     */   private class Element { private String key;
/*     */     private String value;
/*     */     private String comment;
/*     */     
/*     */     private Element() {}
/*     */     
/*     */     public String getKey() {
/* 288 */       return this.key;
/*     */     }
/*     */     public void setKey(String param1String) {
/* 291 */       this.key = param1String;
/*     */     }
/*     */     public String getValue() {
/* 294 */       return this.value;
/*     */     }
/*     */     public void setValue(String param1String) {
/* 297 */       this.value = param1String;
/*     */     }
/*     */     public String getComment() {
/* 300 */       return this.comment;
/*     */     }
/*     */     public void setComment(String param1String) {
/* 303 */       this.comment = param1String;
/*     */     } }
/*     */ 
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/PropertiesFileOperation.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */