/*     */ package weaver.templetecheck;
/*     */ 
/*     */ import java.io.BufferedReader;
/*     */ import java.io.File;
/*     */ import java.io.FileInputStream;
/*     */ import java.io.IOException;
/*     */ import java.io.InputStreamReader;
/*     */ import java.util.Properties;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.GCONST;
/*     */ 
/*     */ 
/*     */ public class FileUtil
/*     */ {
/*  15 */   private static String[] allowPaths = null;
/*     */   
/*     */   public FileUtil() {
/*  18 */     if (allowPaths == null) {
/*  19 */       allowPaths = getAllowPaths();
/*     */     }
/*     */   }
/*     */ 
/*     */   
/*     */   public String getPath(String paramString) {
/*  25 */     if (paramString == null) {
/*  26 */       return "";
/*     */     }
/*  28 */     String str = System.getProperty("os.name").toLowerCase();
/*  29 */     if (!str.startsWith("windows")) {
/*  30 */       return paramString.replaceAll("\\\\+", File.separator);
/*     */     }
/*  32 */     return paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public StringBuffer readFile(File paramFile) {
/*  43 */     String str = "GBK";
/*     */     
/*  45 */     InputStreamReader inputStreamReader = null;
/*  46 */     BufferedReader bufferedReader = null;
/*  47 */     StringBuffer stringBuffer = new StringBuffer();
/*     */     try {
/*  49 */       boolean bool = FileCharsetDetector.check(paramFile);
/*  50 */       if (bool) {
/*  51 */         str = "UTF-8";
/*     */       } else {
/*  53 */         str = "GBK";
/*     */       } 
/*     */       
/*  56 */       inputStreamReader = new InputStreamReader(new FileInputStream(paramFile), str);
/*  57 */       bufferedReader = new BufferedReader(inputStreamReader);
/*  58 */       String str1 = null;
/*  59 */       while ((str1 = bufferedReader.readLine()) != null) {
/*  60 */         stringBuffer.append(str1).append("\r\n");
/*     */       }
/*  62 */     } catch (IOException iOException) {
/*  63 */       iOException.printStackTrace();
/*     */     } finally {
/*  65 */       if (bufferedReader != null) {
/*     */         try {
/*  67 */           bufferedReader.close();
/*  68 */         } catch (IOException iOException) {
/*  69 */           iOException.printStackTrace();
/*     */         } 
/*     */       }
/*  72 */       if (inputStreamReader != null) {
/*     */         try {
/*  74 */           inputStreamReader.close();
/*  75 */         } catch (IOException iOException) {
/*  76 */           iOException.printStackTrace();
/*     */         } 
/*     */       }
/*     */     } 
/*     */ 
/*     */     
/*  82 */     return stringBuffer;
/*     */   }
/*     */   
/*     */   public static String[] getAllowPaths() {
/*  86 */     FileInputStream fileInputStream = null;
/*  87 */     String[] arrayOfString = null;
/*     */     try {
/*  89 */       Properties properties = new Properties();
/*  90 */       String str = GCONST.getRootPath() + "templetecheck" + File.separatorChar + "exclude" + File.separatorChar + "allow.properties";
/*  91 */       if ((new File(str)).exists()) {
/*  92 */         fileInputStream = new FileInputStream(str);
/*  93 */         properties.load(fileInputStream);
/*  94 */         String str1 = properties.getProperty("allow");
/*  95 */         arrayOfString = str1.split(",");
/*  96 */         fileInputStream.close();
/*     */       } 
/*  98 */     } catch (Exception exception) {
/*  99 */       exception.printStackTrace();
/*     */     } finally {
/*     */       try {
/* 102 */         if (fileInputStream != null) {
/* 103 */           fileInputStream.close();
/*     */         }
/* 105 */       } catch (IOException iOException) {}
/*     */     } 
/*     */     
/* 108 */     return arrayOfString;
/*     */   }
/*     */   
/*     */   public boolean checkPath(String paramString) {
/* 112 */     boolean bool = false;
/* 113 */     if ("".equals(paramString) || paramString == null) {
/* 114 */       return true;
/*     */     }
/* 116 */     RecordSet recordSet = new RecordSet();
/* 117 */     paramString = paramString.replace("\\", "/");
/*     */ 
/*     */     
/* 120 */     String str = paramString.toLowerCase().replace(GCONST.getRootPath().replace("\\", "/").toLowerCase(), "");
/* 121 */     int i = paramString.toLowerCase().indexOf(str);
/*     */     
/* 123 */     paramString = paramString.substring(i);
/* 124 */     if (!paramString.startsWith("/")) {
/* 125 */       paramString = "/" + paramString;
/*     */     }
/*     */     
/* 128 */     recordSet.executeQuery("select * from configFileManager where filepath =?", new Object[] { paramString });
/* 129 */     if (recordSet.next()) {
/* 130 */       bool = true;
/*     */     }
/* 132 */     else if (allowPaths != null) {
/* 133 */       for (byte b = 0; b < allowPaths.length; b++) {
/* 134 */         String str1 = allowPaths[b];
/* 135 */         if (paramString.indexOf(str1) > -1) {
/* 136 */           bool = true;
/*     */           
/*     */           break;
/*     */         } 
/*     */       } 
/*     */     } 
/* 142 */     return bool;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/FileUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */