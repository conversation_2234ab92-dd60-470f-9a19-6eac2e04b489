/*    */ package weaver.templetecheck;
/*    */ import java.io.BufferedReader;
/*    */ import java.io.File;
/*    */ import java.io.FileInputStream;
/*    */ import java.io.IOException;
/*    */ import java.io.InputStreamReader;
/*    */ import org.apache.commons.io.input.BOMInputStream;
/*    */ import org.dom4j.Document;
/*    */ import org.dom4j.io.SAXReader;
/*    */ 
/*    */ public class ReadXml {
/* 12 */   private String fileEncode = "";
/* 13 */   UpgradeFileUtil fileUtil = new UpgradeFileUtil();
/*    */   public Document read(String paramString) {
/* 15 */     Document document = null;
/* 16 */     FileInputStream fileInputStream = null;
/* 17 */     InputStreamReader inputStreamReader = null;
/* 18 */     BufferedReader bufferedReader = null;
/* 19 */     BOMInputStream bOMInputStream = null;
/*    */     
/*    */     try {
/* 22 */       String str = "UTF-8";
/* 23 */       File file = new File(this.fileUtil.getPath(paramString));
/*    */ 
/*    */       
/* 26 */       if (file.exists()) {
/* 27 */         boolean bool = FileCharsetDetector.check(file);
/*    */         
/* 29 */         if (bool) {
/* 30 */           str = "UTF-8";
/*    */         } else {
/* 32 */           str = "GBK";
/*    */         } 
/* 34 */         this.fileEncode = str;
/* 35 */         SAXReader sAXReader = new SAXReader();
/* 36 */         if (file.length() > 0L)
/*    */         {
/* 38 */           sAXReader.setEntityResolver(new IgnoreDTDEntityResolver());
/*    */ 
/*    */ 
/*    */ 
/*    */           
/* 43 */           bOMInputStream = new BOMInputStream(new FileInputStream(file));
/* 44 */           bufferedReader = new BufferedReader(new InputStreamReader((InputStream)bOMInputStream, str));
/* 45 */           document = sAXReader.read(bufferedReader);
/*    */ 
/*    */           
/* 48 */           if (bufferedReader != null) {
/* 49 */             bufferedReader.close();
/*    */           }
/* 51 */           if (bOMInputStream != null) {
/* 52 */             bOMInputStream.close();
/*    */           }
/*    */         }
/*    */       
/*    */       } 
/* 57 */     } catch (Exception exception) {
/* 58 */       (new BaseBean()).writeLog("执行出错：" + exception.toString());
/* 59 */       exception.printStackTrace();
/*    */     } finally {
/* 61 */       if (fileInputStream != null) {
/*    */         try {
/* 63 */           fileInputStream.close();
/* 64 */         } catch (IOException iOException) {}
/*    */       }
/* 66 */       if (inputStreamReader != null) {
/*    */         try {
/* 68 */           inputStreamReader.close();
/* 69 */         } catch (IOException iOException) {}
/*    */       }
/* 71 */       if (bufferedReader != null) {
/*    */         try {
/* 73 */           bufferedReader.close();
/* 74 */         } catch (IOException iOException) {}
/*    */       }
/* 76 */       if (bOMInputStream != null) {
/*    */         try {
/* 78 */           bOMInputStream.close();
/* 79 */         } catch (IOException iOException) {}
/*    */       }
/*    */     } 
/* 82 */     return document;
/*    */   }
/*    */ 
/*    */   
/*    */   public String getFileEncode() {
/* 87 */     return this.fileEncode;
/*    */   }
/*    */   
/*    */   public void setFileEncode(String paramString) {
/* 91 */     this.fileEncode = paramString;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/ReadXml.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */