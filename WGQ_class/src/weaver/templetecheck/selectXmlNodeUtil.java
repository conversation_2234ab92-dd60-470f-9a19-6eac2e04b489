/*      */ package weaver.templetecheck;
/*      */ import java.io.BufferedReader;
/*      */ import java.io.File;
/*      */ import java.io.FileOutputStream;
/*      */ import java.io.IOException;
/*      */ import java.util.ArrayList;
/*      */ import java.util.HashMap;
/*      */ import java.util.HashSet;
/*      */ import java.util.Iterator;
/*      */ import java.util.LinkedHashMap;
/*      */ import java.util.LinkedHashSet;
/*      */ import java.util.List;
/*      */ import java.util.Map;
/*      */ import org.apache.commons.io.input.BOMInputStream;
/*      */ import org.dom4j.Attribute;
/*      */ import org.dom4j.Document;
/*      */ import org.dom4j.Element;
/*      */ import org.dom4j.io.OutputFormat;
/*      */ import org.dom4j.io.SAXReader;
/*      */ import org.dom4j.io.XMLWriter;
/*      */ import org.json.JSONArray;
/*      */ import org.json.JSONException;
/*      */ import org.json.JSONObject;
/*      */ import weaver.general.GCONST;
/*      */ import weaver.general.ThreadVarLanguage;
/*      */ import weaver.systeminfo.SystemEnv;
/*      */ 
/*      */ public class selectXmlNodeUtil {
/*   29 */   private JSONArray nodes = new JSONArray();
/*   30 */   private HashMap<String, Element> nodemap = new HashMap<>();
/*   31 */   UpgradeFileUtil fileUtil = new UpgradeFileUtil();
/*   32 */   private String xmlpath = "C:\\Users\\<USER>\\Desktop\\test.xml";
/*      */   Document doc;
/*   34 */   private SysUpgradeCominfo suc = new SysUpgradeCominfo();
/*   35 */   private String fileEncode = "";
/*   36 */   private String autosort = "";
/*   37 */   private String elementKey = "";
/*      */   
/*      */   public selectXmlNodeUtil(String paramString) {
/*   40 */     if (paramString != null && !"".equals(paramString)) {
/*   41 */       this.xmlpath = paramString;
/*      */     }
/*   43 */     BufferedReader bufferedReader = null;
/*   44 */     BOMInputStream bOMInputStream = null;
/*      */     try {
/*   46 */       String str = "UTF-8";
/*   47 */       File file = new File(this.fileUtil.getPath(this.xmlpath));
/*   48 */       if (file.exists()) {
/*   49 */         boolean bool = FileCharsetDetector.check(file);
/*   50 */         if (bool) {
/*   51 */           str = "UTF-8";
/*      */         } else {
/*   53 */           str = "GBK";
/*      */         } 
/*   55 */         this.fileEncode = str;
/*   56 */         SAXReader sAXReader = new SAXReader();
/*   57 */         if (file.length() > 0L)
/*      */         {
/*   59 */           sAXReader.setEntityResolver(new IgnoreDTDEntityResolver());
/*      */ 
/*      */ 
/*      */           
/*   63 */           bOMInputStream = new BOMInputStream(new FileInputStream(file));
/*   64 */           bufferedReader = new BufferedReader(new InputStreamReader((InputStream)bOMInputStream, str));
/*   65 */           this.doc = sAXReader.read(bufferedReader);
/*      */         }
/*      */       
/*      */       }
/*      */     
/*   70 */     } catch (Exception exception) {
/*   71 */       (new BaseBean()).writeLog("111执行出现错误：" + exception.toString());
/*   72 */       exception.printStackTrace();
/*      */     } finally {
/*   74 */       if (bufferedReader != null) {
/*      */         try {
/*   76 */           bufferedReader.close();
/*   77 */         } catch (IOException iOException) {}
/*      */       }
/*   79 */       if (bOMInputStream != null) {
/*      */         try {
/*   81 */           bOMInputStream.close();
/*   82 */         } catch (IOException iOException) {}
/*      */       }
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public selectXmlNodeUtil() {}
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public JSONArray getElementByParentId(String paramString) {
/*   97 */     JSONArray jSONArray = new JSONArray();
/*      */     try {
/*   99 */       Element element = this.doc.getRootElement();
/*      */       
/*  101 */       JSONObject jSONObject = new JSONObject();
/*  102 */       this.nodes.put(jSONObject);
/*  103 */       this.nodemap.put("root", element);
/*  104 */       jSONObject.put("id", "root");
/*  105 */       jSONObject.put("parentId", "0");
/*  106 */       jSONObject.put("name", element.getName());
/*  107 */       jSONObject.put("isParent", true);
/*  108 */       getNameNode_json(element, "root");
/*      */ 
/*      */       
/*  111 */       for (byte b = 0; b < this.nodes.length(); b++) {
/*  112 */         JSONObject jSONObject1 = (JSONObject)this.nodes.get(b);
/*  113 */         String str = (String)jSONObject1.get("parentId");
/*  114 */         if (paramString.equals(str));
/*      */ 
/*      */         
/*  117 */         jSONArray.put(jSONObject1);
/*      */       } 
/*  119 */     } catch (JSONException jSONException) {
/*  120 */       jSONException.printStackTrace();
/*      */     } 
/*      */     
/*  123 */     return jSONArray;
/*      */   }
/*      */   
/*      */   public JSONArray getNameNode_json(Element paramElement, String paramString) {
/*      */     try {
/*  128 */       List list = paramElement.elements();
/*  129 */       byte b = 1;
/*  130 */       for (Iterator<Element> iterator = list.iterator(); iterator.hasNext(); ) {
/*  131 */         String str1 = "";
/*  132 */         if ("0".equals(paramString)) {
/*  133 */           str1 = "" + b;
/*      */         } else {
/*  135 */           str1 = paramString + "_" + b;
/*      */         } 
/*      */         
/*  138 */         Element element = iterator.next();
/*  139 */         List list1 = element.elements();
/*      */         
/*  141 */         JSONObject jSONObject = new JSONObject();
/*  142 */         this.nodes.put(jSONObject);
/*  143 */         jSONObject.put("id", str1);
/*  144 */         jSONObject.put("parentId", paramString);
/*      */ 
/*      */         
/*  147 */         String str2 = getAttrs(element);
/*  148 */         if ("".equals(str2)) {
/*  149 */           jSONObject.put("name", element.getName());
/*      */         } else {
/*  151 */           jSONObject.put("name", element.getName() + "    " + SystemEnv.getHtmlLabelName(10004225, ThreadVarLanguage.getLang()) + "" + str2);
/*      */         } 
/*      */         
/*  154 */         if (list1.size() > 0) {
/*  155 */           jSONObject.put("isParent", true);
/*  156 */           this.nodemap.put(str1 + "", element);
/*  157 */           getNameNode_json(element, str1);
/*      */         } else {
/*  159 */           String str = element.getTextTrim();
/*      */           
/*  161 */           if (str == null) {
/*  162 */             jSONObject.put("isParent", false);
/*      */           } else {
/*      */             
/*  165 */             jSONObject.put("isParent", true);
/*  166 */             jSONObject.put("content", str);
/*  167 */             if (str != null && !"".equals(str)) {
/*  168 */               JSONObject jSONObject1 = new JSONObject();
/*  169 */               jSONObject1.put("id", str1 + "_content");
/*  170 */               jSONObject1.put("isParent", false);
/*  171 */               jSONObject1.put("parentId", str1);
/*  172 */               jSONObject1.put("nocheck", true);
/*  173 */               jSONObject1.put("name", element.getTextTrim());
/*      */               
/*  175 */               this.nodes.put(jSONObject1);
/*      */             } 
/*      */             
/*  178 */             this.nodemap.put(str1 + "_content", element);
/*      */           } 
/*      */         } 
/*      */         
/*  182 */         b++;
/*      */       } 
/*  184 */       return this.nodes;
/*  185 */     } catch (Exception exception) {
/*  186 */       exception.printStackTrace();
/*      */       
/*  188 */       return null;
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getAttrs(Element paramElement) throws JSONException {
/*  198 */     String str = "";
/*  199 */     List<Attribute> list = paramElement.attributes();
/*  200 */     JSONObject jSONObject = new JSONObject();
/*  201 */     LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/*  202 */     for (byte b = 0; b < list.size(); b++) {
/*  203 */       Attribute attribute = list.get(b);
/*  204 */       String str1 = attribute.getName();
/*  205 */       String str2 = attribute.getValue();
/*  206 */       str = str + "    " + str1 + "=" + str2;
/*      */     } 
/*  208 */     return str;
/*      */   }
/*      */   
/*      */   public Element getNodeById(Element paramElement, String paramString1, String paramString2) {
/*  212 */     Element element = null;
/*      */     try {
/*  214 */       List list = paramElement.elements();
/*  215 */       byte b = 1;
/*  216 */       for (Iterator<Element> iterator = list.iterator(); iterator.hasNext(); ) {
/*  217 */         String str = "";
/*  218 */         if ("0".equals(paramString1)) {
/*  219 */           str = "" + b;
/*      */         } else {
/*  221 */           str = paramString1 + "_" + b;
/*      */         } 
/*      */         
/*  224 */         Element element1 = iterator.next();
/*  225 */         List list1 = element1.elements();
/*      */         
/*  227 */         if (str.equals(paramString2)) {
/*  228 */           element = element1;
/*      */           break;
/*      */         } 
/*  231 */         if (list1.size() > 0) {
/*  232 */           element = getNodeById(element1, str, paramString2);
/*  233 */           if (element != null) {
/*      */             break;
/*      */           }
/*      */         } else {
/*      */           
/*  238 */           String str1 = str + "_content";
/*  239 */           if (str1.equals(paramString2)) {
/*  240 */             element = element1;
/*      */             break;
/*      */           } 
/*      */         } 
/*  244 */         b++;
/*      */       }
/*      */     
/*  247 */     } catch (Exception exception) {
/*  248 */       exception.printStackTrace();
/*  249 */       return null;
/*      */     } 
/*  251 */     return element;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String addNode(String paramString1, String paramString2) throws IOException {
/*  262 */     String str = "";
/*  263 */     FileOutputStream fileOutputStream = null;
/*  264 */     XMLWriter xMLWriter = null;
/*  265 */     boolean bool = true;
/*      */     try {
/*  267 */       Element element2, element1 = this.doc.getRootElement();
/*      */       
/*  269 */       if ("root".equals(paramString1)) {
/*  270 */         element2 = element1;
/*      */       } else {
/*  272 */         element2 = getNodeById(element1, "root", paramString1);
/*      */       } 
/*  274 */       if (paramString2 != null && !"".equals(paramString2)) {
/*      */ 
/*      */ 
/*      */         
/*  278 */         paramString2 = "<myroot>" + paramString2 + "</myroot>";
/*  279 */         Document document = Str2Document(paramString2);
/*  280 */         Element element = document.getRootElement();
/*      */         
/*  282 */         List<Element> list = element.elements();
/*  283 */         if (list.size() > 0) {
/*  284 */           for (byte b = 0; b < list.size(); b++) {
/*  285 */             Element element3 = list.get(b);
/*  286 */             Document document1 = Str2Document(element3.asXML());
/*  287 */             Element element4 = document1.getRootElement();
/*  288 */             element2.add(element4);
/*      */           } 
/*      */         } else {
/*      */           
/*  292 */           List list1 = element2.elements();
/*  293 */           if (list1.size() > 0) {
/*  294 */             bool = false;
/*      */           } else {
/*  296 */             element2.setText(element.getTextTrim());
/*      */           } 
/*      */         } 
/*      */ 
/*      */         
/*  301 */         if (bool) {
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */           
/*  309 */           this.doc = sortElement(this.doc);
/*  310 */           writeXml(this.fileUtil.getPath(this.xmlpath), this.doc);
/*      */           
/*  312 */           str = "{\"status\":\"ok\"}";
/*      */         } else {
/*  314 */           str = "{\"status\":\"no\"}";
/*      */         } 
/*      */       } else {
/*  317 */         str = "{\"status\":\"ok\"}";
/*      */       }
/*      */     
/*  320 */     } catch (Exception exception) {
/*  321 */       exception.printStackTrace();
/*  322 */       str = "{\"status\":\"no\"}";
/*      */     } finally {
/*  324 */       if (fileOutputStream != null && xMLWriter != null) {
/*  325 */         fileOutputStream.flush();
/*  326 */         xMLWriter.flush();
/*      */       } 
/*  328 */       if (fileOutputStream != null) {
/*  329 */         fileOutputStream.close();
/*      */       }
/*  331 */       if (xMLWriter != null) {
/*  332 */         xMLWriter.close();
/*      */       }
/*      */     } 
/*  335 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String autoConfig(String paramString1, Document paramDocument, String paramString2) {
/*  342 */     Object object1 = null;
/*  343 */     Object object2 = null;
/*      */     try {
/*  345 */       if (paramString1 == null || "".equals(paramString1)) {
/*  346 */         return null;
/*      */       }
/*      */       
/*  349 */       PropertiesUtil propertiesUtil = new PropertiesUtil();
/*  350 */       propertiesUtil.load(GCONST.getRootPath() + "WEB-INF" + File.separatorChar + "prop" + File.separatorChar + "autoconfigsetting.properties");
/*  351 */       this.autosort = propertiesUtil.getPropertyVal("autosort");
/*      */       
/*  353 */       List list1 = paramDocument.selectNodes(paramString1);
/*  354 */       String str1 = "";
/*      */       
/*  356 */       writeLog2("" + SystemEnv.getHtmlLabelName(10004226, ThreadVarLanguage.getLang()) + "......xpath:" + paramString1);
/*      */       
/*  358 */       if (list1.size() > 0) {
/*      */         
/*  360 */         if (list1.size() == 1) {
/*  361 */           return modyElement(paramString1, paramString2, list1, paramDocument);
/*      */         }
/*      */         
/*  364 */         return "ok";
/*      */       } 
/*      */       
/*  367 */       boolean bool1 = checkXmlFile(paramDocument.asXML(), paramString2);
/*      */       
/*  369 */       Element element1 = getElement(paramString2);
/*      */ 
/*      */       
/*  372 */       if (bool1) {
/*  373 */         return null;
/*      */       }
/*  375 */       List list2 = paramDocument.selectNodes(paramString1);
/*  376 */       if (list2 != null && list2.size() == 1) {
/*  377 */         String str = modyElement(paramString1, paramString2, list2, paramDocument);
/*  378 */         if (str != null) {
/*  379 */           return str;
/*      */         }
/*      */       } 
/*      */       
/*  383 */       Element element2 = null;
/*      */       
/*  385 */       if (paramString2.indexOf("servlet") > -1) {
/*  386 */         element2 = (Element)element1.selectSingleNode("/myroot/servlet/servlet-name");
/*  387 */       } else if (paramString2.indexOf("filter") > -1) {
/*  388 */         element2 = (Element)element1.selectSingleNode("/myroot/filter/filter-name");
/*      */       } 
/*  390 */       if (element2 != null) {
/*  391 */         String str = element2.asXML();
/*  392 */         ArrayList<Element> arrayList1 = (ArrayList)paramDocument.getRootElement().elements();
/*  393 */         for (byte b = 0; b < arrayList1.size(); b++) {
/*      */           
/*  395 */           Element element = arrayList1.get(b);
/*  396 */           bool1 = checkXmlFile(element.asXML(), element2.asXML());
/*  397 */           if (bool1) {
/*      */             
/*  399 */             Element element3 = element.getParent();
/*  400 */             element3.remove(element);
/*      */           } 
/*      */         } 
/*      */       } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  410 */       writeLog2("" + SystemEnv.getHtmlLabelName(10004227, ThreadVarLanguage.getLang()) + ":" + paramString1);
/*  411 */       String str2 = "";
/*      */       
/*  413 */       List<Element> list = element1.elements();
/*      */       
/*  415 */       if (list.size() > 0) {
/*  416 */         Element element = list.get(0);
/*  417 */         str2 = element.getName();
/*      */       } else {
/*  419 */         str2 = paramString2;
/*      */       } 
/*  421 */       String str3 = getNodeByXpath(paramString1, paramDocument, str2);
/*      */ 
/*      */       
/*  424 */       ArrayList<String> arrayList = addNodeToDocument(paramDocument, str3, element1);
/*      */ 
/*      */       
/*  427 */       boolean bool2 = checkInsert(paramDocument, arrayList, paramString1);
/*  428 */       if (bool2) {
/*  429 */         str1 = "ok";
/*      */       } else {
/*  431 */         str1 = paramString1;
/*      */       } 
/*      */       
/*  434 */       return str1;
/*      */     }
/*  436 */     catch (Exception exception) {
/*  437 */       exception.printStackTrace();
/*  438 */       return "error";
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String modyElement(String paramString1, String paramString2, List<Element> paramList, Document paramDocument) {
/*  446 */     String str = null;
/*      */     
/*  448 */     writeLog2("" + SystemEnv.getHtmlLabelName(10004228, ThreadVarLanguage.getLang()) + ":" + paramString1);
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  453 */     Element element1 = paramList.get(0);
/*  454 */     Element element2 = element1.getParent();
/*  455 */     boolean bool = checkXmlFile(paramDocument.asXML(), paramString2);
/*  456 */     if (!bool) {
/*  457 */       str = "ok";
/*      */ 
/*      */       
/*  460 */       ArrayList<Element> arrayList1 = deleteMappingElement(paramString1, paramDocument);
/*      */       
/*  462 */       int i = element2.elements().indexOf(element1);
/*  463 */       element2.remove(element1);
/*      */ 
/*      */       
/*  466 */       Element element = getElement(paramString2);
/*      */       
/*  468 */       List<Element> list = element.elements();
/*      */       
/*  470 */       ArrayList<Element> arrayList2 = new ArrayList();
/*  471 */       for (byte b = 0; b < list.size(); b++) {
/*  472 */         Element element3 = list.get(b);
/*  473 */         Element element4 = element3.createCopy();
/*      */         
/*  475 */         insertElement(element2, element4, arrayList1, element1, i + b);
/*      */         
/*  477 */         arrayList2.add(element4);
/*      */       } 
/*      */ 
/*      */ 
/*      */       
/*  482 */       List list1 = paramDocument.selectNodes(paramString1);
/*  483 */       if (list1.size() == 0) {
/*      */         
/*  485 */         if (!checkXmlFile(paramDocument.asXML(), element1.asXML())) {
/*  486 */           element2.add(element1.createCopy());
/*      */         }
/*      */         
/*  489 */         for (byte b1 = 0; b1 < arrayList2.size(); b1++) {
/*  490 */           Element element3 = arrayList2.get(b1);
/*  491 */           element2.remove(element3);
/*      */         } 
/*  493 */         str = "" + paramString1;
/*      */       } 
/*      */     } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  505 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Document transformDocument(Document paramDocument) {
/*  513 */     String str = paramDocument.asXML();
/*  514 */     str = str.replaceAll("\\s*?xmlns=\"\"\\s*?", "");
/*      */     
/*  516 */     Document document = Str2Document(str);
/*  517 */     paramDocument = (Document)document.clone();
/*  518 */     return paramDocument;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Element getElement(String paramString) {
/*  527 */     Element element = null;
/*  528 */     Document document = Str2Document("<myroot>" + paramString + "</myroot>");
/*  529 */     if (document != null) {
/*  530 */       element = document.getRootElement();
/*      */     }
/*  532 */     return element;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getNodeByXpath(String paramString1, Document paramDocument, String paramString2) {
/*  542 */     if (paramString1 == null || "".equals(paramString1)) {
/*  543 */       return paramString1;
/*      */     }
/*  545 */     int i = paramString1.lastIndexOf("/");
/*  546 */     paramString1 = paramString1.substring(0, i);
/*      */     
/*      */     try {
/*  549 */       List<Element> list = paramDocument.selectNodes(paramString1);
/*  550 */       if (list.size() > 0) {
/*  551 */         String str = ((Element)list.get(0)).getName();
/*      */         
/*  553 */         if (paramString2 != null && !"".equals(paramString2) && 
/*  554 */           str != null) {
/*  555 */           Object object = null;
/*      */           
/*  557 */           if (paramString2.equals(str)) {
/*  558 */             int j = paramString1.lastIndexOf("/");
/*  559 */             paramString1 = paramString1.substring(0, j);
/*      */           } 
/*      */         } 
/*      */         
/*  563 */         return paramString1;
/*      */       } 
/*  565 */       return getNodeByXpath(paramString1, paramDocument, paramString2);
/*      */     }
/*  567 */     catch (Exception exception) {
/*      */       
/*  569 */       return getNodeByXpath(paramString1, paramDocument, paramString2);
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean checkInsert(Document paramDocument, ArrayList<String> paramArrayList, String paramString) {
/*  583 */     boolean bool = true;
/*  584 */     if (paramArrayList != null && paramArrayList.size() > 0) {
/*      */       
/*  586 */       Collections.sort(paramArrayList, new Comparator<String>()
/*      */           {
/*      */             public int compare(String param1String1, String param1String2) {
/*  589 */               if (param1String1.compareTo(param1String2) > 0) {
/*  590 */                 return -1;
/*      */               }
/*  592 */               return 1;
/*      */             }
/*      */           });
/*  595 */       List list = paramDocument.selectNodes(paramString);
/*  596 */       if (list.size() > 0) {
/*  597 */         bool = true;
/*      */       } else {
/*  599 */         bool = false;
/*      */       } 
/*      */       
/*  602 */       if (!bool) {
/*  603 */         for (byte b = 0; b < paramArrayList.size(); b++) {
/*      */           
/*  605 */           Element element1 = (Element)paramDocument.selectSingleNode(paramArrayList.get(b));
/*  606 */           Element element2 = element1.getParent();
/*  607 */           element2.remove(element1);
/*      */         } 
/*      */       }
/*      */     } 
/*  611 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public ArrayList<String> addNodeToDocument(Document paramDocument, String paramString, Element paramElement) throws DocumentException {
/*  619 */     ArrayList<String> arrayList = null;
/*  620 */     if (paramString == null || "".equals(paramString)) {
/*  621 */       return null;
/*      */     }
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  627 */     List<Element> list1 = paramDocument.selectNodes(paramString);
/*  628 */     List<Element> list2 = paramElement.elements();
/*  629 */     if (list1.size() == 1) {
/*  630 */       arrayList = new ArrayList();
/*  631 */       Element element = list1.get(0);
/*      */       
/*  633 */       if (list2.size() > 0) {
/*  634 */         for (byte b = 0; b < list2.size(); b++) {
/*  635 */           Element element1 = list2.get(b);
/*      */ 
/*      */ 
/*      */           
/*  639 */           Element element2 = element1.createCopy();
/*      */           
/*  641 */           insertElement(element, element2, null, null, -1);
/*      */           
/*  643 */           arrayList.add(element2.getUniquePath());
/*      */         } 
/*      */       } else {
/*      */         
/*  647 */         List list = element.elements();
/*  648 */         if (list.size() > 0) {
/*  649 */           return null;
/*      */         }
/*  651 */         element.setText(paramElement.getTextTrim());
/*  652 */         arrayList.add(paramString);
/*      */       } 
/*      */       
/*  655 */       return arrayList;
/*      */     } 
/*  657 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean insertElement(Element paramElement1, Element paramElement2, ArrayList<Element> paramArrayList, Element paramElement3, int paramInt) {
/*  666 */     if (paramElement1 != null && paramElement2 != null) {
/*  667 */       ArrayList<Element> arrayList1 = (ArrayList)paramElement1.elements();
/*  668 */       if (paramInt < 0) {
/*  669 */         paramInt = arrayList1.size();
/*      */       }
/*      */       
/*  672 */       if (!checkXmlFile(paramElement1.asXML(), paramElement2.asXML()))
/*      */       {
/*      */         
/*  675 */         if (paramInt >= arrayList1.size()) {
/*  676 */           arrayList1.add(createNewElement(paramElement2, paramElement3));
/*      */         } else {
/*  678 */           arrayList1.add(paramInt, createNewElement(paramElement2, paramElement3));
/*      */         } 
/*      */       }
/*      */ 
/*      */       
/*  683 */       if (paramArrayList != null) {
/*  684 */         for (byte b1 = 0; b1 < paramArrayList.size(); b1++) {
/*  685 */           Element element = paramArrayList.get(b1);
/*      */           
/*  687 */           if (!checkXmlFile(paramElement1.asXML(), element.asXML()))
/*      */           {
/*  689 */             if (++paramInt >= arrayList1.size()) {
/*  690 */               arrayList1.add(createNewElement(element, paramElement3).createCopy());
/*      */             } else {
/*  692 */               arrayList1.add(paramInt, createNewElement(element, paramElement3).createCopy());
/*      */             } 
/*      */           }
/*      */         } 
/*      */       }
/*      */       
/*  698 */       ArrayList<Element> arrayList2 = (ArrayList)paramElement1.elements(); byte b;
/*  699 */       for (b = 0; b < arrayList2.size(); b++) {
/*  700 */         paramElement1.remove(arrayList2.get(b));
/*      */       }
/*      */ 
/*      */       
/*  704 */       for (b = 0; b < arrayList1.size(); b++) {
/*  705 */         paramElement1.add(((Element)arrayList1.get(b)).createCopy());
/*      */       }
/*      */     } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  809 */     return true;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void insertcheckcondition(PropertiesUtil paramPropertiesUtil, Element paramElement1, Element paramElement2) {
/*  817 */     ArrayList<String> arrayList = getSpecialEle(paramPropertiesUtil, paramElement2);
/*  818 */     List<Element> list = paramElement1.elements();
/*      */     
/*  820 */     ArrayList<Element> arrayList1 = new ArrayList();
/*  821 */     ArrayList<Element> arrayList2 = new ArrayList();
/*      */     
/*  823 */     if (arrayList.size() > 0) {
/*  824 */       for (byte b = 0; b < arrayList.size(); b++) {
/*  825 */         String str1 = arrayList.get(b);
/*  826 */         String str2 = paramPropertiesUtil.getPropertyVal(str1);
/*  827 */         list = paramElement1.elements();
/*  828 */         arrayList1 = new ArrayList();
/*  829 */         arrayList2 = new ArrayList();
/*  830 */         HashMap<String, ArrayList> hashMap = getCondition(str2);
/*  831 */         ArrayList<String> arrayList3 = hashMap.get("previous");
/*  832 */         ArrayList<String> arrayList4 = hashMap.get("after");
/*  833 */         ArrayList<Element> arrayList5 = new ArrayList(); byte b1;
/*  834 */         for (b1 = 0; b1 < list.size(); b1++) {
/*  835 */           Element element = list.get(b1);
/*      */           
/*  837 */           HashSet hashSet = new HashSet();
/*  838 */           getElementSet(element, hashSet);
/*      */           
/*  840 */           if (hashSet.contains(str1)) {
/*  841 */             arrayList5.add(element);
/*  842 */             paramElement1.remove(element);
/*      */           } 
/*      */ 
/*      */           
/*  846 */           boolean bool = false; byte b2;
/*  847 */           for (b2 = 0; arrayList4 != null && b2 < arrayList4.size(); b2++) {
/*  848 */             String str = arrayList4.get(b2);
/*  849 */             if (str != null && !"".equals(str) && hashSet.contains(str)) {
/*  850 */               arrayList2.add(element);
/*  851 */               bool = true;
/*      */             } 
/*      */           } 
/*      */           
/*  855 */           if (!bool)
/*      */           {
/*      */             
/*  858 */             for (b2 = 0; arrayList3 != null && b2 < arrayList3.size(); b2++) {
/*  859 */               String str = arrayList3.get(b2);
/*  860 */               if (str != null && !"".equals(str) && hashSet.contains(str)) {
/*  861 */                 arrayList1.add(element);
/*      */                 
/*  863 */                 paramElement1.remove(element);
/*      */               } 
/*      */             } 
/*      */           }
/*      */         } 
/*      */ 
/*      */ 
/*      */         
/*  871 */         for (b1 = 0; b1 < arrayList5.size(); b1++) {
/*  872 */           Element element = arrayList5.get(b1);
/*  873 */           paramElement1.add(element.createCopy());
/*      */         } 
/*      */         
/*  876 */         if (arrayList1.size() > 0) {
/*  877 */           for (b1 = 0; b1 < arrayList1.size(); b1++) {
/*  878 */             Element element = arrayList1.get(b1);
/*  879 */             boolean bool = checkXmlFile(paramElement1.asXML(), element.asXML());
/*  880 */             if (!bool)
/*      */             {
/*  882 */               paramElement1.add(element.createCopy());
/*      */             }
/*      */           } 
/*      */         }
/*      */       } 
/*      */     }
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public ArrayList<String> getSpecialEle(PropertiesUtil paramPropertiesUtil, Element paramElement) {
/*  898 */     if ("0".equals(this.autosort)) {
/*  899 */       return new ArrayList<>();
/*      */     }
/*  901 */     ArrayList<String> arrayList = new ArrayList();
/*  902 */     List<String> list = paramPropertiesUtil.getKeys();
/*      */     
/*  904 */     HashSet hashSet = new HashSet();
/*  905 */     getElementSet(paramElement, hashSet);
/*      */     
/*  907 */     for (byte b = 0; b < list.size(); b++) {
/*  908 */       String str1 = list.get(b);
/*  909 */       String str2 = paramPropertiesUtil.getPropertyVal(str1);
/*      */       
/*  911 */       String[] arrayOfString = str2.split(",");
/*  912 */       for (byte b1 = 0; b1 < arrayOfString.length; b1++) {
/*  913 */         String str3 = arrayOfString[b1];
/*  914 */         String[] arrayOfString1 = str3.split("@");
/*  915 */         String str4 = "";
/*  916 */         if (arrayOfString1.length == 2) {
/*  917 */           str4 = arrayOfString1[1];
/*  918 */           if (hashSet.contains(str4)) {
/*  919 */             arrayList.add(str1);
/*      */           }
/*      */         } 
/*      */       } 
/*      */     } 
/*  924 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getConditionStr(PropertiesUtil paramPropertiesUtil, Element paramElement) {
/*  933 */     if ("0".equals(this.autosort)) {
/*  934 */       return "";
/*      */     }
/*  936 */     List<String> list = paramPropertiesUtil.getKeys();
/*  937 */     String str = null;
/*  938 */     HashSet hashSet = new HashSet();
/*  939 */     getElementSet(paramElement, hashSet);
/*      */ 
/*      */     
/*  942 */     for (byte b = 0; b < list.size(); b++) {
/*  943 */       String str1 = list.get(b);
/*  944 */       String str2 = paramPropertiesUtil.getPropertyVal(str1);
/*      */ 
/*      */ 
/*      */       
/*  948 */       if (hashSet.contains(str1)) {
/*  949 */         str = str2;
/*  950 */         this.elementKey = str1;
/*      */         break;
/*      */       } 
/*      */     } 
/*  954 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public HashMap<String, ArrayList> getCondition(String paramString) {
/*  963 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  964 */     String[] arrayOfString = paramString.split(",");
/*  965 */     for (byte b = 0; b < arrayOfString.length; b++) {
/*  966 */       String str = arrayOfString[b];
/*  967 */       String[] arrayOfString1 = str.split("@");
/*  968 */       if (arrayOfString1.length == 2) {
/*  969 */         String str1 = arrayOfString1[0];
/*  970 */         String str2 = arrayOfString1[1];
/*  971 */         ArrayList<String> arrayList = (ArrayList)hashMap.get(str1);
/*  972 */         if (arrayList == null || arrayList.size() == 0) {
/*  973 */           arrayList = new ArrayList();
/*  974 */           arrayList.add(str2);
/*      */         } else {
/*  976 */           arrayList.add(str2);
/*      */         } 
/*      */         
/*  979 */         hashMap.put(str1, arrayList);
/*      */       } 
/*      */     } 
/*  982 */     return (HashMap)hashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String deleteConfig(String paramString1, String paramString2, String paramString3) {
/*  991 */     if (paramString1 != null) {
/*      */       try {
/*  993 */         Document document = this.doc;
/*      */         
/*  995 */         String str = paramString3 + "/parent::*";
/*  996 */         Element element = (Element)document.selectSingleNode(str);
/*  997 */         if (element != null) {
/*      */ 
/*      */           
/* 1000 */           deleteMappingElement(paramString3, document);
/*      */           
/* 1002 */           Element element1 = (Element)document.selectSingleNode(paramString3);
/* 1003 */           if (element1 != null) {
/* 1004 */             element.remove(element1);
/*      */           }
/*      */ 
/*      */           
/* 1008 */           String str1 = element.asXML();
/* 1009 */           paramString1 = paramString1.replaceAll("\\\\r|\\\\n", "");
/* 1010 */           str1 = str1.replaceAll("\\s*?xmlns=\"\"\\s*?", "");
/* 1011 */           str1 = str1.replaceAll("\r|\n", "").replaceAll(">\\s+", ">").replaceAll("\\s+<", "<").replaceAll("\\s\\s+", " ");
/* 1012 */           Element element2 = Str2Document("<myroot>" + paramString1 + "</myroot>").getRootElement();
/* 1013 */           List<Element> list = element2.elements();
/* 1014 */           if (list.size() > 0) {
/* 1015 */             for (byte b = 0; b < list.size(); b++) {
/* 1016 */               Element element3 = list.get(b);
/* 1017 */               String str2 = element3.asXML();
/* 1018 */               str2 = str2.replaceAll("\\s*?xmlns=\"\"\\s*?", "");
/* 1019 */               str2 = str2.replaceAll("\n|\r", "").replaceAll(">\\s+", ">").replaceAll("\\s+<", "<").replaceAll("\\s", " ");
/*      */               
/* 1021 */               if (str1.contains(str2)) {
/* 1022 */                 str1 = str1.replace(str2, "");
/*      */               }
/*      */             }
/*      */           
/* 1026 */           } else if (!element2.getTextTrim().equals("") && 
/* 1027 */             str1.contains(element2.getTextTrim())) {
/* 1028 */             str1 = str1.replace(element2.getTextTrim(), "");
/*      */           } 
/*      */ 
/*      */ 
/*      */           
/* 1033 */           if (str1 == null || "".equals(str1)) {
/*      */             
/* 1035 */             Element element3 = element.getParent();
/* 1036 */             if (element3 == null) {
/* 1037 */               document.remove(element);
/*      */             } else {
/* 1039 */               element3.remove(element);
/*      */             } 
/*      */           } else {
/* 1042 */             Document document1 = Str2Document(str1);
/* 1043 */             Element element3 = document1.getRootElement();
/*      */ 
/*      */             
/* 1046 */             Element element4 = element.getParent();
/* 1047 */             if (element4 == null) {
/* 1048 */               document.remove(element);
/* 1049 */               document.add(element3.createCopy());
/*      */             } else {
/* 1051 */               element4.remove(element);
/* 1052 */               element4.add(element3.createCopy());
/*      */             } 
/*      */           } 
/*      */           
/* 1056 */           writeXml(paramString2, document);
/*      */         } 
/*      */         
/* 1059 */         return "ok";
/* 1060 */       } catch (Exception exception) {
/* 1061 */         exception.printStackTrace();
/* 1062 */         return "no";
/*      */       } 
/*      */     }
/* 1065 */     return "ok";
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Document deleteConfig(Document paramDocument, String paramString1, String paramString2, String paramString3) {
/* 1078 */     if (paramString1 != null) {
/*      */       try {
/* 1080 */         XMLUtil xMLUtil = new XMLUtil();
/*      */         
/* 1082 */         Element element1 = Str2Document("<myroot>" + paramString1 + "</myroot>").getRootElement();
/* 1083 */         List<Element> list = element1.elements();
/* 1084 */         for (byte b = 0; b < list.size(); b++) {
/* 1085 */           Element element3 = list.get(b);
/* 1086 */           String str1 = element3.asXML();
/* 1087 */           String str2 = xMLUtil.generateXPathByContent(str1);
/* 1088 */           Element element4 = (Element)paramDocument.selectSingleNode(str2);
/* 1089 */           if (element4 != null) {
/* 1090 */             deleteMappingElement(str2, paramDocument);
/* 1091 */             element4.getParent().remove(element4);
/*      */           } 
/*      */         } 
/*      */ 
/*      */ 
/*      */         
/* 1097 */         String str = paramString3 + "/parent::*";
/* 1098 */         Element element2 = (Element)paramDocument.selectSingleNode(str);
/* 1099 */         if (element2 != null) {
/*      */           
/* 1101 */           deleteMappingElement(paramString3, paramDocument);
/*      */           
/* 1103 */           Element element = (Element)paramDocument.selectSingleNode(paramString3);
/* 1104 */           if (element != null) {
/* 1105 */             element2.remove(element);
/*      */           }
/*      */ 
/*      */           
/* 1109 */           String str1 = element2.asXML();
/* 1110 */           paramString1 = paramString1.replaceAll("\\\\r|\\\\n", "");
/* 1111 */           str1 = str1.replaceAll("\\s*?xmlns=\"\"\\s*?", "");
/* 1112 */           str1 = str1.replaceAll("\r|\n", "").replaceAll(">\\s+", ">").replaceAll("\\s+<", "<").replaceAll("\\s\\s+", " ");
/*      */           
/* 1114 */           if (list.size() > 0) {
/* 1115 */             for (byte b1 = 0; b1 < list.size(); b1++) {
/* 1116 */               Element element3 = list.get(b1);
/* 1117 */               String str2 = element3.asXML();
/* 1118 */               str2 = str2.replaceAll("\\s*?xmlns=\"\"\\s*?", "");
/* 1119 */               str2 = str2.replaceAll("\n|\r", "").replaceAll(">\\s+", ">").replaceAll("\\s+<", "<").replaceAll("\\s", " ");
/*      */               
/* 1121 */               if (str1.contains(str2)) {
/* 1122 */                 str1 = str1.replace(str2, "");
/*      */               
/*      */               }
/*      */             }
/*      */           
/*      */           }
/* 1128 */           else if (!element1.getTextTrim().equals("") && 
/* 1129 */             str1.contains(element1.getTextTrim())) {
/* 1130 */             str1 = str1.replace(element1.getTextTrim(), "");
/*      */           } 
/*      */ 
/*      */ 
/*      */           
/* 1135 */           if (str1 == null || "".equals(str1)) {
/*      */             
/* 1137 */             Element element3 = element2.getParent();
/* 1138 */             if (element3 == null) {
/* 1139 */               paramDocument.remove(element2);
/*      */             } else {
/* 1141 */               element3.remove(element2);
/*      */             } 
/*      */           } else {
/* 1144 */             Document document = Str2Document(str1);
/* 1145 */             Element element3 = document.getRootElement();
/*      */ 
/*      */             
/* 1148 */             Element element4 = element2.getParent();
/* 1149 */             if (element4 == null) {
/* 1150 */               paramDocument.remove(element2);
/* 1151 */               paramDocument.add(element3.createCopy());
/*      */             } else {
/* 1153 */               element4.remove(element2);
/* 1154 */               element4.add(element3.createCopy());
/*      */             } 
/*      */           } 
/*      */         } else {
/* 1158 */           deleteConfigByContent(paramDocument, paramString1);
/*      */         } 
/*      */         
/* 1161 */         return paramDocument;
/* 1162 */       } catch (Exception exception) {
/* 1163 */         exception.printStackTrace();
/* 1164 */         return null;
/*      */       } 
/*      */     }
/* 1167 */     return paramDocument;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Document deleteConfigByContent(Document paramDocument, String paramString) {
/* 1178 */     if (paramString != null) {
/*      */       try {
/* 1180 */         if (paramDocument != null) {
/*      */           
/* 1182 */           Element element1 = paramDocument.getRootElement();
/* 1183 */           String str = element1.asXML();
/* 1184 */           paramString = paramString.replaceAll("\\\\r|\\\\n", "");
/* 1185 */           str = str.replaceAll("\\s*?xmlns=\"\"\\s*?", "");
/* 1186 */           str = str.replaceAll("\r|\n", "").replaceAll(">\\s+", ">").replaceAll("\\s+<", "<").replaceAll("\\s\\s+", " ");
/* 1187 */           Element element2 = Str2Document("<myroot>" + paramString + "</myroot>").getRootElement();
/* 1188 */           List<Element> list = element2.elements();
/* 1189 */           boolean bool = false;
/* 1190 */           if (list.size() > 0) {
/* 1191 */             for (byte b = 0; b < list.size(); b++) {
/* 1192 */               Element element = list.get(b);
/* 1193 */               String str1 = element.asXML();
/*      */               
/* 1195 */               str1 = str1.replaceAll("\\s*?xmlns=\"\"\\s*?", "");
/* 1196 */               str1 = str1.replaceAll("\n|\r", "").replaceAll(">\\s+", ">").replaceAll("\\s+<", "<").replaceAll("\\s", " ");
/*      */               
/* 1198 */               if (str.contains(str1)) {
/* 1199 */                 str = str.replace(str1, "");
/* 1200 */                 bool = true;
/*      */               }
/*      */             
/*      */             } 
/* 1204 */           } else if (!element2.getTextTrim().equals("") && 
/* 1205 */             str.contains(element2.getTextTrim())) {
/* 1206 */             str = str.replace(element2.getTextTrim(), "");
/* 1207 */             bool = true;
/*      */           } 
/*      */ 
/*      */           
/* 1211 */           if (bool) {
/* 1212 */             Document document = Str2Document(str);
/* 1213 */             Element element = document.getRootElement();
/*      */             
/* 1215 */             paramDocument.remove(element1);
/*      */             
/* 1217 */             paramDocument.add(element.createCopy());
/*      */           } 
/*      */         } 
/*      */         
/* 1221 */         return paramDocument;
/* 1222 */       } catch (Exception exception) {
/* 1223 */         exception.printStackTrace();
/* 1224 */         return null;
/*      */       } 
/*      */     }
/* 1227 */     return paramDocument;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public ArrayList<Element> deleteMappingElement(String paramString, Document paramDocument) {
/* 1236 */     ArrayList<Element> arrayList = new ArrayList();
/* 1237 */     if (paramString.indexOf("filter") > -1 || paramString.indexOf("servlet") > -1) {
/* 1238 */       List<Element> list = paramDocument.selectNodes(paramString);
/* 1239 */       if (list.size() == 1) {
/* 1240 */         Element element1 = list.get(0);
/* 1241 */         String str1 = element1.getName();
/* 1242 */         String str2 = "";
/* 1243 */         String str3 = "";
/* 1244 */         if (str1.equals("filter")) {
/* 1245 */           str2 = "filter-mapping/filter-name";
/* 1246 */           str3 = element1.elementTextTrim("filter-name");
/* 1247 */         } else if (str1.equals("servlet")) {
/* 1248 */           str2 = "servlet-mapping/servlet-name";
/* 1249 */           str3 = element1.elementTextTrim("servlet-name");
/*      */         } else {
/* 1251 */           return null;
/*      */         } 
/*      */         
/* 1254 */         Element element2 = element1.getParent();
/* 1255 */         String str4 = element2.getUniquePath();
/* 1256 */         String str5 = str4 + "/" + str2 + "[text()='" + str3 + "']/parent::*";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/* 1266 */         List list1 = element2.elements();
/* 1267 */         for (Element element : list1) {
/* 1268 */           if (replaceAllBlank(element.asXML().toLowerCase()).contains(("<filter-mapping><filter-name>" + str3.trim() + "</filter-name>").toLowerCase()) || 
/* 1269 */             replaceAllBlank(element.asXML().toLowerCase()).contains(("<servlet-mapping><servlet-name>" + str3.trim() + "</servlet-name>").toLowerCase())) {
/*      */             
/* 1271 */             arrayList.add(element);
/* 1272 */             element2.remove(element);
/*      */           } 
/*      */         } 
/*      */       } 
/*      */     } 
/* 1277 */     return arrayList;
/*      */   }
/*      */ 
/*      */   
/*      */   public boolean checkMapping(Element paramElement1, Element paramElement2) {
/* 1282 */     String str = paramElement1.asXML();
/* 1283 */     boolean bool = false;
/*      */     
/* 1285 */     if (str.contains("mapping")) {
/* 1286 */       String str1 = paramElement2.asXML();
/*      */       
/* 1288 */       Element element1 = null;
/* 1289 */       Element element2 = null;
/*      */       
/* 1291 */       if (str.contains("filter") && str1.contains("filter")) {
/* 1292 */         element1 = paramElement1.element("filter-name");
/* 1293 */         element2 = paramElement2.element("filter-name");
/* 1294 */       } else if (str.contains("servlet") && str1.contains("servlet")) {
/* 1295 */         element1 = paramElement1.element("servlet-name");
/* 1296 */         element2 = paramElement2.element("servlet-name");
/*      */       } 
/*      */       
/* 1299 */       if (element1 != null && element2 != null)
/*      */       {
/*      */         
/* 1302 */         if (element1.getTextTrim().equals(element2.getTextTrim())) {
/* 1303 */           bool = true;
/*      */         }
/*      */       }
/*      */     } 
/*      */ 
/*      */ 
/*      */     
/* 1310 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String deleteConfig(String paramString1, Document paramDocument, String paramString2) {
/*      */     try {
/* 1322 */       if (paramString1 != null && !"".equals(paramString1)) {
/*      */ 
/*      */         
/* 1325 */         Element element = paramDocument.getRootElement();
/* 1326 */         List<Element> list = paramDocument.selectNodes(paramString1);
/*      */         
/* 1328 */         if (list.size() >= 1) {
/* 1329 */           int i = paramString1.lastIndexOf("/");
/* 1330 */           String str = paramString1.substring(0, i);
/*      */           
/* 1332 */           Element element1 = paramDocument.getRootElement();
/* 1333 */           Element element2 = (Element)element1.selectSingleNode(str);
/* 1334 */           for (byte b = 0; b < list.size(); b++) {
/* 1335 */             element2.remove(list.get(b));
/*      */           }
/*      */ 
/*      */ 
/*      */           
/* 1340 */           writeXml(paramString2, paramDocument);
/* 1341 */           return "ok";
/*      */         } 
/* 1343 */         return "no";
/*      */       } 
/*      */       
/* 1346 */       return "no";
/*      */     
/*      */     }
/* 1349 */     catch (Exception exception) {
/* 1350 */       exception.printStackTrace();
/* 1351 */       return "xpatherror";
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean checkXmlFile(String paramString1, String paramString2) {
/* 1363 */     Element element = Str2Document(paramString1).getRootElement();
/*      */     
/* 1365 */     paramString2 = paramString2.replaceAll("&lt;", "<");
/* 1366 */     paramString2 = paramString2.replaceAll("&gt;", ">");
/*      */     
/* 1368 */     paramString2 = paramString2.replaceAll("&nbsp;", " ");
/*      */     
/* 1370 */     paramString2 = paramString2.replaceAll("&quot;", "\"");
/* 1371 */     paramString2 = paramString2.replaceAll("&apos;", "'");
/* 1372 */     boolean bool = true;
/*      */ 
/*      */     
/* 1375 */     paramString1 = replaceNotesAndWhitespace(paramString1);
/*      */     try {
/* 1377 */       paramString2 = paramString2.replaceAll("\\\\r|\\\\n", "");
/* 1378 */       Element element1 = Str2Document("<myroot>" + paramString2 + "</myroot>").getRootElement();
/* 1379 */       List<Element> list = element1.elements();
/*      */       
/* 1381 */       int i = list.size();
/* 1382 */       if (i > 0) {
/* 1383 */         for (byte b = 0; b < list.size(); b++) {
/* 1384 */           Element element2 = list.get(b);
/* 1385 */           String str = element2.asXML();
/* 1386 */           str = replaceNotesAndWhitespace(str);
/*      */           
/* 1388 */           if (!paramString1.contains(str))
/*      */           {
/* 1390 */             bool = checkElementByLine(element2, paramString1, element);
/* 1391 */             if (!bool) {
/*      */               break;
/*      */             }
/*      */           }
/*      */         
/*      */         }
/*      */       
/*      */       }
/*      */       else {
/*      */         
/* 1401 */         String str = element1.getTextTrim();
/* 1402 */         str = replaceNotesAndWhitespace(str);
/*      */         
/* 1404 */         if (!paramString1.contains(str)) {
/* 1405 */           bool = checkElementByLine(element1, paramString1, element);
/* 1406 */           return bool;
/*      */         } 
/*      */       } 
/* 1409 */     } catch (Exception exception) {
/* 1410 */       exception.printStackTrace();
/*      */     } 
/*      */     
/* 1413 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String replaceNotesAndWhitespace(String paramString) {
/* 1422 */     if (paramString != null) {
/* 1423 */       paramString = paramString.replaceAll("(?s)<\\!\\-\\-.+?\\-\\->", "");
/* 1424 */       paramString = paramString.replaceAll("xmlns=\"\"", "");
/* 1425 */       paramString = paramString.replaceAll("\n|\r", "").replaceAll(">\\s+", ">").replaceAll("\\s+<", "<").replaceAll("\\s", "");
/*      */     } 
/*      */     
/* 1428 */     return paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void writeXml(String paramString, Document paramDocument) {
/* 1439 */     FileOutputStream fileOutputStream = null;
/* 1440 */     XMLWriter xMLWriter = null;
/*      */     try {
/* 1442 */       String str = "";
/* 1443 */       if ("".equals(this.fileEncode) || this.fileEncode == null) {
/* 1444 */         File file = new File(this.fileUtil.getPath(paramString));
/* 1445 */         if (file.exists()) {
/* 1446 */           boolean bool = FileCharsetDetector.check(file);
/* 1447 */           if (bool) {
/* 1448 */             str = "UTF-8";
/*      */           } else {
/* 1450 */             str = "GBK";
/*      */           } 
/*      */         } 
/*      */       } else {
/* 1454 */         str = this.fileEncode;
/*      */       } 
/*      */       
/* 1457 */       fileOutputStream = new FileOutputStream(this.fileUtil.getPath(paramString));
/* 1458 */       OutputFormat outputFormat = OutputFormat.createPrettyPrint();
/* 1459 */       outputFormat.setEncoding(str);
/* 1460 */       outputFormat.setIndent(true);
/* 1461 */       outputFormat.setIndent("    ");
/* 1462 */       xMLWriter = new XMLWriter(fileOutputStream, outputFormat);
/* 1463 */       xMLWriter.write(paramDocument);
/*      */       
/* 1465 */       fileOutputStream.flush();
/* 1466 */       xMLWriter.flush();
/* 1467 */       if (fileOutputStream != null) {
/* 1468 */         fileOutputStream.close();
/*      */       }
/* 1470 */       if (xMLWriter != null)
/*      */       {
/* 1472 */         xMLWriter.close();
/*      */       }
/* 1474 */     } catch (Exception exception) {
/* 1475 */       exception.printStackTrace();
/*      */     } finally {
/* 1477 */       if (fileOutputStream != null) {
/*      */         try {
/* 1479 */           fileOutputStream.close();
/* 1480 */         } catch (IOException iOException) {
/* 1481 */           iOException.printStackTrace();
/*      */         } 
/*      */       }
/* 1484 */       if (xMLWriter != null) {
/*      */         try {
/* 1486 */           xMLWriter.close();
/* 1487 */         } catch (IOException iOException) {
/* 1488 */           iOException.printStackTrace();
/*      */         } 
/*      */       }
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Document Str2Document(String paramString) {
/* 1502 */     SAXReader sAXReader = new SAXReader();
/* 1503 */     sAXReader.setEntityResolver(new IgnoreDTDEntityResolver());
/* 1504 */     Document document = null;
/*      */     try {
/* 1506 */       document = sAXReader.read(new ByteArrayInputStream(paramString.getBytes("UTF-8")));
/* 1507 */     } catch (Exception exception) {
/* 1508 */       exception.printStackTrace();
/*      */     } 
/* 1510 */     return document;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String changeStr2(String paramString) {
/* 1517 */     paramString = paramString.replaceAll("&lt;", "<");
/* 1518 */     paramString = paramString.replaceAll("&gt;", ">");
/*      */     
/* 1520 */     paramString = paramString.replaceAll("&nbsp;", " ");
/*      */     
/* 1522 */     paramString = paramString.replaceAll("&quot;", "\"");
/* 1523 */     paramString = paramString.replaceAll("&apos;", "'");
/* 1524 */     paramString = paramString.replaceAll("\\\\r|\\\\n", "");
/*      */     
/* 1526 */     return paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static HashSet getElementSet(Element paramElement, HashSet<String> paramHashSet) {
/* 1535 */     paramHashSet.add(paramElement.getName());
/* 1536 */     List<Element> list = paramElement.elements();
/* 1537 */     if (list.size() > 0) {
/* 1538 */       for (byte b = 0; b < list.size(); b++) {
/* 1539 */         getElementSet(list.get(b), paramHashSet);
/*      */       }
/*      */     } else {
/* 1542 */       paramHashSet.add(paramElement.getTextTrim());
/*      */     } 
/*      */     
/* 1545 */     return paramHashSet;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Element createNewElement(Element paramElement1, Element paramElement2) {
/* 1555 */     if (paramElement2 == null) {
/* 1556 */       return paramElement1.createCopy();
/*      */     }
/* 1558 */     String str = paramElement1.asXML();
/* 1559 */     if (str.indexOf("init-param") > -1) {
/* 1560 */       HashMap<String, String> hashMap1 = analysisInitParam(paramElement1);
/* 1561 */       HashMap<String, String> hashMap2 = analysisInitParam(paramElement2);
/*      */       
/* 1563 */       Iterator<Map.Entry> iterator = hashMap1.entrySet().iterator();
/* 1564 */       LinkedHashSet<String> linkedHashSet = new LinkedHashSet();
/* 1565 */       while (iterator.hasNext()) {
/* 1566 */         Map.Entry entry = iterator.next();
/* 1567 */         String str1 = (String)entry.getKey();
/* 1568 */         String str2 = (String)entry.getValue();
/* 1569 */         LinkedHashSet<String> linkedHashSet1 = convert2Set(str2);
/* 1570 */         String str3 = hashMap2.get(str1);
/* 1571 */         String str4 = getSplitChar(str3);
/* 1572 */         linkedHashSet = convert2Set(hashMap2.get(str1));
/* 1573 */         Iterator<String> iterator1 = linkedHashSet.iterator();
/*      */         
/* 1575 */         while (iterator1.hasNext()) {
/* 1576 */           String str6 = iterator1.next();
/* 1577 */           if (linkedHashSet1.size() == 1 && linkedHashSet.size() == 1) {
/*      */             continue;
/*      */           }
/* 1580 */           if (!linkedHashSet1.contains(str6)) {
/* 1581 */             linkedHashSet1.add(str6);
/*      */           }
/*      */         } 
/*      */ 
/*      */         
/* 1586 */         if (str1.indexOf("$") > -1) {
/* 1587 */           str1 = str1.substring(str1.indexOf("$") + 1, str1.length());
/*      */         }
/* 1589 */         Document document = Str2Document(paramElement1.asXML());
/* 1590 */         String str5 = "/" + paramElement1.getName() + "/init-param/param-name[text()='" + str1 + "']";
/*      */         
/* 1592 */         Element element = (Element)document.selectSingleNode(str5);
/*      */         
/* 1594 */         if (element != null) {
/*      */ 
/*      */           
/* 1597 */           Element element1 = element.getParent();
/* 1598 */           List<Element> list = element1.selectNodes(element1.getUniquePath() + "/param-value");
/* 1599 */           if (list.size() == 1) {
/* 1600 */             Element element2 = list.get(0);
/* 1601 */             element2.setText(String.join(str4, (Iterable)linkedHashSet1));
/*      */           } 
/*      */           
/* 1604 */           paramElement1 = document.getRootElement().createCopy();
/*      */         } 
/*      */       } 
/*      */     } 
/* 1608 */     return paramElement1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean checkElementByLine(Element paramElement1, String paramString, Element paramElement2) {
/* 1615 */     boolean bool = true;
/*      */     
/* 1617 */     String str = paramElement1.asXML();
/*      */     
/* 1619 */     if (str.indexOf("init-param") > -1) {
/* 1620 */       if (str != null) {
/* 1621 */         HashMap<String, String> hashMap = analysisInitParam(paramElement1);
/* 1622 */         HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 1623 */         List<Element> list = paramElement2.elements(paramElement1.getName());
/* 1624 */         for (byte b1 = 0; b1 < list.size(); b1++) {
/* 1625 */           Element element = list.get(b1);
/* 1626 */           hashMap1.putAll(analysisInitParam(element));
/*      */         } 
/*      */         
/* 1629 */         Iterator<Map.Entry> iterator = hashMap.entrySet().iterator();
/*      */         
/* 1631 */         while (iterator.hasNext()) {
/* 1632 */           Map.Entry entry = iterator.next();
/* 1633 */           String str1 = (String)entry.getKey();
/* 1634 */           String str2 = (String)entry.getValue();
/* 1635 */           LinkedHashSet<String> linkedHashSet1 = convert2Set(str2);
/* 1636 */           LinkedHashSet<String> linkedHashSet2 = convert2Set((String)hashMap1.get(str1));
/*      */           
/* 1638 */           if (!linkedHashSet2.containsAll(linkedHashSet1)) {
/* 1639 */             bool = false;
/*      */             break;
/*      */           } 
/*      */         } 
/* 1643 */         if (!bool) {
/* 1644 */           return bool;
/*      */         }
/*      */ 
/*      */         
/* 1648 */         String[] arrayOfString = str.split(">");
/* 1649 */         Object object = null;
/* 1650 */         for (byte b2 = 0; b2 < arrayOfString.length; b2++) {
/* 1651 */           String str1 = arrayOfString[b2];
/* 1652 */           str1 = str1 + ">";
/* 1653 */           str1 = str1.replaceAll("xmlns=\"\"", "");
/* 1654 */           str1 = str1.replaceAll("\n|\r", "").replaceAll(">\\s+", ">").replaceAll("\\s+<", "<").replaceAll("\\s", "");
/*      */           
/* 1656 */           if (str1.indexOf("param-value") <= -1)
/*      */           {
/*      */             
/* 1659 */             if (!paramString.contains(str1)) {
/* 1660 */               bool = false;
/*      */               
/*      */               break;
/*      */             } 
/*      */           }
/*      */         } 
/*      */       } 
/*      */     } else {
/* 1668 */       bool = false;
/*      */     } 
/*      */     
/* 1671 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void writeLog2(String paramString) {
/* 1680 */     this.suc.log(paramString);
/*      */   }
/*      */ 
/*      */   
/*      */   public String getFileEncode() {
/* 1685 */     return this.fileEncode;
/*      */   }
/*      */ 
/*      */   
/*      */   public void setFileEncode(String paramString) {
/* 1690 */     this.fileEncode = paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public HashMap<String, String> analysisInitParam(Element paramElement) {
/* 1700 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 1701 */     List<Element> list = paramElement.elements("init-param");
/* 1702 */     Object object = null;
/* 1703 */     String str = "";
/* 1704 */     if (list.size() > 0) {
/* 1705 */       if (paramElement.asXML().indexOf("servlet-name") > -1) {
/* 1706 */         str = paramElement.elementTextTrim("servlet-name");
/* 1707 */       } else if (paramElement.asXML().indexOf("filter-name") > -1) {
/* 1708 */         str = paramElement.elementTextTrim("filter-name");
/*      */       } 
/*      */     }
/*      */     
/* 1712 */     if (str != null && !"".equals(str) && list != null) {
/* 1713 */       for (byte b = 0; b < list.size(); b++) {
/* 1714 */         Element element = list.get(b);
/* 1715 */         String str1 = element.elementTextTrim("param-name");
/* 1716 */         String str2 = element.elementTextTrim("param-value");
/* 1717 */         hashMap.put(str + "$" + str1, str2);
/*      */       } 
/*      */     }
/* 1720 */     return (HashMap)hashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public LinkedHashSet<String> convert2Set(String paramString) {
/* 1729 */     String str = ";";
/* 1730 */     LinkedHashSet<String> linkedHashSet = new LinkedHashSet();
/* 1731 */     if (paramString != null) {
/* 1732 */       str = getSplitChar(paramString);
/* 1733 */       String[] arrayOfString = paramString.split(str);
/* 1734 */       for (byte b = 0; b < arrayOfString.length; b++) {
/* 1735 */         if (arrayOfString[b] != null && !"".equals(arrayOfString[b])) {
/* 1736 */           linkedHashSet.add(arrayOfString[b]);
/*      */         }
/*      */       } 
/*      */     } 
/* 1740 */     return linkedHashSet;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getSplitChar(String paramString) {
/* 1748 */     String str = ";";
/* 1749 */     if (paramString != null) {
/* 1750 */       if (paramString.indexOf("||") > -1) {
/* 1751 */         str = "||";
/* 1752 */       } else if (paramString.indexOf("|") > -1 && paramString.indexOf("||") < 0) {
/* 1753 */         str = "|";
/* 1754 */       } else if (paramString.indexOf(",") > -1) {
/* 1755 */         str = ",";
/*      */       } 
/*      */     }
/*      */     
/* 1759 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Document sortElement(Document paramDocument) {
/* 1768 */     if (paramDocument != null) {
/* 1769 */       Element element = paramDocument.getRootElement();
/* 1770 */       if (element != null) {
/* 1771 */         if ("".equals(this.autosort) || this.autosort == null) {
/* 1772 */           PropertiesUtil propertiesUtil = new PropertiesUtil();
/* 1773 */           propertiesUtil.load(GCONST.getRootPath() + "WEB-INF" + File.separatorChar + "prop" + File.separatorChar + "autoconfigsetting.properties");
/* 1774 */           this.autosort = propertiesUtil.getPropertyVal("autosort");
/*      */         } 
/*      */         
/* 1777 */         if ("0".equals(this.autosort)) {
/* 1778 */           return paramDocument;
/*      */         }
/*      */         
/* 1781 */         ArrayList<Element> arrayList1 = (ArrayList)element.elements();
/* 1782 */         ArrayList<Element> arrayList2 = sortByCondition(arrayList1);
/* 1783 */         if (arrayList1.size() != arrayList2.size()) {
/* 1784 */           writeLog2("" + SystemEnv.getHtmlLabelName(10004229, ThreadVarLanguage.getLang()) + "...");
/*      */         }
/*      */         byte b;
/* 1787 */         for (b = 0; b < arrayList1.size(); b++) {
/* 1788 */           element.remove(arrayList1.get(b));
/*      */         }
/*      */ 
/*      */         
/* 1792 */         for (b = 0; b < arrayList2.size(); b++) {
/* 1793 */           Element element1 = arrayList2.get(b);
/* 1794 */           Element element2 = element1.createCopy();
/* 1795 */           if (!checkXmlFile(element.asXML(), element2.asXML())) {
/* 1796 */             element.add(element2);
/*      */           }
/* 1798 */           if (element1.getName().equals("filter") || element1.getName().equals("servlet")) {
/* 1799 */             checkMappingOrder(element2, element);
/*      */           }
/*      */         } 
/*      */       } 
/*      */     } 
/* 1804 */     return paramDocument;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void checkMappingOrder(Element paramElement1, Element paramElement2) {
/* 1813 */     if (paramElement2 != null) {
/*      */       
/* 1815 */       ArrayList<Element> arrayList1 = new ArrayList();
/* 1816 */       List<Element> list = paramElement1.elements();
/* 1817 */       ArrayList<Element> arrayList2 = (ArrayList)paramElement2.elements();
/*      */       
/* 1819 */       int i = arrayList2.indexOf(paramElement1);
/* 1820 */       String str = "";
/*      */       byte b;
/* 1822 */       for (b = 0; b < list.size(); b++) {
/* 1823 */         if (((Element)list.get(b)).getName().equalsIgnoreCase("filter-name") || ((Element)list.get(b)).getName().equalsIgnoreCase("servlet-name")) {
/* 1824 */           str = ((Element)list.get(b)).getTextTrim();
/*      */           
/*      */           break;
/*      */         } 
/*      */       } 
/*      */       
/* 1830 */       if (str != null && !"".equals(str)) {
/* 1831 */         for (b = 0; b < arrayList2.size(); b++) {
/* 1832 */           Element element = arrayList2.get(b);
/* 1833 */           if (replaceAllBlank(element.asXML().toLowerCase()).contains(("<filter-mapping><filter-name>" + str.trim().toLowerCase() + "</filter-name>").toLowerCase()) || 
/* 1834 */             replaceAllBlank(element.asXML().toLowerCase()).contains(("<servlet-mapping><servlet-name>" + str.trim().toLowerCase() + "</servlet-name>").toLowerCase())) {
/* 1835 */             arrayList1.add(element);
/*      */           }
/*      */         } 
/*      */ 
/*      */         
/* 1840 */         for (b = 0; b < arrayList1.size(); b++) {
/* 1841 */           Element element = arrayList1.get(b);
/* 1842 */           int j = arrayList2.indexOf(element);
/*      */ 
/*      */ 
/*      */           
/* 1846 */           if (j > -1 && j < i) {
/* 1847 */             paramElement2.remove(element);
/* 1848 */             paramElement2.add(element);
/* 1849 */             arrayList2 = (ArrayList<Element>)paramElement2.elements();
/* 1850 */             i = arrayList2.indexOf(paramElement1);
/*      */           } 
/*      */         } 
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public ArrayList<Element> sortByCondition(ArrayList<Element> paramArrayList) {
/* 1864 */     ArrayList<Element> arrayList = new ArrayList();
/* 1865 */     if (paramArrayList != null) {
/*      */       
/* 1867 */       PropertiesUtil propertiesUtil1 = new PropertiesUtil();
/* 1868 */       propertiesUtil1.load(GCONST.getRootPath() + "templetecheck" + File.separatorChar + "xml" + File.separatorChar + "condition.properties");
/*      */ 
/*      */       
/* 1871 */       String str = GCONST.getRootPath() + "templetecheck" + File.separatorChar + "xml" + File.separatorChar + "condition_cus.properties";
/* 1872 */       File file = new File(str);
/* 1873 */       PropertiesUtil propertiesUtil2 = new PropertiesUtil();
/* 1874 */       if (file.exists()) {
/* 1875 */         propertiesUtil2.load(str);
/*      */       }
/* 1877 */       List<String> list = propertiesUtil2.getKeys();
/* 1878 */       for (byte b1 = 0; b1 < list.size(); b1++) {
/* 1879 */         String str1 = list.get(b1);
/* 1880 */         if (propertiesUtil1.containsKey(str1)) {
/* 1881 */           propertiesUtil1.remove(str1);
/*      */         }
/* 1883 */         propertiesUtil1.put(str1, propertiesUtil2.getPropertyVal(str1), propertiesUtil2.getPropertyNotes(str1));
/*      */       } 
/*      */ 
/*      */ 
/*      */       
/* 1888 */       LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/* 1889 */       for (byte b2 = 0; b2 < paramArrayList.size(); b2++) {
/* 1890 */         Element element = paramArrayList.get(b2);
/* 1891 */         HashSet hashSet = new HashSet();
/* 1892 */         getElementSet(element, hashSet);
/* 1893 */         linkedHashMap.put(element, hashSet);
/*      */       } 
/*      */       
/* 1896 */       Iterator<Map.Entry> iterator = linkedHashMap.entrySet().iterator();
/*      */       
/* 1898 */       while (iterator.hasNext()) {
/* 1899 */         Map.Entry entry = iterator.next();
/* 1900 */         Element element = (Element)entry.getKey();
/*      */ 
/*      */         
/* 1903 */         if (arrayList.contains(element)) {
/* 1904 */           System.out.println("已存在请继续！");
/*      */         }
/*      */ 
/*      */         
/* 1908 */         String str1 = getConditionStr(propertiesUtil1, element);
/* 1909 */         ArrayList<Element> arrayList1 = new ArrayList();
/* 1910 */         ArrayList<Element> arrayList2 = new ArrayList();
/* 1911 */         if (str1 != null && !"".equals(str1)) {
/* 1912 */           HashMap<String, ArrayList> hashMap = getCondition(str1);
/* 1913 */           ArrayList<String> arrayList3 = hashMap.get("after");
/* 1914 */           ArrayList<String> arrayList4 = hashMap.get("previous");
/* 1915 */           if (arrayList3 == null) {
/* 1916 */             arrayList3 = new ArrayList();
/*      */           }
/* 1918 */           if (arrayList4 == null) {
/* 1919 */             arrayList4 = new ArrayList();
/*      */           }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */           
/* 1926 */           List<String> list1 = propertiesUtil1.getKeys();
/* 1927 */           boolean bool = false; int i;
/* 1928 */           for (i = 0; i < list1.size(); i++) {
/* 1929 */             String str2 = list1.get(i);
/* 1930 */             String str3 = propertiesUtil1.get(str2);
/* 1931 */             if (str1.equals(str3)) {
/* 1932 */               if (str2.equals(this.elementKey)) {
/* 1933 */                 bool = true;
/*      */               }
/* 1935 */               if (!bool) {
/* 1936 */                 arrayList4.add(str2);
/*      */               } else {
/* 1938 */                 arrayList3.add(str2);
/*      */               } 
/*      */             } 
/*      */           } 
/*      */ 
/*      */           
/* 1944 */           if (arrayList3 == null || arrayList3.size() == 0) {
/* 1945 */             if (!arrayList.contains(element)) {
/* 1946 */               arrayList.add(element);
/*      */             }
/*      */ 
/*      */ 
/*      */ 
/*      */             
/*      */             continue;
/*      */           } 
/*      */ 
/*      */ 
/*      */           
/* 1957 */           arrayList1.addAll(getConditionElementByList((LinkedHashMap)linkedHashMap, arrayList3));
/*      */ 
/*      */ 
/*      */           
/* 1961 */           if (arrayList4 != null && arrayList4.size() > 0)
/*      */           {
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */             
/* 1968 */             arrayList2.addAll(getConditionElementByList((LinkedHashMap)linkedHashMap, arrayList4));
/*      */           }
/*      */ 
/*      */ 
/*      */ 
/*      */           
/* 1974 */           i = -1; int j;
/* 1975 */           for (j = 0; j < arrayList2.size(); j++) {
/*      */             
/* 1977 */             if (arrayList1.indexOf(arrayList2.get(j)) <= -1) {
/*      */ 
/*      */               
/* 1980 */               int k = arrayList.indexOf(arrayList2.get(j));
/*      */               
/* 1982 */               if (k > -1 && (i == -1 || i > k)) {
/* 1983 */                 i = k;
/*      */               }
/*      */             } 
/*      */           } 
/*      */ 
/*      */ 
/*      */           
/* 1990 */           if (i == -1) {
/* 1991 */             if (arrayList.size() > 0) {
/* 1992 */               i = arrayList.size();
/*      */             } else {
/* 1994 */               i = 0;
/*      */             } 
/*      */           }
/*      */ 
/*      */ 
/*      */           
/* 2000 */           if (!arrayList.contains(element)) {
/* 2001 */             arrayList.add(i, element);
/*      */           } else {
/*      */             
/* 2004 */             j = arrayList.indexOf(element);
/* 2005 */             if (i < j) {
/*      */               
/* 2007 */               arrayList.remove(element);
/* 2008 */               arrayList.add(i, element);
/*      */             } 
/*      */           } 
/*      */           
/* 2012 */           ArrayList<Element> arrayList5 = new ArrayList(); byte b;
/* 2013 */           for (b = 0; b < arrayList1.size(); b++) {
/*      */             
/* 2015 */             if (!arrayList.contains(arrayList1.get(b))) {
/* 2016 */               arrayList5.add(arrayList1.get(b));
/*      */             } else {
/* 2018 */               Element element1 = arrayList1.get(b);
/* 2019 */               int k = arrayList.indexOf(element1);
/* 2020 */               int m = arrayList.indexOf(element);
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */               
/* 2027 */               if (k > m) {
/*      */ 
/*      */ 
/*      */                 
/* 2031 */                 arrayList.remove(element1);
/* 2032 */                 arrayList.add(m, element1);
/*      */               } 
/*      */             } 
/*      */           } 
/*      */ 
/*      */           
/* 2038 */           for (b = 0; b < arrayList5.size(); b++)
/*      */           {
/*      */ 
/*      */             
/* 2042 */             arrayList.add(i + b, arrayList5.get(b));
/*      */           }
/*      */ 
/*      */           
/*      */           continue;
/*      */         } 
/*      */         
/* 2049 */         if (!arrayList.contains(element)) {
/* 2050 */           arrayList.add(element);
/*      */         }
/*      */       } 
/*      */     } 
/*      */ 
/*      */ 
/*      */     
/* 2057 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public ArrayList<Element> getConditionElement(LinkedHashMap<Element, HashSet> paramLinkedHashMap, String paramString) {
/* 2067 */     Iterator<Map.Entry> iterator = paramLinkedHashMap.entrySet().iterator();
/* 2068 */     ArrayList<Element> arrayList = new ArrayList();
/* 2069 */     while (iterator.hasNext()) {
/* 2070 */       Map.Entry entry = iterator.next();
/* 2071 */       HashSet hashSet = (HashSet)entry.getValue();
/*      */       
/* 2073 */       if (hashSet.contains(paramString)) {
/* 2074 */         if (this.elementKey != null && !"".equals(this.elementKey)) {
/* 2075 */           if (!hashSet.contains(this.elementKey))
/* 2076 */             arrayList.add(entry.getKey()); 
/*      */           continue;
/*      */         } 
/* 2079 */         arrayList.add(entry.getKey());
/*      */       } 
/*      */     } 
/*      */ 
/*      */ 
/*      */     
/* 2085 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public ArrayList<Element> getConditionElementByList(LinkedHashMap<Element, HashSet> paramLinkedHashMap, ArrayList<String> paramArrayList) {
/* 2095 */     Iterator<Map.Entry> iterator = paramLinkedHashMap.entrySet().iterator();
/* 2096 */     ArrayList<Element> arrayList = new ArrayList();
/* 2097 */     while (iterator.hasNext()) {
/* 2098 */       Map.Entry entry = iterator.next();
/* 2099 */       HashSet hashSet = (HashSet)entry.getValue();
/*      */       
/* 2101 */       for (byte b = 0; b < paramArrayList.size(); b++) {
/* 2102 */         String str = paramArrayList.get(b);
/* 2103 */         if (hashSet.contains(str)) {
/* 2104 */           if (this.elementKey != null && !"".equals(this.elementKey)) {
/* 2105 */             if (!hashSet.contains(this.elementKey)) {
/* 2106 */               arrayList.add(entry.getKey());
/*      */               break;
/*      */             } 
/*      */           } else {
/* 2110 */             arrayList.add(entry.getKey());
/*      */             
/*      */             break;
/*      */           } 
/*      */         }
/*      */       } 
/*      */     } 
/*      */     
/* 2118 */     return arrayList;
/*      */   }
/*      */   
/*      */   public static String replaceAllBlank(String paramString) {
/* 2122 */     String str = "";
/* 2123 */     if (paramString != null) {
/* 2124 */       Pattern pattern = Pattern.compile("\\s*|\t|\r|\n");
/* 2125 */       Matcher matcher = pattern.matcher(paramString);
/* 2126 */       str = matcher.replaceAll("");
/*      */     } 
/* 2128 */     return str;
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/selectXmlNodeUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */