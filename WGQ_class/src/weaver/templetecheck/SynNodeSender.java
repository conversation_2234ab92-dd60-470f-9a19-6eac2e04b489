/*     */ package weaver.templetecheck;
/*     */ 
/*     */ import java.io.InputStream;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Map;
/*     */ import java.util.Set;
/*     */ import java.util.concurrent.ConcurrentHashMap;
/*     */ import org.apache.http.HttpEntity;
/*     */ import org.apache.http.client.config.RequestConfig;
/*     */ import org.apache.http.client.entity.UrlEncodedFormEntity;
/*     */ import org.apache.http.client.methods.CloseableHttpResponse;
/*     */ import org.apache.http.client.methods.HttpPost;
/*     */ import org.apache.http.client.methods.HttpUriRequest;
/*     */ import org.apache.http.impl.client.CloseableHttpClient;
/*     */ import org.apache.http.impl.client.HttpClients;
/*     */ import org.apache.http.message.BasicNameValuePair;
/*     */ import org.apache.http.util.EntityUtils;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SynNodeSender
/*     */ {
/*     */   private static SynNodeSender synNode;
/*  26 */   private ConcurrentHashMap threadmap = new ConcurrentHashMap<>();
/*     */ 
/*     */   
/*     */   public static SynNodeSender getInstance() {
/*  30 */     if (synNode == null) {
/*  31 */       synNode = new SynNodeSender();
/*     */     }
/*  33 */     return synNode;
/*     */   }
/*     */   
/*     */   public String sync(String paramString, Map<String, String> paramMap) {
/*  37 */     HttpPost httpPost = new HttpPost(paramString);
/*  38 */     CloseableHttpClient closeableHttpClient = HttpClients.createDefault();
/*  39 */     String str = null;
/*     */     
/*     */     try {
/*  42 */       ArrayList<BasicNameValuePair> arrayList = new ArrayList();
/*  43 */       if (paramMap != null && paramMap.size() > 0) {
/*  44 */         Set<String> set = paramMap.keySet();
/*  45 */         for (String str1 : set)
/*     */         {
/*  47 */           arrayList.add(new BasicNameValuePair(str1, paramMap.get(str1)));
/*     */         }
/*     */       } 
/*     */ 
/*     */ 
/*     */       
/*  53 */       RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(5000).setConnectionRequestTimeout(1000).setSocketTimeout(30000).build();
/*  54 */       httpPost.setConfig(requestConfig);
/*     */       
/*  56 */       httpPost.setEntity((HttpEntity)new UrlEncodedFormEntity(arrayList, "UTF-8"));
/*  57 */       CloseableHttpResponse closeableHttpResponse = closeableHttpClient.execute((HttpUriRequest)httpPost);
/*  58 */       str = EntityUtils.toString(closeableHttpResponse.getEntity());
/*  59 */     } catch (Exception exception) {
/*  60 */       exception.printStackTrace();
/*     */     } finally {
/*     */       try {
/*  63 */         if (closeableHttpClient != null) {
/*  64 */           closeableHttpClient.close();
/*     */         }
/*  66 */       } catch (Exception exception) {
/*  67 */         exception.printStackTrace();
/*     */       } 
/*     */     } 
/*     */     
/*  71 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void postByThread(final String serverUrl, Map<String, String> paramMap, String paramString2) {
/*  81 */     (new Thread(paramString2)
/*     */       {
/*     */         public void run() {
/*  84 */           HttpPost httpPost = new HttpPost(serverUrl);
/*  85 */           CloseableHttpClient closeableHttpClient = HttpClients.createDefault();
/*  86 */           String str = null;
/*     */           try {
/*  88 */             CloseableHttpResponse closeableHttpResponse = closeableHttpClient.execute((HttpUriRequest)httpPost);
/*  89 */             str = EntityUtils.toString(closeableHttpResponse.getEntity());
/*  90 */           } catch (Exception exception) {
/*  91 */             exception.printStackTrace();
/*     */           } finally {
/*     */             try {
/*  94 */               if (closeableHttpClient != null) {
/*  95 */                 closeableHttpClient.close();
/*     */               }
/*  97 */             } catch (Exception exception) {}
/*     */           
/*     */           }
/*     */         
/*     */         }
/* 102 */       }).start();
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static interface HttpClientDownLoadProgress
/*     */   {
/*     */     void onProgress(int param1Int);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private String getRespString(HttpEntity paramHttpEntity) throws Exception {
/* 115 */     if (paramHttpEntity == null) {
/* 116 */       return null;
/*     */     }
/* 118 */     InputStream inputStream = paramHttpEntity.getContent();
/* 119 */     StringBuffer stringBuffer = new StringBuffer();
/* 120 */     byte[] arrayOfByte = new byte[4096];
/* 121 */     int i = 0;
/* 122 */     while ((i = inputStream.read(arrayOfByte)) > 0) {
/* 123 */       stringBuffer.append(new String(arrayOfByte, 0, i, "UTF-8"));
/*     */     }
/* 125 */     return stringBuffer.toString();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/SynNodeSender.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */