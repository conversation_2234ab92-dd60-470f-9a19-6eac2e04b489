/*     */ package weaver.templetecheck;
/*     */ 
/*     */ import java.io.File;
/*     */ import java.io.FileOutputStream;
/*     */ import java.util.Iterator;
/*     */ import java.util.LinkedHashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import org.jdom.Content;
/*     */ import org.jdom.Document;
/*     */ import org.jdom.Element;
/*     */ import org.jdom.input.SAXBuilder;
/*     */ import org.jdom.output.Format;
/*     */ import org.jdom.output.XMLOutputter;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.upgradetool.UpgradeFileUtil;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class RulePath
/*     */   extends BaseBean
/*     */ {
/*  25 */   public LinkedHashMap<String, String> pointArrayList = new LinkedHashMap<>();
/*  26 */   private String filename = GCONST.getRootPath() + "templetecheck" + File.separatorChar + "rulepath.xml";
/*     */   public Element rootNodeElement;
/*  28 */   public String rulesid = "";
/*  29 */   UpgradeFileUtil fileUtil = new UpgradeFileUtil();
/*     */   
/*     */   public RulePath() {
/*  32 */     SAXBuilder sAXBuilder = new SAXBuilder();
/*     */     
/*  34 */     File file = new File(this.fileUtil.getPath(this.filename));
/*  35 */     if (!file.canWrite()) {
/*  36 */       file.setWritable(true);
/*     */     }
/*     */     
/*     */     try {
/*  40 */       Document document = sAXBuilder.build(file);
/*  41 */       this.rootNodeElement = document.getRootElement();
/*  42 */     } catch (Exception exception) {
/*  43 */       exception.printStackTrace();
/*     */     } 
/*  45 */     init();
/*     */   }
/*     */   
/*     */   private void init() {
/*  49 */     this.rulesid = this.rootNodeElement.getAttributeValue("id");
/*     */     
/*  51 */     List list = this.rootNodeElement.getChildren("path");
/*     */     
/*  53 */     for (Element element : list) {
/*     */       
/*  55 */       String str1 = element.getAttributeValue("id");
/*  56 */       String str2 = element.getTextTrim();
/*  57 */       this.pointArrayList.put(str1, str2);
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean changeTab(String paramString) {
/*  63 */     Document document = new Document();
/*  64 */     Element element = new Element("rules");
/*  65 */     element.setAttribute("id", "rule");
/*  66 */     element.setAttribute("version", "1.0.0");
/*     */     
/*  68 */     Iterator<Map.Entry> iterator = this.pointArrayList.entrySet().iterator();
/*  69 */     while (iterator.hasNext()) {
/*  70 */       Element element1 = new Element("path");
/*  71 */       Map.Entry entry = iterator.next();
/*  72 */       String str1 = (String)entry.getKey();
/*  73 */       String str2 = (String)entry.getValue();
/*  74 */       element1.setAttribute("id", "" + str1);
/*  75 */       if (paramString.indexOf(str1) > -1) {
/*  76 */         element1.addContent(str2);
/*     */ 
/*     */ 
/*     */ 
/*     */         
/*  81 */         element.addContent((Content)element1);
/*     */       } 
/*     */     } 
/*  84 */     document.addContent((Content)element);
/*     */ 
/*     */ 
/*     */     
/*     */     try {
/*  89 */       String str = GCONST.XML_UTF8;
/*     */       
/*  91 */       Format format = Format.getCompactFormat();
/*  92 */       format.setEncoding(str);
/*  93 */       format.setIndent("    ");
/*  94 */       XMLOutputter xMLOutputter = new XMLOutputter(format);
/*  95 */       FileOutputStream fileOutputStream = new FileOutputStream(this.fileUtil.getPath(this.filename));
/*  96 */       xMLOutputter.output(document, fileOutputStream);
/*  97 */       fileOutputStream.flush();
/*  98 */       fileOutputStream.close();
/*     */     }
/* 100 */     catch (Exception exception) {
/* 101 */       exception.printStackTrace();
/* 102 */       return false;
/*     */     } 
/* 104 */     return true;
/*     */   }
/*     */   
/*     */   public boolean savePath(String paramString1, String paramString2) {
/* 108 */     Document document = new Document();
/* 109 */     Element element = new Element("rules");
/* 110 */     element.setAttribute("id", "rule");
/* 111 */     element.setAttribute("version", "1.0.0");
/*     */     
/* 113 */     Iterator<Map.Entry> iterator = this.pointArrayList.entrySet().iterator();
/* 114 */     while (iterator.hasNext()) {
/* 115 */       Element element1 = new Element("path");
/* 116 */       Map.Entry entry = iterator.next();
/* 117 */       String str1 = (String)entry.getKey();
/* 118 */       String str2 = (String)entry.getValue();
/* 119 */       element1.setAttribute("id", "" + str1);
/* 120 */       if (paramString1.equals(str1)) {
/* 121 */         element1.addContent(paramString2);
/*     */       } else {
/* 123 */         element1.addContent(str2);
/*     */       } 
/*     */       
/* 126 */       element.addContent((Content)element1);
/*     */     } 
/*     */ 
/*     */     
/* 130 */     if (!this.pointArrayList.containsKey(paramString1) && paramString2 != null && !"".equals(paramString2)) {
/* 131 */       Element element1 = new Element("path");
/* 132 */       element1.setAttribute("id", "" + paramString1);
/* 133 */       element1.addContent(paramString2);
/* 134 */       element.addContent((Content)element1);
/*     */     } 
/* 136 */     document.addContent((Content)element);
/*     */ 
/*     */ 
/*     */     
/*     */     try {
/* 141 */       String str = GCONST.XML_UTF8;
/* 142 */       File file = new File(this.fileUtil.getPath(this.filename));
/* 143 */       if (!file.canWrite()) {
/* 144 */         file.setWritable(true);
/*     */       }
/*     */       
/* 147 */       Format format = Format.getCompactFormat();
/* 148 */       format.setEncoding(str);
/* 149 */       format.setIndent("    ");
/* 150 */       XMLOutputter xMLOutputter = new XMLOutputter(format);
/* 151 */       FileOutputStream fileOutputStream = new FileOutputStream(this.fileUtil.getPath(this.filename));
/* 152 */       xMLOutputter.output(document, fileOutputStream);
/* 153 */       fileOutputStream.flush();
/* 154 */       fileOutputStream.close();
/*     */     }
/* 156 */     catch (Exception exception) {
/* 157 */       exception.printStackTrace();
/* 158 */       return false;
/*     */     } 
/* 160 */     return true;
/*     */   }
/*     */   
/*     */   public String getpath(String paramString) {
/* 164 */     String str = this.pointArrayList.get(paramString);
/* 165 */     if (str != null && !"".equals(str)) {
/* 166 */       return str;
/*     */     }
/* 168 */     if ("checkwebxml".equals(paramString)) {
/* 169 */       str = GCONST.getRootPath() + "WEB-INF" + File.separatorChar + "web.xml";
/* 170 */       savePath(paramString, str);
/*     */     } 
/* 172 */     if ("checkaction".equals(paramString)) {
/* 173 */       str = GCONST.getRootPath() + "WEB-INF" + File.separatorChar + "service" + File.separatorChar + "action.xml";
/* 174 */       savePath(paramString, str);
/*     */     } 
/* 176 */     if ("checkservice".equals(paramString)) {
/* 177 */       str = GCONST.getRootPath() + "classbean" + File.separatorChar + "META-INF" + File.separatorChar + "xfire" + File.separatorChar + "services.xml";
/* 178 */       savePath(paramString, str);
/*     */     } 
/* 180 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/RulePath.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */