/*     */ package weaver.templetecheck.filecheck;
/*     */ import java.io.File;
/*     */ import java.text.SimpleDateFormat;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.LinkedList;
/*     */ import java.util.List;
/*     */ import java.util.concurrent.CountDownLatch;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.templetecheck.FileUtil;
/*     */ 
/*     */ public class CheckWorkflowThread extends Thread {
/*  15 */   private String threadName = "";
/*  16 */   private String ruleid = "";
/*  17 */   private String workflowids = "";
/*  18 */   private int startHtmlLayoutId = 0;
/*  19 */   private int endHtmlLayoutId = 0;
/*  20 */   public static int fileNum = 0;
/*  21 */   public static int fileNotExistNum = 0;
/*  22 */   private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
/*     */   private static CountDownLatch countDownLatch;
/*  24 */   private static int checkedHtmlNum = 0;
/*  25 */   private static int totolHtmlNum = 0;
/*     */   
/*     */   public int getFileNum() {
/*  28 */     return fileNum;
/*     */   }
/*     */   
/*     */   public int getFileNotExistNum() {
/*  32 */     return fileNotExistNum;
/*     */   }
/*     */   
/*     */   public synchronized void fileNumAdd() {
/*  36 */     this; fileNum++;
/*     */   }
/*     */   
/*     */   public synchronized void fileNotExistNumAdd() {
/*  40 */     this; fileNotExistNum++;
/*     */   }
/*     */   
/*     */   public int getCheckedHtmlNum() {
/*  44 */     return checkedHtmlNum;
/*     */   }
/*     */   
/*     */   public void setCheckedHtmlNum(int paramInt) {
/*  48 */     this; checkedHtmlNum = paramInt;
/*     */   }
/*     */   
/*     */   public synchronized void checkedHtmlNumAdd() {
/*  52 */     this; checkedHtmlNum++;
/*     */   }
/*     */   
/*     */   public int getTotolHtmlNum() {
/*  56 */     return totolHtmlNum;
/*     */   }
/*     */   
/*     */   public void setTotolHtmlNum(int paramInt) {
/*  60 */     this; totolHtmlNum = paramInt;
/*     */   }
/*     */   
/*     */   public CountDownLatch getCountDownLatch() {
/*  64 */     return countDownLatch;
/*     */   }
/*     */   
/*     */   private synchronized void countDownLatchDown() {
/*  68 */     countDownLatch.countDown();
/*     */   }
/*     */   
/*     */   public static void setCountDownLatch(CountDownLatch paramCountDownLatch) {
/*  72 */     countDownLatch = paramCountDownLatch;
/*     */   }
/*     */   
/*  75 */   public static Map<String, String> wfErrMap = new HashMap<>();
/*     */   
/*  77 */   private static String lastChecktime = "";
/*     */   
/*     */   public String getLastChecktime() {
/*  80 */     return lastChecktime;
/*     */   }
/*     */   
/*     */   public void setLastChecktime(String paramString) {
/*  84 */     this; lastChecktime = paramString;
/*     */   }
/*     */ 
/*     */   
/*     */   public CheckWorkflowThread() {}
/*     */ 
/*     */   
/*     */   public CheckWorkflowThread(String paramString1, String paramString2, String paramString3, int paramInt1, int paramInt2) {
/*  92 */     this.threadName = paramString1;
/*  93 */     this.ruleid = paramString2;
/*  94 */     this.workflowids = paramString3;
/*  95 */     this.startHtmlLayoutId = paramInt1;
/*  96 */     this.endHtmlLayoutId = paramInt2;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void run() {
/* 102 */     RecordSet recordSet = new RecordSet();
/* 103 */     recordSet.writeLog("开始执行线程" + this.threadName);
/* 104 */     ArrayList<RuleBean> arrayList = new ArrayList();
/*     */     
/* 106 */     if ("".equals(this.ruleid) || this.ruleid == null) {
/* 107 */       recordSet.execute("select * from upgradecheckworkflowrule order by id");
/*     */     } else {
/* 109 */       if (this.ruleid.endsWith(",")) {
/* 110 */         this.ruleid = this.ruleid.substring(0, this.ruleid.length() - 1);
/*     */       }
/* 112 */       recordSet.execute("select * from upgradecheckworkflowrule where id in (" + this.ruleid + ") order by id");
/*     */     } 
/* 114 */     while (recordSet.next()) {
/* 115 */       RuleBean ruleBean = new RuleBean();
/* 116 */       ruleBean.setId(recordSet.getString("id"));
/* 117 */       ruleBean.setRulename(recordSet.getString("rulename"));
/* 118 */       ruleBean.setRuledesc(recordSet.getString("ruledesc"));
/* 119 */       ruleBean.setRuletype(recordSet.getString("ruletype"));
/* 120 */       ruleBean.setChecktype(recordSet.getString("checktype"));
/* 121 */       ruleBean.setContent(recordSet.getString("content"));
/* 122 */       ruleBean.setReplacecontent(recordSet.getString("replacecontent"));
/* 123 */       ruleBean.setVersion(recordSet.getString("version"));
/* 124 */       arrayList.add(ruleBean);
/*     */     } 
/*     */     
/* 127 */     String str1 = "";
/* 128 */     String str2 = "";
/* 129 */     str1 = "SELECT t3.workflowname as workflowname,type,t2.nodename as nodename,syspath,datajson,pluginjson,scripts,t.id as nodehtmllayoutid,t.WORKFLOWID as WORKFLOWID,t.NODEID as NODEID from workflow_nodehtmllayout t,workflow_nodebase t2,workflow_base t3,workflow_flownode t4  ";
/* 130 */     str2 = " AND  t.nodeid=t2.id and t.nodeid=t4.nodeid and t.workflowid=t4.workflowid and t.workflowid=t3.id and t.isactive='1' and t3.isvalid in (1,2) and t4.ismode=2  AND (t2.requestid is null or t2.requestid='')";
/* 131 */     String str3 = "";
/* 132 */     if (!"".equals(this.workflowids)) {
/* 133 */       str3 = " WHERE t.workflowid in (" + this.workflowids + ")";
/* 134 */       checkExecuteSql(str1 + str3 + str2, arrayList);
/*     */     } else {
/* 136 */       int i; for (i = this.startHtmlLayoutId; i < this.endHtmlLayoutId; i += 3000) {
/* 137 */         int j = (i + 3000 > this.endHtmlLayoutId) ? this.endHtmlLayoutId : (i + 3000);
/* 138 */         str3 = " WHERE t.id>=" + i + " and t.id<" + j;
/* 139 */         checkExecuteSql(str1 + str3 + str2, arrayList);
/*     */       } 
/*     */     } 
/* 142 */     countDownLatchDown();
/*     */   }
/*     */   
/*     */   private void checkExecuteSql(String paramString, ArrayList<RuleBean> paramArrayList) {
/* 146 */     RecordSet recordSet = new RecordSet();
/* 147 */     FileUtil fileUtil = new FileUtil();
/* 148 */     ExcelSecurity excelSecurity = new ExcelSecurity();
/* 149 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 150 */     String str1 = "";
/* 151 */     String str2 = "";
/* 152 */     String str3 = "";
/* 153 */     String str4 = "";
/* 154 */     CheckRule checkRule = new CheckRule();
/* 155 */     paramString = paramString + " order by t.workflowid,t.nodeid ";
/* 156 */     recordSet.writeLog(this.threadName + "检查的sql为：" + paramString);
/* 157 */     recordSet.execute(paramString);
/* 158 */     while (recordSet.next()) {
/* 159 */       String str5 = Util.null2String(recordSet.getString("workflowname"));
/* 160 */       String str6 = Util.null2String(recordSet.getString("nodename"));
/* 161 */       String str7 = Util.null2String(recordSet.getString("syspath"));
/* 162 */       String str8 = Util.null2String(recordSet.getString("WORKFLOWID"));
/* 163 */       String str9 = Util.null2String(recordSet.getString("NODEID"));
/* 164 */       hashMap.put("workflowname", str5);
/* 165 */       hashMap.put("nodename", str6);
/* 166 */       hashMap.put("filepath", str7);
/* 167 */       String str10 = recordSet.getString("nodehtmllayoutid");
/* 168 */       hashMap.put("nodehtmllayoutid", str10);
/* 169 */       recordSet.writeLog("###--" + this.threadName + "开始检查 流程名称：" + str5 + "--节点名称：" + str6);
/* 170 */       recordSet.writeLog("###--" + this.threadName + "开始检查 文件路径：" + str7);
/* 171 */       String str11 = recordSet.getString("datajson");
/* 172 */       String str12 = recordSet.getString("scripts");
/* 173 */       checkedHtmlNumAdd();
/*     */ 
/*     */       
/* 176 */       if (str11 == null || "".equals(str11)) {
/*     */ 
/*     */         
/* 179 */         if ("".equals(str7)) {
/*     */           continue;
/*     */         }
/* 182 */         File file = new File(str7);
/* 183 */         fileNumAdd();
/* 184 */         RecordSet recordSet2 = new RecordSet();
/* 185 */         if (file.exists()) {
/*     */           
/* 187 */           String str14 = this.dateFormat.format(Long.valueOf(file.lastModified()));
/* 188 */           if (str14.compareTo(lastChecktime) <= 0) {
/*     */             
/* 190 */             recordSet2.executeSql("select id,filepath from upgradecheckworkflowresult where nodehtmllayoutid ='" + str10 + "'");
/* 191 */             if (recordSet2.next()) {
/* 192 */               String str = Util.null2String(recordSet2.getString("filepath"));
/* 193 */               if ("".equals(str) || str.replaceAll("\\\\+", "/").equals(str7)) {
/* 194 */                 recordSet2.writeLog(this.threadName + "fileLastModified=" + str14 + "-lastChecktime-" + lastChecktime + ",不需要检查");
/*     */                 
/*     */                 continue;
/*     */               } 
/*     */             } 
/*     */           } 
/* 200 */           recordSet2.executeSql("delete from upgradecheckworkflowresult where nodehtmllayoutid ='" + str10 + "'");
/* 201 */           StringBuffer stringBuffer = fileUtil.readFile(file);
/* 202 */           String str15 = stringBuffer.toString();
/* 203 */           hashMap.put("modetype", "0");
/*     */           
/* 205 */           if (str3.equals(str8) && 
/* 206 */             str1.trim().equals(str15.trim())) {
/* 207 */             ArrayList<String> arrayList = new ArrayList();
/* 208 */             recordSet2.executeQuery("select id from upgradecheckworkflowresult where nodehtmllayoutid ='" + str4 + "'", new Object[0]);
/* 209 */             while (recordSet2.next()) {
/* 210 */               arrayList.add(recordSet2.getString("id"));
/*     */             }
/* 212 */             List<HashMap<String, String>> list = getDataToMapByIds("upgradecheckworkflowresult", arrayList);
/* 213 */             for (HashMap<String, String> hashMap1 : list) {
/* 214 */               hashMap1.put("nodehtmllayoutid", str10);
/* 215 */               hashMap1.put("nodename", str6);
/* 216 */               hashMap1.put("filepath", str7);
/* 217 */               checkRule.insert2DB(hashMap1, "upgradecheckworkflowresult");
/*     */             } 
/*     */             
/*     */             continue;
/*     */           } 
/*     */           
/* 223 */           str3 = str8;
/* 224 */           str4 = str10;
/* 225 */           str1 = str15;
/* 226 */           checkRule.checkContent("upgradecheckworkflowresult", str15, paramArrayList, (HashMap)hashMap); continue;
/*     */         } 
/* 228 */         recordSet2.executeSql("delete from upgradecheckworkflowresult where nodehtmllayoutid ='" + str10 + "'");
/* 229 */         fileNotExistNumAdd();
/* 230 */         recordSet.writeLog(this.threadName + "文件不存在，请检查：" + str7);
/*     */         
/*     */         continue;
/*     */       } 
/* 234 */       recordSet.writeLog("###--" + this.threadName + "文件为新模板");
/* 235 */       if (str12 != null) {
/* 236 */         str12 = excelSecurity.decode(str12);
/*     */       }
/*     */       
/* 239 */       RecordSet recordSet1 = new RecordSet();
/* 240 */       recordSet1.executeSql("select opertime from workflow_nodehtmllayout where id ='" + str10 + "'");
/* 241 */       String str13 = "";
/* 242 */       if (recordSet1.next()) {
/* 243 */         str13 = recordSet1.getString("opertime");
/*     */       }
/* 245 */       if (str13.compareTo(lastChecktime) <= 0) {
/* 246 */         recordSet1.executeSql("select 1 from upgradecheckworkflowresult where nodehtmllayoutid ='" + str10 + "'");
/* 247 */         if (recordSet1.next()) {
/* 248 */           recordSet1.writeLog(this.threadName + "fileLastModified=" + str13 + "-lastChecktime-" + lastChecktime + ",不需要检查");
/*     */           
/*     */           continue;
/*     */         } 
/*     */       } 
/* 253 */       recordSet1.executeSql("delete from upgradecheckworkflowresult where nodehtmllayoutid ='" + str10 + "'");
/*     */       
/* 255 */       hashMap.put("modetype", "1");
/* 256 */       hashMap.put("refertable", "workflow_nodehtmllayout");
/*     */ 
/*     */       
/* 259 */       if (str3.equals(str8)) {
/* 260 */         if (str2.trim().equals(str12.trim())) {
/*     */           
/* 262 */           ArrayList<String> arrayList = new ArrayList();
/* 263 */           recordSet1.executeQuery("select id from upgradecheckworkflowresult where nodehtmllayoutid ='" + str4 + "'", new Object[0]);
/* 264 */           while (recordSet1.next()) {
/* 265 */             arrayList.add(recordSet1.getString("id"));
/*     */           }
/* 267 */           List<HashMap<String, String>> list = getDataToMapByIds("upgradecheckworkflowresult", arrayList);
/* 268 */           for (HashMap<String, String> hashMap1 : list) {
/* 269 */             hashMap1.put("nodehtmllayoutid", str10);
/* 270 */             hashMap1.put("nodename", str6);
/* 271 */             checkRule.insert2DB(hashMap1, "upgradecheckworkflowresult");
/*     */           } 
/*     */           continue;
/*     */         } 
/*     */       } else {
/* 276 */         str3 = str8;
/* 277 */         str4 = str10;
/* 278 */         str2 = str12;
/*     */       } 
/* 280 */       checkRule.checkContent("upgradecheckworkflowresult", str12, paramArrayList, (HashMap)hashMap);
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public List<HashMap<String, String>> getDataToMapByIds(String paramString, List<String> paramList) {
/* 286 */     LinkedList<HashMap<String, String>> linkedList = new LinkedList();
/*     */     
/* 288 */     String str = StringUtils.join(paramList.toArray(), ",");
/*     */     try {
/* 290 */       if ("".equals(str.trim())) {
/* 291 */         return linkedList;
/*     */       }
/* 293 */       (new BaseBean()).writeLog("开始拷贝表" + paramString + "中id为:" + str + "的记录插入到" + paramString + "表中");
/*     */       
/* 295 */       RecordSet recordSet = new RecordSet();
/* 296 */       recordSet.execute("select * from " + paramString + " where id in(" + str + ")");
/* 297 */       while (recordSet.next()) {
/* 298 */         HashMap<Object, Object> hashMap = new HashMap<>();
/* 299 */         String[] arrayOfString = recordSet.getColumnName();
/* 300 */         for (String str1 : arrayOfString) {
/* 301 */           if (!str1.trim().equalsIgnoreCase("id"))
/* 302 */             hashMap.put(str1.trim().toLowerCase(), recordSet.getString(str1.trim())); 
/*     */         } 
/* 304 */         linkedList.add(hashMap);
/*     */       } 
/* 306 */       (new BaseBean()).writeLog("拷贝表" + paramString + "中id为:" + str + "的记录插入到" + paramString + "表中操作完成");
/* 307 */     } catch (Exception exception) {
/* 308 */       wfErrMap.put("wf-copyerror", "copy" + SystemEnv.getHtmlLabelName(10004093, ThreadVarLanguage.getLang()) + "" + exception.getMessage());
/* 309 */       (new BaseBean()).writeLog("拷贝表" + paramString + "中id为:" + str + "的记录插入到" + paramString + "表中失败,错误信息:" + exception.getMessage());
/* 310 */       exception.printStackTrace();
/*     */     } 
/* 312 */     return linkedList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/filecheck/CheckWorkflowThread.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */