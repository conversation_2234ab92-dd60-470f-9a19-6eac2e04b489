/*    */ package weaver.templetecheck.filecheck;
/*    */ 
/*    */ import weaver.general.BaseBean;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class FileCheckUtil
/*    */ {
/* 10 */   private static String toolVersion = "";
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static String getToolVersion() {
/* 17 */     if (toolVersion == null || "".equals(toolVersion)) {
/* 18 */       BaseBean baseBean = new BaseBean();
/* 19 */       toolVersion = baseBean.getPropValue("filecheckutil", "version");
/*    */     } 
/* 21 */     return toolVersion;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/filecheck/FileCheckUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */