/*    */ package weaver.templetecheck.filecheck;
/*    */ 
/*    */ import org.bouncycastle.crypto.BlockCipher;
/*    */ import org.bouncycastle.crypto.CipherParameters;
/*    */ import org.bouncycastle.crypto.engines.AESFastEngine;
/*    */ import org.bouncycastle.crypto.modes.CBCBlockCipher;
/*    */ import org.bouncycastle.crypto.paddings.PaddedBufferedBlockCipher;
/*    */ import org.bouncycastle.crypto.params.KeyParameter;
/*    */ import org.bouncycastle.crypto.params.ParametersWithIV;
/*    */ import org.bouncycastle.util.encoders.Hex;
/*    */ 
/*    */ public class ExcelSecurity
/*    */ {
/* 14 */   static byte[] keybytes = "WEAVER E-DESIGN.".getBytes();
/* 15 */   static byte[] iv = "weaver e-design.".getBytes();
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String encodeStr(String paramString) {
/*    */     try {
/* 25 */       byte[] arrayOfByte1 = "WEAVER E-DESIGN.".getBytes();
/* 26 */       byte[] arrayOfByte2 = "weaver e-design.".getBytes();
/* 27 */       PaddedBufferedBlockCipher paddedBufferedBlockCipher = new PaddedBufferedBlockCipher((BlockCipher)new CBCBlockCipher((BlockCipher)new AESFastEngine()));
/*    */       
/* 29 */       paddedBufferedBlockCipher.init(true, (CipherParameters)new ParametersWithIV((CipherParameters)new KeyParameter(arrayOfByte1), arrayOfByte2));
/*    */ 
/*    */       
/* 32 */       byte[] arrayOfByte3 = new byte[paddedBufferedBlockCipher.getOutputSize((paramString.getBytes()).length)];
/* 33 */       int i = paddedBufferedBlockCipher.processBytes(paramString.getBytes(), 0, (paramString
/* 34 */           .getBytes()).length, arrayOfByte3, 0);
/* 35 */       int j = paddedBufferedBlockCipher.doFinal(arrayOfByte3, i);
/* 36 */       byte[] arrayOfByte4 = new byte[i + j];
/* 37 */       System.arraycopy(arrayOfByte3, 0, arrayOfByte4, 0, arrayOfByte4.length);
/*    */       
/* 39 */       return new String(Hex.encode(arrayOfByte4));
/* 40 */     } catch (Exception exception) {
/*    */ 
/*    */       
/* 43 */       return "";
/*    */     } 
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public String decode(String paramString) {
/*    */     try {
/* 54 */       PaddedBufferedBlockCipher paddedBufferedBlockCipher = new PaddedBufferedBlockCipher((BlockCipher)new CBCBlockCipher((BlockCipher)new AESFastEngine()));
/*    */       
/* 56 */       paddedBufferedBlockCipher.init(true, (CipherParameters)new ParametersWithIV((CipherParameters)new KeyParameter(keybytes), iv));
/*    */       
/* 58 */       byte[] arrayOfByte1 = Hex.decode(paramString);
/* 59 */       paddedBufferedBlockCipher.init(false, (CipherParameters)new ParametersWithIV((CipherParameters)new KeyParameter(keybytes), iv));
/*    */       
/* 61 */       byte[] arrayOfByte2 = new byte[paddedBufferedBlockCipher.getOutputSize(arrayOfByte1.length)];
/* 62 */       int i = paddedBufferedBlockCipher.processBytes(arrayOfByte1, 0, arrayOfByte1.length, arrayOfByte2, 0);
/* 63 */       int j = paddedBufferedBlockCipher.doFinal(arrayOfByte2, i);
/* 64 */       byte[] arrayOfByte3 = new byte[i + j];
/* 65 */       System.arraycopy(arrayOfByte2, 0, arrayOfByte3, 0, arrayOfByte3.length);
/*    */       
/* 67 */       return new String(arrayOfByte3);
/* 68 */     } catch (Exception exception) {
/*    */ 
/*    */       
/* 71 */       return "";
/*    */     } 
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/filecheck/ExcelSecurity.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */