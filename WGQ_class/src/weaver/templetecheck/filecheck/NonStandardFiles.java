/*     */ package weaver.templetecheck.filecheck;
/*     */ import java.io.BufferedReader;
/*     */ import java.io.BufferedWriter;
/*     */ import java.io.File;
/*     */ import java.io.FileReader;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.HashSet;
/*     */ import java.util.Set;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.GCONST;
/*     */ 
/*     */ public class NonStandardFiles {
/*     */   public static final String filetypes = "(\\.jsp|\\.java|\\.class|\\.js|\\.css|\\.jar)$";
/*  15 */   public static int runningStatus = 0; public static final String filetypeset = "jsp,java,class,js,css,jar";
/*  16 */   public final String AES_KEY = "wEAvER_2021";
/*  17 */   Set excludeFileArr = new HashSet();
/*  18 */   Set excludeDirArr = new HashSet();
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getNonStandardFiles(User paramUser, String paramString) {
/*  24 */     RecordSet recordSet = new RecordSet();
/*  25 */     recordSet.execute("select 1 from nonStandardFile");
/*  26 */     if (!recordSet.next() || "1".equals(paramString))
/*     */     {
/*     */ 
/*     */       
/*  30 */       getNonStandardFileList();
/*     */     }
/*     */     
/*  33 */     String str1 = "";
/*  34 */     String str2 = "nonStandardFile";
/*     */     
/*  36 */     String str3 = " t1.* ";
/*  37 */     String str4 = " from nonStandardFile t1 ";
/*  38 */     String str5 = " where 1=1 ";
/*     */ 
/*     */     
/*  41 */     String str6 = "id";
/*     */     
/*  43 */     str1 = "<table instanceid=\"nonStandardFile\" tabletype=\"checkbox\" pagesize=\"" + PageIdConst.getPageSize(str2, paramUser.getUID()) + "\" >\t   <sql backfields=\"" + str3 + "\" sqlform=\"" + str4 + "\"  sqlwhere=\"" + Util.toHtmlForSplitPage(str5) + "\"    sqlprimarykey=\"id\" sqlsortway=\"Desc\"/>\t\t\t<head>";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  49 */     str1 = str1 + "<col width=\"20%\"  text=\"" + SystemEnv.getHtmlLabelName(84, ThreadVarLanguage.getLang()) + "\" column=\"id\" orderkey=\"id\" display=\"true\"/><col width=\"80%\"  text=\"" + SystemEnv.getHtmlLabelName(25391, ThreadVarLanguage.getLang()) + "\" column=\"filepath\" orderkey=\"filepath\" /></head>\t\t<operates>\t\t\t<operate href=\"javascript:matchsingle();\"  otherpara=\"column:filepath\" text=\"" + SystemEnv.getHtmlLabelName(502096, ThreadVarLanguage.getLang()) + "\" index=\"0\"/></operates></table>";
/*     */     
/*  51 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void getNonStandardFileList() {
/*  60 */     Map map = excludeFile();
/*  61 */     this.excludeFileArr = (HashSet)map.get("file");
/*  62 */     this.excludeDirArr = (HashSet)map.get("dir");
/*     */ 
/*     */     
/*  65 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  66 */     File file1 = new File(GCONST.getRootPath() + "templetecheck" + File.separatorChar + "standardfile");
/*  67 */     File[] arrayOfFile = null;
/*  68 */     if (file1.exists()) {
/*  69 */       arrayOfFile = file1.listFiles();
/*     */     }
/*     */ 
/*     */     
/*  73 */     String str = "";
/*     */     try {
/*  75 */       for (byte b = 0; b < arrayOfFile.length; b++) {
/*  76 */         File file = arrayOfFile[b];
/*  77 */         String str1 = file.getName();
/*  78 */         if (str1.indexOf("StandardFile") > -1) {
/*  79 */           String[] arrayOfString = str1.split("_");
/*  80 */           String str2 = arrayOfString[1].split("\\.")[0];
/*     */           
/*  82 */           FileReader fileReader = new FileReader(file);
/*  83 */           BufferedReader bufferedReader = new BufferedReader(fileReader);
/*     */           
/*  85 */           while ((str = bufferedReader.readLine()) != null) {
/*  86 */             if (!"".equals(str)) {
/*  87 */               str = AES.decrypt(str, "wEAvER_2021");
/*  88 */               HashSet<String> hashSet = (HashSet)hashMap.get(str2);
/*     */               
/*  90 */               if (hashSet == null) {
/*  91 */                 hashSet = new HashSet();
/*  92 */                 hashSet.add(str);
/*  93 */                 hashMap.put(str2, hashSet); continue;
/*     */               } 
/*  95 */               hashSet.add(str);
/*     */             }
/*     */           
/*     */           }
/*     */         
/*     */         } 
/*     */       } 
/* 102 */     } catch (Exception exception) {
/* 103 */       exception.printStackTrace();
/*     */     } 
/*     */ 
/*     */     
/* 107 */     File file2 = new File(GCONST.getRootPath());
/* 108 */     (new BaseBean()).writeLog("非标准文件检查开始：" + TimeUtil.getTimeString(new Date()));
/* 109 */     checkFiles(file2, (HashMap)hashMap);
/* 110 */     (new BaseBean()).writeLog("非标准文件检查结束：" + TimeUtil.getTimeString(new Date()));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void checkFiles(File paramFile, HashMap<String, Set<String>> paramHashMap) {
/* 117 */     Pattern pattern = Pattern.compile("(\\.jsp|\\.java|\\.class|\\.js|\\.css|\\.jar)$");
/* 118 */     Matcher matcher = null;
/* 119 */     RecordSet recordSet = new RecordSet();
/* 120 */     if (paramFile.exists()) {
/* 121 */       File[] arrayOfFile = paramFile.listFiles();
/* 122 */       for (byte b = 0; arrayOfFile != null && b < arrayOfFile.length; b++) {
/* 123 */         File file = arrayOfFile[b];
/* 124 */         if (file.isFile()) {
/* 125 */           String str1 = file.getPath();
/* 126 */           String str2 = "/" + str1.replace(GCONST.getRootPath(), "");
/* 127 */           boolean bool = checkExtendFile(str2);
/* 128 */           if (!bool) {
/*     */ 
/*     */             
/* 131 */             matcher = pattern.matcher(str1);
/* 132 */             if (matcher.find()) {
/*     */               
/* 134 */               str1 = str1.replace(GCONST.getRootPath(), "");
/* 135 */               str1 = str1.replaceAll("\\\\", "/");
/* 136 */               str1 = "/" + str1;
/*     */               
/* 138 */               String[] arrayOfString = str1.split("\\.");
/* 139 */               if (arrayOfString != null && arrayOfString.length >= 2) {
/*     */                 
/* 141 */                 String str = arrayOfString[arrayOfString.length - 1];
/* 142 */                 str = str.toUpperCase();
/* 143 */                 HashSet hashSet = (HashSet)paramHashMap.get(str);
/*     */ 
/*     */                 
/* 146 */                 if (hashSet != null) {
/* 147 */                   if (!hashSet.contains(str1)) {
/*     */                     
/* 149 */                     insertNonStandardFiles(recordSet, str, str1);
/*     */                   } else {
/* 151 */                     hashSet.remove(str1);
/*     */                   } 
/*     */                 }
/*     */               } 
/*     */             } 
/*     */           } 
/*     */         } else {
/* 158 */           String str1 = file.getName();
/* 159 */           String str2 = file.getPath();
/* 160 */           str2 = "/" + str2.replace(GCONST.getRootPath(), "");
/* 161 */           boolean bool = checkExtendFile(str2);
/* 162 */           if (!bool)
/*     */           {
/*     */ 
/*     */             
/* 166 */             if (!"spa".equals(str1) && !"filesystem".equals(str1) && str1.indexOf("_ubak") <= -1 && str1.indexOf("bak") <= -1 && str1.indexOf("副本") <= -1)
/*     */             {
/*     */               
/* 169 */               checkFiles(file, paramHashMap);
/*     */             }
/*     */           }
/*     */         } 
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private Map excludeFile() {
/* 182 */     HashSet<String> hashSet1 = new HashSet();
/* 183 */     HashSet<String> hashSet2 = new HashSet();
/* 184 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */ 
/*     */ 
/*     */     
/* 188 */     File file = new File(GCONST.getRootPath() + "templetecheck" + File.separatorChar + "standardfile" + File.separatorChar + "StandardEcludeFile.txt");
/* 189 */     if (file.exists()) {
/*     */       try {
/* 191 */         FileReader fileReader = new FileReader(file);
/* 192 */         BufferedReader bufferedReader = new BufferedReader(fileReader);
/* 193 */         String str = null;
/* 194 */         while ((str = bufferedReader.readLine()) != null) {
/* 195 */           str = str.trim();
/* 196 */           if ("".equals(str) || 
/* 197 */             str.startsWith("#"))
/* 198 */             continue;  str = str.replace("\\", "/");
/* 199 */           if (str.startsWith("*") && str.endsWith("*")) {
/* 200 */             hashSet2.add(str); continue;
/* 201 */           }  if (str.endsWith("/") || str.indexOf(".") == -1) {
/* 202 */             hashSet2.add(str); continue;
/*     */           } 
/* 204 */           hashSet1.add(str);
/*     */         }
/*     */       
/*     */       }
/* 208 */       catch (Exception exception) {
/* 209 */         exception.printStackTrace();
/*     */       } 
/*     */     }
/* 212 */     hashMap.put("file", hashSet1);
/* 213 */     hashMap.put("dir", hashSet2);
/*     */     
/* 215 */     return hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean checkExtendFile(String paramString) {
/* 224 */     boolean bool = false;
/* 225 */     paramString = paramString.replace("\\", "/");
/* 226 */     if (paramString == null || paramString.trim().equals("") || paramString.indexOf("/") == -1) {
/* 227 */       return false;
/*     */     }
/*     */ 
/*     */ 
/*     */     
/* 232 */     for (String str1 : this.excludeDirArr) {
/* 233 */       String str2 = str1;
/* 234 */       if (str2.startsWith("*") && str2.endsWith("*") && paramString.indexOf(str2.substring(1, str2.length() - 1)) >= 0) {
/* 235 */         bool = true; break;
/*     */       } 
/* 237 */       if (paramString.indexOf(str2) == 0) {
/* 238 */         bool = true;
/*     */         break;
/*     */       } 
/*     */     } 
/* 242 */     if (bool) return bool;
/*     */ 
/*     */     
/* 245 */     if (paramString.substring(paramString.lastIndexOf("/"), paramString.length()).indexOf(".") > -1) {
/* 246 */       bool = this.excludeFileArr.contains(paramString);
/*     */     }
/* 248 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void insertNonStandardFiles(RecordSet paramRecordSet, String paramString1, String paramString2) {
/* 255 */     String str = "select 1 from nonStandardFile where filepath = '" + paramString2 + "'";
/* 256 */     paramRecordSet.executeQuery(str, new Object[0]);
/* 257 */     if (!paramRecordSet.next()) {
/*     */ 
/*     */       
/* 260 */       String str1 = "insert into nonStandardFile(filetype,filepath) values (?,?)";
/* 261 */       paramRecordSet.executeUpdate(str1, new Object[] { paramString1, paramString2 });
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void encryptFiles() {
/* 271 */     ArrayList<String> arrayList = new ArrayList();
/* 272 */     File file = new File(GCONST.getRootPath() + "templetecheck" + File.separatorChar + "standardfile");
/* 273 */     File[] arrayOfFile = null;
/* 274 */     if (file.exists()) {
/* 275 */       arrayOfFile = file.listFiles();
/*     */     }
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 281 */     String str = "";
/*     */     try {
/* 283 */       for (byte b = 0; b < arrayOfFile.length; b++) {
/* 284 */         File file1 = arrayOfFile[b];
/* 285 */         String str1 = file1.getName();
/* 286 */         if (str1.contains("StandardFile")) {
/*     */           
/* 288 */           FileReader fileReader = new FileReader(file1);
/* 289 */           BufferedReader bufferedReader = new BufferedReader(fileReader);
/* 290 */           while ((str = bufferedReader.readLine()) != null) {
/* 291 */             if (!"".equals(str)) {
/* 292 */               arrayList.add(str);
/*     */             }
/*     */           } 
/* 295 */           FileWriter fileWriter = new FileWriter(file1);
/* 296 */           BufferedWriter bufferedWriter = new BufferedWriter(fileWriter);
/* 297 */           for (String str2 : arrayList) {
/* 298 */             if (!"".equals(str2)) {
/* 299 */               str2 = AES.encrypt(str2, "wEAvER_2021");
/* 300 */               bufferedWriter.write(str2);
/* 301 */               bufferedWriter.newLine();
/* 302 */               bufferedWriter.flush();
/*     */             } 
/*     */           } 
/* 305 */           arrayList.clear();
/*     */         } 
/*     */       } 
/* 308 */     } catch (Exception exception) {
/* 309 */       exception.printStackTrace();
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/filecheck/NonStandardFiles.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */