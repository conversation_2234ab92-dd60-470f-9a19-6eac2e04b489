/*      */ package weaver.templetecheck.filecheck;
/*      */ import java.io.File;
/*      */ import java.sql.Connection;
/*      */ import java.sql.DriverManager;
/*      */ import java.sql.PreparedStatement;
/*      */ import java.sql.ResultSet;
/*      */ import java.sql.Statement;
/*      */ import java.text.SimpleDateFormat;
/*      */ import java.util.ArrayList;
/*      */ import java.util.HashMap;
/*      */ import java.util.HashSet;
/*      */ import java.util.Iterator;
/*      */ import java.util.List;
/*      */ import java.util.Map;
/*      */ import java.util.Properties;
/*      */ import javax.servlet.http.HttpServletRequest;
/*      */ import org.jdom.Element;
/*      */ import weaver.conn.ConnStatement;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.conn.RecordSetTrans;
/*      */ import weaver.general.BaseBean;
/*      */ import weaver.general.GCONST;
/*      */ import weaver.general.Util;
/*      */ 
/*      */ public class TempleteSyncToolsOperation {
/*   26 */   public static int currentStep = 1;
/*   27 */   public static int syncStatus = 0;
/*   28 */   public static String ids = "";
/*   29 */   public static String verifyCode = "";
/*   30 */   public static int insertCount = 0;
/*      */   
/*   32 */   private static String sqlserverdbtype = "int,decimal,bigint,binary,bit,datetime,datetimeoffset,float,image,money,numeric,real,smallint,tinyint,uniqueidentifier,varbinary,";
/*      */   
/*   34 */   private static final String templeteSync = GCONST.getRootPath() + "templetecheck" + File.separatorChar + "filecheck" + File.separatorChar + "templeteSync.xml";
/*   35 */   FileUtil fileUtil = new FileUtil();
/*      */   private static TempleteSyncToolsOperation templeteSyncToolsOperation;
/*   37 */   TempleteSyncToolsPropBean templeteSyncToolsPropBean = TempleteSyncToolsPropBean.getInstance();
/*   38 */   private HashMap<String, String> closedrtrigger = new HashMap<>();
/*   39 */   private String dbtype = (new RecordSet()).getDBType().toLowerCase();
/*   40 */   private static Pattern isIntegerPattern = Pattern.compile("^[-\\+]?[\\d]*$");
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static TempleteSyncToolsOperation getInstance() {
/*      */     try {
/*   47 */       if (null == templeteSyncToolsOperation) {
/*   48 */         synchronized (TempleteSyncToolsOperation.class) {
/*   49 */           if (null == templeteSyncToolsOperation) {
/*   50 */             templeteSyncToolsOperation = new TempleteSyncToolsOperation();
/*      */           }
/*      */         } 
/*      */       }
/*   54 */     } catch (Exception exception) {
/*   55 */       exception.printStackTrace();
/*      */     } 
/*   57 */     return templeteSyncToolsOperation;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void startSync() {
/*   67 */     if (syncStatus == 1) {
/*      */       return;
/*      */     }
/*   70 */     syncStatus = 1;
/*      */     
/*   72 */     insertCount = 0;
/*   73 */     TempleteSyncLogger.log2File("*************模板数据同步 start *************");
/*   74 */     verifyCode = UUID.randomUUID().toString();
/*   75 */     TempleteSyncLogger.log2File("*************模板的提取码为：" + verifyCode);
/*   76 */     if (ids.equals("all")) {
/*   77 */       TempleteSyncLogger.log2File("*************迁移流程范围为：所有流程");
/*   78 */     } else if (!ids.equals("")) {
/*   79 */       TempleteSyncLogger.log2File("*************迁移流程的ID为：" + TempleteSyncToolsUtil.getIdsOfParams(ids));
/*      */     } 
/*      */     
/*   82 */     syncColumns();
/*   83 */     syncStatus = 2;
/*   84 */     TempleteSyncLogger.log2File("*************模板数据同步 end ***************");
/*      */   }
/*      */   
/*      */   public static boolean isEmptyObject(Object paramObject) {
/*   88 */     if (paramObject == null) return true; 
/*   89 */     if (paramObject instanceof CharSequence) return (((CharSequence)paramObject).length() == 0); 
/*   90 */     if (paramObject instanceof Collection) return ((Collection)paramObject).isEmpty(); 
/*   91 */     if (paramObject instanceof Map) return ((Map)paramObject).isEmpty(); 
/*   92 */     if (paramObject.getClass().isArray()) return (Array.getLength(paramObject) == 0); 
/*   93 */     return false;
/*      */   }
/*      */ 
/*      */   
/*      */   public void syncColumns() {
/*   98 */     Map<String, Map<String, String>> map = getTempleteSyncMap();
/*      */     
/*  100 */     Properties properties = this.templeteSyncToolsPropBean.getProperties();
/*  101 */     if (isEmptyObject(properties)) {
/*      */       return;
/*      */     }
/*  104 */     String str1 = "";
/*  105 */     BaseBean baseBean = new BaseBean();
/*  106 */     String str2 = baseBean.getPropValue(GCONST.getConfigFile(), "DriverClasses");
/*  107 */     String str3 = GCONST.getServerName();
/*  108 */     String str4 = baseBean.getPropValue(GCONST.getConfigFile(), str3 + ".url");
/*  109 */     String str5 = baseBean.getPropValue(GCONST.getConfigFile(), str3 + ".user");
/*  110 */     String str6 = baseBean.getPropValue(GCONST.getConfigFile(), str3 + ".password");
/*  111 */     Connection connection1 = null;
/*  112 */     RecordSet recordSet = new RecordSet();
/*  113 */     Statement statement = null;
/*  114 */     Connection connection2 = null;
/*  115 */     ResultSet resultSet = null;
/*      */ 
/*      */     
/*      */     try {
/*  119 */       Class.forName(str2);
/*  120 */       Properties properties1 = new Properties();
/*  121 */       properties1.put("user", str5);
/*  122 */       properties1.put("password", str6);
/*  123 */       properties1.put("CHARSET", "ISO");
/*  124 */       connection1 = DriverManager.getConnection(str4, properties1);
/*      */       
/*  126 */       connection2 = getSourceConnectionFromProp(properties);
/*  127 */       statement = connection2.createStatement();
/*  128 */       for (String str7 : map.keySet()) {
/*  129 */         TempleteSyncLogger.log2File("=====开始同步表:" + str7);
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  134 */         HashMap<Object, Object> hashMap = new HashMap<>();
/*  135 */         hashMap.put("updateSuccess", new ArrayList());
/*  136 */         hashMap.put("updateFaild", new ArrayList());
/*  137 */         hashMap.put("insertSuccess", new ArrayList());
/*  138 */         hashMap.put("insertFaild", new ArrayList());
/*      */         
/*  140 */         boolean bool1 = judgeExistTable(str7);
/*  141 */         if (!bool1) {
/*  142 */           TempleteSyncLogger.log2File("=====templetecheck/filecheck/templeteSync.xml中配置的表:" + str7 + "在数据库中可能不存在,请检查确认后再同步,当前已为您跳过!");
/*      */           continue;
/*      */         } 
/*  145 */         Map<String, Map<String, String>> map1 = getTableAllColumns(str7);
/*  146 */         Set<String> set = map1.keySet();
/*  147 */         if (set.size() == 0) {
/*  148 */           TempleteSyncLogger.log2File("=====获取表:" + str7 + "的所有字段名失败,跳过该表同步");
/*      */           continue;
/*      */         } 
/*  151 */         str1 = str1 + "," + str7;
/*  152 */         bakupTable(str7);
/*      */         
/*  154 */         if (str7.toLowerCase().equals("workflow_nodeform")) {
/*  155 */           TempleteSyncLogger.log2File("========开始创建索引========create index nodeformtemp0625_index on workflow_nodeform(nodeid,fieldid)");
/*  156 */           recordSet.executeUpdate("create index nodeformtemp0625_index on workflow_nodeform(nodeid,fieldid)", new Object[0]);
/*  157 */           TempleteSyncLogger.log2File("========创建索引结束========");
/*      */         } 
/*      */         
/*  160 */         Map<String, String> map2 = map.get(str7);
/*  161 */         String str8 = (String)map2.get("updatefields");
/*  162 */         boolean bool2 = ((String)map2.get("forceupdate")).equalsIgnoreCase("true");
/*  163 */         String str9 = "".equals(map2.get("sqlwhere")) ? "1=1 " : (String)map2.get("sqlwhere");
/*  164 */         boolean bool3 = ((String)map2.get("needaddrecord")).equalsIgnoreCase("true");
/*  165 */         boolean bool = true;
/*      */         
/*  167 */         if ("workflow_nodeform,workflow_nodehtmllayout,".contains(str7.toLowerCase() + ",") && "".equals(map2.get("sqlwhere"))) {
/*      */           
/*  169 */           String str = getRecentChangeNodeids(statement);
/*  170 */           if (!str.equals("")) {
/*  171 */             str9 = " ( " + str + " )";
/*      */           }
/*  173 */           TempleteSyncLogger.log2File("=======查询的sqlwhere========" + str9);
/*      */         } 
/*      */ 
/*      */         
/*  177 */         int i = 0;
/*  178 */         HashSet<String> hashSet1 = new HashSet();
/*  179 */         HashSet<String> hashSet2 = new HashSet();
/*      */         
/*  181 */         String str10 = (String)map2.get("sqlprimarykey");
/*      */         
/*  183 */         String str11 = (String)map2.get("conditionfields");
/*      */ 
/*      */         
/*  186 */         String str12 = Util.null2String((String)map2.get("extraoperation")).trim();
/*      */ 
/*      */         
/*  189 */         ArrayList arrayList1 = new ArrayList(Arrays.asList((Object[])str11.split(",")));
/*  190 */         Iterator<String> iterator1 = arrayList1.iterator();
/*  191 */         while (iterator1.hasNext()) {
/*  192 */           String str = iterator1.next();
/*  193 */           if (!set.contains(str.toLowerCase().trim())) {
/*  194 */             TempleteSyncLogger.log2File("=====字段:" + str + "在表" + str7 + "中不存在,已自动忽略该字段,将不会同步该字段");
/*  195 */             iterator1.remove();
/*      */           } 
/*      */         } 
/*  198 */         str11 = StringUtils.join(arrayList1.toArray(), ",");
/*  199 */         map2.put("conditionfields", str11);
/*      */         
/*  201 */         ArrayList arrayList2 = new ArrayList(Arrays.asList((Object[])str8.split(",")));
/*  202 */         Iterator<String> iterator2 = arrayList2.iterator();
/*  203 */         while (iterator2.hasNext()) {
/*  204 */           String str = iterator2.next();
/*  205 */           if (!set.contains(str.toLowerCase().trim())) {
/*  206 */             TempleteSyncLogger.log2File("=====字段:" + str + "在表" + str7 + "中不存在,已自动忽略该字段,将不会同步该字段");
/*  207 */             iterator2.remove();
/*      */           } 
/*      */         } 
/*  210 */         str8 = StringUtils.join(arrayList2.toArray(), ",");
/*  211 */         map2.put("updatefields", str8);
/*  212 */         long l1 = 0L;
/*  213 */         long l2 = 0L;
/*  214 */         long l3 = 0L;
/*      */         
/*      */         try {
/*  217 */           resultSet = statement.executeQuery("select count(" + str10 + ") as sumcount from " + str7 + " where " + str9);
/*  218 */           if (resultSet.next()) {
/*  219 */             l1 = Util.getIntValue(resultSet.getString("sumcount"));
/*      */           }
/*      */ 
/*      */           
/*  223 */           if (str10.toLowerCase().equals("id")) {
/*  224 */             recordSet.executeQuery("select max(" + str10 + ") as maxindex1 from " + str7, new Object[0]);
/*  225 */             if (recordSet.next()) {
/*  226 */               l3 = Util.getIntValue(recordSet.getString("maxindex1"));
/*      */             }
/*      */           } 
/*  229 */         } catch (Exception exception) {
/*  230 */           exception.printStackTrace();
/*      */         } 
/*      */ 
/*      */         
/*      */         try {
/*  235 */           (new BaseBean()).writeLog("=========TempleteSyncToolsOperation.java======select (select max(" + str10 + ") from " + str7 + ") as maxid," + str10 + " from " + str7 + " where " + str9);
/*  236 */           resultSet = statement.executeQuery("select (select max(" + str10 + ") from " + str7 + ") as maxid," + str10 + " from " + str7 + " where " + str9);
/*  237 */           while (resultSet.next()) {
/*  238 */             if (!i) {
/*  239 */               i = Util.getIntValue(resultSet.getString("maxid"));
/*      */             }
/*  241 */             hashSet1.add(resultSet.getString(str10));
/*      */           } 
/*      */           
/*  244 */           recordSet.executeQuery("select " + str10 + " from " + str7, new Object[0]);
/*  245 */           while (recordSet.next()) {
/*  246 */             hashSet2.add(recordSet.getString(str10));
/*      */           }
/*  248 */           hashSet1.retainAll(hashSet2);
/*  249 */         } catch (Exception exception) {
/*  250 */           TempleteSyncLogger.log2File("===========error:同步出错:" + exception.getMessage() + "===\t" + exception.getStackTrace());
/*  251 */           exception.printStackTrace();
/*      */           
/*      */           continue;
/*      */         } 
/*  255 */         if (i == -1) {
/*  256 */           bool = false;
/*  257 */           i = 100;
/*      */         } 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  263 */         boolean bool4 = false;
/*  264 */         if ("sqlserver".equals(this.dbtype)) {
/*      */           try {
/*  266 */             bool4 = recordSet.executeUpdate("set IDENTITY_INSERT " + str7 + " on", new Object[0]);
/*  267 */           } catch (Exception exception) {
/*  268 */             bool4 = false;
/*  269 */             TempleteSyncLogger.log2File("检查表：" + str7 + "是否有标识符时出现异常，仅供参考！");
/*      */           } 
/*      */         }
/*      */         
/*  273 */         String str13 = "";
/*      */         
/*  275 */         String str14 = "";
/*  276 */         if (bool4) {
/*  277 */           str14 = "set IDENTITY_INSERT " + str7 + " on;insert into " + str7 + " (";
/*      */         } else {
/*  279 */           str14 = "insert into " + str7 + " (";
/*      */         } 
/*  281 */         String str15 = "(";
/*  282 */         for (String str : set) {
/*  283 */           if ("".equals(str))
/*  284 */             continue;  str14 = str14 + str + ",";
/*  285 */           str15 = str15 + "?,";
/*  286 */           if (!str.toLowerCase().equals("id")) {
/*  287 */             str13 = str13 + str + ",";
/*      */           }
/*      */         } 
/*      */         
/*  291 */         if (str14.endsWith(",") && str15.endsWith(",")) {
/*  292 */           if (str10.toLowerCase().equals("id")) {
/*  293 */             str13 = str13.substring(0, str13.length() - 1);
/*      */           }
/*  295 */           str14 = str14.substring(0, str14.length() - 1) + ") values" + str15.substring(0, str15.length() - 1) + ") ";
/*      */         } else {
/*  297 */           TempleteSyncLogger.log2File("========追加模式插入语句失败:" + str7 + ",请检查该表字段是否完全一致!参数值insertsql:" + str14 + "==insertvalues" + str15);
/*      */           
/*      */           continue;
/*      */         } 
/*  301 */         if (bool4) {
/*  302 */           str14 = str14 + ";set IDENTITY_INSERT " + str7 + " off";
/*      */         }
/*      */ 
/*      */         
/*  306 */         TempleteSyncLogger.log2File("insert sql:" + str14);
/*      */ 
/*      */ 
/*      */         
/*  310 */         String str16 = "";
/*      */         
/*  312 */         for (String str : arrayList2) {
/*      */           try {
/*  314 */             if ("".equals(str))
/*      */               continue; 
/*  316 */             str16 = str16 + str + "=?,";
/*  317 */           } catch (Exception exception) {
/*  318 */             throw new Exception(exception.getMessage() + ",相关字段:" + str);
/*      */           } 
/*      */         } 
/*      */         
/*  322 */         String str17 = str10 + "=?";
/*      */         
/*  324 */         String str18 = str17;
/*      */         
/*  326 */         for (String str : arrayList1) {
/*  327 */           if ("".equals(str))
/*  328 */             continue;  str18 = str18 + " and " + str + "=?";
/*      */         } 
/*      */ 
/*      */         
/*  332 */         String str19 = "update " + str7 + " set " + str16.substring(0, str16.length() - 1) + " where " + str18;
/*      */         
/*  334 */         TempleteSyncLogger.log2File("update sql:" + str19);
/*      */ 
/*      */         
/*  337 */         if (!closeTrigger(str7) && "id".equalsIgnoreCase(str10))
/*      */         {
/*  339 */           TempleteSyncLogger.log2File("关闭表:" + str7 + "触发器失败");
/*      */         }
/*      */         
/*  342 */         long l4 = 0L;
/*      */         
/*  344 */         char c = 'ᎈ';
/*  345 */         if (str10.equals("nodeid")) {
/*  346 */           c = '';
/*      */         }
/*      */         
/*      */         int j;
/*  350 */         for (j = 1; j <= i + c; j += c) {
/*      */           
/*  352 */           String str = "select  * from " + str7 + " where 1=1  and " + str9 + (bool ? (" and " + str10 + ">=" + j + " and " + str10 + "<" + (j + c)) : "");
/*      */           try {
/*  354 */             resultSet = statement.executeQuery(str);
/*  355 */           } catch (Exception exception) {
/*  356 */             TempleteSyncLogger.log2File("======error:同步" + str7 + "出错:" + exception.getMessage() + "===\t" + exception.getStackTrace() + "===相关语句为:" + str + ",请检查templetecheck\\filecheck\\templeteSync.xml中相关表配置是否正确");
/*      */             
/*  358 */             exception.printStackTrace();
/*      */             
/*      */             break;
/*      */           } 
/*  362 */           connection1.setAutoCommit(false);
/*  363 */           PreparedStatement preparedStatement = connection1.prepareStatement(str19);
/*  364 */           ConnStatement connStatement = new ConnStatement();
/*  365 */           ArrayList<String> arrayList = new ArrayList();
/*      */           try {
/*  367 */             while (resultSet.next()) {
/*  368 */               l2++;
/*  369 */               if (l2 > l4) {
/*  370 */                 l4 += 1000L;
/*  371 */                 TempleteSyncLogger.log2File("========同步中,表名:" + str7 + ",已同步" + l2 + "==总数:" + l1);
/*      */               } 
/*  373 */               boolean bool5 = true;
/*  374 */               String str20 = Util.null2String(resultSet.getString(str10));
/*  375 */               HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  376 */               hashMap1.put(str10.toLowerCase(), str20);
/*  377 */               for (String str22 : arrayList1) {
/*  378 */                 if (!"".equals(str22)) {
/*  379 */                   if ("".equals(Util.null2String(resultSet.getString(str22)))) {
/*  380 */                     bool5 = false;
/*  381 */                     TempleteSyncLogger.log2File("========同步记录失败,表名:" + str7 + ",主键:" + str20 + "==由于字段:" + str22 + "的值为空,无法作为条件来更新.");
/*  382 */                     ((ArrayList<String>)hashMap.get("updateFaild")).add(str20);
/*      */                     break;
/*      */                   } 
/*  385 */                   hashMap1.put(str22.toLowerCase(), resultSet.getString(str22));
/*      */                 } 
/*      */               } 
/*      */               
/*  389 */               if (!bool5)
/*      */                 continue; 
/*  391 */               boolean bool6 = false;
/*  392 */               boolean bool7 = false;
/*  393 */               if (bool3) {
/*      */                 
/*  395 */                 String str22 = " where " + str10 + "='" + (String)hashMap1.get(str10) + "' ";
/*  396 */                 for (String str23 : arrayList1) {
/*  397 */                   if ("".equals(str23))
/*  398 */                     continue;  str22 = str22 + " and " + str23 + "='" + (String)hashMap1.get(str23) + "' ";
/*      */                 } 
/*  400 */                 recordSet.executeQuery("select * from " + str7 + str22, new Object[0]);
/*  401 */                 if (!recordSet.next()) {
/*  402 */                   bool6 = true;
/*      */                 }
/*      */                 
/*  405 */                 if (bool6 && !str22.equals("") && str10.toLowerCase().equals("id")) {
/*  406 */                   recordSet.executeQuery("select * from " + str7 + " where " + str10 + "='" + (String)hashMap1.get(str10) + "' ", new Object[0]);
/*  407 */                   if (recordSet.next()) {
/*  408 */                     bool7 = true;
/*      */                   }
/*      */                 } 
/*      */               } 
/*      */ 
/*      */               
/*  414 */               if (bool6) {
/*  415 */                 String str22 = "";
/*      */                 try {
/*  417 */                   connStatement.setStatementSql(str14);
/*  418 */                   if (bool7) {
/*      */                     
/*  420 */                     if (this.dbtype.equals("sqlserver")) {
/*  421 */                       recordSet.executeUpdate(" insert into " + str7 + "(" + str13 + ") select " + str13 + " from " + str7 + " where " + str10 + "=" + str20, new Object[0]);
/*  422 */                       recordSet.executeUpdate(" delete from " + str7 + " where " + str10 + "=" + str20, new Object[0]);
/*  423 */                       recordSet.executeQuery("select max(" + str10 + ") as maxindex1 from " + str7, new Object[0]);
/*  424 */                       if (recordSet.next()) {
/*  425 */                         l3 = Util.getIntValue(recordSet.getString("maxindex1"));
/*      */                       }
/*      */                     } else {
/*  428 */                       recordSet.executeQuery("select max(" + str10 + ") as maxindex1 from " + str7, new Object[0]);
/*  429 */                       if (recordSet.next()) {
/*  430 */                         l3 = Util.getIntValue(recordSet.getString("maxindex1"));
/*      */                       }
/*  432 */                       l3++;
/*  433 */                       recordSet.executeUpdate("update " + str7 + " set " + str10 + "=" + l3 + " where " + str10 + "=" + str20, new Object[0]);
/*      */                     } 
/*  435 */                     if (str7.toLowerCase().equals("workflow_nodehtmllayout")) {
/*  436 */                       recordSet.executeUpdate("update  workflow_printset set modeid=? where modeid=?", new Object[] { Long.valueOf(l3), str20 });
/*      */                     }
/*  438 */                     TempleteSyncLogger.log2File("========已将:" + str7 + "的数据id为:" + str20 + "改成" + l3 + '\001');
/*      */                   } 
/*  440 */                   byte b = 1;
/*      */                   
/*  442 */                   for (String str23 : set) {
/*      */                     try {
/*  444 */                       if ("".equals(str23))
/*  445 */                         continue;  String str24 = Util.null2String(resultSet.getString(str23));
/*      */                       
/*  447 */                       if (str24.length() > 4000 && this.dbtype.equals("oracle")) {
/*  448 */                         str24 = Util.null2String(resultSet.getClob(str23).getSubString(1L, (int)resultSet.getClob(str23).length()));
/*  449 */                         connStatement.setString(b, str24);
/*  450 */                       } else if ("".equals(str24) && this.dbtype.equals("sqlserver")) {
/*  451 */                         String str25 = ((String)((Map)map1.get(str23.toLowerCase())).get("data_type")).toLowerCase();
/*  452 */                         if (sqlserverdbtype.contains(str25 + ",")) {
/*  453 */                           connStatement.setNull(b);
/*      */                         } else {
/*  455 */                           connStatement.setString(b, str24);
/*      */                         } 
/*      */                       } else {
/*  458 */                         connStatement.setString(b, str24);
/*      */                       } 
/*      */                       
/*  461 */                       str22 = str22 + "'" + str24.replace("'", "''") + "',";
/*  462 */                       b++;
/*      */                     }
/*  464 */                     catch (Exception exception) {
/*  465 */                       exception.printStackTrace();
/*  466 */                       throw new Exception(exception.getMessage() + ",追加数据时出错,相关字段:" + str23);
/*      */                     } 
/*      */                   } 
/*  469 */                   if (!str7.toLowerCase().equals("workflow_nodehtmllayout")) {
/*  470 */                     TempleteSyncLogger.log2File("insert sql value:" + str22);
/*      */                   }
/*      */                   
/*  473 */                   int k = connStatement.executeUpdate();
/*  474 */                   if (k >= 1) {
/*  475 */                     ((ArrayList<String>)hashMap.get("insertSuccess")).add(str20);
/*  476 */                     TempleteSyncLogger.log2File("========追加记录成功,表名:" + str7 + ",主键:" + str20 + "----本张表总大小为：" + l1 + "----已同步" + l2);
/*      */                     
/*  478 */                     if (str7.toLowerCase().equals("workflow_nodehtmllayout"))
/*  479 */                       insertTempleteSyncLog(str20, verifyCode, connection2); 
/*      */                     continue;
/*      */                   } 
/*  482 */                   ((ArrayList<String>)hashMap.get("insertFaild")).add(str20);
/*  483 */                   TempleteSyncLogger.log2File("========条件不满足,追加失败,表名:" + str7 + ",主键:" + str20 + ",返回值executesuccess:" + k + "----本张表总大小为：" + l1 + "----已同步" + l2);
/*      */                 }
/*  485 */                 catch (Exception exception) {
/*  486 */                   ((ArrayList<String>)hashMap.get("insertFaild")).add(str20);
/*  487 */                   exception.printStackTrace();
/*      */                 } finally {}
/*      */ 
/*      */ 
/*      */ 
/*      */                 
/*      */                 continue;
/*      */               } 
/*      */ 
/*      */ 
/*      */ 
/*      */               
/*  499 */               if (!hashSet1.contains(str20))
/*      */                 continue; 
/*  501 */               boolean bool8 = true;
/*  502 */               String str21 = "";
/*      */ 
/*      */ 
/*      */               
/*      */               try {
/*  507 */                 byte b = 1;
/*      */                 
/*  509 */                 for (String str22 : arrayList2) {
/*      */                   try {
/*  511 */                     if ("".equals(str22))
/*      */                       continue; 
/*  513 */                     String str23 = Util.null2String(resultSet.getString(str22));
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */                     
/*  519 */                     if (str23.length() > 4000 && this.dbtype.equals("oracle")) {
/*  520 */                       str23 = Util.null2String(resultSet.getClob(str22).getSubString(1L, (int)resultSet.getClob(str22).length()));
/*  521 */                       preparedStatement.setString(b, str23);
/*  522 */                     } else if ("".equals(str23) && this.dbtype.equals("sqlserver")) {
/*  523 */                       String str24 = ((String)((Map)map1.get(str22.toLowerCase())).get("data_type")).toLowerCase();
/*  524 */                       if (sqlserverdbtype.contains(str24 + ",")) {
/*  525 */                         preparedStatement.setObject(b, (Object)null);
/*      */                       } else {
/*  527 */                         preparedStatement.setString(b, str23);
/*      */                       } 
/*      */                     } else {
/*  530 */                       preparedStatement.setString(b, str23);
/*      */                     } 
/*  532 */                     str21 = str21 + "'" + str23.replace("'", "''") + "',";
/*  533 */                     b++;
/*  534 */                   } catch (Exception exception) {
/*  535 */                     exception.printStackTrace();
/*  536 */                     throw new Exception(exception.getMessage() + ",相关字段:" + str22);
/*      */                   } 
/*      */                 } 
/*      */ 
/*      */                 
/*  541 */                 preparedStatement.setString(b, str20);
/*  542 */                 str21 = str21 + "'" + str20 + "',";
/*  543 */                 b++;
/*      */                 
/*  545 */                 for (String str22 : arrayList1) {
/*  546 */                   if ("".equals(str22)) {
/*      */                     continue;
/*      */                   }
/*  549 */                   preparedStatement.setString(b, Util.null2String(resultSet.getString(str22)));
/*  550 */                   str21 = str21 + "'" + Util.null2String(resultSet.getString(str22)).replace("'", "''") + "',";
/*  551 */                   b++;
/*      */                 } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */                 
/*  559 */                 preparedStatement.addBatch();
/*  560 */                 arrayList.add(str20);
/*      */ 
/*      */ 
/*      */                 
/*  564 */                 if (str7.toLowerCase().equals("workflow_nodehtmllayout")) {
/*  565 */                   insertTempleteSyncLog(str20, verifyCode, connection2);
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */                 
/*      */                 }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */               
/*      */               }
/*  580 */               catch (Exception exception) {
/*  581 */                 ((ArrayList<String>)hashMap.get("updateFaild")).add(str20);
/*  582 */                 TempleteSyncLogger.log2File("========error:同步出错,表名:" + str7 + " ,主键:" + str20 + ",错误信息:" + exception.getMessage() + "===\t" + exception.getStackTrace().toString() + ",请检查templetecheck\\filecheck\\templeteSync.xml中相关表配置是否正确");
/*      */                 
/*  584 */                 exception.printStackTrace();
/*      */               } 
/*      */             } 
/*      */ 
/*      */             
/*  589 */             if (arrayList.size() > 0) {
/*  590 */               TempleteSyncLogger.log2File("========开始执行sql");
/*  591 */               preparedStatement.executeBatch();
/*  592 */               connection1.commit();
/*  593 */               ((ArrayList<String>)hashMap.get("updateSuccess")).addAll(arrayList);
/*  594 */               TempleteSyncLogger.log2File("========执行sql结束----,同步成功");
/*  595 */               TempleteSyncLogger.log2File("========同步,表名:" + str7 + "----本张表总大小为：" + l1 + "----已同步" + l2);
/*      */             } 
/*  597 */           } catch (Exception exception) {
/*  598 */             exception.printStackTrace();
/*      */             
/*      */             try {
/*  601 */               ((ArrayList<String>)hashMap.get("updateFaild")).addAll(arrayList);
/*  602 */               if (!connection1.isClosed()) {
/*  603 */                 connection1.rollback();
/*  604 */                 if ("workflow_nodeform,workflow_nodehtmllayout,".contains(str7.toLowerCase() + ",")) {
/*  605 */                   TempleteSyncLogger.log2File("========同步失败，数据回滚,表名:" + str7 + ",查询的sql:select  * from " + str7 + " where 1=1 " + (bool ? (" and " + str10 + ">=" + j + " and " + str10 + "<" + (j + c)) : "") + "");
/*      */                 } else {
/*  607 */                   TempleteSyncLogger.log2File("========同步失败，数据回滚,表名:" + str7 + ",查询的sql:" + str + "");
/*      */                 } 
/*  609 */                 TempleteSyncLogger.log2File("========同步失败的" + str10 + ":" + arrayList.toString());
/*      */               } 
/*  611 */             } catch (SQLException sQLException) {
/*  612 */               sQLException.printStackTrace();
/*      */             } 
/*      */           } finally {
/*      */             try {
/*  616 */               connStatement.close();
/*  617 */             } catch (Exception exception) {
/*  618 */               exception.printStackTrace();
/*      */             } 
/*  620 */             connection1.setAutoCommit(true);
/*      */           } 
/*  622 */           if (!bool) {
/*      */             break;
/*      */           }
/*      */         } 
/*  626 */         if (!openTrigger(str7)) {
/*  627 */           TempleteSyncLogger.log2File("打开表:" + str7 + "触发器失败");
/*      */         }
/*      */         
/*  630 */         if (bool3 && ((ArrayList)hashMap.get("insertSuccess")).size() > 0 && recordSet.getDBType().equalsIgnoreCase("oracle")) {
/*  631 */           RecordSet recordSet1 = new RecordSet();
/*  632 */           recordSet1.executeQuery("select max(" + str10 + ") as id from " + str7, new Object[0]);
/*  633 */           if (recordSet1.next()) {
/*  634 */             boolean bool5 = resetSequence(str7, recordSet1.getInt("id"));
/*  635 */             if (!bool5 && "id".equalsIgnoreCase(str10)) {
/*  636 */               TempleteSyncLogger.log2File("=====给表:" + str7 + "追加了记录,重置sequence失败");
/*      */             }
/*      */           } 
/*      */         } 
/*      */ 
/*      */ 
/*      */         
/*  643 */         TempleteSyncLogger.log2File("=====同步表:" + str7 + "结束,更新了" + ((ArrayList)hashMap.get("updateSuccess")).size() + "条记录,失败了" + ((ArrayList)hashMap.get("updateFaild")).size() + "条记录\n");
/*  644 */         if (bool3) {
/*  645 */           TempleteSyncLogger.log2File("=====同步表:" + str7 + "结束,追加了" + ((ArrayList)hashMap.get("insertSuccess")).size() + "条记录,失败了" + ((ArrayList)hashMap.get("insertFaild")).size() + "条记录\n");
/*  646 */           if (((ArrayList)hashMap.get("insertSuccess")).size() > 0) {
/*  647 */             TempleteSyncLogger.log2File("追加成功的主键:" + (new HashSet((Collection)hashMap.get("insertSuccess"))).toString());
/*      */           }
/*  649 */           if (((ArrayList)hashMap.get("insertFaild")).size() > 0) {
/*  650 */             TempleteSyncLogger.log2File("追加失败的主键:" + (new HashSet((Collection)hashMap.get("insertFaild"))).toString());
/*      */           }
/*  652 */           if (((ArrayList)hashMap.get("insertSuccess")).size() > 0 || ((ArrayList)hashMap.get("insertFaild")).size() > 0) {
/*  653 */             TempleteSyncLogger.log2File("上述主键记录说明:此处仅展示templeteSync.xml中配置的主键的字段值,如果需要配合conditionfields中的字段才能确定唯一记录的,请根据此处数据去数据库对应表中查询具体记录");
/*      */           }
/*      */         } 
/*      */         
/*  657 */         if (!str12.equals("")) {
/*  658 */           recordSet.executeUpdate(str12, new Object[0]);
/*      */         }
/*  660 */         if (str7.toLowerCase().equals("workflow_nodeform")) {
/*  661 */           TempleteSyncLogger.log2File("========开始删除索引=======索引名字nodeformtemp0625_index");
/*  662 */           if (this.dbtype.equals("sqlserver")) {
/*  663 */             recordSet.executeUpdate("drop index nodeformtemp0625_index on workflow_nodeform", new Object[0]);
/*      */           } else {
/*  665 */             recordSet.executeUpdate("drop index nodeformtemp0625_index", new Object[0]);
/*      */           } 
/*  667 */           TempleteSyncLogger.log2File("========删除索引结束========");
/*      */         } 
/*      */       } 
/*  670 */     } catch (Exception exception) {
/*  671 */       TempleteSyncLogger.log2File("============error:同步出错:" + exception.getMessage() + "==\t" + exception.getStackTrace());
/*  672 */       exception.printStackTrace();
/*      */     } finally {
/*      */       
/*  675 */       if (str1.length() > 0) {
/*  676 */         str1 = str1.substring(1);
/*  677 */         CacheFactory.getInstance().removeCache(str1.split(","));
/*      */       } 
/*      */       
/*      */       try {
/*  681 */         if (connection1 != null) {
/*  682 */           connection1.close();
/*      */         }
/*  684 */         if (resultSet != null) {
/*  685 */           resultSet.close();
/*      */         }
/*  687 */         if (statement != null) {
/*  688 */           statement.close();
/*      */         }
/*  690 */         if (connection2 != null) {
/*  691 */           connection2.close();
/*      */         }
/*  693 */       } catch (Exception exception) {
/*  694 */         exception.printStackTrace();
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getRecentChangeNodeids(Statement paramStatement) {
/*      */     try {
/*  709 */       ResultSet resultSet = paramStatement.executeQuery("select distinct(belongTypeTargetId)  from ecology_biz_log where logType=2 and belongType=14 ");
/*  710 */       StringBuffer stringBuffer1 = new StringBuffer();
/*  711 */       String str1 = "";
/*  712 */       int i = 800;
/*  713 */       int j = 1;
/*  714 */       while (resultSet.next()) {
/*  715 */         String str = Util.null2String(resultSet.getString("belongTypeTargetId")).trim();
/*  716 */         if (!str.equals("") && isInteger(str)) {
/*  717 */           j++;
/*  718 */           stringBuffer1.append(str + ",");
/*  719 */           if (j < i)
/*      */             continue; 
/*  721 */           if (str1.equals("")) {
/*  722 */             str1 = " nodeid in(" + stringBuffer1.substring(0, stringBuffer1.length() - 1) + ") ";
/*      */           } else {
/*  724 */             str1 = str1 + " or nodeid in(" + stringBuffer1.substring(0, stringBuffer1.length() - 1) + ") ";
/*      */           } 
/*  726 */           stringBuffer1 = new StringBuffer();
/*  727 */           i += 800;
/*      */         } 
/*      */       } 
/*      */       
/*  731 */       if (!stringBuffer1.equals("")) {
/*  732 */         if (str1.equals("")) {
/*  733 */           str1 = " nodeid in(" + stringBuffer1.substring(0, stringBuffer1.length() - 1) + ") ";
/*      */         } else {
/*  735 */           str1 = str1 + " or nodeid in(" + stringBuffer1.substring(0, stringBuffer1.length() - 1) + ") ";
/*      */         } 
/*      */       }
/*      */       
/*  739 */       StringBuffer stringBuffer2 = new StringBuffer();
/*  740 */       j = 1;
/*  741 */       i = 800;
/*  742 */       resultSet = paramStatement.executeQuery("select distinct(workflowid) from workflow_flownode where " + str1);
/*  743 */       String str2 = "";
/*  744 */       while (resultSet.next()) {
/*  745 */         String str = Util.null2String(resultSet.getString("workflowid")).trim();
/*  746 */         if (!str.equals("")) {
/*  747 */           j++;
/*  748 */           stringBuffer2.append(str + ",");
/*  749 */           if (j < i)
/*      */             continue; 
/*  751 */           if (str2.equals("")) {
/*  752 */             str2 = " workflowid in(" + stringBuffer2.substring(0, stringBuffer2.length() - 1) + ") ";
/*      */           } else {
/*  754 */             str2 = str2 + " or workflowid in(" + stringBuffer2.substring(0, stringBuffer2.length() - 1) + ") ";
/*      */           } 
/*  756 */           stringBuffer2 = new StringBuffer();
/*  757 */           i += 800;
/*      */         } 
/*      */       } 
/*      */ 
/*      */       
/*  762 */       if (!stringBuffer2.equals("")) {
/*  763 */         if (str2.equals("")) {
/*  764 */           str2 = " workflowid in(" + stringBuffer2.substring(0, stringBuffer2.length() - 1) + ") ";
/*      */         } else {
/*  766 */           str2 = str2 + " or workflowid in(" + stringBuffer2.substring(0, stringBuffer2.length() - 1) + ") ";
/*      */         } 
/*      */       }
/*      */       
/*  770 */       TempleteSyncLogger.log2File("========修改过模板的流程========" + str2);
/*  771 */       j = 1;
/*  772 */       i = 800;
/*  773 */       stringBuffer1 = new StringBuffer();
/*  774 */       str1 = "";
/*  775 */       resultSet = paramStatement.executeQuery("select distinct(nodeid) from workflow_flownode where 1=1 and (" + str2 + ")");
/*  776 */       while (resultSet.next()) {
/*  777 */         String str = Util.null2String(resultSet.getString("nodeid")).trim();
/*  778 */         if (!str.equals("")) {
/*  779 */           j++;
/*  780 */           stringBuffer1.append(str + ",");
/*  781 */           if (j < i)
/*      */             continue; 
/*  783 */           if (str1.equals("")) {
/*  784 */             str1 = " nodeid in(" + stringBuffer1.substring(0, stringBuffer1.length() - 1) + ") ";
/*      */           } else {
/*  786 */             str1 = str1 + " or nodeid in(" + stringBuffer1.substring(0, stringBuffer1.length() - 1) + ") ";
/*      */           } 
/*  788 */           stringBuffer1 = new StringBuffer();
/*  789 */           i += 800;
/*      */         } 
/*      */       } 
/*      */       
/*  793 */       if (!stringBuffer1.equals("")) {
/*  794 */         if (str1.equals("")) {
/*  795 */           str1 = " nodeid in(" + stringBuffer1.substring(0, stringBuffer1.length() - 1) + ") ";
/*      */         } else {
/*  797 */           str1 = str1 + " or nodeid in(" + stringBuffer1.substring(0, stringBuffer1.length() - 1) + ") ";
/*      */         } 
/*      */       }
/*  800 */       return str1;
/*  801 */     } catch (Exception exception) {
/*  802 */       exception.printStackTrace();
/*  803 */       return "";
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static boolean isInteger(String paramString) {
/*  814 */     return isIntegerPattern.matcher(paramString).matches();
/*      */   }
/*      */   
/*      */   private Map<String, Map<String, String>> getTableAllColumns(String paramString) {
/*  818 */     RecordSet recordSet = new RecordSet();
/*  819 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*      */     
/*  821 */     String str = "";
/*  822 */     if ("Oracle".equalsIgnoreCase(this.dbtype)) {
/*  823 */       str = "SELECT COLUMN_NAME,DATA_TYPE,DATA_LENGTH,DATA_PRECISION,DATA_SCALE,NULLABLE,DATA_DEFAULT FROM USER_TAB_COLUMNS WHERE TABLE_NAME='" + paramString.toUpperCase() + "'";
/*  824 */       recordSet.executeQuery(str, new Object[0]);
/*  825 */     } else if ("SqlServer".equalsIgnoreCase(this.dbtype)) {
/*  826 */       str = "SELECT SC.NAME AS COLUMN_NAME,ST.NAME AS DATA_TYPE,SC.LENGTH AS DATA_LENGTH,SC.XPREC AS DATA_PRECISION,SC.XSCALE AS DATA_SCALE,SC.ISNULLABLE AS NULLABLE,CM.TEXT AS DATA_DEFAULT FROM SYSCOLUMNS SC LEFT JOIN SYSTYPES ST ON SC.XUSERTYPE=ST.XUSERTYPE LEFT JOIN SYSCOMMENTS CM ON SC.CDEFAULT=CM.ID WHERE SC.ID=Object_Id(?)";
/*  827 */       recordSet.executeQuery(str.replace("?", "'" + paramString.toUpperCase() + "'"), new Object[0]);
/*      */     }
/*  829 */     else if ("postgresql".equalsIgnoreCase(this.dbtype)) {
/*      */       
/*  831 */       str = "select COLUMN_NAME,DATA_TYPE, CHARACTER_OCTET_LENGTH as DATA_LENGTH,numeric_precision as DATA_PRECISION,numeric_scale as DATA_SCALE, is_nullable as NULLABLE,column_default as DATA_DEFAULT from Information_schema.columns WHERE table_name = lower('" + paramString + "')";
/*  832 */       recordSet.executeQuery(str, new Object[0]);
/*      */     } 
/*      */     
/*  835 */     while (recordSet.next()) {
/*  836 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  837 */       hashMap1.put("column_name", Util.null2String(recordSet.getString("COLUMN_NAME")).toLowerCase());
/*  838 */       hashMap1.put("data_type", Util.null2String(recordSet.getString("DATA_TYPE")).toLowerCase());
/*  839 */       hashMap1.put("data_length", Util.null2String(recordSet.getString("DATA_LENGTH")).toLowerCase());
/*  840 */       hashMap1.put("data_precision", Util.null2String(recordSet.getString("DATA_PRECISION")).toLowerCase());
/*  841 */       hashMap1.put("data_scale", Util.null2String(recordSet.getString("DATA_SCALE")).toLowerCase());
/*  842 */       hashMap1.put("nullable", Util.null2String(recordSet.getString("NULLABLE")).toLowerCase());
/*  843 */       hashMap1.put("data_default", Util.null2String(recordSet.getString("DATA_DEFAULT")).toLowerCase());
/*  844 */       hashMap.put(Util.null2String(recordSet.getString("COLUMN_NAME")).toLowerCase(), hashMap1);
/*      */     } 
/*  846 */     return (Map)hashMap;
/*      */   }
/*      */   
/*      */   public Map<String, Map<String, String>> getTempleteSyncMap() {
/*  850 */     RecordSet recordSet = new RecordSet();
/*  851 */     SAXBuilder sAXBuilder = new SAXBuilder();
/*  852 */     TempleteSecurityUtil.setSaxBuilderFeature(sAXBuilder);
/*  853 */     File file = new File(this.fileUtil.getPath(templeteSync));
/*  854 */     if (!file.canWrite()) {
/*  855 */       file.setWritable(true);
/*      */     }
/*  857 */     LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/*      */     
/*      */     try {
/*  860 */       Document document = sAXBuilder.build(file);
/*  861 */       Element element = document.getRootElement();
/*  862 */       List list = element.getChildren("table");
/*  863 */       for (Element element1 : list) {
/*      */         
/*  865 */         String str1 = element1.getAttributeValue("id").toLowerCase();
/*  866 */         String str2 = element1.getAttributeValue("sqlprimarykey").toLowerCase();
/*      */         
/*  868 */         String str3 = Util.null2String(element1.getAttributeValue("needaddrecord")).trim().toLowerCase();
/*  869 */         HashMap<Object, Object> hashMap = new HashMap<>();
/*      */         
/*  871 */         String str4 = Util.null2String(element1.getChildTextTrim("conditionfields")).replaceAll("\\s*|\t|\r|\n", "").toLowerCase();
/*      */         
/*  873 */         String str5 = Util.null2String(element1.getChildTextTrim("updatefields")).replaceAll("\\s*|\t|\r|\n", "").toLowerCase();
/*  874 */         String str6 = Util.null2String(element1.getChildTextTrim("extraoperation")).trim();
/*      */         
/*  876 */         if ("*".equals(str5.trim())) {
/*  877 */           ArrayList arrayList = new ArrayList(getTableAllColumns(str1).keySet());
/*  878 */           arrayList.remove(str2.toLowerCase());
/*  879 */           str5 = StringUtils.join(arrayList.toArray(), ",");
/*      */         } 
/*      */         
/*  882 */         String str7 = Util.null2String(element1.getChildTextTrim("forceupdate")).trim().toLowerCase();
/*  883 */         hashMap.put("forceupdate", str7);
/*  884 */         hashMap.put("needaddrecord", str3);
/*  885 */         hashMap.put("sqlprimarykey", str2);
/*  886 */         hashMap.put("conditionfields", str4);
/*  887 */         hashMap.put("updatefields", str5);
/*  888 */         hashMap.put("extraoperation", str6);
/*      */         
/*  890 */         String str8 = Util.null2String(element1.getChildTextTrim("sqlwhere")).trim();
/*  891 */         if (!"".equals(ids) && ids != "all") {
/*  892 */           if (str1.toLowerCase().equals("workflow_nodehtmllayout")) {
/*  893 */             str8 = " workflowid in(" + TempleteSyncToolsUtil.getIdsOfParams(ids) + ")";
/*  894 */           } else if (str1.toLowerCase().equals("workflow_nodeformgroup")) {
/*  895 */             str8 = " nodeid in(" + getNodeidsByWFids(ids) + ")";
/*  896 */           } else if (str1.toLowerCase().equals("workflow_nodefieldattr")) {
/*  897 */             str8 = " nodeid in(" + getNodeidsByWFids(ids) + ")";
/*  898 */           } else if (str1.toLowerCase().equals("workflow_flownode")) {
/*  899 */             str8 = " workflowid in(" + TempleteSyncToolsUtil.getIdsOfParams(ids) + ")";
/*  900 */           } else if (str1.toLowerCase().equals("workflow_nodeform")) {
/*  901 */             str8 = " nodeid in(" + getNodeidsByWFids(ids) + ")";
/*  902 */           } else if (str1.toLowerCase().equals("workflow_formula_htmllayout")) {
/*  903 */             str8 = " nodeid in(" + getNodeidsByWFids(ids) + ")";
/*  904 */           } else if (str1.toLowerCase().equals("workflow_printset")) {
/*  905 */             str8 = " nodeid in(" + getNodeidsByWFids(ids) + ")";
/*      */           } 
/*      */         }
/*      */         
/*  909 */         hashMap.put("sqlwhere", str8);
/*  910 */         linkedHashMap.put(str1.toLowerCase(), hashMap);
/*      */       } 
/*  912 */     } catch (Exception exception) {
/*  913 */       exception.printStackTrace();
/*      */     } 
/*  915 */     return (Map)linkedHashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Connection getBeTransConnection(HttpServletRequest paramHttpServletRequest) {
/*  924 */     Connection connection = null;
/*      */     try {
/*  926 */       String str1 = Util.null2String(paramHttpServletRequest.getParameter("dbserver"));
/*  927 */       String str2 = Util.null2String(paramHttpServletRequest.getParameter("dbport"));
/*  928 */       String str3 = Util.null2String(paramHttpServletRequest.getParameter("dbname"));
/*  929 */       String str4 = Util.null2String(paramHttpServletRequest.getParameter("username"));
/*  930 */       String str5 = Util.null2String(paramHttpServletRequest.getParameter("password"));
/*  931 */       String str6 = Util.null2String(paramHttpServletRequest.getParameter("dbtype"));
/*  932 */       DriverManager.setLoginTimeout(30);
/*  933 */       if (str6.equalsIgnoreCase("sqlserver")) {
/*  934 */         Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
/*  935 */         connection = DriverManager.getConnection("jdbc:sqlserver://" + str1 + ":" + str2 + ";databaseName=" + str3, str4, str5);
/*  936 */       } else if (str6.equalsIgnoreCase("oracle")) {
/*  937 */         Class.forName("oracle.jdbc.OracleDriver");
/*  938 */         connection = DriverManager.getConnection("jdbc:oracle:thin:@" + str1 + ":" + str2 + ":" + str3, str4, str5);
/*      */       } 
/*  940 */     } catch (Exception exception) {
/*  941 */       (new BaseBean()).writeLog(exception.getMessage());
/*  942 */       exception.printStackTrace();
/*  943 */       if (connection != null) {
/*      */         try {
/*  945 */           connection.close();
/*  946 */         } catch (SQLException sQLException) {
/*  947 */           connection = null;
/*  948 */           sQLException.printStackTrace();
/*      */         } 
/*      */       } else {
/*  951 */         connection = null;
/*      */       } 
/*      */     } 
/*  954 */     return connection;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Connection getSourceConnectionFromProp(Properties paramProperties) {
/*  963 */     Connection connection = null;
/*  964 */     if (paramProperties == null) return connection; 
/*      */     try {
/*  966 */       String str1 = Util.null2String(paramProperties.getProperty("dbserver"));
/*  967 */       String str2 = Util.null2String(paramProperties.getProperty("dbport"));
/*  968 */       String str3 = Util.null2String(paramProperties.getProperty("dbname"));
/*  969 */       String str4 = Util.null2String(paramProperties.getProperty("username"));
/*  970 */       String str5 = Util.null2String(paramProperties.getProperty("password"));
/*  971 */       String str6 = Util.null2String(paramProperties.getProperty("dbtype"));
/*  972 */       DriverManager.setLoginTimeout(30);
/*  973 */       if (str6.equalsIgnoreCase("sqlserver")) {
/*  974 */         Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
/*  975 */         connection = DriverManager.getConnection("jdbc:sqlserver://" + str1 + ":" + str2 + ";databaseName=" + str3, str4, str5);
/*  976 */       } else if (str6.equalsIgnoreCase("oracle")) {
/*  977 */         Class.forName("oracle.jdbc.OracleDriver");
/*  978 */         connection = DriverManager.getConnection("jdbc:oracle:thin:@" + str1 + ":" + str2 + ":" + str3, str4, str5);
/*      */       } 
/*  980 */     } catch (Exception exception) {
/*  981 */       exception.printStackTrace();
/*  982 */       (new BaseBean()).writeLog(exception.getMessage());
/*  983 */       if (connection != null) {
/*      */         try {
/*  985 */           connection.close();
/*  986 */         } catch (SQLException sQLException) {
/*  987 */           connection = null;
/*  988 */           sQLException.printStackTrace();
/*      */         } 
/*      */       } else {
/*  991 */         connection = null;
/*      */       } 
/*      */     } 
/*  994 */     return connection;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public JSONObject getLogInfo(String paramString) {
/* 1001 */     JSONObject jSONObject = new JSONObject();
/* 1002 */     if ("1".equals(paramString)) {
/* 1003 */       TempleteSyncLogger.resetLogFile();
/*      */     }
/* 1005 */     String str = LogUtil.getInstance().getChangeLog(paramString).replaceAll("\t", " ");
/* 1006 */     jSONObject.put("loginfo", str);
/* 1007 */     return jSONObject;
/*      */   }
/*      */ 
/*      */   
/*      */   public static boolean checkDBFieldIsExist(String paramString1, String paramString2) {
/* 1012 */     RecordSet recordSet = new RecordSet();
/* 1013 */     boolean bool = false;
/*      */     try {
/* 1015 */       StringBuffer stringBuffer = new StringBuffer();
/* 1016 */       if (recordSet.getDBType().equals("oracle")) {
/* 1017 */         stringBuffer.append("select 1 ");
/* 1018 */         stringBuffer.append("\t  from user_tab_columns ");
/* 1019 */         stringBuffer.append("\t where LOWER(table_name) = '" + paramString2 + "' ");
/* 1020 */         stringBuffer.append("\t   and LOWER(column_name) = '" + paramString1 + "'");
/*      */       }
/* 1022 */       else if (recordSet.getDBType().equals("oracle")) {
/*      */         
/* 1024 */         stringBuffer.append("select 1 ");
/* 1025 */         stringBuffer.append("\t  from pg_attribute ");
/* 1026 */         stringBuffer.append("where attrelid = lower('" + paramString2 + "')::regclass::oid");
/* 1027 */         stringBuffer.append("and LOWER(column_name) = '" + paramString1 + "'");
/*      */       } else {
/*      */         
/* 1030 */         stringBuffer.append("select 1 ");
/* 1031 */         stringBuffer.append("  from syscolumns c ");
/* 1032 */         stringBuffer.append(" where objectproperty(c.id, 'IsUserTable') = 1 ");
/* 1033 */         stringBuffer.append("   and object_name(c.id) = '" + paramString2 + "' ");
/* 1034 */         stringBuffer.append("   and c.name = '" + paramString1 + "'");
/*      */       } 
/*      */       
/* 1037 */       recordSet.executeSql(stringBuffer.toString());
/* 1038 */       while (recordSet.next()) {
/* 1039 */         if (recordSet.getString(1).equals("1")) {
/* 1040 */           bool = true;
/*      */         }
/*      */       } 
/* 1043 */     } catch (Exception exception) {
/* 1044 */       exception.printStackTrace();
/*      */     } 
/* 1046 */     return bool;
/*      */   }
/*      */ 
/*      */   
/*      */   public Map<String, String> getRecordMap(String paramString, Map<String, String> paramMap1, Map<String, String> paramMap2) {
/* 1051 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 1052 */     ConnStatement connStatement = new ConnStatement();
/*      */     try {
/* 1054 */       String str1 = paramMap1.get("conditionfields");
/* 1055 */       String str2 = paramMap1.get("updatefields");
/* 1056 */       String str3 = paramMap1.get("sqlprimarykey");
/* 1057 */       String str4 = paramMap2.get(str3.toLowerCase());
/*      */       
/* 1059 */       String str5 = "".equals(paramMap1.get("sqlwhere")) ? "1=1 " : paramMap1.get("sqlwhere");
/* 1060 */       List<String> list1 = Arrays.asList(str1.split(","));
/* 1061 */       List<String> list2 = Arrays.asList(str2.split(","));
/*      */ 
/*      */       
/* 1064 */       for (String str : list1) {
/* 1065 */         str5 = str5 + " and " + str + "='" + (String)paramMap2.get(str.toLowerCase()) + "' ";
/*      */       }
/* 1067 */       String str6 = "select " + (str2 + ("".equals(str1.trim()) ? "" : ("," + str1))).replace(",,", ",") + " from " + paramString + " where " + str3 + "='" + str4 + "' and " + str5;
/* 1068 */       connStatement.setStatementSql(str6);
/* 1069 */       connStatement.executeQuery();
/* 1070 */       if (connStatement.next()) {
/* 1071 */         for (String str : list2) {
/* 1072 */           hashMap.put(str.trim().toLowerCase(), Util.null2String(connStatement.getString(str)));
/*      */         }
/* 1074 */         for (String str : list1) {
/* 1075 */           hashMap.put(str.trim().toLowerCase(), Util.null2String(connStatement.getString(str)));
/*      */         }
/*      */       } 
/* 1078 */     } catch (Exception exception) {
/* 1079 */       exception.printStackTrace();
/* 1080 */       return (Map)hashMap;
/*      */     } finally {
/*      */       try {
/* 1083 */         if (connStatement != null) connStatement.close(); 
/* 1084 */       } catch (Exception exception) {
/* 1085 */         exception.printStackTrace();
/*      */       } 
/*      */     } 
/* 1088 */     return (Map)hashMap;
/*      */   }
/*      */ 
/*      */   
/*      */   public void bakupTable(String paramString) {
/* 1093 */     RecordSet recordSet = new RecordSet();
/* 1094 */     SimpleDateFormat simpleDateFormat1 = new SimpleDateFormat("MMddHHmmss");
/* 1095 */     SimpleDateFormat simpleDateFormat2 = new SimpleDateFormat("MMddHHmmss");
/* 1096 */     String str = paramString + "_" + simpleDateFormat1.format(new Date());
/* 1097 */     if (str.length() >= 30) {
/* 1098 */       str = "T_Sync_" + simpleDateFormat2.format(new Date()); 
/* 1099 */       try { Thread.sleep(1000L); } catch (Exception exception) {}
/*      */     } 
/* 1101 */     TempleteSyncLogger.log2File("开始备份表" + paramString + ",备份后的表名为：" + str);
/* 1102 */     if ("sqlserver".equalsIgnoreCase(this.dbtype)) {
/* 1103 */       recordSet.executeUpdate("select * into " + str + " from " + paramString, new Object[0]);
/* 1104 */     } else if ("oracle".equalsIgnoreCase(this.dbtype)) {
/* 1105 */       recordSet.executeUpdate("create table " + str + " as select * from " + paramString, new Object[0]);
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean closeTrigger(String paramString) {
/* 1113 */     boolean bool = true;
/*      */     
/* 1115 */     RecordSetTrans recordSetTrans = new RecordSetTrans();
/* 1116 */     recordSetTrans.setAutoCommit(false);
/*      */     try {
/* 1118 */       if ("oracle".equalsIgnoreCase(this.dbtype)) {
/* 1119 */         recordSetTrans.executeSql("ALTER TABLE " + paramString.toUpperCase() + " DISABLE ALL TRIGGERS");
/* 1120 */       } else if ("sqlserver".equalsIgnoreCase(this.dbtype)) {
/* 1121 */         recordSetTrans.executeSql("ALTER TABLE " + paramString.toUpperCase() + " DISABLE TRIGGER ALL");
/*      */       } 
/* 1123 */       recordSetTrans.commit();
/* 1124 */     } catch (Exception exception) {
/* 1125 */       exception.printStackTrace();
/* 1126 */       recordSetTrans.rollback();
/* 1127 */       bool = false;
/*      */     } finally {
/* 1129 */       recordSetTrans.setAutoCommit(true);
/*      */     } 
/* 1131 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean openTrigger(String paramString) {
/* 1139 */     boolean bool = true;
/* 1140 */     RecordSetTrans recordSetTrans = new RecordSetTrans();
/* 1141 */     recordSetTrans.setAutoCommit(false);
/*      */     try {
/* 1143 */       if ("oracle".equalsIgnoreCase(this.dbtype)) {
/* 1144 */         recordSetTrans.executeSql("ALTER TABLE " + paramString.toUpperCase() + " ENABLE ALL TRIGGERS");
/* 1145 */       } else if ("sqlserver".equalsIgnoreCase(this.dbtype)) {
/* 1146 */         recordSetTrans.executeSql("ALTER TABLE " + paramString.toUpperCase() + " ENABLE TRIGGER ALL");
/*      */       } 
/* 1148 */       recordSetTrans.commit();
/* 1149 */     } catch (Exception exception) {
/* 1150 */       bool = false;
/* 1151 */       exception.printStackTrace();
/* 1152 */       recordSetTrans.rollback();
/*      */     } finally {
/* 1154 */       recordSetTrans.setAutoCommit(true);
/*      */     } 
/* 1156 */     return bool;
/*      */   }
/*      */ 
/*      */   
/*      */   public boolean resetSequence(String paramString, int paramInt) {
/* 1161 */     boolean bool = false;
/*      */     
/*      */     try {
/* 1164 */       HashMap<String, String> hashMap = getTiggerSqlsForOracle(paramString);
/* 1165 */       RecordSet recordSet1 = new RecordSet();
/* 1166 */       RecordSet recordSet2 = new RecordSet();
/* 1167 */       RecordSet recordSet3 = new RecordSet();
/* 1168 */       for (Map.Entry<String, String> entry : hashMap.entrySet()) {
/* 1169 */         if ("".equals(Util.null2String((String)entry.getValue()))) {
/*      */           continue;
/*      */         }
/*      */         
/* 1173 */         String str1 = "create or replace " + (String)entry.getValue();
/*      */         
/* 1175 */         String str2 = "";
/* 1176 */         int i = str1.toUpperCase().indexOf(" select ".toUpperCase()) + " select ".length();
/* 1177 */         int j = str1.toUpperCase().indexOf(".nextval".toUpperCase());
/* 1178 */         if (i == 7 || j == -1 || i == -1 || i > j) {
/*      */           continue;
/*      */         }
/* 1181 */         str2 = str1.substring(i, j).trim().toUpperCase();
/*      */         
/* 1183 */         recordSet1.executeQuery("select  'create sequence ' ||sequence_name|| ' minvalue ' ||min_value|| ' maxvalue ' ||max_value|| ' start with " + paramInt + "' || ' increment by ' ||increment_by|| ( case  when cache_size= 0  then  ' nocache'   else   ' cache ' ||cache_size end) || ( case  when cycle_flag='N' then  ' nocycle' when cycle_flag='Y' then ' cycle' else   ' cycle ' ||cycle_flag end) || ( case  when order_flag='N' then  ' noorder' when order_flag='Y' then ' order' else   ' order ' ||order_flag end) ,min_value from user_sequences where sequence_name='" + str2 + "'", new Object[0]);
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/* 1192 */         if (recordSet1.next()) {
/* 1193 */           recordSet2.executeQuery("select * from user_sequences where sequence_name='" + str2 + "' ", new Object[0]);
/* 1194 */           if (recordSet2.next()) {
/* 1195 */             recordSet3.executeUpdate("drop sequence " + str2, new Object[0]);
/*      */           }
/* 1197 */           String str = Util.null2String(recordSet1.getString(1));
/* 1198 */           if ("".equals(str.trim())) {
/* 1199 */             return bool;
/*      */           }
/* 1201 */           bool = recordSet2.executeUpdate(str, new Object[0]);
/*      */         }
/*      */       
/*      */       } 
/* 1205 */     } catch (Exception exception) {
/* 1206 */       exception.printStackTrace();
/* 1207 */       return false;
/*      */     } 
/* 1209 */     return bool;
/*      */   }
/*      */ 
/*      */   
/*      */   public HashMap<String, String> getTiggerSqlsForOracle(String paramString) throws Exception {
/* 1214 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 1215 */     String str = "SELECT s.text,s.name FROM USER_SOURCE s left join user_triggers t on upper(t.trigger_name)=upper(s.name) WHERE TYPE='TRIGGER' and t.table_name='" + paramString.toUpperCase() + "'  ORDER BY LINE ASC";
/* 1216 */     RecordSet recordSet = new RecordSet();
/* 1217 */     recordSet.executeQuery(str, new Object[0]);
/* 1218 */     while (recordSet.next()) {
/* 1219 */       String str1 = Util.null2String(recordSet.getString("text").trim());
/* 1220 */       String str2 = Util.null2String(recordSet.getString("name").trim());
/* 1221 */       if ("".equals(str1)) {
/*      */         continue;
/*      */       }
/*      */       
/* 1225 */       hashMap1.put(str2, Util.null2String((String)hashMap1.get(str2)) + " " + str1);
/*      */     } 
/* 1227 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 1228 */     for (String str1 : hashMap1.keySet()) {
/* 1229 */       String str2 = (String)hashMap1.get(str1);
/* 1230 */       hashMap2.put(str1, str2);
/*      */     } 
/* 1232 */     return (HashMap)hashMap2;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean judgeExistTable(String paramString) throws Exception {
/* 1243 */     if (paramString == null || paramString.length() == 0) {
/* 1244 */       return false;
/*      */     }
/* 1246 */     boolean bool = false;
/* 1247 */     RecordSet recordSet = new RecordSet();
/* 1248 */     boolean bool1 = "oracle".equalsIgnoreCase(recordSet.getDBType());
/* 1249 */     boolean bool2 = "db2".equalsIgnoreCase(recordSet.getDBType());
/*      */     try {
/* 1251 */       String str = "";
/* 1252 */       if (bool1) {
/* 1253 */         str = "select 1 from user_tables where table_name = upper('" + paramString + "')";
/* 1254 */       } else if (bool2) {
/* 1255 */         str = "select 1 from SYSIBM.SYSTABLES where lower(name) = lower('" + paramString + "')";
/* 1256 */       } else if (recordSet.getDBType().equals("mysql")) {
/* 1257 */         str = "select 1 from information_schema.Tables where LOWER(Table_Name)=LOWER('" + paramString + "') ";
/* 1258 */       } else if (recordSet.getDBType().equals("postgresql")) {
/* 1259 */         str = "select 1 from information_schema.Tables where LOWER(Table_Name)=LOWER('" + paramString + "') ";
/*      */       } else {
/* 1261 */         str = "select 1 a from sysobjects where name = '" + paramString + "' and objectproperty(id, 'IsUserTable') = 1";
/* 1262 */       }  recordSet.execute(str);
/* 1263 */       if (recordSet.next())
/* 1264 */         bool = true; 
/* 1265 */     } catch (Exception exception) {
/* 1266 */       exception.printStackTrace();
/*      */       
/* 1268 */       throw new Exception("判断表" + paramString + "是否存在出现异常", exception);
/*      */     } 
/* 1270 */     return bool;
/*      */   }
/*      */   public String getNodeidsByWFids(String paramString) {
/* 1273 */     RecordSet recordSet = new RecordSet();
/* 1274 */     String str = "";
/* 1275 */     recordSet.executeQuery("select nodeid from WORKFLOW_FLOWNODE where workflowid in(" + TempleteSyncToolsUtil.getIdsOfParams(paramString) + ")", new Object[0]);
/* 1276 */     while (recordSet.next()) {
/* 1277 */       String str1 = Util.null2String(recordSet.getString("nodeid"));
/* 1278 */       str = str + ("".equals(str1) ? "" : (str1 + ","));
/*      */     } 
/* 1280 */     return TempleteSyncToolsUtil.getIdsOfParams(str);
/*      */   }
/*      */ 
/*      */   
/*      */   public void insertTempleteSyncLog(String paramString1, String paramString2, Connection paramConnection) throws Exception {
/* 1285 */     Statement statement = paramConnection.createStatement();
/*      */     try {
/* 1287 */       SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
/* 1288 */       String str = simpleDateFormat.format(new Date());
/* 1289 */       statement.executeUpdate("insert into templeteFilesDownload (htmllayoutid,operatetime,verifycode) values('" + paramString1 + "','" + str + "','" + paramString2 + "')");
/* 1290 */       insertCount++;
/* 1291 */     } catch (Exception exception) {
/* 1292 */       throw new Exception(exception.getMessage() + ",向源库中插入同步日志报错，该报错会导致后续无法通过提取码提取文件，请检查是否两个库中都打了最新的工具包!");
/*      */     } finally {
/* 1294 */       if (statement != null)
/* 1295 */         statement.close(); 
/*      */     } 
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/filecheck/TempleteSyncToolsOperation.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */