/*    */ package weaver.templetecheck.filecheck;
/*    */ 
/*    */ import java.util.Properties;
/*    */ import weaver.general.BaseBean;
/*    */ 
/*    */ 
/*    */ public class TempleteSyncToolsPropBean
/*    */ {
/*    */   private static TempleteSyncToolsPropBean dbCompareProp;
/* 10 */   private Properties Properties = new Properties();
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static TempleteSyncToolsPropBean getInstance() {
/*    */     try {
/* 17 */       if (null == dbCompareProp) {
/* 18 */         synchronized (TempleteSyncToolsPropBean.class) {
/* 19 */           if (null == dbCompareProp) {
/* 20 */             dbCompareProp = new TempleteSyncToolsPropBean();
/*    */           }
/*    */         } 
/*    */       }
/* 24 */     } catch (Exception exception) {
/* 25 */       exception.printStackTrace();
/*    */     } 
/* 27 */     return dbCompareProp;
/*    */   }
/*    */   
/*    */   public Properties getProperties() {
/* 31 */     return this.Properties;
/*    */   }
/*    */   
/*    */   public void setProperties(Properties paramProperties) {
/* 35 */     this.Properties = paramProperties;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public Properties editProp(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6) {
/* 44 */     Properties properties = new Properties();
/*    */     try {
/* 46 */       properties.put("dbserver", paramString1);
/* 47 */       properties.put("dbport", paramString2);
/* 48 */       properties.put("dbname", paramString3);
/* 49 */       properties.put("username", paramString4);
/* 50 */       properties.put("password", paramString5);
/* 51 */       properties.put("dbtype", paramString6);
/*    */     }
/* 53 */     catch (Exception exception) {
/* 54 */       (new BaseBean()).writeLog(exception);
/* 55 */       exception.printStackTrace();
/*    */     } 
/* 57 */     return properties;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/filecheck/TempleteSyncToolsPropBean.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */