/*     */ package weaver.templetecheck.filecheck;
/*     */ import java.io.BufferedWriter;
/*     */ import java.io.File;
/*     */ import java.io.FileWriter;
/*     */ import java.io.IOException;
/*     */ import java.io.PrintWriter;
/*     */ import java.text.SimpleDateFormat;
/*     */ import java.util.Calendar;
/*     */ import java.util.regex.Pattern;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.TimeUtil;
/*     */ 
/*     */ public class TempleteSyncLogger {
/*  15 */   public static String LOGFILE = GCONST.getRootPath() + "log" + File.separatorChar + "templetesync" + File.separatorChar;
/*  16 */   Pattern pattern = null;
/*  17 */   Matcher matcher = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static synchronized void write2File(String paramString) {
/*  24 */     log2File(paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static synchronized void writeErrorLog2File(String paramString) {
/*  31 */     errorLog2File(paramString);
/*     */   }
/*     */ 
/*     */   
/*     */   public static void resetLogFile() {
/*  36 */     String str1 = "yyyyMMddHHmmss";
/*  37 */     SimpleDateFormat simpleDateFormat = new SimpleDateFormat(str1);
/*  38 */     Calendar calendar = Calendar.getInstance();
/*  39 */     String str2 = TimeUtil.getCurrentDateString();
/*  40 */     String str3 = simpleDateFormat.format(calendar.getTime());
/*  41 */     String str4 = LOGFILE;
/*  42 */     File file1 = new File(str4);
/*  43 */     if (!file1.exists()) {
/*  44 */       file1.mkdirs();
/*     */     }
/*  46 */     File file2 = new File(str4 + str2 + ".log");
/*  47 */     if (!file2.exists() || file2.length() == 0L) {
/*     */       return;
/*     */     }
/*  50 */     renameFile(str4, str2 + ".log", str3 + ".log");
/*     */   }
/*     */   
/*     */   public static void renameFile(String paramString1, String paramString2, String paramString3) {
/*  54 */     if (!paramString2.equals(paramString3)) {
/*  55 */       File file1 = new File(paramString1 + "/" + paramString2);
/*  56 */       File file2 = new File(paramString1 + "/" + paramString3);
/*  57 */       if (!file1.exists()) {
/*     */         return;
/*     */       }
/*  60 */       if (file2.exists()) {
/*  61 */         (new BaseBean()).writeLog(paramString3 + "已经存在！");
/*     */       } else {
/*  63 */         file1.renameTo(file2);
/*     */       } 
/*     */     } else {
/*  66 */       (new BaseBean()).writeLog("新文件名和旧文件名相同...");
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static synchronized void log2File(String paramString) {
/*  76 */     FileWriter fileWriter = null;
/*  77 */     BufferedWriter bufferedWriter = null;
/*  78 */     PrintWriter printWriter = null;
/*     */     try {
/*  80 */       String str1 = TimeUtil.getCurrentDateString();
/*  81 */       String str2 = LOGFILE;
/*  82 */       File file1 = new File(str2);
/*  83 */       if (!file1.exists()) {
/*  84 */         file1.mkdirs();
/*     */       }
/*  86 */       File file2 = new File(str2 + str1 + ".log");
/*  87 */       if (!file2.exists()) {
/*  88 */         file2.createNewFile();
/*     */       }
/*     */       
/*  91 */       fileWriter = new FileWriter(file2, true);
/*  92 */       bufferedWriter = new BufferedWriter(fileWriter);
/*  93 */       printWriter = new PrintWriter(bufferedWriter);
/*  94 */       String str3 = TimeUtil.getCurrentTimeString();
/*  95 */       printWriter.println("###[" + str3 + "]:" + paramString);
/*     */       
/*  97 */       printWriter.flush();
/*  98 */       bufferedWriter.flush();
/*  99 */       fileWriter.flush();
/*     */       
/* 101 */       printWriter.close();
/* 102 */       bufferedWriter.close();
/* 103 */       fileWriter.close();
/* 104 */     } catch (IOException iOException) {
/* 105 */       iOException.printStackTrace();
/*     */     } finally {
/* 107 */       if (printWriter != null) {
/* 108 */         printWriter.flush();
/* 109 */         printWriter.close();
/*     */       } 
/* 111 */       if (bufferedWriter != null) {
/*     */         try {
/* 113 */           bufferedWriter.flush();
/* 114 */           bufferedWriter.close();
/* 115 */         } catch (IOException iOException) {}
/*     */       }
/* 117 */       if (bufferedWriter != null) {
/*     */         try {
/* 119 */           fileWriter.flush();
/* 120 */           fileWriter.close();
/* 121 */         } catch (IOException iOException) {}
/*     */       }
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static synchronized void errorLog2File(String paramString) {
/* 132 */     FileWriter fileWriter = null;
/* 133 */     BufferedWriter bufferedWriter = null;
/* 134 */     PrintWriter printWriter = null;
/*     */     try {
/* 136 */       String str1 = TimeUtil.getCurrentDateString();
/* 137 */       String str2 = LOGFILE;
/* 138 */       File file1 = new File(str2);
/* 139 */       if (!file1.exists()) {
/* 140 */         file1.mkdirs();
/*     */       }
/* 142 */       File file2 = new File(str2 + "dbupgrade" + str1 + "_error.log");
/* 143 */       if (!file2.exists()) {
/* 144 */         file2.createNewFile();
/*     */       }
/*     */       
/* 147 */       fileWriter = new FileWriter(file2, true);
/* 148 */       bufferedWriter = new BufferedWriter(fileWriter);
/* 149 */       printWriter = new PrintWriter(bufferedWriter);
/* 150 */       String str3 = TimeUtil.getCurrentTimeString();
/* 151 */       printWriter.println("###[" + str3 + "]:" + paramString);
/*     */       
/* 153 */       printWriter.flush();
/* 154 */       bufferedWriter.flush();
/* 155 */       fileWriter.flush();
/*     */       
/* 157 */       printWriter.close();
/* 158 */       bufferedWriter.close();
/* 159 */       fileWriter.close();
/* 160 */     } catch (IOException iOException) {
/* 161 */       iOException.printStackTrace();
/*     */     } finally {
/* 163 */       if (printWriter != null) {
/* 164 */         printWriter.flush();
/* 165 */         printWriter.close();
/*     */       } 
/* 167 */       if (bufferedWriter != null) {
/*     */         try {
/* 169 */           bufferedWriter.flush();
/* 170 */           bufferedWriter.close();
/* 171 */         } catch (IOException iOException) {}
/*     */       }
/* 173 */       if (bufferedWriter != null)
/*     */         try {
/* 175 */           fileWriter.flush();
/* 176 */           fileWriter.close();
/* 177 */         } catch (IOException iOException) {} 
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/filecheck/TempleteSyncLogger.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */