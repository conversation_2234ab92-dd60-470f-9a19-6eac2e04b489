/*     */ package weaver.templetecheck.filecheck;
/*     */ import com.alibaba.fastjson.JSONArray;
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import java.io.File;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Arrays;
/*     */ import java.util.HashMap;
/*     */ import java.util.LinkedHashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.Set;
/*     */ import javax.xml.parsers.DocumentBuilder;
/*     */ import javax.xml.parsers.DocumentBuilderFactory;
/*     */ import org.jdom.Document;
/*     */ import org.jdom.Element;
/*     */ import org.jdom.input.SAXBuilder;
/*     */ import org.w3c.dom.Element;
/*     */ import org.w3c.dom.NodeList;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ public class MobileModeReportOperation {
/*  27 */   private static final String FOLDER_PATH = GCONST.getRootPath() + File.separatorChar + "mobilemode" + File.separatorChar + "skin";
/*  28 */   private static final String mobileComponent = GCONST.getRootPath() + "templetecheck" + File.separatorChar + "filecheck" + File.separatorChar + "mobileComponent.xml";
/*  29 */   FileUtil fileUtil = new FileUtil();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONObject getMobileModeReportInfo() {
/*  37 */     JSONObject jSONObject = new JSONObject();
/*  38 */     RecordSet recordSet = new RecordSet();
/*     */ 
/*     */     
/*     */     try {
/*  42 */       List list = (List)getAllSkin().get("skinList");
/*  43 */       jSONObject.put("mobilemodeskin", Integer.valueOf(list.size()));
/*     */ 
/*     */       
/*  46 */       String str = " select count(1) as counts from MobileAppBaseInfo a where a.isdelete=0 or a.isdelete is null ";
/*  47 */       recordSet.executeQuery(str, new Object[0]);
/*  48 */       if (recordSet.next()) {
/*  49 */         jSONObject.put("mobilemodeapp", Util.null2String(recordSet.getString("counts"), "0"));
/*     */       }
/*     */ 
/*     */       
/*  53 */       str = " select count(1) as counts from APPHOMEPAGE a join MobileAppBaseInfo b on a.appid=b.id where b.isdelete=0 or b.isdelete is null ";
/*  54 */       recordSet.executeQuery(str, new Object[0]);
/*  55 */       if (recordSet.next()) {
/*  56 */         jSONObject.put("mobilemodepage", Util.null2String(recordSet.getString("counts"), "0"));
/*     */       }
/*     */       
/*  59 */       Map<String, Map<String, String>> map = getMobileComponentMap();
/*     */       
/*  61 */       str = " select  mectype,mecparam  from  MOBILEEXTENDCOMPONENT ";
/*  62 */       recordSet.executeQuery(str, new Object[0]);
/*  63 */       byte b1 = 0;
/*  64 */       byte b2 = 0;
/*  65 */       boolean bool = false;
/*  66 */       byte b3 = 0;
/*  67 */       while (recordSet.next()) {
/*  68 */         String str1 = recordSet.getString("mectype");
/*  69 */         String str2 = recordSet.getString("mecparam");
/*  70 */         Map map1 = map.get(str1.toLowerCase());
/*  71 */         if (map1 == null)
/*  72 */           continue;  b3++;
/*  73 */         List<String> list1 = null;
/*     */         try {
/*  75 */           list1 = jsonToList(str2);
/*  76 */         } catch (Exception exception) {
/*  77 */           recordSet.writeLog("MobileModeReportOperation 解析json失败，不符合json格式的json字符串");
/*     */           continue;
/*     */         } 
/*  80 */         List<String> list2 = Arrays.asList(((String)map1.get("html")).split(","));
/*  81 */         for (String str3 : list2) {
/*  82 */           boolean bool1 = false;
/*  83 */           for (String str4 : list1) {
/*  84 */             if (str4.toLowerCase().indexOf(str3.toLowerCase() + "==>>==") == 0) {
/*  85 */               str4 = str4.substring(str4.indexOf("==>>==") + 6);
/*  86 */               if (!"".equals(str3) && !"".equals(str4)) {
/*  87 */                 b1++;
/*  88 */                 bool1 = true;
/*     */                 break;
/*     */               } 
/*     */             } 
/*     */           } 
/*  93 */           if (bool1)
/*     */             break; 
/*     */         } 
/*  96 */         list2 = Arrays.asList(((String)map1.get("sql")).split(","));
/*  97 */         for (String str3 : list2) {
/*  98 */           boolean bool1 = false;
/*  99 */           for (String str4 : list1) {
/* 100 */             if (str4.toLowerCase().indexOf(str3.toLowerCase() + "==>>==") == 0) {
/* 101 */               str4 = str4.substring(str4.indexOf("==>>==") + 6);
/* 102 */               if (!"".equals(str3) && !"".equals(str4)) {
/* 103 */                 b2++;
/* 104 */                 bool1 = true;
/*     */                 break;
/*     */               } 
/*     */             } 
/*     */           } 
/* 109 */           if (bool1) {
/*     */             break;
/*     */           }
/*     */         } 
/*     */       } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 128 */       jSONObject.put("mobileexpendcomponent", "" + b3);
/*     */ 
/*     */       
/* 131 */       jSONObject.put("mobileexpendcomponent_html", "" + b1);
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 136 */       jSONObject.put("mobileexpendcomponent_sql", "" + b2);
/*     */ 
/*     */ 
/*     */     
/*     */     }
/* 141 */     catch (Exception exception) {
/* 142 */       exception.printStackTrace();
/* 143 */       return null;
/*     */     } 
/*     */     
/* 146 */     return jSONObject;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<Map<String, String>> getMobileModeReportDetailInfo(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 161 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 162 */     int i = Util.getIntValue(paramMap.get("pageSize"));
/* 163 */     int j = Util.getIntValue(paramMap.get("pageIndex"));
/* 164 */     String str = Util.null2String(paramMap.get("detailtype"));
/* 165 */     Map<String, Map<String, String>> map = getMobileComponentMap();
/* 166 */     RecordSet recordSet = new RecordSet();
/* 167 */     if ("mobilemodeskin".equalsIgnoreCase(str)) {
/* 168 */       List list = null;
/*     */       try {
/* 170 */         list = (List)getAllSkin().get("skinList");
/* 171 */       } catch (Exception exception) {
/* 172 */         exception.printStackTrace();
/*     */       } 
/* 174 */       for (Map map1 : list) {
/* 175 */         HashMap<Object, Object> hashMap = new HashMap<>();
/* 176 */         hashMap.put("name", Util.null2String((String)map1.get("name")));
/* 177 */         hashMap.put("location", "" + SystemEnv.getHtmlLabelName(84213, ThreadVarLanguage.getLang()) + " > " + Util.null2String((String)map1.get("name")));
/* 178 */         arrayList.add(hashMap);
/*     */       } 
/* 180 */     } else if ("mobilemodeapp".equalsIgnoreCase(str)) {
/* 181 */       recordSet.execute("select a.id,a.appname as name from MobileAppBaseInfo a where a.isdelete=0 or a.isdelete is null");
/* 182 */       while (recordSet.next()) {
/* 183 */         HashMap<Object, Object> hashMap = new HashMap<>();
/* 184 */         String str1 = Util.null2String(recordSet.getString("name"));
/* 185 */         hashMap.put("name", str1);
/* 186 */         hashMap.put("location", "" + SystemEnv.getHtmlLabelName(25432, ThreadVarLanguage.getLang()) + ":" + str1);
/* 187 */         arrayList.add(hashMap);
/*     */       } 
/* 189 */     } else if ("mobilemodepage".equalsIgnoreCase(str)) {
/* 190 */       recordSet.execute(" select  a.id,a.pagename as name,b.appname from apphomepage a join MobileAppBaseInfo b on a.appid=b.id where b.isdelete=0 or b.isdelete is null ");
/* 191 */       while (recordSet.next()) {
/* 192 */         HashMap<Object, Object> hashMap = new HashMap<>();
/* 193 */         String str1 = Util.null2String(recordSet.getString("name"));
/* 194 */         String str2 = Util.null2String(recordSet.getString("appname"));
/* 195 */         hashMap.put("name", str1);
/* 196 */         hashMap.put("location", "" + SystemEnv.getHtmlLabelName(25432, ThreadVarLanguage.getLang()) + ":" + str2 + " > " + getFolderPath(recordSet.getString("id")));
/* 197 */         arrayList.add(hashMap);
/*     */       } 
/* 199 */     } else if (str.toLowerCase().indexOf("mobileexpendcomponent") >= 0) {
/* 200 */       recordSet.execute("select a.id,a.mectype,a.mecparam,b.pagename,b.id as pageid,c.appname from MOBILEEXTENDCOMPONENT a left join APPHOMEPAGE b on a.objid=b.id left join MobileAppBaseInfo c on b.appid = c.id order by c.id");
/* 201 */       while (recordSet.next()) {
/* 202 */         HashMap<Object, Object> hashMap = new HashMap<>();
/* 203 */         String str1 = Util.null2String(recordSet.getString("id"));
/* 204 */         String str2 = Util.null2String(recordSet.getString("mectype"));
/* 205 */         String str3 = recordSet.getString("mecparam");
/* 206 */         String str4 = Util.null2String(recordSet.getString("appname"));
/* 207 */         Map map1 = map.get(str2.toLowerCase());
/* 208 */         if (map1 == null)
/* 209 */           continue;  String str5 = (String)map1.get("name");
/* 210 */         hashMap.put("name", str5);
/* 211 */         hashMap.put("location", "" + SystemEnv.getHtmlLabelName(25432, ThreadVarLanguage.getLang()) + ":" + str4 + " > " + getFolderPath(recordSet.getString("pageid")) + " > " + SystemEnv.getHtmlLabelName(18700, ThreadVarLanguage.getLang()) + ":" + str5 + "(id:" + str1 + ")");
/*     */         
/* 213 */         List<String> list1 = jsonToList(str3);
/* 214 */         List<String> list2 = null;
/* 215 */         if ("mobileexpendcomponent".equals(str)) {
/* 216 */           arrayList.add(hashMap); continue;
/*     */         } 
/* 218 */         if ("mobileexpendcomponent_html".equals(str)) {
/* 219 */           list2 = Arrays.asList(((String)map1.get("html")).split(","));
/* 220 */         } else if ("mobileexpendcomponent_sql".equals(str)) {
/* 221 */           list2 = Arrays.asList(((String)map1.get("sql")).split(","));
/*     */         } 
/*     */ 
/*     */ 
/*     */         
/* 226 */         for (String str6 : list2) {
/* 227 */           boolean bool = false;
/* 228 */           for (String str7 : list1) {
/* 229 */             if (str7.toLowerCase().indexOf(str6.toLowerCase() + "==>>==") == 0) {
/* 230 */               str7 = str7.substring(str7.indexOf("==>>==") + 6);
/* 231 */               if (!"".equals(str6) && !"".equals(str7)) {
/* 232 */                 arrayList.add(hashMap);
/* 233 */                 bool = true;
/*     */                 break;
/*     */               } 
/*     */             } 
/*     */           } 
/* 238 */           if (bool)
/*     */             break; 
/*     */         } 
/*     */       } 
/* 242 */     }  return (List)arrayList;
/*     */   }
/*     */   
/*     */   public String getMatchDevContentRuleid() {
/* 246 */     String str = "";
/* 247 */     RecordSet recordSet = new RecordSet();
/* 248 */     recordSet.execute("select id from checkmobilemoderule ");
/* 249 */     while (recordSet.next()) {
/* 250 */       str = str + recordSet.getString("id") + ",";
/*     */     }
/* 252 */     return str;
/*     */   }
/*     */   
/*     */   public Map<String, Object> getAllSkin() throws Exception {
/* 256 */     String str = "";
/* 257 */     ArrayList<Map<String, String>> arrayList = new ArrayList();
/*     */     
/* 259 */     File file = new File(FOLDER_PATH);
/* 260 */     File[] arrayOfFile = file.listFiles(new FileFilter()
/*     */         {
/*     */           public boolean accept(File param1File) {
/* 263 */             return (param1File.isDirectory() && param1File.getName().length() == 32);
/*     */           }
/*     */         });
/*     */     
/* 267 */     DocumentBuilderFactory documentBuilderFactory = DocumentBuilderFactory.newInstance();
/* 268 */     TempleteSecurityUtil.setDBFFeature(documentBuilderFactory);
/* 269 */     DocumentBuilder documentBuilder = documentBuilderFactory.newDocumentBuilder();
/*     */     
/* 271 */     for (byte b = 0; arrayOfFile != null && b < arrayOfFile.length; b++) {
/* 272 */       File file1 = arrayOfFile[b];
/*     */       
/* 274 */       File file2 = new File(file1, "_.xml");
/* 275 */       if (file2.exists()) {
/*     */         
/*     */         try {
/*     */           
/* 279 */           Map<String, String> map = getSkinMap(file2, documentBuilder);
/* 280 */           if (map != null) {
/* 281 */             arrayList.add(map);
/*     */           }
/*     */         }
/* 284 */         catch (Exception exception) {
/* 285 */           str = str + "" + SystemEnv.getHtmlLabelName(84213, ThreadVarLanguage.getLang()) + "\"" + file1.getName() + "\"" + SystemEnv.getHtmlLabelName(10004209, ThreadVarLanguage.getLang()) + "（" + exception.getMessage() + "）；";
/* 286 */           exception.printStackTrace();
/*     */         } 
/*     */       }
/*     */     } 
/* 290 */     Collections.sort(arrayList, new Comparator<Map<String, String>>()
/*     */         {
/*     */           public int compare(Map<String, String> param1Map1, Map<String, String> param1Map2) {
/* 293 */             double d1 = Util.getDoubleValue(param1Map1.get("order"), Double.MAX_VALUE);
/* 294 */             double d2 = Util.getDoubleValue(param1Map1.get("order"), Double.MAX_VALUE);
/* 295 */             if (d1 > d2)
/* 296 */               return 1; 
/* 297 */             if (d1 < d2) {
/* 298 */               return -1;
/*     */             }
/* 300 */             return 0;
/*     */           }
/*     */         });
/*     */ 
/*     */     
/* 305 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 306 */     hashMap.put("message", str);
/* 307 */     hashMap.put("skinList", arrayList);
/* 308 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   private Map<String, String> getSkinMap(File paramFile, DocumentBuilder paramDocumentBuilder) throws Exception {
/* 313 */     Document document = paramDocumentBuilder.parse(paramFile);
/*     */     
/* 315 */     NodeList nodeList = document.getElementsByTagName("skin");
/*     */     
/* 317 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/* 319 */     if (nodeList.getLength() > 0) {
/*     */ 
/*     */       
/* 322 */       Element element1 = (Element)nodeList.item(0);
/*     */       
/* 324 */       Element element2 = (Element)element1.getElementsByTagName("id").item(0);
/* 325 */       String str1 = (element2 == null) ? "" : element2.getTextContent().trim();
/* 326 */       hashMap.put("id", str1);
/*     */       
/* 328 */       Element element3 = (Element)element1.getElementsByTagName("name").item(0);
/* 329 */       String str2 = (element3 == null) ? "" : element3.getTextContent().trim();
/* 330 */       hashMap.put("name", str2);
/*     */       
/* 332 */       Element element4 = (Element)element1.getElementsByTagName("previewImg").item(0);
/* 333 */       String str3 = (element4 == null) ? "" : element4.getTextContent().trim();
/* 334 */       hashMap.put("previewimg", str3);
/*     */       
/* 336 */       Element element5 = (Element)element1.getElementsByTagName("isEnabled").item(0);
/* 337 */       String str4 = (element5 == null) ? "" : element5.getTextContent().trim();
/* 338 */       hashMap.put("isenabled", str4);
/*     */       
/* 340 */       Element element6 = (Element)element1.getElementsByTagName("order").item(0);
/* 341 */       String str5 = (element6 == null) ? "" : element6.getTextContent().trim();
/* 342 */       hashMap.put("order", str5);
/*     */       
/* 344 */       Element element7 = (Element)element1.getElementsByTagName("subCompanyId").item(0);
/* 345 */       String str6 = (element7 == null) ? "" : element7.getTextContent().trim();
/* 346 */       hashMap.put("subcompanyid", str6);
/*     */       
/* 348 */       Element element8 = (Element)element1.getElementsByTagName("ecVersion").item(0);
/* 349 */       String str7 = (element8 == null) ? "" : element8.getTextContent().trim();
/* 350 */       hashMap.put("ecversion", str7);
/*     */       
/* 352 */       hashMap.put("csspath", paramFile.getAbsolutePath().replace("_.xml", "_.css"));
/*     */     } 
/*     */     
/* 355 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, Map<String, String>> getMobileComponentMap() {
/* 360 */     SAXBuilder sAXBuilder = new SAXBuilder();
/* 361 */     File file = new File(this.fileUtil.getPath(mobileComponent));
/* 362 */     if (!file.canWrite()) {
/* 363 */       file.setWritable(true);
/*     */     }
/* 365 */     LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/*     */     
/*     */     try {
/* 368 */       Document document = sAXBuilder.build(file);
/* 369 */       Element element = document.getRootElement();
/* 370 */       List list = element.getChildren("mectype");
/* 371 */       for (Element element1 : list) {
/*     */         
/* 373 */         String str1 = element1.getAttributeValue("id");
/* 374 */         String str2 = element1.getAttributeValue("name");
/* 375 */         HashMap<Object, Object> hashMap = new HashMap<>();
/* 376 */         String str3 = Util.null2String(element1.getChildTextTrim("html"));
/* 377 */         String str4 = Util.null2String(element1.getChildTextTrim("sql"));
/* 378 */         String str5 = Util.null2String(element1.getChildTextTrim("java"));
/* 379 */         hashMap.put("name", str2);
/* 380 */         hashMap.put("html", str3);
/* 381 */         hashMap.put("java", str5);
/* 382 */         hashMap.put("sql", str4);
/* 383 */         linkedHashMap.put(str1.toLowerCase(), hashMap);
/*     */       } 
/* 385 */     } catch (Exception exception) {
/* 386 */       exception.printStackTrace();
/*     */     } 
/* 388 */     return (Map)linkedHashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void analysisJson(Object paramObject, String paramString, List<String> paramList) {
/* 394 */     if (paramObject instanceof JSONArray) {
/* 395 */       JSONArray jSONArray = (JSONArray)paramObject;
/* 396 */       for (byte b = 0; b < jSONArray.size(); b++) {
/* 397 */         analysisJson(jSONArray.get(b), paramString, paramList);
/*     */       }
/* 399 */     } else if (paramObject instanceof JSONObject) {
/* 400 */       JSONObject jSONObject = (JSONObject)paramObject;
/* 401 */       Set set = jSONObject.keySet();
/* 402 */       for (String str1 : set) {
/* 403 */         Object object = jSONObject.get(str1);
/* 404 */         String str2 = paramString + str1 + ".";
/*     */         
/* 406 */         if (object instanceof JSONArray) {
/* 407 */           JSONArray jSONArray = (JSONArray)object;
/* 408 */           analysisJson(jSONArray, str2, paramList); continue;
/* 409 */         }  if (object instanceof JSONObject) {
/* 410 */           analysisJson(object, str2, paramList); continue;
/*     */         } 
/* 412 */         paramList.add(paramString + str1 + "==>>==" + ((object != null) ? object.toString() : "") + "");
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public List<String> jsonToList(String paramString) {
/* 419 */     ArrayList<String> arrayList = new ArrayList();
/* 420 */     JSONObject jSONObject = JSONObject.parseObject(paramString);
/* 421 */     analysisJson(jSONObject, "", arrayList);
/* 422 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getFolderPath(String paramString) {
/* 441 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 442 */     String str = " select id,foldername,pid,appid from AppHomepageFolder ";
/* 443 */     RecordSet recordSet = new RecordSet();
/* 444 */     recordSet.execute(str);
/* 445 */     while (recordSet.next()) {
/* 446 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 447 */       String str1 = recordSet.getString("id");
/* 448 */       hashMap1.put("foldername", Util.null2String(recordSet.getString("foldername")));
/* 449 */       hashMap1.put("pid", Util.null2String(recordSet.getString("pid")));
/* 450 */       hashMap1.put("appid", Util.null2String(recordSet.getString("appid")));
/* 451 */       hashMap1.put("id", str1);
/* 452 */       hashMap.put(str1, hashMap1);
/*     */     } 
/* 454 */     return getAppPagePath((Map)hashMap, paramString);
/*     */   }
/*     */ 
/*     */   
/*     */   public static String getAppPagePath(Map<String, Map<String, String>> paramMap, String paramString) {
/* 459 */     String str1 = "";
/* 460 */     String str2 = "";
/* 461 */     String str3 = "";
/*     */     
/* 463 */     RecordSet recordSet1 = new RecordSet();
/* 464 */     RecordSet recordSet2 = new RecordSet();
/* 465 */     recordSet1.execute("select a.pagename,a.pid,m.modelid from apphomepage a left join AppHomepage_Model m on a.id = m.apphomepageid  where id='" + paramString + "'");
/* 466 */     if (recordSet1.next()) {
/* 467 */       str2 = "" + SystemEnv.getHtmlLabelName(22967, ThreadVarLanguage.getLang()) + ":" + Util.null2String(recordSet1.getString("pagename"));
/* 468 */       str1 = Util.null2String(recordSet1.getString("pid"));
/* 469 */       str3 = Util.null2String(recordSet1.getString("modelid"));
/*     */     } else {
/* 471 */       return "" + SystemEnv.getHtmlLabelName(10004210, ThreadVarLanguage.getLang()) + "";
/*     */     } 
/*     */     
/* 474 */     if ("".equals(str3)) {
/* 475 */       while (!"".equals(str1)) {
/* 476 */         str2 = "" + SystemEnv.getHtmlLabelName(33092, ThreadVarLanguage.getLang()) + ":" + (String)((Map)paramMap.get(str1)).get("foldername") + " > " + str2;
/* 477 */         str1 = (String)((Map)paramMap.get(str1)).get("pid");
/*     */       } 
/* 479 */       str2 = "" + SystemEnv.getHtmlLabelName(33092, ThreadVarLanguage.getLang()) + ":" + SystemEnv.getHtmlLabelName(32309, (new User()).getLanguage()) + " > " + str2;
/*     */     } else {
/* 481 */       recordSet2.executeQuery("  select entityname from MobileAppModelInfo where modelid='" + str3 + "'", new Object[0]);
/* 482 */       if (recordSet2.next()) {
/* 483 */         str2 = "" + SystemEnv.getHtmlLabelName(19049, ThreadVarLanguage.getLang()) + ":" + Util.null2String(recordSet2.getString("entityname")) + " > " + str2;
/*     */       }
/*     */     } 
/* 486 */     return str2;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<Map<String, String>> getMobileModeCheckResultList(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 500 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 501 */     int i = Util.getIntValue(paramMap.get("pageSize"));
/* 502 */     int j = Util.getIntValue(paramMap.get("pageIndex"));
/* 503 */     String str1 = Util.null2String(paramMap.get("ruleid"));
/* 504 */     String str2 = Util.null2String(paramMap.get("workflowname"));
/* 505 */     String str3 = Util.null2String(paramMap.get("nodename"));
/* 506 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 508 */     String str4 = " where  1=1 ";
/* 509 */     if (!"".equals(str1)) {
/* 510 */       if (str1.endsWith(",")) {
/* 511 */         str1 = str1.substring(0, str1.length() - 1);
/*     */       }
/*     */       
/* 514 */       str4 = str4 + " and ruleid in (" + str1 + ")";
/*     */     } 
/* 516 */     if (!"".equals(str2)) {
/* 517 */       str4 = str4 + " and workflowname like '%" + str2 + "%'";
/*     */     }
/* 519 */     if (!"".equals(str3)) {
/* 520 */       str4 = str4 + " and nodename like '%" + str3 + "%'";
/*     */     }
/* 522 */     str4 = str4 + " group by t1.workflowname,t1.nodehtmllayoutid,t1.nodename,t1.detailtype,t1.refertable,t1.filepath,t1.contenttype ";
/*     */     
/* 524 */     String str5 = " select  max(t1.id) as id,t1.workflowname,t1.nodehtmllayoutid,t1.nodename,t1.detailtype,t1.refertable,t1.filepath,t1.contenttype   from checkmobilemoderesult t1 " + str4 + " order by  t1.nodehtmllayoutid,t1.detailtype ";
/*     */ 
/*     */     
/* 527 */     recordSet.execute(str5);
/*     */     
/* 529 */     while (recordSet.next()) {
/* 530 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 531 */       String str6 = Util.null2String(recordSet.getString("filepath"));
/* 532 */       String str7 = Util.null2String(recordSet.getString("detailtype"));
/* 533 */       String str8 = Util.null2String(recordSet.getString("nodename"));
/* 534 */       if ("mobilemodeskin".equals(str7)) {
/* 535 */         hashMap.put("nodename", str8 + "[" + str6 + "]");
/*     */       } else {
/* 537 */         hashMap.put("nodename", str8);
/*     */       } 
/* 539 */       hashMap.put("id", Util.null2String(recordSet.getString("id")));
/* 540 */       hashMap.put("workflowname", Util.null2String(recordSet.getString("workflowname")));
/* 541 */       hashMap.put("nodehtmllayoutid", Util.null2String(recordSet.getString("nodehtmllayoutid")));
/* 542 */       hashMap.put("refertable", Util.null2String(recordSet.getString("refertable")));
/* 543 */       hashMap.put("detailtype", str7);
/* 544 */       hashMap.put("contenttype", Util.null2String(recordSet.getString("contenttype")));
/* 545 */       arrayList.add(hashMap);
/*     */     } 
/* 547 */     return (List)arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getAppidForFastEdit(String paramString1, String paramString2) {
/* 554 */     String str1 = "";
/* 555 */     if ("".equals(Util.null2String(paramString1)) || "".equals(Util.null2String(paramString2))) {
/* 556 */       return str1;
/*     */     }
/* 558 */     RecordSet recordSet = new RecordSet();
/* 559 */     String str2 = "";
/* 560 */     if ("mobilemodepage".equalsIgnoreCase(paramString2) || "mobilemodepageattr".equalsIgnoreCase(paramString2)) {
/* 561 */       str2 = "select appid from apphomepage where id='" + paramString1 + "'";
/* 562 */     } else if (paramString2.toLowerCase().indexOf("mobileexpendcomponent") != -1) {
/* 563 */       str2 = "select b.appid from MOBILEEXTENDCOMPONENT a left join APPHOMEPAGE b on a.objid=b.id where a.id='" + paramString1 + "'";
/*     */     } 
/* 565 */     if (!"".equals(str2)) {
/* 566 */       recordSet.executeQuery(str2, new Object[0]);
/* 567 */       if (recordSet.next()) {
/* 568 */         str1 = Util.null2String(recordSet.getString("appid"));
/*     */       }
/*     */     } 
/* 571 */     return str1;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/filecheck/MobileModeReportOperation.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */