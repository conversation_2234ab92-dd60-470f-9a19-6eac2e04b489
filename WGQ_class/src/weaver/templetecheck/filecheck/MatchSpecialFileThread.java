/*    */ package weaver.templetecheck.filecheck;
/*    */ 
/*    */ import weaver.general.BaseBean;
/*    */ 
/*    */ public class MatchSpecialFileThread
/*    */   implements Runnable {
/*  7 */   public String ruleid = "";
/*  8 */   public String filepath = "";
/*  9 */   public String checktype = "";
/* 10 */   public String fileids = "";
/* 11 */   public static int MatchSpecialStatus = 0;
/* 12 */   public String isWorkflow = "";
/*    */   public MatchSpecialFileThread(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5) {
/* 14 */     this.ruleid = paramString1;
/* 15 */     this.filepath = paramString2;
/* 16 */     this.checktype = paramString3;
/* 17 */     this.fileids = paramString4;
/* 18 */     this.isWorkflow = paramString5;
/*    */   }
/*    */   
/*    */   public void run() {
/* 22 */     CheckRule.runningStatus = 1;
/*    */     try {
/* 24 */       if (MatchSpecialStatus == 1) {
/* 25 */         (new BaseBean()).writeLog("execute MatchSpecialFileThread ");
/* 26 */         CheckRule checkRule = new CheckRule();
/* 27 */         checkRule.matchSpecialFile(this.ruleid, this.filepath, this.checktype, this.fileids, this.isWorkflow);
/*    */       } 
/* 29 */     } catch (Exception exception) {
/* 30 */       (new BaseBean()).writeLog("error to execute MarchSpecialFileThread: errorinfo:" + exception.toString());
/*    */     } 
/* 32 */     MatchSpecialStatus = 0;
/* 33 */     CheckRule.runningStatus = 0;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/filecheck/MatchSpecialFileThread.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */