/*     */ package weaver.templetecheck.filecheck;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.LinkedHashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ 
/*     */ public class ParseSheet
/*     */ {
/*     */   private int detailflag;
/*  12 */   private Map<String, Object> fieldmap = new HashMap<>();
/*     */   
/*     */   private int rownum;
/*     */   private int colnum;
/*  16 */   private List<String> detailSheet = new ArrayList<>();
/*  17 */   private int edtitleinrow = -1;
/*  18 */   private int edtailinrow = -1;
/*  19 */   private Map<String, String> fieldattr_map = new HashMap<>();
/*     */   
/*  21 */   private Map<String, Object> data_rowheads = new LinkedHashMap<>();
/*  22 */   private Map<String, Object> data_colheads = new LinkedHashMap<>();
/*  23 */   private List<LinkedHashMap<String, Object>> data_ec = new ArrayList<>();
/*     */   
/*  25 */   private List<LinkedHashMap<String, Object>> plugin_rows = new ArrayList<>();
/*  26 */   private List<LinkedHashMap<String, Object>> plugin_columns = new ArrayList<>();
/*  27 */   private List<LinkedHashMap<String, Integer>> plugin_combine = new ArrayList<>();
/*  28 */   private Map<String, Object> plugin_data = new LinkedHashMap<>();
/*     */   
/*     */   public void buildDataEcMap(CellAttr paramCellAttr, LinkedHashMap<String, Object> paramLinkedHashMap) {
/*  31 */     paramLinkedHashMap.put("id", paramCellAttr.getRowid() + "," + paramCellAttr.getColid());
/*  32 */     paramLinkedHashMap.put("rowspan", paramCellAttr.getRowspan() + "");
/*  33 */     paramLinkedHashMap.put("colspan", paramCellAttr.getColspan() + "");
/*  34 */     paramLinkedHashMap.put("etype", paramCellAttr.getEtype() + "");
/*  35 */     paramLinkedHashMap.put("field", paramCellAttr.getFieldid() + "");
/*  36 */     paramLinkedHashMap.put("evalue", paramCellAttr.getEvalue());
/*     */     
/*  38 */     if (paramCellAttr.getEtype() == 8 || paramCellAttr.getEtype() == 9)
/*  39 */       return;  if (paramCellAttr.getEtype() == 7)
/*  40 */       paramLinkedHashMap.put("detail", "detail_" + paramCellAttr.getEvalue().replace("明细表", "").replace("明细", "")); 
/*  41 */     paramLinkedHashMap.put("fieldtype", paramCellAttr.getFieldtype());
/*  42 */     if (!"".equals(paramCellAttr.getBackground_color()))
/*  43 */       paramLinkedHashMap.put("backgroundColor", paramCellAttr.getBackground_color()); 
/*  44 */     LinkedHashMap<Object, Object> linkedHashMap1 = new LinkedHashMap<>();
/*  45 */     paramLinkedHashMap.put("font", linkedHashMap1);
/*  46 */     if (paramCellAttr.isItalic()) linkedHashMap1.put("italic", "true"); 
/*  47 */     if (paramCellAttr.isBold()) linkedHashMap1.put("bold", "true"); 
/*  48 */     if (paramCellAttr.isUnderline()) linkedHashMap1.put("underline", "true"); 
/*  49 */     switch (paramCellAttr.getHalign()) { case 0:
/*  50 */         linkedHashMap1.put("text-align", "left"); break;
/*  51 */       case 1: linkedHashMap1.put("text-align", "center"); break;
/*  52 */       case 2: linkedHashMap1.put("text-align", "right"); break; }
/*     */     
/*  54 */     switch (paramCellAttr.getValign()) { case 0:
/*  55 */         linkedHashMap1.put("valign", "top"); break;
/*  56 */       case 1: linkedHashMap1.put("valign", "middle"); break;
/*  57 */       case 2: linkedHashMap1.put("valign", "bottom"); break; }
/*     */     
/*  59 */     linkedHashMap1.put("font-size", paramCellAttr.getFont_size());
/*  60 */     linkedHashMap1.put("font-family", paramCellAttr.getFont_family());
/*  61 */     linkedHashMap1.put("color", paramCellAttr.getFont_color());
/*     */     
/*  63 */     if (paramCellAttr.getIndent() > 0.0D) {
/*  64 */       paramLinkedHashMap.put("etxtindent", String.valueOf(paramCellAttr.getIndent()));
/*     */     }
/*  66 */     ArrayList<LinkedHashMap<Object, Object>> arrayList = new ArrayList();
/*  67 */     paramLinkedHashMap.put("eborder", arrayList);
/*  68 */     LinkedHashMap<Object, Object> linkedHashMap2 = new LinkedHashMap<>();
/*  69 */     linkedHashMap2.put("kind", "top");
/*  70 */     linkedHashMap2.put("style", paramCellAttr.getBtop_style() + "");
/*  71 */     linkedHashMap2.put("color", paramCellAttr.getBtop_color());
/*  72 */     arrayList.add(linkedHashMap2);
/*  73 */     LinkedHashMap<Object, Object> linkedHashMap3 = new LinkedHashMap<>();
/*  74 */     linkedHashMap3.put("kind", "bottom");
/*  75 */     linkedHashMap3.put("style", paramCellAttr.getBbottom_style() + "");
/*  76 */     linkedHashMap3.put("color", paramCellAttr.getBbottom_color());
/*  77 */     arrayList.add(linkedHashMap3);
/*  78 */     LinkedHashMap<Object, Object> linkedHashMap4 = new LinkedHashMap<>();
/*  79 */     linkedHashMap4.put("kind", "left");
/*  80 */     linkedHashMap4.put("style", paramCellAttr.getBleft_style() + "");
/*  81 */     linkedHashMap4.put("color", paramCellAttr.getBleft_color());
/*  82 */     arrayList.add(linkedHashMap4);
/*  83 */     LinkedHashMap<Object, Object> linkedHashMap5 = new LinkedHashMap<>();
/*  84 */     linkedHashMap5.put("kind", "right");
/*  85 */     linkedHashMap5.put("style", paramCellAttr.getBright_style() + "");
/*  86 */     linkedHashMap5.put("color", paramCellAttr.getBright_color());
/*  87 */     arrayList.add(linkedHashMap5);
/*     */   }
/*     */   
/*     */   public void buildPluginCellMap(CellAttr paramCellAttr, LinkedHashMap<String, Object> paramLinkedHashMap) {
/*  91 */     paramLinkedHashMap.put("value", paramCellAttr.getEvalue());
/*  92 */     LinkedHashMap<Object, Object> linkedHashMap1 = new LinkedHashMap<>();
/*  93 */     paramLinkedHashMap.put("style", linkedHashMap1);
/*     */     
/*  95 */     if (!"".equals(paramCellAttr.getBackground_color()))
/*  96 */       linkedHashMap1.put("backColor", paramCellAttr.getBackground_color()); 
/*  97 */     if (paramCellAttr.getEtype() == 3)
/*  98 */     { linkedHashMap1.put("textIndent", Double.valueOf(paramCellAttr.getIndent() + 2.5D));
/*  99 */       String str1 = "/workflow/exceldesign/image/controls/" + paramCellAttr.getFieldtype() + paramCellAttr.getFieldattr() + "_wev8.png";
/* 100 */       linkedHashMap1.put("backgroundImage", str1);
/* 101 */       linkedHashMap1.put("backgroundImageLayout", Integer.valueOf(3)); }
/* 102 */     else if (paramCellAttr.getEtype() == 7)
/* 103 */     { linkedHashMap1.put("backgroundImage", "/workflow/exceldesign/image/shortBtn/detail/detailTable_wev8.png");
/* 104 */       linkedHashMap1.put("backgroundImageLayout", Integer.valueOf(3));
/* 105 */       linkedHashMap1.put("textIndent", Integer.valueOf(3)); }
/* 106 */     else if (paramCellAttr.getEtype() == 10)
/* 107 */     { linkedHashMap1.put("backgroundImage", "/workflow/exceldesign/image/shortBtn/detail/de_btn_wev8.png");
/* 108 */       linkedHashMap1.put("backgroundImageLayout", Integer.valueOf(3)); }
/* 109 */     else { if (paramCellAttr.getEtype() == 8 || paramCellAttr.getEtype() == 9) {
/*     */         return;
/*     */       }
/* 112 */       if (paramCellAttr.getIndent() > 0.0D) {
/* 113 */         linkedHashMap1.put("textIndent", Double.valueOf(paramCellAttr.getIndent()));
/*     */       } }
/*     */     
/* 116 */     String str = "";
/* 117 */     if (paramCellAttr.isItalic()) str = str + "italic "; 
/* 118 */     if (paramCellAttr.isBold()) str = str + "bold "; 
/* 119 */     str = str + paramCellAttr.getFont_size() + " ";
/* 120 */     str = str + paramCellAttr.getFont_family();
/* 121 */     linkedHashMap1.put("font", str);
/* 122 */     if (!"".equals(paramCellAttr.getFont_color())) {
/* 123 */       linkedHashMap1.put("foreColor", paramCellAttr.getFont_color());
/*     */     }
/* 125 */     linkedHashMap1.put("hAlign", Integer.valueOf(paramCellAttr.getHalign()));
/* 126 */     linkedHashMap1.put("vAlign", Integer.valueOf(paramCellAttr.getValign()));
/*     */     
/* 128 */     LinkedHashMap<Object, Object> linkedHashMap2 = new LinkedHashMap<>();
/* 129 */     if (!"".equals(paramCellAttr.getBleft_color()))
/* 130 */       linkedHashMap2.put("color", paramCellAttr.getBleft_color()); 
/* 131 */     linkedHashMap2.put("style", Integer.valueOf(paramCellAttr.getBleft_style()));
/* 132 */     linkedHashMap1.put("borderLeft", linkedHashMap2);
/*     */     
/* 134 */     LinkedHashMap<Object, Object> linkedHashMap3 = new LinkedHashMap<>();
/* 135 */     if (!"".equals(paramCellAttr.getBright_color()))
/* 136 */       linkedHashMap3.put("color", paramCellAttr.getBright_color()); 
/* 137 */     linkedHashMap3.put("style", Integer.valueOf(paramCellAttr.getBright_style()));
/* 138 */     linkedHashMap1.put("borderRight", linkedHashMap3);
/*     */     
/* 140 */     LinkedHashMap<Object, Object> linkedHashMap4 = new LinkedHashMap<>();
/* 141 */     if (!"".equals(paramCellAttr.getBtop_color()))
/* 142 */       linkedHashMap4.put("color", paramCellAttr.getBtop_color()); 
/* 143 */     linkedHashMap4.put("style", Integer.valueOf(paramCellAttr.getBtop_style()));
/* 144 */     linkedHashMap1.put("borderTop", linkedHashMap4);
/*     */     
/* 146 */     LinkedHashMap<Object, Object> linkedHashMap5 = new LinkedHashMap<>();
/* 147 */     if (!"".equals(paramCellAttr.getBbottom_color()))
/* 148 */       linkedHashMap5.put("color", paramCellAttr.getBbottom_color()); 
/* 149 */     linkedHashMap5.put("style", Integer.valueOf(paramCellAttr.getBbottom_style()));
/* 150 */     linkedHashMap1.put("borderBottom", linkedHashMap5);
/*     */   }
/*     */ 
/*     */   
/*     */   public int getDetailflag() {
/* 155 */     return this.detailflag;
/*     */   }
/*     */   
/*     */   public Map<String, Object> getFieldmap() {
/* 159 */     return this.fieldmap;
/*     */   }
/*     */   
/*     */   public int getRownum() {
/* 163 */     return this.rownum;
/*     */   }
/*     */   
/*     */   public int getColnum() {
/* 167 */     return this.colnum;
/*     */   }
/*     */   
/*     */   public List<String> getDetailSheet() {
/* 171 */     return this.detailSheet;
/*     */   }
/*     */   
/*     */   public int getEdtitleinrow() {
/* 175 */     return this.edtitleinrow;
/*     */   }
/*     */   
/*     */   public int getEdtailinrow() {
/* 179 */     return this.edtailinrow;
/*     */   }
/*     */   
/*     */   public Map<String, String> getFieldattr_map() {
/* 183 */     return this.fieldattr_map;
/*     */   }
/*     */   
/*     */   public Map<String, Object> getData_rowheads() {
/* 187 */     return this.data_rowheads;
/*     */   }
/*     */   
/*     */   public Map<String, Object> getData_colheads() {
/* 191 */     return this.data_colheads;
/*     */   }
/*     */   
/*     */   public List<LinkedHashMap<String, Object>> getData_ec() {
/* 195 */     return this.data_ec;
/*     */   }
/*     */   
/*     */   public List<LinkedHashMap<String, Object>> getPlugin_rows() {
/* 199 */     return this.plugin_rows;
/*     */   }
/*     */   
/*     */   public List<LinkedHashMap<String, Object>> getPlugin_columns() {
/* 203 */     return this.plugin_columns;
/*     */   }
/*     */   
/*     */   public List<LinkedHashMap<String, Integer>> getPlugin_combine() {
/* 207 */     return this.plugin_combine;
/*     */   }
/*     */   
/*     */   public Map<String, Object> getPlugin_data() {
/* 211 */     return this.plugin_data;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/filecheck/ParseSheet.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */