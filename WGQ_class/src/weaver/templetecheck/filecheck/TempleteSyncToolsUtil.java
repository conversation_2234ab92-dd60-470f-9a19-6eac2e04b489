/*    */ package weaver.templetecheck.filecheck;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ 
/*    */ public class TempleteSyncToolsUtil {
/* 11 */   private String dbtype = (new RecordSet()).getDBType();
/*    */   
/*    */   public List<Map<String, String>> getWFList(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 14 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 15 */     String str1 = getIdsOfParams(Util.null2String(paramMap.get("ids"))).trim();
/* 16 */     String str2 = Util.null2String(paramMap.get("workflowname"));
/* 17 */     String str3 = "select id,workflowname,workflowdesc from workflow_base ";
/* 18 */     String str4 = " where 1=1 ";
/* 19 */     if (!"".equals(str1)) {
/* 20 */       str4 = str4 + " and id in(" + str1 + ") ";
/*    */     }
/* 22 */     if (!"".equals(str2)) {
/* 23 */       str4 = str4 + " and workflowname like '%" + str2 + "%' ";
/*    */     }
/* 25 */     RecordSet recordSet = new RecordSet();
/* 26 */     recordSet.executeQuery(str3 + str4, new Object[0]);
/* 27 */     while (recordSet.next()) {
/* 28 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 29 */       hashMap.put("id", recordSet.getString("id"));
/* 30 */       hashMap.put("workflowname", recordSet.getString("workflowname"));
/* 31 */       hashMap.put("workflowdesc", recordSet.getString("workflowdesc"));
/* 32 */       arrayList.add(hashMap);
/*    */     } 
/* 34 */     return (List)arrayList;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static String getIdsOfParams(String paramString) {
/* 44 */     paramString = Util.null2String(paramString);
/* 45 */     if (paramString.startsWith(",")) {
/* 46 */       paramString = paramString.substring(1);
/*    */     }
/* 48 */     if (paramString.endsWith(",")) {
/* 49 */       if (paramString.length() == 1) {
/* 50 */         paramString = "";
/*    */       } else {
/* 52 */         paramString = paramString.substring(0, paramString.length() - 1);
/*    */       } 
/*    */     }
/* 55 */     return paramString;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/filecheck/TempleteSyncToolsUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */