/*     */ package weaver.templetecheck.filecheck;
/*     */ 
/*     */ public class CellAttr {
/*     */   private int rowid;
/*     */   private int colid;
/*     */   private int colspan;
/*     */   private int rowspan;
/*     */   private int etype;
/*   9 */   private String fieldid = "";
/*     */   private int fieldattr;
/*  11 */   private String fieldtype = "";
/*  12 */   private String evalue = "";
/*     */   private boolean italic = false;
/*     */   private boolean bold = false;
/*     */   private boolean underline = false;
/*     */   private int halign;
/*     */   private int valign;
/*     */   private double indent;
/*  19 */   private String background_color = "";
/*  20 */   private String background_image = "";
/*  21 */   private String background_imagelayout = "";
/*  22 */   private String font_size = "";
/*  23 */   private String font_family = "";
/*  24 */   private String font_color = "";
/*     */   private int btop_style;
/*  26 */   private String btop_color = "";
/*     */   private int bbottom_style;
/*  28 */   private String bbottom_color = "";
/*     */   private int bleft_style;
/*  30 */   private String bleft_color = "";
/*     */   private int bright_style;
/*  32 */   private String bright_color = "";
/*     */ 
/*     */   
/*     */   public int getRowid() {
/*  36 */     return this.rowid;
/*     */   }
/*     */   public void setRowid(int paramInt) {
/*  39 */     this.rowid = paramInt;
/*     */   }
/*     */   public int getColid() {
/*  42 */     return this.colid;
/*     */   }
/*     */   public void setColid(int paramInt) {
/*  45 */     this.colid = paramInt;
/*     */   }
/*     */   public int getColspan() {
/*  48 */     return this.colspan;
/*     */   }
/*     */   public void setColspan(int paramInt) {
/*  51 */     this.colspan = paramInt;
/*     */   }
/*     */   public int getRowspan() {
/*  54 */     return this.rowspan;
/*     */   }
/*     */   public void setRowspan(int paramInt) {
/*  57 */     this.rowspan = paramInt;
/*     */   }
/*     */   public int getEtype() {
/*  60 */     return this.etype;
/*     */   }
/*     */   public void setEtype(int paramInt) {
/*  63 */     this.etype = paramInt;
/*     */   }
/*     */   public String getFieldid() {
/*  66 */     return this.fieldid;
/*     */   }
/*     */   public void setFieldid(String paramString) {
/*  69 */     this.fieldid = paramString;
/*     */   }
/*     */   public int getFieldattr() {
/*  72 */     return this.fieldattr;
/*     */   }
/*     */   public void setFieldattr(int paramInt) {
/*  75 */     this.fieldattr = paramInt;
/*     */   }
/*     */   public String getFieldtype() {
/*  78 */     return this.fieldtype;
/*     */   }
/*     */   public void setFieldtype(String paramString) {
/*  81 */     this.fieldtype = paramString;
/*     */   }
/*     */   public String getEvalue() {
/*  84 */     return this.evalue;
/*     */   }
/*     */   public void setEvalue(String paramString) {
/*  87 */     this.evalue = paramString;
/*     */   }
/*     */   public boolean isItalic() {
/*  90 */     return this.italic;
/*     */   }
/*     */   public void setItalic(boolean paramBoolean) {
/*  93 */     this.italic = paramBoolean;
/*     */   }
/*     */   public boolean isBold() {
/*  96 */     return this.bold;
/*     */   }
/*     */   public void setBold(boolean paramBoolean) {
/*  99 */     this.bold = paramBoolean;
/*     */   }
/*     */   public boolean isUnderline() {
/* 102 */     return this.underline;
/*     */   }
/*     */   public void setUnderline(boolean paramBoolean) {
/* 105 */     this.underline = paramBoolean;
/*     */   }
/*     */   public int getHalign() {
/* 108 */     return this.halign;
/*     */   }
/*     */   public void setHalign(int paramInt) {
/* 111 */     this.halign = paramInt;
/*     */   }
/*     */   public int getValign() {
/* 114 */     return this.valign;
/*     */   }
/*     */   public void setValign(int paramInt) {
/* 117 */     this.valign = paramInt;
/*     */   }
/*     */   public double getIndent() {
/* 120 */     return this.indent;
/*     */   }
/*     */   public void setIndent(double paramDouble) {
/* 123 */     this.indent = paramDouble;
/*     */   }
/*     */   public String getBackground_color() {
/* 126 */     return this.background_color;
/*     */   }
/*     */   public void setBackground_color(String paramString) {
/* 129 */     this.background_color = paramString;
/*     */   }
/*     */   public String getBackground_image() {
/* 132 */     return this.background_image;
/*     */   }
/*     */   public void setBackground_image(String paramString) {
/* 135 */     this.background_image = paramString;
/*     */   }
/*     */   public String getBackground_imagelayout() {
/* 138 */     return this.background_imagelayout;
/*     */   }
/*     */   public void setBackground_imagelayout(String paramString) {
/* 141 */     this.background_imagelayout = paramString;
/*     */   }
/*     */   public String getFont_size() {
/* 144 */     return this.font_size;
/*     */   }
/*     */   public void setFont_size(String paramString) {
/* 147 */     this.font_size = paramString;
/*     */   }
/*     */   public String getFont_family() {
/* 150 */     return this.font_family;
/*     */   }
/*     */   public void setFont_family(String paramString) {
/* 153 */     this.font_family = paramString;
/*     */   }
/*     */   public String getFont_color() {
/* 156 */     return this.font_color;
/*     */   }
/*     */   public void setFont_color(String paramString) {
/* 159 */     this.font_color = paramString;
/*     */   }
/*     */   public int getBtop_style() {
/* 162 */     return this.btop_style;
/*     */   }
/*     */   public void setBtop_style(int paramInt) {
/* 165 */     this.btop_style = paramInt;
/*     */   }
/*     */   public String getBtop_color() {
/* 168 */     return this.btop_color;
/*     */   }
/*     */   public void setBtop_color(String paramString) {
/* 171 */     this.btop_color = paramString;
/*     */   }
/*     */   public int getBbottom_style() {
/* 174 */     return this.bbottom_style;
/*     */   }
/*     */   public void setBbottom_style(int paramInt) {
/* 177 */     this.bbottom_style = paramInt;
/*     */   }
/*     */   public String getBbottom_color() {
/* 180 */     return this.bbottom_color;
/*     */   }
/*     */   public void setBbottom_color(String paramString) {
/* 183 */     this.bbottom_color = paramString;
/*     */   }
/*     */   public int getBleft_style() {
/* 186 */     return this.bleft_style;
/*     */   }
/*     */   public void setBleft_style(int paramInt) {
/* 189 */     this.bleft_style = paramInt;
/*     */   }
/*     */   public String getBleft_color() {
/* 192 */     return this.bleft_color;
/*     */   }
/*     */   public void setBleft_color(String paramString) {
/* 195 */     this.bleft_color = paramString;
/*     */   }
/*     */   public int getBright_style() {
/* 198 */     return this.bright_style;
/*     */   }
/*     */   public void setBright_style(int paramInt) {
/* 201 */     this.bright_style = paramInt;
/*     */   }
/*     */   public String getBright_color() {
/* 204 */     return this.bright_color;
/*     */   }
/*     */   public void setBright_color(String paramString) {
/* 207 */     this.bright_color = paramString;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/filecheck/CellAttr.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */