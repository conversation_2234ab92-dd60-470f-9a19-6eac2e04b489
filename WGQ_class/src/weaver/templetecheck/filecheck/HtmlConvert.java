/*     */ package weaver.templetecheck.filecheck;
/*     */ 
/*     */ import java.io.BufferedWriter;
/*     */ import java.io.File;
/*     */ import java.io.FileWriter;
/*     */ import java.io.IOException;
/*     */ import java.io.PrintWriter;
/*     */ import java.util.LinkedHashMap;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.templetecheck.FileUtil;
/*     */ 
/*     */ 
/*     */ public class HtmlConvert
/*     */   extends BaseBean
/*     */ {
/*     */   public boolean log(String paramString) {
/*     */     try {
/*  21 */       String str1 = GCONST.getRootPath() + "sysupgradelog" + File.separatorChar + "upgradelog";
/*  22 */       File file1 = new File(str1);
/*  23 */       if (!file1.exists()) {
/*  24 */         file1.mkdirs();
/*     */       }
/*  26 */       File file2 = new File(str1 + File.separatorChar + TimeUtil.getCurrentDateString() + ".log");
/*  27 */       if (!file2.exists()) {
/*  28 */         file2.createNewFile();
/*     */       }
/*     */       
/*  31 */       FileWriter fileWriter = new FileWriter(file2, true);
/*  32 */       BufferedWriter bufferedWriter = new BufferedWriter(fileWriter);
/*  33 */       PrintWriter printWriter = new PrintWriter(bufferedWriter);
/*  34 */       String str2 = TimeUtil.getCurrentTimeString();
/*     */       
/*  36 */       printWriter.println("###[" + str2 + "]:" + paramString);
/*  37 */       printWriter.close();
/*  38 */       bufferedWriter.close();
/*  39 */       fileWriter.close();
/*  40 */       return true;
/*  41 */     } catch (IOException iOException) {
/*  42 */       return false;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean startConvert(String paramString) {
/*  50 */     boolean bool = true;
/*  51 */     RecordSet recordSet = new RecordSet();
/*  52 */     String str1 = "select id, syspath, workflowid from workflow_nodehtmllayout WHERE 1=1";
/*  53 */     if (!"".equals(paramString)) {
/*  54 */       str1 = str1 + " and id=" + paramString;
/*     */     }
/*  56 */     String str2 = "update workflow_nodehtmllayout set datajson = ?, pluginjson = ?, scripts = ?,htmlparsescheme= ? where id = ?";
/*  57 */     recordSet.executeQuery(str1, new Object[0]);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  63 */     FileUtil fileUtil = new FileUtil();
/*     */     
/*  65 */     while (recordSet.next()) {
/*     */       
/*  67 */       String str3 = recordSet.getString(1);
/*  68 */       String str4 = recordSet.getString(2);
/*  69 */       String str5 = recordSet.getString(3);
/*  70 */       log("######转化HTML开始######workflow_nodehtmllayout中ID=" + str3);
/*     */       try {
/*  72 */         File file = new File(str4);
/*  73 */         StringBuffer stringBuffer = new StringBuffer();
/*  74 */         log("开始进行转化：" + str4);
/*  75 */         if (file.exists()) {
/*  76 */           stringBuffer = fileUtil.readFile(file);
/*     */         } else {
/*  78 */           log("文件不存在：" + str4);
/*     */           continue;
/*     */         } 
/*  81 */         HtmlAnalysis htmlAnalysis = new HtmlAnalysis();
/*  82 */         LinkedHashMap linkedHashMap = htmlAnalysis.getTableInfo(stringBuffer, str3);
/*     */         
/*  84 */         if (linkedHashMap != null) {
/*  85 */           String str6 = linkedHashMap.get("datajson").toString();
/*  86 */           String str7 = linkedHashMap.get("pluginjson").toString();
/*  87 */           String str8 = linkedHashMap.get("script").toString();
/*  88 */           log("文件不存在：" + str4);
/*  89 */           String str9 = "update workflow_nodehtmllayout set datajson = " + str6 + ", pluginjson = " + str7 + ", scripts = " + str8 + ",htmlparsescheme=1 where id = " + str3;
/*  90 */           log("执行sql：" + str9);
/*  91 */           boolean bool1 = recordSet.executeUpdate(str9, new Object[0]);
/*  92 */           if (!bool1) {
/*  93 */             log("sql执行失败，更新htmlparsescheme，workflow_nodehtmllayout中ID=" + str3);
/*  94 */             recordSet.executeUpdate("update workflow_nodehtmllayout set htmlparsescheme=0", new Object[0]); continue;
/*     */           } 
/*  96 */           log("!!!!!!转化HTML成功!!!!!!workflow_nodehtmllayout中ID=" + str3);
/*     */         }
/*     */       
/*  99 */       } catch (Exception exception) {
/* 100 */         writeLog(exception);
/*     */       } 
/*     */     } 
/*     */     
/* 104 */     writeLog("###转化HTML结束###");
/* 105 */     return bool;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/filecheck/HtmlConvert.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */