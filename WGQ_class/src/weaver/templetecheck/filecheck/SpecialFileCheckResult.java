/*    */ package weaver.templetecheck.filecheck;
/*    */ 
/*    */ import java.io.UnsupportedEncodingException;
/*    */ import java.net.URLDecoder;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import javax.servlet.http.HttpServletRequest;
/*    */ import javax.servlet.http.HttpServletResponse;
/*    */ import org.apache.commons.lang.StringEscapeUtils;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class SpecialFileCheckResult
/*    */ {
/*    */   public List<Map<String, String>> getSpecialFileResult(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 26 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 27 */     String str1 = Util.null2String(paramMap.get("filepath")).trim();
/* 28 */     String str2 = Util.null2String(paramMap.get("isWorkflow")).trim();
/*    */     try {
/* 30 */       str1 = URLDecoder.decode(str1, "utf-8").trim();
/* 31 */       str2 = URLDecoder.decode(str2, "utf-8");
/* 32 */     } catch (Exception exception) {
/* 33 */       exception.printStackTrace();
/*    */     } 
/* 35 */     String str3 = " 1=1 ";
/* 36 */     if (!"".equalsIgnoreCase(str1)) {
/* 37 */       str3 = str3 + " and filepath like '%" + str1 + "%'";
/*    */     }
/* 39 */     String str4 = " select  min(id) as id,filepath from upgradecheckresult t1 where " + str3 + "  group by filepath order by id asc";
/* 40 */     (new BaseBean()).writeLog("执行的sql:" + str4);
/* 41 */     RecordSet recordSet = new RecordSet();
/* 42 */     recordSet.executeQuery(str4, new Object[0]);
/* 43 */     while (recordSet.next()) {
/* 44 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 45 */       hashMap.put("id", Util.null2String(recordSet.getString("id")));
/* 46 */       hashMap.put("filepath", StringEscapeUtils.escapeHtml(Util.null2String(recordSet.getString("filepath"))));
/* 47 */       arrayList.add(hashMap);
/*    */     } 
/* 49 */     return (List)arrayList;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public List<Map<String, String>> getSpecialFileDetailResult(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 58 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 59 */     String str1 = Util.null2String(paramMap.get("filepath")).trim();
/*    */     try {
/* 61 */       str1 = URLDecoder.decode(str1, "UTF-8").trim();
/* 62 */     } catch (UnsupportedEncodingException unsupportedEncodingException) {
/* 63 */       unsupportedEncodingException.printStackTrace();
/*    */     } 
/* 65 */     RecordSet recordSet = new RecordSet();
/* 66 */     String str2 = "select * from upgradecheckresult where filepath='" + str1 + "'";
/* 67 */     recordSet.executeQuery(str2, new Object[0]);
/* 68 */     while (recordSet.next()) {
/* 69 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 70 */       hashMap.put("id", Util.null2String(recordSet.getString("id")));
/* 71 */       hashMap.put("ruleid", Util.null2String(recordSet.getString("ruleid")));
/* 72 */       hashMap.put("rulename", Util.null2String(recordSet.getString("rulename")));
/* 73 */       hashMap.put("ruledesc", Util.null2String(recordSet.getString("ruledesc")));
/* 74 */       hashMap.put("content", Util.null2String(recordSet.getString("content")));
/* 75 */       hashMap.put("filepath", Util.null2String(recordSet.getString("filepath")));
/* 76 */       hashMap.put("replacecontent", Util.null2String(recordSet.getString("replacecontent")));
/* 77 */       arrayList.add(hashMap);
/*    */     } 
/* 79 */     return (List)arrayList;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/filecheck/SpecialFileCheckResult.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */