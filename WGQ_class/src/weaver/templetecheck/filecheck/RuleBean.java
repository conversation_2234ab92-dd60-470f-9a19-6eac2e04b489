/*    */ package weaver.templetecheck.filecheck;
/*    */ 
/*    */ public class RuleBean {
/*  4 */   private String id = null;
/*  5 */   private String rulename = null;
/*  6 */   private String ruledesc = null;
/*  7 */   private String ruletype = null;
/*  8 */   private String checktype = null;
/*  9 */   private String content = null;
/* 10 */   private String replacecontent = null;
/* 11 */   private String version = null;
/* 12 */   private String contenttype = null;
/*    */ 
/*    */   
/*    */   public String getId() {
/* 16 */     return this.id;
/*    */   }
/*    */   public void setId(String paramString) {
/* 19 */     this.id = paramString;
/*    */   }
/*    */   public String getRulename() {
/* 22 */     return this.rulename;
/*    */   }
/*    */   public void setRulename(String paramString) {
/* 25 */     this.rulename = paramString;
/*    */   }
/*    */   public String getRuledesc() {
/* 28 */     return this.ruledesc;
/*    */   }
/*    */   public void setRuledesc(String paramString) {
/* 31 */     this.ruledesc = paramString;
/*    */   }
/*    */   public String getRuletype() {
/* 34 */     return this.ruletype;
/*    */   }
/*    */   public void setRuletype(String paramString) {
/* 37 */     this.ruletype = paramString;
/*    */   }
/*    */   public String getChecktype() {
/* 40 */     return this.checktype;
/*    */   }
/*    */   public void setChecktype(String paramString) {
/* 43 */     this.checktype = paramString;
/*    */   }
/*    */   public String getContent() {
/* 46 */     return this.content;
/*    */   }
/*    */   public void setContent(String paramString) {
/* 49 */     this.content = paramString;
/*    */   }
/*    */   public String getReplacecontent() {
/* 52 */     return this.replacecontent;
/*    */   }
/*    */   public void setReplacecontent(String paramString) {
/* 55 */     this.replacecontent = paramString;
/*    */   }
/*    */   public String getVersion() {
/* 58 */     return this.version;
/*    */   }
/*    */   public void setVersion(String paramString) {
/* 61 */     this.version = paramString;
/*    */   }
/*    */   public String getContenttype() {
/* 64 */     return this.contenttype;
/*    */   }
/*    */   public void setContenttype(String paramString) {
/* 67 */     this.contenttype = paramString;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/filecheck/RuleBean.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */