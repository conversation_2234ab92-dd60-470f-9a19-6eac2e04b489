/*     */ package weaver.templetecheck.filecheck;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import org.apache.commons.lang.StringEscapeUtils;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FileCheckReport
/*     */ {
/*     */   public JSONObject getWorkflowReport() {
/*  19 */     JSONObject jSONObject = new JSONObject();
/*  20 */     RecordSet recordSet = new RecordSet();
/*  21 */     String str1 = recordSet.getDBType();
/*  22 */     Boolean bool = Boolean.valueOf(false);
/*  23 */     if (str1.equalsIgnoreCase("oracle")) {
/*  24 */       bool = Boolean.valueOf(true);
/*     */     }
/*  26 */     recordSet.executeQuery("select count(1) as cnt from workflow_base where isvalid in (1,2)", new Object[0]);
/*  27 */     if (recordSet.next()) {
/*  28 */       jSONObject.put("workflowCount", recordSet.getString("cnt"));
/*     */     }
/*     */     
/*  31 */     String str2 = "";
/*  32 */     str2 = "select count(1) as cnt from(select distinct workflowid,workflowname,nodename,isactive from workflow_nodehtmllayout t,workflow_nodebase t2,workflow_base t3 where t.nodeid=t2.id and t.workflowid=t3.id  and ((syspath is not null and syspath <> '') or (datajson is not null )) and isvalid in (1,2) and isactive=1)t";
/*  33 */     if (bool.booleanValue()) {
/*  34 */       str2 = "select count(1) as cnt from(select distinct workflowid,workflowname,nodename,isactive from workflow_nodehtmllayout t,workflow_nodebase t2,workflow_base t3 where t.nodeid=t2.id and t.workflowid=t3.id  and (syspath is not null or syspath <> '' or datajson is not null) and isvalid in (1,2) and isactive=1)t";
/*     */     }
/*  36 */     recordSet.executeQuery(str2, new Object[0]);
/*  37 */     if (recordSet.next()) {
/*  38 */       jSONObject.put("htmlNodeCount", recordSet.getString("cnt"));
/*     */     }
/*     */ 
/*     */     
/*  42 */     str2 = "select count(1) as cnt from (select distinct workflowid,workflowname from workflow_nodehtmllayout t,workflow_base t3  where t.workflowid=t3.id and ((syspath is not null and syspath <> '') or (datajson is not null )) and isvalid in (1,2) and isactive=1) t";
/*  43 */     if (bool.booleanValue()) {
/*  44 */       str2 = "select count(1) as cnt from (select distinct workflowid,workflowname from workflow_nodehtmllayout t,workflow_base t3  where t.workflowid=t3.id and (syspath is not null or syspath <> '' or datajson is not null) and isvalid in (1,2) and isactive=1) ";
/*     */     }
/*  46 */     recordSet.executeQuery(str2, new Object[0]);
/*  47 */     if (recordSet.next()) {
/*  48 */       jSONObject.put("workflowCountUseHtml", recordSet.getString("cnt"));
/*     */     }
/*     */ 
/*     */     
/*  52 */     str2 = "select count(1) as cnt from (select distinct workflowid,workflowname from workflow_flownode t,workflow_base t3  where t.workflowid=t3.id  and t.ismode=1  and t3.isvalid in (1,2)) a";
/*  53 */     recordSet.executeQuery(str2, new Object[0]);
/*  54 */     if (recordSet.next()) {
/*  55 */       jSONObject.put("workflowCountUseExcel", recordSet.getString("cnt"));
/*     */     }
/*     */ 
/*     */ 
/*     */     
/*  60 */     str2 = "select count(1) as cnt from(select distinct workflowid,workflowname,nodename from workflow_flownode t,workflow_nodebase t2,workflow_base t3 where t.nodeid=t2.id and t.workflowid=t3.id  and t.ismode=1 and t3.isvalid in (1,2) )t";
/*  61 */     recordSet.executeQuery(str2, new Object[0]);
/*  62 */     if (recordSet.next()) {
/*  63 */       jSONObject.put("excelNodeCount", recordSet.getString("cnt"));
/*     */     }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  71 */     str2 = "SELECT workflowid, workflowname, nodename, syspath, isactive FROM workflow_nodehtmllayout t,\tworkflow_nodebase t2, workflow_base t3 WHERE t.nodeid = t2.id AND t.workflowid = t3.id and syspath is not null " + (bool.booleanValue() ? "" : " and syspath <> '' ") + "\tAND datajson IS NULL AND isvalid IN ( 1, 2 ) AND isactive = 1 AND type<>1 UNION ALL SELECT\tt.workflowid, t3.workflowname, t2.nodename,\tt.syspath, t.isactive FROM\tworkflow_nodehtmllayout t,\tworkflow_nodebase t2,\tworkflow_base t3 ,\tworkflow_printset p WHERE\tt.nodeid = t2.id \tAND t.workflowid = t3.id \tand syspath is not null " + (bool.booleanValue() ? "" : " and syspath <> '' ") + "\tAND t.datajson IS NULL\tAND isvalid IN ( 1, 2 ) \tAND t.isactive = 1 \tAND t.type=1\tAND p.modeid=t.id\tAND p.WORKFLOWID=t.WORKFLOWID\tAND p.NODEID=t.NODEID";
/*  72 */     recordSet.executeQuery(str2, new Object[0]);
/*  73 */     byte b = 0;
/*  74 */     while (recordSet.next()) {
/*  75 */       String str = Util.null2String(recordSet.getString("datajson"));
/*  76 */       if ("".equals(str)) {
/*  77 */         b++;
/*     */       }
/*     */     } 
/*  80 */     jSONObject.put("htmlFilecount", Integer.valueOf(b));
/*     */ 
/*     */     
/*  83 */     str2 = "select count(1) as cnt from (select distinct id from \n(select  t.id from workflow_nodebase t,workflow_flownode w,workflow_base b where t.id=w.nodeid and w.workflowid=b.id and b.isvalid in (1,2)\n union(select distinct t1.id from workflow_nodehtmllayout t1,workflow_nodebase t2,workflow_base t3 where t1.nodeid=t2.id and t1.workflowid=t3.id  and ((t1.syspath is not null and t1.syspath <> '') or (t1.datajson is not null )) and t3.isvalid in (1,2) and t1.isactive=1)\n) t) a\n";
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  88 */     if (bool.booleanValue()) {
/*  89 */       str2 = "select count(1) as cnt from(select distinct id from \n(select  t.id from workflow_nodebase t,workflow_flownode w,workflow_base b where t.id=w.nodeid and w.workflowid=b.id and b.isvalid in (1,2)\n union(select distinct t1.id from workflow_nodehtmllayout t1,workflow_nodebase t2,workflow_base t3 where t1.nodeid=t2.id and t1.workflowid=t3.id  and (t1.syspath is not null and t1.syspath <> '' or t1.datajson is not null ) and t3.isvalid in (1,2) and t1.isactive=1)\n) )";
/*     */     }
/*     */ 
/*     */ 
/*     */     
/*  94 */     recordSet.executeQuery(str2, new Object[0]);
/*  95 */     if (recordSet.next()) {
/*  96 */       jSONObject.put("nodeCount", recordSet.getString("cnt"));
/*     */     }
/*     */ 
/*     */     
/* 100 */     recordSet.executeQuery("select count(1) as cnt from(\nselect workflowid,workflowname,nodename,syspath,isactive from workflow_nodehtmllayout t,workflow_nodebase t2,workflow_base t3 where t.nodeid=t2.id and t.workflowid=t3.id and isvalid in (1,2) and isactive=1 and type<>1\nUNION ALL\nselect t.workflowid,t3.workflowname,t2.nodename,t.syspath,t.isactive from workflow_nodehtmllayout t,workflow_nodebase t2,workflow_base t3\n,workflow_printset p \nwhere p.modeid=t.id AND p.WORKFLOWID=t.WORKFLOWID AND p.NODEID=t.NODEID and t.nodeid=t2.id and t.workflowid=t3.id  and isvalid in (1,2) and t.isactive=1 and t.type=1 \n)t", new Object[0]);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 107 */     int i = 0;
/* 108 */     if (recordSet.next()) {
/* 109 */       i = recordSet.getInt("cnt");
/*     */     }
/*     */     
/* 112 */     recordSet.executeQuery("select count(1) as cnt from( select workflowid,workflowname,nodename,syspath,isactive from (select workflowid,workflowname,nodename,syspath,isactive from workflow_nodehtmllayout t,workflow_nodebase t2,workflow_base t3 where t.nodeid=t2.id and t.workflowid=t3.id  and (syspath is null " + (
/* 113 */         bool.booleanValue() ? "" : "or syspath='' ") + ") and datajson is null and isvalid in (1,2) and isactive=1 and type<>1 \nUNION ALL select t.workflowid,t3.workflowname,t2.nodename,t.syspath,t.isactive from workflow_nodehtmllayout t,workflow_nodebase t2,workflow_base t3\n,workflow_printset p where  p.modeid=t.id AND p.WORKFLOWID=t.WORKFLOWID AND p.NODEID=t.NODEID and t.nodeid=t2.id and t.workflowid=t3.id  and (t.syspath is null " + (
/*     */ 
/*     */ 
/*     */         
/* 117 */         bool.booleanValue() ? "" : "or t.syspath='' ") + ") and datajson is null and isvalid in (1,2) and t.isactive=1 and t.type=1 )a \n)t", new Object[0]);
/*     */     
/* 119 */     if (recordSet.next()) {
/* 120 */       jSONObject.put("workflowCountAll", Integer.valueOf(i - recordSet.getInt("cnt")));
/*     */     }
/* 122 */     return jSONObject;
/*     */   }
/*     */   
/*     */   public String getHtmlLayoutResult(String paramString) {
/* 126 */     if ("1".equals(paramString))
/* 127 */       return "<font color='red'>" + SystemEnv.getHtmlLabelName(33796, ThreadVarLanguage.getLang()) + "</font>"; 
/* 128 */     if ("0".equals(paramString)) {
/* 129 */       return "<font color='blue'>" + SystemEnv.getHtmlLabelName(165, ThreadVarLanguage.getLang()) + "</font>";
/*     */     }
/* 131 */     return "<font color='yellow'>" + SystemEnv.getHtmlLabelName(10004206, ThreadVarLanguage.getLang()) + "" + paramString + "</font>";
/*     */   }
/*     */   
/*     */   public String getHtmltype(String paramString) {
/* 135 */     if ("1".equals(paramString))
/* 136 */       return "" + SystemEnv.getHtmlLabelName(128952, ThreadVarLanguage.getLang()) + ""; 
/* 137 */     if ("0".equals(paramString)) {
/* 138 */       return "" + SystemEnv.getHtmlLabelName(16450, ThreadVarLanguage.getLang()) + "";
/*     */     }
/* 140 */     return "" + SystemEnv.getHtmlLabelName(125554, ThreadVarLanguage.getLang()) + "";
/*     */   }
/*     */   
/*     */   public String getHtmlparsescheme(String paramString) {
/* 144 */     if (paramString == null || "".equals(paramString)) {
/* 145 */       return "" + SystemEnv.getHtmlLabelName(10004207, ThreadVarLanguage.getLang()) + "";
/*     */     }
/* 147 */     return "<font color='red'>" + SystemEnv.getHtmlLabelName(10004208, ThreadVarLanguage.getLang()) + "</font>";
/*     */   }
/*     */   
/*     */   public String getHtmlScripts(String paramString) {
/* 151 */     String str = "";
/* 152 */     RecordSet recordSet = new RecordSet();
/* 153 */     recordSet.executeQuery("select scripts from workflow_nodehtmllayout where id='" + Util.null2String(paramString, "0") + "'", new Object[0]);
/*     */     
/* 155 */     if (recordSet.next()) {
/* 156 */       str = Util.null2String(recordSet.getString("scripts"), "");
/*     */     }
/* 158 */     if (!"".equals(str)) {
/* 159 */       ExcelSecurity excelSecurity = new ExcelSecurity();
/* 160 */       str = excelSecurity.decode(str);
/*     */     } 
/* 162 */     return str;
/*     */   }
/*     */   
/*     */   public String getModeHtmlScripts(String paramString) {
/* 166 */     String str = "";
/* 167 */     RecordSet recordSet = new RecordSet();
/* 168 */     boolean bool1 = CheckRule.checkDBFieldIsExist("scriptstr", "modehtmllayout");
/* 169 */     boolean bool2 = CheckRule.checkDBFieldIsExist("scripts", "modehtmllayout");
/* 170 */     if (bool1) {
/* 171 */       recordSet.executeQuery("select scriptstr as scripts from modehtmllayout where id='" + Util.null2String(paramString, "0") + "'", new Object[0]);
/* 172 */     } else if (bool2) {
/* 173 */       recordSet.executeQuery("select scripts from modehtmllayout where id='" + Util.null2String(paramString, "0") + "'", new Object[0]);
/*     */     } else {
/* 175 */       return str;
/*     */     } 
/* 177 */     if (recordSet.next()) {
/* 178 */       str = Util.null2String(recordSet.getString("scripts"), "");
/*     */     }
/* 180 */     if (!"".equals(str)) {
/* 181 */       ExcelSecurity excelSecurity = new ExcelSecurity();
/* 182 */       str = excelSecurity.decode(str);
/*     */     } 
/* 184 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCheckContent(String paramString) {
/* 192 */     if (paramString == null || "".equals(paramString)) {
/* 193 */       return "";
/*     */     }
/* 195 */     RecordSet recordSet = new RecordSet();
/* 196 */     recordSet.executeQuery("  select modetype,nodehtmllayoutid,filepath from upgradecheckworkflowresult where nodehtmllayoutid=" + paramString, new Object[0]);
/* 197 */     if (recordSet.next()) {
/* 198 */       String str1 = Util.null2String(recordSet.getString("modetype"));
/* 199 */       if ("1".equals(str1))
/*     */       {
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 205 */         return "";
/*     */       }
/* 207 */       String str2 = Util.null2String(recordSet.getString("filepath"));
/* 208 */       return StringEscapeUtils.escapeXml(str2);
/*     */     } 
/*     */     
/* 211 */     return "";
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/filecheck/FileCheckReport.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */