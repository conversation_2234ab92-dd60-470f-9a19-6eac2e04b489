/*     */ package weaver.templetecheck.filecheck;
/*     */ 
/*     */ import java.io.File;
/*     */ import java.io.IOException;
/*     */ import java.io.RandomAccessFile;
/*     */ import java.util.concurrent.ConcurrentHashMap;
/*     */ import org.apache.log4j.Logger;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class LogUtil
/*     */ {
/*  17 */   private static LogUtil logutil = null;
/*  18 */   private Logger log = Logger.getLogger(LogUtil.class);
/*  19 */   public ConcurrentHashMap<String, Long> sizemap = new ConcurrentHashMap<>();
/*  20 */   public static final String LOGFILE = GCONST.getRootPath() + "sysupgradelog" + File.separatorChar + "dbupgrade" + File.separatorChar;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static LogUtil getInstance() {
/*  26 */     if (null == logutil)
/*  27 */       logutil = new LogUtil(); 
/*  28 */     return logutil;
/*     */   }
/*     */   
/*     */   public void reset() {
/*  32 */     this.sizemap.clear();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public synchronized String getChangeLog(String paramString) {
/*  40 */     if ("1".equals(paramString)) {
/*  41 */       reset();
/*     */     }
/*  43 */     String str1 = TimeUtil.getCurrentDateString();
/*  44 */     File file = new File(TempleteSyncLogger.LOGFILE + str1 + ".log");
/*  45 */     StringBuffer stringBuffer = getChangeLog(file);
/*  46 */     String str2 = Util.null2String(stringBuffer.toString());
/*     */     
/*  48 */     if (str2.length() > 50000) {
/*  49 */       str2 = str2.substring(str2.length() - 50000);
/*     */     }
/*  51 */     stringBuffer = null;
/*  52 */     return str2;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public synchronized String getLog() {
/*  60 */     String str1 = TimeUtil.getCurrentDateString();
/*  61 */     File file = new File(LOGFILE + "dbupgrade" + str1 + ".log");
/*  62 */     StringBuffer stringBuffer = getLog(file);
/*  63 */     String str2 = Util.null2String(stringBuffer.toString());
/*     */     
/*  65 */     if (str2.length() > 50000) {
/*  66 */       str2 = str2.substring(str2.length() - 50000);
/*     */     }
/*  68 */     return str2;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public synchronized StringBuffer getLog(File paramFile) {
/*  77 */     StringBuffer stringBuffer = new StringBuffer();
/*  78 */     char c = 'Ǵ';
/*  79 */     long l = 0L;
/*  80 */     if (!paramFile.exists() || paramFile.isDirectory() || !paramFile.canRead()) {
/*  81 */       return stringBuffer;
/*     */     }
/*  83 */     RandomAccessFile randomAccessFile = null;
/*     */     try {
/*  85 */       randomAccessFile = new RandomAccessFile(paramFile, "r");
/*  86 */       long l1 = randomAccessFile.length();
/*  87 */       if (l1 == 0L) {
/*  88 */         return stringBuffer;
/*     */       }
/*  90 */       long l2 = l1 - 1L;
/*  91 */       while (l2 > 0L) {
/*  92 */         l2--;
/*  93 */         randomAccessFile.seek(l2);
/*  94 */         if (randomAccessFile.readByte() == 10) {
/*  95 */           String str = new String(randomAccessFile.readLine().getBytes("ISO-8859-1"));
/*  96 */           stringBuffer.append(str);
/*  97 */           stringBuffer.append("\r\n");
/*  98 */           l++;
/*  99 */           if (l == c) {
/*     */             break;
/*     */           }
/*     */         }
/*     */       
/*     */       } 
/* 105 */     } catch (IOException iOException) {
/* 106 */       iOException.printStackTrace();
/*     */     } finally {
/* 108 */       if (randomAccessFile != null) {
/* 109 */         try { randomAccessFile.close(); } catch (Exception exception) {}
/*     */       }
/*     */     } 
/* 112 */     return stringBuffer;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public synchronized StringBuffer getChangeLog(File paramFile) {
/* 120 */     StringBuffer stringBuffer = new StringBuffer();
/* 121 */     RandomAccessFile randomAccessFile = null;
/*     */     try {
/* 123 */       String str1 = paramFile.getName();
/*     */       
/* 125 */       long l = 0L;
/* 126 */       if (this.sizemap.get(str1) != null && ((Long)this.sizemap.get(str1)).longValue() > 0L) {
/* 127 */         l = ((Long)this.sizemap.get(str1)).longValue();
/*     */       }
/* 129 */       randomAccessFile = new RandomAccessFile(paramFile.getAbsolutePath(), "rw");
/*     */       
/* 131 */       randomAccessFile.seek(l);
/* 132 */       String str2 = "";
/* 133 */       while ((str2 = randomAccessFile.readLine()) != null) {
/* 134 */         if (str2.length() < 5120) {
/* 135 */           stringBuffer.append(new String(str2.getBytes("ISO-8859-1")));
/* 136 */           stringBuffer.append("\r\n"); continue;
/*     */         } 
/* 138 */         stringBuffer.append("内容太长，不打印！");
/* 139 */         stringBuffer.append("\r\n");
/*     */       } 
/*     */       
/* 142 */       l = randomAccessFile.length();
/*     */       
/* 144 */       this.sizemap.put(str1, Long.valueOf(l));
/* 145 */     } catch (Exception exception) {
/* 146 */       exception.printStackTrace();
/*     */     } finally {
/*     */       try {
/* 149 */         if (randomAccessFile != null) {
/* 150 */           randomAccessFile.close();
/*     */         }
/* 152 */       } catch (IOException iOException) {}
/*     */     } 
/*     */     
/* 155 */     return stringBuffer;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/filecheck/LogUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */