/*    */ package weaver.templetecheck.filecheck;
/*    */ 
/*    */ import java.io.File;
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.general.GCONST;
/*    */ 
/*    */ public class DoCheckAllPartThread
/*    */   implements Runnable {
/*  9 */   public static int runstatus = 0;
/*    */   
/*    */   public void run() {
/* 12 */     runstatus = 1;
/* 13 */     (new BaseBean()).writeLog("开启执行文件批量检测线程");
/*    */     try {
/* 15 */       CheckRule checkRule = new CheckRule();
/* 16 */       String str = GCONST.getRootPath() + "system" + File.separatorChar + "upgradetoe9" + File.separatorChar + "report" + File.separatorChar;
/* 17 */       checkRule.checkAllPart(str);
/* 18 */       runstatus = 0;
/* 19 */     } catch (Exception exception) {
/* 20 */       (new BaseBean()).writeLog("执行批量检测结束");
/* 21 */       runstatus = 0;
/*    */     } 
/* 23 */     (new BaseBean()).writeLog("执行批量检测线程结束");
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/filecheck/DoCheckAllPartThread.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */