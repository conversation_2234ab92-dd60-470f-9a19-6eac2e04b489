/*      */ package weaver.templetecheck.filecheck;
/*      */ 
/*      */ import java.io.UnsupportedEncodingException;
/*      */ import java.util.ArrayList;
/*      */ import java.util.Iterator;
/*      */ import java.util.LinkedHashMap;
/*      */ import net.sf.json.JSONObject;
/*      */ import org.jsoup.Jsoup;
/*      */ import org.jsoup.nodes.Document;
/*      */ import org.jsoup.nodes.Element;
/*      */ import org.jsoup.select.Elements;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.general.BaseBean;
/*      */ import weaver.general.Util;
/*      */ 
/*      */ 
/*      */ 
/*      */ public class HtmlAnalysis
/*      */ {
/*   20 */   int mainRow = 0;
/*   21 */   int mainCol = 0;
/*   22 */   int detailRow = 0;
/*   23 */   int detailCol = 0;
/*   24 */   int detailCount = 0;
/*   25 */   int totalCols = 6;
/*   26 */   int[][] mainSpanArray = new int[200][50];
/*   27 */   StringBuffer scripts = new StringBuffer();
/*      */   
/*   29 */   ArrayList<LinkedHashMap> mainList = new ArrayList<>();
/*   30 */   ArrayList<LinkedHashMap> detailList = new ArrayList<>();
/*   31 */   ArrayList<LinkedHashMap> mainSpans = new ArrayList<>();
/*   32 */   ArrayList<LinkedHashMap> detailSpans = new ArrayList<>();
/*   33 */   LinkedHashMap<String, Object> eattr = new LinkedHashMap<>();
/*   34 */   LinkedHashMap<String, Object> emaintable = new LinkedHashMap<>();
/*   35 */   LinkedHashMap<String, Object> etables = new LinkedHashMap<>();
/*   36 */   LinkedHashMap<String, Object> rowattrs = new LinkedHashMap<>();
/*   37 */   LinkedHashMap<String, Object> datajson = new LinkedHashMap<>();
/*   38 */   LinkedHashMap<String, Object> pluginjson = new LinkedHashMap<>();
/*   39 */   LinkedHashMap<String, Object> eformdesign = new LinkedHashMap<>();
/*   40 */   LinkedHashMap<String, Object> mainRowheads = new LinkedHashMap<>();
/*   41 */   LinkedHashMap<String, Object> mainColheads = new LinkedHashMap<>();
/*   42 */   LinkedHashMap<String, Object> detailRowheads = new LinkedHashMap<>();
/*   43 */   LinkedHashMap<String, Object> detailColheads = new LinkedHashMap<>();
/*   44 */   LinkedHashMap<String, Object> pluginMaintableData = new LinkedHashMap<>();
/*   45 */   LinkedHashMap<String, Object> pluginDetailtableData = new LinkedHashMap<>();
/*      */   
/*   47 */   ParseSheet parseSheet = new ParseSheet();
/*   48 */   ExcelSecurity excelSecurity = new ExcelSecurity();
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public LinkedHashMap getTableInfo(StringBuffer paramStringBuffer, String paramString) throws Exception {
/*   54 */     Document document = Jsoup.parse(paramStringBuffer.toString());
/*   55 */     if (document.select("td[colspan]").size() > 0 && document.select("td[colspan]").get(0).hasAttr("colspan")) {
/*   56 */       String str = document.select("td[colspan]").get(0).attr("colspan");
/*   57 */       if (!"".equals(str)) {
/*   58 */         this.totalCols = (Integer.valueOf(str).intValue() > this.totalCols) ? Integer.valueOf(str).intValue() : this.totalCols;
/*      */       }
/*      */     } 
/*   61 */     for (byte b = 0; b < this.totalCols; b++) {
/*   62 */       if (b % 2 == 0) { this.mainColheads.put("col_" + b, "100"); }
/*   63 */       else { this.mainColheads.put("col_" + b, "260"); }
/*      */     
/*      */     } 
/*   66 */     RecordSet recordSet1 = new RecordSet();
/*   67 */     RecordSet recordSet2 = new RecordSet();
/*   68 */     RecordSet recordSet3 = new RecordSet();
/*   69 */     recordSet1.executeQuery("select layoutname, workflowid, nodeid, formid, isbill from workflow_nodehtmllayout where id = ?", new Object[] { paramString });
/*   70 */     if (recordSet1.next()) {
/*   71 */       recordSet2.executeQuery("select workflowname from workflow_base where id = ?", new Object[] { Integer.valueOf(Util.getIntValue(recordSet1.getString(2))) });
/*   72 */       recordSet3.executeQuery("select nodename from workflow_nodebase where id = ?", new Object[] { Integer.valueOf(Util.getIntValue(recordSet1.getString(3))) });
/*   73 */       this.eattr.put("formname", recordSet1.getString(1));
/*   74 */       this.eattr.put("wfid", recordSet1.getString(2));
/*   75 */       this.eattr.put("nodeid", recordSet1.getString(3));
/*   76 */       this.eattr.put("formid", recordSet1.getString(4));
/*   77 */       this.eattr.put("isbill", recordSet1.getString(5));
/*      */     } 
/*   79 */     if (recordSet2.next() && recordSet3.next()) (new BaseBean()).writeLog("layoutid : " + paramString + ", workflowname : [" + recordSet2.getString(1) + "], nodename : [" + recordSet3.getString(1) + "]");
/*      */     
/*   81 */     this.emaintable.put("rowheads", this.mainRowheads);
/*   82 */     this.emaintable.put("colheads", this.mainColheads);
/*   83 */     this.emaintable.put("ec", this.mainList);
/*   84 */     this.etables.put("emaintable", this.emaintable);
/*   85 */     this.eformdesign.put("eattr", this.eattr);
/*   86 */     this.eformdesign.put("etables", this.etables);
/*   87 */     this.datajson.put("eformdesign", this.eformdesign);
/*   88 */     LinkedHashMap linkedHashMap = recursiveDFS((Element)document);
/*   89 */     getPluginMap("main_sheet", new LinkedHashMap<>(), this.pluginMaintableData);
/*      */     
/*   91 */     LinkedHashMap<Object, Object> linkedHashMap1 = new LinkedHashMap<>();
/*   92 */     linkedHashMap1.put("datajson", JSONObject.fromObject(linkedHashMap));
/*   93 */     linkedHashMap1.put("pluginjson", JSONObject.fromObject(this.pluginjson));
/*   94 */     linkedHashMap1.put("script", this.excelSecurity.encodeStr(this.scripts.toString()));
/*   95 */     return linkedHashMap1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private LinkedHashMap recursiveDFS(Element paramElement) throws Exception {
/*  113 */     if (paramElement.tagName().equals("tr") && paramElement.childNodes().size() == 0) return this.datajson; 
/*  114 */     if (paramElement.tagName().equals("script")) this.scripts.append(paramElement.toString()); 
/*  115 */     if (paramElement.tagName().equals("tr") && paramElement
/*  116 */       .children().size() > 0 && paramElement.child(0).tagName().equals("td") && (paramElement
/*  117 */       .children().select("tr").size() <= 1 || (paramElement.children().select("table").size() == 1 && paramElement.children().select("table[name*=oTable]").size() == 1)) && paramElement
/*  118 */       .children().select("button").size() == 0) {
/*      */       
/*  120 */       if (((ArrayList)this.emaintable.get("ec")).size() > 0) this.mainRow++; 
/*  121 */       this.mainCol = 0;
/*  122 */       if (this.mainRow > 1 && !this.mainRowheads.containsKey("row_" + (this.mainRow - 1))) {
/*  123 */         this.mainRowheads.put("row_" + (this.mainRow - 1), "30");
/*      */       }
/*  125 */       this.mainRowheads.put("row_" + this.mainRow, "30");
/*  126 */       if (!this.mainRowheads.containsKey("row_0")) this.mainRowheads.put("row_0", "60"); 
/*      */     } 
/*  128 */     if (paramElement.tagName().equals("tr")) {
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  135 */       if (paramElement.children().select("table").size() != paramElement.children().select("table[name*=oTable]").size()) {
/*  136 */         return recursiveDFS(paramElement.child(0));
/*      */       }
/*  138 */       if (checkForDisplayNone(paramElement)) {
/*  139 */         LinkedHashMap<Object, Object> linkedHashMap1 = new LinkedHashMap<>();
/*  140 */         linkedHashMap1.put("hide", "y");
/*  141 */         this.rowattrs.put("row_" + this.mainRow, linkedHashMap1);
/*  142 */         this.emaintable.put("rowattrs", this.rowattrs);
/*      */       } 
/*  144 */       LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/*  145 */       for (byte b = 0; b < paramElement.select("td").size(); b++) {
/*  146 */         Element element = paramElement.select("td").get(b);
/*  147 */         if (element.tagName().equals("td") && 
/*  148 */           !checkForDisplayNone(element)) {
/*      */ 
/*      */           
/*  151 */           if (element.select("table[name*=oTable]").size() > 0) {
/*  152 */             if (element.select("table").get(0).select("strong").size() > 0 && element
/*  153 */               .select("table").get(0).select("button").size() > 0) {
/*  154 */               Element element1 = element.select("table").get(0).select("strong").get(0).parent();
/*  155 */               if (!element1.text().equalsIgnoreCase("&nbsp;") && 
/*  156 */                 !element1.text().equalsIgnoreCase("&nbsp") && 
/*  157 */                 !element1.text().equalsIgnoreCase("")) {
/*      */                 
/*  159 */                 while (this.mainSpanArray[this.mainRow + 1][this.mainCol] == 1) {
/*  160 */                   this.mainCol++;
/*      */                 }
/*  162 */                 CellAttr cellAttr = createCell();
/*  163 */                 cellAttr.setFont_size("12pt");
/*  164 */                 cellAttr.setBold(true);
/*  165 */                 cellAttr.setRowid(++this.mainRow);
/*  166 */                 cellAttr.setColid(this.mainCol);
/*  167 */                 cellAttr.setColid(0);
/*  168 */                 cellAttr.setEtype(1);
/*  169 */                 cellAttr.setEvalue(element1.text());
/*  170 */                 cellAttr.setFont_color(element1.attr("color"));
/*  171 */                 getMainTableSpans(cellAttr, element1);
/*      */                 
/*  173 */                 LinkedHashMap<Object, Object> linkedHashMap1 = new LinkedHashMap<>();
/*  174 */                 LinkedHashMap<Object, Object> linkedHashMap2 = new LinkedHashMap<>();
/*  175 */                 this.parseSheet.buildDataEcMap(cellAttr, (LinkedHashMap)linkedHashMap1);
/*  176 */                 this.parseSheet.buildPluginCellMap(cellAttr, (LinkedHashMap)linkedHashMap2);
/*  177 */                 linkedHashMap.put("0", linkedHashMap2);
/*  178 */                 this.pluginMaintableData.put("" + this.mainRow, linkedHashMap);
/*  179 */                 this.mainList.add(linkedHashMap1);
/*  180 */                 this.emaintable.put("ec", this.mainList);
/*  181 */                 this.mainRowheads.put("row_" + this.mainRow, "30");
/*      */               } 
/*      */             } 
/*  184 */             Iterator<Element> iterator1 = paramElement.select("table").iterator();
/*  185 */             while (iterator1.hasNext()) {
/*  186 */               Element element1 = iterator1.next();
/*      */               
/*  188 */               if (element1.select("table").size() > 1 || (
/*  189 */                 element1.select("strong").size() == 0 && element1.select("input").size() == 0))
/*      */                 continue; 
/*  191 */               this.detailList = new ArrayList<>();
/*  192 */               LinkedHashMap linkedHashMap1 = detailTableAnalysis(element1);
/*  193 */               if (linkedHashMap1.size() > 0) {
/*  194 */                 this.etables.put("detail_" + ++this.detailCount, linkedHashMap1);
/*  195 */                 getPluginMap("detail_" + this.detailCount + "_sheet", new LinkedHashMap<>(), this.pluginDetailtableData);
/*  196 */                 getDetailTableCell("detail_" + this.detailCount);
/*  197 */                 getCoordinate();
/*  198 */                 CellAttr cellAttr = createCell();
/*  199 */                 cellAttr.setRowid(this.mainRow);
/*  200 */                 cellAttr.setColid(this.mainCol);
/*  201 */                 cellAttr.setColid(0);
/*  202 */                 cellAttr.setEvalue("明细表" + this.detailCount);
/*  203 */                 cellAttr.setBackground_color("#e7f3fc");
/*  204 */                 cellAttr.setHalign(0);
/*  205 */                 cellAttr.setBackground_image("/workflow/exceldesign/image/shortBtn/detail/detailTable_wev8.png");
/*  206 */                 cellAttr.setBackground_imagelayout("3");
/*  207 */                 getMainTableSpans(cellAttr, element);
/*      */                 
/*  209 */                 LinkedHashMap<Object, Object> linkedHashMap2 = new LinkedHashMap<>();
/*  210 */                 linkedHashMap = new LinkedHashMap<>();
/*  211 */                 this.parseSheet.buildPluginCellMap(cellAttr, (LinkedHashMap)linkedHashMap2);
/*  212 */                 linkedHashMap.put("" + this.mainCol, linkedHashMap2);
/*  213 */                 this.pluginMaintableData.put("" + this.mainRow, linkedHashMap);
/*  214 */                 this.detailSpans = new ArrayList<>();
/*      */               } 
/*      */             } 
/*  217 */             return this.etables;
/*  218 */           }  if (element.getElementsByClass("InputStyle").size() > 0 && element
/*  219 */             .getElementsByTag("td").size() <= 1 && this.mainList
/*  220 */             .size() > 0) {
/*  221 */             Elements elements = element.getElementsByClass("InputStyle");
/*  222 */             CellAttr cellAttr = createCell();
/*  223 */             String str = elements.get(0).val();
/*  224 */             if (str.contains("序号")) {
/*  225 */               cellAttr.setEtype(22);
/*      */             } else {
/*  227 */               cellAttr.setEtype(3);
/*      */             } 
/*  229 */             if (str.contains("[") && str.contains("]")) {
/*  230 */               cellAttr.setEvalue(str.substring(str.indexOf("]") + 1));
/*      */             } else {
/*  232 */               cellAttr.setEvalue(str);
/*      */             } 
/*  234 */             getCoordinate();
/*  235 */             cellAttr.setRowid(this.mainRow);
/*  236 */             cellAttr.setColid(this.mainCol);
/*  237 */             cellAttr.setFieldid(elements.get(0).attr("name").substring(5));
/*  238 */             cellAttr.setFieldtype(transControlType(cellAttr.getFieldid()));
/*  239 */             getMainTableSpans(cellAttr, element);
/*      */             
/*  241 */             LinkedHashMap<Object, Object> linkedHashMap1 = new LinkedHashMap<>();
/*  242 */             LinkedHashMap<Object, Object> linkedHashMap2 = new LinkedHashMap<>();
/*  243 */             this.parseSheet.buildDataEcMap(cellAttr, (LinkedHashMap)linkedHashMap1);
/*  244 */             this.parseSheet.buildPluginCellMap(cellAttr, (LinkedHashMap)linkedHashMap2);
/*  245 */             linkedHashMap.put("" + this.mainCol, linkedHashMap2);
/*  246 */             this.mainList.add(linkedHashMap1);
/*  247 */             this.pluginMaintableData.put("" + this.mainRow, linkedHashMap);
/*  248 */             this.emaintable.put("ec", this.mainList);
/*  249 */             if (!element.attr("colspan").equals("")) {
/*  250 */               this.mainCol += Integer.valueOf(element.attr("colspan")).intValue();
/*      */             } else {
/*  252 */               this.mainCol++;
/*      */             } 
/*  254 */             if (this.mainCol >= this.totalCols) {
/*  255 */               this.mainCol = 0;
/*      */             }
/*      */           }
/*  258 */           else if (element.select("strong").size() > 0 && element.select("tr").size() == 0) {
/*  259 */             if (element.select("input").size() <= 1 && this.mainList.size() == 0) {
/*  260 */               if (element.text().trim().equals("") || element.text().trim().equals("&nbsp;")) {
/*  261 */                 return this.etables;
/*      */               }
/*  263 */               element = element.select("strong").get(0);
/*  264 */               if (element.tagName().equals("strong")) {
/*  265 */                 element = element.parent();
/*  266 */                 CellAttr cellAttr = createCell();
/*  267 */                 cellAttr.setRowid(this.mainRow);
/*  268 */                 cellAttr.setColid(0);
/*  269 */                 cellAttr.setColspan(this.totalCols);
/*  270 */                 cellAttr.setFont_size("24pt");
/*  271 */                 cellAttr.setBold(true);
/*  272 */                 cellAttr.setRowspan(element.attr("rowspan").equals("") ? 1 : Integer.valueOf(element.attr("rowspan")).intValue());
/*  273 */                 cellAttr.setEtype(1);
/*  274 */                 cellAttr.setEvalue(getTrimedText(element.text()));
/*  275 */                 cellAttr.setFont_color(element.attr("color"));
/*  276 */                 getMainMergedInfo(this.mainRow, this.mainCol, 1, this.totalCols, "colspan");
/*      */                 
/*  278 */                 LinkedHashMap<Object, Object> linkedHashMap1 = new LinkedHashMap<>();
/*  279 */                 LinkedHashMap<Object, Object> linkedHashMap2 = new LinkedHashMap<>();
/*  280 */                 this.parseSheet.buildDataEcMap(cellAttr, (LinkedHashMap)linkedHashMap1);
/*  281 */                 this.parseSheet.buildPluginCellMap(cellAttr, (LinkedHashMap)linkedHashMap2);
/*  282 */                 linkedHashMap.put("" + this.mainCol, linkedHashMap2);
/*  283 */                 this.mainList.add(linkedHashMap1);
/*  284 */                 this.pluginMaintableData.put("" + this.mainRow, linkedHashMap);
/*  285 */                 this.emaintable.put("ec", this.mainList);
/*      */               } 
/*  287 */             } else if (this.mainList.size() > 0 && !((LinkedHashMap)this.mainList.get(0)).get("evalue").toString().contains(element.text())) {
/*      */               
/*  289 */               CellAttr cellAttr = createCell();
/*  290 */               if (element.select("font").size() > 0) {
/*  291 */                 cellAttr.setFont_size(element.select("font").get(0).attr("size"));
/*      */               } else {
/*  293 */                 cellAttr.setFont_size("12pt");
/*      */               } 
/*  295 */               getCoordinate();
/*  296 */               cellAttr.setBold(true);
/*  297 */               cellAttr.setRowid(this.mainRow);
/*  298 */               cellAttr.setColid(this.mainCol);
/*  299 */               cellAttr.setColid(0);
/*  300 */               cellAttr.setEtype(1);
/*  301 */               cellAttr.setEvalue(getTrimedText(element.text()));
/*  302 */               if (element.select("font").size() > 0) {
/*  303 */                 cellAttr.setFont_color(element.select("font").get(0).attr("color"));
/*      */               } else {
/*  305 */                 cellAttr.setFont_color(element.attr("color"));
/*      */               } 
/*  307 */               getMainTableSpans(cellAttr, element);
/*      */               
/*  309 */               LinkedHashMap<Object, Object> linkedHashMap1 = new LinkedHashMap<>();
/*  310 */               LinkedHashMap<Object, Object> linkedHashMap2 = new LinkedHashMap<>();
/*  311 */               this.parseSheet.buildDataEcMap(cellAttr, (LinkedHashMap)linkedHashMap1);
/*  312 */               this.parseSheet.buildPluginCellMap(cellAttr, (LinkedHashMap)linkedHashMap2);
/*  313 */               linkedHashMap.put("" + this.mainCol, linkedHashMap2);
/*  314 */               this.mainList.add(linkedHashMap1);
/*  315 */               this.pluginMaintableData.put("" + this.mainRow, linkedHashMap);
/*  316 */               this.emaintable.put("ec", this.mainList);
/*      */             }
/*      */           
/*  319 */           } else if (element.select("strong").size() == 0 && element
/*  320 */             .getElementsByClass("InputStyle").size() == 0 && element
/*  321 */             .parent().select("button").size() == 0 && element
/*  322 */             .children().select("div[id*=button]").size() == 0) {
/*  323 */             CellAttr cellAttr = createCell();
/*  324 */             if (element.getElementsByClass("Label").size() > 0) {
/*  325 */               cellAttr.setFieldid(element.getElementsByClass("Label").attr("name").substring(5));
/*      */             } else {
/*  327 */               Element element1 = element.nextElementSibling();
/*  328 */               if (element1 != null) {
/*  329 */                 Elements elements = element1.getElementsByClass("InputStyle");
/*  330 */                 if (elements.size() > 0) {
/*  331 */                   for (byte b1 = 0; b1 < elements.size(); b1++) {
/*  332 */                     cellAttr.setFieldid(elements.get(0).attr("name").substring(5));
/*      */                   }
/*      */                 }
/*      */               } 
/*      */             } 
/*  337 */             cellAttr.setEvalue(element.text());
/*  338 */             if (element.select("a").size() > 0) {
/*  339 */               cellAttr.setEtype(11);
/*  340 */             } else if (element.select("p").size() > 0) {
/*  341 */               cellAttr.setEtype(1);
/*  342 */               String str1 = "";
/*  343 */               Iterator<Element> iterator1 = element.select("p").iterator();
/*  344 */               while (iterator1.hasNext()) {
/*  345 */                 Element element1 = iterator1.next();
/*  346 */                 str1 = str1 + element1.text() + "\n";
/*      */               } 
/*  348 */               if (element.select("p").get(0).select("font").size() > 0 && element
/*  349 */                 .select("p").get(0).select("font").get(0).hasAttr("color")) {
/*  350 */                 cellAttr.setFont_color(element.select("p").get(0).select("font").get(0).attr("color"));
/*  351 */                 cellAttr.setHalign(0);
/*      */               } 
/*  353 */               cellAttr.setEvalue(str1);
/*      */             } else {
/*  355 */               if (cellAttr.getFieldid().equals("")) {
/*  356 */                 cellAttr.setEtype(1);
/*  357 */                 cellAttr.setFieldtype("text");
/*      */               } else {
/*  359 */                 cellAttr.setEtype(2);
/*      */               } 
/*  361 */               cellAttr.setEvalue(element.text());
/*      */             } 
/*  363 */             getCoordinate();
/*  364 */             cellAttr.setRowid(this.mainRow);
/*  365 */             cellAttr.setColid(this.mainCol);
/*  366 */             cellAttr.setBackground_color("#e7f3fc");
/*  367 */             getMainTableSpans(cellAttr, element);
/*      */             
/*  369 */             LinkedHashMap<Object, Object> linkedHashMap1 = new LinkedHashMap<>();
/*  370 */             LinkedHashMap<Object, Object> linkedHashMap2 = new LinkedHashMap<>();
/*  371 */             this.parseSheet.buildDataEcMap(cellAttr, (LinkedHashMap)linkedHashMap1);
/*  372 */             this.parseSheet.buildPluginCellMap(cellAttr, (LinkedHashMap)linkedHashMap2);
/*  373 */             linkedHashMap.put("" + this.mainCol, linkedHashMap2);
/*  374 */             this.mainList.add(linkedHashMap1);
/*  375 */             this.pluginMaintableData.put("" + this.mainRow, linkedHashMap);
/*  376 */             this.emaintable.put("ec", this.mainList);
/*      */             
/*  378 */             String str = element.attr("colspan");
/*  379 */             if (!str.equals("")) {
/*  380 */               this.mainCol += Integer.valueOf(str).intValue();
/*      */             } else {
/*  382 */               this.mainCol++;
/*      */             } 
/*  384 */             if (this.mainCol >= this.totalCols) {
/*  385 */               this.mainCol = 0;
/*      */             }
/*      */           } 
/*      */         } 
/*      */       } 
/*      */     } 
/*  391 */     Iterator<Element> iterator = paramElement.children().iterator();
/*  392 */     while (iterator.hasNext()) {
/*  393 */       Element element = iterator.next();
/*  394 */       recursiveDFS(element);
/*      */     } 
/*  396 */     return this.datajson;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private LinkedHashMap detailTableAnalysis(Element paramElement) {
/*  404 */     LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/*  405 */     if (paramElement.select("strong").size() > 0)
/*      */     
/*      */     { 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  662 */       Iterator<Element> iterator = paramElement.children().iterator();
/*  663 */       while (iterator.hasNext()) {
/*  664 */         Element element = iterator.next();
/*  665 */         detailTableAnalysis(element);
/*      */       } 
/*  667 */       return linkedHashMap; }  int i = 0; byte b1 = 0; this.pluginDetailtableData = new LinkedHashMap<>(); Elements elements = paramElement.select("tr"); byte b2; for (b2 = 0; b2 < elements.size(); b2++) { if (elements.get(b2).select("input[class=InputStyle]").size() > 0) { b1 = b2; break; }  }  for (b2 = 0; b2 < elements.size(); b2++) { Element element = elements.get(b2); LinkedHashMap<Object, Object> linkedHashMap1 = new LinkedHashMap<>(); int j = 0; if (element.childNodes().size() > 1) { Iterator<Element> iterator = element.select("td").iterator(); while (iterator.hasNext()) { Element element1 = iterator.next(); if (checkForDisplayNone(element1))
/*      */             continue;  if (element1.getElementsByClass("InputStyle").size() > 0) { Elements elements1 = element1.getElementsByClass("InputStyle"); if (this.detailCol == 0) { CellAttr cellAttr1 = createCell(); cellAttr1.setColspan(1); cellAttr1.setRowspan(1); cellAttr1.setRowid(this.detailRow); cellAttr1.setColid(this.detailCol); cellAttr1.setEtype(21); cellAttr1.setEvalue("选中"); LinkedHashMap<Object, Object> linkedHashMap4 = new LinkedHashMap<>(); LinkedHashMap<Object, Object> linkedHashMap5 = new LinkedHashMap<>(); this.detailList.add(linkedHashMap4); linkedHashMap.put("ec", this.detailList); this.parseSheet.buildDataEcMap(cellAttr1, (LinkedHashMap)linkedHashMap4); this.parseSheet.buildPluginCellMap(cellAttr1, (LinkedHashMap)linkedHashMap5); this.detailColheads.put("col_" + this.detailCol, "50"); linkedHashMap1.put("" + this.detailCol, linkedHashMap5); this.pluginDetailtableData.put("" + this.detailRow, linkedHashMap1); this.detailCol++; }  for (byte b = 0; b < elements1.size(); b++) { CellAttr cellAttr1 = createCell(); String str = elements1.get(b).val(); if (str.contains("序号")) { cellAttr1.setEtype(22); } else { cellAttr1.setEtype(3); }  cellAttr1.setRowid(this.detailRow); cellAttr1.setColid(this.detailCol); cellAttr1.setColspan(1); cellAttr1.setRowspan(1); if (elements1.get(b).hasAttr("name") && !elements1.get(b).attr("name").equals(""))
/*      */                 cellAttr1.setFieldid(elements1.get(b).attr("name").substring(5));  cellAttr1.setFieldtype(transControlType(cellAttr1.getFieldid())); if (str.contains("[") && str.contains("]")) { cellAttr1.setEvalue(str.substring(str.indexOf("]") + 1)); } else { cellAttr1.setEvalue(str); }  LinkedHashMap<Object, Object> linkedHashMap4 = new LinkedHashMap<>(); LinkedHashMap<Object, Object> linkedHashMap5 = new LinkedHashMap<>(); this.parseSheet.buildDataEcMap(cellAttr1, (LinkedHashMap)linkedHashMap4); this.parseSheet.buildPluginCellMap(cellAttr1, (LinkedHashMap)linkedHashMap5); linkedHashMap1.put("" + this.detailCol, linkedHashMap5); this.pluginDetailtableData.put("" + this.detailRow, linkedHashMap1); this.detailList.add(linkedHashMap4); linkedHashMap.put("ec", this.detailList); this.detailCol++; }  } else { if (linkedHashMap.size() == 0) { Iterator<Element> iterator1 = element.select("td").iterator(); while (iterator1.hasNext()) { Element element2 = iterator1.next(); if (element2.hasAttr("colspan")) { i += Integer.valueOf(element2.attr("colspan")).intValue(); continue; }  i++; }  if (element1.text().contains("序号")) { j = i; } else { j = i + 1; }  for (byte b = 0; b < i + 1; b++) { CellAttr cellAttr2 = new CellAttr(); if (b == i) { cellAttr2.setEtype(10); cellAttr2.setBright_style(1); cellAttr2.setBright_color("#90badd"); cellAttr2.setBackground_image("/workflow/exceldesign/image/shortBtn/detail/de_btn_wev8.png"); cellAttr2.setBackground_imagelayout("3"); }  cellAttr2.setRowid(this.detailRow); cellAttr2.setColid(this.detailCol); cellAttr2.setRowspan(1); cellAttr2.setColspan(1); LinkedHashMap<Object, Object> linkedHashMap6 = new LinkedHashMap<>(); LinkedHashMap<Object, Object> linkedHashMap7 = new LinkedHashMap<>(); this.detailList.add(linkedHashMap6); linkedHashMap.put("ec", this.detailList); linkedHashMap1.put("" + this.detailCol, linkedHashMap7); this.parseSheet.buildDataEcMap(cellAttr2, (LinkedHashMap)linkedHashMap6); this.parseSheet.buildPluginCellMap(cellAttr2, (LinkedHashMap)linkedHashMap7); this.pluginDetailtableData.put("" + this.detailRow, linkedHashMap1); this.detailCol++; }  this.detailRowheads.put("row_" + this.detailRow, "30"); linkedHashMap1 = new LinkedHashMap<>(); this.detailRow++; this.detailCol = 0; }  if (this.detailCol == 0 && b2 < b1) { CellAttr cellAttr2 = createCell(); cellAttr2.setRowid(this.detailRow); cellAttr2.setColid(this.detailCol); cellAttr2.setEvalue("全选"); cellAttr2.setEtype(20); cellAttr2.setColspan(1); cellAttr2.setRowspan(1); cellAttr2.setBackground_image("/workflow/exceldesign/image/shortBtn/detail/de_checkall_wev8.png"); cellAttr2.setBackground_imagelayout("3"); if (b1 - b2 > 1) { cellAttr2.setRowspan(b1 - b2); getDetailMergedInfo(this.detailRow, this.detailCol, cellAttr2.getRowspan(), "rowspan"); }  LinkedHashMap<Object, Object> linkedHashMap6 = new LinkedHashMap<>(); LinkedHashMap<Object, Object> linkedHashMap7 = new LinkedHashMap<>(); this.detailList.add(linkedHashMap6); linkedHashMap.put("ec", this.detailList); this.detailColheads.put("col_" + this.detailCol, "50"); this.parseSheet.buildDataEcMap(cellAttr2, (LinkedHashMap)linkedHashMap6); this.parseSheet.buildPluginCellMap(cellAttr2, (LinkedHashMap)linkedHashMap7); linkedHashMap1.put("" + this.detailCol, linkedHashMap7); this.detailCol++; }  CellAttr cellAttr1 = createCell(); cellAttr1.setRowid(this.detailRow); cellAttr1.setColid(this.detailCol); cellAttr1.setEvalue(element1.text()); getDetailTableSpans(cellAttr1, element1); if (element1.getElementsByClass("Label").size() > 0) { cellAttr1.setFieldid(element1.getElementsByClass("Label").attr("name").substring(5)); } else { Element element2 = elements.get(b1); if (b2 == b1) { if (element1.hasAttr("name") && element1.attr("name").contains("field"))
/*      */                   cellAttr1.setFieldid(element1.attr("name").substring(5));  } else if (b2 == b1 - 1) { if (element2.select("td").size() > this.detailCol - 1) { Elements elements1 = element2.select("td").get(this.detailCol - 1).getElementsByClass("InputStyle"); if (elements1.size() > 0)
/*      */                     cellAttr1.setFieldid(elements1.attr("name").substring(5));  }  } else if (b2 > b1 && element1.nextElementSibling() != null && element1.nextElementSibling().select("input[class=InputStyle]").size() > 0) { Element element3 = element1.nextElementSibling().select("input[class=InputStyle]").get(0); if (element3 != null && element3.hasAttr("name"))
/*      */                   cellAttr1.setFieldid(element3.attr("name").substring(5));  }  }  if (cellAttr1.getFieldid().equals("")) { cellAttr1.setEtype(1); } else { cellAttr1.setEtype(2); }  if (cellAttr1.getEvalue().contains("序号")) { this.detailColheads.put("col_" + this.detailCol, "50"); } else { this.detailColheads.put("col_" + this.detailCol, "120"); }  LinkedHashMap<Object, Object> linkedHashMap4 = new LinkedHashMap<>(); LinkedHashMap<Object, Object> linkedHashMap5 = new LinkedHashMap<>(); this.parseSheet.buildDataEcMap(cellAttr1, (LinkedHashMap)linkedHashMap4); this.parseSheet.buildPluginCellMap(cellAttr1, (LinkedHashMap)linkedHashMap5); linkedHashMap1.put("" + this.detailCol, linkedHashMap5); this.detailList.add(linkedHashMap4); linkedHashMap.put("ec", this.detailList); this.detailCol++; }  int k = (element1.siblingIndex() + 1) / 2 - 1; this.pluginDetailtableData.put("" + this.detailRow, linkedHashMap1); if (element1.nextElementSibling() == null || (checkForDisplayNone(element1.nextElementSibling()) && element.select("td:gt(" + k + ")").size() == element.select("td[style=display: none]:gt(" + k + ")").size())) { j = (this.detailCol > j) ? this.detailCol : j; this.detailRowheads.put("row_" + this.detailRow, "30"); this.detailRow++; this.detailCol = 0; linkedHashMap.put("rowheads", this.detailRowheads); }  }  linkedHashMap1 = new LinkedHashMap<>(); CellAttr cellAttr = createCell(); LinkedHashMap<Object, Object> linkedHashMap2 = new LinkedHashMap<>(); LinkedHashMap<Object, Object> linkedHashMap3 = new LinkedHashMap<>(); if (element.nextElementSibling() == null) { cellAttr.setColspan(i); cellAttr.setRowspan(1); cellAttr.setEtype(9); cellAttr.setEvalue("表尾标识"); cellAttr.setValign(1); cellAttr.setBackground_color("#eeeeee"); linkedHashMap.put("edtailinrow", "" + this.detailRow); LinkedHashMap<Object, Object> linkedHashMap4 = new LinkedHashMap<>(); linkedHashMap4.put("row", Integer.valueOf(this.detailRow)); linkedHashMap4.put("rowCount", Integer.valueOf(1)); linkedHashMap4.put("col", Integer.valueOf(this.detailCol)); linkedHashMap4.put("colCount", Integer.valueOf(i + 1)); this.detailSpans.add(linkedHashMap4); } else if (b2 == b1 - 1) { cellAttr.setColspan(i); cellAttr.setRowspan(1); cellAttr.setEtype(8); cellAttr.setEvalue("表头标识"); cellAttr.setValign(1); cellAttr.setBackground_color("#eeeeee"); linkedHashMap.put("edtitleinrow", "" + this.detailRow); LinkedHashMap<Object, Object> linkedHashMap4 = new LinkedHashMap<>(); linkedHashMap4.put("row", Integer.valueOf(this.detailRow)); linkedHashMap4.put("rowCount", Integer.valueOf(1)); linkedHashMap4.put("col", Integer.valueOf(this.detailCol)); linkedHashMap4.put("colCount", Integer.valueOf(i + 1)); this.detailSpans.add(linkedHashMap4); }  if (cellAttr.getEtype() != 0) { linkedHashMap1.put("" + this.detailCol, linkedHashMap3); cellAttr.setRowid(this.detailRow); cellAttr.setColid(this.detailCol); this.parseSheet.buildDataEcMap(cellAttr, (LinkedHashMap)linkedHashMap2); this.parseSheet.buildPluginCellMap(cellAttr, (LinkedHashMap)linkedHashMap3); this.detailList.add(linkedHashMap2); linkedHashMap.put("ec", this.detailList); linkedHashMap.put("seniorset", "1"); linkedHashMap.put("colheads", this.detailColheads); this.detailRowheads.put("row_" + this.detailRow, "30"); this.pluginDetailtableData.put("" + this.detailRow, linkedHashMap1); this.detailRow++; }
/*      */          }
/*      */        }
/*  675 */      this.detailRow = 0; this.detailColheads = new LinkedHashMap<>(); return linkedHashMap; } private void getPluginMap(String paramString, LinkedHashMap<String, String> paramLinkedHashMap1, LinkedHashMap paramLinkedHashMap2) { paramLinkedHashMap1.put("version", "2.0");
/*  676 */     paramLinkedHashMap1.put("tabStripVisible", Boolean.valueOf(false));
/*  677 */     paramLinkedHashMap1.put("canUserEditFormula", Boolean.valueOf(false));
/*  678 */     paramLinkedHashMap1.put("allowUndo", Boolean.valueOf(false));
/*  679 */     paramLinkedHashMap1.put("allowDragDrop", Boolean.valueOf(false));
/*  680 */     paramLinkedHashMap1.put("allowDragFill", Boolean.valueOf(false));
/*  681 */     paramLinkedHashMap1.put("grayAreaBackColor", "white");
/*  682 */     LinkedHashMap<Object, Object> linkedHashMap1 = new LinkedHashMap<>();
/*  683 */     paramLinkedHashMap1.put("sheets", linkedHashMap1);
/*  684 */     LinkedHashMap<Object, Object> linkedHashMap2 = new LinkedHashMap<>();
/*  685 */     linkedHashMap1.put("Sheet1", linkedHashMap2);
/*      */     
/*  687 */     linkedHashMap2.put("name", "Sheet1");
/*  688 */     LinkedHashMap<Object, Object> linkedHashMap3 = new LinkedHashMap<>();
/*  689 */     linkedHashMap3.put("rowHeight", Integer.valueOf(30));
/*  690 */     linkedHashMap3.put("colWidth", Integer.valueOf(60));
/*  691 */     linkedHashMap3.put("rowHeaderColWidth", Integer.valueOf(40));
/*  692 */     linkedHashMap3.put("colHeaderRowHeight", Integer.valueOf(20));
/*  693 */     linkedHashMap2.put("defaults", linkedHashMap3);
/*  694 */     LinkedHashMap<Object, Object> linkedHashMap4 = new LinkedHashMap<>();
/*  695 */     LinkedHashMap<Object, Object> linkedHashMap5 = new LinkedHashMap<>();
/*  696 */     linkedHashMap4.put("0", linkedHashMap5);
/*  697 */     linkedHashMap5.put("row", Integer.valueOf(0));
/*  698 */     linkedHashMap5.put("rowCount", Integer.valueOf(1));
/*  699 */     linkedHashMap5.put("col", Integer.valueOf(0));
/*  700 */     linkedHashMap5.put("colCount", Integer.valueOf(1));
/*  701 */     linkedHashMap2.put("selections", linkedHashMap4);
/*  702 */     linkedHashMap2.put("activeRow", Integer.valueOf(0));
/*  703 */     linkedHashMap2.put("activeCol", Integer.valueOf(0));
/*  704 */     LinkedHashMap<Object, Object> linkedHashMap6 = new LinkedHashMap<>();
/*  705 */     linkedHashMap6.put("color", "#D0D7E5");
/*  706 */     linkedHashMap6.put("showVerticalGridline", Boolean.valueOf(true));
/*  707 */     linkedHashMap6.put("showHorizontalGridline", Boolean.valueOf(true));
/*  708 */     linkedHashMap2.put("gridline", linkedHashMap6);
/*  709 */     linkedHashMap2.put("allowDragDrop", Boolean.valueOf(false));
/*  710 */     linkedHashMap2.put("allowDragFill", Boolean.valueOf(false));
/*      */     
/*  712 */     LinkedHashMap<Object, Object> linkedHashMap7 = new LinkedHashMap<>();
/*  713 */     LinkedHashMap<Object, Object> linkedHashMap8 = new LinkedHashMap<>();
/*  714 */     LinkedHashMap<Object, Object> linkedHashMap9 = new LinkedHashMap<>();
/*  715 */     LinkedHashMap<Object, Object> linkedHashMap10 = new LinkedHashMap<>();
/*  716 */     LinkedHashMap<Object, Object> linkedHashMap11 = new LinkedHashMap<>();
/*  717 */     LinkedHashMap<Object, Object> linkedHashMap12 = new LinkedHashMap<>();
/*  718 */     LinkedHashMap<Object, Object> linkedHashMap13 = new LinkedHashMap<>();
/*      */     
/*  720 */     linkedHashMap13.put("foreColor", "black");
/*  721 */     linkedHashMap10.put("style", linkedHashMap13);
/*      */     
/*  723 */     linkedHashMap2.put("rowHeaderData", linkedHashMap8);
/*  724 */     linkedHashMap2.put("colHeaderData", linkedHashMap9);
/*  725 */     if (paramString.contains("main")) {
/*  726 */       linkedHashMap8.put("rowCount", Integer.valueOf((this.mainRow >= 19) ? (this.mainRow + 5) : 20));
/*  727 */       linkedHashMap9.put("colCount", Integer.valueOf((this.totalCols >= 14) ? (this.totalCols + 3) : 15));
/*  728 */       linkedHashMap8.put("defaultDataNode", linkedHashMap10);
/*  729 */       linkedHashMap9.put("defaultDataNode", linkedHashMap10);
/*  730 */       linkedHashMap11.put("itemsCount", Integer.valueOf((this.mainRow >= 19) ? (this.mainRow + 5) : 20));
/*  731 */       linkedHashMap12.put("itemsCount", Integer.valueOf((this.totalCols >= 14) ? (this.totalCols + 3) : 15));
/*  732 */       linkedHashMap2.put("rowRangeGroup", linkedHashMap11);
/*  733 */       linkedHashMap2.put("colRangeGroup", linkedHashMap12);
/*  734 */       linkedHashMap2.put("spans", this.mainSpans);
/*  735 */       linkedHashMap7.put("rowCount", Integer.valueOf((this.mainRow >= 19) ? (this.mainRow + 5) : 20));
/*  736 */       linkedHashMap7.put("colCount", Integer.valueOf((this.totalCols >= 14) ? (this.totalCols + 3) : 15));
/*  737 */     } else if (paramString.contains("detail")) {
/*  738 */       int i = ((LinkedHashMap)paramLinkedHashMap2.get("0")).size();
/*  739 */       int j = paramLinkedHashMap2.size();
/*  740 */       if (paramLinkedHashMap2.size() > 3) {
/*  741 */         for (byte b = 0; b < paramLinkedHashMap2.size(); b++) {
/*  742 */           if (((LinkedHashMap)paramLinkedHashMap2.get("" + b)).size() == 1 && b != paramLinkedHashMap2.size() - 1) {
/*  743 */             i = ((LinkedHashMap)paramLinkedHashMap2.get("" + (b + 1))).size();
/*      */             break;
/*      */           } 
/*      */         } 
/*      */       }
/*  748 */       linkedHashMap8.put("rowCount", Integer.valueOf((j >= 9) ? (j + 5) : 10));
/*  749 */       linkedHashMap9.put("colCount", Integer.valueOf((i >= 19) ? (i + 5) : 20));
/*  750 */       linkedHashMap8.put("defaultDataNode", linkedHashMap10);
/*  751 */       linkedHashMap9.put("defaultDataNode", linkedHashMap10);
/*  752 */       linkedHashMap11.put("itemsCount", Integer.valueOf((j >= 9) ? (j + 5) : 10));
/*  753 */       linkedHashMap12.put("itemsCount", Integer.valueOf((i >= 19) ? (i + 5) : 20));
/*  754 */       linkedHashMap2.put("rowRangeGroup", linkedHashMap11);
/*  755 */       linkedHashMap2.put("colRangeGroup", linkedHashMap12);
/*  756 */       linkedHashMap2.put("spans", this.detailSpans);
/*  757 */       linkedHashMap7.put("rowCount", Integer.valueOf((j >= 9) ? (j + 5) : 10));
/*  758 */       linkedHashMap7.put("colCount", Integer.valueOf((i >= 19) ? (i + 5) : 20));
/*      */     } 
/*  760 */     linkedHashMap7.put("dataTable", paramLinkedHashMap2);
/*  761 */     linkedHashMap2.put("columns", getPluginColumns(paramString, paramLinkedHashMap2));
/*  762 */     linkedHashMap2.put("data", linkedHashMap7);
/*  763 */     this.pluginjson.put(paramString, paramLinkedHashMap1); }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private ArrayList getPluginColumns(String paramString, LinkedHashMap paramLinkedHashMap) {
/*  771 */     ArrayList<LinkedHashMap<Object, Object>> arrayList = new ArrayList();
/*  772 */     if (paramString.contains("main")) {
/*  773 */       for (byte b = 0; b < this.totalCols; b++) {
/*  774 */         LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/*  775 */         if (b % 2 == 0) { linkedHashMap.put("size", Integer.valueOf(100)); }
/*  776 */         else { linkedHashMap.put("size", Integer.valueOf(260)); }
/*  777 */          linkedHashMap.put("dirty", Boolean.valueOf(true));
/*  778 */         arrayList.add(linkedHashMap);
/*      */       } 
/*      */     } else {
/*  781 */       byte b1 = 0;
/*  782 */       for (byte b2 = 0; b2 < paramLinkedHashMap.size(); b2++) {
/*  783 */         if (((LinkedHashMap)paramLinkedHashMap.get("" + b2)).size() > ((LinkedHashMap)paramLinkedHashMap.get("" + (b2 + 1))).size()) {
/*  784 */           b1 = b2;
/*      */           break;
/*      */         } 
/*      */       } 
/*  788 */       boolean bool = ((LinkedHashMap)((LinkedHashMap)paramLinkedHashMap.get("" + b1)).get("1")).containsValue("序号");
/*  789 */       for (byte b3 = 0; b3 < ((LinkedHashMap)paramLinkedHashMap.get("" + b1)).size(); b3++) {
/*  790 */         LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/*  791 */         if (b3 == 0 || (b3 == 1 && bool)) {
/*  792 */           linkedHashMap.put("size", Integer.valueOf(50));
/*  793 */           linkedHashMap.put("dirty", Boolean.valueOf(true));
/*      */         } else {
/*  795 */           linkedHashMap.put("size", Integer.valueOf(120));
/*  796 */           linkedHashMap.put("dirty", Boolean.valueOf(true));
/*      */         } 
/*  798 */         arrayList.add(linkedHashMap);
/*      */       } 
/*      */     } 
/*  801 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void getDetailTableCell(String paramString) {
/*  809 */     CellAttr cellAttr = createCell();
/*  810 */     cellAttr.setRowid(this.mainRow);
/*  811 */     cellAttr.setColid(this.mainCol);
/*  812 */     cellAttr.setColspan(this.totalCols - this.mainCol);
/*  813 */     cellAttr.setEtype(7);
/*  814 */     cellAttr.setEvalue(paramString.substring(7));
/*      */     
/*  816 */     LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/*  817 */     this.parseSheet.buildDataEcMap(cellAttr, (LinkedHashMap)linkedHashMap);
/*  818 */     this.mainList.add(linkedHashMap);
/*  819 */     this.emaintable.put("ec", this.mainList);
/*  820 */     this.mainRowheads.put("row_" + this.mainRow, "30");
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void getMainTableSpans(CellAttr paramCellAttr, Element paramElement) {
/*  828 */     if (paramElement.childNodes().size() == 1 && paramElement.select("strong").size() > 0) {
/*  829 */       paramCellAttr.setColspan(this.totalCols);
/*  830 */       paramCellAttr.setRowspan(1);
/*  831 */       getMainMergedInfo(this.mainRow, 0, 1, this.totalCols, "colspan");
/*      */       return;
/*      */     } 
/*  834 */     boolean bool1 = !paramElement.attr("colspan").equals("") ? true : false;
/*  835 */     boolean bool2 = !paramElement.attr("rowspan").equals("") ? true : false;
/*  836 */     if (paramElement.tagName().equals("td") && paramElement.select("table[name*=oTable]").size() > 0) {
/*  837 */       getMainMergedInfo(this.mainRow, this.mainCol, 1, this.totalCols - this.mainCol, "colspan");
/*  838 */       paramCellAttr.setColspan(this.totalCols);
/*      */     
/*      */     }
/*  841 */     else if (bool1 && !bool2) {
/*  842 */       if (this.mainCol == this.totalCols) {
/*  843 */         this.mainCol = 0;
/*      */       }
/*  845 */       if (this.mainCol + Integer.valueOf(paramElement.attr("colspan")).intValue() > this.totalCols) {
/*  846 */         getMainMergedInfo(this.mainRow, this.mainCol, 1, this.totalCols - this.mainCol, "colspan");
/*  847 */         paramCellAttr.setColspan(this.totalCols - this.mainCol);
/*      */       } else {
/*  849 */         getMainMergedInfo(this.mainRow, this.mainCol, 1, Integer.valueOf(paramElement.attr("colspan")).intValue(), "colspan");
/*  850 */         paramCellAttr.setColspan(Integer.valueOf(paramElement.attr("colspan")).intValue());
/*      */       } 
/*      */     } else {
/*  853 */       paramCellAttr.setColspan(1);
/*  854 */       this.mainSpanArray[this.mainRow][this.mainCol] = 1;
/*      */     } 
/*      */ 
/*      */     
/*  858 */     if (bool2 && !bool1) {
/*  859 */       if (this.mainCol == this.totalCols) {
/*  860 */         this.mainCol = 0;
/*      */       }
/*  862 */       getMainMergedInfo(this.mainRow, this.mainCol, Integer.valueOf(paramElement.attr("rowspan")).intValue(), 1, "rowspan");
/*  863 */       paramCellAttr.setRowspan(Integer.valueOf(paramElement.attr("rowspan")).intValue());
/*      */     } else {
/*  865 */       paramCellAttr.setRowspan(1);
/*  866 */       this.mainSpanArray[this.mainRow][this.mainCol] = 1;
/*      */     } 
/*      */     
/*  869 */     if (bool1 && bool2) {
/*  870 */       getMainMergedInfo(this.mainRow, this.mainCol, Integer.valueOf(paramElement.attr("rowspan")).intValue(), Integer.valueOf(paramElement.attr("colspan")).intValue(), "colandrow");
/*  871 */       paramCellAttr.setRowspan(Integer.valueOf(paramElement.attr("rowspan")).intValue());
/*  872 */       paramCellAttr.setColspan(Integer.valueOf(paramElement.attr("colspan")).intValue());
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void getDetailTableSpans(CellAttr paramCellAttr, Element paramElement) {
/*  881 */     boolean bool1 = !paramElement.attr("colspan").equals("") ? true : false;
/*  882 */     boolean bool2 = !paramElement.attr("rowspan").equals("") ? true : false;
/*  883 */     if (bool1) {
/*  884 */       int i = Integer.valueOf(paramElement.attr("colspan")).intValue();
/*  885 */       getDetailMergedInfo(this.detailRow, this.detailCol, i, "colspan");
/*  886 */       paramCellAttr.setColspan(i);
/*  887 */       this.detailCol += i - 1;
/*      */     } else {
/*  889 */       paramCellAttr.setColspan(1);
/*      */     } 
/*  891 */     if (bool2) {
/*  892 */       int i = Integer.valueOf(paramElement.attr("rowspan")).intValue();
/*  893 */       getDetailMergedInfo(this.detailRow, this.detailCol, i, "rowspan");
/*  894 */       paramCellAttr.setRowspan(i);
/*      */     } else {
/*  896 */       paramCellAttr.setRowspan(1);
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void getMainMergedInfo(int paramInt1, int paramInt2, int paramInt3, int paramInt4, String paramString) {
/*  905 */     if (paramString.equals("colspan")) {
/*  906 */       LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/*  907 */       linkedHashMap.put("row", Integer.valueOf(paramInt1));
/*  908 */       linkedHashMap.put("rowCount", Integer.valueOf(1));
/*  909 */       linkedHashMap.put("col", Integer.valueOf(paramInt2));
/*  910 */       linkedHashMap.put("colCount", Integer.valueOf(paramInt4));
/*  911 */       this.mainSpans.add(linkedHashMap);
/*  912 */       for (byte b = 0; b < paramInt4; b++) {
/*  913 */         this.mainSpanArray[paramInt1][paramInt2 + b] = 1;
/*      */       }
/*  915 */     } else if (paramString.equals("rowspan")) {
/*  916 */       LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/*  917 */       linkedHashMap.put("row", Integer.valueOf(paramInt1));
/*  918 */       linkedHashMap.put("rowCount", Integer.valueOf(paramInt3));
/*  919 */       linkedHashMap.put("col", Integer.valueOf(paramInt2));
/*  920 */       linkedHashMap.put("colCount", Integer.valueOf(1));
/*  921 */       this.mainSpans.add(linkedHashMap);
/*  922 */       for (byte b = 0; b < paramInt3; b++) {
/*  923 */         this.mainSpanArray[paramInt1 + b][paramInt2] = 1;
/*      */       }
/*  925 */     } else if (paramString.equals("colandrow")) {
/*  926 */       LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/*  927 */       linkedHashMap.put("row", Integer.valueOf(paramInt1));
/*  928 */       linkedHashMap.put("rowCount", Integer.valueOf(paramInt3));
/*  929 */       linkedHashMap.put("col", Integer.valueOf(paramInt2));
/*  930 */       linkedHashMap.put("colCount", Integer.valueOf(paramInt4));
/*  931 */       this.mainSpans.add(linkedHashMap);
/*  932 */       for (byte b = 0; b < paramInt3; b++) {
/*  933 */         for (byte b1 = 0; b1 < paramInt4; b1++) {
/*  934 */           this.mainSpanArray[this.mainRow + b][this.mainCol + b1] = 1;
/*      */         }
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void getDetailMergedInfo(int paramInt1, int paramInt2, int paramInt3, String paramString) {
/*  945 */     if (paramString.equals("colspan")) {
/*  946 */       LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/*  947 */       linkedHashMap.put("row", Integer.valueOf(paramInt1));
/*  948 */       linkedHashMap.put("rowCount", Integer.valueOf(1));
/*  949 */       linkedHashMap.put("col", Integer.valueOf(paramInt2));
/*  950 */       linkedHashMap.put("colCount", Integer.valueOf(paramInt3));
/*  951 */       this.detailSpans.add(linkedHashMap);
/*  952 */     } else if (paramString.equals("rowspan")) {
/*  953 */       LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/*  954 */       linkedHashMap.put("row", Integer.valueOf(paramInt1));
/*  955 */       linkedHashMap.put("rowCount", Integer.valueOf(paramInt3));
/*  956 */       linkedHashMap.put("col", Integer.valueOf(paramInt2));
/*  957 */       linkedHashMap.put("colCount", Integer.valueOf(1));
/*  958 */       this.detailSpans.add(linkedHashMap);
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private boolean checkForDisplayNone(Element paramElement) {
/*  967 */     if (paramElement.hasAttr("style") && (paramElement
/*  968 */       .attr("style").equals("display: none") || paramElement
/*  969 */       .attr("style").equals("display:none"))) {
/*  970 */       return true;
/*      */     }
/*  972 */     return false;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private CellAttr createCell() {
/*  980 */     CellAttr cellAttr = new CellAttr();
/*  981 */     cellAttr.setHalign(1);
/*  982 */     cellAttr.setValign(1);
/*  983 */     cellAttr.setBtop_style(1);
/*  984 */     cellAttr.setBtop_color("#90badd");
/*  985 */     cellAttr.setBbottom_style(1);
/*  986 */     cellAttr.setBbottom_color("#90badd");
/*  987 */     cellAttr.setBleft_style(1);
/*  988 */     cellAttr.setBleft_color("#90badd");
/*  989 */     cellAttr.setBright_style(1);
/*  990 */     cellAttr.setBright_color("#90badd");
/*  991 */     return cellAttr;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void getCoordinate() {
/* 1007 */     while (this.mainSpanArray[this.mainRow][this.mainCol] == 1) {
/* 1008 */       this.mainCol++;
/* 1009 */       if (this.mainCol == this.totalCols) {
/* 1010 */         this.mainRow++;
/* 1011 */         this.mainCol = 0;
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String transControlType(String paramString) {
/* 1021 */     RecordSet recordSet = new RecordSet();
/* 1022 */     int i = Util.getIntValue(paramString);
/* 1023 */     recordSet.executeQuery("select fieldhtmltype, type from workflow_billfield where id = ?", new Object[] { Integer.valueOf(i) });
/*      */     
/* 1025 */     String str = "";
/* 1026 */     if (recordSet.next()) {
/*      */       
/* 1028 */       String str1 = recordSet.getString(1);
/* 1029 */       String str2 = recordSet.getString(2);
/*      */       
/* 1031 */       if (str1.equals("3")) {
/* 1032 */         if (str2.equals("2"))
/* 1033 */         { str = "date"; }
/* 1034 */         else if (str2.equals("19"))
/* 1035 */         { str = "time"; }
/*      */         else
/* 1037 */         { str = "browser"; } 
/* 1038 */       } else if (str1.equals("1")) {
/* 1039 */         str = "text";
/* 1040 */       } else if (str1.equals("2")) {
/* 1041 */         str = "textarea";
/* 1042 */       } else if (str1.equals("4")) {
/* 1043 */         str = "checkbox";
/* 1044 */       } else if (str1.equals("5")) {
/* 1045 */         str = "select";
/* 1046 */       } else if (str1.equals("6")) {
/* 1047 */         str = "affix";
/* 1048 */       } else if (str1.equals("7")) {
/* 1049 */         str = "link";
/* 1050 */       } else if (str1.equals("8")) {
/* 1051 */         str = "radio";
/* 1052 */       } else if (str1.equals("9")) {
/* 1053 */         str = "position";
/*      */       } 
/* 1055 */     }  return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String getTrimedText(String paramString) throws UnsupportedEncodingException {
/* 1063 */     byte[] arrayOfByte = { -62, -96 };
/* 1064 */     String str = null;
/* 1065 */     str = new String(arrayOfByte, "UTF-8");
/* 1066 */     paramString = paramString.replaceAll(str, "");
/* 1067 */     return paramString;
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/filecheck/HtmlAnalysis.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */