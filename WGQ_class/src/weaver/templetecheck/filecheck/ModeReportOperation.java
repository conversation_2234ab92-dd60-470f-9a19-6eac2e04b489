/*     */ package weaver.templetecheck.filecheck;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ModeReportOperation
/*     */ {
/*     */   public JSONObject getModeReportInfo() {
/*  21 */     JSONObject jSONObject = new JSONObject();
/*  22 */     RecordSet recordSet = new RecordSet();
/*  23 */     String str1 = "";
/*  24 */     String str2 = recordSet.getDBType().toLowerCase();
/*     */     
/*  26 */     str1 = "select count(1) as counts from modetreefield where isdelete=0 or isdelete is null ";
/*  27 */     recordSet.executeQuery(str1, new Object[0]);
/*  28 */     if (recordSet.next()) {
/*  29 */       jSONObject.put("modeapp", Util.null2String(recordSet.getString("counts"), "0"));
/*     */     }
/*     */ 
/*     */     
/*  33 */     str1 = "select count(1) as counts from modeinfo where isdelete=0 or isdelete is null ";
/*  34 */     recordSet.executeQuery(str1, new Object[0]);
/*  35 */     if (recordSet.next()) {
/*  36 */       jSONObject.put("mode", Util.null2String(recordSet.getString("counts"), "0"));
/*     */     }
/*     */ 
/*     */     
/*  40 */     boolean bool = CheckRule.checkDBFieldIsExist("version", "modehtmllayout");
/*  41 */     str1 = "select count(1) as counts from modehtmllayout a left join modeinfo b on a.modeid=b.id left join modetreefield c on b.modetype=c.id   where " + (bool ? " version!=2 and" : "") + " b.id is not null and c.id is not null and (b.isdelete=0 or b.isdelete is null) and (c.isdelete=0 or c.isdelete is null) ";
/*  42 */     recordSet.executeQuery(str1, new Object[0]);
/*  43 */     if (recordSet.next()) {
/*  44 */       jSONObject.put("html", Util.null2String(recordSet.getString("counts"), "0"));
/*     */     }
/*     */ 
/*     */     
/*  48 */     str1 = "select count(1) as counts from modehtmllayout a left join modeinfo b on a.modeid=b.id left join modetreefield c on b.modetype=c.id   where " + (bool ? " version=2 and " : "1=2 and ") + " b.id is not null and c.id is not null and (b.isdelete=0 or b.isdelete is null) and (c.isdelete=0 or c.isdelete is null) ";
/*  49 */     recordSet.executeQuery(str1, new Object[0]);
/*  50 */     if (recordSet.next()) {
/*  51 */       jSONObject.put("excel", Util.null2String(recordSet.getString("counts"), "0"));
/*     */     }
/*     */ 
/*     */     
/*  55 */     str1 = "select count(a.id) as counts from mode_pageexpand  a left join modeinfo b on a.modeid=b.id left join modetreefield c on b.modetype=c.id where a.hreftarget is not null " + (!"oracle".equals(str2) ? " and a.hreftarget !='' " : "") + " and b.id is not null and c.id is not null and (b.isdelete=0 or b.isdelete is null) and (c.isdelete=0 or c.isdelete is null) ";
/*  56 */     recordSet.executeQuery(str1, new Object[0]);
/*  57 */     if (recordSet.next()) {
/*  58 */       jSONObject.put("pageExpand", Util.null2String(recordSet.getString("counts"), "0"));
/*     */     }
/*     */ 
/*     */     
/*  62 */     str1 = "select count(1) as counts from mode_customsearch a left join modetreefield c on a.appid=c.id where a.defaultsql is not null " + (!"oracle".equals(str2) ? " and a.defaultsql !='' " : "") + " and c.id is not null and (c.isdelete=0 or c.isdelete is null)";
/*     */     
/*  64 */     str1 = str1 + " and a.searchconditiontype!=2 ";
/*  65 */     recordSet.executeQuery(str1, new Object[0]);
/*  66 */     if (recordSet.next()) {
/*  67 */       jSONObject.put("customSearch", Util.null2String(recordSet.getString("counts"), "0"));
/*     */     }
/*     */     
/*  70 */     str1 = "select count(1) as counts from mode_customsearch a left join modetreefield c on a.appid=c.id where a.defaultsql is not null " + (!"oracle".equals(str2) ? " and a.defaultsql !='' " : "") + " and searchconditiontype=1 and c.id is not null and (c.isdelete=0 or c.isdelete is null)";
/*  71 */     recordSet.executeQuery(str1, new Object[0]);
/*  72 */     if (recordSet.next()) {
/*  73 */       jSONObject.put("customSearchForSql", Util.null2String(recordSet.getString("counts"), "0"));
/*     */     }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  85 */     str1 = "select count(1) as counts from mode_customSearchButton a left join mode_customsearch b on a.objid=b.id left join modeinfo c on b.modeid=c.id  left join modetreefield d on c.modetype=d.id where a.jsmethodbody is not null  and b.id is not null and  c.id is not null and d.id is not null and (c.isdelete=0 or c.isdelete is null) and (d.isdelete=0 or d.isdelete is null) ";
/*     */     
/*  87 */     recordSet.executeQuery(str1, new Object[0]);
/*  88 */     if (recordSet.next()) {
/*  89 */       jSONObject.put("customSearchButton", Util.null2String(recordSet.getString("counts"), "0"));
/*     */     }
/*     */ 
/*     */ 
/*     */     
/*  94 */     str1 = "select count(1) as counts from mode_custombrowser a left join modetreefield c on a.appid=c.id where a.defaultsql is not null " + (!"oracle".equals(str2) ? " and a.defaultsql !='' " : "") + " and c.id is not null and (c.isdelete=0 or c.isdelete is null)  ";
/*     */     
/*  96 */     str1 = str1 + "  and searchconditiontype!=2";
/*  97 */     recordSet.executeQuery(str1, new Object[0]);
/*  98 */     if (recordSet.next()) {
/*  99 */       jSONObject.put("customBrowser", Util.null2String(recordSet.getString("counts"), "0"));
/*     */     }
/*     */ 
/*     */ 
/*     */     
/* 104 */     str1 = "select count(1) as counts from mode_custombrowser a left join modetreefield c on a.appid=c.id where  a.defaultsql is not null " + (!"oracle".equals(str2) ? " and a.defaultsql !='' " : "") + " and c.id is not null and (c.isdelete=0 or c.isdelete is null)  and searchconditiontype=1 ";
/* 105 */     recordSet.executeQuery(str1, new Object[0]);
/* 106 */     if (recordSet.next()) {
/* 107 */       jSONObject.put("customBrowserForSql", Util.null2String(recordSet.getString("counts"), "0"));
/*     */     }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 116 */     return jSONObject;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<Map<String, String>> getModeReportDetailInfo(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 130 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 131 */     int i = Util.getIntValue(paramMap.get("pageSize"));
/* 132 */     int j = Util.getIntValue(paramMap.get("pageIndex"));
/* 133 */     String str1 = Util.null2String(paramMap.get("detailtype"));
/* 134 */     RecordSet recordSet = new RecordSet();
/* 135 */     String str2 = recordSet.getDBType().toLowerCase();
/* 136 */     if ("modeapp".equalsIgnoreCase(str1)) {
/* 137 */       recordSet.execute("select id,id as modetype,treefieldname as name from modetreefield where isdelete=0 or isdelete is null ");
/* 138 */       while (recordSet.next()) {
/* 139 */         HashMap<Object, Object> hashMap = new HashMap<>();
/* 140 */         hashMap.put("name", Util.null2String(recordSet.getString("name")));
/* 141 */         hashMap.put("location", getAppLocation(Util.null2String(recordSet.getString("modetype"))));
/* 142 */         arrayList.add(hashMap);
/*     */       } 
/* 144 */     } else if ("mode".equalsIgnoreCase(str1)) {
/* 145 */       recordSet.execute("select modename as name,modetype from  modeinfo a  left join modetreefield b on a.modetype = b.id where  b.id is not null and (a.isdelete=0 or a.isdelete is null) and (b.isdelete=0 or b.isdelete is null) ");
/* 146 */       while (recordSet.next()) {
/* 147 */         HashMap<Object, Object> hashMap = new HashMap<>();
/* 148 */         hashMap.put("name", Util.null2String(recordSet.getString("name")));
/* 149 */         hashMap.put("location", getAppLocation(Util.null2String(recordSet.getString("modetype"))));
/* 150 */         arrayList.add(hashMap);
/*     */       } 
/* 152 */     } else if ("excel".equalsIgnoreCase(str1) || "pageExpand".equalsIgnoreCase(str1) || "html".equalsIgnoreCase(str1)) {
/* 153 */       String str = "";
/* 154 */       if ("html".equalsIgnoreCase(str1)) {
/* 155 */         str = "select a.id,a.layoutname as name,a.syspath,b.modetype,b.modename,c.treeFieldName from modehtmllayout a left join modeinfo b on a.modeid=b.id left join modetreefield c on b.modetype=c.id where a.version!=2 and b.id is not null and c.id is not null and (b.isdelete=0 or b.isdelete is null) and (c.isdelete=0 or c.isdelete is null) order by a.id";
/* 156 */       } else if ("excel".equalsIgnoreCase(str1)) {
/* 157 */         str = "select a.id,a.layoutname  as name,a.syspath,b.modetype,b.modename,c.treeFieldName from modehtmllayout a left join modeinfo b on a.modeid=b.id left join modetreefield c on b.modetype=c.id where a.version=2 and b.id is not null and c.id is not null  and (b.isdelete=0 or b.isdelete is null)  and (c.isdelete=0 or c.isdelete is null) order by a.id";
/* 158 */       } else if ("pageExpand".equalsIgnoreCase(str1)) {
/* 159 */         str = "select a.id,a.expendname  as name,issystem,issystemflag,b.modetype,b.modename,c.treeFieldName from mode_pageexpand a left join modeinfo b on a.modeid=b.id left join modetreefield c on b.modetype=c.id where a.hreftarget is not null " + (!"oracle".equals(str2) ? " and a.hreftarget !='' " : "") + " and b.id is not null and c.id is not null and (c.isdelete=0 or c.isdelete is null) and (b.isdelete=0 or b.isdelete is null)   order by a.id";
/*     */       } 
/* 161 */       recordSet.execute(str);
/* 162 */       while (recordSet.next()) {
/* 163 */         HashMap<Object, Object> hashMap = new HashMap<>();
/* 164 */         String str3 = Util.null2String(recordSet.getString("modename"));
/* 165 */         if ("pageExpand".equalsIgnoreCase(str1)) {
/* 166 */           String str4 = Util.null2String(recordSet.getString("issystem"), "0");
/* 167 */           String str5 = Util.null2String(recordSet.getString("issystemflag"), "0");
/* 168 */           String str6 = Util.null2String(recordSet.getString("name"));
/* 169 */           int k = paramUser.getLanguage();
/* 170 */           hashMap.put("name", getExpandNameWithoutUrl(str6, str4, str5, k));
/*     */         } else {
/* 172 */           hashMap.put("name", Util.null2String(recordSet.getString("name")));
/*     */         } 
/* 174 */         if ("html".equalsIgnoreCase(str1)) {
/* 175 */           hashMap.put("location", Util.null2String(recordSet.getString("syspath")));
/*     */         } else {
/* 177 */           hashMap.put("location", getAppLocation(Util.null2String(recordSet.getString("modetype"))) + " > " + SystemEnv.getHtmlLabelName(19049, ThreadVarLanguage.getLang()) + ":" + str3);
/*     */         } 
/* 179 */         arrayList.add(hashMap);
/*     */       } 
/* 181 */     } else if ("customSearch".equalsIgnoreCase(str1) || "customSearchForSql".equalsIgnoreCase(str1) || "customSearchForJava".equalsIgnoreCase(str1)) {
/* 182 */       String str3 = " a.defaultsql is not null " + (!"oracle".equals(str2) ? " and a.defaultsql !='' " : "") + " and c.id is not null and (c.isdelete=0 or c.isdelete is null) ";
/* 183 */       if ("customSearchForSql".equalsIgnoreCase(str1)) {
/* 184 */         str3 = str3 + " and searchconditiontype=1 ";
/*     */       }
/*     */ 
/*     */ 
/*     */       
/* 189 */       String str4 = "select a.id,a.customname as name ,a.appid as modetype from mode_customsearch a left join modetreefield c on a.appid=c.id where " + str3 + "   order by a.id ";
/* 190 */       recordSet.executeQuery(str4, new Object[0]);
/* 191 */       while (recordSet.next()) {
/* 192 */         HashMap<Object, Object> hashMap = new HashMap<>();
/* 193 */         String str = Util.null2String(recordSet.getString("name"));
/* 194 */         hashMap.put("name", str);
/* 195 */         hashMap.put("location", getAppLocation(Util.null2String(recordSet.getString("modetype"))) + " > " + SystemEnv.getHtmlLabelName(527, ThreadVarLanguage.getLang()) + ":" + str);
/* 196 */         arrayList.add(hashMap);
/*     */       } 
/* 198 */     } else if ("customSearchButton".equalsIgnoreCase(str1)) {
/* 199 */       recordSet.executeQuery("select a.buttonname as name,c.modetype,b.customname  from mode_customSearchButton a left join mode_customsearch b on a.objid=b.id left join modeinfo c on b.modeid=c.id left join modetreefield d on c.modetype=d.id where a.jsmethodbody is not null and b.id is not null  and c.id is not null and d.id is not null and (c.isdelete=0 or c.isdelete is null) and (d.isdelete=0 or d.isdelete is null) order by a.id", new Object[0]);
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 204 */       while (recordSet.next()) {
/* 205 */         HashMap<Object, Object> hashMap = new HashMap<>();
/* 206 */         String str = Util.null2String(recordSet.getString("name"));
/* 207 */         hashMap.put("name", str);
/* 208 */         hashMap.put("location", getAppLocation(Util.null2String(recordSet.getString("modetype"))) + " > " + SystemEnv.getHtmlLabelName(527, ThreadVarLanguage.getLang()) + ":" + Util.null2String(recordSet.getString("customname")));
/* 209 */         arrayList.add(hashMap);
/*     */       } 
/* 211 */     } else if ("customBrowser".equalsIgnoreCase(str1) || "customBrowserForSql".equalsIgnoreCase(str1) || "customBrowserForJava".equalsIgnoreCase(str1)) {
/* 212 */       String str3 = "  a.defaultsql is not null " + (!"oracle".equals(str2) ? " and a.defaultsql !='' " : "") + " and c.id is not null and (c.isdelete=0 or c.isdelete is null) ";
/* 213 */       if ("customBrowserForSql".equalsIgnoreCase(str1)) {
/* 214 */         str3 = str3 + " and a.searchconditiontype=1 ";
/*     */       }
/*     */ 
/*     */ 
/*     */       
/* 219 */       String str4 = "select a.id,a.customname as name,a.appid as modetype from mode_custombrowser a left join modetreefield c on a.appid=c.id  where " + str3 + " order by a.id";
/* 220 */       recordSet.executeQuery(str4, new Object[0]);
/* 221 */       while (recordSet.next()) {
/* 222 */         HashMap<Object, Object> hashMap = new HashMap<>();
/* 223 */         String str = Util.null2String(recordSet.getString("name"));
/* 224 */         hashMap.put("name", str);
/* 225 */         hashMap.put("location", getAppLocation(Util.null2String(recordSet.getString("modetype"))) + " > " + SystemEnv.getHtmlLabelName(32306, ThreadVarLanguage.getLang()) + ":" + str);
/* 226 */         arrayList.add(hashMap);
/*     */       } 
/*     */     } 
/* 229 */     return (List)arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getAppLocation(String paramString) {
/* 239 */     String str = "";
/* 240 */     if ("".equals(paramString)) {
/* 241 */       return "<span style=\"color:red\" > " + SystemEnv.getHtmlLabelName(10004210, ThreadVarLanguage.getLang()) + "</span>";
/*     */     }
/* 243 */     RecordSet recordSet = new RecordSet();
/* 244 */     recordSet.execute("select id,treefieldname,allSuperFieldId from modetreefield where id='" + paramString + "' and (isdelete=0 or isdelete is null) ");
/* 245 */     if (recordSet.next()) {
/* 246 */       String str1 = recordSet.getString("treefieldname");
/* 247 */       String str2 = Util.null2String(recordSet.getString("allSuperFieldId"));
/* 248 */       if (!"".equals(str2)) {
/* 249 */         if (str2.startsWith(",")) str2 = str2.substring(1); 
/* 250 */         if (str2.endsWith(",")) str2 = str2.substring(0, str2.length() - 1); 
/*     */       } 
/* 252 */       if (!"".equals(str2)) {
/* 253 */         RecordSet recordSet1 = new RecordSet();
/* 254 */         recordSet1.executeQuery("select treeFieldName from modetreefield where id in(" + str2 + ") order by superFieldid ", new Object[0]);
/* 255 */         while (recordSet1.next()) {
/* 256 */           str = str + "" + SystemEnv.getHtmlLabelName(25432, ThreadVarLanguage.getLang()) + ":" + recordSet1.getString("treefieldname") + " > ";
/*     */         }
/*     */       } 
/* 259 */       str = str + "" + SystemEnv.getHtmlLabelName(25432, ThreadVarLanguage.getLang()) + ":" + str1;
/*     */     } else {
/* 261 */       str = "<span style=\"color:red\" > " + SystemEnv.getHtmlLabelName(10004210, ThreadVarLanguage.getLang()) + "</span>";
/*     */     } 
/* 263 */     return str;
/*     */   }
/*     */   
/*     */   public String getMatchDevContentRuleid() {
/* 267 */     String str = "";
/* 268 */     RecordSet recordSet = new RecordSet();
/* 269 */     recordSet.execute("select id from upgradecheckmoderule ");
/* 270 */     while (recordSet.next()) {
/* 271 */       str = str + recordSet.getString("id") + ",";
/*     */     }
/* 273 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<Map<String, String>> getModeCheckResultList(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 286 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 287 */     int i = Util.getIntValue(paramMap.get("pageSize"));
/* 288 */     int j = Util.getIntValue(paramMap.get("pageIndex"));
/* 289 */     String str1 = Util.null2String(paramMap.get("ruleid"));
/* 290 */     String str2 = Util.null2String(paramMap.get("workflowname"));
/* 291 */     String str3 = Util.null2String(paramMap.get("nodename"));
/* 292 */     RecordSet recordSet = new RecordSet();
/*     */ 
/*     */     
/* 295 */     String str4 = " where  1=1 ";
/* 296 */     if (!"".equals(str1)) {
/* 297 */       if (str1.endsWith(",")) {
/* 298 */         str1 = str1.substring(0, str1.length() - 1);
/*     */       }
/*     */       
/* 301 */       str4 = str4 + " and ruleid in (" + str1 + ")";
/*     */     } 
/* 303 */     if (!"".equals(str2)) {
/* 304 */       str4 = str4 + " and workflowname like '%" + str2 + "%'";
/*     */     }
/* 306 */     if (!"".equals(str3)) {
/* 307 */       str4 = str4 + " and nodename like '%" + str3 + "%'";
/*     */     }
/* 309 */     str4 = str4 + " group by t1.workflowname,t1.nodehtmllayoutid,t1.nodename,t1.detailtype,t1.refertable,t1.filepath,t1.contenttype ";
/*     */     
/* 311 */     String str5 = " select  a.id,a.workflowname,a.nodehtmllayoutid,a.nodename,a.detailtype,a.refertable,a.filepath,a.contenttype,b.formid,b.modeid,b.type as layouttype,b.version  from ( select  max(t1.id) as id,t1.workflowname,t1.nodehtmllayoutid,t1.nodename,t1.detailtype,t1.refertable,t1.filepath,t1.contenttype from upgradecheckmoderesult t1 " + str4 + " ) a left join modehtmllayout b on a.nodehtmllayoutid =b.id   order by  a.nodehtmllayoutid,a.detailtype ";
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 316 */     recordSet.execute(str5);
/*     */     
/* 318 */     while (recordSet.next()) {
/* 319 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 320 */       String str = Util.null2String(recordSet.getString("filepath"));
/* 321 */       if (!"".equals(str)) {
/* 322 */         hashMap.put("nodename", str);
/*     */       } else {
/* 324 */         hashMap.put("nodename", Util.null2String(recordSet.getString("nodename")));
/*     */       } 
/* 326 */       hashMap.put("id", Util.null2String(recordSet.getString("id")));
/* 327 */       hashMap.put("workflowname", Util.null2String(recordSet.getString("workflowname")));
/* 328 */       hashMap.put("nodehtmllayoutid", Util.null2String(recordSet.getString("nodehtmllayoutid")));
/* 329 */       hashMap.put("refertable", Util.null2String(recordSet.getString("refertable")));
/* 330 */       hashMap.put("detailtype", Util.null2String(recordSet.getString("detailtype")));
/* 331 */       hashMap.put("modeid", Util.null2String(recordSet.getString("modeid")));
/* 332 */       hashMap.put("formid", Util.null2String(recordSet.getString("formid")));
/* 333 */       hashMap.put("layouttype", Util.null2String(recordSet.getString("layouttype")));
/* 334 */       hashMap.put("version", Util.null2String(recordSet.getString("version")));
/* 335 */       hashMap.put("contenttype", Util.null2String(recordSet.getString("contenttype")));
/* 336 */       arrayList.add(hashMap);
/*     */     } 
/* 338 */     return (List)arrayList;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getExpandNameWithoutUrl(String paramString1, String paramString2, String paramString3, int paramInt) {
/* 343 */     if (!Util.isEnableMultiLang()) {
/* 344 */       paramString1 = Util.formatMultiLang(paramString1, Util.null2String(Integer.valueOf(paramInt)));
/*     */     }
/* 346 */     String str = paramString1;
/* 347 */     if (paramString2.equals("1")) {
/* 348 */       str = str + "(";
/* 349 */       if (paramString3.equals("1")) {
/* 350 */         str = str + SystemEnv.getHtmlLabelName(30829, paramInt);
/* 351 */       } else if (paramString3.equals("2")) {
/* 352 */         str = str + SystemEnv.getHtmlLabelName(30830, paramInt);
/* 353 */       } else if (paramString3.equals("3")) {
/* 354 */         str = str + SystemEnv.getHtmlLabelName(30831, paramInt);
/* 355 */       } else if (paramString3.equals("4")) {
/* 356 */         str = str + SystemEnv.getHtmlLabelName(30832, paramInt);
/* 357 */       } else if (paramString3.equals("5")) {
/* 358 */         str = str + SystemEnv.getHtmlLabelName(30833, paramInt);
/* 359 */       } else if (paramString3.equals("6")) {
/* 360 */         str = str + SystemEnv.getHtmlLabelName(30834, paramInt);
/* 361 */       } else if (paramString3.equals("7")) {
/* 362 */         str = str + SystemEnv.getHtmlLabelName(257, paramInt);
/* 363 */       } else if (paramString3.equals("8")) {
/* 364 */         str = str + SystemEnv.getHtmlLabelName(33418, paramInt);
/* 365 */       } else if (paramString3.equals("100")) {
/* 366 */         str = str + SystemEnv.getHtmlLabelName(197, paramInt);
/* 367 */       } else if (paramString3.equals("101")) {
/* 368 */         str = str + SystemEnv.getHtmlLabelName(82, paramInt);
/* 369 */       } else if (paramString3.equals("102")) {
/* 370 */         str = str + SystemEnv.getHtmlLabelName(91, paramInt);
/* 371 */       } else if (paramString3.equals("103")) {
/* 372 */         str = str + SystemEnv.getHtmlLabelName(26601, paramInt);
/* 373 */       } else if (paramString3.equals("105")) {
/* 374 */         str = str + SystemEnv.getHtmlLabelName(17416, paramInt);
/* 375 */       } else if (paramString3.equals("104")) {
/* 376 */         str = str + SystemEnv.getHtmlLabelName(18037, paramInt);
/* 377 */       } else if (paramString3.equals("9")) {
/* 378 */         str = str + SystemEnv.getHtmlLabelName(83, paramInt);
/* 379 */       } else if (paramString3.equals("10")) {
/* 380 */         str = str + SystemEnv.getHtmlLabelName(32720, paramInt);
/* 381 */       } else if (paramString3.equals("17")) {
/* 382 */         str = str + SystemEnv.getHtmlLabelName(500750, paramInt);
/* 383 */       } else if (paramString3.equals("106")) {
/* 384 */         str = str + SystemEnv.getHtmlLabelName(32535, paramInt);
/* 385 */       } else if (paramString3.equals("11")) {
/* 386 */         str = str + SystemEnv.getHtmlLabelName(125511, paramInt);
/* 387 */       } else if (paramString3.equals("12")) {
/* 388 */         str = str + SystemEnv.getHtmlLabelName(125512, paramInt);
/* 389 */       } else if (paramString3.equals("13")) {
/* 390 */         str = str + SystemEnv.getHtmlLabelName(220, paramInt);
/* 391 */       } else if (paramString3.equals("14")) {
/* 392 */         str = str + SystemEnv.getHtmlLabelName(81470, paramInt);
/* 393 */       } else if (paramString3.equals("170")) {
/* 394 */         str = str + SystemEnv.getHtmlLabelName(126683, paramInt);
/* 395 */       } else if (paramString3.equals("171")) {
/* 396 */         str = str + SystemEnv.getHtmlLabelName(126684, paramInt);
/* 397 */       } else if (paramString3.equals("110")) {
/* 398 */         str = str + SystemEnv.getHtmlLabelName(389877, paramInt);
/* 399 */       } else if (paramString3.equals("167")) {
/* 400 */         str = str + SystemEnv.getHtmlLabelName(384962, paramInt);
/*     */       } 
/* 402 */       str = str + ")";
/*     */     } 
/* 404 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/filecheck/ModeReportOperation.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */