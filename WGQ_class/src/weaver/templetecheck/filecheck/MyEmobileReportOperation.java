/*     */ package weaver.templetecheck.filecheck;
/*     */ 
/*     */ import java.io.File;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.regex.Matcher;
/*     */ import java.util.regex.Pattern;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import org.dom4j.Document;
/*     */ import org.dom4j.Element;
/*     */ import org.dom4j.io.SAXReader;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.templetecheck.FileUtil;
/*     */ 
/*     */ public class MyEmobileReportOperation
/*     */ {
/*  25 */   private static final String XMLPATH = GCONST.getRootPath() + "mobile" + File.separatorChar + "plugin" + File.separatorChar + "plugin.xml";
/*     */   
/*     */   public String getElementText(Element paramElement) {
/*  28 */     if (paramElement == null) {
/*  29 */       return "";
/*     */     }
/*  31 */     return paramElement.getText();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static int getMyEmobileXMLNum() {
/*  41 */     SAXReader sAXReader = new SAXReader();
/*  42 */     Document document = null;
/*  43 */     int i = 0;
/*  44 */     File file = new File(XMLPATH);
/*  45 */     if (file.exists()) {
/*     */       try {
/*  47 */         document = sAXReader.read(file);
/*  48 */         Element element1 = document.getRootElement();
/*  49 */         Element element2 = element1.element("components");
/*  50 */         i = element2.elements().size();
/*  51 */       } catch (Exception exception) {
/*  52 */         (new BaseBean()).writeLog("解决xml出现问题，跳过");
/*     */       } 
/*     */     }
/*     */     
/*  56 */     return i;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<Map<String, String>> getMyEmobileXML(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  65 */     SAXReader sAXReader = new SAXReader();
/*  66 */     Document document = null;
/*  67 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  68 */     File file = new File(XMLPATH);
/*  69 */     if (!file.exists()) {
/*  70 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*  71 */       hashMap.put("id", "");
/*  72 */       hashMap.put("name", "none");
/*  73 */       hashMap.put("description", "" + SystemEnv.getHtmlLabelName(10004211, ThreadVarLanguage.getLang()) + "XML" + SystemEnv.getHtmlLabelName(10004212, ThreadVarLanguage.getLang()) + "Emobile" + SystemEnv.getHtmlLabelName(10004213, ThreadVarLanguage.getLang()) + "");
/*  74 */       hashMap.put("isOK", "");
/*  75 */       arrayList.add(hashMap);
/*     */     } else {
/*     */       try {
/*  78 */         document = sAXReader.read(file);
/*  79 */         Element element1 = document.getRootElement();
/*  80 */         Element element2 = element1.element("components");
/*  81 */         List list = element2.elements();
/*  82 */         for (Element element : list) {
/*  83 */           String str1 = getElementText(element.element("name"));
/*  84 */           String str2 = getElementText(element.element("id"));
/*  85 */           String str3 = getElementText(element.element("description"));
/*  86 */           HashMap<Object, Object> hashMap = new HashMap<>();
/*  87 */           hashMap.put("id", str2);
/*  88 */           hashMap.put("name", str1);
/*  89 */           hashMap.put("description", str3);
/*  90 */           hashMap.put("isOK", "<font color='red'>" + SystemEnv.getHtmlLabelName(2245, ThreadVarLanguage.getLang()) + "</font>");
/*  91 */           arrayList.add(hashMap);
/*     */         } 
/*  93 */       } catch (Exception exception) {
/*  94 */         exception.printStackTrace();
/*     */       } 
/*     */     } 
/*  97 */     return (List)arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<Map<String, String>> getMyEmobileXML() {
/* 107 */     SAXReader sAXReader = new SAXReader();
/* 108 */     Document document = null;
/* 109 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 110 */     File file = new File(XMLPATH);
/* 111 */     if (!file.exists()) {
/* 112 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 113 */       hashMap.put("id", "");
/* 114 */       hashMap.put("name", "none");
/* 115 */       hashMap.put("description", "" + SystemEnv.getHtmlLabelName(10004211, ThreadVarLanguage.getLang()) + "XML" + SystemEnv.getHtmlLabelName(10004212, ThreadVarLanguage.getLang()) + "Emobile" + SystemEnv.getHtmlLabelName(10004213, ThreadVarLanguage.getLang()) + "");
/* 116 */       hashMap.put("isOK", "");
/* 117 */       arrayList.add(hashMap);
/*     */     } else {
/*     */       try {
/* 120 */         document = sAXReader.read(file);
/* 121 */         Element element1 = document.getRootElement();
/* 122 */         Element element2 = element1.element("components");
/* 123 */         List list = element2.elements();
/* 124 */         for (Element element : list) {
/* 125 */           String str1 = getElementText(element.element("name"));
/* 126 */           String str2 = getElementText(element.element("id"));
/* 127 */           String str3 = getElementText(element.element("description"));
/* 128 */           HashMap<Object, Object> hashMap = new HashMap<>();
/* 129 */           hashMap.put("id", str2);
/* 130 */           hashMap.put("name", str1);
/* 131 */           hashMap.put("description", str3);
/* 132 */           hashMap.put("isOK", "<font color='red'>" + SystemEnv.getHtmlLabelName(2245, ThreadVarLanguage.getLang()) + "</font>");
/* 133 */           arrayList.add(hashMap);
/*     */         } 
/* 135 */       } catch (Exception exception) {
/* 136 */         exception.printStackTrace();
/*     */       } 
/*     */     } 
/* 139 */     return (List)arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int getMyEmobileXMLNumUseRegex() {
/* 148 */     int i = 0;
/* 149 */     List<Map<String, String>> list = getPluginXmlInfo();
/* 150 */     if (list.size() > 0) {
/* 151 */       if (list.size() == 1) {
/* 152 */         Map map = list.get(0);
/* 153 */         if (!Util.null2String((String)map.get("id")).equals(""))
/*     */         {
/* 155 */           i = 1;
/*     */         }
/*     */       } else {
/* 158 */         i = list.size();
/*     */       } 
/*     */     }
/* 161 */     return i;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<Map<String, String>> getPluginXmlInfo(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 168 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 169 */     FileUtil fileUtil = new FileUtil();
/*     */     
/* 171 */     File file = new File(XMLPATH);
/* 172 */     if (file.exists()) {
/*     */ 
/*     */       
/* 175 */       StringBuffer stringBuffer = fileUtil.readFile(file);
/* 176 */       String str = stringBuffer.toString();
/* 177 */       str = str.replace("\n", "");
/*     */       
/* 179 */       List<String> list = findStrByRegex(str, "(<component>[\\s\\S]*?</component>)");
/* 180 */       for (byte b = 0; b < list.size(); b++) {
/* 181 */         HashMap<Object, Object> hashMap = new HashMap<>();
/* 182 */         String str1 = Util.null2String(list.get(b));
/* 183 */         String str2 = findStrValueByRegex(str1, "(<id>)(.*?)(</id>)");
/* 184 */         String str3 = findStrValueByRegex(str1, "(<name>)(.*?)(</name>)");
/* 185 */         String str4 = findStrValueByRegex(str1, "(<description>)(.*?)(</description>)");
/* 186 */         hashMap.put("id", str2);
/* 187 */         hashMap.put("name", str3);
/* 188 */         hashMap.put("description", str3);
/* 189 */         hashMap.put("isOK", "<font color='red'>" + SystemEnv.getHtmlLabelName(2245, ThreadVarLanguage.getLang()) + "</font>");
/* 190 */         arrayList.add(hashMap);
/*     */       } 
/*     */     } 
/* 193 */     return (List)arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<Map<String, String>> getPluginXmlInfo() {
/* 200 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 201 */     FileUtil fileUtil = new FileUtil();
/*     */     
/* 203 */     File file = new File(XMLPATH);
/* 204 */     if (file.exists()) {
/*     */ 
/*     */       
/* 207 */       StringBuffer stringBuffer = fileUtil.readFile(file);
/* 208 */       String str = stringBuffer.toString();
/* 209 */       str = str.replace("\n", "");
/*     */       
/* 211 */       List<String> list = findStrByRegex(str, "(<component>[\\s\\S]*?</component>)");
/* 212 */       for (byte b = 0; b < list.size(); b++) {
/* 213 */         HashMap<Object, Object> hashMap = new HashMap<>();
/* 214 */         String str1 = Util.null2String(list.get(b));
/* 215 */         String str2 = findStrValueByRegex(str1, "(<id>)(.*?)(</id>)");
/* 216 */         String str3 = findStrValueByRegex(str1, "(<name>)(.*?)(</name>)");
/* 217 */         String str4 = findStrValueByRegex(str1, "(<description>)(.*?)(</description>)");
/* 218 */         hashMap.put("id", str2);
/* 219 */         hashMap.put("name", str3);
/* 220 */         hashMap.put("description", str3);
/* 221 */         hashMap.put("isOK", "<font color='red'>" + SystemEnv.getHtmlLabelName(2245, ThreadVarLanguage.getLang()) + "</font>");
/* 222 */         arrayList.add(hashMap);
/*     */       } 
/*     */     } else {
/* 225 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 226 */       hashMap.put("id", "");
/* 227 */       hashMap.put("name", "none");
/* 228 */       hashMap.put("description", "" + SystemEnv.getHtmlLabelName(10004211, ThreadVarLanguage.getLang()) + "XML" + SystemEnv.getHtmlLabelName(10004212, ThreadVarLanguage.getLang()) + "Emobile" + SystemEnv.getHtmlLabelName(10004213, ThreadVarLanguage.getLang()) + "");
/* 229 */       hashMap.put("isOK", "");
/* 230 */       arrayList.add(hashMap);
/*     */     } 
/* 232 */     return (List)arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<String> findStrByRegex(String paramString1, String paramString2) {
/* 242 */     ArrayList<String> arrayList = new ArrayList();
/*     */ 
/*     */     
/*     */     try {
/* 246 */       Pattern pattern = Pattern.compile(paramString2);
/* 247 */       Matcher matcher = pattern.matcher(paramString1);
/* 248 */       while (matcher.find()) {
/* 249 */         arrayList.add(matcher.group());
/*     */       }
/* 251 */     } catch (Exception exception) {
/* 252 */       (new BaseBean()).writeLog("匹配出现错误：" + exception.toString());
/* 253 */       exception.printStackTrace();
/*     */     } 
/* 255 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String findStrValueByRegex(String paramString1, String paramString2) {
/* 265 */     ArrayList<String> arrayList = new ArrayList();
/*     */ 
/*     */     
/*     */     try {
/* 269 */       Pattern pattern = Pattern.compile(paramString2);
/* 270 */       Matcher matcher = pattern.matcher(paramString1);
/* 271 */       while (matcher.find()) {
/* 272 */         arrayList.add(matcher.group(2));
/*     */       }
/* 274 */     } catch (Exception exception) {
/* 275 */       exception.printStackTrace();
/*     */     } 
/* 277 */     if (arrayList.size() > 0) {
/* 278 */       return arrayList.get(0);
/*     */     }
/* 280 */     return "";
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/filecheck/MyEmobileReportOperation.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */