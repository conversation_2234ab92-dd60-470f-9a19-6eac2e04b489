/*      */ package weaver.templetecheck.filecheck;
/*      */ import com.alibaba.fastjson.JSONObject;
/*      */ import com.api.formmode.cache.CustomSearchComInfo;
/*      */ import com.api.formmode.cache.ModeBrowserComInfo;
/*      */ import com.api.formmode.cache.ModeExpandPageComInfo;
/*      */ import java.io.BufferedWriter;
/*      */ import java.io.File;
/*      */ import java.io.IOException;
/*      */ import java.io.OutputStreamWriter;
/*      */ import java.net.URLEncoder;
/*      */ import java.text.SimpleDateFormat;
/*      */ import java.util.ArrayList;
/*      */ import java.util.Arrays;
/*      */ import java.util.Date;
/*      */ import java.util.HashMap;
/*      */ import java.util.HashSet;
/*      */ import java.util.Iterator;
/*      */ import java.util.List;
/*      */ import java.util.ListIterator;
/*      */ import java.util.regex.Matcher;
/*      */ import java.util.regex.Pattern;
/*      */ import weaver.conn.ConnStatement;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.general.BaseBean;
/*      */ import weaver.general.GCONST;
/*      */ import weaver.general.Util;
/*      */ import weaver.templetecheck.transmethod.RuleTrans;
/*      */ 
/*      */ public class ReplaceOperation {
/*   30 */   FileUtil fileUtil = new FileUtil();
/*   31 */   public static String target = GCONST.getRootPath() + "config_ubak\\";
/*   32 */   public static SimpleDateFormat yyyyMMdd = new SimpleDateFormat("yyyyMMdd");
/*   33 */   public static SimpleDateFormat yyyyMMdd_sql = new SimpleDateFormat("yyyy-MM-dd");
/*   34 */   public static SimpleDateFormat HHmmss = new SimpleDateFormat("HHmmss");
/*   35 */   public static SimpleDateFormat HHmmss_sql = new SimpleDateFormat("HH:mm:ss");
/*   36 */   public String bakRootPath = "";
/*   37 */   ExcelSecurity excelSecurity = new ExcelSecurity();
/*   38 */   MobileModeReportOperation mmro = new MobileModeReportOperation();
/*   39 */   Map<String, Map<String, String>> mobileComponentMap = this.mmro.getMobileComponentMap();
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean replaceSpecialFile(String paramString) {
/*   46 */     String str = "upgradecheckresult";
/*   47 */     this.bakRootPath = getBakRootPath();
/*   48 */     RecordSet recordSet = new RecordSet();
/*   49 */     boolean bool = true;
/*   50 */     if ("".equals(formatIds(paramString))) {
/*   51 */       recordSet.execute("select * from " + str);
/*      */     } else {
/*   53 */       recordSet.execute("select * from " + str + " where id in(" + paramString + ")");
/*      */     } 
/*      */     
/*   56 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*   57 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*   58 */     HashMap<Object, Object> hashMap3 = new HashMap<>();
/*   59 */     HashMap<Object, Object> hashMap4 = new HashMap<>();
/*      */     
/*   61 */     HashMap<Object, Object> hashMap5 = new HashMap<>();
/*      */ 
/*      */     
/*   64 */     while (recordSet.next()) {
/*   65 */       String str1 = recordSet.getString("ruletype");
/*   66 */       String str2 = recordSet.getString("filepath");
/*   67 */       String str3 = recordSet.getString("content");
/*   68 */       String str4 = recordSet.getString("replacecontent");
/*   69 */       String str5 = recordSet.getString("modetype");
/*   70 */       String str6 = recordSet.getString("id");
/*   71 */       if (str4.indexOf("无法自动替换") > -1) {
/*   72 */         (new BaseBean()).writeLog("改规则无法自动替换,id=" + str6);
/*      */         
/*      */         continue;
/*      */       } 
/*   76 */       if ("1".equals(str1)) {
/*   77 */         Object object = null;
/*      */         
/*   79 */         if (hashMap1.containsKey(str2)) {
/*   80 */           ArrayList arrayList = (ArrayList)hashMap1.get(str2);
/*   81 */           arrayList.add(object);
/*      */         } else {
/*   83 */           ArrayList arrayList = new ArrayList();
/*   84 */           arrayList.add(object);
/*   85 */           hashMap1.put(str2, arrayList);
/*      */         } 
/*      */         
/*   88 */         if (hashMap2.containsKey(str2)) {
/*   89 */           ArrayList<String> arrayList = (ArrayList)hashMap2.get(str2);
/*   90 */           arrayList.add(str3);
/*      */         } else {
/*   92 */           ArrayList<String> arrayList = new ArrayList();
/*   93 */           arrayList.add(str3);
/*   94 */           hashMap2.put(str2, arrayList);
/*      */         } 
/*      */         
/*   97 */         if (hashMap4.containsKey(str2)) {
/*   98 */           ArrayList<String> arrayList = (ArrayList)hashMap4.get(str2);
/*   99 */           arrayList.add(str6);
/*      */         } else {
/*  101 */           ArrayList<String> arrayList = new ArrayList();
/*  102 */           arrayList.add(str6);
/*  103 */           hashMap4.put(str2, arrayList);
/*      */         } 
/*      */         
/*  106 */         if (hashMap3.containsKey(str2)) {
/*  107 */           ArrayList<String> arrayList = (ArrayList)hashMap3.get(str2);
/*  108 */           arrayList.add(str1);
/*      */         } else {
/*  110 */           ArrayList<String> arrayList = new ArrayList();
/*  111 */           arrayList.add(str1);
/*  112 */           hashMap3.put(str2, arrayList);
/*      */         } 
/*      */ 
/*      */         
/*  116 */         if (!hashMap5.containsKey(str2)) {
/*  117 */           hashMap5.put(str2, str2);
/*      */         }
/*      */         continue;
/*      */       } 
/*  121 */       Pattern pattern = null;
/*  122 */       pattern = Pattern.compile(str3);
/*      */       
/*  124 */       if (hashMap1.containsKey(str2)) {
/*  125 */         ArrayList<Pattern> arrayList = (ArrayList)hashMap1.get(str2);
/*  126 */         arrayList.add(pattern);
/*      */       } else {
/*  128 */         ArrayList<Pattern> arrayList = new ArrayList();
/*  129 */         arrayList.add(pattern);
/*  130 */         hashMap1.put(str2, arrayList);
/*      */       } 
/*      */       
/*  133 */       if (hashMap2.containsKey(str2)) {
/*  134 */         ArrayList<String> arrayList = (ArrayList)hashMap2.get(str2);
/*  135 */         arrayList.add(str4);
/*      */       } else {
/*  137 */         ArrayList<String> arrayList = new ArrayList();
/*  138 */         arrayList.add(str4);
/*  139 */         hashMap2.put(str2, arrayList);
/*      */       } 
/*      */ 
/*      */       
/*  143 */       if (hashMap4.containsKey(str2)) {
/*  144 */         ArrayList<String> arrayList = (ArrayList)hashMap4.get(str2);
/*  145 */         arrayList.add(str6);
/*      */       } else {
/*  147 */         ArrayList<String> arrayList = new ArrayList();
/*  148 */         arrayList.add(str6);
/*  149 */         hashMap4.put(str2, arrayList);
/*      */       } 
/*      */       
/*  152 */       if (hashMap3.containsKey(str2)) {
/*  153 */         ArrayList<String> arrayList = (ArrayList)hashMap3.get(str2);
/*  154 */         arrayList.add(str1);
/*      */       } else {
/*  156 */         ArrayList<String> arrayList = new ArrayList();
/*  157 */         arrayList.add(str1);
/*  158 */         hashMap3.put(str2, arrayList);
/*      */       } 
/*      */       
/*  161 */       if (!hashMap5.containsKey(str2)) {
/*  162 */         hashMap5.put(str2, str2);
/*      */       }
/*      */     } 
/*      */ 
/*      */ 
/*      */     
/*  168 */     Iterator<String> iterator = hashMap2.keySet().iterator();
/*  169 */     File file = new File("");
/*  170 */     while (iterator.hasNext()) {
/*  171 */       String str1 = iterator.next();
/*  172 */       file = new File((String)hashMap5.get(str1));
/*  173 */       ArrayList<Pattern> arrayList = (ArrayList)hashMap1.get(str1);
/*  174 */       ArrayList<String> arrayList1 = (ArrayList)hashMap2.get(str1);
/*  175 */       ArrayList<String> arrayList2 = (ArrayList)hashMap3.get(str1);
/*  176 */       ArrayList<String> arrayList3 = (ArrayList)hashMap4.get(str1);
/*      */       
/*  178 */       replaceFile(file, arrayList, arrayList1, arrayList2, arrayList3, str);
/*      */     } 
/*      */     
/*  181 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean replaceWorkflow(String paramString) {
/*  188 */     return replaceWorkflow(paramString, null);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean replaceWorkflow(String paramString1, String paramString2) {
/*  195 */     String str1 = "upgradecheckworkflowresult";
/*  196 */     this.bakRootPath = getBakRootPath();
/*  197 */     RecordSet recordSet = new RecordSet();
/*  198 */     boolean bool = true;
/*  199 */     String str2 = " where 1=1 ";
/*  200 */     if (!"".equals(formatIds(paramString2))) {
/*  201 */       str2 = str2 + " and ruleid in(" + formatIds(paramString2) + ") ";
/*      */     }
/*  203 */     if (!"".equals(formatIds(paramString1))) {
/*  204 */       str2 = str2 + " and id in(" + formatIds(paramString1) + ")";
/*      */     }
/*  206 */     recordSet.execute("select * from " + str1 + str2 + " order by id desc ");
/*  207 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  208 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  209 */     HashMap<Object, Object> hashMap3 = new HashMap<>();
/*  210 */     HashMap<Object, Object> hashMap4 = new HashMap<>();
/*      */     
/*  212 */     HashMap<Object, Object> hashMap5 = new HashMap<>();
/*      */     
/*  214 */     HashMap<Object, Object> hashMap6 = new HashMap<>();
/*  215 */     HashMap<Object, Object> hashMap7 = new HashMap<>();
/*  216 */     HashMap<Object, Object> hashMap8 = new HashMap<>();
/*  217 */     HashMap<Object, Object> hashMap9 = new HashMap<>();
/*      */     
/*  219 */     while (recordSet.next()) {
/*  220 */       String str3 = recordSet.getString("ruletype");
/*  221 */       String str4 = recordSet.getString("filepath");
/*  222 */       String str5 = recordSet.getString("content");
/*  223 */       String str6 = recordSet.getString("replacecontent");
/*  224 */       String str7 = recordSet.getString("modetype");
/*  225 */       String str8 = recordSet.getString("nodehtmllayoutid");
/*  226 */       String str9 = recordSet.getString("id");
/*  227 */       if (str6.indexOf("无法自动替换") > -1) {
/*  228 */         (new BaseBean()).writeLog("该规则无法自动替换,id=" + str9);
/*      */         
/*      */         continue;
/*      */       } 
/*  232 */       if ("1".equals(str3)) {
/*  233 */         if ("0".equals(str7)) {
/*  234 */           Object object1 = null;
/*  235 */           if (hashMap1.containsKey(str8)) {
/*  236 */             ArrayList arrayList2 = (ArrayList)hashMap1.get(str8);
/*  237 */             arrayList2.add(object1);
/*      */           } else {
/*  239 */             ArrayList arrayList2 = new ArrayList();
/*  240 */             arrayList2.add(object1);
/*  241 */             hashMap1.put(str8, arrayList2);
/*      */           } 
/*      */           
/*  244 */           if (hashMap2.containsKey(str8)) {
/*  245 */             ArrayList<String> arrayList2 = (ArrayList)hashMap2.get(str8);
/*  246 */             arrayList2.add(str5);
/*      */           } else {
/*  248 */             ArrayList<String> arrayList2 = new ArrayList();
/*  249 */             arrayList2.add(str5);
/*  250 */             hashMap2.put(str8, arrayList2);
/*      */           } 
/*      */           
/*  253 */           if (hashMap4.containsKey(str8)) {
/*  254 */             ArrayList<String> arrayList2 = (ArrayList)hashMap4.get(str8);
/*  255 */             arrayList2.add(str9);
/*      */           } else {
/*  257 */             ArrayList<String> arrayList2 = new ArrayList();
/*  258 */             arrayList2.add(str9);
/*  259 */             hashMap4.put(str8, arrayList2);
/*      */           } 
/*      */           
/*  262 */           if (hashMap3.containsKey(str8)) {
/*  263 */             ArrayList<String> arrayList2 = (ArrayList)hashMap3.get(str8);
/*  264 */             arrayList2.add(str3);
/*      */           } else {
/*  266 */             ArrayList<String> arrayList2 = new ArrayList();
/*  267 */             arrayList2.add(str3);
/*  268 */             hashMap3.put(str8, arrayList2);
/*      */           } 
/*      */ 
/*      */           
/*  272 */           if (!hashMap5.containsKey(str8)) {
/*  273 */             hashMap5.put(str8, str4);
/*      */           }
/*      */           continue;
/*      */         } 
/*  277 */         Object object = null;
/*  278 */         if (hashMap6.containsKey(str8)) {
/*  279 */           ArrayList arrayList2 = (ArrayList)hashMap6.get(str8);
/*  280 */           arrayList2.add(object);
/*      */         } else {
/*  282 */           ArrayList arrayList2 = new ArrayList();
/*  283 */           arrayList2.add(object);
/*  284 */           hashMap6.put(str8, arrayList2);
/*      */         } 
/*      */         
/*  287 */         if (hashMap7.containsKey(str8)) {
/*      */           
/*  289 */           ArrayList<String> arrayList2 = (ArrayList)hashMap7.get(str8);
/*  290 */           arrayList2.add(str5);
/*      */         } else {
/*  292 */           ArrayList<String> arrayList2 = new ArrayList();
/*  293 */           arrayList2.add(str5);
/*  294 */           hashMap7.put(str8, arrayList2);
/*      */         } 
/*      */ 
/*      */         
/*  298 */         if (hashMap9.containsKey(str8)) {
/*  299 */           ArrayList<String> arrayList2 = (ArrayList)hashMap9.get(str8);
/*  300 */           arrayList2.add(str9);
/*      */         } else {
/*  302 */           ArrayList<String> arrayList2 = new ArrayList();
/*  303 */           arrayList2.add(str9);
/*  304 */           hashMap9.put(str8, arrayList2);
/*      */         } 
/*      */ 
/*      */         
/*  308 */         if (hashMap8.containsKey(str8)) {
/*  309 */           ArrayList<String> arrayList2 = (ArrayList)hashMap8.get(str8);
/*  310 */           arrayList2.add(str3); continue;
/*      */         } 
/*  312 */         ArrayList<String> arrayList1 = new ArrayList();
/*  313 */         arrayList1.add(str3);
/*  314 */         hashMap8.put(str8, arrayList1);
/*      */ 
/*      */         
/*      */         continue;
/*      */       } 
/*      */ 
/*      */       
/*  321 */       Pattern pattern = null;
/*  322 */       pattern = Pattern.compile(str5);
/*  323 */       if ("0".equals(str7)) {
/*      */         
/*  325 */         if (hashMap1.containsKey(str8)) {
/*  326 */           ArrayList<Pattern> arrayList1 = (ArrayList)hashMap1.get(str8);
/*  327 */           arrayList1.add(pattern);
/*      */         } else {
/*  329 */           ArrayList<Pattern> arrayList1 = new ArrayList();
/*  330 */           arrayList1.add(pattern);
/*  331 */           hashMap1.put(str8, arrayList1);
/*      */         } 
/*      */         
/*  334 */         if (hashMap2.containsKey(str8)) {
/*  335 */           ArrayList<String> arrayList1 = (ArrayList)hashMap2.get(str8);
/*  336 */           arrayList1.add(str6);
/*      */         } else {
/*  338 */           ArrayList<String> arrayList1 = new ArrayList();
/*  339 */           arrayList1.add(str6);
/*  340 */           hashMap2.put(str8, arrayList1);
/*      */         } 
/*      */ 
/*      */         
/*  344 */         if (hashMap4.containsKey(str8)) {
/*  345 */           ArrayList<String> arrayList1 = (ArrayList)hashMap4.get(str8);
/*  346 */           arrayList1.add(str9);
/*      */         } else {
/*  348 */           ArrayList<String> arrayList1 = new ArrayList();
/*  349 */           arrayList1.add(str9);
/*  350 */           hashMap4.put(str8, arrayList1);
/*      */         } 
/*      */         
/*  353 */         if (hashMap3.containsKey(str8)) {
/*  354 */           ArrayList<String> arrayList1 = (ArrayList)hashMap3.get(str8);
/*  355 */           arrayList1.add(str3);
/*      */         } else {
/*  357 */           ArrayList<String> arrayList1 = new ArrayList();
/*  358 */           arrayList1.add(str3);
/*  359 */           hashMap3.put(str8, arrayList1);
/*      */         } 
/*      */         
/*  362 */         if (!hashMap5.containsKey(str8)) {
/*  363 */           hashMap5.put(str8, str4);
/*      */         }
/*      */         
/*      */         continue;
/*      */       } 
/*  368 */       if (hashMap6.containsKey(str8)) {
/*  369 */         ArrayList<Pattern> arrayList1 = (ArrayList)hashMap6.get(str8);
/*  370 */         arrayList1.add(pattern);
/*      */       } else {
/*  372 */         ArrayList<Pattern> arrayList1 = new ArrayList();
/*  373 */         arrayList1.add(pattern);
/*  374 */         hashMap6.put(str8, arrayList1);
/*      */       } 
/*      */       
/*  377 */       if (hashMap7.containsKey(str8)) {
/*  378 */         ArrayList<String> arrayList1 = (ArrayList)hashMap7.get(str8);
/*  379 */         arrayList1.add(str6);
/*      */       } else {
/*  381 */         ArrayList<String> arrayList1 = new ArrayList();
/*  382 */         arrayList1.add(str6);
/*  383 */         hashMap7.put(str8, arrayList1);
/*      */       } 
/*      */       
/*  386 */       if (hashMap9.containsKey(str8)) {
/*  387 */         ArrayList<String> arrayList1 = (ArrayList)hashMap9.get(str8);
/*  388 */         arrayList1.add(str9);
/*      */       } else {
/*  390 */         ArrayList<String> arrayList1 = new ArrayList();
/*  391 */         arrayList1.add(str9);
/*  392 */         hashMap9.put(str8, arrayList1);
/*      */       } 
/*      */ 
/*      */       
/*  396 */       if (hashMap8.containsKey(str8)) {
/*  397 */         ArrayList<String> arrayList1 = (ArrayList)hashMap8.get(str8);
/*  398 */         arrayList1.add(str3); continue;
/*      */       } 
/*  400 */       ArrayList<String> arrayList = new ArrayList();
/*  401 */       arrayList.add(str3);
/*  402 */       hashMap8.put(str8, arrayList);
/*      */     } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  410 */     Iterator<String> iterator = hashMap2.keySet().iterator();
/*  411 */     File file = new File("");
/*  412 */     while (iterator.hasNext()) {
/*  413 */       String str = iterator.next();
/*  414 */       file = new File((String)hashMap5.get(str));
/*  415 */       ArrayList<Pattern> arrayList = (ArrayList)hashMap1.get(str);
/*  416 */       ArrayList<String> arrayList1 = (ArrayList)hashMap2.get(str);
/*  417 */       ArrayList<String> arrayList2 = (ArrayList)hashMap3.get(str);
/*  418 */       ArrayList<String> arrayList3 = (ArrayList)hashMap4.get(str);
/*      */       
/*  420 */       replaceFile(file, arrayList, arrayList1, arrayList2, arrayList3, str1);
/*      */     } 
/*      */ 
/*      */     
/*  424 */     if (hashMap7.size() > 0) {
/*  425 */       Date date = new Date();
/*  426 */       String str3 = yyyyMMdd.format(date);
/*  427 */       String str4 = yyyyMMdd_sql.format(date);
/*  428 */       String str5 = HHmmss.format(date);
/*  429 */       String str6 = HHmmss_sql.format(date);
/*  430 */       String str7 = "workflow_nodehtmllayout";
/*  431 */       String str8 = "TEMPCHECK_" + str3 + str5;
/*  432 */       recordSet.execute("INSERT INTO TEMPLETECHECKBAKLOG(SOURCENAME,TARGETNAME,CREATEDATE,CREATETIME) values('" + str7 + "','" + str8 + "','" + str4 + "','" + str6 + "')");
/*  433 */       String str9 = recordSet.getDBType();
/*  434 */       if ("sqlserver".equalsIgnoreCase(str9)) {
/*  435 */         recordSet.execute(" SELECT * into " + str8 + " FROM " + str7);
/*      */       } else {
/*  437 */         recordSet.execute("CREATE TABLE " + str8 + " as SELECT * FROM " + str7);
/*      */       } 
/*      */     } 
/*      */     
/*  441 */     iterator = hashMap7.keySet().iterator();
/*  442 */     while (iterator.hasNext()) {
/*  443 */       String str = iterator.next();
/*  444 */       ArrayList<Pattern> arrayList = (ArrayList)hashMap6.get(str);
/*  445 */       ArrayList<String> arrayList1 = (ArrayList)hashMap7.get(str);
/*  446 */       ArrayList<String> arrayList2 = (ArrayList)hashMap8.get(str);
/*  447 */       ArrayList<String> arrayList3 = (ArrayList)hashMap9.get(str);
/*      */       
/*  449 */       replaceColumn(str, "workflow_nodehtmllayout", "scripts", arrayList, arrayList1, arrayList2, arrayList3, str1);
/*      */     } 
/*  451 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean replaceModeOrMobilemode(String paramString1, String paramString2) {
/*  461 */     this.bakRootPath = getBakRootPath();
/*  462 */     RecordSet recordSet = new RecordSet();
/*      */ 
/*      */     
/*  465 */     HashSet<String> hashSet1 = new HashSet();
/*  466 */     HashSet<String> hashSet2 = new HashSet();
/*  467 */     HashSet<String> hashSet3 = new HashSet();
/*  468 */     CustomSearchComInfo customSearchComInfo = new CustomSearchComInfo();
/*  469 */     ModeExpandPageComInfo modeExpandPageComInfo = new ModeExpandPageComInfo();
/*  470 */     ModeBrowserComInfo modeBrowserComInfo = new ModeBrowserComInfo();
/*      */     
/*  472 */     String str = "";
/*  473 */     if ("checkmobilemoderesult".equals(paramString2.toLowerCase())) {
/*  474 */       str = "CHECKMOBILEMODERULE";
/*  475 */     } else if ("checkmobilemoderesult".equals(paramString2.toLowerCase())) {
/*  476 */       str = "UPGRADECHECKMODERULE";
/*      */     } 
/*  478 */     boolean bool = true;
/*  479 */     if ("".equals(formatIds(paramString1))) {
/*  480 */       recordSet.execute("select * from " + paramString2);
/*      */     } else {
/*  482 */       recordSet.execute("select * from " + paramString2 + " where id in(" + paramString1 + ")");
/*      */     } 
/*      */     
/*  485 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  486 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  487 */     HashMap<Object, Object> hashMap3 = new HashMap<>();
/*  488 */     HashMap<Object, Object> hashMap4 = new HashMap<>();
/*      */     
/*  490 */     HashMap<Object, Object> hashMap5 = new HashMap<>();
/*      */     
/*  492 */     HashMap<Object, Object> hashMap6 = new HashMap<>();
/*  493 */     HashMap<Object, Object> hashMap7 = new HashMap<>();
/*  494 */     HashMap<Object, Object> hashMap8 = new HashMap<>();
/*  495 */     HashMap<Object, Object> hashMap9 = new HashMap<>();
/*      */     
/*  497 */     while (recordSet.next()) {
/*  498 */       String str1 = recordSet.getString("ruletype");
/*  499 */       String str2 = recordSet.getString("filepath");
/*  500 */       String str3 = recordSet.getString("content");
/*  501 */       String str4 = recordSet.getString("replacecontent");
/*  502 */       String str5 = recordSet.getString("modetype");
/*  503 */       String str6 = Util.null2String(recordSet.getString("nodehtmllayoutid"));
/*  504 */       String str7 = recordSet.getString("id");
/*  505 */       String str8 = Util.null2String(recordSet.getString("detailtype"));
/*  506 */       String str9 = Util.null2String(recordSet.getString("refertable"));
/*      */       
/*  508 */       if ("".equals(str8) || "".equals(str6) || str4.indexOf("无法自动替换") > -1) {
/*  509 */         (new BaseBean()).writeLog("改规则无法自动替换,id=" + str7);
/*      */         
/*      */         continue;
/*      */       } 
/*      */       
/*  514 */       if ("mode_customsearch".equalsIgnoreCase(str9.toLowerCase())) {
/*  515 */         hashSet1.add(str6);
/*  516 */       } else if ("mode_pageexpand".equalsIgnoreCase(str9.toLowerCase())) {
/*  517 */         hashSet2.add(str6);
/*  518 */       } else if ("mode_customBrowser".equalsIgnoreCase(str9.toLowerCase())) {
/*  519 */         hashSet3.add(str6);
/*      */       } 
/*      */       
/*  522 */       String str10 = str6 + "==" + str8;
/*  523 */       if ("1".equals(str1)) {
/*  524 */         if ("0".equals(str5)) {
/*  525 */           Pattern pattern2 = Pattern.compile("");
/*      */           
/*  527 */           addItemToMap((HashMap)hashMap1, str10, pattern2);
/*      */           
/*  529 */           addItemToMap((HashMap)hashMap2, str10, str3);
/*      */           
/*  531 */           addItemToMap((HashMap)hashMap4, str10, str7);
/*      */           
/*  533 */           addItemToMap((HashMap)hashMap3, str10, str1);
/*      */           
/*  535 */           if (!hashMap5.containsKey(str10)) {
/*  536 */             hashMap5.put(str10, str2);
/*      */           }
/*      */           continue;
/*      */         } 
/*  540 */         Pattern pattern1 = Pattern.compile("");
/*      */         
/*  542 */         addItemToMap((HashMap)hashMap6, str10, pattern1);
/*      */         
/*  544 */         addItemToMap((HashMap)hashMap7, str10, str3);
/*      */         
/*  546 */         addItemToMap((HashMap)hashMap9, str10, str7);
/*      */         
/*  548 */         addItemToMap((HashMap)hashMap8, str10, str1);
/*      */         
/*      */         continue;
/*      */       } 
/*  552 */       Pattern pattern = Pattern.compile(str3);
/*  553 */       if ("0".equals(str5)) {
/*      */         
/*  555 */         addItemToMap((HashMap)hashMap1, str10, pattern);
/*      */         
/*  557 */         addItemToMap((HashMap)hashMap2, str10, str4);
/*      */         
/*  559 */         addItemToMap((HashMap)hashMap4, str10, str7);
/*      */         
/*  561 */         addItemToMap((HashMap)hashMap3, str10, str1);
/*      */         
/*  563 */         if (!hashMap5.containsKey(str10)) {
/*  564 */           hashMap5.put(str10, str2);
/*      */         }
/*      */         continue;
/*      */       } 
/*  568 */       addItemToMap((HashMap)hashMap6, str10, pattern);
/*      */       
/*  570 */       addItemToMap((HashMap)hashMap7, str10, str4);
/*      */       
/*  572 */       addItemToMap((HashMap)hashMap9, str10, str7);
/*      */       
/*  574 */       addItemToMap((HashMap)hashMap8, str10, str1);
/*      */     } 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  580 */     Iterator<String> iterator = hashMap2.keySet().iterator();
/*  581 */     File file = new File("");
/*  582 */     while (iterator.hasNext()) {
/*  583 */       String str1 = iterator.next();
/*  584 */       file = new File((String)hashMap5.get(str1));
/*  585 */       ArrayList<Pattern> arrayList = (ArrayList)hashMap1.get(str1);
/*  586 */       ArrayList<String> arrayList1 = (ArrayList)hashMap2.get(str1);
/*  587 */       ArrayList<String> arrayList2 = (ArrayList)hashMap3.get(str1);
/*  588 */       ArrayList<String> arrayList3 = (ArrayList)hashMap4.get(str1);
/*      */       
/*  590 */       replaceFile(file, arrayList, arrayList1, arrayList2, arrayList3, paramString2);
/*      */     } 
/*      */ 
/*      */     
/*  594 */     if (hashMap7.size() > 0) {
/*  595 */       RecordSet recordSet1 = new RecordSet();
/*  596 */       HashSet<String> hashSet = new HashSet();
/*  597 */       for (String str1 : hashMap7.keySet()) {
/*  598 */         String str2 = "";
/*  599 */         String str3 = str1.split("==")[1];
/*  600 */         if ("html".equalsIgnoreCase(str3) || "excel".equalsIgnoreCase(str3)) {
/*  601 */           str2 = "modehtmllayout";
/*  602 */         } else if ("expand".equalsIgnoreCase(str3)) {
/*  603 */           str2 = "mode_pageexpand";
/*  604 */         } else if ("customSearchForSql".equalsIgnoreCase(str3) || "customSearchForJava".equalsIgnoreCase(str3)) {
/*  605 */           str2 = "mode_customsearch";
/*  606 */         } else if ("customSearchButton".equalsIgnoreCase(str3)) {
/*  607 */           str2 = "mode_customSearchButton";
/*  608 */         } else if ("customBrowserForSql".equalsIgnoreCase(str3) || "customBrowserForJava".equalsIgnoreCase(str3)) {
/*  609 */           str2 = "mode_custombrowser";
/*  610 */         } else if ("mobilemodepage".equalsIgnoreCase(str3) || "mobilemodepageattr".equalsIgnoreCase(str3)) {
/*  611 */           str2 = "apphomepage";
/*  612 */         } else if ("mobileexpendcomponent_html".equalsIgnoreCase(str3) || "mobileexpendcomponent_sql".equalsIgnoreCase(str3)) {
/*  613 */           str2 = "MOBILEEXTENDCOMPONENT";
/*      */         } 
/*  615 */         hashSet.add(str2);
/*      */       } 
/*  617 */       backupTable(hashSet);
/*      */     } 
/*      */     
/*  620 */     iterator = hashMap7.keySet().iterator();
/*  621 */     while (iterator.hasNext()) {
/*  622 */       String str1 = "";
/*  623 */       String str2 = "";
/*  624 */       String str3 = iterator.next();
/*  625 */       ArrayList<Pattern> arrayList = (ArrayList)hashMap6.get(str3);
/*  626 */       ArrayList<String> arrayList1 = (ArrayList)hashMap7.get(str3);
/*  627 */       ArrayList<String> arrayList2 = (ArrayList)hashMap8.get(str3);
/*  628 */       ArrayList<String> arrayList3 = (ArrayList)hashMap9.get(str3);
/*      */       
/*  630 */       String[] arrayOfString = str3.split("==");
/*  631 */       if (arrayOfString.length != 2 || "".equals(arrayOfString[0]) || "".equals(arrayOfString[1])) {
/*      */         continue;
/*      */       }
/*  634 */       String str4 = arrayOfString[1];
/*  635 */       String str5 = arrayOfString[0];
/*  636 */       if ("html".equalsIgnoreCase(str4) || "excel".equalsIgnoreCase(str4)) {
/*  637 */         str1 = "modehtmllayout";
/*  638 */         str2 = "scriptstr";
/*  639 */       } else if ("expand".equalsIgnoreCase(str4)) {
/*  640 */         str1 = "mode_pageexpand";
/*  641 */         str2 = "hreftarget";
/*  642 */       } else if ("customSearchForSql".equalsIgnoreCase(str4) || "customSearchForJava".equalsIgnoreCase(str4)) {
/*  643 */         str1 = "mode_customsearch";
/*  644 */         str2 = "defaultsql";
/*  645 */       } else if ("customSearchButton".equalsIgnoreCase(str4)) {
/*  646 */         str1 = "mode_customSearchButton";
/*  647 */         str2 = "jsmethodbody";
/*  648 */       } else if ("customBrowserForSql".equalsIgnoreCase(str4)) {
/*  649 */         str1 = "mode_custombrowser";
/*  650 */         str2 = "defaultsql";
/*  651 */       } else if ("mobilemodepage".equalsIgnoreCase(str4)) {
/*  652 */         str1 = "apphomepage";
/*  653 */         str2 = "pagecontent";
/*  654 */       } else if ("mobilemodepageattr".equalsIgnoreCase(str4)) {
/*  655 */         str1 = "apphomepage";
/*  656 */         str2 = "pageattr";
/*  657 */       } else if ("mobileexpendcomponent_html".equalsIgnoreCase(str4) || "mobileexpendcomponent_sql".equalsIgnoreCase(str4)) {
/*  658 */         str1 = "MOBILEEXTENDCOMPONENT";
/*  659 */         str2 = "mecparam";
/*      */       } 
/*  661 */       if ("mecparam".equals(str2) && "MOBILEEXTENDCOMPONENT".equals(str1)) {
/*  662 */         replaceComponent1(str5, arrayList, arrayList1, arrayList2, arrayList3); continue;
/*      */       } 
/*  664 */       replaceColumn(str5, str1, str2, arrayList, arrayList1, arrayList2, arrayList3, paramString2);
/*      */     } 
/*      */ 
/*      */ 
/*      */     
/*  669 */     for (String str1 : hashSet1) {
/*  670 */       customSearchComInfo.updateCache("" + str1);
/*      */     }
/*  672 */     for (String str1 : hashSet2) {
/*  673 */       modeExpandPageComInfo.updateCache("" + str1);
/*      */     }
/*  675 */     for (String str1 : hashSet3) {
/*  676 */       modeBrowserComInfo.updateCache("" + str1);
/*      */     }
/*      */     
/*  679 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String replaceComponent(String paramString, ArrayList<Pattern> paramArrayList, ArrayList<String> paramArrayList1, ArrayList<String> paramArrayList2, ArrayList<String> paramArrayList3) {
/*  687 */     RecordSet recordSet1 = new RecordSet();
/*  688 */     RecordSet recordSet2 = new RecordSet();
/*  689 */     recordSet1.execute("select * from MOBILEEXTENDCOMPONENT  where mecparam is not null and id='" + paramString + "'");
/*  690 */     if (recordSet1.next()) {
/*  691 */       String str1 = recordSet1.getString("mecparam");
/*  692 */       String str2 = str1;
/*      */ 
/*      */       
/*  695 */       str2 = str2.replaceAll("\\\\r\\\\n", "\n");
/*  696 */       JSONObject jSONObject = JSONObject.parseObject(str2);
/*  697 */       str2 = jSONObject.toString();
/*  698 */       if (!isJsonObject(str2)) return null; 
/*  699 */       String str3 = Util.null2String(recordSet1.getString("mectype"));
/*  700 */       List<String> list = this.mmro.jsonToList(str2);
/*  701 */       if (!"".equals(str3)) {
/*  702 */         for (byte b = 0; b < paramArrayList3.size(); b++) {
/*  703 */           String str = "";
/*  704 */           recordSet2.execute("select * from checkmobilemoderesult where id='" + (String)paramArrayList3.get(b) + "'");
/*  705 */           if (recordSet2.next()) {
/*  706 */             String str4 = recordSet2.getString("referkey");
/*  707 */             String str5 = Util.null2String(recordSet2.getString("sourcecontent")).replaceAll("\\r\\n", "\n");
/*  708 */             ListIterator<String> listIterator = list.listIterator();
/*  709 */             String str6 = "";
/*  710 */             String str7 = recordSet2.getString("sameSourceContentFlag");
/*  711 */             boolean bool = false;
/*  712 */             while (listIterator.hasNext()) {
/*  713 */               String str8 = listIterator.next();
/*  714 */               if (str8.toLowerCase().indexOf(str4.toLowerCase() + "==>>==") == 0) {
/*  715 */                 str8 = str8.substring(str8.indexOf("==>>==") + 6);
/*  716 */                 String str9 = str8;
/*  717 */                 if ("FLBS".equalsIgnoreCase(str3) && "backscript".equalsIgnoreCase(str4)) {
/*      */                   try {
/*  719 */                     str9 = URLDecoder.decode(str8, "UTF-8");
/*  720 */                   } catch (Exception exception) {
/*  721 */                     exception.printStackTrace();
/*      */                   } 
/*      */                 }
/*  724 */                 if (!"".equals(str4) && !"".equals(str9) && (str5.trim().equals(str9.trim()) || str5.trim().equals(str9.replaceAll("\n", "").trim()))) {
/*  725 */                   str = str8;
/*      */                   
/*  727 */                   List<T> list1 = (paramArrayList == null) ? null : Arrays.<T>asList((T[])new Pattern[] { paramArrayList.get(b) });
/*  728 */                   str8 = doReplace(str8, (List)list1, Arrays.asList(new String[] { paramArrayList1.get(b) }, ), Arrays.asList(new String[] { paramArrayList2.get(b) }, ), Arrays.asList(new String[] { paramArrayList3.get(b) }, ), "checkmobilemoderesult");
/*  729 */                   if (str.equals(str8)) {
/*      */                     continue;
/*      */                   }
/*  732 */                   String str10 = str4.contains(".") ? str4.substring(str4.lastIndexOf(".") + 1) : str4;
/*  733 */                   str6 = str8;
/*  734 */                   String str11 = str8;
/*  735 */                   if ("FLBS".equalsIgnoreCase(str3) && "backscript".equalsIgnoreCase(str4)) {
/*      */                     try {
/*  737 */                       str8 = URLEncoder.encode(str8, "UTF-8");
/*  738 */                       str11 = URLEncoder.encode(str11, "UTF-8");
/*  739 */                     } catch (Exception exception) {
/*  740 */                       exception.printStackTrace();
/*      */                       continue;
/*      */                     } 
/*      */                   }
/*  744 */                   str = RuleTrans.changeStr(str);
/*  745 */                   str = RuleTrans.excludeRexChar(str);
/*  746 */                   str8 = RuleTrans.changeStr(str8);
/*  747 */                   str2 = str2.replaceFirst("\"" + str10 + "\":\"" + str + "\"", "\\\"" + str10 + "\\\":\\\"" + str8 + "\\\"");
/*  748 */                   listIterator.set(str4 + "==>>==" + str11);
/*  749 */                   bool = true;
/*      */                   break;
/*      */                 } 
/*      */               } 
/*      */             } 
/*  754 */             if (bool) {
/*      */ 
/*      */               
/*  757 */               JSONObject jSONObject1 = (JSONObject)JSONObject.toJSON(str2);
/*  758 */               String str8 = "update MOBILEEXTENDCOMPONENT set mecparam=? where id='" + paramString + "'";
/*  759 */               String str9 = " update checkmobilemoderesult set sourcecontent=? where sameSourceContentFlag='" + str7 + "'";
/*  760 */               ConnStatement connStatement = new ConnStatement();
/*      */             } 
/*      */           } 
/*      */         } 
/*      */       }
/*      */     } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  782 */     return null;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String replaceComponent1(String paramString, ArrayList<Pattern> paramArrayList, ArrayList<String> paramArrayList1, ArrayList<String> paramArrayList2, ArrayList<String> paramArrayList3) {
/*  789 */     RecordSet recordSet1 = new RecordSet();
/*  790 */     RecordSet recordSet2 = new RecordSet();
/*  791 */     recordSet1.execute("select * from MOBILEEXTENDCOMPONENT  where mecparam is not null and id='" + paramString + "'");
/*  792 */     if (recordSet1.next()) {
/*  793 */       String str1 = recordSet1.getString("mecparam");
/*  794 */       JSONObject jSONObject = JSONObject.parseObject(str1);
/*  795 */       str1 = jSONObject.toJSONString();
/*  796 */       String str2 = str1;
/*  797 */       if (!isJsonObject(str2)) return null;
/*      */       
/*  799 */       String str3 = Util.null2String(recordSet1.getString("mectype"));
/*  800 */       List<String> list = this.mmro.jsonToList(str2);
/*  801 */       if (!"".equals(str3)) {
/*  802 */         for (byte b = 0; b < paramArrayList3.size(); b++) {
/*  803 */           String str = "";
/*  804 */           recordSet2.execute("select * from checkmobilemoderesult where id='" + (String)paramArrayList3.get(b) + "'");
/*  805 */           if (recordSet2.next()) {
/*  806 */             String str4 = Util.null2String(recordSet2.getString("referkey"));
/*  807 */             String str5 = "";
/*  808 */             String str6 = Util.null2String(recordSet2.getString("sourcecontent"));
/*      */ 
/*      */             
/*  811 */             JSONObject jSONObject1 = JSONObject.parseObject("{" + str6 + "}");
/*  812 */             if (str4.contains(".")) {
/*  813 */               str5 = str4.substring(str4.lastIndexOf(".") + 1);
/*  814 */             } else if (!"".equals(str4)) {
/*  815 */               str5 = str4;
/*      */             } else {
/*      */               continue;
/*      */             } 
/*  819 */             String str7 = jSONObject1.getString(str5);
/*      */             
/*  821 */             String str8 = "";
/*  822 */             String str9 = recordSet2.getString("sameSourceContentFlag");
/*      */             
/*  824 */             ListIterator<String> listIterator = list.listIterator();
/*  825 */             boolean bool = false;
/*  826 */             while (listIterator.hasNext()) {
/*  827 */               String str10 = listIterator.next();
/*  828 */               if (str10.toLowerCase().indexOf(str4.toLowerCase() + "==>>==") == 0) {
/*  829 */                 str10 = str10.substring(str10.indexOf("==>>==") + 6);
/*  830 */                 String str11 = str10;
/*      */                 
/*  832 */                 if (!"".equals(str4) && !"".equals(str11) && (str7.trim().equals(str11.trim()) || str7.trim().equals(str11.replaceAll("\n", "").trim()))) {
/*  833 */                   if ("FLBS".equalsIgnoreCase(str3) && "backscript".equalsIgnoreCase(str4)) {
/*      */                     try {
/*  835 */                       str11 = URLDecoder.decode(str10, "UTF-8");
/*  836 */                     } catch (Exception exception) {
/*  837 */                       exception.printStackTrace();
/*      */                     } 
/*      */                   }
/*  840 */                   str = str11;
/*  841 */                   List<T> list1 = (paramArrayList == null) ? null : Arrays.<T>asList((T[])new Pattern[] { paramArrayList.get(b) });
/*  842 */                   str11 = doReplace(str11, (List)list1, Arrays.asList(new String[] { paramArrayList1.get(b) }, ), Arrays.asList(new String[] { paramArrayList2.get(b) }, ), Arrays.asList(new String[] { paramArrayList3.get(b) }, ), "checkmobilemoderesult");
/*  843 */                   if (str.equals(str11)) {
/*      */                     continue;
/*      */                   }
/*  846 */                   String str12 = str4.contains(".") ? str4.substring(str4.lastIndexOf(".") + 1) : str4;
/*  847 */                   if ("FLBS".equalsIgnoreCase(str3) && "backscript".equalsIgnoreCase(str4)) {
/*      */                     try {
/*  849 */                       str11 = URLEncoder.encode(str11, "UTF-8").replaceAll("\\+", "");
/*  850 */                     } catch (Exception exception) {
/*  851 */                       exception.printStackTrace();
/*      */                       continue;
/*      */                     } 
/*      */                   }
/*  855 */                   JSONObject jSONObject2 = new JSONObject();
/*  856 */                   jSONObject2.put(str12, str11);
/*  857 */                   str8 = jSONObject2.toJSONString().substring(1, jSONObject2.toJSONString().length() - 1);
/*  858 */                   str2 = str2.replace(str6, str8);
/*  859 */                   listIterator.set(str4 + "==>>==" + str11);
/*  860 */                   bool = true;
/*      */                   break;
/*      */                 } 
/*      */               } 
/*      */             } 
/*  865 */             if (bool) {
/*      */ 
/*      */               
/*  868 */               if (!isJsonObject(str2)) {
/*  869 */                 (new BaseBean()).writeLog("替换后的字符串不符合json数据格式,替换存在问题,插件id:" + paramString + "===相关检测结果ID为:" + (String)paramArrayList3.get(b));
/*      */               }
/*  871 */               String str10 = "update MOBILEEXTENDCOMPONENT set mecparam=? where id='" + paramString + "'";
/*  872 */               String str11 = " update checkmobilemoderesult set sourcecontent=? where sameSourceContentFlag='" + str9 + "'";
/*  873 */               ConnStatement connStatement = new ConnStatement();
/*      */             } 
/*      */           } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */           
/*      */           continue;
/*      */         } 
/*      */       }
/*      */     } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  895 */     return null;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String replaceColumn(String paramString1, String paramString2, String paramString3, ArrayList<Pattern> paramArrayList, ArrayList<String> paramArrayList1, ArrayList<String> paramArrayList2, ArrayList<String> paramArrayList3, String paramString4) {
/*  904 */     RecordSet recordSet1 = new RecordSet();
/*  905 */     RecordSet recordSet2 = new RecordSet();
/*  906 */     recordSet1.execute("select * from " + paramString2 + " where " + paramString3 + " is not null and id='" + paramString1 + "'");
/*  907 */     JSONObject jSONObject = new JSONObject();
/*  908 */     if (recordSet1.next()) {
/*  909 */       String str1 = recordSet1.getString(paramString3);
/*      */       
/*  911 */       if (paramString3.equalsIgnoreCase("scripts") || paramString3.equalsIgnoreCase("scriptstr")) {
/*  912 */         str1 = this.excelSecurity.decode(str1);
/*      */       }
/*  914 */       String str2 = recordSet1.getString("id");
/*  915 */       if (str1 != null) {
/*  916 */         String str = str1;
/*  917 */         boolean bool = isJsonObject(str);
/*      */         
/*  919 */         if (bool && paramString2.equalsIgnoreCase("apphomepage") && paramString3.equalsIgnoreCase("pageattr")) {
/*  920 */           jSONObject = JSONObject.parseObject(str1);
/*  921 */           str = Util.null2String(jSONObject.getString("onloadScript"));
/*      */         } 
/*  923 */         str = doReplace(str, paramArrayList, paramArrayList1, paramArrayList2, paramArrayList3, paramString4);
/*  924 */         if (str == null || "".equals(str)) {
/*  925 */           return null;
/*      */         }
/*  927 */         if (bool)
/*      */         {
/*  929 */           if (paramString2.equalsIgnoreCase("apphomepage") && paramString3.equalsIgnoreCase("pageattr")) {
/*  930 */             jSONObject.put("onloadScript", str);
/*  931 */             str = jSONObject.toString();
/*      */           } else {
/*  933 */             str = RuleTrans.changeStr(str);
/*      */             
/*  935 */             if (!isJsonObject(str)) {
/*  936 */               (new BaseBean()).writeLog("filecheck======替换失败！替换字段时，发现字段为json格式，替换后破坏原有json结构，表名：" + paramString2 + "===字段名：" + paramString3 + "====id:" + paramString1);
/*  937 */               return recordSet1.getString(paramString3);
/*      */             } 
/*      */           } 
/*      */         }
/*      */         
/*  942 */         str = str.replaceAll("'", "''");
/*  943 */         if (paramString3.equalsIgnoreCase("scripts") || paramString3.equalsIgnoreCase("scriptstr")) {
/*  944 */           str = this.excelSecurity.encodeStr(str);
/*      */         }
/*  946 */         if (recordSet1.getDBType().equalsIgnoreCase("oracle")) {
/*  947 */           recordSet2.executeUpdate("declare  v_clob clob :='" + str + "'; begin update " + paramString2 + " set " + paramString3 + "=v_clob where id='" + str2 + "';end;", new Object[0]);
/*      */         } else {
/*  949 */           recordSet2.executeUpdate("update " + paramString2 + " set " + paramString3 + "='" + str + "' where id='" + str2 + "'", new Object[0]);
/*      */         } 
/*      */       } 
/*      */     } 
/*  953 */     return null;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String replaceFile(File paramFile, ArrayList<Pattern> paramArrayList, ArrayList<String> paramArrayList1, ArrayList<String> paramArrayList2, ArrayList<String> paramArrayList3, String paramString) {
/*  964 */     String str = "";
/*  965 */     if (paramFile.isFile()) {
/*  966 */       String str1 = paramFile.getPath();
/*  967 */       (new BaseBean()).writeLog("正则规则替换，开始替换文件：" + this.fileUtil.getPath(str1));
/*      */       
/*  969 */       long l = (new Date()).getTime();
/*      */       
/*  971 */       if (str1.indexOf("vssver") > -1) {
/*  972 */         return null;
/*      */       }
/*  974 */       String str2 = paramFile.getName();
/*  975 */       str = str1.substring(0, str1.lastIndexOf("" + File.separatorChar));
/*  976 */       str = str + "_cbak" + File.separatorChar;
/*  977 */       String str3 = str + str2;
/*      */       
/*  979 */       String str4 = "GBK";
/*      */       
/*  981 */       OutputStreamWriter outputStreamWriter = null;
/*  982 */       BufferedWriter bufferedWriter = null;
/*  983 */       File file = null;
/*      */       try {
/*  985 */         boolean bool = FileCharsetDetector.check(paramFile);
/*  986 */         if (bool) {
/*  987 */           str4 = "UTF-8";
/*      */         } else {
/*  989 */           str4 = "GBK";
/*      */         } 
/*      */         
/*  992 */         File file1 = new File(this.fileUtil.getPath(str));
/*  993 */         if (!file1.exists()) {
/*  994 */           file1.mkdirs();
/*      */         }
/*  996 */         file = new File(this.fileUtil.getPath(str3));
/*  997 */         if (!file.exists()) {
/*  998 */           file.createNewFile();
/*      */         }
/*      */         
/* 1001 */         file.setWritable(true);
/* 1002 */         paramFile.setWritable(true);
/*      */         
/* 1004 */         outputStreamWriter = new OutputStreamWriter(new FileOutputStream(this.fileUtil.getPath(str3)), str4);
/* 1005 */         bufferedWriter = new BufferedWriter(outputStreamWriter);
/*      */         
/* 1007 */         String str5 = "";
/* 1008 */         str5 = this.fileUtil.readFile(paramFile).toString();
/* 1009 */         str5 = doReplace(str5, paramArrayList, paramArrayList1, paramArrayList2, paramArrayList3, paramString);
/* 1010 */         if (str5 == null || "".equals(str5)) {
/* 1011 */           return null;
/*      */         }
/* 1013 */         bufferedWriter.write(str5, 0, str5.length());
/*      */       }
/* 1015 */       catch (Exception exception) {
/* 1016 */         exception.printStackTrace();
/*      */       } finally {
/*      */         try {
/* 1019 */           bufferedWriter.flush();
/* 1020 */           bufferedWriter.close();
/* 1021 */           outputStreamWriter.close();
/*      */         }
/* 1023 */         catch (IOException iOException) {}
/*      */       } 
/*      */ 
/*      */ 
/*      */       
/* 1028 */       if (file != null && paramFile != null) {
/*      */         try {
/*      */           String[] arrayOfString;
/*      */           
/* 1032 */           if (str1.toLowerCase().contains("filesystem")) {
/* 1033 */             arrayOfString = str1.split("filesystem");
/*      */           } else {
/* 1035 */             arrayOfString = str1.split(GCONST.getRootPath().replace("\\", "\\\\"));
/*      */           } 
/* 1037 */           String str5 = "";
/* 1038 */           String str6 = this.bakRootPath;
/* 1039 */           if (arrayOfString.length == 2) {
/* 1040 */             str5 = arrayOfString[1];
/* 1041 */             if (str1.contains("filesystem")) {
/* 1042 */               str6 = this.bakRootPath + "filesystem" + File.separatorChar + str5;
/*      */             } else {
/* 1044 */               str6 = this.bakRootPath + File.separatorChar + str5;
/*      */             } 
/*      */           } else {
/* 1047 */             str6 = this.bakRootPath + File.separatorChar + paramFile.getName();
/*      */           } 
/* 1049 */           FileUtils.copyFile(paramFile, new File(str6));
/*      */           
/* 1051 */           FileUtils.copyFile(file, paramFile);
/*      */           
/* 1053 */           deleteFile(new File(str));
/*      */         }
/* 1055 */         catch (IOException iOException) {
/* 1056 */           iOException.printStackTrace();
/*      */         } 
/*      */       }
/* 1059 */       (new BaseBean()).writeLog("正则规则替换，已经替换文件：" + this.fileUtil.getPath(str1) + "结束,检测耗时：" + ((new Date()).getTime() - l) + "毫秒");
/* 1060 */     } else if (paramFile.isDirectory()) {
/* 1061 */       File[] arrayOfFile = paramFile.listFiles();
/* 1062 */       if (arrayOfFile.length > 0) {
/* 1063 */         for (byte b = 0; b < arrayOfFile.length; b++) {
/* 1064 */           replaceFile(arrayOfFile[b], paramArrayList, paramArrayList1, paramArrayList2, paramArrayList3, paramString);
/*      */         }
/*      */       }
/*      */       
/* 1068 */       deleteFile(new File(this.fileUtil.getPath(paramFile.getPath() + "_cbak")));
/*      */     } else {
/*      */       
/*      */       try {
/* 1072 */         (new BaseBean()).writeLog("错误文件路径：" + paramFile.getAbsolutePath());
/* 1073 */       } catch (Exception exception) {
/* 1074 */         (new BaseBean()).writeLog("文件异常直接跳过" + exception.toString());
/*      */       } 
/*      */     } 
/* 1077 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private boolean deleteFile(File paramFile) {
/* 1089 */     if (paramFile.isDirectory()) {
/* 1090 */       String[] arrayOfString = paramFile.list();
/*      */       
/* 1092 */       for (byte b = 0; b < arrayOfString.length; b++) {
/* 1093 */         boolean bool = deleteFile(new File(paramFile, this.fileUtil.getPath(arrayOfString[b])));
/* 1094 */         if (!bool) {
/* 1095 */           return false;
/*      */         }
/*      */       } 
/*      */     } 
/*      */     
/* 1100 */     return paramFile.delete();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String doReplace(String paramString1, List<Pattern> paramList, List<String> paramList1, List<String> paramList2, List<String> paramList3, String paramString2) {
/* 1112 */     if ("".equals(Util.null2String(paramString1))) {
/* 1113 */       return "";
/*      */     }
/* 1115 */     for (byte b = 0; b < paramList2.size(); b++) {
/* 1116 */       String str = paramList2.get(b);
/*      */       
/* 1118 */       Matcher matcher = null;
/* 1119 */       Pattern pattern = null;
/*      */ 
/*      */       
/* 1122 */       if ("0".equals(str)) {
/*      */         
/* 1124 */         matcher = null;
/* 1125 */         pattern = paramList.get(b);
/* 1126 */         String str1 = paramList1.get(b);
/*      */         
/* 1128 */         matcher = pattern.matcher(paramString1);
/* 1129 */         if (str1 != null && !"".equals(str1) && str1.indexOf("无法自动替换") < 0) {
/* 1130 */           deleteRefRecord("" + paramList3.get(b), paramString2);
/*      */         }
/*      */         
/* 1133 */         while (matcher.find() && str1 != null && !"".equals(str1))
/*      */         {
/* 1135 */           paramString1 = matcher.replaceAll(str1);
/*      */         }
/*      */       }
/*      */       else {
/*      */         
/* 1140 */         String str1 = paramList1.get(b);
/*      */         
/*      */         try {
/* 1143 */           if (str1 != null && !"".equals(str1) && str1.indexOf("无法自动替换") <= -1) {
/*      */ 
/*      */             
/* 1146 */             Class<?> clazz = Class.forName(str1);
/* 1147 */             Method method = clazz.getMethod("replaceRule", new Class[] { String.class });
/* 1148 */             Object object = method.invoke(clazz.newInstance(), new Object[] { paramString1 });
/* 1149 */             String str2 = (String)object;
/* 1150 */             if (!"".equals(str2))
/* 1151 */             { deleteRefRecord("" + paramList3.get(b), paramString2);
/* 1152 */               paramString1 = str2; } 
/*      */           } 
/* 1154 */         } catch (Exception exception) {
/* 1155 */           exception.printStackTrace();
/* 1156 */           (new BaseBean()).writeLog("请检查接口rulepath是否正确!!!");
/* 1157 */           return null;
/*      */         } 
/*      */       } 
/*      */     } 
/*      */     
/* 1162 */     return paramString1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void deleteRefRecord(String paramString1, String paramString2) {
/* 1172 */     RecordSet recordSet1 = new RecordSet();
/* 1173 */     RecordSet recordSet2 = new RecordSet();
/* 1174 */     recordSet1.execute("select * from " + paramString2 + " where id='" + paramString1 + "'");
/* 1175 */     if (recordSet1.next()) {
/* 1176 */       String str1 = recordSet1.getString("ruleid");
/* 1177 */       String str2 = Util.null2String(recordSet1.getString("filepath"));
/* 1178 */       String str3 = Util.null2String(recordSet1.getString("refertable"));
/* 1179 */       String str4 = Util.null2String(recordSet1.getString("nodehtmllayoutid"));
/* 1180 */       if (!"mobileextendcomponent".equalsIgnoreCase(str3)) {
/* 1181 */         String str = " where ruleid='" + str1 + "' ";
/* 1182 */         if (!"upgradecheckresult".equalsIgnoreCase(paramString2)) {
/* 1183 */           str = str + " and nodehtmllayoutid='" + str4 + "'";
/*      */         }
/* 1185 */         if (!"".equals(str2)) {
/* 1186 */           str = str + " and filepath='" + str2 + "' ";
/*      */         }
/* 1188 */         if (!"".equals(str3)) {
/* 1189 */           str = str + " and refertable='" + str3 + "'";
/*      */         }
/* 1191 */         (new BaseBean()).writeLog("执行清除数据desql::delete from " + paramString2 + " where id in(select id from " + paramString2 + str + ")");
/* 1192 */         recordSet2.execute("delete from " + paramString2 + " where id in(select id from " + paramString2 + str + ")");
/*      */       } else {
/* 1194 */         recordSet2.execute("delete from " + paramString2 + " where id='" + paramString1 + "'");
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getBakRootPath() {
/* 1204 */     String str1 = target;
/* 1205 */     Date date = new Date();
/* 1206 */     String str2 = yyyyMMdd.format(date);
/* 1207 */     String str3 = HHmmss.format(date);
/* 1208 */     str1 = target + str2 + "\\" + str3 + "\\ecology\\";
/* 1209 */     return str1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public <T> void addItemToMap(HashMap<String, ArrayList<T>> paramHashMap, String paramString, T paramT) {
/* 1216 */     if (paramHashMap.containsKey(paramString)) {
/* 1217 */       ArrayList<T> arrayList = paramHashMap.get(paramString);
/* 1218 */       arrayList.add(paramT);
/*      */     } else {
/* 1220 */       ArrayList<T> arrayList = new ArrayList();
/* 1221 */       arrayList.add(paramT);
/* 1222 */       paramHashMap.put(paramString, arrayList);
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static boolean isJsonObject(String paramString) {
/* 1235 */     if (paramString == null || "".equals(paramString))
/* 1236 */       return false; 
/*      */     try {
/* 1238 */       JSONObject jSONObject = JSONObject.parseObject(paramString);
/* 1239 */       return true;
/* 1240 */     } catch (Exception exception) {
/* 1241 */       return false;
/*      */     } 
/*      */   }
/*      */   
/*      */   public static void backupTable(Set<String> paramSet) {
/* 1246 */     RecordSet recordSet = new RecordSet();
/* 1247 */     String str1 = recordSet.getDBType();
/* 1248 */     Date date = new Date();
/* 1249 */     String str2 = yyyyMMdd.format(date);
/* 1250 */     String str3 = yyyyMMdd_sql.format(date);
/* 1251 */     String str4 = HHmmss.format(date);
/* 1252 */     String str5 = HHmmss_sql.format(date);
/* 1253 */     String str6 = "TEMPCHECK_" + str2 + str4;
/* 1254 */     byte b = 0;
/* 1255 */     for (String str7 : paramSet) {
/* 1256 */       b++;
/* 1257 */       String str8 = str6 + b;
/* 1258 */       recordSet.execute("INSERT INTO TEMPLETECHECKBAKLOG(SOURCENAME,TARGETNAME,CREATEDATE,CREATETIME) values('" + str7 + "','" + str8 + "','" + str3 + "','" + str5 + "')");
/* 1259 */       if ("sqlserver".equalsIgnoreCase(str1)) {
/* 1260 */         recordSet.execute(" SELECT * into " + str8 + " FROM " + str7); continue;
/*      */       } 
/* 1262 */       recordSet.execute(" CREATE TABLE " + str8 + " as SELECT * FROM " + str7);
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static String formatIds(String paramString) {
/* 1272 */     String str = Util.null2String(paramString).toLowerCase().replaceAll(",+", ",");
/* 1273 */     if (str.startsWith(",")) {
/* 1274 */       str = str.substring(1);
/*      */     }
/* 1276 */     if (str.endsWith(",")) {
/* 1277 */       str = str.substring(0, str.length() - 1);
/*      */     }
/* 1279 */     return str.trim();
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/filecheck/ReplaceOperation.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */