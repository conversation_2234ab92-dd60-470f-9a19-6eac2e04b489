/*     */ package weaver.templetecheck.filecheck;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import java.io.File;
/*     */ import java.io.FileInputStream;
/*     */ import java.io.FileNotFoundException;
/*     */ import java.io.IOException;
/*     */ import java.util.Properties;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ public class PortalReportOperation
/*     */ {
/*  18 */   BaseBean baseBean = new BaseBean();
/*  19 */   Properties p = getProp();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONObject getPortalReportInfo() {
/*  27 */     this.baseBean.writeLog("weaver.templetecheck.filecheck.PortalReportOperation.getPortalReportInfo start");
/*  28 */     JSONObject jSONObject = new JSONObject();
/*  29 */     RecordSet recordSet = new RecordSet();
/*  30 */     String str1 = "";
/*     */     
/*  32 */     String str2 = this.p.getProperty("portalBeforeLogin4E9");
/*  33 */     String str3 = this.p.getProperty("portalAfterLogin4E9");
/*  34 */     String str4 = this.p.getProperty("portalElement4E9");
/*  35 */     this.baseBean.writeLog("portalBeforeLogin4E9:" + str2 + ";portalAfterLogin4E9:" + str3 + ";portalElementE9:" + str4);
/*  36 */     this.p.clear();
/*  37 */     int i = 0;
/*  38 */     int j = 0;
/*  39 */     int k = 0;
/*  40 */     int m = 0;
/*  41 */     int n = 0;
/*  42 */     int i1 = 0;
/*  43 */     int i2 = 0;
/*  44 */     int i3 = 0;
/*  45 */     int i4 = 0;
/*  46 */     int i5 = 0;
/*  47 */     int i6 = 0;
/*  48 */     int i7 = 0;
/*  49 */     int i8 = 0;
/*     */     
/*  51 */     jSONObject.put("emobile", Integer.valueOf((new MyEmobileReportOperation()).getMyEmobileXMLNumUseRegex()));
/*     */     
/*  53 */     str1 = "select count(1) as counts from WORKFLOW_BASE where ((custompage is not null " + (recordSet.getDBType().equals("oracle") ? "" : " and custompage<>''") + " )or (custompage4emoble is not null " + (recordSet.getDBType().equals("oracle") ? "" : " and custompage4emoble<>''") + "))";
/*  54 */     recordSet.executeQuery(str1, new Object[0]);
/*  55 */     if (recordSet.next()) {
/*  56 */       i2 = recordSet.getInt("counts");
/*     */     }
/*  58 */     jSONObject.put("custompage", Integer.valueOf(i2));
/*     */ 
/*     */     
/*  61 */     str1 = "select count(1) as counts from SystemLoginTemplate ";
/*  62 */     recordSet.executeQuery(str1, new Object[0]);
/*  63 */     if (recordSet.next()) {
/*  64 */       i = recordSet.getInt("counts");
/*     */     }
/*  66 */     jSONObject.put("portalBeforeLogin", Integer.valueOf(i));
/*     */ 
/*     */     
/*  69 */     str1 = "select count(1) as counts from SystemLoginTemplate WHERE LOGINTEMPLATEID IN (" + str2 + ")";
/*  70 */     recordSet.executeQuery(str1, new Object[0]);
/*  71 */     if (recordSet.next()) {
/*  72 */       k = recordSet.getInt("counts");
/*     */     }
/*  74 */     jSONObject.put("portalBL4E9", Integer.valueOf(k));
/*     */     
/*  76 */     jSONObject.put("portalBL4E7", Integer.valueOf(i - k));
/*     */ 
/*     */ 
/*     */     
/*  80 */     str1 = "select count(1) as counts from SystemTemplate ";
/*  81 */     recordSet.executeQuery(str1, new Object[0]);
/*  82 */     if (recordSet.next()) {
/*  83 */       j = recordSet.getInt("counts");
/*     */     }
/*  85 */     jSONObject.put("portalAfterLogin", Integer.valueOf(j));
/*     */ 
/*     */     
/*  88 */     str1 = "select count(1) as counts from SystemTemplate WHERE ID IN (" + str3 + ")";
/*  89 */     recordSet.executeQuery(str1, new Object[0]);
/*  90 */     if (recordSet.next()) {
/*  91 */       m = recordSet.getInt("counts");
/*     */     }
/*  93 */     jSONObject.put("portalAL4E9", Integer.valueOf(m));
/*     */ 
/*     */     
/*  96 */     jSONObject.put("portalAL4E7", Integer.valueOf(j - m));
/*     */ 
/*     */     
/*  99 */     jSONObject.put("portaltotal", Integer.valueOf(i + j));
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 104 */     str1 = "select count(1) as counts from hpinfo where  creatortype=0 and subcompanyid=-1 and infoname is not null ";
/* 105 */     recordSet.executeQuery(str1, new Object[0]);
/* 106 */     if (recordSet.next()) {
/* 107 */       i = recordSet.getInt("counts");
/*     */     }
/* 109 */     jSONObject.put("portalBeforeLoginpage", Integer.valueOf(i));
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 114 */     if ("sqlserver".equalsIgnoreCase(recordSet.getDBType())) {
/* 115 */       str1 = " select count(1) as counts from hpinfo  where  subcompanyid!=-1 and  infoname != ''";
/*     */     } else {
/* 117 */       str1 = " select count(1) as counts from hpinfo  where  subcompanyid!=-1 and  infoname is not null";
/*     */     } 
/*     */     
/* 120 */     recordSet.executeQuery(str1, new Object[0]);
/* 121 */     if (recordSet.next()) {
/* 122 */       j = recordSet.getInt("counts");
/*     */     }
/* 124 */     jSONObject.put("portalAfterLoginpage", Integer.valueOf(j));
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 130 */     str1 = "select count(1) as counts from hpbaseelement where loginview != '4' ";
/* 131 */     String str5 = "'fnaBudgetAssistant','fnaBudgetAssistant1'";
/* 132 */     String str6 = Util.null2String((new BaseBean()).getPropValue("disableelements", "ebaseids"));
/* 133 */     String[] arrayOfString = Util.TokenizerStringNew(str6, ","); byte b; int i9;
/* 134 */     for (b = 0, i9 = arrayOfString.length; b < i9; b++) {
/* 135 */       str5 = str5 + ",'" + arrayOfString[b] + "'";
/*     */     }
/* 137 */     if (!"".equals(str5)) str1 = str1 + " and id not in(" + str5 + ")"; 
/* 138 */     recordSet.executeQuery(str1, new Object[0]);
/* 139 */     if (recordSet.next()) {
/* 140 */       n = recordSet.getInt("counts");
/*     */     }
/* 142 */     jSONObject.put("portalElement", Integer.valueOf(n));
/*     */ 
/*     */     
/* 145 */     str1 = "select count(1) as counts from hpbaseelement where ID IN (" + str4 + ") and loginview != '4'";
/* 146 */     if (!"".equals(str5)) str1 = str1 + " and id not in(" + str5 + ")"; 
/* 147 */     recordSet.executeQuery(str1, new Object[0]);
/* 148 */     if (recordSet.next()) {
/* 149 */       i1 = recordSet.getInt("counts");
/* 150 */       jSONObject.put("portalElement4E9", Integer.valueOf(i1));
/*     */     } 
/*     */     
/* 153 */     jSONObject.put("portalElement4E7", Integer.valueOf(n - i1));
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 158 */     str1 = "select  count(1) as counts from datasourcesetting";
/* 159 */     recordSet.executeQuery(str1, new Object[0]);
/* 160 */     if (recordSet.next()) {
/* 161 */       i3 = recordSet.getInt("counts");
/*     */     }
/* 163 */     jSONObject.put("dataSource", Integer.valueOf(i3));
/*     */ 
/*     */     
/* 166 */     String str7 = " count(1) as counts ";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 189 */     String str8 = " ((select d.id,      d.dmlactionname as actionname,d.typename,      d.formid,      d.isbill,      d.datasourceid,      '1' as fromtype,      '" + SystemEnv.getHtmlLabelName(82986, 7) + "' as fromtypename from formactionset d  union all select s.id,       s.actionname,s.typename,       s.formid,       s.isbill,       '' as datasourceid,       '2' as fromtype,      '" + SystemEnv.getHtmlLabelName(82987, 7) + "' as fromtypename  from wsformactionset s  union all select e.id as id,       e.actionname,'' as typename,       e.formid,       e.isbill,       '' as datasourceid,       '4' as fromtype,      '" + SystemEnv.getHtmlLabelName(381878, 7) + "' as fromtypename  from esbformactionset e ";
/*     */     
/* 191 */     if (recordSet.getDBType().equals("oracle")) {
/* 192 */       str8 = str8 + " union all select s.id,nvl(s.actionshowname,actionname) as actionname,typename,0 as formid,0 as isbill,'' as datasourceid,'3' as fromtype, '" + SystemEnv.getHtmlLabelName(82988, 7) + "' as fromtypename ";
/* 193 */     } else if (recordSet.getDBType().equals("mysql")) {
/* 194 */       str8 = str8 + " union all select s.id,ifnull(s.actionshowname,actionname) as actionname,typename,0 as formid,0 as isbill,'' as datasourceid,'3' as fromtype, '" + SystemEnv.getHtmlLabelName(82988, 7) + "' as fromtypename ";
/*     */     } else {
/* 196 */       str8 = str8 + " union all select s.id,isnull(s.actionshowname,actionname) as actionname,typename,0 as formid,0 as isbill,'' as datasourceid,'3' as fromtype, '" + SystemEnv.getHtmlLabelName(82988, 7) + "' as fromtypename ";
/* 197 */     }  str8 = str8 + " from actionsetting s) a left outer  join (select id,formname,0 as isbill from workflow_formbase c union all select c.id,h.labelname as formname,1 as isbill from workflow_bill c ,htmllabelinfo h where c.namelabel=h.indexid and h.languageid=7) b on a.formid=b.id and a.isbill=b.isbill) ";
/* 198 */     str1 = " select " + str7 + " from " + str8;
/* 199 */     recordSet.executeQuery(str1, new Object[0]);
/* 200 */     if (recordSet.next()) {
/* 201 */       i4 = recordSet.getInt("counts");
/*     */     }
/* 203 */     jSONObject.put("workflowAction", Integer.valueOf(i4));
/*     */ 
/*     */     
/* 206 */     str1 = " select " + str7 + " from " + str8 + " where a.fromtype=1";
/* 207 */     recordSet.executeQuery(str1, new Object[0]);
/* 208 */     if (recordSet.next()) {
/* 209 */       i5 = recordSet.getInt("counts");
/*     */     }
/* 211 */     jSONObject.put("DmlAction", Integer.valueOf(i5));
/*     */ 
/*     */     
/* 214 */     str1 = " select " + str7 + " from " + str8 + " where a.fromtype=2";
/* 215 */     recordSet.executeQuery(str1, new Object[0]);
/* 216 */     if (recordSet.next()) {
/* 217 */       i6 = recordSet.getInt("counts");
/*     */     }
/* 219 */     jSONObject.put("WebServiceAction", Integer.valueOf(i6));
/*     */ 
/*     */     
/* 222 */     str1 = " select " + str7 + " from " + str8 + " where a.fromtype=3";
/* 223 */     recordSet.executeQuery(str1, new Object[0]);
/* 224 */     if (recordSet.next()) {
/* 225 */       i7 = recordSet.getInt("counts");
/*     */     }
/* 227 */     jSONObject.put("CustomerAction", Integer.valueOf(i7));
/*     */ 
/*     */     
/* 230 */     str1 = " select " + str7 + " from " + str8 + " where a.fromtype=4";
/* 231 */     recordSet.executeQuery(str1, new Object[0]);
/* 232 */     if (recordSet.next()) {
/* 233 */       i8 = recordSet.getInt("counts");
/*     */     }
/* 235 */     jSONObject.put("ESBAction", Integer.valueOf(i8));
/*     */     
/* 237 */     return jSONObject;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getPortalResult(String paramString1, String paramString2) {
/* 247 */     String str1 = this.p.getProperty("portalBeforeLogin4E9");
/* 248 */     String str2 = this.p.getProperty("portalAfterLogin4E9");
/* 249 */     String str3 = this.p.getProperty("portalElement4E9");
/* 250 */     String str4 = "1,2,3";
/* 251 */     String str5 = "ID";
/* 252 */     if ("SystemLoginTemplate".equals(paramString2)) {
/* 253 */       str5 = "LOGINTEMPLATEID";
/* 254 */       str4 = str1;
/* 255 */     } else if ("hpinfo".equals(paramString2)) {
/* 256 */       str5 = "ID";
/* 257 */       str4 = str2;
/* 258 */     } else if ("hpbaseelement".equals(paramString2)) {
/* 259 */       str5 = "ID";
/* 260 */       str4 = str3;
/*     */     } 
/* 262 */     String str6 = "select * from " + paramString2 + " where " + str5 + " IN (" + str4 + ") AND " + str5 + " = '" + paramString1 + "'";
/* 263 */     RecordSet recordSet = new RecordSet();
/* 264 */     recordSet.executeQuery(str6, new Object[0]);
/* 265 */     String str7 = "<font color='red'>" + SystemEnv.getHtmlLabelName(383763, ThreadVarLanguage.getLang()) + "</font>";
/* 266 */     if (recordSet.next()) {
/* 267 */       str7 = "" + SystemEnv.getHtmlLabelName(149, ThreadVarLanguage.getLang()) + "";
/*     */     }
/* 269 */     return str7;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getElementIsuse(String paramString) {
/* 278 */     if ("1".equals(paramString)) {
/* 279 */       return "" + SystemEnv.getHtmlLabelName(388229, ThreadVarLanguage.getLang()) + "";
/*     */     }
/* 281 */     return "<font color='blue'>" + SystemEnv.getHtmlLabelName(22205, ThreadVarLanguage.getLang()) + "</font>";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getPortalDepartment(String paramString) {
/* 291 */     String str1 = "";
/* 292 */     String str2 = "select SUBCOMPANYNAME from HRMSUBCOMPANY where id =" + paramString;
/* 293 */     RecordSet recordSet = new RecordSet();
/* 294 */     recordSet.executeQuery(str2, new Object[0]);
/* 295 */     if (recordSet.next()) {
/* 296 */       str1 = recordSet.getString("SUBCOMPANYNAME");
/*     */     }
/* 298 */     if ("0".equals(paramString)) {
/* 299 */       str1 = "" + SystemEnv.getHtmlLabelName(32646, ThreadVarLanguage.getLang()) + "";
/*     */     }
/* 301 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Properties getProp() {
/* 309 */     String str = GCONST.getRootPath() + "templetecheck" + File.separatorChar + "exclude" + File.separatorChar + "checkSystemFile.properties";
/* 310 */     File file = new File(str);
/* 311 */     FileInputStream fileInputStream = null;
/* 312 */     Properties properties = new Properties();
/*     */     try {
/* 314 */       fileInputStream = new FileInputStream(file);
/* 315 */       properties.load(fileInputStream);
/* 316 */     } catch (FileNotFoundException fileNotFoundException) {
/* 317 */       this.baseBean.writeLog("FileNotFoundException");
/* 318 */       fileNotFoundException.printStackTrace();
/* 319 */     } catch (IOException iOException) {
/* 320 */       this.baseBean.writeLog("IOException");
/* 321 */       iOException.printStackTrace();
/*     */     } 
/* 323 */     return properties;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getPortal(String paramString) {
/* 332 */     RecordSet recordSet = new RecordSet();
/* 333 */     String str = "";
/* 334 */     recordSet.execute("select be.id ,be.title ,hp.id ,hp.infoname from hpelement e , hpinfo hp ,hpbaseelement be where e.hpid=hp.id and e.ebaseid=be.id and be.id='" + paramString + "' group by be.id,be.title,hp.id,hp.infoname  order by be.id,hp.id");
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 339 */     while (recordSet.next()) {
/* 340 */       str = str + recordSet.getString("infoname") + ",";
/*     */     }
/*     */     
/* 343 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSubCompany(String paramString) {
/* 352 */     RecordSet recordSet = new RecordSet();
/* 353 */     String str = "";
/* 354 */     recordSet.execute("select be.id ,be.title ,hp.id ,hp.infoname,hp.subcompanyid from hpelement e , hpinfo hp ,hpbaseelement be where e.hpid=hp.id and e.ebaseid=be.id and be.id='" + paramString + "' group by be.id,be.title,hp.id,hp.infoname,hp.subcompanyid  order by be.id,hp.id");
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 359 */     while (recordSet.next()) {
/* 360 */       String str1 = recordSet.getString("subcompanyid");
/* 361 */       str = str + getPortalDepartment(str1) + ",";
/*     */     } 
/* 363 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getIsCluster(String paramString) {
/* 372 */     String str = "" + SystemEnv.getHtmlLabelName(25105, ThreadVarLanguage.getLang()) + "";
/* 373 */     if ("2".equals(paramString)) {
/* 374 */       str = "" + SystemEnv.getHtmlLabelName(163, ThreadVarLanguage.getLang()) + "";
/*     */     }
/* 376 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getUsePool(String paramString) {
/* 385 */     String str = "" + SystemEnv.getHtmlLabelName(25105, ThreadVarLanguage.getLang()) + "";
/* 386 */     if ("1".equals(paramString)) {
/* 387 */       str = "" + SystemEnv.getHtmlLabelName(163, ThreadVarLanguage.getLang()) + "";
/*     */     }
/* 389 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/filecheck/PortalReportOperation.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */