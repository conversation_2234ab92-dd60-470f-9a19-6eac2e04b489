/*      */ package weaver.templetecheck.filecheck;
/*      */ import com.alibaba.fastjson.JSONObject;
/*      */ import java.io.BufferedWriter;
/*      */ import java.io.File;
/*      */ import java.io.FileOutputStream;
/*      */ import java.io.IOException;
/*      */ import java.util.ArrayList;
/*      */ import java.util.HashMap;
/*      */ import java.util.LinkedHashMap;
/*      */ import java.util.List;
/*      */ import java.util.Map;
/*      */ import java.util.regex.Matcher;
/*      */ import java.util.regex.Pattern;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.general.GCONST;
/*      */ import weaver.general.ThreadVarLanguage;
/*      */ import weaver.general.Util;
/*      */ import weaver.systeminfo.SystemEnv;
/*      */ import weaver.templetecheck.PageTransMethod;
/*      */ 
/*      */ public class ExportReportOperation {
/*   22 */   private String columnsHtml = "";
/*   23 */   private List<LinkedHashMap<String, String>> datas = null;
/*   24 */   private int everyNum = 3000;
/*      */   private static int totolWorkflowFile;
/*      */   
/*      */   public void setCheckedWorkflowFile(int paramInt) {
/*   28 */     this; checkedWorkflowFile = paramInt;
/*      */   }
/*      */   public void checkedWorkflowFileAdd() {
/*   31 */     this; checkedWorkflowFile++;
/*      */   }
/*   33 */   private static int checkedWorkflowFile = 0;
/*      */   
/*      */   public int getTotolWorkflowFile() {
/*   36 */     return totolWorkflowFile;
/*      */   }
/*      */   
/*      */   public void setTotolWorkflowFile() {
/*   40 */     RecordSet recordSet = new RecordSet();
/*   41 */     recordSet.executeQuery("select count(1) as ids from upgradecheckworkflowresult", new Object[0]);
/*   42 */     if (recordSet.next()) {
/*   43 */       totolWorkflowFile = (int)Math.ceil(recordSet.getInt(1) * 1.0D / this.everyNum);
/*      */     }
/*      */   }
/*      */   
/*      */   public int getCheckedWorkflowFile() {
/*   48 */     return checkedWorkflowFile;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void exportReport(String paramString) {
/*   58 */     String str1 = "";
/*   59 */     getCheckResult(paramString, "");
/*   60 */     if ("upgradecheckworkflowresult".equals(paramString)) {
/*      */       return;
/*      */     }
/*   63 */     if ("mobilemode".equals(paramString)) {
/*   64 */       str1 = GCONST.getRootPath() + File.separatorChar + "templetecheck" + File.separatorChar + "report" + File.separatorChar + "MobileModeReport.xls";
/*   65 */     } else if ("mode".equals(paramString)) {
/*   66 */       str1 = GCONST.getRootPath() + File.separatorChar + "templetecheck" + File.separatorChar + "report" + File.separatorChar + "ModeReport.xls";
/*   67 */     } else if ("modeBatch".equals(paramString)) {
/*   68 */       str1 = GCONST.getRootPath() + File.separatorChar + "templetecheck" + File.separatorChar + "report" + File.separatorChar + "ModeReportBatch.xls";
/*   69 */     } else if ("WorkflowReportDetail".equals(paramString)) {
/*   70 */       str1 = GCONST.getRootPath() + File.separatorChar + "templetecheck" + File.separatorChar + "report" + File.separatorChar + "WorkflowReport.xls";
/*   71 */     } else if ("HtmlNodeReportDetail".equals(paramString)) {
/*   72 */       str1 = GCONST.getRootPath() + File.separatorChar + "templetecheck" + File.separatorChar + "report" + File.separatorChar + "HtmlNodeReportDetail.xls";
/*   73 */     } else if ("NodeChangeReportDetail".equals(paramString)) {
/*   74 */       str1 = GCONST.getRootPath() + File.separatorChar + "templetecheck" + File.separatorChar + "report" + File.separatorChar + "NeedChangeReportDetail.xls";
/*   75 */     } else if ("WorkflowCustomReport".equals(paramString)) {
/*   76 */       str1 = GCONST.getRootPath() + File.separatorChar + "templetecheck" + File.separatorChar + "report" + File.separatorChar + "WorkflowCustomReport.xls";
/*   77 */     } else if ("CustomCheck".equals(paramString)) {
/*   78 */       str1 = GCONST.getRootPath() + File.separatorChar + "templetecheck" + File.separatorChar + "report" + File.separatorChar + "CustomCheck.xls";
/*   79 */     } else if ("upgradecheckworkflowresult".equals(paramString)) {
/*   80 */       str1 = GCONST.getRootPath() + File.separatorChar + "templetecheck" + File.separatorChar + "report" + File.separatorChar + "WorkflowCheckResult.xls";
/*   81 */     } else if ("upgradecheckworkflowresultBatch".equals(paramString)) {
/*   82 */       str1 = GCONST.getRootPath() + File.separatorChar + "templetecheck" + File.separatorChar + "report" + File.separatorChar + "WorkflowCheckResultBatch.xls";
/*   83 */     } else if ("upgradecheckSpecialFileresult".equals(paramString)) {
/*   84 */       str1 = GCONST.getRootPath() + File.separatorChar + "templetecheck" + File.separatorChar + "report" + File.separatorChar + "SpecialFileResult.xls";
/*   85 */     } else if ("allErrMap".equals(paramString)) {
/*   86 */       str1 = GCONST.getRootPath() + File.separatorChar + "templetecheck" + File.separatorChar + "report" + File.separatorChar + "allErrLog.xls";
/*      */     } 
/*      */     
/*   89 */     String str2 = "";
/*   90 */     if ("CustomCheck".equals(paramString)) {
/*   91 */       str2 = getCustomCheckResult(paramString);
/*   92 */     } else if (!"allErrMap".equals(paramString)) {
/*      */ 
/*      */       
/*   95 */       str2 = getReportHtml();
/*      */     } 
/*   97 */     writeReportFile(str2, str1);
/*      */   }
/*      */   
/*      */   public void getReportXls(String paramString, List paramList) {
/*  101 */     getReportXls(paramString, paramList, new HashMap<>());
/*      */   }
/*      */   
/*      */   public void getReportXls(String paramString, List paramList, Map<String, String> paramMap) {
/*  105 */     RecordSet recordSet = new RecordSet();
/*  106 */     String str1 = "";
/*  107 */     String str2 = "";
/*  108 */     String str3 = Util.null2String(paramMap.get("workflowids"));
/*  109 */     String str4 = Util.null2String(paramMap.get("fromtab"));
/*  110 */     String str5 = Util.null2String(paramMap.get("ruleid"));
/*  111 */     String str6 = "";
/*  112 */     if ("workflow".equals(paramString)) {
/*  113 */       str1 = "upgradecheckworkflowresult";
/*  114 */       str2 = "WorkflowCheckResult";
/*  115 */       str6 = "upgradecheckworkflowresult";
/*  116 */     } else if ("workflowBatch".equals(paramString)) {
/*  117 */       str1 = "upgradecheckworkflowresult";
/*  118 */       str2 = "WorkflowCheckResultBatch";
/*  119 */       str6 = "upgradecheckworkflowresultBatch";
/*  120 */     } else if ("allWorkflow".equals(paramString)) {
/*  121 */       str1 = "upgradecheckworkflowresult";
/*  122 */       str2 = "AllWorkflowResult";
/*  123 */       str6 = "allWorkflow";
/*  124 */     } else if ("mobilemode".equals(paramString)) {
/*  125 */       str1 = "checkmobilemoderesult";
/*  126 */       str2 = "MobileModeReport";
/*  127 */       str6 = "mobilemode";
/*  128 */     } else if ("mode".equals(paramString)) {
/*  129 */       str1 = "upgradecheckmoderesult";
/*  130 */       str2 = "ModeReport";
/*  131 */       str6 = "mode";
/*  132 */     } else if ("modeBatch".equals(paramString)) {
/*  133 */       str1 = "upgradecheckmoderesult";
/*  134 */       str2 = "ModeReportBatch";
/*  135 */       str6 = "modeBatch";
/*      */     } 
/*  137 */     String str7 = GCONST.getRootPath() + "templetecheck" + File.separatorChar + "report" + File.separatorChar + str2 + ".xls";
/*  138 */     String str8 = "select id from " + str1;
/*  139 */     String str9 = " where 1=1 ";
/*  140 */     if ("allWorkflow".equals(paramString)) {
/*  141 */       getReport(paramList, str7, str6, str9);
/*      */       return;
/*      */     } 
/*  144 */     if ("myworkflowcheck".equals(str4)) {
/*  145 */       str9 = str9 + " and content <>'^[\\s\\S]*.*[^\\s][\\s\\S]*$' ";
/*      */     }
/*  147 */     if (!"".equals(str3)) {
/*  148 */       RecordSet recordSet1 = new RecordSet();
/*  149 */       String str = "";
/*  150 */       recordSet1.executeQuery("select id from workflow_nodehtmllayout where workflowid in (" + str3 + ")", new Object[0]);
/*  151 */       while (recordSet1.next()) {
/*  152 */         str = str + recordSet1.getString("id") + ",";
/*      */       }
/*  154 */       if (str.endsWith(",")) {
/*  155 */         str = str.substring(0, str.length() - 1);
/*      */       }
/*  157 */       str9 = str9 + " and nodehtmllayoutid in (" + str + ")";
/*      */     } 
/*  159 */     if (!"".equals(str5)) {
/*  160 */       str9 = str9 + " and ruleid in (" + str5 + ")";
/*      */     }
/*  162 */     str8 = str8 + str9 + " order by id asc ";
/*  163 */     int i = 0;
/*  164 */     int j = 0;
/*  165 */     byte b1 = 0;
/*  166 */     recordSet.execute(str8);
/*  167 */     byte b2 = 0;
/*  168 */     String str10 = "";
/*  169 */     while (recordSet.next()) {
/*  170 */       if (!b2) {
/*  171 */         i = recordSet.getInt("id");
/*      */       }
/*  173 */       b2++;
/*  174 */       if (b2 >= this.everyNum) {
/*  175 */         checkedWorkflowFileAdd();
/*  176 */         j = recordSet.getInt("id");
/*  177 */         str10 = " and u.id>=" + i + " and u.id<=" + j + " ";
/*  178 */         b2 = 0;
/*  179 */         if (b1) {
/*  180 */           str7 = GCONST.getRootPath() + "templetecheck" + File.separatorChar + "report" + File.separatorChar + str2 + "_" + b1 + ".xls";
/*      */         }
/*  182 */         getReport(paramList, str7, str6, str9 + str10);
/*  183 */         b1++; continue;
/*      */       } 
/*  185 */       j = recordSet.getInt("id");
/*  186 */       str10 = " and u.id>=" + i + " and u.id<=" + j + " ";
/*      */     } 
/*      */ 
/*      */     
/*  190 */     if (b2 > 0) {
/*  191 */       checkedWorkflowFileAdd();
/*  192 */       str7 = GCONST.getRootPath() + "templetecheck" + File.separatorChar + "report" + File.separatorChar + str2 + ((b1 == 0) ? "" : ("_" + b1)) + ".xls";
/*  193 */       getReport(paramList, str7, str6, str9 + str10);
/*      */     } 
/*      */     
/*  196 */     if (b2 == 0 && b1 == 0) {
/*  197 */       str7 = GCONST.getRootPath() + "templetecheck" + File.separatorChar + "report" + File.separatorChar + str2 + ".xls";
/*  198 */       getReport(paramList, str7, str6, str9);
/*      */     } 
/*      */   }
/*      */ 
/*      */   
/*      */   public void getReport(List<String> paramList, String paramString1, String paramString2, String paramString3) {
/*      */     try {
/*  205 */       File file = new File(paramString1);
/*  206 */       if (file.exists()) {
/*  207 */         file.delete();
/*      */       }
/*  209 */       getCheckResult(paramString2, paramString3);
/*  210 */       String str = getReportHtml();
/*  211 */       writeReportFile(str, paramString1);
/*  212 */       file = new File(paramString1);
/*  213 */       if (file.exists()) {
/*  214 */         paramList.add(file.getAbsolutePath());
/*      */       }
/*  216 */     } catch (Exception exception) {
/*  217 */       exception.printStackTrace();
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void getCheckResult(String paramString1, String paramString2) {
/*  228 */     this.datas = new ArrayList<>();
/*  229 */     FileUtil fileUtil = new FileUtil();
/*  230 */     RecordSet recordSet = new RecordSet();
/*  231 */     Boolean bool = Boolean.valueOf(false);
/*  232 */     if ("oracle".equalsIgnoreCase(recordSet.getDBType())) {
/*  233 */       bool = Boolean.valueOf(true);
/*      */     }
/*  235 */     String str1 = "";
/*      */     
/*  237 */     String str2 = "";
/*  238 */     if ("mobilemode".equals(paramString1)) {
/*  239 */       str2 = "checkmobilemoderesult u ";
/*  240 */     } else if ("mode".equals(paramString1) || "modeBatch".equals(paramString1)) {
/*  241 */       str2 = "upgradecheckmoderesult u ";
/*  242 */     } else if ("WorkflowReportDetail".equals(paramString1)) {
/*  243 */       str2 = "workflow_nodehtmllayout t,workflow_base t3";
/*  244 */     } else if ("HtmlNodeReportDetail".equals(paramString1) || "NodeChangeReportDetail".equals(paramString1)) {
/*  245 */       str2 = "workflow_nodehtmllayout t,workflow_nodebase t2,workflow_base t3";
/*  246 */     } else if ("WorkflowCustomReport".equals(paramString1)) {
/*  247 */       str2 = "workflow_base";
/*  248 */     } else if ("upgradecheckworkflowresult".equals(paramString1) || "upgradecheckworkflowresultBatch".equals(paramString1) || "allWorkflow".equals(paramString1)) {
/*  249 */       str2 = "upgradecheckworkflowresult u,workflow_nodehtmllayout w";
/*  250 */     } else if ("upgradecheckSpecialFileresult".equalsIgnoreCase(paramString1)) {
/*  251 */       str2 = "upgradecheckresult";
/*  252 */     } else if (!"CustomCheck".equals(paramString1)) {
/*      */       return;
/*      */     } 
/*      */ 
/*      */     
/*  257 */     this.columnsHtml = getReportDescription(paramString1);
/*  258 */     this.columnsHtml += getReportInfo(paramString1);
/*      */     
/*  260 */     if ("mobilemode".equals(paramString1) || "mode".equals(paramString1)) {
/*  261 */       Map<String, String> map; this
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  271 */         .columnsHtml = this.columnsHtml + "     &nbsp;&nbsp;&nbsp;&nbsp;<th width=\"5%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(381923, ThreadVarLanguage.getLang()) + "</th>\n            <th width=\"5%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(10004094, ThreadVarLanguage.getLang()) + "ID</th>\n            <th width=\"14%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(10004095, ThreadVarLanguage.getLang()) + "</th>\n            <th width=\"5%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(10004096, ThreadVarLanguage.getLang()) + "</th>\n            <th width=\"7%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(25391, ThreadVarLanguage.getLang()) + "</th>\n            <th width=\"6%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(19829, ThreadVarLanguage.getLang()) + "</th>\n            <th width=\"7%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(10004097, ThreadVarLanguage.getLang()) + "</th>\n            <th width=\"14%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(388832, ThreadVarLanguage.getLang()) + "</th>\n            <th width=\"11%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(10004098, ThreadVarLanguage.getLang()) + "</th>\n            <th width=\"12%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(22318, ThreadVarLanguage.getLang()) + "</th>\n            <th width=\"19%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(10004099, ThreadVarLanguage.getLang()) + "</th>\n";
/*  272 */       str1 = "select * from " + str2;
/*  273 */       recordSet.execute(str1);
/*  274 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*  275 */       if ("mobilemode".equals(paramString1)) {
/*  276 */         map = CheckRule.getMobileModeErrMap();
/*  277 */         if (!recordSet.next() && 
/*  278 */           map.isEmpty()) {
/*  279 */           map.put("oError", "" + SystemEnv.getHtmlLabelName(10004100, ThreadVarLanguage.getLang()) + "");
/*      */         }
/*      */       } 
/*      */ 
/*      */       
/*  284 */       if ("mode".equals(paramString1)) {
/*  285 */         map = CheckRule.getModeErrMap();
/*  286 */         if (!recordSet.next() && 
/*  287 */           map.isEmpty()) {
/*  288 */           map.put("oError", "" + SystemEnv.getHtmlLabelName(10004101, ThreadVarLanguage.getLang()) + "");
/*      */         }
/*      */       } 
/*      */       
/*  292 */       for (Map.Entry<String, String> entry : map.entrySet()) {
/*  293 */         String str3 = (String)entry.getKey();
/*  294 */         String str4 = (String)entry.getValue();
/*  295 */         LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/*  296 */         linkedHashMap.put(str3, str4);
/*  297 */         this.datas.add(linkedHashMap);
/*      */       } 
/*  299 */       recordSet.execute("select * from " + str2 + paramString2 + " order by workflowname ");
/*  300 */       while (recordSet.next()) {
/*  301 */         LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/*  302 */         linkedHashMap.put("workflowname", Util.formatMultiLang(changeStr3(Util.null2String(recordSet.getString("workflowname"))), "7"));
/*  303 */         linkedHashMap.put("nodehtmllayoutid", changeStr3(Util.null2String(recordSet.getString("nodehtmllayoutid"))));
/*  304 */         linkedHashMap.put("nodename", Util.formatMultiLang(changeStr3(Util.null2String(recordSet.getString("nodename"))), "7"));
/*  305 */         linkedHashMap.put("refertable", changeStr3(Util.null2String(recordSet.getString("refertable"))));
/*  306 */         linkedHashMap.put("filepath", changeStr3(Util.null2String(recordSet.getString("filepath"))));
/*  307 */         linkedHashMap.put("rulename", changeStr3(Util.null2String(recordSet.getString("rulename"))));
/*  308 */         linkedHashMap.put("ruledesc", changeStr3(Util.null2String(recordSet.getString("ruledesc"))));
/*  309 */         String str = changeStr3(Util.null2String(recordSet.getString("content")));
/*  310 */         linkedHashMap.put("content", str);
/*  311 */         linkedHashMap.put("replacecontent", changeStr3(Util.null2String(recordSet.getString("replacecontent"))));
/*  312 */         linkedHashMap.put("matchcontent", str.equals("^[\\s\\S]*.*[^\\s][\\s\\S]*$") ? "" : changeStr3(Util.null2String(recordSet.getString("matchcontent"))));
/*  313 */         linkedHashMap.put("htmlfilecontentscript", changeStr3(Util.null2String(recordSet.getString("sourcecontent"))));
/*  314 */         this.datas.add(linkedHashMap);
/*      */       } 
/*  316 */     } else if ("modeBatch".equals(paramString1)) {
/*  317 */       this
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  325 */         .columnsHtml = this.columnsHtml + "     &nbsp;&nbsp;&nbsp;&nbsp;<th width=\"5%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(381923, ThreadVarLanguage.getLang()) + "</th>\n            <th width=\"5%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(10004094, ThreadVarLanguage.getLang()) + "ID</th>\n            <th width=\"14%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(10004095, ThreadVarLanguage.getLang()) + "</th>\n            <th width=\"5%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(10004096, ThreadVarLanguage.getLang()) + "</th>\n            <th width=\"7%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(25391, ThreadVarLanguage.getLang()) + "</th>\n            <th width=\"8%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(127495, ThreadVarLanguage.getLang()) + "</th>\n            <th width=\"6%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(19829, ThreadVarLanguage.getLang()) + "</th>\n            <th width=\"7%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(10004097, ThreadVarLanguage.getLang()) + "</th>\n            <th width=\"14%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(388832, ThreadVarLanguage.getLang()) + "</th>\n";
/*      */ 
/*      */ 
/*      */       
/*  329 */       str1 = "select * from " + str2;
/*  330 */       recordSet.execute(str1);
/*  331 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*      */       
/*  333 */       Map<String, String> map = CheckRule.getModeErrMap();
/*  334 */       if (!recordSet.next() && 
/*  335 */         map.isEmpty()) {
/*  336 */         map.put("oError", "" + SystemEnv.getHtmlLabelName(10004102, ThreadVarLanguage.getLang()) + "");
/*      */       }
/*      */ 
/*      */       
/*  340 */       for (Map.Entry<String, String> entry : map.entrySet()) {
/*  341 */         String str5 = (String)entry.getKey();
/*  342 */         String str6 = (String)entry.getValue();
/*  343 */         LinkedHashMap<Object, Object> linkedHashMap1 = new LinkedHashMap<>();
/*  344 */         linkedHashMap1.put(str5, str6);
/*  345 */         this.datas.add(linkedHashMap1);
/*      */       } 
/*  347 */       FileCheckReport fileCheckReport = new FileCheckReport();
/*      */       
/*  349 */       String str3 = "";
/*  350 */       String str4 = "";
/*  351 */       LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/*  352 */       byte b1 = 1;
/*  353 */       byte b2 = 1;
/*  354 */       byte b3 = 1;
/*  355 */       recordSet.execute("select * from " + str2 + paramString2 + " order by workflowname ");
/*  356 */       while (recordSet.next()) {
/*      */         
/*  358 */         String str5 = changeStr3(Util.null2String(recordSet.getString("nodehtmllayoutid")));
/*  359 */         String str6 = Util.formatMultiLang(changeStr3(Util.null2String(recordSet.getString("workflowname"))), "7");
/*  360 */         String str7 = changeStr3(Util.null2String(recordSet.getString("refertable")));
/*      */         
/*  362 */         if ("".equals(str3) && "".equals(str4)) {
/*  363 */           str3 = str5;
/*  364 */           str4 = str6;
/*      */         } 
/*      */         
/*  367 */         if (!str3.equals(str5) || !str4.equals(str6)) {
/*  368 */           str3 = str5;
/*  369 */           str4 = str6;
/*  370 */           this.datas.add(linkedHashMap);
/*  371 */           b1 = 1;
/*  372 */           b2 = 1;
/*  373 */           b3 = 1;
/*  374 */           linkedHashMap = new LinkedHashMap<>();
/*      */         } 
/*  376 */         String str8 = changeStr3(Util.null2String(recordSet.getString("rulename")));
/*  377 */         String str9 = changeStr3(Util.null2String(recordSet.getString("ruledesc")));
/*  378 */         String str10 = changeStr3(Util.null2String(recordSet.getString("content")));
/*  379 */         String str11 = changeStr3(Util.null2String(recordSet.getString("filepath")));
/*  380 */         linkedHashMap.put("workflowname", str6);
/*  381 */         linkedHashMap.put("nodehtmllayoutid", str5);
/*  382 */         linkedHashMap.put("nodename", Util.formatMultiLang(changeStr3(Util.null2String(recordSet.getString("nodename"))), "7"));
/*  383 */         linkedHashMap.put("refertable", str7);
/*  384 */         linkedHashMap.put("filepath", str11);
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  389 */         String str12 = "";
/*  390 */         String str13 = "";
/*      */         
/*  392 */         if (!"".equals(str11)) {
/*  393 */           File file = new File(str11);
/*  394 */           if (file.exists()) {
/*  395 */             str13 = fileUtil.readFile(file).toString();
/*      */           }
/*      */         } else {
/*  398 */           str13 = fileCheckReport.getModeHtmlScripts(Util.null2String(recordSet.getString("nodehtmllayoutid")));
/*      */         } 
/*      */         
/*  401 */         Pattern pattern = null;
/*  402 */         Matcher matcher = null;
/*      */         try {
/*  404 */           pattern = Pattern.compile("(?i)<script([\\s\\S]*?</script>|.*?src=.*?>)");
/*  405 */           matcher = pattern.matcher(str13);
/*  406 */         } catch (Exception exception) {
/*  407 */           (new BaseBean()).writeLog("正则匹配出错" + exception.getMessage() + exception);
/*      */         } 
/*  409 */         String str14 = "";
/*  410 */         while (matcher.find()) {
/*  411 */           String str = matcher.group();
/*  412 */           if (str.toLowerCase().contains("src=") && str.toLowerCase().contains(".js") && str.length() < 150) {
/*  413 */             str14 = str14 + "<span style=\"color:red\">" + (new CheckUtil()).changeStr(str) + "</span>"; continue;
/*      */           } 
/*  415 */           str12 = str12 + (new CheckUtil()).changeStr(str);
/*      */         } 
/*      */ 
/*      */         
/*  419 */         linkedHashMap.put("htmlfilecontentscript", str14 + changeStr3(str12));
/*      */         
/*  421 */         if (!Util.null2String((String)linkedHashMap.get("content")).contains(str10 + "<br/>") && !Util.null2String((String)linkedHashMap.get("content")).endsWith(str10)) {
/*  422 */           linkedHashMap.put("rulename", Util.null2String((String)linkedHashMap.get("rulename")) + b1++ + "." + str8 + "<br/>");
/*  423 */           linkedHashMap.put("ruledesc", Util.null2String((String)linkedHashMap.get("ruledesc")) + b2++ + "." + str9 + "<br/>");
/*  424 */           linkedHashMap.put("content", Util.null2String((String)linkedHashMap.get("content")) + b3++ + "." + str10 + "<br/>");
/*      */         } 
/*      */       } 
/*      */ 
/*      */ 
/*      */       
/*  430 */       this.datas.add(linkedHashMap);
/*  431 */     } else if ("WorkflowReportDetail".equals(paramString1)) {
/*  432 */       this
/*  433 */         .columnsHtml = "     &nbsp;&nbsp;&nbsp;&nbsp;<th width=\"40%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(33569, ThreadVarLanguage.getLang()) + "ID</th>\n            <th width=\"60%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(18104, ThreadVarLanguage.getLang()) + "</th>\n";
/*  434 */       str1 = "select distinct workflowid,workflowname from " + str2 + " where t.workflowid=t3.id and syspath is not null and syspath <> '' and isvalid in (1,2) order by workflowid ";
/*  435 */       if (bool.booleanValue()) {
/*  436 */         str1 = "select distinct workflowid,workflowname from " + str2 + " where t.workflowid=t3.id and (syspath is not null or syspath <> '') and isvalid in (1,2) order by workflowid ";
/*      */       }
/*  438 */       recordSet.execute(str1);
/*  439 */       while (recordSet.next()) {
/*  440 */         LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/*  441 */         linkedHashMap.put("workflowid", changeStr3(Util.null2String(recordSet.getString("workflowid"))));
/*  442 */         linkedHashMap.put("workflowname", changeStr3(Util.null2String(recordSet.getString("workflowname"))));
/*  443 */         this.datas.add(linkedHashMap);
/*      */       } 
/*  445 */     } else if ("upgradecheckworkflowresult".equals(paramString1)) {
/*  446 */       this
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  458 */         .columnsHtml = this.columnsHtml + "     &nbsp;&nbsp;&nbsp;&nbsp;<th width=\"5%\" scope=\"col\">ID</th>\n<th width=\"5%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(64, ThreadVarLanguage.getLang()) + "ID</th>\n            <th width=\"5%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(18104, ThreadVarLanguage.getLang()) + "</th>\n            <th width=\"15%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(15070, ThreadVarLanguage.getLang()) + "</th>\n            <th width=\"5%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(19471, ThreadVarLanguage.getLang()) + "</th>\n            <th width=\"20%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(25437, ThreadVarLanguage.getLang()) + "</th>\n            <th width=\"8%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(25391, ThreadVarLanguage.getLang()) + "</th>\n            <th width=\"6%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(19829, ThreadVarLanguage.getLang()) + "</th>\n            <th width=\"7%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(10004097, ThreadVarLanguage.getLang()) + "</th>\n            <th width=\"14%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(388832, ThreadVarLanguage.getLang()) + "</th>\n            <th width=\"11%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(10004098, ThreadVarLanguage.getLang()) + "</th>\n            <th width=\"12%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(22318, ThreadVarLanguage.getLang()) + "</th>\n            <th width=\"7%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(127495, ThreadVarLanguage.getLang()) + "</th>\n";
/*  459 */       str1 = "select * from upgradecheckworkflowresult u " + paramString2;
/*  460 */       recordSet.execute(str1);
/*  461 */       Map<String, String> map = CheckRule.getWfErrMap();
/*  462 */       if (!recordSet.next() && map.isEmpty()) {
/*  463 */         map.put("oError", "" + SystemEnv.getHtmlLabelName(10004103, ThreadVarLanguage.getLang()) + "");
/*      */       }
/*  465 */       for (Map.Entry<String, String> entry : map.entrySet()) {
/*  466 */         String str3 = (String)entry.getKey();
/*  467 */         String str4 = (String)entry.getValue();
/*  468 */         LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/*  469 */         linkedHashMap.put(str3, str4);
/*  470 */         this.datas.add(linkedHashMap);
/*      */       } 
/*  472 */       str1 = "select u.id as upid,nodehtmllayoutid,isactive,workflowname,nodename,filepath,htmlparsescheme,rulename,ruledesc,content,replacecontent,matchcontent from " + str2 + " where u.nodehtmllayoutid=w.id " + paramString2.replace("where", " and") + " order by w.workflowid ,w.nodeid,u.nodehtmllayoutid  ";
/*  473 */       FileCheckReport fileCheckReport = new FileCheckReport();
/*  474 */       recordSet.execute(str1);
/*  475 */       while (recordSet.next()) {
/*  476 */         LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/*  477 */         linkedHashMap.put("uid", changeStr3(Util.null2String(recordSet.getString("upid"))));
/*  478 */         linkedHashMap.put("nodehtmllayoutid", changeStr3(Util.null2String(recordSet.getString("nodehtmllayoutid"))));
/*  479 */         linkedHashMap.put("workflowname", Util.formatMultiLang(changeStr3(Util.null2String(recordSet.getString("workflowname"))), "7"));
/*  480 */         linkedHashMap.put("nodename", Util.formatMultiLang(changeStr3(Util.null2String(recordSet.getString("nodename"))), "7"));
/*  481 */         linkedHashMap.put("htmlparsescheme", fileCheckReport.getHtmlparsescheme(Util.null2String(recordSet.getString("filepath"))));
/*  482 */         linkedHashMap.put("isactive", fileCheckReport.getHtmlLayoutResult(Util.null2String(recordSet.getString("isactive"))));
/*  483 */         String str3 = changeStr3(fileCheckReport.getCheckContent(Util.null2String(recordSet.getString("nodehtmllayoutid"))));
/*  484 */         linkedHashMap.put("filepath", str3);
/*  485 */         linkedHashMap.put("rulename", changeStr3(Util.null2String(recordSet.getString("rulename"))));
/*  486 */         linkedHashMap.put("ruledesc", changeStr3(Util.null2String(recordSet.getString("ruledesc"))));
/*  487 */         String str4 = changeStr3(Util.null2String(recordSet.getString("content")));
/*      */         
/*  489 */         linkedHashMap.put("content", str4);
/*  490 */         linkedHashMap.put("replacecontent", changeStr3(Util.null2String(recordSet.getString("replacecontent"))));
/*  491 */         linkedHashMap.put("matchcontent", str4.equals("^[\\s\\S]*.*[^\\s][\\s\\S]*$") ? "" : changeStr3(Util.null2String(recordSet.getString("matchcontent"))));
/*  492 */         String str5 = "";
/*      */         
/*  494 */         if (!"".equals(str3)) {
/*  495 */           File file = new File(str3);
/*  496 */           if (file.exists()) {
/*  497 */             str5 = fileUtil.readFile(file).toString();
/*      */           }
/*      */         } else {
/*  500 */           str5 = fileCheckReport.getHtmlScripts(Util.null2String(recordSet.getString("nodehtmllayoutid")));
/*      */         } 
/*  502 */         linkedHashMap.put("htmlfilecontentscript", changeStr3(Util.null2String(str5)));
/*  503 */         this.datas.add(linkedHashMap);
/*      */       } 
/*  505 */     } else if ("upgradecheckworkflowresultBatch".equals(paramString1)) {
/*  506 */       this
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  516 */         .columnsHtml = this.columnsHtml + "<th width=\"5%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(64, ThreadVarLanguage.getLang()) + "ID</th>\n            <th width=\"5%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(18104, ThreadVarLanguage.getLang()) + "</th>\n            <th width=\"15%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(15070, ThreadVarLanguage.getLang()) + "</th>\n            <th width=\"5%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(19471, ThreadVarLanguage.getLang()) + "</th>\n            <th width=\"5%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(25437, ThreadVarLanguage.getLang()) + "</th>\n            <th width=\"8%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(25391, ThreadVarLanguage.getLang()) + "</th>\n            <th width=\"7%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(127495, ThreadVarLanguage.getLang()) + "</th>\n            <th width=\"10%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(19829, ThreadVarLanguage.getLang()) + "</th>\n            <th width=\"17%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(10004097, ThreadVarLanguage.getLang()) + "</th>\n            <th width=\"16%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(388832, ThreadVarLanguage.getLang()) + "</th>\n";
/*      */ 
/*      */ 
/*      */       
/*  520 */       str1 = "select * from upgradecheckworkflowresult u " + paramString2;
/*  521 */       recordSet.execute(str1);
/*  522 */       Map<String, String> map = CheckRule.getWfErrMap();
/*  523 */       if (!recordSet.next() && map.isEmpty()) {
/*  524 */         map.put("oError", "" + SystemEnv.getHtmlLabelName(10004103, ThreadVarLanguage.getLang()) + "");
/*      */       }
/*  526 */       for (Map.Entry<String, String> entry : map.entrySet()) {
/*  527 */         String str5 = (String)entry.getKey();
/*  528 */         String str6 = (String)entry.getValue();
/*  529 */         LinkedHashMap<Object, Object> linkedHashMap1 = new LinkedHashMap<>();
/*  530 */         linkedHashMap1.put(str5, str6);
/*  531 */         this.datas.add(linkedHashMap1);
/*      */       } 
/*  533 */       str1 = "select u.id as upid,nodehtmllayoutid,isactive,workflowname,nodename,filepath,htmlparsescheme,rulename,ruledesc,content,replacecontent,matchcontent from " + str2 + " where u.nodehtmllayoutid=w.id  " + paramString2.replace("where", " and") + " order by w.workflowid ,w.nodeid,u.nodehtmllayoutid ";
/*  534 */       FileCheckReport fileCheckReport = new FileCheckReport();
/*  535 */       recordSet.execute(str1);
/*      */       
/*  537 */       String str3 = "";
/*  538 */       String str4 = "";
/*  539 */       LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/*  540 */       byte b1 = 1;
/*  541 */       byte b2 = 1;
/*  542 */       byte b3 = 1;
/*  543 */       while (recordSet.next()) {
/*      */         
/*  545 */         String str5 = changeStr3(Util.null2String(recordSet.getString("nodehtmllayoutid")));
/*  546 */         String str6 = changeStr3(Util.null2String(recordSet.getString("workflowname")));
/*      */         
/*  548 */         if ("".equals(str3) && "".equals(str4)) {
/*  549 */           str3 = str5;
/*  550 */           str4 = str6;
/*      */         } 
/*      */         
/*  553 */         if (!str3.equals(str5) || !str4.equals(str6)) {
/*  554 */           str3 = str5;
/*  555 */           str4 = str6;
/*  556 */           this.datas.add(linkedHashMap);
/*  557 */           b1 = 1;
/*  558 */           b2 = 1;
/*  559 */           b3 = 1;
/*  560 */           linkedHashMap = new LinkedHashMap<>();
/*      */         } 
/*      */         
/*  563 */         String str7 = changeStr3(Util.null2String(recordSet.getString("rulename")));
/*  564 */         String str8 = changeStr3(Util.null2String(recordSet.getString("ruledesc")));
/*  565 */         String str9 = changeStr3(Util.null2String(recordSet.getString("content")));
/*  566 */         String str10 = changeStr3(fileCheckReport.getCheckContent(Util.null2String(recordSet.getString("nodehtmllayoutid"))));
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  571 */         linkedHashMap.put("nodehtmllayoutid", changeStr3(Util.null2String(recordSet.getString("nodehtmllayoutid"))));
/*  572 */         linkedHashMap.put("workflowname", Util.formatMultiLang(Util.null2String(recordSet.getString("workflowname"))));
/*  573 */         linkedHashMap.put("nodename", Util.formatMultiLang(Util.null2String(recordSet.getString("nodename"))));
/*  574 */         linkedHashMap.put("htmlparsescheme", fileCheckReport.getHtmlparsescheme(Util.null2String(recordSet.getString("filepath"))));
/*  575 */         linkedHashMap.put("isactive", fileCheckReport.getHtmlLayoutResult(Util.null2String(recordSet.getString("isactive"))));
/*  576 */         linkedHashMap.put("filepath", str10);
/*      */         
/*  578 */         String str11 = "";
/*  579 */         String str12 = "";
/*      */         
/*  581 */         if (!"".equals(str10)) {
/*  582 */           File file = new File(str10);
/*  583 */           if (file.exists()) {
/*  584 */             str12 = fileUtil.readFile(file).toString();
/*      */           }
/*      */         } else {
/*  587 */           str12 = fileCheckReport.getHtmlScripts(Util.null2String(recordSet.getString("nodehtmllayoutid")));
/*      */         } 
/*      */         
/*  590 */         Pattern pattern = null;
/*  591 */         Matcher matcher = null;
/*      */         try {
/*  593 */           pattern = Pattern.compile("(?i)<script([\\s\\S]*?</script>|.*?src=.*?>)");
/*  594 */           matcher = pattern.matcher(str12);
/*  595 */         } catch (Exception exception) {
/*  596 */           (new BaseBean()).writeLog("正则匹配出错" + exception.getMessage() + exception);
/*      */         } 
/*  598 */         String str13 = "";
/*  599 */         while (matcher.find()) {
/*  600 */           String str = matcher.group();
/*  601 */           if (str.toLowerCase().contains("src=") && str.toLowerCase().contains(".js") && str.length() < 150) {
/*  602 */             str13 = str13 + "<span style=\"color:red\">" + (new CheckUtil()).changeStr(str) + "</span>"; continue;
/*      */           } 
/*  604 */           str11 = str11 + (new CheckUtil()).changeStr(str);
/*      */         } 
/*      */ 
/*      */         
/*  608 */         linkedHashMap.put("htmlfilecontentscript", str13 + changeStr3(str11));
/*      */         
/*  610 */         if (!Util.null2String((String)linkedHashMap.get("content")).contains(str9 + "<br/>") && !Util.null2String((String)linkedHashMap.get("content")).endsWith(str9)) {
/*  611 */           linkedHashMap.put("rulename", Util.null2String((String)linkedHashMap.get("rulename")) + b1++ + "." + str7 + "<br/>");
/*  612 */           linkedHashMap.put("ruledesc", Util.null2String((String)linkedHashMap.get("ruledesc")) + b2++ + "." + str8 + "<br/>");
/*  613 */           linkedHashMap.put("content", Util.null2String((String)linkedHashMap.get("content")) + b3++ + "." + str9 + "<br/>");
/*      */         } 
/*      */       } 
/*      */ 
/*      */ 
/*      */       
/*  619 */       this.datas.add(linkedHashMap);
/*  620 */     } else if ("allWorkflow".equals(paramString1)) {
/*  621 */       this.columnsHtml += "  <th width=\"5%\" scope=\"col\">流程ID</th>\n  <th width=\"5%\" scope=\"col\">流程名称</th>\n  <th width=\"15%\" scope=\"col\">写有js的模板数量</th>\n  <th width=\"35%\" scope=\"col\">命中规则次数详情</th>\n";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  630 */       str1 = "select t.workflowid,t.workflowname,t.matchTimes,t.rulecontent,(select count(distinct nodehtmllayoutid) from upgradecheckworkflowresult where workflowname=t.workflowname)  jslayoutnum from (  select b.workflowid,u.workflowname,count(u.ruleid) as matchTimes,(select content from upgradecheckworkflowrule where id=u.ruleid) as rulecontent \n from upgradecheckworkflowresult u left join workflow_nodehtmllayout b on u.nodehtmllayoutid=b.id  where  b.workflowid is not null and  u.matchcontent is not null " + (recordSet.getDBType().toLowerCase().equals("sqlserver") ? " and DATALENGTH(u.matchcontent)>0 " : "") + paramString2.replace("where", " and") + " group by u.workflowname,b.workflowid,u.ruleid  ) t order by t.workflowid asc ";
/*      */       
/*  632 */       LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/*  633 */       String str = "0";
/*  634 */       recordSet.execute(str1);
/*  635 */       while (recordSet.next()) {
/*  636 */         String str3 = Util.null2String(recordSet.getString("workflowid"));
/*  637 */         String str4 = Util.null2String(recordSet.getString("jslayoutnum"));
/*  638 */         String str5 = changeStr3(Util.null2String(recordSet.getString("workflowname")));
/*  639 */         String str6 = Util.null2String(recordSet.getString("matchTimes"));
/*  640 */         String str7 = changeStr3(Util.null2String(recordSet.getString("rulecontent")));
/*  641 */         if ("0".equals(str)) str = str3; 
/*  642 */         if (!str.equals(str3)) {
/*  643 */           this.datas.add(linkedHashMap);
/*  644 */           str = str3;
/*  645 */           linkedHashMap = new LinkedHashMap<>();
/*      */         } 
/*      */         
/*  648 */         linkedHashMap.put("workflowid", str3);
/*  649 */         linkedHashMap.put("workflowname", str5);
/*  650 */         linkedHashMap.put("jslayoutnum", str4);
/*  651 */         linkedHashMap.put("ruledesc", "命中规则" + str6 + "次======" + str7 + "<br/>" + Util.null2String((String)linkedHashMap.get("ruledesc")));
/*      */       } 
/*      */ 
/*      */       
/*  655 */       if (linkedHashMap.size() != 0) {
/*  656 */         this.datas.add(linkedHashMap);
/*      */       }
/*  658 */     } else if ("upgradecheckSpecialFileresult".equals(paramString1)) {
/*  659 */       this
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  664 */         .columnsHtml = this.columnsHtml + "     &nbsp;&nbsp;&nbsp;&nbsp;<th width=\"5%\" scope=\"col\">ID</th>\n            <th width=\"30%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(25391, ThreadVarLanguage.getLang()) + "</th>\n            <th width=\"10%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(19829, ThreadVarLanguage.getLang()) + "</th>\n            <th width=\"20%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(10004097, ThreadVarLanguage.getLang()) + "</th>\n            <th width=\"12%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(22318, ThreadVarLanguage.getLang()) + "</th>\n            <th width=\"20%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(10004098, ThreadVarLanguage.getLang()) + "</th>\n";
/*  665 */       str1 = "select  * from " + str2 + "  order by id asc ";
/*  666 */       recordSet.execute(str1);
/*  667 */       while (recordSet.next()) {
/*  668 */         LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/*  669 */         linkedHashMap.put("ID", changeStr3(Util.null2String(recordSet.getString("id"))));
/*  670 */         linkedHashMap.put("filepath", changeStr3(Util.null2String(recordSet.getString("filepath"))));
/*  671 */         linkedHashMap.put("rulename", changeStr3(Util.null2String(recordSet.getString("rulename"))));
/*  672 */         linkedHashMap.put("ruledesc", changeStr3(Util.null2String(recordSet.getString("ruledesc"))));
/*  673 */         linkedHashMap.put("matchcontent", changeStr3(Util.null2String(recordSet.getString("matchcontent"))));
/*  674 */         linkedHashMap.put("replacecontent", changeStr3(Util.null2String(recordSet.getString("replacecontent"))));
/*  675 */         this.datas.add(linkedHashMap);
/*      */       } 
/*  677 */     } else if ("HtmlNodeReportDetail".equals(paramString1)) {
/*  678 */       this
/*      */         
/*  680 */         .columnsHtml = " <th width=\"10%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(33569, ThreadVarLanguage.getLang()) + "ID</th>\n<th width=\"15%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(18104, ThreadVarLanguage.getLang()) + "</th>\n<th width=\"15%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(15070, ThreadVarLanguage.getLang()) + "</th>\n";
/*  681 */       str1 = "select distinct workflowid,workflowname,nodename from " + str2 + " where t.nodeid=t2.id and t.workflowid=t3.id  and syspath is not null and syspath <> '' and isvalid in (1,2) and isactive=1 order by t.workflowid ";
/*  682 */       if (bool.booleanValue()) {
/*  683 */         str1 = "select distinct workflowid,workflowname,nodename from " + str2 + " where t.nodeid=t2.id and t.workflowid=t3.id  and (syspath is not null or syspath <> '') and isvalid in (1,2) and isactive=1 order by t.workflowid ";
/*      */       }
/*  685 */       recordSet.execute(str1);
/*  686 */       while (recordSet.next()) {
/*  687 */         LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/*  688 */         linkedHashMap.put("workflowid", changeStr3(Util.null2String(recordSet.getString("workflowid"))));
/*  689 */         linkedHashMap.put("workflowname", changeStr3(Util.null2String(recordSet.getString("workflowname"))));
/*  690 */         linkedHashMap.put("nodename", changeStr3(Util.null2String(recordSet.getString("nodename"))));
/*  691 */         this.datas.add(linkedHashMap);
/*      */       } 
/*  693 */     } else if ("NodeChangeReportDetail".equals(paramString1)) {
/*  694 */       this
/*      */ 
/*      */ 
/*      */         
/*  698 */         .columnsHtml = " <th width=\"10%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(33569, ThreadVarLanguage.getLang()) + "ID</th>\n<th width=\"15%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(18104, ThreadVarLanguage.getLang()) + "</th>\n<th width=\"15%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(15070, ThreadVarLanguage.getLang()) + "</th>\n<th width=\"40%\" scope=\"col\">html" + SystemEnv.getHtmlLabelName(25391, ThreadVarLanguage.getLang()) + "</th>\n<th width=\"10%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(19471, ThreadVarLanguage.getLang()) + "</th>\n";
/*  699 */       str1 = "select type,workflowid,workflowname,nodename,syspath from " + str2 + " where t.nodeid=t2.id and t.workflowid=t3.id  and syspath is not null and syspath <> '' and datajson is null and isvalid in (1,2) and isactive=1 order by t.workflowid ";
/*  700 */       if (bool.booleanValue()) {
/*  701 */         str1 = "select type,workflowid,workflowname,nodename,syspath from " + str2 + " where t.nodeid=t2.id and t.workflowid=t3.id  and (syspath is not null or syspath <> '') and datajson is null and isvalid in (1,2) and isactive=1 order by t.workflowid ";
/*      */       }
/*  703 */       recordSet.execute(str1);
/*  704 */       while (recordSet.next()) {
/*  705 */         LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/*  706 */         linkedHashMap.put("workflowid", changeStr3(Util.null2String(recordSet.getString("workflowid"))));
/*  707 */         linkedHashMap.put("workflowname", changeStr3(Util.null2String(recordSet.getString("workflowname"))));
/*  708 */         linkedHashMap.put("nodename", changeStr3(Util.null2String(recordSet.getString("nodename"))));
/*  709 */         linkedHashMap.put("syspath", changeStr3(Util.null2String(recordSet.getString("syspath"))));
/*  710 */         linkedHashMap.put("type", (new FileCheckReport()).getHtmltype(changeStr3(Util.null2String(recordSet.getString("type")))));
/*  711 */         this.datas.add(linkedHashMap);
/*      */       } 
/*  713 */     } else if ("WorkflowCustomReport".equals(paramString1)) {
/*  714 */       this
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  719 */         .columnsHtml = " <th width=\"10%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(33569, ThreadVarLanguage.getLang()) + "ID</th>\n<th width=\"15%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(18104, ThreadVarLanguage.getLang()) + "</th>\n<th width=\"15%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(32309, ThreadVarLanguage.getLang()) + "</th>\n<th width=\"15%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(507045, ThreadVarLanguage.getLang()) + "</th>\n<th width=\"35%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(10004104, ThreadVarLanguage.getLang()) + "</th>\n<th width=\"10%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(502102, ThreadVarLanguage.getLang()) + "</th>\n";
/*  720 */       str1 = "select * from " + str2 + " where 1=1 and ((custompage is not null and custompage <>'') or (custompage4emoble is not null and custompage4emoble <>''))";
/*  721 */       if (bool.booleanValue()) {
/*  722 */         str1 = "select * from " + str2 + " where 1=1 and ((custompage is not null or custompage <>'') or (custompage4emoble is not null or custompage4emoble <>'')) ";
/*      */       }
/*  724 */       recordSet.execute(str1);
/*  725 */       while (recordSet.next()) {
/*  726 */         LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/*  727 */         PageTransMethod pageTransMethod = new PageTransMethod();
/*  728 */         linkedHashMap.put("workflowid", changeStr3(Util.null2String(recordSet.getString("id"))));
/*  729 */         linkedHashMap.put("workflowname", changeStr3(Util.null2String(recordSet.getString("workflowname"))));
/*  730 */         linkedHashMap.put("custompage", changeStr3(Util.null2String(recordSet.getString("custompage"))));
/*  731 */         linkedHashMap.put("custompage4emoble", changeStr3(Util.null2String(recordSet.getString("custompage4emoble"))));
/*  732 */         linkedHashMap.put("script", pageTransMethod.getScript(Util.null2String(recordSet.getString("id"))));
/*  733 */         linkedHashMap.put("bm", pageTransMethod.getFileCharset(Util.null2String(recordSet.getString("id"))));
/*  734 */         this.datas.add(linkedHashMap);
/*      */       } 
/*  736 */     } else if ("CustomCheck".equals(paramString1)) {
/*  737 */       PortalReportOperation portalReportOperation = new PortalReportOperation();
/*  738 */       JSONObject jSONObject = portalReportOperation.getPortalReportInfo();
/*  739 */       this
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  750 */         .columnsHtml = " <th width=\"10%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(10004105, ThreadVarLanguage.getLang()) + "</th>\n<th width=\"10%\" scope=\"col\">E-Mobile" + SystemEnv.getHtmlLabelName(10004106, ThreadVarLanguage.getLang()) + "</th>\n<th width=\"10%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(10004107, ThreadVarLanguage.getLang()) + "</th>\n<th width=\"10%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(10004108, ThreadVarLanguage.getLang()) + "</th>\n<th width=\"10%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(10004109, ThreadVarLanguage.getLang()) + "</th>\n<th width=\"10%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(10004110, ThreadVarLanguage.getLang()) + "</th>\n<th width=\"10%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(10004111, ThreadVarLanguage.getLang()) + "</th>\n<th width=\"10%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(10004112, ThreadVarLanguage.getLang()) + "</th>\n<th width=\"10%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(10004113, ThreadVarLanguage.getLang()) + "</th>\n<th width=\"10%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(10004114, ThreadVarLanguage.getLang()) + "</th>\n<th width=\"10%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(10004115, ThreadVarLanguage.getLang()) + "</th>\n<th width=\"10%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(10004116, ThreadVarLanguage.getLang()) + "</th>\n";
/*  751 */       StringBuffer stringBuffer = new StringBuffer();
/*  752 */       LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/*  753 */       PageTransMethod pageTransMethod = new PageTransMethod();
/*  754 */       linkedHashMap.put("custompage", changeStr3(Util.null2String(jSONObject.get("custompage"))));
/*  755 */       linkedHashMap.put("emobile", changeStr3(Util.null2String(jSONObject.get("emobile"))));
/*  756 */       linkedHashMap.put("portaltotal", changeStr3(Util.null2String(jSONObject.get("portaltotal"))));
/*  757 */       linkedHashMap.put("portalBeforeLogin", changeStr3(Util.null2String(jSONObject.get("portalBeforeLogin"))));
/*  758 */       linkedHashMap.put("portalBL4E7", changeStr3(Util.null2String(jSONObject.get("portalBL4E7"))));
/*  759 */       linkedHashMap.put("portalBL4E9", changeStr3(Util.null2String(jSONObject.get("portalBL4E9"))));
/*      */       
/*  761 */       linkedHashMap.put("portalAfterLogin", changeStr3(Util.null2String(jSONObject.get("portalAfterLogin"))));
/*  762 */       linkedHashMap.put("portalAL4E7", changeStr3(Util.null2String(jSONObject.get("portalAL4E7"))));
/*  763 */       linkedHashMap.put("portalAL4E9", changeStr3(Util.null2String(jSONObject.get("portalAL4E9"))));
/*      */       
/*  765 */       linkedHashMap.put("portalElement", changeStr3(Util.null2String(jSONObject.get("portalElement"))));
/*  766 */       linkedHashMap.put("portalElement4E7", changeStr3(Util.null2String(jSONObject.get("portalElement4E7"))));
/*  767 */       linkedHashMap.put("portalElement4E9", changeStr3(Util.null2String(jSONObject.get("portalElement4E9"))));
/*  768 */       this.datas.add(linkedHashMap);
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String getCustomCheckResult(String paramString) {
/*  797 */     RecordSet recordSet = new RecordSet();
/*  798 */     StringBuffer stringBuffer = new StringBuffer();
/*  799 */     stringBuffer.append("<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">\n<html xmlns=\"http://www.w3.org/1999/xhtml\">\n<head>\n<meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\" />\n<title>" + 
/*      */ 
/*      */ 
/*      */         
/*  803 */         SystemEnv.getHtmlLabelName(10004117, ThreadVarLanguage.getLang()) + "</title>\n</head>\n<body>\n");
/*      */ 
/*      */ 
/*      */     
/*  807 */     String str1 = "";
/*      */     
/*  809 */     String str2 = getReportInfo(paramString);
/*  810 */     stringBuffer.append(str2 + "<br/>");
/*  811 */     stringBuffer.append("<h2>" + SystemEnv.getHtmlLabelName(10004118, ThreadVarLanguage.getLang()) + "</h2><br/>");
/*      */     
/*  813 */     PageTransMethod pageTransMethod = new PageTransMethod();
/*      */     
/*  815 */     this.datas = new ArrayList<>();
/*  816 */     this.columnsHtml = "";
/*  817 */     this
/*      */ 
/*      */ 
/*      */       
/*  821 */       .columnsHtml = " <th width=\"10%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(33569, ThreadVarLanguage.getLang()) + "ID</th>\n<th width=\"15%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(18104, ThreadVarLanguage.getLang()) + "</th>\n<th width=\"15%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(32309, ThreadVarLanguage.getLang()) + "</th>\n<th width=\"40%\" scope=\"col\">script" + SystemEnv.getHtmlLabelName(10003622, ThreadVarLanguage.getLang()) + "</th>\n<th width=\"10%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(502102, ThreadVarLanguage.getLang()) + "</th>\n";
/*  822 */     str1 = "select  t1.* from workflow_base t1  where 1=1 and custompage is not null " + (recordSet.getDBType().equals("oracle") ? "" : " and custompage<>''");
/*  823 */     boolean bool = recordSet.execute(str1);
/*  824 */     if (!bool) {
/*  825 */       LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/*  826 */       linkedHashMap.put("WFcustompage", "" + SystemEnv.getHtmlLabelName(10004119, ThreadVarLanguage.getLang()) + "sql" + SystemEnv.getHtmlLabelName(10003747, ThreadVarLanguage.getLang()) + "");
/*  827 */       this.datas.add(linkedHashMap);
/*      */     } 
/*  829 */     boolean bool1 = true;
/*  830 */     while (recordSet.next()) {
/*  831 */       bool1 = false;
/*  832 */       LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/*  833 */       linkedHashMap.put("id", changeStr3(Util.null2String(recordSet.getString("id"))));
/*  834 */       linkedHashMap.put("workflowname", changeStr3(Util.null2String(recordSet.getString("workflowname"))));
/*  835 */       linkedHashMap.put("custompage", changeStr3(Util.null2String(recordSet.getString("custompage"))));
/*  836 */       linkedHashMap.put("script", pageTransMethod.getScript(Util.null2String(recordSet.getString("id"))));
/*  837 */       linkedHashMap.put("charset", pageTransMethod.getFileCharset(Util.null2String(recordSet.getString("id"))));
/*  838 */       this.datas.add(linkedHashMap);
/*      */     } 
/*  840 */     if (bool && bool1) {
/*  841 */       LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/*  842 */       linkedHashMap.put("workflowname", changeStr3("" + SystemEnv.getHtmlLabelName(10004120, ThreadVarLanguage.getLang()) + ""));
/*  843 */       this.datas.add(linkedHashMap);
/*      */     } 
/*  845 */     stringBuffer.append(getProtableReportHtml());
/*      */     
/*  847 */     stringBuffer.append("<h2>" + SystemEnv.getHtmlLabelName(10004121, ThreadVarLanguage.getLang()) + "E-Mobile" + SystemEnv.getHtmlLabelName(10004122, ThreadVarLanguage.getLang()) + "</h2><br/>");
/*  848 */     this.datas = new ArrayList<>();
/*  849 */     this.columnsHtml = "";
/*  850 */     this
/*      */ 
/*      */       
/*  853 */       .columnsHtml = " <th width=\"10%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(18700, ThreadVarLanguage.getLang()) + "ID</th>\n<th width=\"15%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(33522, ThreadVarLanguage.getLang()) + "</th>\n<th width=\"15%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(10004123, ThreadVarLanguage.getLang()) + "</th>\n<th width=\"40%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(81710, ThreadVarLanguage.getLang()) + "</th>\n";
/*  854 */     MyEmobileReportOperation myEmobileReportOperation = new MyEmobileReportOperation();
/*  855 */     List<Map<String, String>> list = myEmobileReportOperation.getPluginXmlInfo();
/*  856 */     for (byte b1 = 0; b1 < list.size(); b1++) {
/*  857 */       LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/*  858 */       Map map = list.get(b1);
/*  859 */       linkedHashMap.put("id", changeStr3(Util.null2String((String)map.get("id"))));
/*  860 */       linkedHashMap.put("name", changeStr3(Util.null2String((String)map.get("name"))));
/*  861 */       linkedHashMap.put("isOK", Util.null2String((String)map.get("isOK")));
/*  862 */       linkedHashMap.put("description", changeStr3(Util.null2String((String)map.get("description"))));
/*  863 */       this.datas.add(linkedHashMap);
/*      */     } 
/*  865 */     stringBuffer.append(getProtableReportHtml());
/*      */     
/*  867 */     stringBuffer.append("<h2>" + SystemEnv.getHtmlLabelName(10004124, ThreadVarLanguage.getLang()) + "</h2><br/>");
/*      */     
/*  869 */     PortalReportOperation portalReportOperation = new PortalReportOperation();
/*  870 */     this.datas = new ArrayList<>();
/*  871 */     this.columnsHtml = "";
/*  872 */     this
/*      */       
/*  874 */       .columnsHtml = " <th width=\"10%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(582, ThreadVarLanguage.getLang()) + "ID</th>\n<th width=\"15%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(32533, ThreadVarLanguage.getLang()) + "</th>\n<th width=\"15%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(10004123, ThreadVarLanguage.getLang()) + "</th>\n";
/*  875 */     str1 = "select  * from  SystemLoginTemplate ";
/*  876 */     recordSet.execute(str1);
/*  877 */     while (recordSet.next()) {
/*  878 */       LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/*  879 */       linkedHashMap.put("LOGINTEMPLATEID", changeStr3(Util.null2String(recordSet.getString("LOGINTEMPLATEID"))));
/*  880 */       linkedHashMap.put("logintemplatename", changeStr3(Util.null2String(recordSet.getString("logintemplatename"))));
/*  881 */       linkedHashMap.put("LOGINTEMP", portalReportOperation.getPortalResult(Util.null2String(recordSet.getString("LOGINTEMPLATEID")), "SystemLoginTemplate"));
/*  882 */       this.datas.add(linkedHashMap);
/*      */     } 
/*  884 */     stringBuffer.append(getProtableReportHtml());
/*      */     
/*  886 */     stringBuffer.append("<h2>" + SystemEnv.getHtmlLabelName(10004125, ThreadVarLanguage.getLang()) + "</h2><br/>");
/*      */     
/*  888 */     this.datas = new ArrayList<>();
/*  889 */     this.columnsHtml = "";
/*  890 */     this
/*      */ 
/*      */       
/*  893 */       .columnsHtml = " <th width=\"10%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(33677, ThreadVarLanguage.getLang()) + "ID</th>\n<th width=\"15%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(10004126, ThreadVarLanguage.getLang()) + "</th>\n<th width=\"15%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(10004127, ThreadVarLanguage.getLang()) + "</th>\n<th width=\"40%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(10004123, ThreadVarLanguage.getLang()) + "</th>\n";
/*  894 */     str1 = " select * from  hpinfo where subcompanyid=-1  and infoname is not null";
/*  895 */     recordSet.execute(str1);
/*      */     
/*  897 */     while (recordSet.next()) {
/*  898 */       LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/*  899 */       linkedHashMap.put("ID", changeStr3(Util.null2String(recordSet.getString("ID"))));
/*  900 */       linkedHashMap.put("infoname", changeStr3(Util.null2String(recordSet.getString("infoname"))));
/*  901 */       linkedHashMap.put("subcompanyname", portalReportOperation.getPortalDepartment(Util.null2String(recordSet.getString("subcompanyid"))));
/*  902 */       linkedHashMap.put("efftive", portalReportOperation.getPortalResult(Util.null2String(recordSet.getString("id")), "SystemTemplate"));
/*  903 */       this.datas.add(linkedHashMap);
/*      */     } 
/*  905 */     stringBuffer.append(getProtableReportHtml());
/*      */ 
/*      */     
/*  908 */     stringBuffer.append("<h2>" + SystemEnv.getHtmlLabelName(10004128, ThreadVarLanguage.getLang()) + "</h2><br/>");
/*      */     
/*  910 */     this.datas = new ArrayList<>();
/*  911 */     this.columnsHtml = "";
/*  912 */     this
/*      */       
/*  914 */       .columnsHtml = "<th width=\"15%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(32533, ThreadVarLanguage.getLang()) + "</th>\n<th width=\"15%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(10004127, ThreadVarLanguage.getLang()) + "</th>\n<th width=\"15%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(10004123, ThreadVarLanguage.getLang()) + "</th>\n";
/*  915 */     str1 = "select  distinct templatename,companyid,companyid  from  SystemTemplate ";
/*  916 */     recordSet.execute(str1);
/*  917 */     while (recordSet.next()) {
/*  918 */       LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/*  919 */       linkedHashMap.put("templatename", changeStr3(Util.null2String(recordSet.getString("templatename"))));
/*  920 */       linkedHashMap.put("companyname", portalReportOperation.getPortalDepartment(Util.null2String(recordSet.getString("companyid"))));
/*  921 */       linkedHashMap.put("efftive", portalReportOperation.getPortalResult(Util.null2String(recordSet.getString("companyid")), "SystemTemplate"));
/*  922 */       this.datas.add(linkedHashMap);
/*      */     } 
/*  924 */     stringBuffer.append(getProtableReportHtml());
/*      */     
/*  926 */     stringBuffer.append("<h2>" + SystemEnv.getHtmlLabelName(10004129, ThreadVarLanguage.getLang()) + "</h2><br/>");
/*      */     
/*  928 */     this.datas = new ArrayList<>();
/*  929 */     this.columnsHtml = "";
/*  930 */     this
/*      */ 
/*      */       
/*  933 */       .columnsHtml = " <th width=\"10%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(33677, ThreadVarLanguage.getLang()) + "ID</th>\n<th width=\"15%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(10004126, ThreadVarLanguage.getLang()) + "</th>\n<th width=\"15%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(10004127, ThreadVarLanguage.getLang()) + "</th>\n<th width=\"40%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(10004123, ThreadVarLanguage.getLang()) + "</th>\n";
/*  934 */     str1 = " select * from  hpinfo where subcompanyid != -1 ";
/*  935 */     if ("sqlserver".equals(recordSet.getDBType())) {
/*  936 */       str1 = str1 + "  and subcompanyid!= -1 and  infoname != ''";
/*      */     } else {
/*  938 */       str1 = str1 + "  and subcompanyid!= -1 and  infoname is not null";
/*      */     } 
/*  940 */     recordSet.execute(str1);
/*      */     
/*  942 */     while (recordSet.next()) {
/*  943 */       LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/*  944 */       linkedHashMap.put("ID", changeStr3(Util.null2String(recordSet.getString("ID"))));
/*  945 */       linkedHashMap.put("infoname", changeStr3(Util.formatMultiLang(Util.null2String(recordSet.getString("infoname"), "7"))));
/*  946 */       linkedHashMap.put("subcompanyname", portalReportOperation.getPortalDepartment(Util.formatMultiLang(Util.null2String(recordSet.getString("subcompanyid"), "7"))));
/*  947 */       linkedHashMap.put("efftive", portalReportOperation.getPortalResult(Util.null2String(recordSet.getString("id")), "SystemTemplate"));
/*  948 */       this.datas.add(linkedHashMap);
/*      */     } 
/*  950 */     stringBuffer.append(getProtableReportHtml());
/*      */     
/*  952 */     stringBuffer.append("<h2>" + SystemEnv.getHtmlLabelName(10004130, ThreadVarLanguage.getLang()) + "</h2><br/>");
/*      */     
/*  954 */     this.datas = new ArrayList<>();
/*  955 */     this.columnsHtml = "";
/*  956 */     this
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  961 */       .columnsHtml = " <th width=\"10%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(19408, ThreadVarLanguage.getLang()) + "ID</th>\n<th width=\"15%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(33838, ThreadVarLanguage.getLang()) + "</th>\n<th width=\"15%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(25005, ThreadVarLanguage.getLang()) + "</th>\n<th width=\"15%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(32533, ThreadVarLanguage.getLang()) + "</th>\n<th width=\"15%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(141, ThreadVarLanguage.getLang()) + "</th>\n<th width=\"10%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(10004123, ThreadVarLanguage.getLang()) + "</th>\n";
/*  962 */     str1 = " select * from  hpbaseelement where 1=1 ";
/*  963 */     str1 = str1 + " and  loginview != '4' ";
/*  964 */     String str3 = "'fnaBudgetAssistant','fnaBudgetAssistant1'";
/*  965 */     String str4 = Util.null2String((new BaseBean()).getPropValue("disableelements", "ebaseids"));
/*  966 */     String[] arrayOfString = Util.TokenizerStringNew(str4, ","); byte b2; int i;
/*  967 */     for (b2 = 0, i = arrayOfString.length; b2 < i; b2++) {
/*  968 */       str3 = str3 + ",'" + arrayOfString[b2] + "'";
/*      */     }
/*  970 */     if (!"".equals(str3)) str1 = str1 + " and id not in(" + str3 + ")"; 
/*  971 */     bool = recordSet.execute(str1);
/*  972 */     if (!bool) {
/*  973 */       LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/*  974 */       linkedHashMap.put("hpbaseelement", "" + SystemEnv.getHtmlLabelName(10004131, ThreadVarLanguage.getLang()) + "ql" + SystemEnv.getHtmlLabelName(10003747, ThreadVarLanguage.getLang()) + "");
/*  975 */       this.datas.add(linkedHashMap);
/*      */     } 
/*      */     
/*  978 */     while (recordSet.next()) {
/*  979 */       LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/*  980 */       linkedHashMap.put("ID", changeStr3(Util.null2String(recordSet.getString("ID"))));
/*  981 */       linkedHashMap.put("custitle", changeStr3(Util.formatMultiLang(Util.null2String(recordSet.getString("custitle"), "7"))));
/*  982 */       linkedHashMap.put("isuse", portalReportOperation.getElementIsuse(Util.null2String(recordSet.getString("isuse"))));
/*  983 */       linkedHashMap.put("name", Util.formatMultiLang(portalReportOperation.getPortal(Util.null2String(recordSet.getString("id"), "7"))));
/*  984 */       linkedHashMap.put("subcompanyname", Util.formatMultiLang(portalReportOperation.getSubCompany(Util.null2String(recordSet.getString("ID"))), "7"));
/*  985 */       linkedHashMap.put("efftive", portalReportOperation.getPortalResult(Util.null2String(recordSet.getString("id")), "hpbaseelement"));
/*  986 */       this.datas.add(linkedHashMap);
/*      */     } 
/*  988 */     stringBuffer.append(getProtableReportHtml());
/*  989 */     stringBuffer.append("<h2>" + SystemEnv.getHtmlLabelName(10004132, ThreadVarLanguage.getLang()) + "</h2><br/>");
/*      */     
/*  991 */     this.datas = new ArrayList<>();
/*  992 */     this.columnsHtml = "";
/*  993 */     this
/*      */ 
/*      */       
/*  996 */       .columnsHtml = " <th width=\"20%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(23963, ThreadVarLanguage.getLang()) + "</th>\n<th width=\"20%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(10004132, ThreadVarLanguage.getLang()) + "</th>\n<th width=\"20%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(10004133, ThreadVarLanguage.getLang()) + "/" + SystemEnv.getHtmlLabelName(10004134, ThreadVarLanguage.getLang()) + "</th>\n<th width=\"20%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(129703, ThreadVarLanguage.getLang()) + "</th>\n";
/*  997 */     str1 = " select * from datasourcesetting";
/*  998 */     recordSet.execute(str1);
/*      */     
/* 1000 */     while (recordSet.next()) {
/* 1001 */       LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/* 1002 */       linkedHashMap.put("datasourcename", changeStr3(Util.null2String(recordSet.getString("datasourcename"))));
/* 1003 */       linkedHashMap.put("type", changeStr3(Util.null2String(recordSet.getString("type"))));
/* 1004 */       linkedHashMap.put("iscluster", portalReportOperation.getIsCluster(Util.null2String(recordSet.getString("iscluster"))));
/* 1005 */       linkedHashMap.put("usepool", portalReportOperation.getUsePool(Util.null2String(recordSet.getString("usepool"))));
/* 1006 */       this.datas.add(linkedHashMap);
/*      */     } 
/* 1008 */     stringBuffer.append(getProtableReportHtml());
/*      */ 
/*      */     
/* 1011 */     stringBuffer.append("<h2>" + SystemEnv.getHtmlLabelName(10004135, ThreadVarLanguage.getLang()) + "</h2><br/>");
/*      */     
/* 1013 */     this.datas = new ArrayList<>();
/* 1014 */     this.columnsHtml = "";
/* 1015 */     this
/*      */       
/* 1017 */       .columnsHtml = " <th width=\"20%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(22009, ThreadVarLanguage.getLang()) + "</th>\n<th width=\"20%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(22256, ThreadVarLanguage.getLang()) + "</th>\n<th width=\"20%\" scope=\"col\">" + SystemEnv.getHtmlLabelName(516884, ThreadVarLanguage.getLang()) + "/" + SystemEnv.getHtmlLabelName(15586, ThreadVarLanguage.getLang()) + "</th>\n";
/*      */     
/* 1019 */     String str5 = " a.*,b.formname ";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/* 1042 */     String str6 = " ((select d.id,      d.dmlactionname as actionname,d.typename,      d.formid,      d.isbill,      d.datasourceid,      '1' as fromtype,      '" + SystemEnv.getHtmlLabelName(82986, 7) + "' as fromtypename from formactionset d  union all select s.id,       s.actionname,s.typename,       s.formid,       s.isbill,       '' as datasourceid,       '2' as fromtype,      '" + SystemEnv.getHtmlLabelName(82987, 7) + "' as fromtypename  from wsformactionset s  union all select e.id as id,       e.actionname,'' as typename,       e.formid,       e.isbill,       '' as datasourceid,       '4' as fromtype,      '" + SystemEnv.getHtmlLabelName(381878, 7) + "' as fromtypename  from esbformactionset e ";
/*      */     
/* 1044 */     if (recordSet.getDBType().equals("oracle")) {
/* 1045 */       str6 = str6 + " union all select s.id,nvl(s.actionshowname,actionname) as actionname,typename,0 as formid,0 as isbill,'' as datasourceid,'3' as fromtype, '" + SystemEnv.getHtmlLabelName(82988, 7) + "' as fromtypename ";
/* 1046 */     } else if (recordSet.getDBType().equals("mysql")) {
/* 1047 */       str6 = str6 + " union all select s.id,ifnull(s.actionshowname,actionname) as actionname,typename,0 as formid,0 as isbill,'' as datasourceid,'3' as fromtype, '" + SystemEnv.getHtmlLabelName(82988, 7) + "' as fromtypename ";
/*      */     } else {
/* 1049 */       str6 = str6 + " union all select s.id,isnull(s.actionshowname,actionname) as actionname,typename,0 as formid,0 as isbill,'' as datasourceid,'3' as fromtype, '" + SystemEnv.getHtmlLabelName(82988, 7) + "' as fromtypename ";
/* 1050 */     }  str6 = str6 + " from actionsetting s) a left outer  join (select id,formname,0 as isbill from workflow_formbase c union all select c.id,h.labelname as formname,1 as isbill from workflow_bill c ,htmllabelinfo h where c.namelabel=h.indexid and h.languageid=7) b on a.formid=b.id and a.isbill=b.isbill) ";
/* 1051 */     SplitPageTransmethod splitPageTransmethod = new SplitPageTransmethod();
/* 1052 */     str1 = " select " + str5 + " from " + str6;
/* 1053 */     (new BaseBean()).writeLog("自定义接口查询语句:" + str1);
/* 1054 */     bool = recordSet.execute(str1);
/* 1055 */     if (!bool) {
/* 1056 */       LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/* 1057 */       linkedHashMap.put("custemInterfaceSql", "" + SystemEnv.getHtmlLabelName(10004136, ThreadVarLanguage.getLang()) + "sql" + SystemEnv.getHtmlLabelName(10003747, ThreadVarLanguage.getLang()) + "");
/* 1058 */       this.datas.add(linkedHashMap);
/*      */     } 
/* 1060 */     while (recordSet.next()) {
/* 1061 */       LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/* 1062 */       linkedHashMap.put("actionname", changeStr3(Util.null2String(recordSet.getString("actionname"))));
/* 1063 */       linkedHashMap.put("fromtypename", changeStr3(Util.null2String(recordSet.getString("fromtypename"))));
/*      */       try {
/* 1065 */         linkedHashMap.put("useworkflow", splitPageTransmethod.getActionWorkflowInfo(Util.null2String(recordSet.getString("id")), Util.null2String(recordSet.getString("fromtype") + "+" + recordSet.getString("actionname"))));
/* 1066 */       } catch (Exception exception) {
/* 1067 */         linkedHashMap.put("useworkflow", "");
/*      */       } 
/* 1069 */       this.datas.add(linkedHashMap);
/*      */     } 
/* 1071 */     stringBuffer.append(getProtableReportHtml());
/*      */     
/* 1073 */     stringBuffer.append("</body>\n</html>\n");
/*      */     
/* 1075 */     return stringBuffer.toString();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String getReportHtml() {
/* 1084 */     StringBuilder stringBuilder = new StringBuilder();
/* 1085 */     stringBuilder.append("<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">\n<html xmlns=\"http://www.w3.org/1999/xhtml\">\n<head>\n<meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\" />\n<title>" + 
/*      */ 
/*      */ 
/*      */         
/* 1089 */         SystemEnv.getHtmlLabelName(10004117, ThreadVarLanguage.getLang()) + "</title>\n</head>\n<body>\n<table border=1px solid; cellspacing=0px;>\n    <thead>\n    <tr>\n" + this.columnsHtml + "        </tr>\n    </thead>\n    <tbody>\n");
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/* 1099 */     if (this.datas.size() > 0) {
/* 1100 */       StringBuilder stringBuilder1 = new StringBuilder();
/* 1101 */       StringBuilder stringBuilder2 = new StringBuilder();
/* 1102 */       byte b1 = 0;
/* 1103 */       byte b2 = 0;
/* 1104 */       String str1 = "";
/* 1105 */       String str2 = "";
/* 1106 */       for (Map<String, String> map : this.datas) {
/* 1107 */         b1++;
/*      */         
/* 1109 */         b2++;
/* 1110 */         stringBuilder1.append("<tr style=\"height:50px\">\n");
/* 1111 */         for (String str : map.keySet()) {
/* 1112 */           if (b1 == 1 && str.equals("htmlfilecontentscript")) {
/* 1113 */             str1 = Util.formatMultiLang((String)map.get(str), "7");
/* 1114 */             stringBuilder1.append("        <td rowspan='01532_content_Count'>" + Util.formatMultiLang((String)map.get(str), "7") + "</td>\n"); continue;
/*      */           } 
/* 1116 */           if (b2 == 1 && str.equals("workflowname")) {
/* 1117 */             str2 = Util.formatMultiLang((String)map.get(str), "7");
/* 1118 */             stringBuilder1.append("        <td rowspan='01532_workflowname_Count'>" + Util.formatMultiLang((String)map.get(str), "7") + "</td>\n"); continue;
/* 1119 */           }  if (b1 > 1 && str.equals("htmlfilecontentscript")) {
/* 1120 */             if (str1.length() <= 0 || !str1.equals(Util.formatMultiLang((String)map.get(str), "7"))) {
/* 1121 */               stringBuilder2 = new StringBuilder(stringBuilder2.toString().replace("01532_content_Count", String.valueOf(b1 - 1)));
/* 1122 */               b1 = 1;
/* 1123 */               str1 = Util.formatMultiLang((String)map.get(str), "7");
/* 1124 */               stringBuilder1.append("        <td rowspan='01532_content_Count'>" + Util.formatMultiLang((String)map.get(str), "7") + "</td>\n");
/*      */             } 
/*      */             continue;
/*      */           } 
/* 1128 */           if (b2 > 1 && str.equals("workflowname")) {
/* 1129 */             if (str2.length() <= 0 || !str2.equals(Util.formatMultiLang((String)map.get(str), "7"))) {
/* 1130 */               stringBuilder2 = new StringBuilder(stringBuilder2.toString().replace("01532_workflowname_Count", String.valueOf(b2 - 1)));
/* 1131 */               b2 = 1;
/* 1132 */               str2 = Util.formatMultiLang((String)map.get(str), "7");
/* 1133 */               stringBuilder1.append("        <td rowspan='01532_workflowname_Count'>" + Util.formatMultiLang((String)map.get(str), "7") + "</td>\n");
/*      */             } 
/*      */             
/*      */             continue;
/*      */           } 
/* 1138 */           stringBuilder1.append("        <td>" + Util.formatMultiLang((String)map.get(str), "7") + "</td>\n");
/*      */         } 
/*      */ 
/*      */         
/* 1142 */         stringBuilder2.append(stringBuilder1.toString());
/* 1143 */         stringBuilder2.append("</tr>\n");
/* 1144 */         stringBuilder1 = new StringBuilder();
/*      */       } 
/*      */       
/* 1147 */       stringBuilder.append(stringBuilder2.toString().replace("01532_content_Count", String.valueOf(b1)).replace("01532_workflowname_Count", String.valueOf(b2)));
/*      */     } 
/* 1149 */     stringBuilder.append("  </tbody>\n</table>\n</body>\n</html>\n");
/*      */ 
/*      */ 
/*      */     
/* 1153 */     return stringBuilder.toString();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String getProtableReportHtml() {
/* 1163 */     StringBuilder stringBuilder = new StringBuilder();
/*      */     
/* 1165 */     stringBuilder.append("<table border=1px solid; cellspacing=0px;>\n    <thead>\n    <tr>\n" + this.columnsHtml + "        </tr>\n    </thead>\n    <tbody>\n");
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/* 1172 */     if (this.datas.size() > 0) {
/* 1173 */       for (Map<String, String> map : this.datas) {
/* 1174 */         stringBuilder.append("<tr>\n");
/* 1175 */         for (String str : map.keySet()) {
/* 1176 */           stringBuilder.append("        <td>" + Util.formatMultiLang((String)map.get(str), "7") + "</td>\n");
/*      */         }
/* 1178 */         stringBuilder.append("     </tr>\n");
/*      */       } 
/*      */     }
/* 1181 */     stringBuilder.append("  </tbody>\n</table> <br/><br/>\n");
/*      */     
/* 1183 */     return stringBuilder.toString();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void writeReportFile(String paramString1, String paramString2) {
/* 1193 */     byte[] arrayOfByte = paramString1.getBytes();
/* 1194 */     FileOutputStream fileOutputStream = null;
/* 1195 */     BufferedWriter bufferedWriter = null;
/* 1196 */     if (arrayOfByte.length <= 0)
/* 1197 */       return;  File file1 = new File(paramString2);
/* 1198 */     File file2 = new File(GCONST.getRootPath() + File.separatorChar + "templetecheck" + File.separatorChar + "report" + File.separatorChar);
/* 1199 */     file2.mkdirs();
/* 1200 */     if (file1.exists()) {
/* 1201 */       file1.delete();
/*      */     }
/*      */     try {
/* 1204 */       file1.createNewFile();
/* 1205 */     } catch (IOException iOException) {
/* 1206 */       iOException.printStackTrace();
/*      */     } 
/*      */     try {
/* 1209 */       fileOutputStream = new FileOutputStream(paramString2);
/* 1210 */       bufferedWriter = new BufferedWriter(new OutputStreamWriter(fileOutputStream, "UTF-8"));
/* 1211 */       bufferedWriter.write(paramString1);
/* 1212 */     } catch (FileNotFoundException fileNotFoundException) {
/* 1213 */       fileNotFoundException.printStackTrace();
/* 1214 */     } catch (IOException iOException) {
/* 1215 */       iOException.printStackTrace();
/*      */     } finally {
/* 1217 */       if (bufferedWriter != null) {
/*      */         try {
/* 1219 */           bufferedWriter.close();
/* 1220 */         } catch (IOException iOException) {
/* 1221 */           iOException.printStackTrace();
/*      */         } 
/*      */       }
/* 1224 */       if (fileOutputStream != null) {
/*      */         try {
/* 1226 */           fileOutputStream.close();
/* 1227 */         } catch (IOException iOException) {
/* 1228 */           iOException.printStackTrace();
/*      */         } 
/*      */       }
/*      */     } 
/*      */   }
/*      */   
/*      */   public String getReportInfo(String paramString) {
/* 1235 */     String str = "";
/* 1236 */     if ("mobilemode".equals(paramString)) {
/* 1237 */       MobileModeReportOperation mobileModeReportOperation = new MobileModeReportOperation();
/* 1238 */       JSONObject jSONObject = mobileModeReportOperation.getMobileModeReportInfo();
/* 1239 */       String str1 = jSONObject.get("mobilemodeskin").toString();
/* 1240 */       String str2 = jSONObject.get("mobilemodeapp").toString();
/* 1241 */       String str3 = jSONObject.get("mobilemodepage").toString();
/* 1242 */       String str4 = jSONObject.get("mobileexpendcomponent").toString();
/* 1243 */       String str5 = jSONObject.get("mobileexpendcomponent_html").toString();
/* 1244 */       String str6 = jSONObject.get("mobileexpendcomponent_sql").toString();
/* 1245 */       str = str + "" + SystemEnv.getHtmlLabelName(10004137, ThreadVarLanguage.getLang()) + "" + str1 + "" + SystemEnv.getHtmlLabelName(27591, ThreadVarLanguage.getLang()) + "<br>\n";
/* 1246 */       str = str + "" + SystemEnv.getHtmlLabelName(10004138, ThreadVarLanguage.getLang()) + "" + str2 + "" + SystemEnv.getHtmlLabelName(27591, ThreadVarLanguage.getLang()) + "<br>\n";
/* 1247 */       str = str + "&nbsp;&nbsp;&nbsp;&nbsp;" + SystemEnv.getHtmlLabelName(10004139, ThreadVarLanguage.getLang()) + "" + str3 + "" + SystemEnv.getHtmlLabelName(27591, ThreadVarLanguage.getLang()) + "<br>\n";
/* 1248 */       str = str + "&nbsp;&nbsp;&nbsp;&nbsp;" + SystemEnv.getHtmlLabelName(10004140, ThreadVarLanguage.getLang()) + "" + str4 + "" + SystemEnv.getHtmlLabelName(27591, ThreadVarLanguage.getLang()) + "<br>\n";
/* 1249 */       str = str + "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" + SystemEnv.getHtmlLabelName(10004141, ThreadVarLanguage.getLang()) + "HTML" + SystemEnv.getHtmlLabelName(10004142, ThreadVarLanguage.getLang()) + "" + str5 + "" + SystemEnv.getHtmlLabelName(27591, ThreadVarLanguage.getLang()) + "<br>\n";
/* 1250 */       str = str + "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" + SystemEnv.getHtmlLabelName(10004141, ThreadVarLanguage.getLang()) + "SQL" + SystemEnv.getHtmlLabelName(10004143, ThreadVarLanguage.getLang()) + "" + str6 + "" + SystemEnv.getHtmlLabelName(27591, ThreadVarLanguage.getLang()) + "<br>\n";
/* 1251 */     } else if ("mode".equals(paramString) || "modeBatch".equals(paramString)) {
/* 1252 */       ModeReportOperation modeReportOperation = new ModeReportOperation();
/* 1253 */       JSONObject jSONObject = modeReportOperation.getModeReportInfo();
/* 1254 */       String str1 = jSONObject.get("modeapp").toString();
/* 1255 */       String str2 = jSONObject.get("mode").toString();
/* 1256 */       String str3 = jSONObject.get("html").toString();
/* 1257 */       String str4 = jSONObject.get("excel").toString();
/* 1258 */       String str5 = jSONObject.get("pageExpand").toString();
/* 1259 */       String str6 = jSONObject.get("customSearch").toString();
/* 1260 */       String str7 = jSONObject.get("customSearchForSql").toString();
/* 1261 */       String str8 = jSONObject.get("customSearchButton").toString();
/* 1262 */       String str9 = jSONObject.get("customBrowser").toString();
/* 1263 */       String str10 = jSONObject.get("customBrowserForSql").toString();
/* 1264 */       str = str + "" + SystemEnv.getHtmlLabelName(10004144, ThreadVarLanguage.getLang()) + "" + str1 + "" + SystemEnv.getHtmlLabelName(27591, ThreadVarLanguage.getLang()) + "<br>\n";
/* 1265 */       str = str + "" + SystemEnv.getHtmlLabelName(10004145, ThreadVarLanguage.getLang()) + "" + str2 + "" + SystemEnv.getHtmlLabelName(27591, ThreadVarLanguage.getLang()) + "<br>\n";
/* 1266 */       str = str + "&nbsp;&nbsp;&nbsp;&nbsp;" + SystemEnv.getHtmlLabelName(10004146, ThreadVarLanguage.getLang()) + "HTML" + SystemEnv.getHtmlLabelName(10004147, ThreadVarLanguage.getLang()) + "" + str3 + "" + SystemEnv.getHtmlLabelName(27591, ThreadVarLanguage.getLang()) + "<br>\n";
/* 1267 */       str = str + "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" + SystemEnv.getHtmlLabelName(10004148, ThreadVarLanguage.getLang()) + "EXCEL" + SystemEnv.getHtmlLabelName(10004147, ThreadVarLanguage.getLang()) + "" + str4 + "" + SystemEnv.getHtmlLabelName(27591, ThreadVarLanguage.getLang()) + "<br>\n";
/* 1268 */       str = str + "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" + SystemEnv.getHtmlLabelName(10004149, ThreadVarLanguage.getLang()) + "" + str5 + "" + SystemEnv.getHtmlLabelName(27591, ThreadVarLanguage.getLang()) + "<br>\n";
/* 1269 */       str = str + "&nbsp;&nbsp;&nbsp;&nbsp;" + SystemEnv.getHtmlLabelName(10004150, ThreadVarLanguage.getLang()) + "" + str6 + "" + SystemEnv.getHtmlLabelName(27591, ThreadVarLanguage.getLang()) + "<br>\n";
/* 1270 */       str = str + "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" + SystemEnv.getHtmlLabelName(10004151, ThreadVarLanguage.getLang()) + "SQL" + SystemEnv.getHtmlLabelName(10004152, ThreadVarLanguage.getLang()) + "" + str7 + "" + SystemEnv.getHtmlLabelName(27591, ThreadVarLanguage.getLang()) + "<br>\n";
/* 1271 */       str = str + "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" + SystemEnv.getHtmlLabelName(10004153, ThreadVarLanguage.getLang()) + "" + str8 + "" + SystemEnv.getHtmlLabelName(27591, ThreadVarLanguage.getLang()) + "<br>\n";
/* 1272 */       str = str + "&nbsp;&nbsp;&nbsp;&nbsp;" + SystemEnv.getHtmlLabelName(10004154, ThreadVarLanguage.getLang()) + "" + str9 + "" + SystemEnv.getHtmlLabelName(27591, ThreadVarLanguage.getLang()) + "<br>\n";
/* 1273 */       str = str + "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" + SystemEnv.getHtmlLabelName(10004151, ThreadVarLanguage.getLang()) + "SQL" + SystemEnv.getHtmlLabelName(10004152, ThreadVarLanguage.getLang()) + "" + str10 + "" + SystemEnv.getHtmlLabelName(27591, ThreadVarLanguage.getLang()) + "<br>\n";
/* 1274 */     } else if (!"WorkflowReportDetail".equals(paramString)) {
/*      */       
/* 1276 */       if (!"HtmlNodeReportDetail".equals(paramString) && !"NodeChangeReportDetail".equals(paramString))
/*      */       {
/* 1278 */         if (!"WorkflowCustomReport".equals(paramString))
/*      */         {
/* 1280 */           if ("upgradecheckworkflowresult".equalsIgnoreCase(paramString) || "upgradecheckworkflowresultBatch".equalsIgnoreCase(paramString)) {
/* 1281 */             String str9, str10, str11; FileCheckReport fileCheckReport = new FileCheckReport();
/* 1282 */             JSONObject jSONObject = fileCheckReport.getWorkflowReport();
/* 1283 */             String str1 = jSONObject.getString("workflowCount");
/* 1284 */             String str2 = jSONObject.getString("htmlNodeCount");
/* 1285 */             String str3 = jSONObject.getString("excelNodeCount");
/* 1286 */             String str4 = jSONObject.getString("workflowCountUseExcel");
/*      */             
/* 1288 */             String str5 = jSONObject.getString("htmlFilecount");
/* 1289 */             String str6 = jSONObject.getString("nodeCount");
/* 1290 */             String str7 = jSONObject.getString("workflowCountAll");
/* 1291 */             String str8 = jSONObject.getString("workflowCountUseHtml");
/* 1292 */             DecimalFormat decimalFormat = new DecimalFormat("##.00");
/*      */ 
/*      */ 
/*      */ 
/*      */             
/* 1297 */             if (str7.equals("0") || str5.equals("0")) {
/* 1298 */               str9 = "0";
/*      */             } else {
/* 1300 */               str9 = decimalFormat.format(Util.getIntValue(str5) * 1.0D / Util.getIntValue(str7) * 100.0D);
/*      */             } 
/*      */             
/* 1303 */             if (str6.equals("0") || str2.equals("0")) {
/* 1304 */               str10 = "0";
/*      */             } else {
/* 1306 */               str10 = decimalFormat.format(Util.getDoubleValue(str2) / Util.getIntValue(str6) * 100.0D);
/*      */             } 
/*      */             
/* 1309 */             if (str6.equals("0") || str3.equals("0")) {
/* 1310 */               str11 = "0";
/*      */             } else {
/* 1312 */               str11 = decimalFormat.format(Util.getDoubleValue(str3) / Util.getIntValue(str6) * 100.0D);
/*      */             } 
/* 1314 */             str = str + "" + SystemEnv.getHtmlLabelName(10004155, ThreadVarLanguage.getLang()) + "" + str1 + "" + SystemEnv.getHtmlLabelName(27591, ThreadVarLanguage.getLang()) + "<br>\n";
/* 1315 */             str = str + "&nbsp;&nbsp;&nbsp;&nbsp;" + SystemEnv.getHtmlLabelName(10004151, ThreadVarLanguage.getLang()) + "html" + SystemEnv.getHtmlLabelName(10004156, ThreadVarLanguage.getLang()) + "" + str8 + "" + SystemEnv.getHtmlLabelName(18256, ThreadVarLanguage.getLang()) + "<br>\n";
/* 1316 */             str = str + "&nbsp;&nbsp;&nbsp;&nbsp;" + SystemEnv.getHtmlLabelName(10004151, ThreadVarLanguage.getLang()) + "html" + SystemEnv.getHtmlLabelName(10004157, ThreadVarLanguage.getLang()) + "" + str2 + "" + SystemEnv.getHtmlLabelName(27591, ThreadVarLanguage.getLang()) + "," + SystemEnv.getHtmlLabelName(10004158, ThreadVarLanguage.getLang()) + "(" + str6 + ")" + SystemEnv.getHtmlLabelName(10004159, ThreadVarLanguage.getLang()) + "" + str10 + "%<br>\n";
/* 1317 */             str = str + "&nbsp;&nbsp;&nbsp;&nbsp;" + SystemEnv.getHtmlLabelName(10004160, ThreadVarLanguage.getLang()) + "" + str4 + "" + SystemEnv.getHtmlLabelName(18256, ThreadVarLanguage.getLang()) + "<br>\n";
/* 1318 */             str = str + "&nbsp;&nbsp;&nbsp;&nbsp;" + SystemEnv.getHtmlLabelName(10004161, ThreadVarLanguage.getLang()) + "" + str3 + "" + SystemEnv.getHtmlLabelName(27591, ThreadVarLanguage.getLang()) + "," + SystemEnv.getHtmlLabelName(10004158, ThreadVarLanguage.getLang()) + "(" + str6 + ")" + SystemEnv.getHtmlLabelName(10004159, ThreadVarLanguage.getLang()) + "" + str11 + "%<br>\n";
/*      */             
/* 1320 */             str = str + "&nbsp;&nbsp;&nbsp;&nbsp;html" + SystemEnv.getHtmlLabelName(10004162, ThreadVarLanguage.getLang()) + "" + str5 + "" + SystemEnv.getHtmlLabelName(10004163, ThreadVarLanguage.getLang()) + "(" + str7 + ")" + SystemEnv.getHtmlLabelName(10004164, ThreadVarLanguage.getLang()) + "" + str9 + "%<br>\n";
/*      */           }
/* 1322 */           else if (!"".equalsIgnoreCase(paramString)) {
/*      */             
/* 1324 */             if ("CustomCheck".equals(paramString))
/* 1325 */             { PortalReportOperation portalReportOperation = new PortalReportOperation();
/* 1326 */               JSONObject jSONObject = portalReportOperation.getPortalReportInfo();
/* 1327 */               str = str + "" + SystemEnv.getHtmlLabelName(10004165, ThreadVarLanguage.getLang()) + "" + changeStr3(Util.null2String(jSONObject.get("custompage"))) + "" + SystemEnv.getHtmlLabelName(27591, ThreadVarLanguage.getLang()) + "<br>\n";
/* 1328 */               str = str + "" + SystemEnv.getHtmlLabelName(10004121, ThreadVarLanguage.getLang()) + "E-Mobile" + SystemEnv.getHtmlLabelName(10004166, ThreadVarLanguage.getLang()) + "" + changeStr3(Util.null2String(jSONObject.get("emobile"))) + "" + SystemEnv.getHtmlLabelName(27591, ThreadVarLanguage.getLang()) + "<br>\n";
/* 1329 */               str = str + "" + SystemEnv.getHtmlLabelName(10004167, ThreadVarLanguage.getLang()) + "" + changeStr3(Util.null2String(jSONObject.get("portaltotal"))) + "" + SystemEnv.getHtmlLabelName(27591, ThreadVarLanguage.getLang()) + "<br>\n";
/* 1330 */               str = str + "&nbsp;&nbsp;&nbsp;&nbsp;" + SystemEnv.getHtmlLabelName(10004168, ThreadVarLanguage.getLang()) + "" + changeStr3(Util.null2String(jSONObject.get("portalBeforeLogin"))) + "" + SystemEnv.getHtmlLabelName(27591, ThreadVarLanguage.getLang()) + "<br>\n";
/* 1331 */               str = str + "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" + SystemEnv.getHtmlLabelName(10004169, ThreadVarLanguage.getLang()) + "" + changeStr3(Util.null2String(jSONObject.get("portalBL4E7"))) + "" + SystemEnv.getHtmlLabelName(27591, ThreadVarLanguage.getLang()) + "<br>\n";
/* 1332 */               str = str + "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" + SystemEnv.getHtmlLabelName(10004170, ThreadVarLanguage.getLang()) + "" + changeStr3(Util.null2String(jSONObject.get("portalBL4E9"))) + "" + SystemEnv.getHtmlLabelName(27591, ThreadVarLanguage.getLang()) + "<br>\n";
/* 1333 */               str = str + "&nbsp;&nbsp;&nbsp;&nbsp;" + SystemEnv.getHtmlLabelName(10004171, ThreadVarLanguage.getLang()) + "" + changeStr3(Util.null2String(jSONObject.get("portalBeforeLoginpage"))) + "" + SystemEnv.getHtmlLabelName(27591, ThreadVarLanguage.getLang()) + "<br>\n";
/* 1334 */               str = str + "&nbsp;&nbsp;&nbsp;&nbsp;" + SystemEnv.getHtmlLabelName(10004172, ThreadVarLanguage.getLang()) + "" + changeStr3(Util.null2String(jSONObject.get("portalAfterLogin"))) + "" + SystemEnv.getHtmlLabelName(27591, ThreadVarLanguage.getLang()) + "<br>\n";
/* 1335 */               str = str + "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" + SystemEnv.getHtmlLabelName(10004169, ThreadVarLanguage.getLang()) + "" + changeStr3(Util.null2String(jSONObject.get("portalAL4E7"))) + "" + SystemEnv.getHtmlLabelName(27591, ThreadVarLanguage.getLang()) + "<br>\n";
/* 1336 */               str = str + "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" + SystemEnv.getHtmlLabelName(10004170, ThreadVarLanguage.getLang()) + "" + changeStr3(Util.null2String(jSONObject.get("portalAL4E9"))) + "" + SystemEnv.getHtmlLabelName(27591, ThreadVarLanguage.getLang()) + "<br>\n";
/* 1337 */               str = str + "&nbsp;&nbsp;&nbsp;&nbsp;" + SystemEnv.getHtmlLabelName(10004173, ThreadVarLanguage.getLang()) + "" + changeStr3(Util.null2String(jSONObject.get("portalAfterLoginpage"))) + "" + SystemEnv.getHtmlLabelName(27591, ThreadVarLanguage.getLang()) + "<br>\n";
/* 1338 */               str = str + "&nbsp;&nbsp;&nbsp;&nbsp;" + SystemEnv.getHtmlLabelName(10004174, ThreadVarLanguage.getLang()) + "" + changeStr3(Util.null2String(jSONObject.get("portalElement"))) + "" + SystemEnv.getHtmlLabelName(27591, ThreadVarLanguage.getLang()) + "<br>\n";
/* 1339 */               str = str + "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" + SystemEnv.getHtmlLabelName(10004175, ThreadVarLanguage.getLang()) + "" + changeStr3(Util.null2String(jSONObject.get("portalElement4E7"))) + "" + SystemEnv.getHtmlLabelName(27591, ThreadVarLanguage.getLang()) + "<br>\n";
/* 1340 */               str = str + "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" + SystemEnv.getHtmlLabelName(10004176, ThreadVarLanguage.getLang()) + "" + changeStr3(Util.null2String(jSONObject.get("portalElement4E9"))) + "" + SystemEnv.getHtmlLabelName(27591, ThreadVarLanguage.getLang()) + "<br>\n";
/* 1341 */               str = str + "" + SystemEnv.getHtmlLabelName(10004177, ThreadVarLanguage.getLang()) + "<br>\n";
/* 1342 */               str = str + "&nbsp;&nbsp;&nbsp;&nbsp;" + SystemEnv.getHtmlLabelName(10004178, ThreadVarLanguage.getLang()) + "" + changeStr3(Util.null2String(jSONObject.get("dataSource"))) + "" + SystemEnv.getHtmlLabelName(27591, ThreadVarLanguage.getLang()) + "<br>\n";
/* 1343 */               str = str + "&nbsp;&nbsp;&nbsp;&nbsp;" + SystemEnv.getHtmlLabelName(10004179, ThreadVarLanguage.getLang()) + "" + changeStr3(Util.null2String(jSONObject.get("workflowAction"))) + "" + SystemEnv.getHtmlLabelName(27591, ThreadVarLanguage.getLang()) + "<br>\n";
/* 1344 */               str = str + "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" + SystemEnv.getHtmlLabelName(19181, ThreadVarLanguage.getLang()) + "DML" + SystemEnv.getHtmlLabelName(10004180, ThreadVarLanguage.getLang()) + "" + changeStr3(Util.null2String(jSONObject.get("DmlAction"))) + "" + SystemEnv.getHtmlLabelName(27591, ThreadVarLanguage.getLang()) + "<br>\n";
/* 1345 */               str = str + "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" + SystemEnv.getHtmlLabelName(19181, ThreadVarLanguage.getLang()) + "Webservice" + SystemEnv.getHtmlLabelName(10004180, ThreadVarLanguage.getLang()) + "" + changeStr3(Util.null2String(jSONObject.get("WebServiceAction"))) + "" + SystemEnv.getHtmlLabelName(27591, ThreadVarLanguage.getLang()) + "<br>\n";
/* 1346 */               str = str + "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" + SystemEnv.getHtmlLabelName(10004181, ThreadVarLanguage.getLang()) + "" + changeStr3(Util.null2String(jSONObject.get("CustomerAction"))) + "" + SystemEnv.getHtmlLabelName(27591, ThreadVarLanguage.getLang()) + "<br>\n";
/* 1347 */               str = str + "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" + SystemEnv.getHtmlLabelName(19181, ThreadVarLanguage.getLang()) + "ESB" + SystemEnv.getHtmlLabelName(10004180, ThreadVarLanguage.getLang()) + "" + changeStr3(Util.null2String(jSONObject.get("ESBAction"))) + "" + SystemEnv.getHtmlLabelName(27591, ThreadVarLanguage.getLang()) + "<br>\n"; } 
/*      */           }  }  } 
/* 1349 */     }  if ("errMap".equals(paramString)) {
/* 1350 */       str = str + "" + SystemEnv.getHtmlLabelName(10004182, ThreadVarLanguage.getLang()) + "\n";
/*      */     }
/* 1352 */     return str;
/*      */   }
/*      */   
/*      */   public String getReportDescription(String paramString) {
/* 1356 */     String str = "<span style=\"color:red;font-weight:bold\">";
/* 1357 */     if ("mobilemode".equals(paramString)) {
/* 1358 */       str = str + "" + SystemEnv.getHtmlLabelName(10004183, ThreadVarLanguage.getLang()) + "“" + SystemEnv.getHtmlLabelName(10004095, ThreadVarLanguage.getLang()) + "”" + SystemEnv.getHtmlLabelName(10004184, ThreadVarLanguage.getLang()) + "“" + SystemEnv.getHtmlLabelName(33641, ThreadVarLanguage.getLang()) + "”" + SystemEnv.getHtmlLabelName(10004185, ThreadVarLanguage.getLang()) + "";
/* 1359 */     } else if ("mode".equals(paramString) || "modeBatch".equals(paramString)) {
/* 1360 */       str = str + "" + SystemEnv.getHtmlLabelName(10004186, ThreadVarLanguage.getLang()) + "“" + SystemEnv.getHtmlLabelName(10004095, ThreadVarLanguage.getLang()) + "”" + SystemEnv.getHtmlLabelName(10004184, ThreadVarLanguage.getLang()) + "“" + SystemEnv.getHtmlLabelName(30235, ThreadVarLanguage.getLang()) + "”" + SystemEnv.getHtmlLabelName(10004187, ThreadVarLanguage.getLang()) + "";
/* 1361 */     } else if ("upgradecheckworkflowresult".equals(paramString)) {
/* 1362 */       str = str + "";
/* 1363 */     } else if ("CustomCheck".equals(paramString)) {
/* 1364 */       str = str + "";
/* 1365 */     } else if ("upgradecheckSpecialFileresult".equalsIgnoreCase(paramString)) {
/* 1366 */       str = str + "" + SystemEnv.getHtmlLabelName(10004188, ThreadVarLanguage.getLang()) + "";
/* 1367 */     } else if ("upgradecheckworkflowresult".equals(paramString)) {
/* 1368 */       str = str + "" + SystemEnv.getHtmlLabelName(10004189, ThreadVarLanguage.getLang()) + "“" + SystemEnv.getHtmlLabelName(18104, ThreadVarLanguage.getLang()) + "”" + SystemEnv.getHtmlLabelName(10004190, ThreadVarLanguage.getLang()) + "“" + SystemEnv.getHtmlLabelName(15070, ThreadVarLanguage.getLang()) + "”" + SystemEnv.getHtmlLabelName(10004191, ThreadVarLanguage.getLang()) + "“" + SystemEnv.getHtmlLabelName(33636, ThreadVarLanguage.getLang()) + "”" + SystemEnv.getHtmlLabelName(10004192, ThreadVarLanguage.getLang()) + "";
/*      */     } 
/* 1370 */     str = str + "<br>\n" + SystemEnv.getHtmlLabelName(10004193, ThreadVarLanguage.getLang()) + "<br>\n";
/* 1371 */     str = str + "1." + SystemEnv.getHtmlLabelName(10004194, ThreadVarLanguage.getLang()) + "“" + SystemEnv.getHtmlLabelName(10004095, ThreadVarLanguage.getLang()) + "”，" + SystemEnv.getHtmlLabelName(10004195, ThreadVarLanguage.getLang()) + "," + SystemEnv.getHtmlLabelName(10004196, ThreadVarLanguage.getLang()) + "," + SystemEnv.getHtmlLabelName(10004197, ThreadVarLanguage.getLang()) + "<br>\n";
/* 1372 */     str = str + "2." + SystemEnv.getHtmlLabelName(10004198, ThreadVarLanguage.getLang()) + "," + SystemEnv.getHtmlLabelName(10004199, ThreadVarLanguage.getLang()) + "," + SystemEnv.getHtmlLabelName(10004200, ThreadVarLanguage.getLang()) + "“" + SystemEnv.getHtmlLabelName(10004201, ThreadVarLanguage.getLang()) + "”<br>\n";
/* 1373 */     str = str + "3." + SystemEnv.getHtmlLabelName(10004202, ThreadVarLanguage.getLang()) + "," + SystemEnv.getHtmlLabelName(10004203, ThreadVarLanguage.getLang()) + "“" + SystemEnv.getHtmlLabelName(10004097, ThreadVarLanguage.getLang()) + "”" + SystemEnv.getHtmlLabelName(10004204, ThreadVarLanguage.getLang()) + "<br>\n";
/* 1374 */     str = str + "4." + SystemEnv.getHtmlLabelName(10004205, ThreadVarLanguage.getLang()) + "<br>\n";
/* 1375 */     str = str + "</span><hr><br>\n";
/* 1376 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String changeStr3(String paramString) {
/* 1383 */     if (paramString != null) {
/* 1384 */       paramString = paramString.replaceAll("&amp;", "&");
/*      */       
/* 1386 */       paramString = paramString.replaceAll("&nbsp;", " ");
/*      */       
/* 1388 */       paramString = paramString.replaceAll("&quot;", "\"");
/* 1389 */       paramString = paramString.replaceAll("&apos;", "'");
/* 1390 */       paramString = paramString.replaceAll("&124;", "\\|");
/* 1391 */       paramString = paramString.replaceAll("&94;", "\\^");
/* 1392 */       paramString = paramString.replaceAll("\\\\n", "");
/* 1393 */       paramString = paramString.replaceAll("<", "&lt;");
/* 1394 */       paramString = paramString.replaceAll(">", "&gt;");
/*      */     } 
/* 1396 */     return paramString;
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/filecheck/ExportReportOperation.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */