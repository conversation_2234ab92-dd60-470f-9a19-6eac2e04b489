/*     */ package weaver.templetecheck.filecheck;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class WorkflowOperation
/*     */ {
/*     */   public List<Map<String, String>> geWorkflowhtml(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  21 */     String str1 = "select distinct workflowid,workflowname from workflow_nodehtmllayout t,workflow_base t3 where  t.workflowid=t3.id and ((syspath is not null and syspath <> '') or (datajson is not null ))  and isvalid in (1,2)  and isactive=1";
/*  22 */     String str2 = Util.null2String(paramMap.get("workflowname"));
/*  23 */     RecordSet recordSet = new RecordSet();
/*  24 */     String str3 = recordSet.getDBType();
/*  25 */     Boolean bool = Boolean.valueOf(false);
/*  26 */     if (str3.equalsIgnoreCase("oracle")) {
/*  27 */       bool = Boolean.valueOf(true);
/*     */     }
/*  29 */     if (bool.booleanValue()) {
/*  30 */       str1 = "select distinct workflowid,workflowname from workflow_nodehtmllayout t,workflow_base t3 where  t.workflowid=t3.id and (syspath is not null or syspath <> ''  or datajson is not null) and isvalid in (1,2)  and isactive=1";
/*     */     }
/*  32 */     if (!"".equals(str2)) {
/*  33 */       str1 = str1 + " and workflowname like '%" + str2 + "%'";
/*     */     }
/*     */     
/*  36 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  37 */     recordSet.executeQuery(str1, new Object[0]);
/*  38 */     FileCheckReport fileCheckReport = new FileCheckReport();
/*  39 */     while (recordSet.next()) {
/*  40 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*  41 */       hashMap.put("workflowid", Util.null2String(recordSet.getString("workflowid")));
/*  42 */       hashMap.put("workflowname", Util.null2String(recordSet.getString("workflowname")));
/*  43 */       arrayList.add(hashMap);
/*     */     } 
/*  45 */     return (List)arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<Map<String, String>> geWorkflowExcel(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  53 */     String str1 = "select distinct workflowid,workflowname from workflow_flownode t,workflow_base t3 where  t.workflowid=t3.id and t.ismode=1 and t3.isvalid in (1,2)";
/*  54 */     String str2 = Util.null2String(paramMap.get("workflowname"));
/*  55 */     RecordSet recordSet = new RecordSet();
/*  56 */     String str3 = recordSet.getDBType();
/*  57 */     Boolean bool = Boolean.valueOf(false);
/*     */     
/*  59 */     if (!"".equals(str2)) {
/*  60 */       str1 = str1 + " and workflowname like '%" + str2 + "%'";
/*     */     }
/*     */     
/*  63 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  64 */     recordSet.executeQuery(str1, new Object[0]);
/*  65 */     FileCheckReport fileCheckReport = new FileCheckReport();
/*  66 */     while (recordSet.next()) {
/*  67 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*  68 */       hashMap.put("workflowid", Util.null2String(recordSet.getString("workflowid")));
/*  69 */       hashMap.put("workflowname", Util.null2String(recordSet.getString("workflowname")));
/*  70 */       arrayList.add(hashMap);
/*     */     } 
/*  72 */     return (List)arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<Map<String, String>> geNodeResult(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*  81 */     String str1 = "select distinct workflowid,workflowname,nodename from workflow_nodehtmllayout t,workflow_nodebase t2,workflow_base t3 where  t.nodeid=t2.id and t.workflowid=t3.id  and ((syspath is not null and syspath <> '') or (datajson is not null )) and isvalid in (1,2) and isactive=1";
/*  82 */     String str2 = Util.null2String(paramMap.get("workflowname"));
/*  83 */     RecordSet recordSet = new RecordSet();
/*  84 */     String str3 = recordSet.getDBType();
/*  85 */     Boolean bool = Boolean.valueOf(false);
/*  86 */     if (str3.equalsIgnoreCase("oracle")) {
/*  87 */       bool = Boolean.valueOf(true);
/*     */     }
/*  89 */     if (bool.booleanValue()) {
/*  90 */       str1 = "select distinct workflowid,workflowname,nodename from workflow_nodehtmllayout t,workflow_nodebase t2,workflow_base t3 where  t.nodeid=t2.id and t.workflowid=t3.id  and (syspath is not null or syspath <>'' or datajson is not null ) and isvalid in (1,2) and isactive=1";
/*     */     }
/*  92 */     if (!"".equals(str2)) {
/*  93 */       str1 = str1 + " and workflowname like '%" + str2 + "%'";
/*     */     }
/*  95 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  96 */     recordSet.executeQuery(str1, new Object[0]);
/*  97 */     FileCheckReport fileCheckReport = new FileCheckReport();
/*  98 */     while (recordSet.next()) {
/*  99 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 100 */       hashMap.put("nodeid", Util.null2String(recordSet.getString("nodeid")));
/* 101 */       hashMap.put("workflowid", Util.null2String(recordSet.getString("workflowid")));
/* 102 */       hashMap.put("workflowname", Util.null2String(recordSet.getString("workflowname")));
/* 103 */       hashMap.put("nodename", Util.null2String(recordSet.getString("nodename")));
/* 104 */       arrayList.add(hashMap);
/*     */     } 
/* 106 */     return (List)arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<Map<String, String>> geExcelNodeResult(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 114 */     String str1 = "select distinct workflowid,workflowname,nodename from workflow_flownode t,workflow_nodebase t2,workflow_base t3 where  t.nodeid=t2.id and t.workflowid=t3.id  and t.ismode=1 and t3.isvalid in (1,2) ";
/* 115 */     String str2 = Util.null2String(paramMap.get("workflowname"));
/* 116 */     RecordSet recordSet = new RecordSet();
/* 117 */     String str3 = recordSet.getDBType();
/* 118 */     Boolean bool = Boolean.valueOf(false);
/* 119 */     if (!"".equals(str2)) {
/* 120 */       str1 = str1 + " and workflowname like '%" + str2 + "%'";
/*     */     }
/* 122 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 123 */     recordSet.executeQuery(str1, new Object[0]);
/* 124 */     FileCheckReport fileCheckReport = new FileCheckReport();
/* 125 */     while (recordSet.next()) {
/* 126 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 127 */       hashMap.put("nodeid", Util.null2String(recordSet.getString("nodeid")));
/* 128 */       hashMap.put("workflowid", Util.null2String(recordSet.getString("workflowid")));
/* 129 */       hashMap.put("workflowname", Util.null2String(recordSet.getString("workflowname")));
/* 130 */       hashMap.put("nodename", Util.null2String(recordSet.getString("nodename")));
/* 131 */       arrayList.add(hashMap);
/*     */     } 
/* 133 */     return (List)arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<Map<String, String>> geNodeHTMLResult(User paramUser, Map<String, String> paramMap, HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 142 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 143 */     String str1 = Util.null2String(paramMap.get("workflowname"));
/* 144 */     String str2 = Util.null2String(paramMap.get("type"));
/* 145 */     RecordSet recordSet = new RecordSet();
/* 146 */     String str3 = recordSet.getDBType();
/* 147 */     Boolean bool = Boolean.valueOf(false);
/* 148 */     if (str3.equalsIgnoreCase("oracle")) {
/* 149 */       bool = Boolean.valueOf(true);
/*     */     }
/* 151 */     String str4 = "";
/* 152 */     if (!"".equals(str1)) {
/* 153 */       str4 = str4 + " and workflowname like '%" + str1 + "%'";
/*     */     }
/* 155 */     if (!"".equals(str2)) {
/* 156 */       str4 = str4 + " and t.type=" + str2;
/*     */     }
/*     */     
/* 159 */     String str5 = "SELECT datajson,type,workflowid,workflowname,nodename,syspath FROM workflow_nodehtmllayout t,\tworkflow_nodebase t2, workflow_base t3 WHERE t.nodeid = t2.id AND t.workflowid = t3.id and syspath is not null " + (bool.booleanValue() ? "" : " and syspath <> '' ") + "\tAND datajson IS NULL AND isvalid IN ( 1, 2 ) AND isactive = 1 AND type<>1 " + str4 + " UNION ALL SELECT\tt.datajson ,t.type,t.workflowid, t3.workflowname, t2.nodename,\tt.syspath  FROM\tworkflow_nodehtmllayout t,\tworkflow_nodebase t2,\tworkflow_base t3 ,\tworkflow_printset p WHERE\tt.nodeid = t2.id \tAND t.workflowid = t3.id \tand syspath is not null " + (bool.booleanValue() ? "" : " and syspath <> '' ") + "\tAND t.datajson IS NULL\tAND isvalid IN ( 1, 2 ) \tAND t.isactive = 1 \tAND t.type=1\tAND p.modeid=t.id\tAND p.WORKFLOWID=t.WORKFLOWID\tAND p.NODEID=t.NODEID " + str4;
/*     */     
/* 161 */     recordSet.executeQuery(str5, new Object[0]);
/* 162 */     FileCheckReport fileCheckReport = new FileCheckReport();
/* 163 */     while (recordSet.next()) {
/* 164 */       String str = Util.null2String(recordSet.getString("datajson"));
/* 165 */       if ("".equals(str)) {
/* 166 */         HashMap<Object, Object> hashMap = new HashMap<>();
/* 167 */         hashMap.put("nodeid", Util.null2String(recordSet.getString("nodeid")));
/* 168 */         hashMap.put("workflowid", Util.null2String(recordSet.getString("workflowid")));
/* 169 */         hashMap.put("workflowname", Util.null2String(recordSet.getString("workflowname")));
/* 170 */         hashMap.put("nodename", Util.null2String(recordSet.getString("nodename")));
/* 171 */         hashMap.put("type", fileCheckReport.getHtmltype(Util.null2String(recordSet.getString("type"))));
/* 172 */         hashMap.put("syspath", Util.null2String(recordSet.getString("syspath")));
/* 173 */         arrayList.add(hashMap);
/*     */       } 
/*     */     } 
/* 176 */     return (List)arrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/filecheck/WorkflowOperation.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */