/*      */ package weaver.templetecheck.filecheck;
/*      */ import com.alibaba.fastjson.JSONObject;
/*      */ import java.io.File;
/*      */ import java.lang.reflect.Method;
/*      */ import java.util.ArrayList;
/*      */ import java.util.Arrays;
/*      */ import java.util.HashMap;
/*      */ import java.util.HashSet;
/*      */ import java.util.Iterator;
/*      */ import java.util.List;
/*      */ import java.util.Map;
/*      */ import java.util.Set;
/*      */ import java.util.concurrent.CountDownLatch;
/*      */ import java.util.regex.Matcher;
/*      */ import java.util.regex.Pattern;
/*      */ import org.jsoup.nodes.Document;
/*      */ import weaver.conn.ConnStatement;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.general.BaseBean;
/*      */ import weaver.general.GCONST;
/*      */ import weaver.general.ThreadVarLanguage;
/*      */ import weaver.general.TimeUtil;
/*      */ import weaver.general.Util;
/*      */ import weaver.systeminfo.SystemEnv;
/*      */ import weaver.templetecheck.FileUtil;
/*      */ 
/*      */ public class CheckRule {
/*   28 */   public static int runningStatus = 0;
/*   29 */   public static int runningWorkflowStatus = 0;
/*   30 */   public static int runningModeStatus = 0;
/*   31 */   public static int runningMobileModeStatus = 0;
/*      */ 
/*      */ 
/*      */   
/*   35 */   public static int isrunpartStatus = 0;
/*   36 */   private int fileNum = 0;
/*      */   
/*   38 */   public static final String CANNOTREPLACE = SystemEnv.getHtmlLabelName(10004221, ThreadVarLanguage.getLang()) + "";
/*      */ 
/*      */   
/*   41 */   public static final List<String> defaultHtmlStrList = Arrays.asList(new String[] {
/*   42 */         replaceBlank("<!-- " + SystemEnv.getHtmlLabelName(128989, ThreadVarLanguage.getLang()) + " -->").toLowerCase(), 
/*   43 */         replaceBlank("<script></script>").toLowerCase(), 
/*   44 */         replaceBlank("<!-- " + SystemEnv.getHtmlLabelName(128989, ThreadVarLanguage.getLang()) + " --> <script type=\"text/javascript\"> /* * TODO * " + SystemEnv.getHtmlLabelName(128990, ThreadVarLanguage.getLang()) + " */ </script>").toLowerCase(), 
/*   45 */         replaceBlank("<script type=\"text/javascript\">/** " + SystemEnv.getHtmlLabelName(389599, ThreadVarLanguage.getLang()) + "*/</script>").toLowerCase(), 
/*   46 */         replaceBlank("<script type=\"text/javascript\">(function(){/** " + SystemEnv.getHtmlLabelName(389599, ThreadVarLanguage.getLang()) + "*/}());</script><style type=\"text/css\">/** " + SystemEnv.getHtmlLabelName(522717, ThreadVarLanguage.getLang()) + "*/</style>"), 
/*   47 */         replaceBlank("<html><head><style>/*" + SystemEnv.getHtmlLabelName(390139, ThreadVarLanguage.getLang()) + "*/</style></head><body></body></html>")
/*      */       });
/*   49 */   FileUtil fileutil = new FileUtil();
/*      */   
/*   51 */   private static HashMap<String, ArrayList<HashMap<String, String>>> matchResult = new HashMap<>();
/*   52 */   private static Map<String, List<String>> clobmap = new HashMap<>();
/*   53 */   private MobileModeReportOperation mmro = new MobileModeReportOperation();
/*   54 */   private ModeReportOperation mro = new ModeReportOperation();
/*   55 */   public static Map<String, String> wfErrMap = new HashMap<>();
/*   56 */   public static Map<String, String> modeErrMap = new HashMap<>();
/*   57 */   public static Map<String, String> mobileModeErrMap = new HashMap<>();
/*      */   
/*      */   public static Map<String, String> getWfErrMap() {
/*   60 */     return wfErrMap;
/*      */   }
/*      */   
/*      */   public static void setWfErrMap(Map<String, String> paramMap) {
/*   64 */     wfErrMap = paramMap;
/*      */   }
/*      */   
/*      */   public static Map<String, String> getModeErrMap() {
/*   68 */     return modeErrMap;
/*      */   }
/*      */   
/*      */   public static void setModeErrMap(Map<String, String> paramMap) {
/*   72 */     modeErrMap = paramMap;
/*      */   }
/*      */   
/*      */   public static Map<String, String> getMobileModeErrMap() {
/*   76 */     return mobileModeErrMap;
/*      */   }
/*      */   
/*      */   public static void setMobileModeErrMap(Map<String, String> paramMap) {
/*   80 */     mobileModeErrMap = paramMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void matchSpecialFile(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5) throws Exception {
/*   89 */     RecordSet recordSet = new RecordSet();
/*   90 */     recordSet.writeLog("###指定文件检查--开始###");
/*   91 */     ArrayList<RuleBean> arrayList = new ArrayList();
/*   92 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*   93 */     String str = "upgradecheckrule";
/*   94 */     if ("true".equals(paramString5)) {
/*   95 */       str = "upgradecheckworkflowrule";
/*      */     }
/*      */     
/*   98 */     if ("".equals(paramString1) || paramString1 == null) {
/*   99 */       recordSet.execute("select * from " + str + " where content!='^[\\s\\S]*.*[^\\s][\\s\\S]*$' order by id");
/*      */     } else {
/*  101 */       recordSet.execute("select * from " + str + " where id in (" + ReplaceOperation.formatIds(paramString1) + ") order by id");
/*      */     } 
/*      */ 
/*      */     
/*  105 */     while (recordSet.next()) {
/*  106 */       RuleBean ruleBean = new RuleBean();
/*  107 */       ruleBean.setId(recordSet.getString("id"));
/*  108 */       ruleBean.setRulename(recordSet.getString("rulename"));
/*  109 */       ruleBean.setRuledesc(recordSet.getString("ruledesc"));
/*  110 */       ruleBean.setRuletype(recordSet.getString("ruletype"));
/*  111 */       ruleBean.setChecktype(recordSet.getString("checktype"));
/*  112 */       ruleBean.setContent(recordSet.getString("content"));
/*  113 */       ruleBean.setReplacecontent(recordSet.getString("replacecontent"));
/*  114 */       ruleBean.setVersion(recordSet.getString("version"));
/*  115 */       arrayList.add(ruleBean);
/*      */     } 
/*      */     
/*  118 */     recordSet.execute("delete from upgradecheckresult");
/*      */ 
/*      */     
/*  121 */     ArrayList<String> arrayList1 = new ArrayList();
/*      */     
/*  123 */     if ("1".equals(paramString3)) {
/*  124 */       if (paramString4 == null || "".equals(paramString4) || "-1".equals(paramString4)) {
/*  125 */         recordSet.execute("select * from nonstandardfile");
/*      */       } else {
/*  127 */         paramString4 = paramString4.substring(0, paramString4.length() - 1);
/*  128 */         recordSet.execute("select * from nonstandardfile where id in (" + paramString4 + ")");
/*      */       } 
/*  130 */       while (recordSet.next()) {
/*  131 */         arrayList1.add(recordSet.getString("filepath"));
/*      */       }
/*      */     }
/*  134 */     else if (paramString2.indexOf("，") > -1) {
/*  135 */       String[] arrayOfString = paramString2.split("，");
/*  136 */       for (byte b1 = 0; b1 < arrayOfString.length; b1++) {
/*  137 */         arrayList1.add(arrayOfString[b1]);
/*      */       }
/*      */     } else {
/*  140 */       String[] arrayOfString = paramString2.split(",");
/*  141 */       for (byte b1 = 0; b1 < arrayOfString.length; b1++) {
/*  142 */         arrayList1.add(arrayOfString[b1]);
/*      */       }
/*      */     } 
/*      */ 
/*      */     
/*  147 */     for (byte b = 0; b < arrayList1.size(); b++) {
/*  148 */       String str1 = arrayList1.get(b);
/*  149 */       if ("1".equals(paramString3) && 
/*  150 */         str1.indexOf(":") <= -1) {
/*  151 */         str1 = GCONST.getRootPath() + str1;
/*  152 */         str1 = str1.replaceAll("\\\\", "/");
/*  153 */         str1 = str1.replaceAll("//", "/");
/*      */       } 
/*      */       
/*  156 */       hashMap.put("workflowname", "--");
/*  157 */       hashMap.put("nodename", "--");
/*  158 */       hashMap.put("filepath", str1);
/*  159 */       File file = new File(str1);
/*  160 */       checkDirectory(file, "upgradecheckresult", arrayList, (HashMap)hashMap);
/*      */     } 
/*      */     
/*  163 */     recordSet.writeLog("###指定文件检查--结束###");
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void checkDirectory(File paramFile, String paramString, ArrayList<RuleBean> paramArrayList, HashMap<String, String> paramHashMap) {
/*  170 */     if (paramFile != null) {
/*  171 */       if (paramFile.isFile()) {
/*  172 */         if (!paramFile.exists()) {
/*      */           return;
/*      */         }
/*  175 */         String str = paramFile.getPath();
/*  176 */         if ((str.endsWith(".js") || str.endsWith(".html") || str.endsWith(".css") || str.endsWith(".less") || str.endsWith(".jsp") || str.endsWith(".java") || str.endsWith(".sql")) && 
/*  177 */           str.indexOf("- 副本") < 0 && str.indexOf(".bak") < 0) {
/*  178 */           (new BaseBean()).writeLog("###开始检查 文件：" + str + "###");
/*  179 */           StringBuffer stringBuffer = this.fileutil.readFile(paramFile);
/*  180 */           String str1 = stringBuffer.toString();
/*  181 */           paramHashMap.put("filepath", paramFile.getPath());
/*  182 */           checkContent("upgradecheckresult", str1, paramArrayList, paramHashMap);
/*      */         }
/*      */       
/*      */       } else {
/*      */         
/*  187 */         File[] arrayOfFile = paramFile.listFiles();
/*  188 */         for (byte b = 0; b < arrayOfFile.length; b++)
/*      */         {
/*  190 */           checkDirectory(arrayOfFile[b], paramString, paramArrayList, paramHashMap);
/*      */         }
/*      */       } 
/*      */     }
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void matchWorkflow() throws Exception {
/*  204 */     String str = getWorkflowCheckRule();
/*  205 */     matchWorkflow(str, "");
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void matchWorkflow(String paramString1, String paramString2) throws Exception {
/*  213 */     RecordSet recordSet1 = new RecordSet();
/*  214 */     RecordSet recordSet2 = new RecordSet();
/*  215 */     FileUtil fileUtil = new FileUtil();
/*  216 */     ExcelSecurity excelSecurity = new ExcelSecurity();
/*  217 */     recordSet1.writeLog("###检查流程模板--开始###");
/*      */ 
/*      */     
/*  220 */     ArrayList arrayList = new ArrayList();
/*  221 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  222 */     String str1 = "select t.id as htmllayoutid from workflow_nodehtmllayout t,workflow_nodebase t2,workflow_base t3,workflow_flownode t4 WHERE t.nodeid=t2.id and t.nodeid=t4.nodeid and t.workflowid=t4.workflowid and t.workflowid=t3.id and t.isactive='1' and t3.isvalid in (1,2) and t4.ismode=2 AND (t2.requestid is null or t2.requestid='') ";
/*  223 */     if (!"".equals(paramString2)) {
/*  224 */       str1 = str1 + " and t.workflowid in (" + paramString2 + ")";
/*      */     }
/*  226 */     boolean bool = recordSet1.executeSql(str1);
/*  227 */     recordSet1.writeLog("流程模板检查sql:" + str1);
/*      */ 
/*      */     
/*  230 */     this; wfErrMap.clear();
/*  231 */     if (!bool) {
/*  232 */       wfErrMap.put("wf-sqlerror", SystemEnv.getHtmlLabelName(524986, ThreadVarLanguage.getLang()) + "");
/*      */       
/*      */       return;
/*      */     } 
/*  236 */     int i = 0;
/*      */     
/*  238 */     int j = 0;
/*  239 */     int k = 0;
/*      */ 
/*      */     
/*  242 */     CheckWorkflowThread.wfErrMap.clear();
/*  243 */     String str2 = "select lastchektime from upgeradechecktime  where eventname='checkwf'";
/*  244 */     String str3 = "";
/*  245 */     recordSet1.executeSql(str2);
/*  246 */     if (recordSet1.next()) {
/*  247 */       str3 = recordSet1.getString("lastchektime");
/*      */     }
/*  249 */     String str4 = TimeUtil.getCurrentTimeString();
/*  250 */     String str5 = "update upgeradechecktime set lastchektime='" + str4 + "' WHERE  eventname='checkwf'";
/*  251 */     recordSet1.executeSql(str5);
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  256 */     String str6 = "select count(1) as countids ";
/*  257 */     String str7 = " FROM workflow_nodehtmllayout t,workflow_nodebase t2,workflow_base t3,workflow_flownode t4 ";
/*  258 */     String str8 = " WHERE t.nodeid=t2.id AND t.nodeid=t4.nodeid AND t.workflowid=t4.workflowid AND t.workflowid=t3.id and t.isactive='1' AND t3.isvalid in (1,2) AND t4.ismode=2 AND (t2.requestid is null or t2.requestid='') ";
/*  259 */     if (!"".equals(paramString2)) {
/*  260 */       str8 = " WHERE t.workflowid in (" + paramString2 + ") " + str8.replace("WHERE", "AND");
/*      */     }
/*  262 */     recordSet1.executeSql(str6 + str7 + str8);
/*  263 */     if (recordSet1.next()) {
/*  264 */       k = Util.getIntValue(recordSet1.getString("countids"));
/*      */     }
/*  266 */     BaseBean baseBean = new BaseBean();
/*  267 */     String str9 = Util.null2String(baseBean.getPropValue("filecheckutil", "workflowcheckthread"));
/*  268 */     int m = Integer.valueOf(Util.null2String(baseBean.getPropValue("filecheckutil", "minhtmllayoutmun"), "0")).intValue();
/*  269 */     i = Integer.valueOf(str9).intValue();
/*  270 */     if (k <= m) {
/*  271 */       i = 1;
/*      */     }
/*  273 */     int n = (int)Math.ceil(k / i * 1.0D);
/*  274 */     str6 = "select MIN(t.id) as minid,MAX(t.id) as maxid ";
/*  275 */     recordSet1.executeSql(str6 + str7 + str8);
/*  276 */     int i1 = j;
/*  277 */     if (recordSet1.next()) {
/*  278 */       j = recordSet1.getInt("minid");
/*  279 */       i1 = recordSet1.getInt("maxid");
/*      */     } 
/*  281 */     str6 = "select t.id as tids ";
/*  282 */     recordSet1.executeSql(str6 + str7 + str8);
/*  283 */     byte b = 0;
/*  284 */     int i2 = 1;
/*      */     
/*  286 */     int i3 = j + 1;
/*  287 */     if (k == 0) {
/*  288 */       i = 0;
/*      */     }
/*      */     
/*  291 */     CountDownLatch countDownLatch = new CountDownLatch(i);
/*  292 */     CheckWorkflowThread checkWorkflowThread = new CheckWorkflowThread();
/*  293 */     checkWorkflowThread.setLastChecktime(str3);
/*  294 */     CheckWorkflowThread.setCountDownLatch(countDownLatch);
/*  295 */     checkWorkflowThread.setCheckedHtmlNum(0);
/*  296 */     checkWorkflowThread.setTotolHtmlNum(k);
/*  297 */     boolean bool1 = false;
/*  298 */     while (recordSet1.next()) {
/*  299 */       if (i2 == i) {
/*  300 */         bool1 = true;
/*      */       }
/*  302 */       b++;
/*  303 */       if (b == n || bool1) {
/*  304 */         i3 = recordSet1.getInt("tids");
/*  305 */         if (bool1) {
/*  306 */           i3 = i1 + 1;
/*      */         }
/*  308 */         String str = "thread-" + i2;
/*  309 */         recordSet1.writeLog("进程参数：name=" + str + ";startLayoutId=" + j + ";endLayoutId=" + i3);
/*  310 */         CheckWorkflowThread checkWorkflowThread1 = new CheckWorkflowThread(str, paramString1, paramString2, j, i3);
/*  311 */         checkWorkflowThread1.start();
/*  312 */         if (bool1) {
/*      */           break;
/*      */         }
/*  315 */         i2++;
/*  316 */         b = 0;
/*  317 */         j = i3;
/*      */       } 
/*      */     } 
/*      */     
/*      */     try {
/*  322 */       countDownLatch.await();
/*  323 */     } catch (InterruptedException interruptedException) {
/*  324 */       interruptedException.printStackTrace();
/*      */     } 
/*  326 */     recordSet1.writeLog("###检查流程模板--线程检测结束###");
/*  327 */     int i4 = checkWorkflowThread.getFileNum();
/*  328 */     int i5 = checkWorkflowThread.getFileNotExistNum();
/*  329 */     if (i4 != 0 && i5 == i4) {
/*  330 */       wfErrMap.put("wf-fileNotExist", SystemEnv.getHtmlLabelName(524989, ThreadVarLanguage.getLang()) + "");
/*      */     }
/*  332 */     runningWorkflowStatus = 0;
/*  333 */     recordSet1.writeLog("###检查流程模板--结束###");
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void matchModeDev() throws Exception {
/*  342 */     String str = Util.null2String(this.mro.getMatchDevContentRuleid());
/*  343 */     matchMode(str);
/*      */   }
/*      */   
/*      */   public void matchMode(String paramString) throws Exception {
/*  347 */     RecordSet recordSet = new RecordSet();
/*  348 */     FileUtil fileUtil = new FileUtil();
/*  349 */     ExcelSecurity excelSecurity = new ExcelSecurity();
/*  350 */     recordSet.writeLog("###检查表单建模--开始###");
/*  351 */     ArrayList<RuleBean> arrayList = new ArrayList();
/*  352 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  353 */     String str1 = recordSet.getDBType().toLowerCase();
/*      */     
/*  355 */     if (paramString == null || "".equals(paramString)) {
/*  356 */       recordSet.execute("select * from upgradecheckModerule where content!='^[\\s\\S]*.*[^\\s][\\s\\S]*$'  order by id");
/*      */     } else {
/*  358 */       if (paramString.endsWith(",")) {
/*  359 */         paramString = paramString.substring(0, paramString.length() - 1);
/*      */       }
/*  361 */       recordSet.execute("select * from upgradecheckModerule where id in (" + paramString + ") order by id");
/*      */     } 
/*      */     
/*  364 */     while (recordSet.next()) {
/*  365 */       RuleBean ruleBean = new RuleBean();
/*  366 */       ruleBean.setId(recordSet.getString("id"));
/*  367 */       ruleBean.setRulename(recordSet.getString("rulename"));
/*  368 */       ruleBean.setRuledesc(recordSet.getString("ruledesc"));
/*  369 */       ruleBean.setRuletype(recordSet.getString("ruletype"));
/*  370 */       ruleBean.setChecktype(recordSet.getString("checktype"));
/*  371 */       ruleBean.setContent(recordSet.getString("content"));
/*  372 */       ruleBean.setReplacecontent(recordSet.getString("replacecontent"));
/*  373 */       ruleBean.setVersion(recordSet.getString("version"));
/*  374 */       ruleBean.setContenttype(recordSet.getString("contenttype"));
/*  375 */       arrayList.add(ruleBean);
/*      */     } 
/*  377 */     recordSet.executeUpdate("delete from  upgradecheckmoderesult ", new Object[0]);
/*      */     
/*  379 */     boolean bool1 = checkDBFieldIsExist("version", "modehtmllayout");
/*  380 */     String str2 = "select a.id,a.layoutname as name,a.datajson,a.scripts,a.scriptstr,a.pluginjson," + (bool1 ? "version" : "'' as version") + ",a.syspath,b.modetype,b.modename,c.treeFieldName from modehtmllayout a left join modeinfo b on a.modeid=b.id left join modetreefield c on b.modetype=c.id where b.id is not null and c.id is not null and  (b.isdelete=0 or b.isdelete is null) and (c.isdelete=0 or c.isdelete is null) order by a.id ";
/*  381 */     this.fileNum = 0;
/*  382 */     boolean bool2 = recordSet.execute(str2);
/*  383 */     recordSet.writeLog("表单建模模块表单sql:" + str2);
/*  384 */     modeErrMap.clear();
/*  385 */     if (!bool2) {
/*  386 */       modeErrMap.put("mode-sql", SystemEnv.getHtmlLabelName(524990, ThreadVarLanguage.getLang()) + "");
/*      */     }
/*  388 */     byte b = 0;
/*  389 */     while (recordSet.next()) {
/*  390 */       hashMap.clear();
/*  391 */       String str3 = recordSet.getString("name");
/*  392 */       String str4 = "";
/*  393 */       String str5 = ModeReportOperation.getAppLocation(Util.null2String(recordSet.getString("modetype"))) + " > " + SystemEnv.getHtmlLabelName(517815, ThreadVarLanguage.getLang()) + "(" + ("html".equalsIgnoreCase(str4) ? SystemEnv.getHtmlLabelName(524992, ThreadVarLanguage.getLang()) : SystemEnv.getHtmlLabelName(524993, ThreadVarLanguage.getLang())) + "):" + Util.null2String(recordSet.getString("modename"));
/*  394 */       String str6 = recordSet.getString("syspath");
/*  395 */       String str7 = Util.null2String(recordSet.getString("version"));
/*  396 */       if ("2".equals(str7)) {
/*  397 */         str4 = "excel";
/*      */       } else {
/*  399 */         str4 = "html";
/*      */       } 
/*  401 */       hashMap.put("workflowname", str3);
/*  402 */       hashMap.put("nodename", str5);
/*  403 */       hashMap.put("filepath", str6);
/*  404 */       hashMap.put("nodehtmllayoutid", recordSet.getString("id"));
/*  405 */       hashMap.put("detailtype", str4);
/*  406 */       hashMap.put("refertable", "modehtmllayout");
/*      */       
/*  408 */       recordSet.writeLog("###--开始检查 模块(老式html)名称：" + str3 + "--模块名称：" + str5 + "###");
/*  409 */       String str8 = recordSet.getString("datajson");
/*  410 */       String str9 = recordSet.getString("pluginjson");
/*  411 */       String str10 = Util.null2String(recordSet.getString("scriptstr"));
/*  412 */       String str11 = "";
/*  413 */       if (!"".equals(str10)) {
/*  414 */         str11 = excelSecurity.decode(str10);
/*      */       }
/*      */       
/*  417 */       if (str8 == null || "".equals(str8)) {
/*      */         
/*  419 */         recordSet.writeLog("###--模板路径：" + str6 + "###");
/*  420 */         File file = new File(str6);
/*  421 */         this.fileNum++;
/*  422 */         if (file.exists()) {
/*  423 */           StringBuffer stringBuffer = fileUtil.readFile(file);
/*  424 */           String str = stringBuffer.toString();
/*  425 */           hashMap.put("sourcecontent", str);
/*  426 */           hashMap.put("contenttype", "0");
/*  427 */           hashMap.put("modetype", "0");
/*  428 */           checkContent("upgradecheckmoderesult", str, arrayList, (HashMap)hashMap); continue;
/*      */         } 
/*  430 */         b++;
/*      */         
/*      */         continue;
/*      */       } 
/*  434 */       hashMap.put("modetype", "1");
/*  435 */       hashMap.put("sourcecontent", str11);
/*  436 */       hashMap.put("contenttype", "0");
/*  437 */       checkContent("upgradecheckmoderesult", str11, arrayList, (HashMap)hashMap);
/*      */     } 
/*      */     
/*  440 */     if (this.fileNum != 0 && this.fileNum == b) {
/*  441 */       modeErrMap.put("mode-fileNotExist", SystemEnv.getHtmlLabelName(524994, ThreadVarLanguage.getLang()) + "");
/*      */     }
/*      */ 
/*      */     
/*  445 */     str2 = "select  a.id,a.expendname as name,a.hreftarget as scripts, b.modetype,b.modename,c.treeFieldName from mode_pageexpand a left join modeinfo b on a.modeid=b.id left join modetreefield c on b.modetype=c.id where a.hreftarget is not null " + (!"oracle".equals(str1) ? " and a.hreftarget !='' " : "") + " and b.id is not null and c.id is not null and (b.isdelete=0 or b.isdelete is null) and a.hreftype=2 and (c.isdelete=0 or c.isdelete is null)  order by a.id";
/*  446 */     bool2 = recordSet.execute(str2);
/*  447 */     if (!bool2) {
/*  448 */       modeErrMap.put("mode-expendSqlErr", SystemEnv.getHtmlLabelName(524995, ThreadVarLanguage.getLang()) + "");
/*      */     }
/*  450 */     while (recordSet.next()) {
/*  451 */       hashMap.clear();
/*  452 */       String str3 = recordSet.getString("name");
/*  453 */       String str4 = ModeReportOperation.getAppLocation(Util.null2String(recordSet.getString("modetype"))) + " > " + SystemEnv.getHtmlLabelName(524996, ThreadVarLanguage.getLang()) + ":" + Util.null2String(recordSet.getString("modename"));
/*  454 */       hashMap.put("workflowname", str3);
/*  455 */       hashMap.put("nodename", str4);
/*  456 */       hashMap.put("nodehtmllayoutid", recordSet.getString("id"));
/*  457 */       hashMap.put("detailtype", "expand");
/*      */       
/*  459 */       recordSet.writeLog("###--开始检查 模块(页面扩展)名称：" + str3 + "--模块名称：" + str4 + "###");
/*  460 */       String str5 = recordSet.getString("scripts");
/*  461 */       hashMap.put("modetype", "1");
/*  462 */       hashMap.put("refertable", "mode_pageexpand");
/*  463 */       hashMap.put("sourcecontent", str5);
/*  464 */       hashMap.put("contenttype", "0");
/*  465 */       checkContent("upgradecheckmoderesult", str5, arrayList, (HashMap)hashMap);
/*      */     } 
/*      */ 
/*      */     
/*  469 */     str2 = "select a.id,a.customname as name ,a.searchconditiontype,a.defaultsql,a.appid as modetype from mode_customsearch a left join modetreefield c on a.appid=c.id  where a.defaultsql is not null " + (!"oracle".equals(str1) ? " and a.defaultsql !='' " : "") + "  and c.id is not null and (c.isdelete=0 or c.isdelete is null) order by a.id";
/*  470 */     bool2 = recordSet.execute(str2);
/*  471 */     if (!bool2) {
/*  472 */       modeErrMap.put("mode-sqlAndJavaSqlErr", SystemEnv.getHtmlLabelName(524997, ThreadVarLanguage.getLang()) + "");
/*      */     }
/*  474 */     while (recordSet.next()) {
/*  475 */       hashMap.clear();
/*  476 */       String str3 = recordSet.getString("searchconditiontype");
/*  477 */       String str4 = recordSet.getString("name");
/*  478 */       String str5 = ModeReportOperation.getAppLocation(Util.null2String(recordSet.getString("modetype"))) + " > " + SystemEnv.getHtmlLabelName(521572, ThreadVarLanguage.getLang()) + "(" + ("1".equalsIgnoreCase(str3) ? SystemEnv.getHtmlLabelName(524998, ThreadVarLanguage.getLang()) : SystemEnv.getHtmlLabelName(524999, ThreadVarLanguage.getLang())) + "):" + Util.null2String(recordSet.getString("modename"));
/*  479 */       String str6 = "";
/*  480 */       hashMap.put("workflowname", str4);
/*  481 */       hashMap.put("nodename", str5);
/*  482 */       hashMap.put("nodehtmllayoutid", recordSet.getString("id"));
/*  483 */       hashMap.put("modetype", "1");
/*  484 */       hashMap.put("refertable", "mode_customsearch");
/*  485 */       recordSet.writeLog("###--开始检查 查询(以java/sql作为条件)名称：" + str4 + "--模块名称：" + str5 + "###");
/*  486 */       if ("1".equals(str3)) {
/*  487 */         hashMap.put("detailtype", "customSearchForSql");
/*  488 */         str6 = Util.null2String(recordSet.getString("defaultsql"));
/*  489 */         if (!"".equals(str6)) {
/*  490 */           hashMap.put("sourcecontent", str6);
/*  491 */           hashMap.put("contenttype", "1");
/*  492 */           checkContent("upgradecheckmoderesult", str6, arrayList, (HashMap)hashMap);
/*      */         } 
/*      */       } 
/*      */     } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  512 */     str2 = "select a.id,a.jsmethodbody,a.buttonname as name,'customSearchButton' as detailtype ,b.modeid,c.modetype,b.customname ,c.modename,d.treeFieldname from mode_customSearchButton a left join mode_customsearch b on a.objid=b.id left join modeinfo c on b.modeid=c.id  left join modetreefield d on c.modetype=d.id where a.jsmethodbody is not null and c.id is not null and d.id is not null and  (c.isdelete=0 or c.isdelete is null) and a.hreftype=1 and (d.isdelete=0 or d.isdelete is null) order by a.id";
/*  513 */     bool2 = recordSet.execute(str2);
/*  514 */     if (!bool2) {
/*  515 */       modeErrMap.put("mode-buttonSqlErr", SystemEnv.getHtmlLabelName(525000, ThreadVarLanguage.getLang()));
/*      */     }
/*  517 */     while (recordSet.next()) {
/*  518 */       hashMap.clear();
/*  519 */       String str3 = recordSet.getString("name");
/*  520 */       String str4 = ModeReportOperation.getAppLocation(Util.null2String(recordSet.getString("modetype"))) + " > " + SystemEnv.getHtmlLabelName(525001, ThreadVarLanguage.getLang()) + ":" + Util.null2String(recordSet.getString("modename"));
/*  521 */       String str5 = "";
/*  522 */       hashMap.put("workflowname", str3);
/*  523 */       hashMap.put("nodename", str4);
/*  524 */       hashMap.put("nodehtmllayoutid", recordSet.getString("id"));
/*  525 */       hashMap.put("modetype", "1");
/*  526 */       hashMap.put("detailtype", recordSet.getString("detailtype"));
/*  527 */       hashMap.put("refertable", "mode_customSearchButton");
/*  528 */       recordSet.writeLog("###--开始检查 查询(自定义按钮)名称：" + str3 + "--模块名称：" + str4 + "###");
/*  529 */       str5 = Util.null2String(recordSet.getString("jsmethodbody"));
/*  530 */       if (!"".equals(str5)) {
/*  531 */         hashMap.put("sourcecontent", str5);
/*  532 */         hashMap.put("contenttype", "0");
/*  533 */         checkContent("upgradecheckmoderesult", str5, arrayList, (HashMap)hashMap);
/*      */       } 
/*      */     } 
/*      */ 
/*      */ 
/*      */     
/*  539 */     str2 = "select a.id,a.customname,a.searchconditiontype,a.defaultsql,a.customname as name,a.appid as modetype,c.treeFieldName from mode_custombrowser a left join modetreefield c on a.appid=c.id where  a.defaultsql is not null " + (!"oracle".equals(str1) ? " and a.defaultsql !='' " : "") + " and c.id is not null and (c.isdelete=0 or c.isdelete is null)  order by a.id";
/*  540 */     bool2 = recordSet.execute(str2);
/*  541 */     if (!bool2) {
/*  542 */       modeErrMap.put("mode-customSqlErr", SystemEnv.getHtmlLabelName(525002, ThreadVarLanguage.getLang()) + "");
/*      */     }
/*  544 */     while (recordSet.next()) {
/*  545 */       hashMap.clear();
/*  546 */       String str3 = recordSet.getString("searchconditiontype");
/*  547 */       String str4 = recordSet.getString("name");
/*  548 */       String str5 = ModeReportOperation.getAppLocation(Util.null2String(recordSet.getString("modetype"))) + " > " + SystemEnv.getHtmlLabelName(83327, ThreadVarLanguage.getLang()) + "(" + ("1".equalsIgnoreCase(str3) ? SystemEnv.getHtmlLabelName(524998, ThreadVarLanguage.getLang()) : SystemEnv.getHtmlLabelName(524999, ThreadVarLanguage.getLang())) + "):" + Util.null2String(recordSet.getString("modename"));
/*  549 */       String str6 = "";
/*  550 */       hashMap.put("workflowname", str4);
/*  551 */       hashMap.put("nodename", str5);
/*  552 */       hashMap.put("nodehtmllayoutid", recordSet.getString("id"));
/*  553 */       hashMap.put("modetype", "1");
/*  554 */       hashMap.put("refertable", "mode_custombrowser");
/*  555 */       recordSet.writeLog("###--开始检查 自定义浏览框(以java/sql作为条件)名称：" + str4 + "--模块名称：" + str5 + "###");
/*  556 */       if ("1".equals(str3)) {
/*  557 */         hashMap.put("detailtype", "customBrowserForSql");
/*  558 */         str6 = recordSet.getString("defaultsql");
/*  559 */         hashMap.put("contenttype", "1");
/*  560 */         hashMap.put("sourcecontent", str6);
/*  561 */         checkContent("upgradecheckmoderesult", str6, arrayList, (HashMap)hashMap);
/*      */       } 
/*      */     } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  578 */     recordSet.writeLog("###检查表单建模--结束###");
/*  579 */     runningModeStatus = 0;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void matchMobileModeDev() throws Exception {
/*  587 */     String str = Util.null2String(this.mmro.getMatchDevContentRuleid());
/*  588 */     matchMobileMode(str);
/*      */   }
/*      */   
/*      */   public void matchMobileMode(String paramString) {
/*  592 */     mobileModeErrMap.clear();
/*  593 */     RecordSet recordSet = new RecordSet();
/*  594 */     FileUtil fileUtil = new FileUtil();
/*      */     
/*  596 */     Map<String, Map<String, String>> map = this.mmro.getMobileComponentMap();
/*  597 */     ExcelSecurity excelSecurity = new ExcelSecurity();
/*  598 */     recordSet.writeLog("###检查移动引擎=--开始###");
/*  599 */     ArrayList<RuleBean> arrayList = new ArrayList();
/*  600 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*      */     
/*  602 */     if (paramString == null || "".equals(paramString)) {
/*  603 */       recordSet.execute("select * from checkmobilemoderule where content!='^[\\s\\S]*.*[^\\s][\\s\\S]*$'  order by id");
/*      */     } else {
/*  605 */       if (paramString.endsWith(",")) {
/*  606 */         paramString = paramString.substring(0, paramString.length() - 1);
/*      */       }
/*  608 */       recordSet.execute("select * from checkmobilemoderule where id in (" + paramString + ") order by id");
/*      */     } 
/*  610 */     while (recordSet.next()) {
/*  611 */       RuleBean ruleBean = new RuleBean();
/*  612 */       ruleBean.setId(recordSet.getString("id"));
/*  613 */       ruleBean.setRulename(recordSet.getString("rulename"));
/*  614 */       ruleBean.setRuledesc(recordSet.getString("ruledesc"));
/*  615 */       ruleBean.setRuletype(recordSet.getString("ruletype"));
/*  616 */       ruleBean.setChecktype(recordSet.getString("checktype"));
/*  617 */       ruleBean.setContent(recordSet.getString("content"));
/*  618 */       ruleBean.setContenttype(recordSet.getString("contenttype"));
/*  619 */       ruleBean.setReplacecontent(recordSet.getString("replacecontent"));
/*  620 */       ruleBean.setVersion(recordSet.getString("version"));
/*  621 */       arrayList.add(ruleBean);
/*      */     } 
/*      */     
/*  624 */     recordSet.executeUpdate("delete from  checkmobilemoderesult ", new Object[0]);
/*      */     
/*      */     try {
/*  627 */       List list = (List)this.mmro.getAllSkin().get("skinList");
/*  628 */       this.fileNum = list.size();
/*  629 */       byte b = 0;
/*  630 */       String str1 = "";
/*  631 */       for (Map map1 : list) {
/*  632 */         hashMap.clear();
/*  633 */         String str2 = SystemEnv.getHtmlLabelName(84213, ThreadVarLanguage.getLang()) + " > " + Util.null2String((String)map1.get("name"));
/*  634 */         hashMap.put("workflowname", map1.get("name"));
/*  635 */         hashMap.put("nodename", str2);
/*  636 */         hashMap.put("nodehtmllayoutid", map1.get("id"));
/*  637 */         hashMap.put("detailtype", "mobilemodeskin");
/*  638 */         hashMap.put("contenttype", "0");
/*  639 */         hashMap.put("refertable", "");
/*  640 */         hashMap.put("referkey", "");
/*  641 */         str1 = Util.null2String((String)map1.get("csspath"));
/*  642 */         File file = new File(str1);
/*  643 */         hashMap.put("filepath", str1);
/*  644 */         if (file.exists()) {
/*  645 */           StringBuffer stringBuffer = fileUtil.readFile(file);
/*  646 */           String str3 = stringBuffer.toString();
/*  647 */           hashMap.put("sourcecontent", str3);
/*  648 */           hashMap.put("modetype", "0");
/*  649 */           checkContent("checkmobilemoderesult", str3, arrayList, (HashMap)hashMap); continue;
/*      */         } 
/*  651 */         b++;
/*      */       } 
/*      */       
/*  654 */       if (this.fileNum != 0 && b == this.fileNum) {
/*  655 */         mobileModeErrMap.put("mbmode-cssfile", SystemEnv.getHtmlLabelName(525004, ThreadVarLanguage.getLang()) + "");
/*      */       }
/*  657 */     } catch (Exception exception) {
/*  658 */       exception.printStackTrace();
/*      */     } 
/*      */ 
/*      */     
/*  662 */     String str = "select  a.id,a.pagename as name,a.pagecontent,b.appname from apphomepage a left join MobileAppBaseInfo b on a.appid=b.id  where b.isdelete=0 or b.isdelete is null ";
/*  663 */     boolean bool = recordSet.execute(str);
/*  664 */     if (!bool) {
/*  665 */       mobileModeErrMap.put("mbmode-pagecontentsql", SystemEnv.getHtmlLabelName(525005, ThreadVarLanguage.getLang()) + "");
/*      */     }
/*  667 */     boolean bool1 = false;
/*  668 */     while (recordSet.next()) {
/*  669 */       hashMap.clear();
/*  670 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  671 */       String str1 = Util.null2String(recordSet.getString("appname"));
/*  672 */       String str2 = "应用:" + str1 + " > " + MobileModeReportOperation.getFolderPath(recordSet.getString("id"));
/*  673 */       String str3 = Util.null2String(recordSet.getString("pagecontent"));
/*  674 */       hashMap.put("workflowname", str1);
/*  675 */       hashMap.put("nodename", str2);
/*  676 */       hashMap.put("nodehtmllayoutid", recordSet.getString("id"));
/*  677 */       hashMap.put("detailtype", "mobilemodepage");
/*  678 */       hashMap.put("modetype", "1");
/*  679 */       hashMap.put("contenttype", "0");
/*  680 */       hashMap.put("refertable", "apphomepage");
/*  681 */       hashMap.put("referkey", "");
/*  682 */       if (!"".equals(str3)) {
/*  683 */         Document document = Jsoup.parse(str3);
/*  684 */         document.select("#homepageContainer").remove();
/*  685 */         str3 = document.html();
/*  686 */         hashMap.put("sourcecontent", str3);
/*  687 */         checkContent("checkmobilemoderesult", str3, arrayList, (HashMap)hashMap);
/*      */       } 
/*      */     } 
/*      */ 
/*      */     
/*  692 */     str = "select  a.id,a.pagename as name,a.pageattr,b.appname from apphomepage a left join MobileAppBaseInfo b on a.appid=b.id where b.isdelete=0 or b.isdelete is null ";
/*  693 */     bool = recordSet.execute(str);
/*  694 */     if (!bool) {
/*  695 */       mobileModeErrMap.put("mbmode-pageattrsql", SystemEnv.getHtmlLabelName(525006, ThreadVarLanguage.getLang()) + "");
/*      */     }
/*  697 */     while (recordSet.next()) {
/*  698 */       hashMap.clear();
/*  699 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  700 */       String str1 = Util.null2String(recordSet.getString("appname"));
/*  701 */       String str2 = SystemEnv.getHtmlLabelName(514292, ThreadVarLanguage.getLang()) + ":" + str1 + " > " + MobileModeReportOperation.getFolderPath(recordSet.getString("id")) + ">" + SystemEnv.getHtmlLabelName(130639, ThreadVarLanguage.getLang());
/*  702 */       String str3 = Util.null2String(recordSet.getString("pageattr"));
/*  703 */       hashMap.put("workflowname", str1);
/*  704 */       hashMap.put("nodename", str2);
/*  705 */       hashMap.put("nodehtmllayoutid", recordSet.getString("id"));
/*  706 */       hashMap.put("detailtype", "mobilemodepageattr");
/*  707 */       hashMap.put("modetype", "1");
/*  708 */       hashMap.put("contenttype", "0");
/*  709 */       hashMap.put("refertable", "apphomepage");
/*  710 */       hashMap.put("referkey", "");
/*  711 */       if (!"".equals(str3)) {
/*  712 */         hashMap.put("sourcecontent", str3);
/*  713 */         checkContent("checkmobilemoderesult", str3, arrayList, (HashMap)hashMap);
/*      */       } 
/*      */     } 
/*      */ 
/*      */     
/*  718 */     recordSet.execute("select a.id,a.mectype,a.mecparam,b.pagename,b.id as pageid,c.appname from MOBILEEXTENDCOMPONENT a left join APPHOMEPAGE b on a.objid=b.id left join MobileAppBaseInfo c on b.appid = c.id left join MobileAppBaseInfo d on b.appid=d.id where d.isdelete!=1");
/*  719 */     while (recordSet.next()) {
/*  720 */       hashMap.clear();
/*  721 */       String str1 = Util.null2String(recordSet.getString("mectype"));
/*  722 */       String str2 = recordSet.getString("mecparam");
/*  723 */       String str3 = Util.null2String(recordSet.getString("appname"));
/*  724 */       String str4 = Util.null2String(recordSet.getString("id"));
/*  725 */       Map map1 = map.get(str1.toLowerCase());
/*  726 */       if (map1 == null)
/*  727 */         continue;  String str5 = "应用:" + str3 + " > " + MobileModeReportOperation.getFolderPath(recordSet.getString("pageid"));
/*  728 */       hashMap.put("workflowname", str3);
/*  729 */       hashMap.put("nodehtmllayoutid", str4);
/*  730 */       hashMap.put("modetype", "1");
/*  731 */       List<String> list1 = this.mmro.jsonToList(str2);
/*      */       
/*  733 */       List<String> list2 = Arrays.asList(((String)map1.get("html")).split(","));
/*  734 */       for (String str6 : list2) {
/*  735 */         for (String str7 : list1) {
/*  736 */           if (str7.toLowerCase().indexOf(str6.toLowerCase() + "==>>==") == 0) {
/*  737 */             str7 = str7.substring(str7.indexOf("==>>==") + 6);
/*  738 */             if (!"".equals(str6) && !"".equals(str7)) {
/*  739 */               hashMap.put("detailtype", "mobileexpendcomponent_html");
/*  740 */               hashMap.put("contenttype", "0");
/*  741 */               hashMap.put("refertable", "mobileextendcomponent");
/*  742 */               hashMap.put("sourcecontent", str7);
/*  743 */               hashMap.put("nodename", str5 + " > " + SystemEnv.getHtmlLabelName(525007, ThreadVarLanguage.getLang()) + ":" + (String)map1.get("name"));
/*  744 */               hashMap.put("referkey", str6);
/*      */ 
/*      */               
/*  747 */               if ("FLBS".equalsIgnoreCase(str1) && "backscript".equalsIgnoreCase(str6)) {
/*      */                 try {
/*  749 */                   str7 = URLDecoder.decode(str7, "UTF-8");
/*  750 */                 } catch (Exception exception) {
/*  751 */                   exception.printStackTrace();
/*      */                 } 
/*      */               }
/*  754 */               checkContent("checkmobilemoderesult", str7, arrayList, (HashMap)hashMap);
/*      */             } 
/*      */           } 
/*      */         } 
/*      */       } 
/*      */       
/*  760 */       list2 = Arrays.asList(((String)map1.get("sql")).split(","));
/*  761 */       for (String str6 : list2) {
/*  762 */         for (String str7 : list1) {
/*  763 */           if (str7.toLowerCase().indexOf(str6.toLowerCase() + "==>>==") == 0) {
/*  764 */             str7 = str7.substring(str7.indexOf("==>>==") + 6);
/*  765 */             if (!"".equals(str6) && !"".equals(str7)) {
/*  766 */               hashMap.put("detailtype", "mobileexpendcomponent_sql");
/*  767 */               hashMap.put("contenttype", "1");
/*  768 */               hashMap.put("refertable", "mobileextendcomponent");
/*  769 */               hashMap.put("sourcecontent", str7);
/*  770 */               hashMap.put("nodename", str5 + " > " + SystemEnv.getHtmlLabelName(525008, ThreadVarLanguage.getLang()) + ":" + (String)map1.get("name"));
/*  771 */               hashMap.put("referkey", str6);
/*  772 */               checkContent("checkmobilemoderesult", str7, arrayList, (HashMap)hashMap);
/*      */             } 
/*      */           } 
/*      */         } 
/*      */       } 
/*      */     } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  789 */     recordSet.writeLog("###检查移动引擎--结束###");
/*  790 */     runningMobileModeStatus = 0;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void checkContent(String paramString1, String paramString2, ArrayList<RuleBean> paramArrayList, HashMap<String, String> paramHashMap) {
/*  799 */     if (paramString2 == null || "".equals(paramString2)) {
/*      */       return;
/*      */     }
/*  802 */     if (defaultHtmlStrList.contains(replaceBlank(paramString2).toLowerCase())) {
/*      */       return;
/*      */     }
/*  805 */     String str1 = paramString1;
/*  806 */     String str2 = UUID.randomUUID().toString().replace("-", "");
/*  807 */     if (paramArrayList != null) {
/*  808 */       for (byte b = 0; b < paramArrayList.size(); b++) {
/*  809 */         RuleBean ruleBean = paramArrayList.get(b);
/*  810 */         String str3 = ruleBean.getRuletype();
/*  811 */         String str4 = ruleBean.getContent();
/*  812 */         String str5 = ruleBean.getId();
/*  813 */         String str6 = ruleBean.getRulename();
/*  814 */         String str7 = ruleBean.getRuledesc();
/*  815 */         String str8 = ruleBean.getChecktype();
/*  816 */         String str9 = ruleBean.getContenttype();
/*  817 */         String str10 = ruleBean.getVersion();
/*  818 */         String str11 = ruleBean.getReplacecontent();
/*      */         
/*  820 */         String str12 = paramHashMap.get("workflowname");
/*  821 */         String str13 = paramHashMap.get("nodename");
/*  822 */         String str14 = paramHashMap.get("modetype");
/*  823 */         String str15 = (paramHashMap.get("filepath") == null) ? "" : paramHashMap.get("filepath");
/*  824 */         String str16 = paramHashMap.get("nodehtmllayoutid");
/*  825 */         String str17 = "";
/*  826 */         String str18 = "";
/*  827 */         String str19 = Util.null2String(paramHashMap.get("refertable"));
/*  828 */         String str20 = "";
/*  829 */         String str21 = paramHashMap.get("sourcecontent");
/*  830 */         if ("upgradecheckmoderesult".equalsIgnoreCase(paramString1) || "checkmobilemoderesult".equalsIgnoreCase(paramString1)) {
/*  831 */           str17 = paramHashMap.get("detailtype");
/*  832 */           str18 = paramHashMap.get("contenttype");
/*  833 */           str20 = Util.null2String(paramHashMap.get("referkey"));
/*  834 */           if (!str18.equals(str9)) {
/*      */             continue;
/*      */           }
/*      */         } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  843 */         if ("1".equals(str3)) {
/*      */           try {
/*  845 */             Class<?> clazz = Class.forName(str4);
/*  846 */             Method method = clazz.getMethod("checkRule", new Class[] { String.class });
/*  847 */             List list = (List)method.invoke(clazz.newInstance(), new Object[] { paramString2 });
/*  848 */             for (String str22 : list) {
/*  849 */               HashMap<Object, Object> hashMap = new HashMap<>();
/*  850 */               hashMap.put("ruleid", str5);
/*  851 */               hashMap.put("rulename", str6);
/*  852 */               hashMap.put("ruledesc", str7);
/*  853 */               hashMap.put("ruletype", str3);
/*  854 */               hashMap.put("checktype", str8);
/*  855 */               hashMap.put("workflowname", str12);
/*  856 */               hashMap.put("nodename", str13);
/*  857 */               hashMap.put("content", str4);
/*  858 */               String str23 = str11;
/*  859 */               if ("".equals(str23)) {
/*  860 */                 str23 = "" + CANNOTREPLACE;
/*      */               }
/*  862 */               hashMap.put("replacecontent", str23);
/*      */               
/*  864 */               str15 = str15.replaceAll("\\\\+", "/");
/*  865 */               hashMap.put("filepath", str15);
/*  866 */               hashMap.put("version", str10);
/*  867 */               hashMap.put("matchcontent", str22);
/*  868 */               hashMap.put("line", "");
/*  869 */               hashMap.put("modetype", str14);
/*  870 */               hashMap.put("nodehtmllayoutid", str16);
/*  871 */               if ("upgradecheckmoderesult".equalsIgnoreCase(paramString1) || "checkmobilemoderesult".equalsIgnoreCase(paramString1)) {
/*  872 */                 hashMap.put("refertable", str19);
/*  873 */                 hashMap.put("detailtype", str17);
/*  874 */                 hashMap.put("referkey", str20);
/*  875 */                 hashMap.put("contenttype", str9);
/*  876 */                 hashMap.put("sameSourceContentFlag", str2);
/*      */                 
/*  878 */                 if (!"".equals(str20) && "checkmobilemoderesult".equalsIgnoreCase(paramString1)) {
/*  879 */                   String str = "";
/*  880 */                   if (str20.contains(".")) {
/*  881 */                     str = str20.substring(str20.lastIndexOf(".") + 1);
/*      */                   } else {
/*  883 */                     str = str20;
/*      */                   } 
/*  885 */                   JSONObject jSONObject = new JSONObject();
/*  886 */                   jSONObject.put(str, str21);
/*  887 */                   str21 = jSONObject.toJSONString();
/*  888 */                   if (str21.startsWith("{") && str21.endsWith("}")) {
/*  889 */                     str21 = str21.substring(1, str21.length() - 1);
/*      */                   }
/*      */                 } 
/*  892 */                 hashMap.put("sourcecontent", str21);
/*      */               } 
/*      */               
/*  895 */               insert2DB((HashMap)hashMap, str1);
/*  896 */               if ("checkmobilemoderesult".equalsIgnoreCase(paramString1) && !"".equals(str20)) {
/*      */                 break;
/*      */               }
/*      */             } 
/*  900 */           } catch (Exception exception) {
/*  901 */             exception.printStackTrace();
/*  902 */             (new BaseBean()).writeLog("请检查接口" + str4 + "是否正确!!!错误信息为：" + exception.toString());
/*      */           }
/*      */         
/*      */         } else {
/*      */           
/*  907 */           Pattern pattern = null;
/*  908 */           Matcher matcher = null;
/*      */           try {
/*  910 */             pattern = Pattern.compile(str4);
/*  911 */             matcher = pattern.matcher(paramString2);
/*  912 */           } catch (Exception exception) {
/*  913 */             (new BaseBean()).writeLog("正则匹配出错" + exception.getMessage() + exception);
/*      */           } 
/*      */ 
/*      */           
/*  917 */           while (matcher.find()) {
/*  918 */             HashMap<Object, Object> hashMap = new HashMap<>();
/*  919 */             hashMap.put("ruleid", str5);
/*  920 */             hashMap.put("rulename", str6);
/*  921 */             hashMap.put("ruledesc", str7);
/*  922 */             hashMap.put("ruletype", str3);
/*  923 */             hashMap.put("checktype", str8);
/*  924 */             if (!"upgradecheckresult".equals(paramString1)) {
/*  925 */               hashMap.put("workflowname", str12);
/*  926 */               hashMap.put("nodename", str13);
/*  927 */               hashMap.put("modetype", str14);
/*  928 */               hashMap.put("nodehtmllayoutid", str16);
/*      */             } 
/*      */             
/*  931 */             hashMap.put("content", str4);
/*  932 */             String str = str11;
/*  933 */             if ("".equals(str)) {
/*  934 */               str = "" + CANNOTREPLACE;
/*      */             }
/*  936 */             hashMap.put("replacecontent", "" + str);
/*      */             
/*  938 */             str15 = str15.replaceAll("\\\\", "/");
/*  939 */             hashMap.put("filepath", str15);
/*  940 */             hashMap.put("version", str10);
/*  941 */             hashMap.put("matchcontent", matcher.group());
/*  942 */             hashMap.put("line", "");
/*      */             
/*  944 */             if ("upgradecheckmoderesult".equalsIgnoreCase(paramString1) || "checkmobilemoderesult".equalsIgnoreCase(paramString1)) {
/*  945 */               hashMap.put("detailtype", str17);
/*  946 */               hashMap.put("refertable", str19);
/*  947 */               hashMap.put("referkey", str20);
/*  948 */               hashMap.put("contenttype", str9);
/*  949 */               hashMap.put("sameSourceContentFlag", str2);
/*      */               
/*  951 */               if (!"".equals(str20) && "checkmobilemoderesult".equalsIgnoreCase(paramString1)) {
/*  952 */                 String str22 = "";
/*  953 */                 if (str20.contains(".")) {
/*  954 */                   str22 = str20.substring(str20.lastIndexOf(".") + 1);
/*      */                 } else {
/*  956 */                   str22 = str20;
/*      */                 } 
/*  958 */                 JSONObject jSONObject = new JSONObject();
/*  959 */                 jSONObject.put(str22, str21);
/*  960 */                 str21 = jSONObject.toJSONString();
/*  961 */                 if (str21.startsWith("{") && str21.endsWith("}")) {
/*  962 */                   str21 = str21.substring(1, str21.length() - 1);
/*      */                 }
/*      */               } 
/*  965 */               hashMap.put("sourcecontent", str21);
/*      */             } 
/*      */             
/*  968 */             insert2DB((HashMap)hashMap, str1);
/*      */             
/*  970 */             if ("checkmobilemoderesult".equalsIgnoreCase(paramString1) && !"".equals(str20)) {
/*      */               break;
/*      */             }
/*      */           } 
/*      */         } 
/*      */         continue;
/*      */       } 
/*      */     }
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void insert2DB2(HashMap<String, String> paramHashMap, String paramString) {
/*  987 */     RecordSet recordSet = new RecordSet();
/*  988 */     String str1 = recordSet.getDBType();
/*  989 */     List list = new ArrayList();
/*  990 */     String str2 = "DECLARE ";
/*  991 */     if ("Oracle".equalsIgnoreCase(str1)) {
/*  992 */       if (clobmap.get(paramString.toLowerCase()) == null) {
/*  993 */         String str = "SELECT COLUMN_NAME,DATA_TYPE FROM USER_TAB_COLUMNS WHERE TABLE_NAME='" + paramString.toUpperCase() + "'";
/*  994 */         recordSet.executeQuery(str, new Object[0]);
/*  995 */         while (recordSet.next()) {
/*  996 */           String str6 = recordSet.getString("COLUMN_NAME");
/*  997 */           String str7 = recordSet.getString("DATA_TYPE");
/*  998 */           if (str7.equalsIgnoreCase("clob")) {
/*  999 */             list.add(str6.toLowerCase());
/*      */           }
/*      */         } 
/* 1002 */         clobmap.put(paramString, list);
/*      */       } else {
/* 1004 */         list = clobmap.get(paramString.toLowerCase());
/*      */       } 
/*      */     }
/*      */     
/* 1008 */     String str3 = "insert into " + paramString;
/* 1009 */     Iterator<String> iterator = paramHashMap.keySet().iterator();
/* 1010 */     String str4 = "";
/* 1011 */     String str5 = "";
/* 1012 */     byte b = 0;
/* 1013 */     while (iterator.hasNext()) {
/* 1014 */       b++;
/* 1015 */       String str6 = iterator.next();
/* 1016 */       String str7 = Util.null2String(paramHashMap.get(str6)).replaceAll("'", "''");
/* 1017 */       str4 = str4 + str6 + ",";
/* 1018 */       if (list.contains(str6.toLowerCase())) {
/* 1019 */         str2 = str2 + " column" + b + " CLOB := '" + str7 + "';";
/* 1020 */         str5 = str5 + "column" + b + ","; continue;
/*      */       } 
/* 1022 */       str5 = str5 + "'" + str7 + "',";
/*      */     } 
/*      */     
/* 1025 */     str4 = " (" + str4.substring(0, str4.length() - 1) + ")";
/*      */     
/* 1027 */     str5 = " (" + str5.substring(0, str5.length() - 1) + ")";
/* 1028 */     str3 = str3 + str4 + " values " + str5;
/* 1029 */     if (list.size() > 0) {
/* 1030 */       str2 = str2 + " BEGIN ";
/* 1031 */       str3 = str2 + str3 + "; end;";
/*      */     } 
/* 1033 */     recordSet.executeUpdate(str3, new Object[0]);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private static Set<String> getCLobField(String paramString) {
/* 1042 */     RecordSet recordSet = new RecordSet();
/* 1043 */     String str = recordSet.getDBType();
/*      */     
/* 1045 */     if (StringUtil.isNotNull(str) && str.toLowerCase().contains("oracle")) {
/* 1046 */       String str1 = " select A.COLUMN_NAME,A.DATA_TYPE  from user_tab_columns A where TABLE_NAME='" + paramString.toUpperCase() + "' and ( A.DATA_TYPE = 'CLOB'  or A.DATA_LENGTH>=4000)";
/*      */       
/* 1048 */       HashSet<String> hashSet = new HashSet();
/* 1049 */       recordSet.execute(str1);
/* 1050 */       while (recordSet.next()) {
/* 1051 */         String str2 = recordSet.getString(1);
/* 1052 */         if (null != str2 && str2.length() > 0) {
/* 1053 */           hashSet.add(str2.toUpperCase());
/*      */         }
/*      */       } 
/* 1056 */       return hashSet;
/*      */     } 
/* 1058 */     return null;
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public void insert2DB(HashMap<String, String> paramHashMap, String paramString) {
/* 1064 */     String str1 = "insert into " + paramString;
/* 1065 */     Iterator<String> iterator = paramHashMap.keySet().iterator();
/* 1066 */     String str2 = "";
/* 1067 */     String str3 = "";
/*      */     
/* 1069 */     while (iterator.hasNext()) {
/* 1070 */       String str = iterator.next();
/* 1071 */       str2 = str2 + str + ",";
/* 1072 */       str3 = str3 + "?,";
/*      */     } 
/* 1074 */     str2 = " (" + str2.substring(0, str2.length() - 1) + ")";
/*      */     
/* 1076 */     str3 = " (" + str3.substring(0, str3.length() - 1) + ")";
/* 1077 */     str1 = str1 + str2 + " values " + str3;
/* 1078 */     Set<String> set = getCLobField(paramString);
/* 1079 */     ConnStatement connStatement = new ConnStatement();
/*      */     
/*      */     try {
/* 1082 */       connStatement.setStatementSql(str1);
/* 1083 */       byte b = 0;
/* 1084 */       Iterator<String> iterator1 = paramHashMap.keySet().iterator();
/* 1085 */       while (iterator1.hasNext()) {
/* 1086 */         String str4 = iterator1.next();
/* 1087 */         String str5 = paramHashMap.get(str4);
/* 1088 */         b++;
/* 1089 */         if (null != set && set.contains(str4.toUpperCase()) && str5.length() >= 4000) {
/* 1090 */           connStatement.setCharacterStream(b, str5); continue;
/*      */         } 
/* 1092 */         connStatement.setString(b, str5);
/*      */       } 
/*      */ 
/*      */       
/* 1096 */       connStatement.executeUpdate();
/* 1097 */     } catch (Exception exception) {
/* 1098 */       throw new RuntimeException(exception);
/*      */     } finally {
/*      */       try {
/* 1101 */         connStatement.close();
/* 1102 */       } catch (Exception exception) {}
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void match(File paramFile, List<Map<String, String>> paramList1, List<Map<String, String>> paramList2) throws Exception {}
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String changeStr3(String paramString) {
/* 1119 */     if (paramString != null) {
/* 1120 */       paramString = paramString.replaceAll("&amp;", "&");
/*      */       
/* 1122 */       paramString = paramString.replaceAll("&nbsp;", " ");
/* 1123 */       paramString = paramString.replaceAll("&quot;", "\"");
/* 1124 */       paramString = paramString.replaceAll("&apos;", "'");
/* 1125 */       paramString = paramString.replaceAll("&124;", "\\|");
/* 1126 */       paramString = paramString.replaceAll("&94;", "\\^");
/* 1127 */       paramString = paramString.replaceAll("\\\\n", "");
/* 1128 */       paramString = paramString.replaceAll("<", "&lt;");
/* 1129 */       paramString = paramString.replaceAll(">", "&gt;");
/*      */     } 
/*      */     
/* 1132 */     return paramString;
/*      */   }
/*      */   
/*      */   private String changeStr4(String paramString) {
/* 1136 */     if (paramString != null) {
/* 1137 */       paramString = paramString.replace("<", "&lt;");
/* 1138 */       paramString = paramString.replace(">", "&gt;");
/*      */     } 
/* 1140 */     return paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static boolean checkDBFieldIsExist(String paramString1, String paramString2) {
/* 1150 */     RecordSet recordSet = new RecordSet();
/* 1151 */     boolean bool = false;
/*      */     try {
/* 1153 */       StringBuffer stringBuffer = new StringBuffer();
/* 1154 */       if (recordSet.getDBType().equals("oracle")) {
/* 1155 */         stringBuffer.append("select 1 ");
/* 1156 */         stringBuffer.append("\t  from user_tab_columns ");
/* 1157 */         stringBuffer.append("\t where LOWER(table_name) = '" + paramString2 + "' ");
/* 1158 */         stringBuffer.append("\t   and LOWER(column_name) = '" + paramString1 + "'");
/* 1159 */       } else if (recordSet.getDBType().equals("mysql")) {
/* 1160 */         stringBuffer.append("select 1 from Information_schema.columns where LOWER(table_name) = '" + paramString2.toLowerCase() + "'");
/* 1161 */         stringBuffer.append("\t   and LOWER(column_name) = '" + paramString1 + "'");
/*      */       }
/* 1163 */       else if (recordSet.getDBType().equals("postgresql")) {
/* 1164 */         stringBuffer.append("select 1 from Information_schema.columns where LOWER(table_name) = '" + paramString2.toLowerCase() + "'");
/* 1165 */         stringBuffer.append("\t   and LOWER(column_name) = '" + paramString1 + "'");
/*      */       } else {
/*      */         
/* 1168 */         stringBuffer.append("select 1 ");
/* 1169 */         stringBuffer.append("  from syscolumns c ");
/* 1170 */         stringBuffer.append(" where objectproperty(c.id, 'IsUserTable') = 1 ");
/* 1171 */         stringBuffer.append("   and object_name(c.id) = '" + paramString2 + "' ");
/* 1172 */         stringBuffer.append("   and c.name = '" + paramString1 + "'");
/*      */       } 
/*      */       
/* 1175 */       recordSet.executeQuery(stringBuffer.toString(), new Object[0]);
/* 1176 */       while (recordSet.next()) {
/* 1177 */         if (recordSet.getString(1).equals("1")) {
/* 1178 */           bool = true;
/*      */         }
/*      */       } 
/* 1181 */     } catch (Exception exception) {
/* 1182 */       exception.printStackTrace();
/*      */     } 
/* 1184 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void checkAllPart(String paramString) {
/* 1196 */     if (!paramString.endsWith("/") && !paramString.endsWith("\\")) {
/* 1197 */       paramString = paramString + File.separatorChar;
/*      */     }
/* 1199 */     ArrayList arrayList = new ArrayList();
/* 1200 */     String str = "";
/* 1201 */     isrunpartStatus = 1;
/* 1202 */     (new BaseBean()).writeLog("开始检测流程模板");
/* 1203 */     ExportReportOperation exportReportOperation = new ExportReportOperation();
/*      */     try {
/* 1205 */       String str1 = getWorkflowCheckRule();
/* 1206 */       matchWorkflow();
/*      */       
/* 1208 */       exportReportOperation.setCheckedWorkflowFile(0);
/* 1209 */       exportReportOperation.setTotolWorkflowFile();
/* 1210 */       exportReportOperation.getReportXls("workflowBatch", arrayList);
/* 1211 */       exportReportOperation.getReportXls("allWorkflow", arrayList);
/* 1212 */     } catch (Exception exception) {
/* 1213 */       exception.printStackTrace();
/*      */     } 
/* 1215 */     isrunpartStatus = 2;
/* 1216 */     (new BaseBean()).writeLog("开始移动模板检测");
/*      */     try {
/* 1218 */       matchMobileModeDev();
/* 1219 */       exportReportOperation.getReportXls("mobilemode", arrayList);
/* 1220 */     } catch (Exception exception) {
/* 1221 */       exception.printStackTrace();
/*      */     } 
/* 1223 */     isrunpartStatus = 3;
/* 1224 */     (new BaseBean()).writeLog("开始表单模板检测");
/*      */     try {
/* 1226 */       matchModeDev();
/*      */ 
/*      */ 
/*      */       
/* 1230 */       exportReportOperation.getReportXls("modeBatch", arrayList);
/* 1231 */     } catch (Exception exception) {
/* 1232 */       exception.printStackTrace();
/*      */     } 
/* 1234 */     (new BaseBean()).writeLog("开始自定义开发模板检测");
/* 1235 */     isrunpartStatus = 4;
/*      */     try {
/* 1237 */       HashMap<Object, Object> hashMap = new HashMap<>();
/* 1238 */       str = GCONST.getRootPath() + File.separatorChar + "templetecheck" + File.separatorChar + "report" + File.separatorChar + "CustomCheck.xls";
/* 1239 */       getTempleteheckReport("CustomCheck", str, arrayList, hashMap);
/*      */     }
/* 1241 */     catch (Exception exception) {
/* 1242 */       exception.printStackTrace();
/*      */     } 
/* 1244 */     isrunpartStatus = 5;
/*      */ 
/*      */ 
/*      */     
/* 1248 */     ZipUtils.execute(arrayList, paramString + "templetecheckReport_all.zip", GCONST.getRootPath() + "templetecheck" + File.separatorChar + "report" + File.separatorChar, "templetecheckReport_all");
/* 1249 */     isrunpartStatus = 6;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void getTempleteheckReport(String paramString1, String paramString2, List<String> paramList, Map paramMap) {
/* 1258 */     ExportReportOperation exportReportOperation = new ExportReportOperation();
/*      */     try {
/* 1260 */       File file = new File(paramString2);
/* 1261 */       if (file.exists()) {
/* 1262 */         file.delete();
/*      */       }
/* 1264 */       exportReportOperation.exportReport(paramString1);
/* 1265 */       file = new File(paramString2);
/* 1266 */       if (file.exists()) {
/* 1267 */         paramList.add(file.getAbsolutePath());
/*      */       }
/* 1269 */     } catch (Exception exception) {
/* 1270 */       exception.printStackTrace();
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getWorkflowCheckRule() {
/* 1280 */     RecordSet recordSet = new RecordSet();
/* 1281 */     String str = "";
/* 1282 */     recordSet.executeQuery(" select id from upgradecheckworkflowrule ", new Object[0]);
/* 1283 */     while (recordSet.next()) {
/* 1284 */       str = str + "" + Util.null2String(recordSet.getString("id")) + ",";
/*      */     }
/* 1286 */     return str;
/*      */   }
/*      */   
/*      */   public static String replaceBlank(String paramString) {
/* 1290 */     String str = "";
/* 1291 */     if (paramString != null) {
/* 1292 */       Pattern pattern = Pattern.compile("\\s*|\t|\r|\n");
/* 1293 */       Matcher matcher = pattern.matcher(paramString);
/* 1294 */       str = matcher.replaceAll("");
/*      */     } 
/* 1296 */     return str;
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/filecheck/CheckRule.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */