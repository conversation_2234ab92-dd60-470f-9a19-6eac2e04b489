/*     */ package weaver.templetecheck;
/*     */ 
/*     */ import java.lang.reflect.Method;
/*     */ import javax.xml.parsers.DocumentBuilderFactory;
/*     */ import org.jdom.input.SAXBuilder;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class TempleteSecurityUtil
/*     */ {
/*     */   public static void setDBFFeature(DocumentBuilderFactory paramDocumentBuilderFactory) {
/*     */     try {
/*  21 */       Class<?> clazz = Class.forName("weaver.security.util.SecurityMethodUtil");
/*  22 */       if (clazz != null) {
/*  23 */         Object object = clazz.newInstance();
/*  24 */         Method method = clazz.getMethod("setDBFFeature", new Class[] { DocumentBuilderFactory.class });
/*  25 */         if (method != null) {
/*  26 */           method.invoke(object, new Object[] { paramDocumentBuilderFactory });
/*     */         } else {
/*  28 */           setDBFFeature1(paramDocumentBuilderFactory);
/*     */         } 
/*     */       } else {
/*  31 */         setDBFFeature1(paramDocumentBuilderFactory);
/*     */       } 
/*  33 */     } catch (Throwable throwable) {
/*  34 */       setDBFFeature1(paramDocumentBuilderFactory);
/*  35 */       throwable.printStackTrace();
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void setSaxBuilderFeature(SAXBuilder paramSAXBuilder) {
/*     */     try {
/*  47 */       Class<?> clazz = Class.forName("weaver.security.util.SecurityMethodUtil");
/*  48 */       if (clazz != null) {
/*  49 */         Object object = clazz.newInstance();
/*  50 */         Method method = clazz.getMethod("setSaxBuilderFeature", new Class[] { SAXBuilder.class });
/*  51 */         if (method != null) {
/*  52 */           method.invoke(object, new Object[] { paramSAXBuilder });
/*     */         } else {
/*  54 */           setSaxBuilderFeature1(paramSAXBuilder);
/*     */         } 
/*     */       } else {
/*  57 */         setSaxBuilderFeature1(paramSAXBuilder);
/*     */       } 
/*  59 */     } catch (Throwable throwable) {
/*  60 */       setSaxBuilderFeature1(paramSAXBuilder);
/*  61 */       throwable.printStackTrace();
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void setDBFFeature1(DocumentBuilderFactory paramDocumentBuilderFactory) {
/*     */     try {
/*  74 */       paramDocumentBuilderFactory.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
/*  75 */     } catch (Exception exception) {}
/*     */     try {
/*  77 */       paramDocumentBuilderFactory.setFeature("http://xml.org/sax/features/external-parameter-entities", false);
/*  78 */     } catch (Exception exception) {}
/*     */     try {
/*  80 */       paramDocumentBuilderFactory.setFeature("http://apache.org/xml/features/nonvalidating/load-external-dtd", false);
/*  81 */     } catch (Exception exception) {}
/*     */     try {
/*  83 */       paramDocumentBuilderFactory.setFeature("http://xml.org/sax/features/external-general-entities", false);
/*  84 */     } catch (Exception exception) {}
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void setSaxBuilderFeature1(SAXBuilder paramSAXBuilder) {
/*     */     try {
/*  95 */       paramSAXBuilder.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
/*  96 */     } catch (Exception exception) {}
/*     */     try {
/*  98 */       paramSAXBuilder.setFeature("http://xml.org/sax/features/external-general-entities", false);
/*  99 */     } catch (Exception exception) {}
/*     */     try {
/* 101 */       paramSAXBuilder.setFeature("http://xml.org/sax/features/external-parameter-entities", false);
/* 102 */     } catch (Exception exception) {}
/*     */     try {
/* 104 */       paramSAXBuilder.setFeature("http://apache.org/xml/features/nonvalidating/load-external-dtd", false);
/* 105 */     } catch (Exception exception) {}
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/TempleteSecurityUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */