/*    */ package weaver.templetecheck;
/*    */ 
/*    */ import java.util.List;
/*    */ import org.dom4j.Element;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class XMLConfigBean
/*    */ {
/*    */   public String filepath;
/*    */   public String from;
/*    */   private String action;
/*    */   public String note;
/*    */   private Element beforeElement;
/*    */   private Element afterElement;
/*    */   private List<Element> content;
/*    */   public static final String ACTION_ADD = "add";
/*    */   public static final String ACTION_DELETE = "delete";
/*    */   public static final String ACTION_BATCH = "batch";
/*    */   
/*    */   public XMLConfigBean(String paramString1, String paramString2, Element paramElement1, Element paramElement2, List<Element> paramList, String paramString3) {
/* 24 */     this.filepath = "/WEB-INF/web.xml";
/* 25 */     this.from = paramString1;
/* 26 */     this.action = paramString2;
/* 27 */     this.beforeElement = paramElement1;
/* 28 */     this.afterElement = paramElement2;
/* 29 */     this.content = paramList;
/* 30 */     this.note = paramString3;
/*    */   }
/*    */   
/*    */   public XMLConfigBean(String paramString1, String paramString2, String paramString3, Element paramElement1, Element paramElement2, List<Element> paramList, String paramString4) {
/* 34 */     this.filepath = paramString1;
/* 35 */     this.from = paramString2;
/* 36 */     this.action = paramString3;
/* 37 */     this.beforeElement = paramElement1;
/* 38 */     this.afterElement = paramElement2;
/* 39 */     this.content = paramList;
/* 40 */     this.note = paramString4;
/*    */   }
/*    */   
/*    */   public String getAction() {
/* 44 */     return this.action;
/*    */   }
/*    */   
/*    */   public void setAction(String paramString) {
/* 48 */     this.action = paramString;
/*    */   }
/*    */   
/*    */   public Element getBeforeElement() {
/* 52 */     return this.beforeElement;
/*    */   }
/*    */   
/*    */   public void setBeforeElement(Element paramElement) {
/* 56 */     this.beforeElement = paramElement;
/*    */   }
/*    */   
/*    */   public Element getAfterElement() {
/* 60 */     return this.afterElement;
/*    */   }
/*    */   
/*    */   public void setAfterElement(Element paramElement) {
/* 64 */     this.afterElement = paramElement;
/*    */   }
/*    */   
/*    */   public List<Element> getContent() {
/* 68 */     return this.content;
/*    */   }
/*    */   
/*    */   public void setContent(List<Element> paramList) {
/* 72 */     this.content = paramList;
/*    */   }
/*    */   
/*    */   public String getFilepath() {
/* 76 */     return this.filepath;
/*    */   }
/*    */   
/*    */   public void setFilepath(String paramString) {
/* 80 */     this.filepath = paramString;
/*    */   }
/*    */   
/*    */   public String getFrom() {
/* 84 */     return this.from;
/*    */   }
/*    */   
/*    */   public void setFrom(String paramString) {
/* 88 */     this.from = paramString;
/*    */   }
/*    */   public String getNote() {
/* 91 */     return this.note;
/*    */   }
/*    */   
/*    */   public void setNote(String paramString) {
/* 95 */     this.note = paramString;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/XMLConfigBean.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */