/*     */ package weaver.templetecheck;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class KBVersionCompare
/*     */ {
/*     */   public int compareVersion(String paramString1, String paramString2) {
/*     */     int i;
/*  13 */     if ((paramString1 == null || "".equals(paramString1)) && (paramString2 == null || "".equals(paramString2))) {
/*  14 */       return 0;
/*     */     }
/*     */     
/*  17 */     if (paramString1 == null || "".equals(paramString1)) {
/*  18 */       return 1;
/*     */     }
/*  20 */     if (paramString2 == null || "".equals(paramString2)) {
/*  21 */       return -1;
/*     */     }
/*  23 */     paramString1 = paramString1.trim();
/*  24 */     paramString2 = paramString2.trim();
/*  25 */     String[] arrayOfString1 = paramString1.split("\\+");
/*  26 */     String[] arrayOfString2 = paramString2.split("\\+");
/*     */ 
/*     */     
/*  29 */     if (arrayOfString1.length == 2) {
/*  30 */       String str1 = arrayOfString1[0];
/*  31 */       String str2 = arrayOfString1[1];
/*  32 */       str1 = trim(str1);
/*  33 */       str2 = trim(str2);
/*     */       
/*  35 */       String str3 = null;
/*  36 */       String str4 = null;
/*  37 */       if (arrayOfString2.length == 2) {
/*  38 */         str3 = arrayOfString2[0];
/*  39 */         str4 = arrayOfString2[1];
/*  40 */         str3 = trim(str3);
/*  41 */         str4 = trim(str4);
/*     */         
/*  43 */         i = str3.compareTo(str1);
/*  44 */         if (i == 0) {
/*  45 */           i = str4.compareTo(str2);
/*     */         }
/*     */       } else {
/*  48 */         String str = arrayOfString2[0];
/*  49 */         str = trim(str);
/*  50 */         if (!str.startsWith("KB")) {
/*  51 */           i = str.compareTo(str1);
/*  52 */           if (i == 0) {
/*  53 */             i = 1;
/*     */           }
/*     */         } else {
/*  56 */           i = str.compareTo(str2);
/*     */         }
/*     */       
/*     */       }
/*     */     
/*     */     }
/*  62 */     else if (arrayOfString2.length == 2) {
/*  63 */       String str1 = arrayOfString1[0];
/*  64 */       String str2 = arrayOfString2[0];
/*  65 */       if (!str1.startsWith("KB")) {
/*  66 */         str1 = trim(str1);
/*  67 */         str2 = trim(str2);
/*     */         
/*  69 */         i = str2.compareTo(str1);
/*  70 */         if (i == 0) {
/*  71 */           i = -1;
/*     */         }
/*     */       } else {
/*  74 */         str1 = trim(str1);
/*  75 */         String str = trim(arrayOfString2[1]);
/*     */         
/*  77 */         i = str.compareTo(str1);
/*     */       }
/*     */     
/*  80 */     } else if (paramString1.indexOf("KB") > -1 && paramString2.indexOf("KB") > -1) {
/*  81 */       i = paramString2.compareTo(paramString1);
/*  82 */     } else if (paramString1.indexOf("KB") < 0 && paramString2.indexOf("KB") < 0) {
/*  83 */       i = paramString2.compareTo(paramString1);
/*     */     } else {
/*  85 */       paramString1 = paramString1.replace("KB", "");
/*  86 */       paramString2 = paramString2.replace("KB", "");
/*  87 */       i = paramString2.compareTo(paramString1);
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  93 */     return i;
/*     */   }
/*     */   
/*     */   public String trim(String paramString) {
/*  97 */     if (paramString != null) {
/*  98 */       paramString = paramString.trim();
/*     */     }
/* 100 */     return paramString;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/KBVersionCompare.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */