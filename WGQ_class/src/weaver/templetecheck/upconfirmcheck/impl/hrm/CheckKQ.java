/*    */ package weaver.templetecheck.upconfirmcheck.impl.hrm;
/*    */ 
/*    */ import java.util.LinkedHashMap;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.system.CompatibleUtil;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ import weaver.templetecheck.upconfirmcheck.Check;
/*    */ import weaver.templetecheck.upconfirmcheck.CheckElement;
/*    */ 
/*    */ public class CheckKQ
/*    */   extends Check {
/* 12 */   private String note = "";
/* 13 */   private String name = "";
/*    */ 
/*    */   
/*    */   public float getOrder() {
/* 17 */     return 0.0F;
/*    */   }
/*    */ 
/*    */   
/*    */   public String getNote() {
/* 22 */     return this.note;
/*    */   }
/*    */ 
/*    */   
/*    */   public String getName() {
/* 27 */     return this.name;
/*    */   }
/*    */ 
/*    */   
/*    */   public LinkedHashMap<String, CheckElement> check(String paramString) {
/* 32 */     this.name = SystemEnv.getHtmlLabelNames("525020", paramString);
/* 33 */     this.note = SystemEnv.getHtmlLabelNames("525021", paramString);
/* 34 */     RecordSet recordSet = new RecordSet();
/* 35 */     if (Check.beforeVersion.compareTo("9") < 0) {
/*    */ 
/*    */       
/* 38 */       boolean bool = CompatibleUtil.is122Open();
/* 39 */       if (!bool) {
/* 40 */         CheckElement checkElement = new CheckElement();
/* 41 */         checkElement.setNote(SystemEnv.getHtmlLabelNames("525050", paramString));
/* 42 */         checkElement.setSolution(SystemEnv.getHtmlLabelNames("525050", paramString));
/* 43 */         this.errorMap.put(SystemEnv.getHtmlLabelNames("525049", paramString), checkElement);
/*    */       } 
/*    */     } 
/* 46 */     return this.errorMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/upconfirmcheck/impl/hrm/CheckKQ.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */