/*    */ package weaver.templetecheck.upconfirmcheck.impl.emobile;
/*    */ 
/*    */ import java.util.LinkedHashMap;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.system.CompatibleUtil;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ import weaver.templetecheck.upconfirmcheck.Check;
/*    */ import weaver.templetecheck.upconfirmcheck.CheckElement;
/*    */ 
/*    */ public class CheckMobileDeploy
/*    */   extends Check {
/* 12 */   private String note = "";
/* 13 */   private String name = "";
/*    */ 
/*    */   
/*    */   public float getOrder() {
/* 17 */     return 0.0F;
/*    */   }
/*    */ 
/*    */   
/*    */   public String getNote() {
/* 22 */     return this.note;
/*    */   }
/*    */ 
/*    */   
/*    */   public String getName() {
/* 27 */     return this.name;
/*    */   }
/*    */ 
/*    */   
/*    */   public LinkedHashMap<String, CheckElement> check(String paramString) {
/* 32 */     this.name = SystemEnv.getHtmlLabelNames("509877", paramString) + "EMobile";
/* 33 */     this.note = SystemEnv.getHtmlLabelNames("509877", paramString) + "EMobile";
/* 34 */     RecordSet recordSet = new RecordSet();
/* 35 */     if (Check.beforeVersion.compareTo("9") < 0) {
/*    */       
/* 37 */       boolean bool1 = CompatibleUtil.checkNonstandardStatus("117");
/*    */       
/* 39 */       boolean bool2 = CompatibleUtil.checkNonstandardStatus("037");
/*    */       
/* 41 */       boolean bool3 = CompatibleUtil.checkNonstandardStatus("025");
/* 42 */       if ((bool2 || bool3) && 
/* 43 */         !bool1) {
/* 44 */         CheckElement checkElement = new CheckElement();
/* 45 */         checkElement.setNote(SystemEnv.getHtmlLabelNames("525023", paramString));
/* 46 */         checkElement.setSolution(SystemEnv.getHtmlLabelNames("525023", paramString));
/* 47 */         this.errorMap.put(SystemEnv.getHtmlLabelNames("525022", paramString), checkElement);
/*    */       } 
/*    */ 
/*    */       
/* 51 */       if (bool2) {
/* 52 */         CheckElement checkElement = new CheckElement();
/* 53 */         checkElement.setNote(SystemEnv.getHtmlLabelNames("525025", paramString));
/* 54 */         checkElement.setSolution(SystemEnv.getHtmlLabelNames("525025", paramString));
/* 55 */         this.errorMap.put(SystemEnv.getHtmlLabelNames("525024", paramString), checkElement);
/*    */       } 
/*    */     } 
/*    */ 
/*    */     
/* 60 */     return this.errorMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/upconfirmcheck/impl/emobile/CheckMobileDeploy.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */