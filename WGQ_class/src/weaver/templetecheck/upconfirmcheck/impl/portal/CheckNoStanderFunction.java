/*     */ package weaver.templetecheck.upconfirmcheck.impl.portal;
/*     */ 
/*     */ import java.io.File;
/*     */ import java.util.LinkedHashMap;
/*     */ import java.util.regex.Matcher;
/*     */ import java.util.regex.Pattern;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.online.IPUtil;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.templetecheck.upconfirmcheck.Check;
/*     */ import weaver.templetecheck.upconfirmcheck.CheckElement;
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CheckNoStanderFunction
/*     */   extends Check
/*     */ {
/*  21 */   private String note = "";
/*  22 */   private String name = "";
/*  23 */   private static String srcfilePath = GCONST.getRootPath() + "upgrade" + File.separatorChar + "src";
/*  24 */   private static String targetfilePath = GCONST.getRootPath() + "upgrade" + File.separatorChar + "target";
/*     */ 
/*     */ 
/*     */   
/*     */   public float getOrder() {
/*  29 */     return 0.0F;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getNote() {
/*  34 */     return this.note;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getName() {
/*  39 */     return this.name;
/*     */   }
/*     */ 
/*     */   
/*     */   public LinkedHashMap<String, CheckElement> check(String paramString) {
/*  44 */     this.name = SystemEnv.getHtmlLabelNames("529840", paramString);
/*  45 */     this.note = SystemEnv.getHtmlLabelNames("529840", paramString);
/*  46 */     RecordSet recordSet = new RecordSet();
/*  47 */     StringBuffer stringBuffer = new StringBuffer();
/*  48 */     Pattern pattern = Pattern.compile("\\d+");
/*  49 */     if (Check.beforeVersion.compareTo("9") < 0) {
/*  50 */       File file = new File(targetfilePath);
/*  51 */       String str = "select num,name,status from hp_nonstandard_function_info where  num=? ";
/*     */       
/*  53 */       if (file.exists() && file.isDirectory()) {
/*  54 */         String[] arrayOfString = file.list();
/*  55 */         for (String str1 : arrayOfString) {
/*  56 */           (new BaseBean()).writeLog("检查:" + str1);
/*     */           try {
/*  58 */             if (str1.endsWith(".xml")) {
/*  59 */               Matcher matcher = pattern.matcher(str1);
/*  60 */               if (matcher.find()) {
/*  61 */                 String str2 = matcher.group().trim();
/*     */                 
/*  63 */                 if (!"".equals(str2)) {
/*  64 */                   recordSet.executeQuery(str, new Object[] { str2 });
/*  65 */                   if (recordSet.next()) {
/*  66 */                     String str3 = Util.null2String(recordSet.getString("name")).trim();
/*  67 */                     if (!"".equals(str3)) {
/*  68 */                       str2 = str3;
/*     */                     }
/*     */                   } 
/*  71 */                   if (stringBuffer.length() > 0) {
/*  72 */                     stringBuffer.append("," + str2);
/*     */                   } else {
/*  74 */                     stringBuffer.append("" + str2);
/*     */                   } 
/*     */                 } 
/*     */               } 
/*     */             } 
/*  79 */           } catch (Exception exception) {
/*  80 */             exception.printStackTrace();
/*     */           } 
/*     */         } 
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 178 */     if (stringBuffer.length() > 0) {
/* 179 */       CheckElement checkElement = new CheckElement();
/* 180 */       checkElement.setNote(this.note);
/* 181 */       if (Check.beforeVersion.compareTo("8") < 0) {
/* 182 */         checkElement.setSolution(SystemEnv.getHtmlLabelNames("529841", paramString) + stringBuffer + "");
/* 183 */         this.errorMap.put(this.name, checkElement);
/* 184 */         return this.errorMap;
/* 185 */       }  if (Check.beforeVersion.compareTo("9") < 0) {
/* 186 */         checkElement.setSolution(SystemEnv.getHtmlLabelNames("529843", paramString) + "" + stringBuffer + SystemEnv.getHtmlLabelNames("529844", paramString));
/* 187 */         this.errorMap.put(this.name, checkElement);
/* 188 */         return this.errorMap;
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 193 */     return this.errorMap;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private String getLocalIp() {
/* 199 */     return IPUtil.getLocalIp();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/upconfirmcheck/impl/portal/CheckNoStanderFunction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */