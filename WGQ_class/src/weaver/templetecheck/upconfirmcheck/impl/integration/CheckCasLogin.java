/*     */ package weaver.templetecheck.upconfirmcheck.impl.integration;
/*     */ import java.io.BufferedReader;
/*     */ import java.io.File;
/*     */ import java.io.FileInputStream;
/*     */ import java.io.IOException;
/*     */ import java.io.InputStreamReader;
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ import java.util.regex.Matcher;
/*     */ import java.util.regex.Pattern;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.OrderProperties;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.templetecheck.upconfirmcheck.CheckElement;
/*     */ 
/*     */ public class CheckCasLogin extends Check {
/*  18 */   private String note = "";
/*  19 */   private String name = "";
/*     */ 
/*     */   
/*     */   public float getOrder() {
/*  23 */     return 0.0F;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getNote() {
/*  28 */     return this.note;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getName() {
/*  33 */     return this.name;
/*     */   }
/*     */ 
/*     */   
/*     */   public LinkedHashMap<String, CheckElement> check(String paramString) {
/*  38 */     this.name = SystemEnv.getHtmlLabelNames("529845", paramString);
/*  39 */     this.note = SystemEnv.getHtmlLabelNames("529845", paramString);
/*     */     
/*  41 */     List<String> list = getNoCasList();
/*  42 */     String str = "";
/*  43 */     File file = new File(GCONST.getRootPath() + File.separatorChar + "WEB-INF" + File.separatorChar + "web.xml");
/*  44 */     if (file.exists()) {
/*  45 */       StringBuffer stringBuffer = readFile(file);
/*  46 */       String str1 = stringBuffer.toString();
/*  47 */       str1 = str1.replaceAll("<!--.*?-->", "");
/*     */ 
/*     */       
/*  50 */       Pattern pattern = Pattern.compile("(?i)(<filter>)([\\s\\S]*?)(</filter>)");
/*  51 */       Matcher matcher = pattern.matcher(str1);
/*  52 */       while (matcher.find()) {
/*  53 */         String str2 = matcher.group(2).toUpperCase();
/*  54 */         boolean bool = false;
/*  55 */         for (String str3 : list) {
/*  56 */           if (str2.indexOf(str3) >= 0) {
/*  57 */             bool = true;
/*     */             break;
/*     */           } 
/*     */         } 
/*  61 */         if (bool) {
/*     */           continue;
/*     */         }
/*  64 */         if (str2.indexOf("CAS") > -1 || str2.indexOf("OAUTH") > 0 || str2.indexOf("SSO") > 0 || str2.indexOf("SAML") > 0) {
/*  65 */           CheckElement checkElement = new CheckElement();
/*  66 */           checkElement.setNote(this.note);
/*  67 */           checkElement.setSolution(SystemEnv.getHtmlLabelNames("529846", paramString));
/*  68 */           this.errorMap.put(this.name, checkElement);
/*     */         } 
/*     */       } 
/*     */     } 
/*     */     
/*  73 */     return this.errorMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<String> getNoCasList() {
/*  83 */     ArrayList<String> arrayList = new ArrayList();
/*  84 */     OrderProperties orderProperties = new OrderProperties();
/*  85 */     orderProperties.load(GCONST.getRootPath() + File.separatorChar + "WEB-INF" + File.separatorChar + "sysupgrade" + File.separatorChar + "upconfirmcheck.properties");
/*  86 */     String str = Util.null2String(orderProperties.get("nocasconfig")).toUpperCase().trim();
/*  87 */     String[] arrayOfString = str.split(",");
/*  88 */     for (byte b = 0; b < arrayOfString.length; b++) {
/*  89 */       arrayList.add(arrayOfString[b]);
/*     */     }
/*  91 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public StringBuffer readFile(File paramFile) {
/* 102 */     String str = "GBK";
/*     */     
/* 104 */     StringBuffer stringBuffer = new StringBuffer();
/*     */     try {
/* 106 */       InputStreamReader inputStreamReader = new InputStreamReader(new FileInputStream(paramFile), str);
/* 107 */       BufferedReader bufferedReader = new BufferedReader(inputStreamReader);
/* 108 */       String str1 = null;
/* 109 */       while ((str1 = bufferedReader.readLine()) != null) {
/* 110 */         stringBuffer.append(str1).append("\r\n");
/*     */       }
/* 112 */     } catch (IOException iOException) {
/* 113 */       iOException.printStackTrace();
/*     */     } 
/* 115 */     return stringBuffer;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/upconfirmcheck/impl/integration/CheckCasLogin.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */