/*    */ package weaver.templetecheck.upconfirmcheck.impl.workflow;
/*    */ 
/*    */ import java.util.LinkedHashMap;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ import weaver.templetecheck.upconfirmcheck.Check;
/*    */ import weaver.templetecheck.upconfirmcheck.CheckElement;
/*    */ 
/*    */ public class CheckWorkflowJS
/*    */   extends Check {
/* 11 */   private String note = "";
/* 12 */   private String name = "";
/*    */   
/*    */   public float getOrder() {
/* 15 */     return 0.0F;
/*    */   }
/*    */ 
/*    */   
/*    */   public String getNote() {
/* 20 */     return this.note;
/*    */   }
/*    */ 
/*    */   
/*    */   public String getName() {
/* 25 */     return this.name;
/*    */   }
/*    */ 
/*    */   
/*    */   public LinkedHashMap<String, CheckElement> check(String paramString) {
/* 30 */     this.name = SystemEnv.getHtmlLabelNames("525042", paramString) + "JS";
/* 31 */     this.note = SystemEnv.getHtmlLabelNames("525042", paramString) + "JS";
/* 32 */     RecordSet recordSet = new RecordSet();
/* 33 */     if (Check.beforeVersion.compareTo("9") < 0) {
/* 34 */       CheckElement checkElement = new CheckElement();
/* 35 */       checkElement.setNote(SystemEnv.getHtmlLabelNames("525044", paramString));
/* 36 */       checkElement.setSolution(SystemEnv.getHtmlLabelNames("525044", paramString));
/* 37 */       this.errorMap.put(SystemEnv.getHtmlLabelNames("525042", paramString) + "JS", checkElement);
/*    */     } 
/* 39 */     return this.errorMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/upconfirmcheck/impl/workflow/CheckWorkflowJS.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */