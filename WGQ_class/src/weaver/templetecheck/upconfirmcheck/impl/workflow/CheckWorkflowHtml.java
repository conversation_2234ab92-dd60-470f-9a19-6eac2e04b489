/*    */ package weaver.templetecheck.upconfirmcheck.impl.workflow;
/*    */ 
/*    */ import java.util.LinkedHashMap;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ import weaver.templetecheck.upconfirmcheck.Check;
/*    */ import weaver.templetecheck.upconfirmcheck.CheckElement;
/*    */ 
/*    */ public class CheckWorkflowHtml
/*    */   extends Check {
/* 11 */   private String note = "";
/* 12 */   private String name = "";
/*    */ 
/*    */   
/*    */   public float getOrder() {
/* 16 */     return 0.0F;
/*    */   }
/*    */ 
/*    */   
/*    */   public String getNote() {
/* 21 */     return this.note;
/*    */   }
/*    */ 
/*    */   
/*    */   public String getName() {
/* 26 */     return this.name;
/*    */   }
/*    */ 
/*    */   
/*    */   public LinkedHashMap<String, CheckElement> check(String paramString) {
/* 31 */     this.name = SystemEnv.getHtmlLabelNames("525042", paramString);
/* 32 */     this.note = SystemEnv.getHtmlLabelNames("525042", paramString);
/* 33 */     RecordSet recordSet = new RecordSet();
/* 34 */     if (Check.beforeVersion.compareTo("9") < 0) {
/*    */       
/* 36 */       recordSet.executeQuery("select 1 from workflow_nodehtmllayout WHERE syspath is not null " + (recordSet.getDBType().equals("oracle") ? "" : " and syspath<>''"), new Object[0]);
/* 37 */       if (recordSet.next()) {
/*    */         
/* 39 */         CheckElement checkElement = new CheckElement();
/* 40 */         checkElement.setNote(SystemEnv.getHtmlLabelNames("525043", paramString));
/* 41 */         checkElement.setSolution(SystemEnv.getHtmlLabelNames("525043", paramString));
/* 42 */         this.errorMap.put(SystemEnv.getHtmlLabelNames("525042", paramString), checkElement);
/*    */       } 
/*    */     } 
/*    */     
/* 46 */     return this.errorMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/upconfirmcheck/impl/workflow/CheckWorkflowHtml.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */