/*    */ package weaver.templetecheck.upconfirmcheck.impl.sys;
/*    */ 
/*    */ import java.io.File;
/*    */ import java.util.LinkedHashMap;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.GCONST;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ import weaver.templetecheck.upconfirmcheck.Check;
/*    */ import weaver.templetecheck.upconfirmcheck.CheckElement;
/*    */ 
/*    */ public class CheckDiskSpace
/*    */   extends Check {
/* 14 */   private String note = "";
/* 15 */   private String name = "";
/*    */ 
/*    */   
/*    */   public float getOrder() {
/* 19 */     return 0.0F;
/*    */   }
/*    */ 
/*    */   
/*    */   public String getNote() {
/* 24 */     return this.note;
/*    */   }
/*    */ 
/*    */   
/*    */   public String getName() {
/* 29 */     return this.name;
/*    */   }
/*    */ 
/*    */   
/*    */   public LinkedHashMap<String, CheckElement> check(String paramString) {
/* 34 */     this.name = SystemEnv.getHtmlLabelNames("529815", paramString);
/* 35 */     this.note = SystemEnv.getHtmlLabelNames("529815", paramString);
/*    */     
/* 37 */     String str = SystemEnv.getHtmlLabelNames("529816", paramString);
/* 38 */     File file = new File(GCONST.getRootPath());
/* 39 */     long l1 = file.getFreeSpace();
/* 40 */     long l2 = l1 / 1048576L;
/* 41 */     if (l2 < 0L) {
/* 42 */       l2 = 0L;
/*    */     }
/* 44 */     if (l2 <= 2048L) {
/* 45 */       CheckElement checkElement = new CheckElement();
/* 46 */       checkElement.setNote(SystemEnv.getHtmlLabelNames("529817", paramString) + SystemEnv.getHtmlLabelNames("529818", paramString) + "2G" + SystemEnv.getHtmlLabelNames("529819", paramString));
/* 47 */       checkElement.setSolution(str);
/* 48 */       this.errorMap.put(SystemEnv.getHtmlLabelNames("529820", paramString), checkElement);
/* 49 */     } else if (l2 <= 5120L) {
/* 50 */       CheckElement checkElement = new CheckElement();
/* 51 */       checkElement.setNote(SystemEnv.getHtmlLabelNames("529821", paramString) + SystemEnv.getHtmlLabelNames("529818", paramString) + "5G" + SystemEnv.getHtmlLabelNames("529819", paramString));
/* 52 */       checkElement.setSolution(str);
/* 53 */       this.errorMap.put(SystemEnv.getHtmlLabelNames("529820", paramString), checkElement);
/* 54 */     } else if (l2 <= 10240L) {
/* 55 */       CheckElement checkElement = new CheckElement();
/* 56 */       checkElement.setNote(SystemEnv.getHtmlLabelNames("529822", paramString) + SystemEnv.getHtmlLabelNames("529818", paramString) + "10G" + SystemEnv.getHtmlLabelNames("529819", paramString));
/* 57 */       checkElement.setSolution(str);
/* 58 */       this.errorMap.put(SystemEnv.getHtmlLabelNames("529820", paramString), checkElement);
/*    */     } else {
/* 60 */       RecordSet recordSet = new RecordSet();
/* 61 */       recordSet.executeQuery("select  filesystem from systemset", new Object[0]);
/* 62 */       if (recordSet.next()) {
/* 63 */         String str1 = Util.null2String(recordSet.getString("filesystem"));
/* 64 */         if (str1.trim().equals("")) {
/* 65 */           str1 = GCONST.getSysFilePath();
/*    */         }
/* 67 */         File file1 = new File(str1);
/* 68 */         if (file1.exists()) {
/* 69 */           long l = file1.getFreeSpace() / 1048576L;
/* 70 */           if (l < 10240L) {
/* 71 */             CheckElement checkElement = new CheckElement();
/* 72 */             checkElement.setNote(SystemEnv.getHtmlLabelNames("529823", paramString) + str1);
/* 73 */             checkElement.setSolution(str);
/* 74 */             this.errorMap.put(SystemEnv.getHtmlLabelNames("529824", paramString), checkElement);
/*    */           } 
/*    */         } 
/*    */       } 
/*    */     } 
/*    */     
/* 80 */     return this.errorMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/upconfirmcheck/impl/sys/CheckDiskSpace.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */