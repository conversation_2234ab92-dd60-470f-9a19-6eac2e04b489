/*    */ package weaver.templetecheck.upconfirmcheck.impl.sys;
/*    */ 
/*    */ import java.io.File;
/*    */ import java.util.ArrayList;
/*    */ import java.util.LinkedHashMap;
/*    */ import java.util.List;
/*    */ import org.dom4j.Document;
/*    */ import org.dom4j.Element;
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.general.GCONST;
/*    */ import weaver.general.OrderProperties;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ import weaver.templetecheck.ReadXml;
/*    */ import weaver.templetecheck.upconfirmcheck.Check;
/*    */ import weaver.templetecheck.upconfirmcheck.CheckElement;
/*    */ 
/*    */ public class CheckWEBSERVICE
/*    */   extends Check {
/* 20 */   private String note = "";
/* 21 */   private String name = "";
/*    */ 
/*    */   
/*    */   public float getOrder() {
/* 25 */     return 0.0F;
/*    */   }
/*    */ 
/*    */   
/*    */   public String getNote() {
/* 30 */     return this.note;
/*    */   }
/*    */ 
/*    */   
/*    */   public String getName() {
/* 35 */     return this.name;
/*    */   }
/*    */ 
/*    */   
/*    */   public LinkedHashMap<String, CheckElement> check(String paramString) {
/* 40 */     this.name = SystemEnv.getHtmlLabelNames("529836", paramString);
/* 41 */     this.note = SystemEnv.getHtmlLabelNames("529836", paramString);
/*    */     
/* 43 */     String str1 = "";
/* 44 */     List<String> list = getServicesList();
/* 45 */     ReadXml readXml = new ReadXml();
/* 46 */     String str2 = GCONST.getRootPath() + "classbean" + File.separatorChar + "META-INF" + File.separatorChar + "xfire" + File.separatorChar + "services.xml";
/* 47 */     File file = new File(str2);
/* 48 */     if (file.exists()) {
/* 49 */       Document document = readXml.read(str2);
/* 50 */       Element element = document.getRootElement();
/*    */ 
/*    */       
/* 53 */       List list1 = element.elements();
/* 54 */       for (Element element1 : list1) {
/* 55 */         Element element2 = element1.element("name");
/* 56 */         String str = element2.getTextTrim();
/* 57 */         (new BaseBean()).writeLog("elementname::::" + str);
/* 58 */         if (list.contains(str)) {
/*    */           continue;
/*    */         }
/* 61 */         str1 = str1 + str + ",";
/*    */       } 
/*    */       
/* 64 */       if (str1.length() > 0) {
/* 65 */         str1 = str1.substring(0, str1.length() - 1);
/* 66 */         CheckElement checkElement = new CheckElement();
/* 67 */         checkElement.setNote(SystemEnv.getHtmlLabelNames("529836", paramString));
/* 68 */         checkElement.setSolution(SystemEnv.getHtmlLabelNames("529837", paramString) + "" + str1 + "" + SystemEnv.getHtmlLabelNames("529839", paramString));
/* 69 */         this.errorMap.put(SystemEnv.getHtmlLabelNames("529836", paramString), checkElement);
/* 70 */         return this.errorMap;
/*    */       } 
/*    */     } 
/*    */     
/* 74 */     return this.errorMap;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public List<String> getServicesList() {
/* 84 */     ArrayList<String> arrayList = new ArrayList();
/* 85 */     BaseBean baseBean = new BaseBean();
/* 86 */     OrderProperties orderProperties = new OrderProperties();
/* 87 */     orderProperties.load(GCONST.getRootPath() + File.separatorChar + "WEB-INF" + File.separatorChar + "sysupgrade" + File.separatorChar + "upconfirmcheck.properties");
/* 88 */     String str = Util.null2String(orderProperties.get("standerservices"));
/* 89 */     String[] arrayOfString = str.split(",");
/* 90 */     for (byte b = 0; b < arrayOfString.length; b++) {
/* 91 */       arrayList.add(arrayOfString[b]);
/*    */     }
/* 93 */     return arrayList;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/upconfirmcheck/impl/sys/CheckWEBSERVICE.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */