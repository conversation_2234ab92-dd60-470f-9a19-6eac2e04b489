/*     */ package weaver.templetecheck.upconfirmcheck.impl.sys;
/*     */ 
/*     */ import com.weaver.function.ConfigInfo;
/*     */ import java.io.BufferedReader;
/*     */ import java.io.File;
/*     */ import java.io.FileReader;
/*     */ import java.io.IOException;
/*     */ import java.util.LinkedHashMap;
/*     */ import java.util.List;
/*     */ import java.util.regex.Matcher;
/*     */ import java.util.regex.Pattern;
/*     */ import org.dom4j.Document;
/*     */ import org.dom4j.Element;
/*     */ import org.dom4j.io.SAXReader;
/*     */ import org.xml.sax.SAXException;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.templetecheck.upconfirmcheck.Check;
/*     */ import weaver.templetecheck.upconfirmcheck.CheckElement;
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CheckMemerySpace
/*     */   extends Check
/*     */ {
/*  26 */   private String note = "";
/*  27 */   private String name = "";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public float getOrder() {
/*  36 */     return 0.0F;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getNote() {
/*  46 */     return this.note;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getName() {
/*  56 */     return this.name;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public LinkedHashMap<String, CheckElement> check(String paramString) {
/*  67 */     this.name = SystemEnv.getHtmlLabelNames("529829", paramString);
/*  68 */     this.note = SystemEnv.getHtmlLabelNames("529829", paramString);
/*     */     
/*  70 */     ConfigInfo configInfo = new ConfigInfo();
/*  71 */     String str = ConfigInfo.getResinpath();
/*  72 */     if (configInfo.isResin()) {
/*  73 */       String str1 = str + File.separatorChar + "conf" + File.separatorChar + "resin.properties";
/*  74 */       File file = new File(str1);
/*  75 */       if (file.exists()) {
/*  76 */         BufferedReader bufferedReader = null;
/*     */         try {
/*  78 */           bufferedReader = new BufferedReader(new FileReader(file));
/*  79 */           String str2 = bufferedReader.readLine();
/*  80 */           while (str2 != null) {
/*  81 */             str2 = str2.trim();
/*  82 */             if (str2.startsWith("jvm_args")) {
/*  83 */               Pattern pattern = Pattern.compile("-Xmx\\d+");
/*  84 */               Matcher matcher = pattern.matcher(str2);
/*  85 */               if (matcher.find()) {
/*  86 */                 String str3 = matcher.group().substring(4);
/*     */                 try {
/*  88 */                   long l = Long.parseLong(str3.trim());
/*  89 */                   if (l < 5500L) {
/*  90 */                     CheckElement checkElement = new CheckElement();
/*  91 */                     checkElement.setNote(SystemEnv.getHtmlLabelNames("529830", paramString));
/*  92 */                     checkElement.setSolution(SystemEnv.getHtmlLabelNames("529831", paramString) + "5500m~8500m");
/*  93 */                     this.errorMap.put(this.name, checkElement);
/*     */                   } 
/*  95 */                 } catch (Exception exception) {
/*  96 */                   exception.printStackTrace();
/*     */                 } 
/*     */               } 
/*     */               break;
/*     */             } 
/* 101 */             str2 = bufferedReader.readLine();
/*     */           } 
/* 103 */         } catch (Exception exception) {
/* 104 */           exception.printStackTrace();
/*     */         } finally {
/*     */           try {
/* 107 */             if (bufferedReader != null) {
/* 108 */               bufferedReader.close();
/*     */             }
/* 110 */           } catch (IOException iOException) {}
/*     */         } 
/*     */       } else {
/*     */         
/* 114 */         String str2 = str + File.separatorChar + "conf" + File.separatorChar + "resin.conf";
/* 115 */         File file1 = new File(str2);
/* 116 */         if (file1.exists()) {
/* 117 */           Document document = null;
/*     */           try {
/* 119 */             SAXReader sAXReader = new SAXReader();
/*     */             try {
/* 121 */               sAXReader.setFeature("http://apache.org/xml/features/nonvalidating/load-external-dtd", false);
/* 122 */             } catch (SAXException sAXException) {
/* 123 */               sAXException.printStackTrace();
/*     */             } 
/* 125 */             document = sAXReader.read(file1);
/* 126 */             Element element = document.getRootElement().element("cluster");
/* 127 */             if (element != null) {
/* 128 */               Element element1 = element.element("server-default");
/* 129 */               if (element1 != null) {
/* 130 */                 List list = element1.elements("jvm-arg");
/* 131 */                 for (Element element2 : list) {
/* 132 */                   String str3 = element2.getTextTrim();
/* 133 */                   if (str3.startsWith("-Xmx")) {
/* 134 */                     Pattern pattern = Pattern.compile("\\d+");
/* 135 */                     Matcher matcher = pattern.matcher(str3);
/* 136 */                     if (matcher.find()) {
/* 137 */                       String str4 = Util.null2String(matcher.group()).trim();
/* 138 */                       long l = Long.parseLong(str4);
/* 139 */                       if (l < 5500L) {
/* 140 */                         CheckElement checkElement = new CheckElement();
/* 141 */                         checkElement.setNote(SystemEnv.getHtmlLabelNames("529830", paramString));
/* 142 */                         checkElement.setSolution(SystemEnv.getHtmlLabelNames("529831", paramString) + "5500m~8500m");
/* 143 */                         this.errorMap.put(this.name, checkElement);
/*     */                       } 
/*     */                     } 
/*     */                     break;
/*     */                   } 
/*     */                 } 
/*     */               } 
/*     */             } 
/* 151 */           } catch (Exception exception) {
/* 152 */             exception.printStackTrace();
/*     */           } 
/*     */         } 
/*     */       } 
/*     */     } 
/* 157 */     return this.errorMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/upconfirmcheck/impl/sys/CheckMemerySpace.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */