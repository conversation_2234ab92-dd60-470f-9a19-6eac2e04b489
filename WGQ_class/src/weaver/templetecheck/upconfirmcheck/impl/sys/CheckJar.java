/*    */ package weaver.templetecheck.upconfirmcheck.impl.sys;
/*    */ 
/*    */ import java.io.File;
/*    */ import java.util.LinkedHashMap;
/*    */ import weaver.general.GCONST;
/*    */ import weaver.general.OrderProperties;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ import weaver.templetecheck.upconfirmcheck.Check;
/*    */ import weaver.templetecheck.upconfirmcheck.CheckElement;
/*    */ 
/*    */ public class CheckJar
/*    */   extends Check {
/* 14 */   private String note = "";
/* 15 */   private String name = "";
/*    */ 
/*    */   
/*    */   public float getOrder() {
/* 19 */     return 0.0F;
/*    */   }
/*    */ 
/*    */   
/*    */   public String getNote() {
/* 24 */     return this.note;
/*    */   }
/*    */ 
/*    */   
/*    */   public String getName() {
/* 29 */     return this.name;
/*    */   }
/*    */ 
/*    */   
/*    */   public LinkedHashMap<String, CheckElement> check(String paramString) {
/* 34 */     this.name = SystemEnv.getHtmlLabelNames("525033", paramString);
/* 35 */     this.note = SystemEnv.getHtmlLabelNames("525034", paramString);
/*    */     
/* 37 */     OrderProperties orderProperties = new OrderProperties();
/* 38 */     String str = "";
/* 39 */     orderProperties.load(GCONST.getRootPath() + File.separatorChar + "WEB-INF" + File.separatorChar + "sysupgrade" + File.separatorChar + "upconfirmcheck.properties");
/* 40 */     String[] arrayOfString1 = Util.null2String(orderProperties.get("conflictsjars")).split(",");
/* 41 */     String[] arrayOfString2 = Util.null2String(orderProperties.get("conflictsjarsmsg")).split(",");
/* 42 */     File file = new File(GCONST.getRootPath() + File.separatorChar + "WEB-INF" + File.separatorChar + "lib" + File.separatorChar);
/* 43 */     String[] arrayOfString3 = file.list();
/*    */     try {
/* 45 */       for (String str1 : arrayOfString3) {
/* 46 */         for (byte b = 0; b < arrayOfString1.length; b++) {
/* 47 */           if (arrayOfString1[b].equals(str1)) {
/* 48 */             if (str.length() > 0) {
/* 49 */               str = str + "<br/>" + str1 + ":" + SystemEnv.getHtmlLabelNames(arrayOfString2[b], paramString); break;
/*    */             } 
/* 51 */             str = str1 + ":" + SystemEnv.getHtmlLabelNames(arrayOfString2[b], paramString);
/*    */             
/*    */             break;
/*    */           } 
/*    */         } 
/*    */       } 
/* 57 */     } catch (Exception exception) {
/* 58 */       exception.printStackTrace();
/*    */     } 
/* 60 */     if (str.length() > 0) {
/* 61 */       CheckElement checkElement = new CheckElement();
/* 62 */       checkElement.setNote(SystemEnv.getHtmlLabelNames("529827", paramString));
/* 63 */       checkElement.setSolution(str);
/* 64 */       this.errorMap.put(SystemEnv.getHtmlLabelNames("529827", paramString), checkElement);
/* 65 */       return this.errorMap;
/*    */     } 
/*    */     
/* 68 */     return this.errorMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/upconfirmcheck/impl/sys/CheckJar.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */