/*     */ package weaver.templetecheck.upconfirmcheck.impl.sys;
/*     */ 
/*     */ import java.util.LinkedHashMap;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ import weaver.templetecheck.upconfirmcheck.Check;
/*     */ import weaver.templetecheck.upconfirmcheck.CheckElement;
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CheckClusterDBConnConfig
/*     */   extends Check
/*     */ {
/*  16 */   private String note = "";
/*  17 */   private String name = "";
/*     */ 
/*     */   
/*     */   public float getOrder() {
/*  21 */     return 0.0F;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getNote() {
/*  26 */     return this.note;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getName() {
/*  31 */     return this.name;
/*     */   }
/*     */ 
/*     */   
/*     */   public LinkedHashMap<String, CheckElement> check(String paramString) {
/*  36 */     this.name = SystemEnv.getHtmlLabelNames("529807", paramString);
/*  37 */     this.note = SystemEnv.getHtmlLabelNames("529807", paramString);
/*     */     
/*  39 */     String str1 = SystemEnv.getHtmlLabelNames("529808", paramString);
/*  40 */     BaseBean baseBean = new BaseBean();
/*  41 */     String str2 = Util.null2String(baseBean.getPropValue("weaver", "MainControlIP"));
/*  42 */     if (!str2.equals("")) {
/*  43 */       String str = Util.null2String(baseBean.getPropValue("weaver", "ecology.maxconn"), "0");
/*  44 */       long l = Long.parseLong(str);
/*  45 */       if (l > 300L) {
/*  46 */         CheckElement checkElement = new CheckElement();
/*  47 */         checkElement.setNote(SystemEnv.getHtmlLabelNames("529809", paramString));
/*  48 */         checkElement.setSolution(str1);
/*  49 */         this.errorMap.put(SystemEnv.getHtmlLabelNames("529810", paramString), checkElement);
/*     */       } else {
/*  51 */         String str3 = Util.null2String(baseBean.getPropValue("weaver", "initial_hosts"));
/*  52 */         String[] arrayOfString = str3.split(",");
/*  53 */         RecordSet recordSet = new RecordSet();
/*  54 */         String str4 = recordSet.getDBType();
/*  55 */         if ("oracle".equalsIgnoreCase(str4)) {
/*  56 */           recordSet.executeQuery("select value from v$parameter where name ='processes'", new Object[0]);
/*  57 */           if (recordSet.next()) {
/*  58 */             String str5 = Util.null2String(recordSet.getString("value"));
/*  59 */             if (!str5.equals("")) {
/*  60 */               long l1 = Long.parseLong(str5);
/*  61 */               if (l1 < l + 200L) {
/*  62 */                 CheckElement checkElement = new CheckElement();
/*  63 */                 checkElement.setNote(SystemEnv.getHtmlLabelNames("529809", paramString));
/*  64 */                 checkElement.setSolution(SystemEnv.getHtmlLabelNames("529811", paramString));
/*  65 */                 this.errorMap.put(SystemEnv.getHtmlLabelNames("529812", paramString), checkElement);
/*     */               
/*     */               }
/*     */ 
/*     */             
/*     */             }
/*     */ 
/*     */           
/*     */           }
/*     */         
/*     */         }
/*  76 */         else if ("sqlserver".equalsIgnoreCase(str4)) {
/*  77 */           recordSet.executeQuery("SELECT @@MAX_CONNECTIONS as conns", new Object[0]);
/*  78 */           if (recordSet.next()) {
/*  79 */             int i = Util.getIntValue(recordSet.getString("conns"), 0);
/*  80 */             if (i > 0 && 
/*  81 */               i < l + 200L) {
/*  82 */               CheckElement checkElement = new CheckElement();
/*  83 */               checkElement.setNote(SystemEnv.getHtmlLabelNames("529809", paramString));
/*  84 */               checkElement.setSolution(SystemEnv.getHtmlLabelNames("529813", paramString));
/*  85 */               this.errorMap.put(SystemEnv.getHtmlLabelNames("529812", paramString), checkElement);
/*     */ 
/*     */             
/*     */             }
/*     */ 
/*     */ 
/*     */           
/*     */           }
/*     */ 
/*     */         
/*     */         }
/*  96 */         else if ("mysql".equalsIgnoreCase(str4)) {
/*  97 */           recordSet.executeQuery("SHOW VARIABLES WHERE variable_name IN ('max_connections');", new Object[0]);
/*  98 */           if (recordSet.next()) {
/*  99 */             int i = Util.getIntValue(recordSet.getString("value"));
/* 100 */             if (i > 0 && 
/* 101 */               i < l + 200L) {
/* 102 */               CheckElement checkElement = new CheckElement();
/* 103 */               checkElement.setNote(SystemEnv.getHtmlLabelNames("529809", paramString));
/* 104 */               checkElement.setSolution(SystemEnv.getHtmlLabelNames("529813", paramString));
/* 105 */               this.errorMap.put(SystemEnv.getHtmlLabelNames("529812", paramString), checkElement);
/*     */ 
/*     */             
/*     */             }
/*     */ 
/*     */ 
/*     */           
/*     */           }
/*     */ 
/*     */         
/*     */         }
/* 116 */         else if ("postgresql".equalsIgnoreCase(str4)) {
/* 117 */           recordSet.executeQuery("show max_connections;", new Object[0]);
/* 118 */           if (recordSet.next()) {
/* 119 */             int i = Util.getIntValue(recordSet.getString("max_connections"));
/* 120 */             if (i > 0 && 
/* 121 */               i < l + 200L) {
/* 122 */               CheckElement checkElement = new CheckElement();
/* 123 */               checkElement.setNote(SystemEnv.getHtmlLabelNames("529809", paramString));
/* 124 */               checkElement.setSolution(SystemEnv.getHtmlLabelNames("529813", paramString));
/* 125 */               this.errorMap.put(SystemEnv.getHtmlLabelNames("529812", paramString), checkElement);
/*     */             } 
/*     */           } 
/*     */         } 
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 139 */     return this.errorMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/upconfirmcheck/impl/sys/CheckClusterDBConnConfig.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */