/*    */ package weaver.templetecheck.upconfirmcheck.impl.sys;
/*    */ 
/*    */ import java.io.File;
/*    */ import java.util.LinkedHashMap;
/*    */ import org.dom4j.Document;
/*    */ import weaver.general.GCONST;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ import weaver.templetecheck.ReadXml;
/*    */ import weaver.templetecheck.upconfirmcheck.Check;
/*    */ import weaver.templetecheck.upconfirmcheck.CheckElement;
/*    */ 
/*    */ public class CheckXMLConfig
/*    */   extends Check {
/* 14 */   private String note = "";
/* 15 */   private String name = "";
/*    */ 
/*    */   
/*    */   public float getOrder() {
/* 19 */     return 0.0F;
/*    */   }
/*    */ 
/*    */   
/*    */   public String getNote() {
/* 24 */     return this.note;
/*    */   }
/*    */ 
/*    */   
/*    */   public String getName() {
/* 29 */     return this.name;
/*    */   }
/*    */ 
/*    */   
/*    */   public LinkedHashMap<String, CheckElement> check(String paramString) {
/* 34 */     this.name = SystemEnv.getHtmlLabelNames("525035", paramString);
/* 35 */     this.note = SystemEnv.getHtmlLabelNames("525035", paramString);
/*    */     
/* 37 */     ReadXml readXml = new ReadXml();
/* 38 */     Document document = readXml.read(GCONST.getRootPath() + "WEB-INF" + File.separatorChar + "web.xml");
/* 39 */     String str1 = document.asXML();
/* 40 */     String str2 = SystemEnv.getHtmlLabelNames("525036", paramString);
/* 41 */     if (str1.indexOf("SecurityFilter") < 0) {
/* 42 */       CheckElement checkElement = new CheckElement();
/* 43 */       checkElement.setNote(SystemEnv.getHtmlLabelNames("525037", paramString));
/* 44 */       checkElement.setSolution(str2);
/* 45 */       this.errorMap.put(SystemEnv.getHtmlLabelNames("525037", paramString) + "(SecurityFilter)", checkElement);
/*    */     } 
/* 47 */     if (str1.indexOf("MultiLangFilter") < 0) {
/* 48 */       CheckElement checkElement = new CheckElement();
/* 49 */       checkElement.setNote(SystemEnv.getHtmlLabelNames("525038", paramString));
/* 50 */       checkElement.setSolution(str2);
/* 51 */       this.errorMap.put(SystemEnv.getHtmlLabelNames("525038", paramString) + "(MultiLangFilter)", checkElement);
/*    */     } 
/* 53 */     if (str1.indexOf("restservlet") < 0) {
/* 54 */       CheckElement checkElement = new CheckElement();
/* 55 */       checkElement.setNote(SystemEnv.getHtmlLabelNames("525039", paramString));
/* 56 */       checkElement.setSolution(str2);
/* 57 */       this.errorMap.put(SystemEnv.getHtmlLabelNames("525039", paramString) + "(restservlet)", checkElement);
/*    */     } 
/*    */     
/* 60 */     if (str1.indexOf("FileNamingCheckFilter") < 0) {
/* 61 */       CheckElement checkElement = new CheckElement();
/* 62 */       checkElement.setNote(SystemEnv.getHtmlLabelNames("525040", paramString));
/* 63 */       checkElement.setSolution(str2);
/* 64 */       this.errorMap.put(SystemEnv.getHtmlLabelNames("525040", paramString) + "(FileNamingCheckFilter)", checkElement);
/*    */     } 
/*    */     
/* 67 */     return this.errorMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/upconfirmcheck/impl/sys/CheckXMLConfig.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */