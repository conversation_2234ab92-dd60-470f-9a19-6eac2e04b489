/*     */ package weaver.templetecheck.upconfirmcheck.impl.sys;
/*     */ 
/*     */ import java.util.LinkedHashMap;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.templetecheck.upconfirmcheck.Check;
/*     */ import weaver.templetecheck.upconfirmcheck.CheckElement;
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CheckSeQuenceIndex
/*     */   extends Check
/*     */ {
/*  15 */   private String note = "";
/*  16 */   private String name = "";
/*     */ 
/*     */   
/*     */   public float getOrder() {
/*  20 */     return 0.0F;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getNote() {
/*  25 */     return this.note;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getName() {
/*  30 */     return this.name;
/*     */   }
/*     */ 
/*     */   
/*     */   public LinkedHashMap<String, CheckElement> check(String paramString) {
/*  35 */     this.name = "";
/*  36 */     this.note = "";
/*     */     
/*     */     try {
/*  39 */       RecordSet recordSet = new RecordSet();
/*  40 */       long l1 = 0L;
/*  41 */       long l2 = 0L;
/*  42 */       long l3 = 0L;
/*  43 */       long l4 = 0L;
/*  44 */       long l5 = 0L;
/*     */       
/*  46 */       recordSet.executeQuery("select  MAX(imagefileid) from imagefile  ", new Object[0]);
/*  47 */       if (recordSet.next()) {
/*  48 */         l1 = Long.parseLong(Util.null2String(recordSet.getString("imagefileid")));
/*     */       }
/*  50 */       recordSet.executeQuery("select  MAX(id) from docimagefile", new Object[0]);
/*  51 */       if (recordSet.next()) {
/*  52 */         l2 = Long.parseLong(Util.null2String(recordSet.getString("id")));
/*     */       }
/*     */       
/*  55 */       recordSet.executeQuery("select  MAX(versionid) from docimagefile", new Object[0]);
/*  56 */       if (recordSet.next()) {
/*  57 */         l3 = Long.parseLong(Util.null2String(recordSet.getString("versionid")));
/*     */       }
/*     */       
/*  60 */       recordSet.executeQuery("select  MAX(id) from docdetail", new Object[0]);
/*  61 */       if (recordSet.next()) {
/*  62 */         l4 = Long.parseLong(Util.null2String(recordSet.getString("id")));
/*     */       }
/*     */       
/*  65 */       recordSet.executeQuery("select  MAX(doceditionid) from docdetail", new Object[0]);
/*  66 */       if (recordSet.next()) {
/*  67 */         l5 = Long.parseLong(Util.null2String(recordSet.getString("doceditionid")));
/*     */       }
/*     */ 
/*     */       
/*  71 */       recordSet.executeQuery("select  * form SequenceIndex", new Object[0]);
/*  72 */       while (recordSet.next()) {
/*  73 */         String str1 = Util.null2String(recordSet.getString("indexdesc"));
/*  74 */         String str2 = Util.null2String(recordSet.getString("currentid"));
/*  75 */         long l = 0L;
/*  76 */         if (!str2.equals("")) {
/*  77 */           l = Long.parseLong(str2);
/*     */         }
/*  79 */         if ("imagefileid".equals(str1)) {
/*  80 */           if (l <= l1) {
/*  81 */             (new BaseBean()).writeLog("imagefileid" + l + "小于当前表中的最大id：" + l1);
/*  82 */             (new BaseBean()).writeLog("执行sql update SequenceIndex set currentid=? where indexdesc='imagefileid'" + (l1 + 1L));
/*  83 */             recordSet.executeUpdate("update SequenceIndex set currentid=? where indexdesc='imagefileid'", new Object[] { Long.valueOf(l1 + 1L) });
/*     */           }  continue;
/*  85 */         }  if ("docimageid".equals(str1)) {
/*  86 */           if (l <= l2) {
/*  87 */             (new BaseBean()).writeLog("docimageid:" + l + "小于当前表中的最大id：" + l2);
/*  88 */             (new BaseBean()).writeLog("执行sql update SequenceIndex set currentid=? where indexdesc='docimageid'" + (l2 + 1L));
/*  89 */             recordSet.executeUpdate("update SequenceIndex set currentid=? where indexdesc='docimageid'", new Object[] { Long.valueOf(l2 + 1L) });
/*     */           }  continue;
/*  91 */         }  if ("versionid".equals(str1)) {
/*  92 */           if (l <= l3) {
/*  93 */             (new BaseBean()).writeLog("versionid:" + l + "小于当前表中的最大id：" + l3);
/*  94 */             (new BaseBean()).writeLog("执行sql update SequenceIndex set currentid=? where indexdesc='versionid'" + (l3 + 1L));
/*  95 */             recordSet.executeUpdate("update SequenceIndex set currentid=? where indexdesc='versionid'", new Object[] { Long.valueOf(l3 + 1L) });
/*     */           }  continue;
/*  97 */         }  if ("docid".equals(str1)) {
/*  98 */           if (l <= l4) {
/*  99 */             (new BaseBean()).writeLog("docid:" + l + "小于当前表中的最大id：" + l4);
/* 100 */             (new BaseBean()).writeLog("执行sql update SequenceIndex set currentid=? where indexdesc='docid'" + (l4 + 1L));
/* 101 */             recordSet.executeUpdate("update SequenceIndex set currentid=? where indexdesc='docid'", new Object[] { Long.valueOf(l4 + 1L) });
/*     */           }  continue;
/* 103 */         }  if ("doceditionid".equals(str1))
/*     */         {
/* 105 */           if (l <= l5) {
/* 106 */             (new BaseBean()).writeLog("doceditionid:" + l + "小于当前表中的最大id：" + l5);
/* 107 */             (new BaseBean()).writeLog("执行sql update SequenceIndex set currentid=? where indexdesc='doceditionid'" + (l5 + 1L));
/* 108 */             recordSet.executeUpdate("update SequenceIndex set currentid=? where indexdesc='doceditionid'", new Object[] { Long.valueOf(l5 + 1L) });
/*     */           } 
/*     */         }
/*     */       } 
/* 112 */     } catch (Exception exception) {
/* 113 */       exception.printStackTrace();
/*     */     } 
/*     */     
/* 116 */     return this.errorMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/upconfirmcheck/impl/sys/CheckSeQuenceIndex.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */