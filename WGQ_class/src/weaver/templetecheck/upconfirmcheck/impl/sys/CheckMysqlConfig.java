/*    */ package weaver.templetecheck.upconfirmcheck.impl.sys;
/*    */ 
/*    */ import java.util.LinkedHashMap;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ import weaver.templetecheck.upconfirmcheck.Check;
/*    */ import weaver.templetecheck.upconfirmcheck.CheckElement;
/*    */ 
/*    */ 
/*    */ 
/*    */ public class CheckMysqlConfig
/*    */   extends Check
/*    */ {
/* 15 */   private String note = "";
/* 16 */   private String name = "";
/*    */ 
/*    */   
/*    */   public float getOrder() {
/* 20 */     return 0.0F;
/*    */   }
/*    */ 
/*    */   
/*    */   public String getNote() {
/* 25 */     return this.note;
/*    */   }
/*    */ 
/*    */   
/*    */   public String getName() {
/* 30 */     return this.name;
/*    */   }
/*    */ 
/*    */   
/*    */   public LinkedHashMap<String, CheckElement> check(String paramString) {
/* 35 */     this.name = SystemEnv.getHtmlLabelNames("529832", paramString);
/* 36 */     this.note = SystemEnv.getHtmlLabelNames("529832", paramString);
/*    */     
/* 38 */     RecordSet recordSet = new RecordSet();
/* 39 */     String str = recordSet.getDBType();
/* 40 */     if (str.equalsIgnoreCase("mysql")) {
/* 41 */       recordSet.executeQuery("SHOW VARIABLES WHERE variable_name IN (  'log_bin_trust_function_creators',  'transaction_isolation',  'lower_case_table_names',  'sql_mode',  'innodb_large_prefix',  'group_concat_max_len');", new Object[0]);
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */       
/* 49 */       String str1 = "";
/* 50 */       while (recordSet.next()) {
/* 51 */         String str2 = Util.null2String(recordSet.getString("variable_name")).trim();
/* 52 */         String str3 = Util.null2String(recordSet.getString("value")).trim();
/* 53 */         if (str2.equalsIgnoreCase("log_bin_trust_function_creators")) {
/* 54 */           if (!str3.equalsIgnoreCase("ON") && !str3.equalsIgnoreCase("1"))
/* 55 */             str1 = str1 + SystemEnv.getHtmlLabelNames("561", paramString) + ":log_bin_trust_function_creators," + SystemEnv.getHtmlLabelNames("529834", paramString) + "ON   \n";  continue;
/*    */         } 
/* 57 */         if (str2.equalsIgnoreCase("transaction_isolation")) {
/* 58 */           if (!str3.equalsIgnoreCase("READ-COMMITTED"))
/* 59 */             str1 = str1 + SystemEnv.getHtmlLabelNames("561", paramString) + ":transaction_isolation," + SystemEnv.getHtmlLabelNames("529834", paramString) + "COMMITTED \n";  continue;
/*    */         } 
/* 61 */         if (str2.equalsIgnoreCase("lower_case_table_names")) {
/* 62 */           if (!str3.equalsIgnoreCase("1"))
/* 63 */             str1 = str1 + SystemEnv.getHtmlLabelNames("561", paramString) + ":lower_case_table_names," + SystemEnv.getHtmlLabelNames("529834", paramString) + "1  \n";  continue;
/*    */         } 
/* 65 */         if (str2.equalsIgnoreCase("sql_mode")) {
/* 66 */           if (!str3.equalsIgnoreCase("STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION"))
/* 67 */             str1 = str1 + SystemEnv.getHtmlLabelNames("561", paramString) + ":sql_mode," + SystemEnv.getHtmlLabelNames("529834", paramString) + "STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION \n";  continue;
/*    */         } 
/* 69 */         if (str2.equalsIgnoreCase("innodb_large_prefix")) {
/* 70 */           if (!str3.equalsIgnoreCase("ON"))
/* 71 */             str1 = str1 + SystemEnv.getHtmlLabelNames("561", paramString) + ":innodb_large_prefix," + SystemEnv.getHtmlLabelNames("529834", paramString) + "ON \n";  continue;
/*    */         } 
/* 73 */         if (str2.equalsIgnoreCase("group_concat_max_len") && 
/* 74 */           !str3.equalsIgnoreCase("102400")) {
/* 75 */           str1 = str1 + SystemEnv.getHtmlLabelNames("561", paramString) + ":group_concat_max_len," + SystemEnv.getHtmlLabelNames("529834", paramString) + "102400  \n";
/*    */         }
/*    */       } 
/*    */       
/* 79 */       if (!str1.equals("")) {
/* 80 */         CheckElement checkElement = new CheckElement();
/* 81 */         checkElement.setNote(this.note);
/* 82 */         checkElement.setSolution(str1);
/* 83 */         this.errorMap.put(this.name, checkElement);
/* 84 */         return this.errorMap;
/*    */       } 
/*    */     } 
/* 87 */     return this.errorMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/upconfirmcheck/impl/sys/CheckMysqlConfig.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */