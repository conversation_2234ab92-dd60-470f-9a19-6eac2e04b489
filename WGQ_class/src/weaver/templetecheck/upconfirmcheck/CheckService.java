/*     */ package weaver.templetecheck.upconfirmcheck;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONArray;
/*     */ import com.alibaba.fastjson.JSONException;
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import java.lang.reflect.Method;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Collections;
/*     */ import java.util.Comparator;
/*     */ import java.util.HashMap;
/*     */ import java.util.Iterator;
/*     */ import java.util.LinkedHashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.Set;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.hrm.User;
/*     */ 
/*     */ public class CheckService extends BaseBean {
/*     */   public JSONObject check(String paramString, User paramUser) {
/*  21 */     JSONObject jSONObject = new JSONObject();
/*  22 */     HashMap<String, List<Check>> hashMap = CheckFactory.getInstanceList(paramString, paramUser.getLanguage());
/*  23 */     Set<String> set = hashMap.keySet();
/*  24 */     Iterator<String> iterator = set.iterator();
/*  25 */     writeLog(">>>>>>.   upgraderule size :" + hashMap.size());
/*  26 */     while (iterator.hasNext()) {
/*  27 */       String str = iterator.next();
/*  28 */       JSONArray jSONArray = new JSONArray();
/*  29 */       List<Check> list = hashMap.get(str);
/*  30 */       for (byte b = 0; b < list.size(); b++) {
/*  31 */         JSONObject jSONObject1 = new JSONObject();
/*     */         try {
/*  33 */           Check check = list.get(b);
/*  34 */           writeLog(">>>upgrade check:" + check.getClass());
/*  35 */           LinkedHashMap<String, CheckElement> linkedHashMap = check.check("" + paramUser.getLanguage());
/*     */           
/*  37 */           Iterator<Map.Entry> iterator1 = linkedHashMap.entrySet().iterator();
/*  38 */           while (iterator1.hasNext()) {
/*  39 */             Map.Entry entry = iterator1.next();
/*  40 */             CheckElement checkElement = (CheckElement)entry.getValue();
/*  41 */             jSONObject1.put("name", "" + (String)entry.getKey());
/*  42 */             jSONObject1.put("note", "" + checkElement.getNote());
/*  43 */             jSONObject1.put("solution", "" + checkElement.getSolution());
/*  44 */             String str1 = check.getClass().getName().replace("weaver.templetecheck.upconfirmcheck.impl.", "");
/*  45 */             String str2 = "";
/*  46 */             if (str1.indexOf(".") > 0) {
/*  47 */               str2 = str1.substring(0, str1.indexOf("."));
/*     */             }
/*  49 */             jSONObject1.put("module_tag", str2);
/*  50 */             jSONObject1.put("action", "" + str1);
/*     */             
/*  52 */             jSONArray.add(jSONObject1);
/*     */           } 
/*  54 */         } catch (Exception exception) {
/*  55 */           exception.printStackTrace();
/*     */         } 
/*     */       } 
/*     */ 
/*     */       
/*  60 */       if (jSONArray != null && jSONArray.size() > 0) {
/*  61 */         jSONObject.put(str, jSONArray);
/*     */       }
/*     */     } 
/*     */     
/*  65 */     return jSONObject;
/*     */   }
/*     */   
/*     */   public JSONObject doRepair(String paramString1, String paramString2) {
/*  69 */     JSONObject jSONObject = new JSONObject();
/*     */     try {
/*  71 */       if (paramString2 != null && paramString2.startsWith("this.")) {
/*  72 */         paramString2 = paramString2.replace("this.", "");
/*  73 */         if (paramString2.indexOf("(") > -1) {
/*  74 */           paramString2 = paramString2.substring(0, paramString2.indexOf("("));
/*     */         }
/*     */       } 
/*  77 */       Class<?> clazz = Class.forName("weaver.templetecheck.upconfirmcheck.impl." + paramString1);
/*  78 */       if (Check.class.isAssignableFrom(clazz)) {
/*  79 */         Object object = clazz.newInstance();
/*  80 */         Method method = clazz.getMethod(paramString2, new Class[0]);
/*  81 */         if (method != null) {
/*  82 */           jSONObject = (JSONObject)method.invoke(object, new Object[0]);
/*     */         }
/*     */       } 
/*  85 */     } catch (Exception exception) {
/*  86 */       exception.printStackTrace();
/*     */     } 
/*  88 */     return jSONObject;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static JSONArray sortJsonArray(JSONArray paramJSONArray, final String orderKey, final int ordertype) {
/* 102 */     JSONArray jSONArray = new JSONArray();
/* 103 */     ArrayList<JSONObject> arrayList = new ArrayList(); byte b;
/* 104 */     for (b = 0; b < paramJSONArray.size(); b++) {
/* 105 */       arrayList.add(paramJSONArray.getJSONObject(b));
/*     */     }
/* 107 */     Collections.sort(arrayList, new Comparator<JSONObject>() {
/* 108 */           private final String KEY_NAME = orderKey;
/*     */           
/*     */           public int compare(JSONObject param1JSONObject1, JSONObject param1JSONObject2) {
/* 111 */             String str1 = new String();
/* 112 */             String str2 = new String();
/*     */             try {
/* 114 */               String str3 = param1JSONObject1.getString(this.KEY_NAME);
/* 115 */               str1 = str3.replaceAll("-", "");
/* 116 */               String str4 = param1JSONObject2.getString(this.KEY_NAME);
/* 117 */               str2 = str4.replaceAll("-", "");
/* 118 */             } catch (JSONException jSONException) {}
/*     */             
/* 120 */             if (ordertype >= 0) {
/* 121 */               return str1.compareTo(str2);
/*     */             }
/* 123 */             return str2.compareTo(str1);
/*     */           }
/*     */         });
/*     */ 
/*     */     
/* 128 */     for (b = 0; b < paramJSONArray.size(); b++) {
/* 129 */       jSONArray.add(arrayList.get(b));
/*     */     }
/* 131 */     return jSONArray;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/upconfirmcheck/CheckService.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */