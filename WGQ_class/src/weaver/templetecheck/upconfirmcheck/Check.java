/*    */ package weaver.templetecheck.upconfirmcheck;
/*    */ 
/*    */ import java.util.LinkedHashMap;
/*    */ 
/*    */ public abstract class Check
/*    */ {
/*  7 */   public static String beforeVersion = UpCheckUtil.getBeforVersion();
/*    */   public float order;
/*    */   public String name;
/* 10 */   public LinkedHashMap<String, CheckElement> errorMap = new LinkedHashMap<>();
/*    */   
/*    */   public abstract float getOrder();
/*    */   
/*    */   public abstract String getNote();
/*    */   
/*    */   public abstract String getName();
/*    */   
/*    */   public abstract LinkedHashMap<String, CheckElement> check(String paramString);
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/upconfirmcheck/Check.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */