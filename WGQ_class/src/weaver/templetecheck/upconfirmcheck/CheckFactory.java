/*    */ package weaver.templetecheck.upconfirmcheck;
/*    */ 
/*    */ import java.io.File;
/*    */ import java.util.ArrayList;
/*    */ import java.util.Collections;
/*    */ import java.util.Comparator;
/*    */ import java.util.HashMap;
/*    */ import java.util.List;
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.general.GCONST;
/*    */ import weaver.general.OrderProperties;
/*    */ import weaver.general.Util;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ public class CheckFactory
/*    */   extends BaseBean
/*    */ {
/*    */   public static <T> T getInstance(String paramString, Class<T> paramClass) {
/* 19 */     T t = null;
/*    */     try {
/* 21 */       t = (T)Class.forName(paramString).getDeclaredConstructor(new Class[0]).newInstance(new Object[0]);
/* 22 */     } catch (Exception exception) {
/* 23 */       exception.printStackTrace();
/*    */     } 
/* 25 */     return t;
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public static HashMap<String, List<Check>> getInstanceList(String paramString, int paramInt) {
/* 31 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */     try {
/* 33 */       String str1 = "";
/* 34 */       String str2 = CheckFactory.class.getResource("").getPath() + File.separatorChar + "impl";
/*    */ 
/*    */       
/* 37 */       File file = new File(str2);
/* 38 */       File[] arrayOfFile = file.listFiles();
/* 39 */       if (arrayOfFile != null)
/*    */       {
/* 41 */         for (byte b = 0; b < arrayOfFile.length; b++) {
/* 42 */           ArrayList<Check> arrayList = new ArrayList();
/* 43 */           String str3 = arrayOfFile[b].getName();
/* 44 */           String str4 = GCONST.getRootPath() + "packagedescription" + File.separatorChar + "group.properties";
/*    */           
/* 46 */           OrderProperties orderProperties = new OrderProperties();
/* 47 */           orderProperties.load(str4);
/* 48 */           String str5 = orderProperties.get(str3);
/* 49 */           String str6 = str3;
/* 50 */           if (str5 != null && str5.matches("[0-9]*")) {
/* 51 */             str6 = SystemEnv.getHtmlLabelName(Integer.parseInt(str5), paramInt);
/*    */           }
/* 53 */           String[] arrayOfString = arrayOfFile[b].list();
/*    */           
/* 55 */           for (byte b1 = 0; b1 < arrayOfString.length; b1++) {
/* 56 */             String str = arrayOfString[b1];
/*    */             
/* 58 */             if (str.endsWith("class")) {
/*    */               
/*    */               try {
/*    */                 
/* 62 */                 str = str.replace("/", ".").replace("\\", ".");
/* 63 */                 str = str.replace(".class", "");
/*    */                 
/* 65 */                 Class<?> clazz = Class.forName("weaver.templetecheck.upconfirmcheck.impl." + str3 + "." + str);
/* 66 */                 if (Check.class.isAssignableFrom(clazz)) {
/*    */                   
/* 68 */                   Object object = clazz.newInstance();
/* 69 */                   arrayList.add((Check)object);
/*    */                 } 
/* 71 */               } catch (Exception exception) {
/* 72 */                 exception.printStackTrace();
/*    */               } 
/*    */             }
/*    */           } 
/*    */ 
/*    */           
/* 78 */           Collections.sort(arrayList, new Comparator<Check>()
/*    */               {
/*    */                 public int compare(Check param1Check1, Check param1Check2) {
/* 81 */                   float f = Util.getFloatValue("" + param1Check1.getOrder(), 0.0F) - Util.getFloatValue("" + param1Check2.getOrder(), 0.0F);
/* 82 */                   if (f > 0.0F)
/* 83 */                     return -1; 
/* 84 */                   if (f == 0.0F) {
/* 85 */                     return 0;
/*    */                   }
/* 87 */                   return 1;
/*    */                 }
/*    */               });
/*    */           
/* 91 */           hashMap.put(str6, arrayList);
/*    */         } 
/*    */       }
/* 94 */     } catch (Exception exception) {
/* 95 */       exception.printStackTrace();
/*    */     } 
/* 97 */     return (HashMap)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/upconfirmcheck/CheckFactory.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */