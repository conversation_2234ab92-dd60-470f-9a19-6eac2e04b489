/*    */ package weaver.templetecheck.upconfirmcheck;
/*    */ 
/*    */ public class CheckElement {
/*    */   private String note;
/*    */   private String solution;
/*    */   
/*    */   public String getNote() {
/*  8 */     return this.note;
/*    */   }
/*    */   
/*    */   public void setNote(String paramString) {
/* 12 */     this.note = paramString;
/*    */   }
/*    */   
/*    */   public String getSolution() {
/* 16 */     return this.solution;
/*    */   }
/*    */   
/*    */   public void setSolution(String paramString) {
/* 20 */     this.solution = paramString;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/upconfirmcheck/CheckElement.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */