/*    */ package weaver.templetecheck.upconfirmcheck;
/*    */ 
/*    */ import weaver.conn.RecordSet;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class UpCheckUtil
/*    */ {
/*    */   public static String getBeforVersion() {
/* 12 */     RecordSet recordSet = new RecordSet();
/* 13 */     String str = "";
/* 14 */     recordSet.executeQuery("select * from SysKBUpgrade order by id desc", new Object[0]);
/* 15 */     if (recordSet.next()) {
/* 16 */       str = recordSet.getString("kb_old");
/*    */     }
/* 18 */     if (str == null) {
/* 19 */       str = "";
/*    */     }
/* 21 */     return str;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/upconfirmcheck/UpCheckUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */