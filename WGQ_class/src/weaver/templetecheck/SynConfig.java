/*     */ package weaver.templetecheck;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import java.io.File;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.concurrent.ConcurrentHashMap;
/*     */ import org.dom4j.Document;
/*     */ import org.dom4j.DocumentHelper;
/*     */ import org.dom4j.Element;
/*     */ import org.jdom.Element;
/*     */ import org.jdom.output.XMLOutputter;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ 
/*     */ public class SynConfig
/*     */   extends BaseBean
/*     */ {
/*     */   private Logger logger;
/*     */   
/*     */   public JSONObject synConfig(ConfigVO paramConfigVO) {
/*  26 */     if (paramConfigVO != null) {
/*  27 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*  28 */       hashMap.put("pre-condition", parse(paramConfigVO.getBeforeElement()));
/*  29 */       hashMap.put("aft-condition", parse(paramConfigVO.getAfterElement()));
/*  30 */       hashMap.put("action", paramConfigVO.getAction());
/*     */       
/*  32 */       hashMap.put("filepath", "/WEB-INF/web.xml");
/*  33 */       this.logger.info("=====action：" + paramConfigVO.getAction());
/*  34 */       this.logger.info("=====pre-condition：" + element2Xml(paramConfigVO.getBeforeElement()));
/*  35 */       this.logger.info("=====aft-condition：" + element2Xml(paramConfigVO.getAfterElement()));
/*  36 */       List<Element> list = paramConfigVO.getContent();
/*     */       
/*  38 */       ArrayList<Element> arrayList = new ArrayList();
/*  39 */       if (list != null && 
/*  40 */         list.size() > 0) {
/*  41 */         for (Element element : list) {
/*  42 */           arrayList.add(parse(element));
/*     */         }
/*     */       }
/*     */       
/*  46 */       hashMap.put("elements", arrayList);
/*  47 */       this.logger.info("=====dom4j elements：" + arrayList);
/*     */ 
/*     */       
/*  50 */       return synConfig(paramConfigVO.getFrom(), (Map)hashMap);
/*     */     } 
/*  52 */     JSONObject jSONObject = new JSONObject();
/*  53 */     jSONObject.put("status", "failure");
/*  54 */     return jSONObject;
/*     */   }
/*     */   
/*     */   private String element2Xml(Element paramElement) {
/*  58 */     if (paramElement != null) {
/*  59 */       return (new XMLOutputter()).outputString(paramElement);
/*     */     }
/*     */     
/*  62 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private Element parse(Element paramElement) {
/*     */     try {
/*  71 */       if (paramElement != null) {
/*  72 */         String str = (new XMLOutputter()).outputString(paramElement);
/*  73 */         Document document = DocumentHelper.parseText(str);
/*  74 */         return document.getRootElement();
/*     */       }
/*     */     
/*  77 */     } catch (Exception exception) {
/*  78 */       exception.printStackTrace();
/*  79 */       this.logger.error("=============parse error occurred!!" + exception.getMessage());
/*     */     } 
/*     */     
/*  82 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONObject synXMLConfig(XMLConfigBean paramXMLConfigBean) {
/*  91 */     if (paramXMLConfigBean != null) {
/*  92 */       ConcurrentHashMap<Object, Object> concurrentHashMap = new ConcurrentHashMap<>();
/*  93 */       concurrentHashMap.put("filepath", paramXMLConfigBean.getFilepath());
/*  94 */       concurrentHashMap.put("pre-condition", paramXMLConfigBean.getBeforeElement());
/*  95 */       concurrentHashMap.put("aft-condition", paramXMLConfigBean.getAfterElement());
/*  96 */       concurrentHashMap.put("elements", paramXMLConfigBean.getContent());
/*  97 */       if (paramXMLConfigBean.getContent() != null) {
/*  98 */         writeLog("###synXMLConfig 配置长度：" + paramXMLConfigBean.getContent().size());
/*     */       }
/*     */       
/* 101 */       concurrentHashMap.put("action", paramXMLConfigBean.getAction());
/* 102 */       concurrentHashMap.put("note", paramXMLConfigBean.getNote());
/* 103 */       return synConfig(paramXMLConfigBean.getFrom(), (Map)concurrentHashMap);
/*     */     } 
/* 105 */     JSONObject jSONObject = new JSONObject();
/* 106 */     jSONObject.put("status", "failure");
/* 107 */     return jSONObject;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONObject synConfig(String paramString, List<ConfigVO> paramList) {
/* 115 */     this.logger.info("=====fromtype：" + paramString + ",start sync...");
/* 116 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 117 */     if (paramString == null || "".equals(paramString)) {
/* 118 */       paramString = "jc";
/*     */     }
/* 120 */     if (paramList != null) {
/* 121 */       for (byte b = 0; b < paramList.size(); b++) {
/* 122 */         ConfigVO configVO = paramList.get(b);
/* 123 */         HashMap<Object, Object> hashMap = new HashMap<>();
/* 124 */         hashMap.put("pre-condition", parse(configVO.getBeforeElement()));
/* 125 */         hashMap.put("aft-condition", parse(configVO.getAfterElement()));
/* 126 */         hashMap.put("action", configVO.getAction());
/*     */         
/* 128 */         List<Element> list = configVO.getContent();
/*     */         
/* 130 */         ArrayList<Element> arrayList1 = new ArrayList();
/* 131 */         if (list != null && 
/* 132 */           list.size() > 0) {
/* 133 */           for (Element element : list) {
/* 134 */             arrayList1.add(parse(element));
/*     */           }
/*     */         }
/*     */         
/* 138 */         this.logger.info("=====action：" + configVO.getAction());
/* 139 */         this.logger.info("=====pre-condition：" + element2Xml(configVO.getBeforeElement()));
/* 140 */         this.logger.info("=====aft-condition：" + element2Xml(configVO.getAfterElement()));
/* 141 */         this.logger.info("=====dom4j elements：" + arrayList1);
/* 142 */         hashMap.put("elements", arrayList1);
/*     */         
/* 144 */         hashMap.put("filepath", "/WEB-INF/web.xml");
/* 145 */         arrayList.add(hashMap);
/*     */       } 
/*     */       
/* 148 */       JSONObject jSONObject1 = new JSONObject();
/* 149 */       SynConfigOperation synConfigOperation = new SynConfigOperation();
/* 150 */       jSONObject1 = synConfigOperation.synBatch(paramString, (ArrayList)arrayList);
/* 151 */       this.logger.info("=====fromtype：" + paramString + ",end sync...");
/* 152 */       return jSONObject1;
/*     */     } 
/* 154 */     JSONObject jSONObject = new JSONObject();
/* 155 */     jSONObject.put("status", "failure");
/* 156 */     return jSONObject;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONObject synXMLConfig(String paramString, List<XMLConfigBean> paramList) {
/* 163 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 164 */     if (paramList != null && 
/* 165 */       paramList != null) {
/* 166 */       for (byte b = 0; b < paramList.size(); b++) {
/* 167 */         XMLConfigBean xMLConfigBean = paramList.get(b);
/* 168 */         HashMap<Object, Object> hashMap = new HashMap<>();
/* 169 */         hashMap.put("filepath", xMLConfigBean.getFilepath());
/* 170 */         hashMap.put("pre-condition", xMLConfigBean.getBeforeElement());
/* 171 */         hashMap.put("aft-condition", xMLConfigBean.getAfterElement());
/* 172 */         hashMap.put("elements", xMLConfigBean.getContent());
/* 173 */         if (xMLConfigBean.getContent() != null) {
/* 174 */           writeLog("###synXMLConfig 配置长度：" + xMLConfigBean.getContent().size());
/*     */         }
/*     */         
/* 177 */         hashMap.put("action", xMLConfigBean.getAction());
/* 178 */         hashMap.put("note", xMLConfigBean.getNote());
/* 179 */         arrayList.add(hashMap);
/*     */       } 
/*     */       
/* 182 */       JSONObject jSONObject1 = new JSONObject();
/* 183 */       SynConfigOperation synConfigOperation = new SynConfigOperation();
/* 184 */       jSONObject1 = synConfigOperation.synBatch(paramString, (ArrayList)arrayList);
/* 185 */       return jSONObject1;
/*     */     } 
/*     */     
/* 188 */     JSONObject jSONObject = new JSONObject();
/* 189 */     jSONObject.put("status", "failure");
/* 190 */     return jSONObject;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONObject synPropConfig(PropConfigBean paramPropConfigBean) {
/* 200 */     if (paramPropConfigBean != null) {
/* 201 */       ConcurrentHashMap<Object, Object> concurrentHashMap = new ConcurrentHashMap<>();
/* 202 */       concurrentHashMap.put("filepath", paramPropConfigBean.getFilepath());
/* 203 */       concurrentHashMap.put("content", paramPropConfigBean.getContent());
/* 204 */       concurrentHashMap.put("needCheckMap", paramPropConfigBean.getNeedCheckMap());
/* 205 */       concurrentHashMap.put("note", paramPropConfigBean.getNote());
/*     */       
/* 207 */       return synConfig(paramPropConfigBean.getFrom(), (Map)concurrentHashMap);
/*     */     } 
/* 209 */     JSONObject jSONObject = new JSONObject();
/* 210 */     jSONObject.put("status", "failure");
/* 211 */     return jSONObject;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public SynConfig() {
/* 387 */     this.logger = LoggerFactory.getLogger(getClass());
/*     */   }
/*     */   
/*     */   public void test() {
/*     */     String str1 = GCONST.getRootPath() + "WEB-INF" + File.separatorChar + "web.xml";
/*     */     XMLUtil xMLUtil = new XMLUtil(str1);
/*     */     String str2 = "<myroot>    <filter> \n        <filter-name>CloudStoreCompatible</filter-name>  \n        <filter-class>com.cloudstore.api.util.Util_CompatibleFilter</filter-class> \n    </filter></myroot>";
/*     */     Document document = xMLUtil.String2XML(str2);
/*     */     Element element1 = document.getRootElement().elements().get(0);
/*     */     str2 = "<myroot>        <filter> \n        <filter-name>InitFilter</filter-name>  \n        <filter-class>weaver.filter.InitFilter</filter-class> \n    </filter></myroot>";
/*     */     document = xMLUtil.String2XML(str2);
/*     */     Element element2 = document.getRootElement().elements().get(0);
/*     */     ArrayList<Element> arrayList1 = new ArrayList();
/*     */     Element element3 = document.getRootElement();
/*     */     str2 = "<myroot> <listener><listener-class>org.jasig.cas.client.session.SingleSignOutHttpSessionListener</listener-class></listener>\n</myroot>";
/*     */     document = xMLUtil.String2XML(str2);
/*     */     List<Element> list = document.getRootElement().elements();
/*     */     for (byte b1 = 0; b1 < list.size(); b1++)
/*     */       arrayList1.add(list.get(b1)); 
/*     */     str2 = "<myroot><servlet>\n        <servlet-name>FileDownloadForWps</servlet-name>\n        <servlet-class>weaver.file.other.DownloadForTokenServlet</servlet-class>\n    </servlet>\n    <servlet-mapping>\n        <servlet-name>FileDownloadForWps</servlet-name>\n        <url-pattern>/weaver/weaver.file.FileDownloadForWps</url-pattern>\n    </servlet-mapping></myroot>";
/*     */     ArrayList<Element> arrayList2 = new ArrayList();
/*     */     document = xMLUtil.String2XML(str2);
/*     */     list = document.getRootElement().elements();
/*     */     for (byte b2 = 0; b2 < list.size(); b2++)
/*     */       arrayList2.add(list.get(b2)); 
/*     */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     hashMap.put("test", "123");
/*     */     PropConfigBean propConfigBean = new PropConfigBean("", "/WEB-INF/prop/upgradesetting.properties", (HashMap)hashMap, "jc_cas");
/*     */     ArrayList<XMLConfigBean> arrayList = new ArrayList();
/*     */     XMLConfigBean xMLConfigBean1 = new XMLConfigBean("jc", "add", element1, element2, arrayList1, "jc_cas");
/*     */     XMLConfigBean xMLConfigBean2 = new XMLConfigBean("jc", "delete", null, null, arrayList2, "jc_cas");
/*     */     arrayList.add(xMLConfigBean1);
/*     */     arrayList.add(xMLConfigBean2);
/*     */     synXMLConfig("jc", arrayList);
/*     */     synPropConfig(propConfigBean);
/*     */   }
/*     */   
/*     */   public JSONObject synConfig(String paramString, Map<String, Object> paramMap) {
/*     */     writeLog("开始同步配置...fromtype:" + paramString);
/*     */     JSONObject jSONObject = new JSONObject();
/*     */     SynConfigOperation synConfigOperation = new SynConfigOperation();
/*     */     jSONObject = synConfigOperation.syn(paramString, paramMap);
/*     */     return jSONObject;
/*     */   }
/*     */   
/*     */   public JSONObject synConfig(String paramString) {
/*     */     writeLog("开始同步配置...fromtype:" + paramString);
/*     */     JSONObject jSONObject = new JSONObject();
/*     */     jSONObject.put("status", "success");
/*     */     return jSONObject;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/SynConfig.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */