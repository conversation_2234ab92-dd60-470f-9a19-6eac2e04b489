/*     */ package weaver.templetecheck;
/*     */ 
/*     */ import java.io.File;
/*     */ import java.io.FileOutputStream;
/*     */ import java.util.ArrayList;
/*     */ import java.util.LinkedHashMap;
/*     */ import java.util.List;
/*     */ import org.jdom.Content;
/*     */ import org.jdom.Document;
/*     */ import org.jdom.Element;
/*     */ import org.jdom.input.SAXBuilder;
/*     */ import org.jdom.output.Format;
/*     */ import org.jdom.output.XMLOutputter;
/*     */ import weaver.general.GCONST;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ConfigUtil
/*     */ {
/*  23 */   public LinkedHashMap<String, ArrayList<String>> pointArrayList = new LinkedHashMap<>();
/*  24 */   private String filename = GCONST.getRootPath() + "templetecheck" + File.separatorChar + "config.xml";
/*     */   public Element rootNodeElement;
/*  26 */   public String rulesid = "";
/*  27 */   FileUtil fileUtil = new FileUtil();
/*     */   
/*     */   public ConfigUtil() {
/*  30 */     SAXBuilder sAXBuilder = new SAXBuilder();
/*     */     
/*  32 */     File file = new File(this.fileUtil.getPath(this.filename));
/*  33 */     if (!file.canWrite()) {
/*  34 */       file.setWritable(true);
/*     */     }
/*     */     
/*     */     try {
/*  38 */       Document document = sAXBuilder.build(file);
/*  39 */       this.rootNodeElement = document.getRootElement();
/*  40 */     } catch (Exception exception) {
/*  41 */       exception.printStackTrace();
/*     */     } 
/*  43 */     init();
/*     */   }
/*     */   
/*     */   private void init() {
/*  47 */     this.rulesid = this.rootNodeElement.getAttributeValue("id");
/*     */     
/*  49 */     List list = this.rootNodeElement.getChildren("tab");
/*     */ 
/*     */     
/*  52 */     for (Element element : list) {
/*     */       
/*  54 */       String str1 = element.getAttributeValue("id");
/*  55 */       ArrayList<String> arrayList = new ArrayList();
/*  56 */       String str2 = element.getChildTextTrim("name");
/*  57 */       String str3 = element.getChildTextTrim("ishtml");
/*  58 */       arrayList.add(str2);
/*  59 */       arrayList.add(str3);
/*  60 */       this.pointArrayList.put(str1, arrayList);
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean save(String[] paramArrayOfString1, String[] paramArrayOfString2, String[] paramArrayOfString3) {
/*  66 */     Document document = new Document();
/*  67 */     Element element = new Element("tabs");
/*  68 */     element.setAttribute("id", "tab");
/*  69 */     element.setAttribute("version", "1.0.0");
/*  70 */     String str = "checkservice,checkaction,checkwebxml,";
/*     */     
/*  72 */     for (byte b = 0; b < paramArrayOfString1.length; b++) {
/*  73 */       Element element1 = new Element("tab");
/*  74 */       element1.setAttribute("id", "" + paramArrayOfString1[b]);
/*  75 */       str = str + paramArrayOfString1[b] + ",";
/*     */       
/*  77 */       Element element2 = new Element("name");
/*  78 */       element2.addContent(paramArrayOfString2[b]);
/*  79 */       element1.addContent((Content)element2);
/*  80 */       Element element3 = new Element("ishtml");
/*  81 */       element3.addContent(paramArrayOfString3[b]);
/*  82 */       element1.addContent((Content)element3);
/*  83 */       element.addContent((Content)element1);
/*     */     } 
/*     */     
/*  86 */     document.addContent((Content)element);
/*     */     
/*     */     try {
/*  89 */       String str1 = GCONST.XML_UTF8;
/*  90 */       Format format = Format.getCompactFormat();
/*  91 */       format.setEncoding(str1);
/*  92 */       format.setIndent("    ");
/*  93 */       XMLOutputter xMLOutputter = new XMLOutputter(format);
/*  94 */       xMLOutputter.output(document, new FileOutputStream(this.fileUtil.getPath(this.filename)));
/*     */     }
/*  96 */     catch (Exception exception) {
/*  97 */       exception.printStackTrace();
/*  98 */       return false;
/*     */     } 
/*     */     
/* 101 */     CheckUtil checkUtil = new CheckUtil();
/* 102 */     checkUtil.changeTab(str);
/* 103 */     RulePath rulePath = new RulePath();
/* 104 */     rulePath.changeTab(str);
/* 105 */     return true;
/*     */   }
/*     */   
/*     */   public LinkedHashMap<String, ArrayList<String>> getalltabs() {
/* 109 */     return this.pointArrayList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/templetecheck/ConfigUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */