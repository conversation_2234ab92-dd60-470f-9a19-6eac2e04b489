/*     */ package weaver.odoceditutil;
/*     */ import java.io.FileInputStream;
/*     */ import java.io.FileNotFoundException;
/*     */ import java.io.FileOutputStream;
/*     */ import java.io.IOException;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Date;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.regex.Matcher;
/*     */ import java.util.regex.Pattern;
/*     */ import org.apache.poi.xwpf.usermodel.XWPFDocument;
/*     */ import org.apache.xmlbeans.XmlObject;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ public class ConvertToDocx {
/*     */   public static Map<String, Object> toDocx(String paramString1, String paramString2, String paramString3) {
/*  21 */     return toDocx(paramString1, paramString2, paramString3, "", null);
/*     */   }
/*     */ 
/*     */   
/*     */   public static Map<String, Object> toDocx(String paramString1, String paramString2, String paramString3, String paramString4, Map<String, String> paramMap) {
/*  26 */     Date date = new Date();
/*     */     
/*  28 */     Map<String, Object> map = ResultBuildUtil.buildResult(1, "" + SystemEnv.getHtmlLabelName(382011, ThreadVarLanguage.getLang()) + "", "", -1);
/*     */     try {
/*  30 */       (new BaseBean()).writeLog("--------to---------weaver/odoceditutil/ConvertToDocx.java");
/*  31 */       FileInputStream fileInputStream1 = new FileInputStream(paramString2);
/*  32 */       XWPFDocument xWPFDocument1 = new XWPFDocument(fileInputStream1);
/*  33 */       paramString1 = paramString1.replace("revision_", "");
/*     */       
/*  35 */       FileInputStream fileInputStream2 = null;
/*  36 */       XWPFDocument xWPFDocument2 = null;
/*     */       
/*  38 */       if (paramString4 == null || paramString4.isEmpty()) {
/*     */ 
/*     */         
/*  41 */         createrComment(paramString1, xWPFDocument1);
/*     */       }
/*     */       else {
/*     */         
/*  45 */         fileInputStream2 = new FileInputStream(paramString4);
/*  46 */         xWPFDocument2 = new XWPFDocument(fileInputStream2);
/*     */         
/*  48 */         createrComment(paramString1, xWPFDocument2);
/*     */       } 
/*     */       
/*  51 */       (new BaseBean()).writeLog("--------docx---------htInputStream=" + fileInputStream2);
/*  52 */       (new BaseBean()).writeLog("--------docx---------inputStream=" + fileInputStream1);
/*  53 */       (new BaseBean()).writeLog("--------docx---------xwpfDocument=" + xWPFDocument1);
/*  54 */       (new BaseBean()).writeLog("--------docx---------htXwpfDocument=" + xWPFDocument2);
/*     */       
/*  56 */       String str = xWPFDocument1.getDocument().xmlText();
/*     */       
/*  58 */       List<ParagraphBean> list = getParagraphBeanList(str);
/*     */       
/*  60 */       StringBuffer stringBuffer = new StringBuffer("");
/*     */       
/*  62 */       ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  63 */       while (paramString1.length() > 0) {
/*     */         
/*  65 */         HashMap<Object, Object> hashMap = new HashMap<>();
/*  66 */         String str1 = "";
/*  67 */         int i = -1;
/*  68 */         String str2 = "";
/*  69 */         String str3 = "";
/*  70 */         if (paramString1.startsWith("<div")) {
/*     */           
/*  72 */           i = paramString1.indexOf("</div>") + 6;
/*  73 */           str1 = paramString1.substring(0, i);
/*  74 */           str3 = RegExUtil.match(str1, "div", "listadvancetype").trim();
/*  75 */           String str4 = RegExUtil.match(str1, "div", "data-margin").trim();
/*  76 */           if (str3.equals("CHCPT")) {
/*     */             
/*  78 */             str3 = ElementType.ODOC_XUHAO1.getType();
/*     */           }
/*  80 */           else if (str3.equals("CHCPTP")) {
/*     */             
/*  82 */             str3 = ElementType.ODOC_XUHAO2.getType();
/*     */           }
/*  84 */           else if (str3.equals("NUMBER")) {
/*     */             
/*  86 */             str3 = ElementType.ODOC_XUHAO3.getType();
/*     */           }
/*  88 */           else if (str3.equals("NUMBERP")) {
/*     */             
/*  90 */             str3 = ElementType.ODOC_XUHAO4.getType();
/*     */           } 
/*  92 */           hashMap.put("classname", str3);
/*  93 */           String str5 = "<p.*?>(.*?)</p>";
/*  94 */           Pattern pattern = Pattern.compile(str5);
/*  95 */           Matcher matcher = pattern.matcher(str1);
/*  96 */           if (matcher.find()) {
/*  97 */             str2 = matcher.group(1);
/*  98 */             hashMap.put("content", str2);
/*     */           }
/*     */         
/*     */         } else {
/*     */           
/* 103 */           i = paramString1.indexOf("</p>") + 4;
/* 104 */           str1 = paramString1.substring(0, i);
/*     */           
/* 106 */           String str4 = "<p.*?>(.*?)</p>";
/* 107 */           Pattern pattern = Pattern.compile(str4);
/* 108 */           Matcher matcher = pattern.matcher(str1);
/* 109 */           if (matcher.find()) {
/* 110 */             str2 = matcher.group(1);
/* 111 */             str3 = RegExUtil.match(str1, "p", "class");
/* 112 */             str3 = str3.trim();
/* 113 */             hashMap.put("content", str2);
/* 114 */             hashMap.put("classname", str3);
/*     */           } 
/*     */         } 
/* 117 */         arrayList.add(hashMap);
/* 118 */         paramString1 = paramString1.substring(i);
/*     */       } 
/*     */ 
/*     */       
/* 122 */       for (Map<Object, Object> map1 : arrayList) {
/*     */         
/* 124 */         String str1 = (String)map1.get("content");
/* 125 */         String str2 = (String)map1.get("classname");
/*     */         
/* 127 */         str2 = str2.trim();
/* 128 */         if (str2.equals(ElementType.ODOC_ZHUBIAOTI.getType())) {
/*     */           
/* 130 */           stringBuffer.append(packageRunXml(str1, list.get(0), "")); continue;
/*     */         } 
/* 132 */         if (str2.equals(ElementType.ODOC_FUBIAOTI.getType())) {
/*     */           
/* 134 */           stringBuffer.append(packageRunXml(str1, list.get(1), "")); continue;
/*     */         } 
/* 136 */         if (str2.equals(ElementType.ODOC_WENHAO.getType())) {
/*     */           
/* 138 */           stringBuffer.append(packageRunXml(str1, list.get(2), "")); continue;
/*     */         } 
/* 140 */         if (str2.equals(ElementType.ODOC_ZHUSONG.getType())) {
/*     */           
/* 142 */           stringBuffer.append(packageRunXml(str1, list.get(3), "")); continue;
/*     */         } 
/* 144 */         if (str2.equals(ElementType.ODOC_ZHENGWEN.getType())) {
/*     */           
/* 146 */           stringBuffer.append(packageRunXml(str1, list.get(4), "")); continue;
/*     */         } 
/* 148 */         if (str2.equals(ElementType.ODOC_XUHAO1.getType())) {
/*     */           
/* 150 */           stringBuffer.append(packageRunXml(str1, list.get(5), "")); continue;
/*     */         } 
/* 152 */         if (str2.equals(ElementType.ODOC_XUHAO2.getType())) {
/*     */           
/* 154 */           stringBuffer.append(packageRunXml(str1, list.get(6), "")); continue;
/*     */         } 
/* 156 */         if (str2.equals(ElementType.ODOC_XUHAO3.getType())) {
/*     */           
/* 158 */           stringBuffer.append(packageRunXml(str1, list.get(7), "")); continue;
/*     */         } 
/* 160 */         if (str2.equals(ElementType.ODOC_XUHAO4.getType())) {
/*     */           
/* 162 */           stringBuffer.append(packageRunXml(str1, list.get(8), "")); continue;
/*     */         } 
/* 164 */         if (str2.equals(ElementType.ODOC_XUHAO5.getType())) {
/*     */           
/* 166 */           stringBuffer.append(packageRunXml(str1, list.get(9), "")); continue;
/*     */         } 
/* 168 */         if (str2.equals(ElementType.ODOC_FUJIAN2.getType())) {
/*     */           
/* 170 */           stringBuffer.append(packageRunXml(str1, list.get(10), "<w:r w:rsidRPr=\"00717FF9\"><w:rPr><w:rFonts w:ascii=\"仿宋\" w:eastAsia=\"仿宋\" w:hAnsi=\"仿宋\" w:cs=\"宋体\" w:hint=\"eastAsia\" /><w:kern w:val=\"0\" /><w:sz w:val=\"32\" /><w:szCs w:val=\"32\" /></w:rPr><w:t>附件：</w:t></w:r>")); continue;
/*     */         } 
/* 172 */         if (str2.equals(ElementType.ODOC_FUJIAN3.getType())) {
/*     */           
/* 174 */           stringBuffer.append(packageRunXml(str1, list.get(11), "")); continue;
/*     */         } 
/* 176 */         if (str2.equals(ElementType.ODOC_FUJIAN1.getType())) {
/*     */           
/* 178 */           stringBuffer.append(packageRunXml(str1, list.get(12), "<w:r w:rsidRPr=\"00717FF9\"><w:rPr><w:rFonts w:ascii=\"仿宋\" w:eastAsia=\"仿宋\" w:hAnsi=\"仿宋\" w:cs=\"宋体\" w:hint=\"eastAsia\" /><w:kern w:val=\"0\" /><w:sz w:val=\"32\" /><w:szCs w:val=\"32\" /></w:rPr><w:t>附件：</w:t></w:r>")); continue;
/*     */         } 
/* 180 */         if (str2.equals(ElementType.ODOC_SHUMING1.getType())) {
/*     */           
/* 182 */           stringBuffer.append(packageRunXml(str1, list.get(13), "")); continue;
/*     */         } 
/* 184 */         if (str2.equals(ElementType.ODOC_QIANSHURIQI1.getType())) {
/*     */           
/* 186 */           stringBuffer.append(packageRunXml(str1, list.get(14), "")); continue;
/*     */         } 
/* 188 */         if (str2.equals(ElementType.ODOC_FUZHU.getType()))
/*     */         {
/* 190 */           stringBuffer.append(packageRunXml(str1, list.get(15), ""));
/*     */         }
/*     */       } 
/*     */       
/* 194 */       if (paramString4.isEmpty()) {
/*     */         
/* 196 */         String str1 = "<w:sectPr .*?>.*?</w:sectPr>";
/* 197 */         Pattern pattern = Pattern.compile(str1);
/* 198 */         Matcher matcher = pattern.matcher(str);
/* 199 */         if (matcher.find())
/*     */         {
/* 201 */           stringBuffer.append(matcher.group());
/*     */         }
/*     */         
/* 204 */         String str2 = "<w:body>" + stringBuffer.append("</w:body>");
/* 205 */         str1 = "<w:body>.*?</w:body>";
/* 206 */         pattern = Pattern.compile(str1);
/* 207 */         matcher = pattern.matcher(str);
/* 208 */         if (matcher.find())
/*     */         {
/* 210 */           str = str.replace(matcher.group(), str2);
/*     */         }
/*     */         
/* 213 */         xWPFDocument1.getDocument().set(XmlObject.Factory.parse(str));
/*     */         
/* 215 */         FileOutputStream fileOutputStream = new FileOutputStream(paramString3);
/* 216 */         xWPFDocument1.write(fileOutputStream);
/* 217 */         fileOutputStream.close();
/*     */       }
/*     */       else {
/*     */         
/* 221 */         String str1 = xWPFDocument2.getDocument().xmlText();
/* 222 */         str1 = replaceBookmark(str1, paramMap);
/*     */         
/* 224 */         String str2 = "<w:p .*?>.*?Content.*?</w:p>";
/* 225 */         Pattern pattern = Pattern.compile(str2);
/* 226 */         Matcher matcher = pattern.matcher(str1);
/* 227 */         if (matcher.find()) {
/*     */           
/* 229 */           String str3 = matcher.group();
/* 230 */           str3 = str3.substring(str3.lastIndexOf("<w:p "));
/*     */           
/* 232 */           str1 = str1.replace(str3, stringBuffer);
/*     */         } 
/* 234 */         xWPFDocument2.getDocument().set(XmlObject.Factory.parse(str1));
/*     */         
/* 236 */         FileOutputStream fileOutputStream = new FileOutputStream(paramString3);
/* 237 */         xWPFDocument2.write(fileOutputStream);
/* 238 */         fileOutputStream.close();
/*     */       } 
/*     */ 
/*     */       
/* 242 */       if (fileInputStream2 != null) {
/*     */         
/* 244 */         xWPFDocument2.close();
/* 245 */         fileInputStream2.close();
/*     */       } 
/* 247 */       xWPFDocument1.close();
/* 248 */       fileInputStream1.close();
/*     */       
/* 250 */       Date date1 = new Date();
/* 251 */       map = ResultBuildUtil.buildResult(0, "" + SystemEnv.getHtmlLabelName(25008, ThreadVarLanguage.getLang()) + "", "", (int)(date1.getTime() - date.getTime()));
/* 252 */     } catch (FileNotFoundException fileNotFoundException) {
/* 253 */       (new BaseBean()).writeLog("--------ConvertToDocx-11-----e=" + fileNotFoundException);
/* 254 */       map = ResultBuildUtil.buildResult(1, "" + SystemEnv.getHtmlLabelName(382011, ThreadVarLanguage.getLang()) + "", "", -1);
/* 255 */       (new BaseBean()).writeLog("--------ConvertToDocx-11-----result=" + map);
/* 256 */     } catch (IOException iOException) {
/* 257 */       map = ResultBuildUtil.buildResult(1, "" + SystemEnv.getHtmlLabelName(382011, ThreadVarLanguage.getLang()) + "", "", -1);
/* 258 */       (new BaseBean()).writeLog("--------ConvertToDocx---22---result=" + map);
/* 259 */     } catch (Exception exception) {
/* 260 */       exception.printStackTrace();
/* 261 */       map = ResultBuildUtil.buildResult(1, "" + SystemEnv.getHtmlLabelName(382011, ThreadVarLanguage.getLang()) + "", "", -1);
/* 262 */       (new BaseBean()).writeLog("--------ConvertToDocx---33---result=" + map);
/*     */     } 
/* 264 */     (new BaseBean()).writeLog("--------ConvertToDocx------result=" + map);
/* 265 */     return map;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private static String packageRunXml(String paramString1, ParagraphBean paramParagraphBean, String paramString2) {
/* 271 */     StringBuffer stringBuffer = new StringBuffer(paramParagraphBean.getXmlStr().replaceAll("</w:p>", ""));
/* 272 */     stringBuffer.append(paramParagraphBean.getpPrXmlStr()).append(paramString2);
/*     */     
/* 274 */     paramString1 = packagePiZhu(paramString1);
/*     */ 
/*     */     
/* 277 */     String str = "<ft.*?>(.*?)</ft>";
/* 278 */     Pattern pattern = Pattern.compile(str);
/* 279 */     Matcher matcher = pattern.matcher(paramString1);
/* 280 */     while (matcher.find()) {
/*     */       
/* 282 */       String str1 = matcher.group(1);
/* 283 */       Map<String, String> map = getRunMap(str1);
/* 284 */       String str2 = map.get("author");
/* 285 */       String str3 = map.get("date");
/* 286 */       String str4 = map.get("content");
/*     */       
/* 288 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*     */       
/* 290 */       String str5 = "";
/* 291 */       String str6 = "";
/*     */       
/* 293 */       if (hashMap.size() > 0) {
/*     */         
/* 295 */         str5 = (String)hashMap.get("startStr");
/* 296 */         str6 = (String)hashMap.get("endStr");
/* 297 */         str4 = (String)hashMap.get("content");
/*     */       } 
/* 299 */       if (((String)map.get("type")).equals("zhengwen")) {
/*     */         
/* 301 */         stringBuffer.append(str5).append(paramParagraphBean.getrXmlStr().replaceAll("" + SystemEnv.getHtmlLabelName(10003823, ThreadVarLanguage.getLang()) + "", str4)).append(str6); continue;
/*     */       } 
/* 303 */       if (((String)map.get("type")).equals("del")) {
/*     */         
/* 305 */         String str7 = paramParagraphBean.getDelXmlStr();
/* 306 */         str7 = str7.replaceAll("xdauthor", str2).replaceAll("1900-01-01T00:00:00Z", str3);
/* 307 */         stringBuffer.append(str5).append(str7.replaceAll("" + SystemEnv.getHtmlLabelName(10003824, ThreadVarLanguage.getLang()) + "", str4)).append(str6); continue;
/*     */       } 
/* 309 */       if (((String)map.get("type")).equals("ins")) {
/*     */         
/* 311 */         String str7 = paramParagraphBean.getInsXmlStr();
/* 312 */         str7 = str7.replaceAll("xdauthor", str2).replaceAll("1900-01-01T00:00:00Z", str3);
/*     */         
/* 314 */         stringBuffer.append(str5).append(str7.replaceAll("" + SystemEnv.getHtmlLabelName(10003825, ThreadVarLanguage.getLang()) + "", str4)).append(str6);
/*     */       } 
/*     */     } 
/* 317 */     stringBuffer.append("</w:p>");
/* 318 */     return stringBuffer.toString();
/*     */   }
/*     */   
/*     */   private static List<ParagraphBean> getParagraphBeanList(String paramString) {
/* 322 */     ArrayList<ParagraphBean> arrayList = new ArrayList();
/* 323 */     String str = "<w:p .*?>(.*?)</w:p>";
/* 324 */     Pattern pattern = Pattern.compile(str);
/* 325 */     Matcher matcher = pattern.matcher(paramString);
/* 326 */     while (matcher.find()) {
/*     */       
/* 328 */       ParagraphBean paragraphBean = new ParagraphBean();
/* 329 */       String str1 = matcher.group(0);
/* 330 */       paragraphBean.setXmlStr(str1.replaceAll(matcher.group(1), ""));
/* 331 */       String str2 = "<w:pPr>.*?</w:pPr>";
/* 332 */       Pattern pattern1 = Pattern.compile(str2);
/* 333 */       Matcher matcher1 = pattern1.matcher(str1);
/* 334 */       if (matcher1.find())
/*     */       {
/* 336 */         paragraphBean.setpPrXmlStr(matcher1.group());
/*     */       }
/* 338 */       str2 = "<w:r>.*?</w:r>";
/* 339 */       pattern1 = Pattern.compile(str2);
/* 340 */       matcher1 = pattern1.matcher(str1);
/* 341 */       while (matcher1.find()) {
/*     */         
/* 343 */         if (matcher1.group().indexOf("正") > 0) {
/*     */           
/* 345 */           paragraphBean.setrXmlStr(matcher1.group());
/*     */           
/*     */           break;
/*     */         } 
/*     */       } 
/* 350 */       if (paragraphBean.getrXmlStr() == null) {
/*     */         
/* 352 */         str2 = "<w:r w:.*?>.*?</w:r>";
/* 353 */         pattern1 = Pattern.compile(str2);
/* 354 */         matcher1 = pattern1.matcher(str1);
/* 355 */         while (matcher1.find()) {
/*     */           
/* 357 */           if (matcher1.group().indexOf("正") > 0) {
/*     */             
/* 359 */             paragraphBean.setrXmlStr(matcher1.group());
/*     */             
/*     */             break;
/*     */           } 
/*     */         } 
/*     */       } 
/* 365 */       str2 = "<w:del .*?>(.*?)</w:del>";
/* 366 */       pattern1 = Pattern.compile(str2);
/* 367 */       matcher1 = pattern1.matcher(str1);
/* 368 */       if (matcher1.find())
/*     */       {
/* 370 */         paragraphBean.setDelXmlStr(matcher1.group());
/*     */       }
/* 372 */       str2 = "<w:ins .*?>(.*?)</w:ins>";
/* 373 */       pattern1 = Pattern.compile(str2);
/* 374 */       matcher1 = pattern1.matcher(str1);
/* 375 */       if (matcher1.find())
/*     */       {
/* 377 */         paragraphBean.setInsXmlStr(matcher1.group());
/*     */       }
/* 379 */       arrayList.add(paragraphBean);
/*     */     } 
/* 381 */     return arrayList;
/*     */   }
/*     */ 
/*     */   
/*     */   private static String packagePiZhu(String paramString) {
/* 386 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 387 */     String str = "<postil .*?></postil>";
/* 388 */     Pattern pattern = Pattern.compile(str);
/* 389 */     Matcher matcher = pattern.matcher(paramString);
/* 390 */     while (matcher.find()) {
/*     */       
/* 392 */       String str1 = matcher.group();
/* 393 */       String str2 = RegExUtil.match(matcher.group(0), "postil", "dataid");
/* 394 */       String str3 = RegExUtil.match(matcher.group(0), "postil", "type");
/* 395 */       if (str2 != null && !str2.isEmpty()) {
/*     */         
/* 397 */         if (str3.equals("start")) {
/*     */           
/* 399 */           paramString = paramString.replaceAll(matcher.group(0), "<w:commentRangeStart w:id=\"" + str2 + "\"/>"); continue;
/*     */         } 
/* 401 */         if (str3.equals("end")) {
/*     */           
/* 403 */           String str4 = "<w:commentRangeEnd w:id=\"" + str2 + "\"/><w:r><w:rPr><w:rStyle w:val=\"aa\"/><w:rFonts w:ascii=\"Calibri\" w:hAnsi=\"Calibri\" w:cs=\"Times New Roman\"/><w:kern w:val=\"2\"/></w:rPr><w:commentReference w:id=\"" + str2 + "\"/></w:r>";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */           
/* 410 */           paramString = paramString.replaceAll(matcher.group(0), str4);
/*     */         } 
/*     */       } 
/*     */     } 
/* 414 */     return paramString;
/*     */   }
/*     */ 
/*     */   
/*     */   public static Map<String, String> getRunMap(String paramString) {
/* 419 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 420 */     if (paramString.startsWith("<s ")) {
/*     */       
/* 422 */       String str1 = "<s .*?>(.*?)</s>";
/* 423 */       Pattern pattern = Pattern.compile(str1);
/* 424 */       Matcher matcher = pattern.matcher(paramString);
/* 425 */       if (matcher.find())
/*     */       {
/* 427 */         hashMap.put("content", matcher.group(1));
/*     */       }
/* 429 */       hashMap.put("type", "del");
/* 430 */       String str2 = RegExUtil.match(paramString, "s", "xiudingren");
/* 431 */       String str3 = RegExUtil.match(paramString, "s", "xiudingshijian");
/* 432 */       hashMap.put("author", str2);
/* 433 */       hashMap.put("date", str3);
/*     */     }
/* 435 */     else if (paramString.startsWith("<u")) {
/*     */       
/* 437 */       String str1 = "<u .*?>(.*?)</u>";
/* 438 */       Pattern pattern = Pattern.compile(str1);
/* 439 */       Matcher matcher = pattern.matcher(paramString);
/* 440 */       if (matcher.find())
/*     */       {
/* 442 */         hashMap.put("content", matcher.group(1));
/*     */       }
/* 444 */       hashMap.put("type", "ins");
/* 445 */       String str2 = RegExUtil.match(paramString, "u", "xiudingren");
/* 446 */       String str3 = RegExUtil.match(paramString, "u", "xiudingshijian");
/* 447 */       hashMap.put("author", str2);
/* 448 */       hashMap.put("date", str3);
/*     */     }
/*     */     else {
/*     */       
/* 452 */       hashMap.put("type", "zhengwen");
/* 453 */       hashMap.put("content", paramString);
/*     */     } 
/* 455 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   private static void createrComment(String paramString, XWPFDocument paramXWPFDocument) throws Exception {
/* 459 */     ArrayList<CommentBean> arrayList = new ArrayList();
/* 460 */     String str = "<postil .*?>";
/* 461 */     Pattern pattern = Pattern.compile(str);
/* 462 */     Matcher matcher = pattern.matcher(paramString);
/* 463 */     while (matcher.find()) {
/*     */       
/* 465 */       CommentBean commentBean = new CommentBean();
/* 466 */       String str1 = RegExUtil.match(matcher.group(), "postil", "author");
/* 467 */       String str2 = RegExUtil.match(matcher.group(), "postil", "datetime");
/* 468 */       String str3 = RegExUtil.match(matcher.group(), "postil", "text");
/* 469 */       String str4 = RegExUtil.match(matcher.group(), "postil", "dataid");
/* 470 */       if (str4.isEmpty()) {
/*     */         continue;
/*     */       }
/*     */       
/* 474 */       commentBean.setAuthor(str1);
/* 475 */       commentBean.setDate(str2);
/* 476 */       commentBean.setText(str3);
/* 477 */       commentBean.setId(str4);
/* 478 */       arrayList.add(commentBean);
/*     */     } 
/* 480 */     CommentUtils.createrComment(paramXWPFDocument, arrayList);
/*     */   }
/*     */ 
/*     */   
/*     */   private static String replaceBookmark(String paramString, Map<String, String> paramMap) {
/* 485 */     String str = "<w:bookmarkStart .*?/>";
/* 486 */     Pattern pattern = Pattern.compile(str);
/* 487 */     Matcher matcher = pattern.matcher(paramString);
/* 488 */     while (matcher.find()) {
/*     */       
/* 490 */       String str1 = matcher.group();
/* 491 */       String str2 = RegExUtil.match(str1, "w:bookmarkStart", "w:id");
/* 492 */       String str3 = RegExUtil.match(str1, "w:bookmarkStart", "w:name");
/* 493 */       String str4 = paramMap.get(str3);
/*     */       
/* 495 */       if (str4 == null || str3.equals("Content")) {
/*     */         continue;
/*     */       }
/*     */       
/* 499 */       String str5 = "<w:bookmarkStart w:id=\"" + str2 + "\" w:name=\"" + str3 + "\"/>.*?<w:bookmarkEnd w:id=\"" + str2 + "\"/>";
/* 500 */       Pattern pattern1 = Pattern.compile(str5);
/* 501 */       Matcher matcher1 = pattern1.matcher(paramString);
/* 502 */       if (matcher1.find()) {
/*     */         
/* 504 */         String str6 = matcher1.group();
/* 505 */         String str7 = "<w:t>.*?</w:t>";
/* 506 */         Pattern pattern2 = Pattern.compile(str7);
/* 507 */         Matcher matcher2 = pattern2.matcher(str6);
/* 508 */         while (matcher2.find()) {
/*     */           
/* 510 */           String str8 = matcher2.group();
/* 511 */           str6 = str6.replaceAll(str8, "<w:t>" + str4 + "</w:t>");
/* 512 */           str4 = "";
/*     */         } 
/* 514 */         paramString = paramString.replace(matcher1.group(), str6);
/*     */       } 
/*     */     } 
/* 517 */     return paramString;
/*     */   }
/*     */ 
/*     */   
/*     */   public static void main(String[] paramArrayOfString) throws FileNotFoundException {
/* 522 */     String str = "<p class=\"odoc_zhengwen\"><ft>1111</ft></p>";
/* 523 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 524 */     hashMap.put("文档编号", "");
/* 525 */     hashMap.put("紧急程度", "");
/* 526 */     hashMap.put("保密期限", "");
/* 527 */     hashMap.put("密级", "");
/* 528 */     hashMap.put("印发单位", "");
/* 529 */     hashMap.put("发文机关", "");
/* 530 */     hashMap.put("成文日期", "");
/* 531 */     hashMap.put("印发日期", "");
/* 532 */     hashMap.put("抄送", "");
/* 533 */     hashMap.put("发文字号", "");
/* 534 */     hashMap.put("附注", "");
/* 535 */     hashMap.put("标题", "");
/* 536 */     hashMap.put("签发人", "");
/*     */ 
/*     */     
/* 539 */     toDocx(str, "E:\\WORK\\20190410\\ecologyAAA\\gjbzgw_mould.docx", "E:\\WORK\\20190410\\ecologyAAA\\temp11111.docx", "E:\\WORK\\20190410\\ecologyAAA\\1553671712579.docx", (Map)hashMap);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/odoceditutil/ConvertToDocx.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */