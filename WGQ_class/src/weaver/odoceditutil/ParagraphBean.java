/*    */ package weaver.odoceditutil;
/*    */ 
/*    */ public class ParagraphBean {
/*    */   private String delXmlStr;
/*    */   private String insXmlStr;
/*    */   private String rXmlStr;
/*    */   private String pPrXmlStr;
/*    */   private String xmlStr;
/*    */   
/*    */   public String getXmlStr() {
/* 11 */     return this.xmlStr;
/*    */   }
/*    */   
/*    */   public void setXmlStr(String paramString) {
/* 15 */     this.xmlStr = paramString;
/*    */   }
/*    */   
/*    */   public String getDelXmlStr() {
/* 19 */     return this.delXmlStr;
/*    */   }
/*    */   
/*    */   public void setDelXmlStr(String paramString) {
/* 23 */     this.delXmlStr = paramString;
/*    */   }
/*    */   
/*    */   public String getInsXmlStr() {
/* 27 */     return this.insXmlStr;
/*    */   }
/*    */   
/*    */   public void setInsXmlStr(String paramString) {
/* 31 */     this.insXmlStr = paramString;
/*    */   }
/*    */   
/*    */   public String getrXmlStr() {
/* 35 */     return this.rXmlStr;
/*    */   }
/*    */   
/*    */   public void setrXmlStr(String paramString) {
/* 39 */     this.rXmlStr = paramString;
/*    */   }
/*    */   
/*    */   public String getpPrXmlStr() {
/* 43 */     return this.pPrXmlStr;
/*    */   }
/*    */   
/*    */   public void setpPrXmlStr(String paramString) {
/* 47 */     this.pPrXmlStr = paramString;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/odoceditutil/ParagraphBean.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */