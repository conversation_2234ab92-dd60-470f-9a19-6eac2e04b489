/*    */ package weaver.odoceditutil;
/*    */ 
/*    */ public enum FileType
/*    */ {
/*  5 */   DOC("doc"),
/*  6 */   DOCX("docx"),
/*  7 */   UNKNOWN("unknown");
/*    */   
/*    */   private String type;
/*    */   
/*    */   FileType(String paramString1) {
/* 12 */     this.type = paramString1;
/*    */   }
/*    */   
/*    */   public String getType() {
/* 16 */     return this.type;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/odoceditutil/FileType.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */