/*     */ package weaver.odoceditutil;
/*     */ 
/*     */ import java.util.regex.Matcher;
/*     */ import java.util.regex.Pattern;
/*     */ import org.apache.poi.xwpf.usermodel.XWPFParagraph;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public enum ElementType
/*     */ {
/*  13 */   ODOC_ZHUBIAOTI("odoc_zhubiaoti", SystemEnv.getHtmlLabelName(524749, 7)),
/*  14 */   ODOC_FUBIAOTI("odoc_fubiaoti", SystemEnv.getHtmlLabelName(27216, 7)),
/*  15 */   ODOC_WENHAO("odoc_wenhao", SystemEnv.getHtmlLabelName(26609, 7)),
/*  16 */   ODOC_ZHUSONG("odoc_zhusong", SystemEnv.getHtmlLabelName(18342, 7)),
/*  17 */   ODOC_ZHENGWEN("odoc_zhengwen", SystemEnv.getHtmlLabelName(1265, 7)),
/*  18 */   ODOC_XUHAO1("odoc_xuhao1", SystemEnv.getHtmlLabelName(524750, 7)),
/*  19 */   ODOC_XUHAO2("odoc_xuhao2", SystemEnv.getHtmlLabelName(524751, 7)),
/*  20 */   ODOC_XUHAO3("odoc_xuhao3", SystemEnv.getHtmlLabelName(524752, 7)),
/*  21 */   ODOC_XUHAO4("odoc_xuhao4", SystemEnv.getHtmlLabelName(524753, 7)),
/*  22 */   ODOC_XUHAO5("odoc_xuhao5", SystemEnv.getHtmlLabelName(524754, 7)),
/*  23 */   ODOC_FUJIAN1("odoc_fujian1", SystemEnv.getHtmlLabelName(524755, 7)),
/*  24 */   ODOC_FUJIAN2("odoc_fujian2", SystemEnv.getHtmlLabelName(524756, 7)),
/*  25 */   ODOC_FUJIAN3("odoc_fujian3", SystemEnv.getHtmlLabelName(524757, 7)),
/*  26 */   ODOC_FUZHU("odoc_fuzhu", SystemEnv.getHtmlLabelName(517325, 7)),
/*  27 */   ODOC_SHUMING1("odoc_shuming1", SystemEnv.getHtmlLabelName(524758, 7)),
/*  28 */   ODOC_SHUMING2("odoc_shuming2", SystemEnv.getHtmlLabelName(524759, 7)),
/*  29 */   ODOC_QIANSHURIQI1("odoc_qianshuriqi1", SystemEnv.getHtmlLabelName(524760, 7));
/*     */   
/*     */   private String type;
/*     */   private String des;
/*     */   
/*     */   ElementType(String paramString1, String paramString2) {
/*  35 */     this.type = paramString1;
/*  36 */     this.des = paramString2;
/*     */   }
/*     */   
/*     */   public String getType() {
/*  40 */     return this.type;
/*     */   }
/*     */   
/*     */   public String getDes() {
/*  44 */     return this.des;
/*     */   }
/*     */   
/*     */   public static ElementType getElementTypeForDocx(XWPFParagraph paramXWPFParagraph, boolean paramBoolean) {
/*  48 */     String str1 = paramXWPFParagraph.getCTP().xmlText();
/*  49 */     String str2 = paramXWPFParagraph.getText();
/*  50 */     int i = paramXWPFParagraph.getFontAlignment();
/*  51 */     int j = paramXWPFParagraph.getFirstLineIndent();
/*     */     
/*  53 */     if (paramXWPFParagraph.getNumFmt() != null) {
/*     */       
/*  55 */       System.out.println(paramXWPFParagraph.getNumFmt());
/*     */       
/*  57 */       System.out.println(paramXWPFParagraph.getNumLevelText());
/*  58 */       if (paramXWPFParagraph.getNumFmt().equals("chineseCountingThousand") || paramXWPFParagraph.getNumFmt().equals("japaneseCounting")) {
/*     */         
/*  60 */         if (paramXWPFParagraph.getNumLevelText().endsWith("%1、"))
/*     */         {
/*  62 */           return ODOC_XUHAO1;
/*     */         }
/*     */ 
/*     */         
/*  66 */         return ODOC_XUHAO2;
/*     */       } 
/*     */ 
/*     */ 
/*     */       
/*  71 */       if (paramXWPFParagraph.getNumLevelText().endsWith("%1."))
/*     */       {
/*  73 */         return ODOC_XUHAO3;
/*     */       }
/*     */ 
/*     */       
/*  77 */       return ODOC_XUHAO4;
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/*  82 */     if (str2.replaceAll(" ", "").endsWith("：")) if ((((i == 1) ? 1 : 0) & ((j < 0) ? 1 : 0)) != 0)
/*     */       {
/*  84 */         return ODOC_ZHUSONG;
/*     */       } 
/*  86 */     if (str2.replaceAll(" ", "").startsWith("附件：1．") || str2.startsWith("附件：1、"))
/*     */     {
/*  88 */       return ODOC_FUJIAN2;
/*     */     }
/*  90 */     if (str2.replaceAll(" ", "").startsWith("附件："))
/*     */     {
/*  92 */       return ODOC_FUJIAN1;
/*     */     }
/*  94 */     if (str2.replaceAll(" ", "").startsWith("(") && str2.endsWith(")"))
/*     */     {
/*  96 */       return ODOC_FUZHU;
/*     */     }
/*  98 */     if (isValidDate(str2))
/*     */     {
/* 100 */       return ODOC_QIANSHURIQI1;
/*     */     }
/*     */ 
/*     */ 
/*     */     
/* 105 */     String str3 = "<w:jc .*?>";
/* 106 */     Pattern pattern = Pattern.compile(str3);
/* 107 */     Matcher matcher = pattern.matcher(str1);
/*     */     
/* 109 */     if (matcher.find()) {
/*     */       
/* 111 */       String str4 = matcher.group();
/* 112 */       String str5 = RegExUtil.match(str4, "w:jc", "w:val");
/* 113 */       if (!str5.isEmpty())
/*     */       {
/* 115 */         if (str5.equals("center")) {
/*     */           
/* 117 */           str3 = "<w:sz .*?>";
/* 118 */           pattern = Pattern.compile(str3);
/* 119 */           matcher = pattern.matcher(str1);
/* 120 */           if (matcher.find())
/*     */           {
/* 122 */             String str = matcher.group();
/* 123 */             int k = Integer.parseInt(RegExUtil.match(str, "w:sz", "w:val"));
/*     */             
/* 125 */             if (k == 44)
/*     */             {
/* 127 */               return ODOC_ZHUBIAOTI;
/*     */             }
/* 129 */             if (k == 32)
/*     */             {
/* 131 */               if (str2.endsWith(" 号"))
/*     */               {
/* 133 */                 return ODOC_WENHAO;
/*     */               }
/* 135 */               if (k == 32)
/*     */               {
/* 137 */                 return ODOC_FUBIAOTI;
/*     */               }
/*     */             }
/*     */           
/*     */           }
/*     */         
/* 143 */         } else if (str5.equals("right")) {
/*     */           
/* 145 */           return ODOC_SHUMING1;
/*     */         } 
/*     */       }
/*     */     } 
/*     */     
/* 150 */     return paramBoolean ? ODOC_FUJIAN3 : ODOC_ZHENGWEN;
/*     */   }
/*     */   
/*     */   public static boolean isValidDate(String paramString) {
/* 154 */     String str = "^[一二三四五六七八九○]{4}年[一二三四五六七八九十]{1,2}月[一二三四五六七八九十]{1,2}日$";
/* 155 */     Pattern pattern = Pattern.compile(str);
/* 156 */     Matcher matcher = pattern.matcher(paramString);
/* 157 */     if (matcher.find())
/*     */     {
/* 159 */       return true;
/*     */     }
/*     */     
/* 162 */     str = "^[1234567890]{4}年[1234567890]{1,2}月[1234567890]{1,2}日$";
/* 163 */     pattern = Pattern.compile(str);
/* 164 */     matcher = pattern.matcher(paramString);
/* 165 */     if (matcher.find())
/*     */     {
/* 167 */       return true;
/*     */     }
/*     */     
/* 170 */     str = "^[1234567890]{4}-[1234567890]{1,2}-[1234567890]{1,2}$";
/* 171 */     pattern = Pattern.compile(str);
/* 172 */     matcher = pattern.matcher(paramString);
/* 173 */     if (matcher.find())
/*     */     {
/* 175 */       return true;
/*     */     }
/* 177 */     return false;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/odoceditutil/ElementType.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */