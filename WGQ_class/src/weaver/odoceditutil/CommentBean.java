/*    */ package weaver.odoceditutil;
/*    */ 
/*    */ public class CommentBean {
/*    */   private String id;
/*    */   private String author;
/*    */   private String date;
/*    */   private String text;
/*    */   
/*    */   public String getId() {
/* 10 */     return this.id;
/*    */   }
/*    */   
/*    */   public void setId(String paramString) {
/* 14 */     this.id = paramString;
/*    */   }
/*    */   
/*    */   public String getText() {
/* 18 */     return this.text;
/*    */   }
/*    */   
/*    */   public void setText(String paramString) {
/* 22 */     this.text = paramString;
/*    */   }
/*    */   
/*    */   public String getDate() {
/* 26 */     return this.date;
/*    */   }
/*    */   
/*    */   public void setDate(String paramString) {
/* 30 */     this.date = paramString;
/*    */   }
/*    */   
/*    */   public String getAuthor() {
/* 34 */     return this.author;
/*    */   }
/*    */   
/*    */   public void setAuthor(String paramString) {
/* 38 */     this.author = paramString;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/odoceditutil/CommentBean.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */