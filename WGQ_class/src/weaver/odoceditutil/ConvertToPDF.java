/*    */ package weaver.odoceditutil;
/*    */ 
/*    */ import java.io.InputStream;
/*    */ import java.util.Map;
/*    */ import weaver.general.ThreadVarLanguage;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ public class ConvertToPDF {
/*    */   public static Map<String, Object> toPDF(InputStream paramInputStream) {
/* 10 */     return ResultBuildUtil.buildResult(2, "" + SystemEnv.getHtmlLabelName(10003827, ThreadVarLanguage.getLang()) + "", "", -1);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/odoceditutil/ConvertToPDF.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */