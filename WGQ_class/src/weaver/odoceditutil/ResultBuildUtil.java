/*    */ package weaver.odoceditutil;
/*    */ 
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ 
/*    */ public class ResultBuildUtil
/*    */ {
/*    */   public static Map<String, Object> buildResult(int paramInt1, String paramString1, String paramString2, int paramInt2) {
/*  9 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 10 */     hashMap.put("resultCode", Integer.valueOf(paramInt1));
/* 11 */     hashMap.put("message", paramString1);
/* 12 */     hashMap.put("htmlStr", paramString2);
/* 13 */     hashMap.put("time", Integer.valueOf(paramInt2));
/* 14 */     return (Map)hashMap;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/odoceditutil/ResultBuildUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */