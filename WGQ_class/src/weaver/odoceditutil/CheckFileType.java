/*    */ package weaver.odoceditutil;
/*    */ 
/*    */ import java.io.InputStream;
/*    */ import org.apache.poi.poifs.filesystem.FileMagic;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class CheckFileType
/*    */ {
/*    */   public static FileType getFileType(InputStream paramInputStream) {
/*    */     try {
/* 13 */       InputStream inputStream = FileMagic.prepareToCheckMagic(paramInputStream);
/* 14 */       FileMagic fileMagic = FileMagic.valueOf(inputStream);
/* 15 */       FileType fileType = FileType.UNKNOWN;
/*    */ 
/*    */       
/* 18 */       DocxToHtml.toHtml(inputStream);
/*    */       
/* 20 */       switch (fileMagic)
/*    */       { case OLE2:
/* 22 */           fileType = FileType.DOC;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */           
/* 36 */           return fileType;case XML: fileType = FileType.DOCX; return fileType;case OOXML: fileType = FileType.DOCX; return fileType;case UNKNOWN: fileType = FileType.UNKNOWN; return fileType; }  fileType = FileType.UNKNOWN; return fileType;
/*    */     }
/* 38 */     catch (Exception exception) {
/*    */       
/* 40 */       return FileType.UNKNOWN;
/*    */     } 
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/odoceditutil/CheckFileType.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */