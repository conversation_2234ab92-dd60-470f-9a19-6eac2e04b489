/*    */ package weaver.odoceditutil;
/*    */ import java.io.FileInputStream;
/*    */ import java.io.FileNotFoundException;
/*    */ import java.io.InputStream;
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import org.apache.poi.poifs.filesystem.FileMagic;
/*    */ import weaver.general.ThreadVarLanguage;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ public class ConvertToHtml {
/*    */   public static Map<String, Object> toHtml(InputStream paramInputStream) {
/*    */     Map<String, Object> map;
/* 14 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*    */     
/*    */     try {
/* 17 */       InputStream inputStream = FileMagic.prepareToCheckMagic(paramInputStream);
/* 18 */       FileMagic fileMagic = FileMagic.valueOf(inputStream);
/* 19 */       switch (fileMagic) {
/*    */         case OLE2:
/* 21 */           map = DocToHtml.toHtml(inputStream);
/*    */           break;
/*    */         case XML:
/* 24 */           map = DocxToHtml.toHtml(inputStream);
/*    */           break;
/*    */         case OOXML:
/* 27 */           map = DocxToHtml.toHtml(inputStream);
/*    */           break;
/*    */         
/*    */         default:
/* 31 */           map.put("resultCode", Integer.valueOf(4));
/* 32 */           map.put("message", "" + SystemEnv.getHtmlLabelName(10003826, ThreadVarLanguage.getLang()) + ""); break;
/*    */       } 
/* 34 */       inputStream.close();
/*    */     }
/* 36 */     catch (Exception exception) {
/*    */       
/* 38 */       map.put("resultCode", Integer.valueOf(1));
/* 39 */       map.put("message", "" + SystemEnv.getHtmlLabelName(382011, ThreadVarLanguage.getLang()) + "");
/*    */     } 
/* 41 */     return map;
/*    */   }
/*    */   
/*    */   public static void main(String[] paramArrayOfString) throws FileNotFoundException {
/* 45 */     FileInputStream fileInputStream = new FileInputStream("/Users/<USER>/dvs/gw/test/tt.docx");
/* 46 */     Map<String, Object> map = toHtml(fileInputStream);
/*    */     
/* 48 */     System.out.println(map);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/odoceditutil/ConvertToHtml.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */