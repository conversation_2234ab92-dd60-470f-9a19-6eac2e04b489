/*    */ package weaver.odoceditutil;
/*    */ 
/*    */ import java.io.InputStream;
/*    */ import java.util.Map;
/*    */ import weaver.general.ThreadVarLanguage;
/*    */ import weaver.systeminfo.SystemEnv;
/*    */ 
/*    */ public class DocToHtml
/*    */ {
/*    */   public static Map<String, Object> toHtml(InputStream paramInputStream) {
/* 11 */     return ResultBuildUtil.buildResult(2, "" + SystemEnv.getHtmlLabelName(10003827, ThreadVarLanguage.getLang()) + "", "", -1);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/odoceditutil/DocToHtml.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */