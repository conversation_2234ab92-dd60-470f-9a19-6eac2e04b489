/*    */ package weaver.odoceditutil;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.List;
/*    */ import java.util.regex.Matcher;
/*    */ import java.util.regex.Pattern;
/*    */ 
/*    */ public class RegExUtil {
/*    */   public static String match(String paramString1, String paramString2, String paramString3) {
/* 10 */     paramString1 = paramString1.replaceAll("/>", " />");
/* 11 */     paramString1 = paramString1.replaceAll(">", " >");
/* 12 */     ArrayList<String> arrayList = new ArrayList();
/* 13 */     String str = "<" + paramString2 + "[^<>]*?\\s" + paramString3 + "=['\"]?(.*?)['\"]?\\s.*?>";
/* 14 */     Matcher matcher = Pattern.compile(str).matcher(paramString1);
/* 15 */     while (matcher.find()) {
/* 16 */       String str1 = matcher.group(1);
/* 17 */       arrayList.add(str1);
/*    */     } 
/* 19 */     return (arrayList.size() > 0) ? arrayList.get(0) : "";
/*    */   }
/*    */   
/*    */   public static List<String> parser(String paramString) {
/* 23 */     ArrayList<String> arrayList = new ArrayList();
/*    */     
/* 25 */     Pattern pattern = Pattern.compile("<.*?>(.*)</.*?>");
/* 26 */     Matcher matcher = pattern.matcher(paramString);
/* 27 */     if (matcher.matches()) {
/* 28 */       String str = matcher.group(1);
/*    */       
/* 30 */       pattern = Pattern.compile("<(.*?)>");
/* 31 */       matcher = pattern.matcher(str);
/* 32 */       while (matcher.find()) {
/* 33 */         String str1 = matcher.group(1);
/*    */         
/* 35 */         if (str1.endsWith("/")) {
/* 36 */           arrayList.add(matcher.group()); continue;
/*    */         } 
/* 38 */         if (!str1.startsWith("/") && !str1.endsWith("/")) {
/*    */           
/* 40 */           int i = matcher.end() - matcher.group().length();
/*    */           
/* 42 */           byte b = 1;
/* 43 */           while (matcher.find()) {
/* 44 */             String str2 = matcher.group(1);
/* 45 */             if (!str2.startsWith("/") && !str2.endsWith("/")) {
/* 46 */               b++;
/* 47 */             } else if (str2.startsWith("/")) {
/* 48 */               b--;
/*    */             } 
/*    */             
/* 51 */             if (b == 0) {
/*    */               break;
/*    */             }
/*    */           } 
/*    */           
/* 56 */           int j = matcher.end();
/*    */           
/* 58 */           arrayList.add(str.substring(i, j));
/*    */         } 
/*    */       } 
/*    */     } 
/* 62 */     return arrayList;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/odoceditutil/RegExUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */