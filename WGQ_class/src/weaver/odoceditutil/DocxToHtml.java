/*     */ package weaver.odoceditutil;
/*     */ 
/*     */ import java.io.IOException;
/*     */ import java.io.InputStream;
/*     */ import java.math.BigInteger;
/*     */ import java.util.Date;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.regex.Matcher;
/*     */ import java.util.regex.Pattern;
/*     */ import org.apache.poi.xwpf.usermodel.XWPFDocument;
/*     */ import org.apache.poi.xwpf.usermodel.XWPFParagraph;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ public class DocxToHtml {
/*     */   public static Map<String, Object> toHtml(InputStream paramInputStream) {
/*  19 */     Map<String, Object> map = ResultBuildUtil.buildResult(1, "" + SystemEnv.getHtmlLabelName(10003828, ThreadVarLanguage.getLang()) + "", "", -1);
/*     */     
/*     */     try {
/*  22 */       Date date1 = new Date();
/*     */       
/*  24 */       XWPFDocument xWPFDocument = new XWPFDocument(paramInputStream);
/*     */ 
/*     */       
/*  27 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*     */       
/*  29 */       Map<String, CommentBean> map1 = CommentUtils.getComments(xWPFDocument);
/*     */       
/*  31 */       boolean bool = false;
/*  32 */       StringBuffer stringBuffer = new StringBuffer();
/*  33 */       List list = xWPFDocument.getParagraphs();
/*     */       
/*  35 */       for (XWPFParagraph xWPFParagraph : list) {
/*     */ 
/*     */         
/*  38 */         String str1 = xWPFParagraph.getCTP().xmlText();
/*  39 */         String str2 = xWPFParagraph.getText();
/*     */         
/*  41 */         if (str2.isEmpty() || str2.equals(" ") || str2.equals("&nbsp;") || xWPFParagraph.isEmpty()) {
/*     */           continue;
/*     */         }
/*     */ 
/*     */         
/*  46 */         ElementType elementType = ElementType.getElementTypeForDocx(xWPFParagraph, bool);
/*     */ 
/*     */         
/*  49 */         List<String> list1 = RegExUtil.parser(str1);
/*     */         
/*  51 */         BigInteger bigInteger = xWPFParagraph.getNumID();
/*     */         
/*  53 */         if (bigInteger != null)
/*     */         {
/*  55 */           if (hashMap.get(bigInteger) != null) {
/*     */             
/*  57 */             hashMap.put(bigInteger, Integer.valueOf(((Integer)hashMap.get(bigInteger)).intValue() + 1));
/*     */           }
/*     */           else {
/*     */             
/*  61 */             hashMap.put(bigInteger, Integer.valueOf(1));
/*     */           } 
/*     */         }
/*  64 */         String str3 = "";
/*  65 */         String str4 = "";
/*     */         
/*  67 */         if (bigInteger != null)
/*     */         {
/*  69 */           if (elementType.equals(ElementType.ODOC_XUHAO1)) {
/*     */             
/*  71 */             int i = ((Integer)hashMap.get(bigInteger)).intValue();
/*  72 */             if (!xWPFParagraph.getText().startsWith(toChinese(i + "") + "、"))
/*     */             {
/*  74 */               str3 = toChinese(((Integer)hashMap.get(bigInteger)).toString()) + "、";
/*     */             }
/*  76 */             str4 = "<div class=\"wea-ck-list-advance listadvance-chcpt\" listadvancetype=\"CHCPT\" data-content=\"" + str3 + "\" data-num=\"1\" data-key=\"1\" data-margin=\"2\" style=\"margin-left: 2em;\">";
/*     */           }
/*  78 */           else if (elementType.equals(ElementType.ODOC_XUHAO2)) {
/*     */             
/*  80 */             if (!xWPFParagraph.getText().startsWith("（" + toChinese(((Integer)hashMap.get(bigInteger)).toString()) + "）"))
/*     */             {
/*  82 */               str3 = "（" + toChinese(((Integer)hashMap.get(bigInteger)).toString()) + "）";
/*     */             }
/*  84 */             str4 = "<div class=\"wea-ck-list-advance listadvance-chcptp\" listadvancetype=\"CHCPTP\" data-content=\"" + str3 + "\" data-num=\"1\" data-key=\"1\" data-margin=\"2\" style=\"margin-left: 2em;\">";
/*     */           }
/*  86 */           else if (elementType.equals(ElementType.ODOC_XUHAO3)) {
/*     */             
/*  88 */             if (!xWPFParagraph.getText().startsWith(((Integer)hashMap.get(bigInteger)).toString() + "．"))
/*     */             {
/*  90 */               str3 = ((Integer)hashMap.get(bigInteger)).toString() + "．";
/*     */             }
/*  92 */             str4 = "<div class=\"wea-ck-list-advance listadvance-number\" listadvancetype=\"NUMBER\" data-content=\"" + str3 + "\" data-num=\"1\" data-key=\"1\" data-margin=\"2\" style=\"margin-left: 2em;\">";
/*     */           }
/*  94 */           else if (elementType.equals(ElementType.ODOC_XUHAO4)) {
/*     */             
/*  96 */             if (!xWPFParagraph.getText().startsWith("（" + ((Integer)hashMap.get(bigInteger)).toString() + "）"))
/*     */             {
/*  98 */               str3 = "（" + ((Integer)hashMap.get(bigInteger)).toString() + "）";
/*     */             }
/* 100 */             str4 = "<div class=\"wea-ck-list-advance listadvance-numberp\" listadvancetype=\"NUMBERP\" data-content=\"" + str3 + "\" data-num=\"1\" data-key=\"1\" data-margin=\"2\" style=\"margin-left: 2em;\">";
/*     */           } 
/*     */         }
/*     */         
/* 104 */         StringBuffer stringBuffer1 = new StringBuffer("");
/* 105 */         for (String str5 : list1) {
/* 106 */           String str6 = "";
/* 107 */           str5 = str5.replaceAll("<w:t>", "<w:t >");
/* 108 */           if (str5.startsWith("<w:r ") || str5.startsWith("<w:r>")) {
/*     */             
/* 110 */             String str = "<w:t.*?>(.*?)</w:t>";
/* 111 */             Pattern pattern = Pattern.compile(str);
/* 112 */             Matcher matcher = pattern.matcher(str5);
/* 113 */             while (matcher.find())
/*     */             {
/* 115 */               str6 = str6 + matcher.group(1);
/*     */             }
/* 117 */             stringBuffer1.append(str3).append(str6);
/* 118 */             str3 = ""; continue;
/*     */           } 
/* 120 */           if (str5.startsWith("<w:del ")) {
/*     */             
/* 122 */             String str = "<w:del .*?>";
/* 123 */             Pattern pattern = Pattern.compile(str);
/* 124 */             Matcher matcher = pattern.matcher(str5);
/* 125 */             if (matcher.find()) {
/*     */               
/* 127 */               String str7 = matcher.group().replaceAll(">", " >");
/* 128 */               String str8 = RegExUtil.match(str7, "w:del", "w:author");
/* 129 */               String str9 = RegExUtil.match(str7, "w:del", "w:date");
/* 130 */               String str10 = RegExUtil.match(str7, "w:del", "w:id");
/* 131 */               str = "<w:delText.*?>(.*?)</w:delText>";
/* 132 */               pattern = Pattern.compile(str);
/* 133 */               matcher = pattern.matcher(str5);
/* 134 */               if (matcher.find())
/*     */               {
/* 136 */                 str6 = matcher.group(1);
/*     */               }
/* 138 */               stringBuffer1.append("<s id='revision_" + str10 + "' xiudingren='" + str8 + "' xiudingshijian='" + str9 + "'>").append(str3).append(str6).append("</s>");
/* 139 */               str3 = "";
/*     */             }  continue;
/*     */           } 
/* 142 */           if (str5.startsWith("<w:ins ")) {
/*     */             
/* 144 */             String str = "<w:t.*?>(.*?)</w:t>";
/* 145 */             Pattern pattern = Pattern.compile(str);
/* 146 */             Matcher matcher = pattern.matcher(str5);
/* 147 */             while (matcher.find())
/*     */             {
/* 149 */               str6 = str6 + matcher.group(1);
/*     */             }
/*     */             
/* 152 */             str = "<w:ins .*?>";
/* 153 */             pattern = Pattern.compile(str);
/* 154 */             matcher = pattern.matcher(str5);
/* 155 */             if (matcher.find()) {
/*     */               
/* 157 */               String str7 = RegExUtil.match(matcher.group(), "w:ins", "w:id");
/* 158 */               String str8 = RegExUtil.match(matcher.group(), "w:ins", "w:author");
/* 159 */               String str9 = RegExUtil.match(matcher.group().replaceAll(">", " >"), "w:ins", "w:date");
/* 160 */               stringBuffer1.append("<u id='revision_" + str7 + "' class='odoc_insertTagInline' xiudingren='" + str8 + "' xiudingshijian='" + str9 + "'>").append(str3).append(str6).append("</u>");
/* 161 */               str3 = "";
/*     */             }  continue;
/*     */           } 
/* 164 */           if (str5.startsWith("<w:commentRangeStart ")) {
/*     */             
/* 166 */             String str7 = RegExUtil.match(str5, "w:commentRangeStart", "w:id");
/* 167 */             CommentBean commentBean = map1.get(str7);
/* 168 */             stringBuffer1.append("<postil class='odoc_postilWord' dataid='" + str7 + "' author='" + commentBean.getAuthor() + "' datetime = '" + commentBean.getDate() + "' text='" + commentBean.getText() + "' type='start' ></postil>");
/*     */             
/* 170 */             String str8 = "<w:t.*?>(.*?)</w:t>";
/* 171 */             Pattern pattern = Pattern.compile(str8);
/* 172 */             Matcher matcher = pattern.matcher(str5);
/* 173 */             while (matcher.find())
/*     */             {
/* 175 */               str6 = str6 + matcher.group(1);
/*     */             }
/* 177 */             stringBuffer1.append(str3).append(str6);
/* 178 */             str3 = ""; continue;
/*     */           } 
/* 180 */           if (str5.startsWith("<w:commentRangeEnd ")) {
/*     */             
/* 182 */             String str = RegExUtil.match(str5, "w:commentRangeEnd", "w:id");
/* 183 */             CommentBean commentBean = map1.get(str);
/* 184 */             stringBuffer1.append("<postil class='odoc_postilWord' dataid='" + str + "' author='" + commentBean.getAuthor() + "' datetime = '" + commentBean.getDate() + "' text='" + commentBean.getText() + "' type='end' ></postil>");
/*     */           } 
/*     */         } 
/*     */ 
/*     */         
/* 189 */         if (elementType.equals(ElementType.ODOC_FUJIAN2))
/*     */         {
/* 191 */           bool = true;
/*     */         }
/* 193 */         stringBuffer.append("<p class='" + elementType.getType() + "'>").append(stringBuffer1).append("</p>");
/*     */       } 
/* 195 */       paramInputStream.close();
/* 196 */       Date date2 = new Date();
/* 197 */       map = ResultBuildUtil.buildResult(0, "" + SystemEnv.getHtmlLabelName(25008, ThreadVarLanguage.getLang()) + "", stringBuffer.toString(), (int)(date2.getTime() - date1.getTime()));
/* 198 */     } catch (IOException iOException) {
/* 199 */       map = ResultBuildUtil.buildResult(3, "" + SystemEnv.getHtmlLabelName(10003829, ThreadVarLanguage.getLang()) + "", "", -1);
/* 200 */     } catch (Exception exception) {
/* 201 */       map = ResultBuildUtil.buildResult(3, "" + SystemEnv.getHtmlLabelName(10003830, ThreadVarLanguage.getLang()) + "", "", -1);
/*     */     } 
/* 203 */     return map;
/*     */   }
/*     */   
/*     */   public static String toChinese(String paramString) {
/* 207 */     String[] arrayOfString1 = { "零", "一", "二", "三", "四", "五", "六", "七", "八", "九" };
/* 208 */     String[] arrayOfString2 = { "十", "百", "千", "万", "十", "百", "千", "亿", "十", "百", "千" };
/* 209 */     String str = "";
/* 210 */     int i = paramString.length();
/* 211 */     for (byte b = 0; b < i; b++) {
/* 212 */       int j = paramString.charAt(b) - 48;
/* 213 */       if (b != i - 1 && j != 0) {
/* 214 */         str = str + arrayOfString1[j] + arrayOfString2[i - 2 - b];
/*     */       } else {
/* 216 */         str = str + arrayOfString1[j];
/*     */       } 
/*     */     } 
/* 219 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/odoceditutil/DocxToHtml.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */