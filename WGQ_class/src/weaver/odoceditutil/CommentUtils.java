/*     */ package weaver.odoceditutil;
/*     */ import java.io.OutputStream;
/*     */ import java.math.BigInteger;
/*     */ import java.text.SimpleDateFormat;
/*     */ import java.util.Calendar;
/*     */ import java.util.Date;
/*     */ import java.util.HashMap;
/*     */ import java.util.Iterator;
/*     */ import java.util.Map;
/*     */ import org.apache.poi.ooxml.POIXMLDocumentPart;
/*     */ import org.apache.poi.openxml4j.opc.OPCPackage;
/*     */ import org.apache.poi.openxml4j.opc.PackagePart;
/*     */ import org.apache.poi.openxml4j.opc.PackagePartName;
/*     */ import org.apache.poi.xwpf.usermodel.XWPFDocument;
/*     */ import org.apache.poi.xwpf.usermodel.XWPFParagraph;
/*     */ import org.apache.poi.xwpf.usermodel.XWPFRelation;
/*     */ import org.apache.xmlbeans.XmlCursor;
/*     */ import org.apache.xmlbeans.XmlOptions;
/*     */ import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTComment;
/*     */ import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTComments;
/*     */ import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTDocument1;
/*     */ import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTP;
/*     */ import org.openxmlformats.schemas.wordprocessingml.x2006.main.CommentsDocument;
/*     */ 
/*     */ public class CommentUtils {
/*     */   public static Map<String, CommentBean> getComments(XWPFDocument paramXWPFDocument) {
/*  27 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     try {
/*  29 */       POIXMLDocumentPart pOIXMLDocumentPart = paramXWPFDocument.getPart();
/*  30 */       CTDocument1 cTDocument1 = paramXWPFDocument.getDocument();
/*  31 */       XmlCursor xmlCursor = cTDocument1.newCursor();
/*     */       
/*  33 */       xmlCursor.selectPath("./*");
/*  34 */       while (xmlCursor.toNextSelection()) {
/*  35 */         Iterator<POIXMLDocumentPart.RelationPart> iterator = pOIXMLDocumentPart.getRelationParts().iterator();
/*  36 */         while (iterator.hasNext()) {
/*  37 */           POIXMLDocumentPart.RelationPart relationPart = iterator.next();
/*  38 */           POIXMLDocumentPart pOIXMLDocumentPart1 = relationPart.getDocumentPart();
/*  39 */           String str = relationPart.getRelationship().getRelationshipType();
/*     */           
/*  41 */           if (str.equals(XWPFRelation.COMMENT.getRelation())) {
/*  42 */             CommentsDocument commentsDocument = CommentsDocument.Factory.parse(pOIXMLDocumentPart1.getPackagePart().getInputStream(), POIXMLTypeLoader.DEFAULT_XML_OPTIONS);
/*     */             
/*  44 */             CTComment[] arrayOfCTComment = commentsDocument.getComments().getCommentArray();
/*     */             
/*  46 */             for (byte b = 0; b < arrayOfCTComment.length; b++) {
/*  47 */               CTComment cTComment = arrayOfCTComment[b];
/*     */               
/*  49 */               String str1 = cTComment.getId().toString();
/*  50 */               String str2 = cTComment.getAuthor();
/*  51 */               String str3 = "";
/*  52 */               CTP[] arrayOfCTP = cTComment.getPArray();
/*  53 */               int i = arrayOfCTP.length;
/*  54 */               for (byte b1 = 0; b1 < i; b1++) {
/*  55 */                 CTP cTP = arrayOfCTP[b1];
/*  56 */                 XWPFParagraph xWPFParagraph = new XWPFParagraph(cTP, (IBody)paramXWPFDocument);
/*  57 */                 str3 = str3 + xWPFParagraph.getText();
/*     */               } 
/*  59 */               CommentBean commentBean = new CommentBean();
/*  60 */               commentBean.setId(str1);
/*  61 */               commentBean.setAuthor(str2);
/*  62 */               commentBean.setText(str3);
/*  63 */               commentBean.setDate(cTComment.getDate().toString());
/*  64 */               hashMap.put(str1, commentBean);
/*     */             } 
/*     */           } 
/*     */         } 
/*     */       } 
/*  69 */       xmlCursor.dispose();
/*  70 */     } catch (Exception exception) {
/*  71 */       exception.printStackTrace();
/*     */     } 
/*  73 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */   
/*     */   private static CusXWPFCommentsDocument createCommentsDocument(XWPFDocument paramXWPFDocument) throws Exception {
/*  78 */     OPCPackage oPCPackage = paramXWPFDocument.getPackage();
/*  79 */     PackagePartName packagePartName = PackagingURIHelper.createPartName("/word/comments.xml");
/*  80 */     PackagePart packagePart = oPCPackage.createPart(packagePartName, "application/vnd.openxmlformats-officedocument.wordprocessingml.comments+xml");
/*  81 */     CusXWPFCommentsDocument cusXWPFCommentsDocument = new CusXWPFCommentsDocument(packagePart);
/*     */     
/*  83 */     String str = "rId" + (paramXWPFDocument.getRelationParts().size() + 1);
/*  84 */     paramXWPFDocument.addRelation(str, (POIXMLRelation)XWPFRelation.COMMENT, cusXWPFCommentsDocument);
/*  85 */     return cusXWPFCommentsDocument;
/*     */   }
/*     */   
/*     */   private static class CusXWPFCommentsDocument
/*     */     extends POIXMLDocumentPart {
/*     */     private CTComments comments;
/*     */     
/*     */     private CusXWPFCommentsDocument(PackagePart param1PackagePart) throws Exception {
/*  93 */       super(param1PackagePart);
/*  94 */       this.comments = CommentsDocument.Factory.newInstance().addNewComments();
/*     */     }
/*     */     
/*     */     private CTComments getComments() {
/*  98 */       return this.comments;
/*     */     }
/*     */ 
/*     */     
/*     */     protected void commit() throws IOException {
/* 103 */       XmlOptions xmlOptions = new XmlOptions(POIXMLTypeLoader.DEFAULT_XML_OPTIONS);
/* 104 */       xmlOptions.setSaveSyntheticDocumentElement(new QName(CTComments.type.getName().getNamespaceURI(), "comments"));
/* 105 */       PackagePart packagePart = getPackagePart();
/* 106 */       OutputStream outputStream = packagePart.getOutputStream();
/* 107 */       this.comments.save(outputStream, xmlOptions);
/* 108 */       outputStream.close();
/*     */     }
/*     */   }
/*     */ 
/*     */   
/*     */   public static boolean createrComment(XWPFDocument paramXWPFDocument, List<CommentBean> paramList) throws Exception {
/* 114 */     CusXWPFCommentsDocument cusXWPFCommentsDocument = createCommentsDocument(paramXWPFDocument);
/* 115 */     CTComments cTComments = cusXWPFCommentsDocument.getComments();
/* 116 */     for (CommentBean commentBean : paramList) {
/*     */ 
/*     */       
/* 119 */       BigInteger bigInteger = BigInteger.valueOf(Integer.parseInt(commentBean.getId()));
/*     */       
/* 121 */       CTComment cTComment = cTComments.addNewComment();
/* 122 */       cTComment.setAuthor(commentBean.getAuthor());
/* 123 */       cTComment.setInitials(commentBean.getAuthor());
/* 124 */       SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss Z");
/* 125 */       String str = commentBean.getDate().replace("Z", " UTC");
/* 126 */       Date date = simpleDateFormat.parse(str);
/* 127 */       Calendar calendar = Calendar.getInstance();
/* 128 */       calendar.setTime(date);
/* 129 */       cTComment.setDate(calendar);
/*     */       
/* 131 */       cTComment.addNewP().addNewR().addNewT().setStringValue(commentBean.getText());
/*     */       
/* 133 */       cTComment.setId(bigInteger);
/*     */     } 
/* 135 */     return true;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/odoceditutil/CommentUtils.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */