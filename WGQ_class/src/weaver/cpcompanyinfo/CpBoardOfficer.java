/*    */ package weaver.cpcompanyinfo;
/*    */ 
/*    */ 
/*    */ 
/*    */ public class CpBoardOfficer
/*    */ {
/*    */   private int boardofficerid;
/*    */   private int directorsid;
/*    */   private String sessions;
/*    */   private String officename;
/*    */   private String officebegindate;
/*    */   private String officeenddate;
/*    */   private String isstop;
/*    */   
/*    */   public int getBoardofficerid() {
/* 16 */     return this.boardofficerid;
/*    */   }
/*    */   public void setBoardofficerid(int paramInt) {
/* 19 */     this.boardofficerid = paramInt;
/*    */   }
/*    */   public int getDirectorsid() {
/* 22 */     return this.directorsid;
/*    */   }
/*    */   public void setDirectorsid(int paramInt) {
/* 25 */     this.directorsid = paramInt;
/*    */   }
/*    */   public String getSessions() {
/* 28 */     return this.sessions;
/*    */   }
/*    */   public void setSessions(String paramString) {
/* 31 */     this.sessions = paramString;
/*    */   }
/*    */   public String getOfficename() {
/* 34 */     return this.officename;
/*    */   }
/*    */   public void setOfficename(String paramString) {
/* 37 */     this.officename = paramString;
/*    */   }
/*    */   public String getOfficebegindate() {
/* 40 */     return this.officebegindate;
/*    */   }
/*    */   public void setOfficebegindate(String paramString) {
/* 43 */     this.officebegindate = paramString;
/*    */   }
/*    */   public String getOfficeenddate() {
/* 46 */     return this.officeenddate;
/*    */   }
/*    */   public void setOfficeenddate(String paramString) {
/* 49 */     this.officeenddate = paramString;
/*    */   }
/*    */   public String getIsstop() {
/* 52 */     return this.isstop;
/*    */   }
/*    */   public void setIsstop(String paramString) {
/* 55 */     this.isstop = paramString;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpcompanyinfo/CpBoardOfficer.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */