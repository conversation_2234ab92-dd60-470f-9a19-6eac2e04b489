/*     */ package weaver.cpcompanyinfo;
/*     */ 
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ProManageUtil
/*     */ {
/*     */   public boolean writeCompanylog(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5) {
/*  28 */     boolean bool = false;
/*     */     
/*     */     try {
/*  31 */       String str1 = "select * from ";
/*  32 */       if ("1".equals(paramString1)) {
/*     */         
/*  34 */         str1 = str1 + " pro_single";
/*  35 */         str1 = str1 + " where pr_id='" + paramString2 + "'";
/*     */       } 
/*  37 */       if ("2".equals(paramString1)) {
/*     */         
/*  39 */         str1 = str1 + " pro_singletask";
/*  40 */         str1 = str1 + " where id='" + paramString2 + "'";
/*     */       } 
/*  42 */       if ("3".equals(paramString1) || "4".equals(paramString1)) {
/*  43 */         str1 = str1 + " cpcompanyinfo";
/*  44 */         str1 = str1 + " where companyid = '" + paramString2 + "'";
/*     */       } 
/*     */ 
/*     */       
/*  48 */       String str2 = "";
/*  49 */       String str3 = "";
/*     */       
/*  51 */       String str4 = Util.date(2);
/*  52 */       String str5 = str4.split(" ")[1];
/*  53 */       String str6 = str4.split(" ")[0];
/*     */ 
/*     */       
/*  56 */       RecordSet recordSet = new RecordSet();
/*  57 */       if (recordSet.execute(str1) && recordSet.next()) {
/*     */         
/*  59 */         if ("1".equals(paramString1)) {
/*     */           
/*  61 */           str2 = recordSet.getString("pr_name");
/*  62 */           str3 = recordSet.getString("pr_coding");
/*     */         } 
/*  64 */         if ("2".equals(paramString1)) {
/*     */           
/*  66 */           str2 = recordSet.getString("psl_name");
/*  67 */           str3 = recordSet.getString("psl_coding");
/*     */         } 
/*  69 */         if ("3".equals(paramString1) || "4".equals(paramString1)) {
/*  70 */           if (!paramString5.equals("")) {
/*  71 */             str2 = recordSet.getString("companyname") + "--" + paramString5;
/*     */           } else {
/*  73 */             str2 = recordSet.getString("companyname");
/*     */           } 
/*     */           
/*  76 */           str3 = recordSet.getString("archivenum");
/*     */         } 
/*     */ 
/*     */         
/*  80 */         str1 = " insert into pro_taskLog (plog_qf,plog_protaskid,plog_proname,plog_coding,plog_desc,plog_data,plog_time,plog_person)";
/*  81 */         str1 = str1 + " values('" + paramString1 + "','" + paramString2 + "','" + str2 + "','" + str3 + "','" + paramString3 + "','" + str6 + "','" + str5 + "','" + paramString4 + "')";
/*     */ 
/*     */         
/*  84 */         RecordSet recordSet1 = new RecordSet();
/*  85 */         if (recordSet1.execute(str1) && recordSet1.next())
/*     */         {
/*  87 */           bool = true;
/*     */         }
/*     */       } 
/*  90 */     } catch (Exception exception) {
/*     */       
/*  92 */       exception.printStackTrace();
/*  93 */       bool = false;
/*     */     } 
/*  95 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static int getIsOpenCpcompanyinfo() {
/* 103 */     return Util.getIntValue((new BaseBean()).getPropValue("cpcompanyinfo", "isOpenCpcompanyinfo"), 0);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean checkEdition(String paramString1, String paramString2, String paramString3, String paramString4) {
/* 116 */     boolean bool = false;
/* 117 */     String str = "";
/* 118 */     RecordSet recordSet = new RecordSet();
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     try {
/* 124 */       if (paramString1.equals("license"))
/*     */       {
/* 126 */         str = "select count(*) s  from CPBUSINESSLICENSEVERSION where versionnum = '" + paramString4 + "' and licenseid='" + paramString2 + "' and companyid='" + paramString3 + "'";
/*     */       }
/* 128 */       if (paramString1.equals("constitution"))
/*     */       {
/* 130 */         str = "select count(*) s  from CPCONSTITUTIONVERSION where versionnum  = '" + paramString4 + "' and companyid='" + paramString3 + "'";
/*     */       }
/* 132 */       if (paramString1.equals("share"))
/*     */       {
/* 134 */         str = "select count(*) s  from CPSHAREHOLDERVERSION where versionnum  = '" + paramString4 + "' and companyid='" + paramString3 + "'";
/*     */       }
/* 136 */       if (paramString1.equals("director"))
/*     */       {
/* 138 */         str = "select count(*) s  from CPBOARDVERSION where versionnum  = '" + paramString4 + "' and companyid='" + paramString3 + "'";
/*     */       }
/* 140 */       recordSet.execute(str);
/* 141 */       if (recordSet.next() && recordSet.getInt("s") > 0) {
/* 142 */         bool = true;
/*     */       }
/* 144 */     } catch (Exception exception) {
/* 145 */       exception.printStackTrace();
/*     */     } 
/* 147 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String fetchString(String paramString) {
/* 167 */     if (paramString.indexOf(".") != -1) {
/* 168 */       if (paramString.startsWith(".")) {
/* 169 */         paramString = "0" + paramString;
/*     */       }
/* 171 */       return replacedZero(paramString);
/*     */     } 
/* 173 */     return paramString;
/*     */   }
/*     */ 
/*     */   
/*     */   public static String replacedZero(String paramString) {
/* 178 */     if (paramString.endsWith("0")) {
/* 179 */       paramString = paramString.substring(0, paramString.length() - 1);
/* 180 */       paramString = replacedZero(paramString);
/*     */     } 
/* 182 */     if (paramString.endsWith(".")) {
/* 183 */       paramString = paramString + "0";
/*     */     }
/* 185 */     return paramString;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpcompanyinfo/ProManageUtil.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */