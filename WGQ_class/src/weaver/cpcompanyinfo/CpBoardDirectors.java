/*     */ package weaver.cpcompanyinfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CpBoardDirectors
/*     */ {
/*     */   private int directorsid;
/*     */   private String chairman;
/*     */   private String appointbegindate;
/*     */   private String appointenddate;
/*     */   private String appointduetime;
/*     */   private String supervisor;
/*     */   private String generalmanager;
/*     */   private String drectorsaffix;
/*     */   private String createdatetime;
/*     */   private String updatetime;
/*     */   private int versionid;
/*     */   private String isdel;
/*     */   private String ischairman;
/*     */   private String managerbegindate;
/*     */   private String managerenddate;
/*     */   private String managerduetime;
/*  26 */   private String imgid = "";
/*  27 */   private String imgname = "";
/*     */   
/*     */   public String getImgid() {
/*  30 */     return this.imgid;
/*     */   }
/*     */   public void setImgid(String paramString) {
/*  33 */     this.imgid = paramString;
/*     */   }
/*     */   public String getImgname() {
/*  36 */     return this.imgname;
/*     */   }
/*     */   public void setImgname(String paramString) {
/*  39 */     this.imgname = paramString;
/*     */   }
/*     */   public String getManagerbegindate() {
/*  42 */     return this.managerbegindate;
/*     */   }
/*     */   public void setManagerbegindate(String paramString) {
/*  45 */     this.managerbegindate = paramString;
/*     */   }
/*     */   public String getManagerenddate() {
/*  48 */     return this.managerenddate;
/*     */   }
/*     */   public void setManagerenddate(String paramString) {
/*  51 */     this.managerenddate = paramString;
/*     */   }
/*     */   public String getManagerduetime() {
/*  54 */     return this.managerduetime;
/*     */   }
/*     */   public void setManagerduetime(String paramString) {
/*  57 */     this.managerduetime = paramString;
/*     */   }
/*     */   public int getDirectorsid() {
/*  60 */     return this.directorsid;
/*     */   }
/*     */   public void setDirectorsid(int paramInt) {
/*  63 */     this.directorsid = paramInt;
/*     */   }
/*     */   public String getChairman() {
/*  66 */     return this.chairman;
/*     */   }
/*     */   public void setChairman(String paramString) {
/*  69 */     this.chairman = paramString;
/*     */   }
/*     */   public String getAppointbegindate() {
/*  72 */     return this.appointbegindate;
/*     */   }
/*     */   public void setAppointbegindate(String paramString) {
/*  75 */     this.appointbegindate = paramString;
/*     */   }
/*     */   public String getAppointenddate() {
/*  78 */     return this.appointenddate;
/*     */   }
/*     */   public void setAppointenddate(String paramString) {
/*  81 */     this.appointenddate = paramString;
/*     */   }
/*     */   public String getAppointduetime() {
/*  84 */     return this.appointduetime;
/*     */   }
/*     */   public void setAppointduetime(String paramString) {
/*  87 */     this.appointduetime = paramString;
/*     */   }
/*     */   public String getSupervisor() {
/*  90 */     return this.supervisor;
/*     */   }
/*     */   public void setSupervisor(String paramString) {
/*  93 */     this.supervisor = paramString;
/*     */   }
/*     */   public String getGeneralmanager() {
/*  96 */     return this.generalmanager;
/*     */   }
/*     */   public void setGeneralmanager(String paramString) {
/*  99 */     this.generalmanager = paramString;
/*     */   }
/*     */   public String getDrectorsaffix() {
/* 102 */     return this.drectorsaffix;
/*     */   }
/*     */   public void setDrectorsaffix(String paramString) {
/* 105 */     this.drectorsaffix = paramString;
/*     */   }
/*     */   public String getCreatedatetime() {
/* 108 */     return this.createdatetime;
/*     */   }
/*     */   public void setCreatedatetime(String paramString) {
/* 111 */     this.createdatetime = paramString;
/*     */   }
/*     */   public String getUpdatetime() {
/* 114 */     return this.updatetime;
/*     */   }
/*     */   public void setUpdatetime(String paramString) {
/* 117 */     this.updatetime = paramString;
/*     */   }
/*     */   public int getVersionid() {
/* 120 */     return this.versionid;
/*     */   }
/*     */   public void setVersionid(int paramInt) {
/* 123 */     this.versionid = paramInt;
/*     */   }
/*     */   public String getIsdel() {
/* 126 */     return this.isdel;
/*     */   }
/*     */   public void setIsdel(String paramString) {
/* 129 */     this.isdel = paramString;
/*     */   }
/*     */   public String getIschairman() {
/* 132 */     return this.ischairman;
/*     */   }
/*     */   public void setIschairman(String paramString) {
/* 135 */     this.ischairman = paramString;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpcompanyinfo/CpBoardDirectors.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */