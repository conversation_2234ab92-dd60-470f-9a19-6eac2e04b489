/*    */ package weaver.cpcompanyinfo;
/*    */ 
/*    */ public class CompanyKeyValue {
/*    */   private String key;
/*    */   private String value;
/*    */   private String datetime;
/*    */   private String desc;
/*    */   
/*    */   public String getKey() {
/* 10 */     return this.key;
/*    */   }
/*    */   public void setKey(String paramString) {
/* 13 */     this.key = paramString;
/*    */   }
/*    */   public String getValue() {
/* 16 */     return this.value;
/*    */   }
/*    */   public void setValue(String paramString) {
/* 19 */     this.value = paramString;
/*    */   }
/*    */   public String getDatetime() {
/* 22 */     return this.datetime;
/*    */   }
/*    */   public void setDatetime(String paramString) {
/* 25 */     this.datetime = paramString;
/*    */   }
/*    */   public String getDesc() {
/* 28 */     return this.desc;
/*    */   }
/*    */   public void setDesc(String paramString) {
/* 31 */     this.desc = paramString;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpcompanyinfo/CompanyKeyValue.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */