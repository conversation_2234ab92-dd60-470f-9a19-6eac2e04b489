/*     */ package weaver.cpcompanyinfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CpLicense
/*     */ {
/*   8 */   private String registeraddress = "";
/*   9 */   private String corporation = "";
/*  10 */   private String recordnum = "";
/*  11 */   private String usefulbegindate = "";
/*  12 */   private String usefulenddate = "";
/*  13 */   private String usefulyear = "";
/*  14 */   private String dateinssue = "";
/*  15 */   private String licensestatu = "";
/*  16 */   private String annualinspection = "";
/*  17 */   private String departinssue = "";
/*  18 */   private String scopebusiness = "";
/*  19 */   private String registercapital = "";
/*  20 */   private String paiclupcapital = "";
/*  21 */   private String currencyid = "";
/*  22 */   private String currencyname = "";
/*  23 */   private String corporatdelegate = "";
/*  24 */   private String licenseregistnum = "";
/*  25 */   private String memo = "";
/*  26 */   private String companytype = "";
/*  27 */   private String licensename = "";
/*  28 */   private String licensetype = "";
/*  29 */   private int licenseaffixid = 0;
/*  30 */   private String affixdoc = "";
/*  31 */   private String imgid = "";
/*  32 */   private String imgname = "";
/*     */   
/*  34 */   private String requestid = "";
/*  35 */   private String requestname = "";
/*  36 */   private String requestaffixid = "";
/*     */   public String getRegisteraddress() {
/*  38 */     return this.registeraddress;
/*     */   }
/*     */   public void setRegisteraddress(String paramString) {
/*  41 */     this.registeraddress = paramString;
/*     */   }
/*     */   public String getCorporation() {
/*  44 */     return this.corporation;
/*     */   }
/*     */   public void setCorporation(String paramString) {
/*  47 */     this.corporation = paramString;
/*     */   }
/*     */   public String getRecordnum() {
/*  50 */     return this.recordnum;
/*     */   }
/*     */   public void setRecordnum(String paramString) {
/*  53 */     this.recordnum = paramString;
/*     */   }
/*     */   public String getUsefulbegindate() {
/*  56 */     return this.usefulbegindate;
/*     */   }
/*     */   public void setUsefulbegindate(String paramString) {
/*  59 */     this.usefulbegindate = paramString;
/*     */   }
/*     */   public String getUsefulenddate() {
/*  62 */     return this.usefulenddate;
/*     */   }
/*     */   public void setUsefulenddate(String paramString) {
/*  65 */     this.usefulenddate = paramString;
/*     */   }
/*     */   public String getUsefulyear() {
/*  68 */     return this.usefulyear;
/*     */   }
/*     */   public void setUsefulyear(String paramString) {
/*  71 */     this.usefulyear = paramString;
/*     */   }
/*     */   public String getDateinssue() {
/*  74 */     return this.dateinssue;
/*     */   }
/*     */   public void setDateinssue(String paramString) {
/*  77 */     this.dateinssue = paramString;
/*     */   }
/*     */   public String getLicensestatu() {
/*  80 */     return this.licensestatu;
/*     */   }
/*     */   public void setLicensestatu(String paramString) {
/*  83 */     this.licensestatu = paramString;
/*     */   }
/*     */   public String getAnnualinspection() {
/*  86 */     return this.annualinspection;
/*     */   }
/*     */   public void setAnnualinspection(String paramString) {
/*  89 */     this.annualinspection = paramString;
/*     */   }
/*     */   public String getDepartinssue() {
/*  92 */     return this.departinssue;
/*     */   }
/*     */   public void setDepartinssue(String paramString) {
/*  95 */     this.departinssue = paramString;
/*     */   }
/*     */   public String getScopebusiness() {
/*  98 */     return this.scopebusiness;
/*     */   }
/*     */   public void setScopebusiness(String paramString) {
/* 101 */     this.scopebusiness = paramString;
/*     */   }
/*     */   public String getRegistercapital() {
/* 104 */     return this.registercapital;
/*     */   }
/*     */   public void setRegistercapital(String paramString) {
/* 107 */     this.registercapital = paramString;
/*     */   }
/*     */   public String getPaiclupcapital() {
/* 110 */     return this.paiclupcapital;
/*     */   }
/*     */   public void setPaiclupcapital(String paramString) {
/* 113 */     this.paiclupcapital = paramString;
/*     */   }
/*     */   public String getCurrencyid() {
/* 116 */     return this.currencyid;
/*     */   }
/*     */   public void setCurrencyid(String paramString) {
/* 119 */     this.currencyid = paramString;
/*     */   }
/*     */   public String getCorporatdelegate() {
/* 122 */     return this.corporatdelegate;
/*     */   }
/*     */   public void setCorporatdelegate(String paramString) {
/* 125 */     this.corporatdelegate = paramString;
/*     */   }
/*     */   public String getLicenseregistnum() {
/* 128 */     return this.licenseregistnum;
/*     */   }
/*     */   public void setLicenseregistnum(String paramString) {
/* 131 */     this.licenseregistnum = paramString;
/*     */   }
/*     */   public String getMemo() {
/* 134 */     return this.memo;
/*     */   }
/*     */   public void setMemo(String paramString) {
/* 137 */     this.memo = paramString;
/*     */   }
/*     */   public String getCompanytype() {
/* 140 */     return this.companytype;
/*     */   }
/*     */   public void setCompanytype(String paramString) {
/* 143 */     this.companytype = paramString;
/*     */   }
/*     */   public String getLicensename() {
/* 146 */     return this.licensename;
/*     */   }
/*     */   public void setLicensename(String paramString) {
/* 149 */     this.licensename = paramString;
/*     */   }
/*     */   public String getLicensetype() {
/* 152 */     return this.licensetype;
/*     */   }
/*     */   public void setLicensetype(String paramString) {
/* 155 */     this.licensetype = paramString;
/*     */   }
/*     */   public int getLicenseaffixid() {
/* 158 */     return this.licenseaffixid;
/*     */   }
/*     */   public void setLicenseaffixid(int paramInt) {
/* 161 */     this.licenseaffixid = paramInt;
/*     */   }
/*     */   public String getAffixdoc() {
/* 164 */     return this.affixdoc;
/*     */   }
/*     */   public void setAffixdoc(String paramString) {
/* 167 */     this.affixdoc = paramString;
/*     */   }
/*     */   public String getImgid() {
/* 170 */     return this.imgid;
/*     */   }
/*     */   public void setImgid(String paramString) {
/* 173 */     this.imgid = paramString;
/*     */   }
/*     */   public String getImgname() {
/* 176 */     return this.imgname;
/*     */   }
/*     */   public void setImgname(String paramString) {
/* 179 */     this.imgname = paramString;
/*     */   }
/*     */   public String getRequestid() {
/* 182 */     return this.requestid;
/*     */   }
/*     */   public void setRequestid(String paramString) {
/* 185 */     this.requestid = paramString;
/*     */   }
/*     */   public String getRequestname() {
/* 188 */     return this.requestname;
/*     */   }
/*     */   public void setRequestname(String paramString) {
/* 191 */     this.requestname = paramString;
/*     */   }
/*     */   public String getRequestaffixid() {
/* 194 */     return this.requestaffixid;
/*     */   }
/*     */   public void setRequestaffixid(String paramString) {
/* 197 */     this.requestaffixid = paramString;
/*     */   }
/*     */   public String getCurrencyname() {
/* 200 */     return this.currencyname;
/*     */   }
/*     */   public void setCurrencyname(String paramString) {
/* 203 */     this.currencyname = paramString;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpcompanyinfo/CpLicense.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */