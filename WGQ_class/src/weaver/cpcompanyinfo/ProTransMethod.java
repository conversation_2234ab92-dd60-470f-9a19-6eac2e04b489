/*     */ package weaver.cpcompanyinfo;
/*     */ 
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ProTransMethod
/*     */ {
/*     */   public String getIsShowBoxtwo(String paramString) {
/*  39 */     return "true";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getPr_name(String paramString1, String paramString2) {
/*  46 */     String str = "";
/*  47 */     if (paramString2.length() > 7) {
/*     */       
/*  49 */       str = "<a onclick='openpro(" + paramString1 + ")' title='" + paramString2 + "' style=\"text-decoration:none;cursor: pointer;\">" + paramString2.substring(0, 7) + "..</a>";
/*     */     } else {
/*     */       
/*  52 */       str = "<a onclick='openpro(" + paramString1 + ")' title='" + paramString2 + "' style=\"text-decoration:none;cursor: pointer;\" >" + paramString2 + "</a>";
/*     */     } 
/*  54 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getPr_libper(String paramString) {
/*     */     try {
/*  64 */       ResourceComInfo resourceComInfo = new ResourceComInfo();
/*  65 */       return resourceComInfo.getLastname(paramString);
/*  66 */     } catch (Exception exception) {
/*     */       
/*  68 */       exception.printStackTrace();
/*  69 */       return "";
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public String getPr_status(String paramString) {
/*  76 */     String str1 = "";
/*  77 */     String str2 = "select * from pro_status where ps_id='" + paramString + "'";
/*  78 */     RecordSet recordSet = new RecordSet();
/*  79 */     recordSet.execute(str2);
/*  80 */     if (recordSet.next())
/*     */     {
/*  82 */       str1 = recordSet.getString("ps_name");
/*     */     }
/*  84 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getPt_name(String paramString1, String paramString2) {
/*  92 */     return "<a  href='javascript:openpro(" + paramString2 + ")' title=" + paramString1 + " style=\"text-decoration:none;cursor: pointer;\">" + paramString1 + "</a>";
/*     */   }
/*     */ 
/*     */   
/*     */   public String getStage_mb(String paramString1, String paramString2) {
/*  97 */     String str1 = "";
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 102 */     String str2 = "select pst_status,pst_sdata,pst_edata from pro_single_stage where pst_prid='" + paramString1 + "' and pst_id='" + paramString2 + "' ";
/* 103 */     RecordSet recordSet = new RecordSet();
/* 104 */     if (recordSet.execute(str2) && recordSet.next())
/*     */     {
/* 106 */       str1 = recordSet.getString("pst_status");
/*     */     }
/* 108 */     if ("1".equals(str1))
/*     */     {
/* 110 */       return "<img src='/newworktask/request/images/ModeO_wev8.gif' title='" + SystemEnv.getHtmlLabelName(1979, ThreadVarLanguage.getLang()) + "[" + SystemEnv.getHtmlLabelName(24978, ThreadVarLanguage.getLang()) + ":" + recordSet.getString("pst_sdata") + "--" + SystemEnv.getHtmlLabelName(1323, ThreadVarLanguage.getLang()) + ":" + recordSet.getString("pst_edata") + "]'/>"; } 
/* 111 */     if ("2".equals(str1))
/*     */     {
/* 113 */       return "<img src='/newworktask/request/images/ModeB_wev8.gif' title='" + SystemEnv.getHtmlLabelName(1960, ThreadVarLanguage.getLang()) + "[" + SystemEnv.getHtmlLabelName(24978, ThreadVarLanguage.getLang()) + ":" + recordSet.getString("pst_sdata") + "--" + SystemEnv.getHtmlLabelName(1323, ThreadVarLanguage.getLang()) + ":" + recordSet.getString("pst_edata") + "]'/>"; } 
/* 114 */     if ("3".equals(str1))
/*     */     {
/* 116 */       return "<img src='/newworktask/request/images/ModeR_wev8.gif' title='" + SystemEnv.getHtmlLabelName(1961, ThreadVarLanguage.getLang()) + "[" + SystemEnv.getHtmlLabelName(24978, ThreadVarLanguage.getLang()) + ":" + recordSet.getString("pst_sdata") + "--" + SystemEnv.getHtmlLabelName(1323, ThreadVarLanguage.getLang()) + ":" + recordSet.getString("pst_edata") + "]'/>";
/*     */     }
/*     */     
/* 119 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getPt_isuse(String paramString) {
/* 126 */     if ("1".equals(paramString))
/*     */     {
/* 128 */       return "" + SystemEnv.getHtmlLabelName(31675, ThreadVarLanguage.getLang()) + "";
/*     */     }
/* 130 */     return "" + SystemEnv.getHtmlLabelName(26472, ThreadVarLanguage.getLang()) + "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getPt_grow(String paramString) {
/* 137 */     String str = "";
/* 138 */     int i = Util.getIntValue(paramString, 0);
/* 139 */     if (i < 10) {
/*     */       
/* 141 */       str = "00" + i;
/* 142 */     } else if (i >= 10 && i < 100) {
/*     */       
/* 144 */       str = "0" + i;
/*     */     } 
/*     */     
/* 147 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public String getpt_issys(String paramString) {
/* 153 */     if ("1".equals(paramString))
/*     */     {
/* 155 */       return "" + SystemEnv.getHtmlLabelName(19843, ThreadVarLanguage.getLang()) + "";
/*     */     }
/* 157 */     return "" + SystemEnv.getHtmlLabelName(163, ThreadVarLanguage.getLang()) + "";
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public String getPlog_desc(String paramString1, String paramString2) {
/* 163 */     if ("1".equals(paramString1))
/*     */     {
/* 165 */       return "" + SystemEnv.getHtmlLabelName(125, Util.getIntValue(paramString2)); } 
/* 166 */     if ("2".equals(paramString1))
/*     */     {
/* 168 */       return "" + SystemEnv.getHtmlLabelName(103, Util.getIntValue(paramString2)); } 
/* 169 */     if ("3".equals(paramString1))
/*     */     {
/* 171 */       return "" + SystemEnv.getHtmlLabelName(91, Util.getIntValue(paramString2));
/*     */     }
/*     */     
/* 174 */     return "" + SystemEnv.getHtmlLabelName(367, Util.getIntValue(paramString2));
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public String getPlog_person(String paramString) {
/* 180 */     String str = "";
/*     */     try {
/* 182 */       ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 183 */       str = resourceComInfo.getLastname(paramString);
/* 184 */     } catch (Exception exception) {
/* 185 */       exception.printStackTrace();
/*     */     } 
/*     */ 
/*     */     
/* 189 */     return str;
/*     */   }
/*     */   
/*     */   public static String getDefaultCurrency() {
/* 193 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 195 */     String str = "";
/* 196 */     recordSet.execute("select id,currencyname,currencydesc from FnaCurrency where isdefault=1");
/* 197 */     if (recordSet.next()) {
/* 198 */       String str1 = Util.null2String(recordSet.getString("id"));
/* 199 */       String str2 = Util.null2String(recordSet.getString("currencyname"));
/* 200 */       String str3 = Util.null2String(recordSet.getString("currencydesc"));
/* 201 */       str = str + str1 + "," + str2 + "," + str3;
/*     */     } 
/* 203 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpcompanyinfo/ProTransMethod.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */