/*      */ package weaver.cpcompanyinfo;
/*      */ 
/*      */ import java.sql.Date;
/*      */ import java.text.ParseException;
/*      */ import java.text.SimpleDateFormat;
/*      */ import java.util.ArrayList;
/*      */ import java.util.Arrays;
/*      */ import java.util.Calendar;
/*      */ import java.util.Date;
/*      */ import java.util.Enumeration;
/*      */ import java.util.HashMap;
/*      */ import java.util.Iterator;
/*      */ import java.util.List;
/*      */ import java.util.Map;
/*      */ import java.util.Properties;
/*      */ import javax.servlet.http.HttpServletRequest;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.general.ThreadVarLanguage;
/*      */ import weaver.general.Util;
/*      */ import weaver.systeminfo.SystemEnv;
/*      */ 
/*      */ public class JspUtil {
/*   23 */   private static int temp = 1;
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public synchronized int getNumber() {
/*   29 */     if (temp > 10000)
/*      */     {
/*   31 */       temp = 1;
/*      */     }
/*   33 */     return temp++;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean insert(String paramString1, String paramString2, Properties paramProperties) throws Exception {
/*   43 */     String str = "";
/*   44 */     boolean bool = false;
/*   45 */     if (paramProperties == null) return bool; 
/*   46 */     str = "insert into " + paramString1 + " (" + paramString2 + ") values(" + getSqlValueStringForInsert(paramString2, paramProperties) + ")";
/*   47 */     bool = executeSql(str);
/*   48 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean delete(String paramString1, String paramString2, String paramString3) throws Exception {
/*   58 */     String str = "";
/*   59 */     boolean bool = false;
/*   60 */     if (isempty(paramString1) || isempty(paramString2) || isempty(paramString3)) return bool; 
/*   61 */     str = "delete " + paramString1 + " where " + paramString2 + "='" + paramString3 + "'";
/*   62 */     bool = executeSql(str);
/*   63 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean delete(String paramString1, String paramString2) throws Exception {
/*   73 */     String str = "";
/*   74 */     boolean bool = false;
/*   75 */     if (isempty(paramString1) || isempty(paramString2)) return bool; 
/*   76 */     str = "delete " + paramString1 + " where id='" + paramString2 + "'";
/*   77 */     bool = executeSql(str);
/*   78 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean update(String paramString1, String paramString2, String paramString3, Properties paramProperties) throws Exception {
/*   90 */     String str = "";
/*   91 */     boolean bool = false;
/*   92 */     if (paramProperties == null) return bool; 
/*   93 */     str = "update " + paramString1 + " set " + getSqlValueStringForUpdate(paramString2, paramProperties) + " where id='" + paramString3 + "'";
/*   94 */     bool = executeSql(str);
/*   95 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean update(String paramString1, String paramString2, String paramString3, String paramString4, Properties paramProperties) throws Exception {
/*  107 */     String str = "";
/*  108 */     boolean bool = false;
/*  109 */     if (paramProperties == null) return bool; 
/*  110 */     str = "update " + paramString1 + " set " + getSqlValueStringForUpdate(paramString2, paramProperties) + " where " + paramString3 + "='" + paramString4 + "'";
/*  111 */     bool = executeSql(str);
/*  112 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List query(String paramString) throws Exception {
/*  120 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/*  121 */     HashMap<Object, Object> hashMap = null;
/*  122 */     int i = 0;
/*  123 */     byte b = 0;
/*  124 */     String str1 = null;
/*  125 */     String str2 = null;
/*  126 */     RecordSet recordSet = new RecordSet();
/*      */     
/*      */     try {
/*  129 */       recordSet.executeSql(paramString);
/*  130 */       i = recordSet.getColCounts();
/*  131 */       String[] arrayOfString = recordSet.getColumnName();
/*  132 */       for (b = 0; b < i; b++) {
/*  133 */         arrayOfString[b] = recordSet.getColumnName(b + 1).toLowerCase();
/*      */       }
/*  135 */       while (recordSet.next()) {
/*      */         
/*  137 */         hashMap = new HashMap<>();
/*  138 */         for (b = 0; b < i; b++) {
/*      */           
/*  140 */           str1 = arrayOfString[b];
/*  141 */           str2 = recordSet.getString(b + 1);
/*  142 */           if (str2 == null)
/*  143 */             str2 = ""; 
/*  144 */           hashMap.put(str1, str2);
/*      */         } 
/*  146 */         arrayList.add(hashMap);
/*      */       } 
/*  148 */     } catch (Exception exception) {
/*  149 */       throw new Exception(exception + ",sql=" + paramString);
/*      */     } finally {
/*  151 */       recordSet = null;
/*      */     } 
/*      */     
/*  154 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Map query(String paramString, HttpServletRequest paramHttpServletRequest) throws Exception {
/*  163 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*      */     
/*  165 */     if ("".equals(paramString)) return hashMap;
/*      */     
/*  167 */     String str1 = "select 1 from ";
/*  168 */     if (paramString.indexOf("order") > -1) {
/*  169 */       str1 = str1 + paramString.substring(paramString.indexOf("from") + 4, paramString.indexOf("order") - 1);
/*      */     } else {
/*  171 */       str1 = str1 + paramString.substring(paramString.indexOf("from") + 4, paramString.length());
/*      */     } 
/*      */     
/*  174 */     RecordSet recordSet = new RecordSet();
/*      */     
/*  176 */     recordSet.executeSql(str1);
/*      */     
/*  178 */     int i = recordSet.getCounts();
/*  179 */     int j = getIntValue(null2String(paramHttpServletRequest.getParameter("currentpage")), 1);
/*  180 */     int k = getIntValue(null2String(paramHttpServletRequest.getParameter("pagesize")), 10);
/*  181 */     int m = i / k + ((i % k > 0) ? 1 : 0);
/*  182 */     if (m < j) j = m; 
/*  183 */     int n = j * k;
/*  184 */     int i1 = k;
/*  185 */     if (i - n + k < k) i1 = i - n + k; 
/*  186 */     if (i < k) i1 = i;
/*      */     
/*  188 */     paramString = "select t1.*,rownum rn from (" + paramString + ") t1 where rownum <= " + n;
/*  189 */     paramString = "select t2.* from (" + paramString + ") t2 where rn > " + (n - k);
/*      */     
/*  191 */     List list = query(paramString);
/*      */     
/*  193 */     String str2 = "";
/*  194 */     str2 = getPageBar(i, m, j, k);
/*      */     
/*  196 */     hashMap.put("data", list);
/*  197 */     hashMap.put("bar", str2);
/*      */     
/*  199 */     return hashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getPageBar(int paramInt1, int paramInt2, int paramInt3, int paramInt4) throws Exception {
/*  207 */     StringBuffer stringBuffer = new StringBuffer();
/*      */     
/*  209 */     stringBuffer.append("&nbsp;&nbsp;" + SystemEnv.getHtmlLabelName(18609, ThreadVarLanguage.getLang()) + "" + paramInt1 + "" + SystemEnv.getHtmlLabelName(24683, ThreadVarLanguage.getLang()) + "," + paramInt2 + "" + SystemEnv.getHtmlLabelName(30642, ThreadVarLanguage.getLang()) + "&nbsp;&nbsp;");
/*  210 */     stringBuffer.append(getPageUrl(paramInt3, paramInt2));
/*  211 */     stringBuffer.append("" + SystemEnv.getHtmlLabelName(33066, ThreadVarLanguage.getLang()) + "<select name=currentpage onchange=frmmain.submit()>");
/*  212 */     stringBuffer.append(getOption(paramInt2, paramInt3) + "</select>" + SystemEnv.getHtmlLabelName(30642, ThreadVarLanguage.getLang()) + "");
/*  213 */     stringBuffer.append("" + SystemEnv.getHtmlLabelName(265, ThreadVarLanguage.getLang()) + "<select name='pagesize' onchange=frmmain.submit()>");
/*  214 */     stringBuffer.append(getOption("10,30,50,100,500", "10,30,50,100,500", paramInt4 + ""));
/*  215 */     stringBuffer.append("</select>" + SystemEnv.getHtmlLabelName(18256, ThreadVarLanguage.getLang()) + "");
/*      */     
/*  217 */     return stringBuffer + "";
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getPageUrl(int paramInt1, int paramInt2) throws Exception {
/*  225 */     String str = "";
/*  226 */     int i = paramInt1 - 1;
/*  227 */     int j = paramInt1 + 1;
/*      */     
/*  229 */     if (paramInt1 < 1 || (paramInt1 == 1 && paramInt1 == paramInt2)) {
/*  230 */       str = "<a>" + SystemEnv.getHtmlLabelName(18363, ThreadVarLanguage.getLang()) + "</a> | <a>" + SystemEnv.getHtmlLabelName(1258, ThreadVarLanguage.getLang()) + "</a> | <a>" + SystemEnv.getHtmlLabelName(1259, ThreadVarLanguage.getLang()) + "</a> | <a>" + SystemEnv.getHtmlLabelName(18362, ThreadVarLanguage.getLang()) + "</a>\n";
/*      */     }
/*  232 */     if (paramInt1 == 1 && paramInt1 < paramInt2)
/*      */     {
/*      */       
/*  235 */       str = "<a>" + SystemEnv.getHtmlLabelName(18363, ThreadVarLanguage.getLang()) + "</a> | <a>" + SystemEnv.getHtmlLabelName(1258, ThreadVarLanguage.getLang()) + "</a> | <a href=javascript:f_go_page(" + j + ")>" + SystemEnv.getHtmlLabelName(1259, ThreadVarLanguage.getLang()) + "</a> | <a href=javascript:f_go_page(" + paramInt2 + ")>" + SystemEnv.getHtmlLabelName(18362, ThreadVarLanguage.getLang()) + "</a>\n";
/*      */     }
/*      */     
/*  238 */     if (paramInt1 > 1 && paramInt1 == paramInt2)
/*      */     {
/*  240 */       str = "<a href=javascript:f_go_page(1)>" + SystemEnv.getHtmlLabelName(18363, ThreadVarLanguage.getLang()) + "</a> | <a href=javascript:f_go_page(" + i + ")>" + SystemEnv.getHtmlLabelName(1258, ThreadVarLanguage.getLang()) + "</a> | <a>" + SystemEnv.getHtmlLabelName(1259, ThreadVarLanguage.getLang()) + "</a> | <a>" + SystemEnv.getHtmlLabelName(18362, ThreadVarLanguage.getLang()) + "</a>\n";
/*      */     }
/*  242 */     if (paramInt1 > 1 && paramInt1 < paramInt2)
/*      */     {
/*      */ 
/*      */       
/*  246 */       str = "<a href=javascript:f_go_page(1)>" + SystemEnv.getHtmlLabelName(18363, ThreadVarLanguage.getLang()) + "</a> | <a href=javascript:f_go_page(" + i + ")>" + SystemEnv.getHtmlLabelName(1258, ThreadVarLanguage.getLang()) + "</a> | <a href=javascript:f_go_page(" + j + ")>" + SystemEnv.getHtmlLabelName(1259, ThreadVarLanguage.getLang()) + "</a> | <a href=javascript:f_go_page(" + paramInt2 + ")>" + SystemEnv.getHtmlLabelName(18362, ThreadVarLanguage.getLang()) + "</a>\n";
/*      */     }
/*  248 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public String getHiddenHtml(String paramString, HttpServletRequest paramHttpServletRequest) throws Exception {
/*  254 */     Properties properties = getReqProp(paramHttpServletRequest);
/*  255 */     return getHiddenHtml(paramString, properties);
/*      */   }
/*      */   
/*      */   public String getHiddenHtml(String paramString, Properties paramProperties) throws Exception {
/*  259 */     if (paramString == null) return ""; 
/*  260 */     paramString = paramString.trim();
/*  261 */     if (paramString.length() < 1) return "";
/*      */     
/*  263 */     String[] arrayOfString = paramString.split(",");
/*  264 */     byte b = 0;
/*  265 */     int i = arrayOfString.length;
/*  266 */     String str1 = null;
/*  267 */     String str2 = null;
/*  268 */     String str3 = "";
/*  269 */     for (b = 0; b < i; b++) {
/*  270 */       str2 = arrayOfString[b];
/*  271 */       str1 = paramProperties.getProperty(str2);
/*  272 */       if (str1 == null) str1 = ""; 
/*  273 */       str1 = encodeHtml(str1);
/*  274 */       str3 = str3 + "<input type=hidden name=\"" + str2 + "\" value=\"" + str1 + "\">\n";
/*      */     } 
/*  276 */     return str3;
/*      */   }
/*      */   
/*      */   public String encodeHtml(String paramString) {
/*  280 */     if (paramString == null) return null; 
/*  281 */     paramString = paramString.replaceAll("<", "&lt;");
/*  282 */     paramString = paramString.replaceAll(">", "&gt;");
/*  283 */     paramString = paramString.replaceAll("\"", "&quot;");
/*  284 */     return paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Map queryProKNode(String paramString) throws Exception {
/*  292 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  293 */     HashMap<Object, Object> hashMap2 = null;
/*  294 */     boolean bool1 = false;
/*  295 */     boolean bool2 = false;
/*  296 */     Object object1 = null;
/*  297 */     Object object2 = null;
/*  298 */     RecordSet recordSet = new RecordSet();
/*      */     
/*      */     try {
/*  301 */       recordSet.executeSql(paramString);
/*  302 */       while (recordSet.next()) {
/*      */         
/*  304 */         hashMap2 = new HashMap<>();
/*  305 */         hashMap2.put("id", recordSet.getString("id"));
/*  306 */         hashMap2.put("proid", recordSet.getString("proid"));
/*  307 */         hashMap2.put("keynid", recordSet.getString("keynid"));
/*  308 */         hashMap2.put("affectoccu", recordSet.getString("affectoccu"));
/*  309 */         hashMap2.put("completestatus", recordSet.getString("completestatus"));
/*  310 */         hashMap2.put("planvetime", recordSet.getString("planvetime"));
/*  311 */         hashMap2.put("realcomptime", recordSet.getString("realcomptime"));
/*      */         
/*  313 */         hashMap1.put(recordSet.getString("keynid") + "-" + recordSet.getString("proid"), hashMap2);
/*      */       } 
/*  315 */     } catch (Exception exception) {
/*  316 */       throw new Exception(exception + ",sql=" + paramString);
/*      */     } finally {
/*  318 */       recordSet = null;
/*      */     } 
/*      */     
/*  321 */     return hashMap1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Map queryProTNode(String paramString) throws Exception {
/*  330 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  331 */     ArrayList<HashMap<Object, Object>> arrayList = null;
/*  332 */     HashMap<Object, Object> hashMap2 = null;
/*  333 */     boolean bool1 = false;
/*  334 */     boolean bool2 = false;
/*  335 */     Object object = null;
/*  336 */     RecordSet recordSet = new RecordSet();
/*  337 */     String str = "";
/*      */     
/*      */     try {
/*  340 */       recordSet.executeSql(paramString);
/*  341 */       while (recordSet.next()) {
/*      */         
/*  343 */         hashMap2 = new HashMap<>();
/*  344 */         str = recordSet.getString("proid");
/*  345 */         hashMap2.put("proid", str);
/*  346 */         hashMap2.put("keynid", recordSet.getString("keynid"));
/*  347 */         hashMap2.put("keyname", recordSet.getString("keyname"));
/*  348 */         hashMap2.put("completestatus", recordSet.getString("completestatus"));
/*  349 */         hashMap2.put("planvetime", recordSet.getString("planvetime"));
/*  350 */         hashMap2.put("realcomptime", recordSet.getString("realcomptime"));
/*      */         
/*  352 */         if (hashMap1.containsKey(str)) {
/*  353 */           arrayList = (ArrayList)hashMap1.get(str);
/*  354 */           arrayList.add(hashMap2);
/*      */         } else {
/*  356 */           arrayList = new ArrayList<>();
/*  357 */           arrayList.add(hashMap2);
/*  358 */           hashMap1.put(str, arrayList);
/*      */         } 
/*  360 */         str = "";
/*      */       } 
/*  362 */     } catch (Exception exception) {
/*  363 */       throw new Exception(exception + ",sql=" + paramString);
/*      */     } finally {
/*  365 */       recordSet = null;
/*      */     } 
/*      */     
/*  368 */     return hashMap1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Map queryOne(String paramString) throws Exception {
/*  375 */     int i = 0;
/*      */     
/*  377 */     boolean bool = false;
/*  378 */     HashMap<Object, Object> hashMap = null;
/*  379 */     String str1 = null;
/*  380 */     String str2 = null;
/*  381 */     RecordSet recordSet = new RecordSet();
/*      */     try {
/*  383 */       recordSet.executeSql(paramString);
/*  384 */       i = recordSet.getColCounts();
/*  385 */       String[] arrayOfString = new String[i]; byte b;
/*  386 */       for (b = 0; b < i; b++) {
/*  387 */         arrayOfString[b] = recordSet.getColumnName(b + 1).toLowerCase();
/*      */       }
/*  389 */       bool = recordSet.next();
/*  390 */       if (bool) {
/*  391 */         recordSet.first();
/*  392 */         hashMap = new HashMap<>();
/*  393 */         for (b = 0; b < i; b++) {
/*      */           
/*  395 */           str2 = arrayOfString[b];
/*  396 */           str1 = recordSet.getString(str2);
/*  397 */           if (str1 == null)
/*  398 */             str1 = ""; 
/*  399 */           hashMap.put(str2, str1);
/*      */         } 
/*      */       } 
/*  402 */     } catch (Exception exception) {
/*  403 */       throw new Exception(exception + ",sql=" + paramString);
/*      */     } finally {
/*      */       
/*  406 */       recordSet = null;
/*      */     } 
/*      */     
/*  409 */     return hashMap;
/*      */   }
/*      */ 
/*      */   
/*      */   public String getSqlCond(String paramString) throws Exception {
/*  414 */     String[] arrayOfString = paramString.split(",");
/*  415 */     StringBuffer stringBuffer = new StringBuffer(100);
/*  416 */     stringBuffer.append("'-9999'");
/*  417 */     for (byte b = 0; b < arrayOfString.length; b++) {
/*  418 */       if (arrayOfString[b] != null && !"".equals(arrayOfString[b])) {
/*  419 */         stringBuffer.append(",'" + arrayOfString[b] + "'");
/*      */       }
/*      */     } 
/*  422 */     return stringBuffer.toString();
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean existRecorders(String paramString) throws Exception {
/*  428 */     boolean bool = false;
/*  429 */     RecordSet recordSet = new RecordSet();
/*      */     try {
/*  431 */       recordSet.executeSql(paramString);
/*  432 */       bool = recordSet.next();
/*  433 */     } catch (Exception exception) {
/*  434 */       throw new Exception(exception + ",sql=" + paramString);
/*      */     } finally {
/*  436 */       recordSet = null;
/*      */     } 
/*      */     
/*  439 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Map queryOne(String paramString1, String paramString2) throws Exception {
/*  446 */     return queryOne("select * from " + paramString1 + " where id='" + paramString2 + "'");
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getOption(String paramString1, String paramString2, String paramString3) throws Exception {
/*  457 */     if (paramString1 == null)
/*  458 */       paramString1 = ""; 
/*  459 */     if (paramString2 == null)
/*  460 */       paramString2 = ""; 
/*  461 */     paramString1 = paramString1.trim();
/*  462 */     paramString2 = paramString2.trim();
/*  463 */     if (paramString1.length() == 0 || paramString2.length() == 0)
/*  464 */       return ""; 
/*  465 */     String str1 = "";
/*  466 */     if (paramString3 == null)
/*  467 */       paramString3 = ""; 
/*  468 */     paramString3 = paramString3.trim();
/*  469 */     String str2 = null;
/*  470 */     String str3 = null;
/*  471 */     byte b = 0;
/*  472 */     int i = 0;
/*  473 */     String[] arrayOfString1 = paramString1.split(",");
/*  474 */     String[] arrayOfString2 = paramString2.split(",");
/*  475 */     i = arrayOfString1.length;
/*  476 */     if (arrayOfString2.length != i)
/*  477 */       throw new Exception("值与标题长度不一致"); 
/*  478 */     String str4 = null;
/*  479 */     for (b = 0; b < i; b++) {
/*  480 */       str2 = arrayOfString1[b];
/*  481 */       str3 = arrayOfString2[b];
/*  482 */       str2 = str2.trim();
/*  483 */       str3 = str3.trim();
/*  484 */       if (str2.equals(paramString3)) {
/*  485 */         str4 = " selected";
/*      */       } else {
/*  487 */         str4 = "";
/*  488 */       }  str1 = str1 + "<option value=\"" + str2 + "\"" + str4 + ">" + str3 + "</option>\n";
/*      */     } 
/*      */ 
/*      */     
/*  492 */     return str1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getOption(int paramInt1, int paramInt2) throws Exception {
/*  499 */     byte b = 0;
/*  500 */     String str1 = "";
/*  501 */     String str2 = "";
/*  502 */     for (b = 1; b <= paramInt1; b++) {
/*  503 */       if (b == paramInt2) {
/*  504 */         str1 = " selected";
/*      */       } else {
/*  506 */         str1 = "";
/*  507 */       }  str2 = str2 + "<option value=\"" + b + "\"" + str1 + ">" + b + "</option>\n";
/*      */     } 
/*      */ 
/*      */     
/*  511 */     return str2;
/*      */   }
/*      */   
/*      */   public String getOption(String paramString1, String paramString2) throws Exception {
/*      */     String str3;
/*  516 */     if (paramString2 == null)
/*  517 */       paramString2 = ""; 
/*  518 */     paramString2 = paramString2.trim();
/*  519 */     StringBuffer stringBuffer = new StringBuffer(100);
/*  520 */     String str1 = null;
/*  521 */     String str2 = null;
/*      */     
/*  523 */     RecordSet recordSet = new RecordSet();
/*      */     
/*      */     try {
/*  526 */       recordSet.executeSql(paramString1);
/*  527 */       stringBuffer.append("<option value=''>" + SystemEnv.getHtmlLabelName(82857, ThreadVarLanguage.getLang()) + "</option>");
/*  528 */       for (; recordSet.next(); stringBuffer.append(">").append(str2).append("</option>\n")) {
/*      */         
/*  530 */         str1 = recordSet.getString(1);
/*  531 */         str2 = recordSet.getString(2);
/*  532 */         if (str1 == null)
/*  533 */           str1 = ""; 
/*  534 */         if (str2 == null)
/*  535 */           str2 = ""; 
/*  536 */         str1 = str1.trim();
/*  537 */         str2 = str2.trim();
/*  538 */         stringBuffer.append("<option value=\"");
/*  539 */         stringBuffer.append(str1);
/*  540 */         stringBuffer.append("\"");
/*  541 */         if (paramString2.equals(str1)) {
/*  542 */           stringBuffer.append(" selected");
/*      */         }
/*      */       } 
/*  545 */       str3 = stringBuffer.toString();
/*      */     }
/*  547 */     catch (Exception exception) {
/*      */       
/*  549 */       throw new Exception(exception + "," + paramString1);
/*      */     } finally {
/*  551 */       recordSet = null;
/*      */     } 
/*      */     
/*  554 */     return str3;
/*      */   }
/*      */ 
/*      */   
/*      */   public boolean executeSql(String paramString) throws Exception {
/*  559 */     boolean bool = false;
/*  560 */     RecordSet recordSet = new RecordSet();
/*      */     try {
/*  562 */       bool = recordSet.executeSql(paramString);
/*  563 */     } catch (Exception exception) {
/*  564 */       throw new Exception(exception + ",sql=" + paramString);
/*      */     } finally {
/*  566 */       recordSet = null;
/*      */     } 
/*  568 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getSqlValueStringForInsert(String paramString, Properties paramProperties) throws Exception {
/*  579 */     String str1 = "";
/*  580 */     int i = 0;
/*      */     
/*  582 */     if (paramProperties == null) return ""; 
/*  583 */     String[] arrayOfString = paramString.split(",");
/*  584 */     i = arrayOfString.length;
/*  585 */     String str2 = null;
/*  586 */     for (byte b = 1; b <= i; b++) {
/*      */       
/*  588 */       str2 = paramProperties.getProperty(arrayOfString[b - 1]);
/*  589 */       if (isempty(str2)) str2 = ""; 
/*  590 */       if (isempty(str1)) {
/*  591 */         str1 = "'" + str2 + "'";
/*      */       } else {
/*  593 */         str1 = str1 + ",'" + str2 + "'";
/*      */       } 
/*      */     } 
/*      */ 
/*      */     
/*  598 */     return str1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getSqlValueStringForUpdate(String paramString, Properties paramProperties) throws Exception {
/*  607 */     String str1 = "";
/*  608 */     int i = 0;
/*      */     
/*  610 */     if (paramProperties == null) return ""; 
/*  611 */     String[] arrayOfString = paramString.split(",");
/*  612 */     i = arrayOfString.length;
/*  613 */     String str2 = null;
/*  614 */     for (byte b = 1; b <= i; b++) {
/*      */       
/*  616 */       str2 = paramProperties.getProperty(arrayOfString[b - 1]);
/*  617 */       if (isempty(str2)) str2 = ""; 
/*  618 */       if (isempty(str1)) {
/*  619 */         str1 = arrayOfString[b - 1] + "='" + str2 + "'";
/*      */       } else {
/*  621 */         str1 = str1 + "," + arrayOfString[b - 1] + "='" + str2 + "'";
/*      */       } 
/*      */     } 
/*      */ 
/*      */     
/*  626 */     return str1;
/*      */   }
/*      */ 
/*      */   
/*      */   public boolean isempty(String paramString) {
/*  631 */     int i = 0;
/*  632 */     boolean bool = false;
/*  633 */     i = isEmpty(paramString);
/*  634 */     if (i > 0) {
/*  635 */       bool = true;
/*      */     } else {
/*  637 */       bool = false;
/*  638 */     }  return bool;
/*      */   }
/*      */ 
/*      */   
/*      */   public int isEmpty(String paramString) {
/*  643 */     if (paramString == null)
/*  644 */       return 1; 
/*  645 */     paramString = paramString.trim();
/*  646 */     return (paramString.length() >= 1) ? 0 : 1;
/*      */   }
/*      */ 
/*      */   
/*      */   public Properties getReqProp(HttpServletRequest paramHttpServletRequest) throws Exception {
/*  651 */     Properties properties = null;
/*  652 */     String str = paramHttpServletRequest.getMethod();
/*  653 */     str = str.toLowerCase();
/*  654 */     byte b = 0;
/*  655 */     if (str.equals("get") || str.equals("post")) {
/*  656 */       properties = getReqProp(paramHttpServletRequest, 1);
/*  657 */       b = 1;
/*      */     } 
/*  659 */     if (b < 1) {
/*  660 */       throw new Exception("未知的HTTP请求方式,unknow http request method");
/*      */     }
/*  662 */     return properties;
/*      */   }
/*      */   
/*      */   public Properties getReqProp(HttpServletRequest paramHttpServletRequest, int paramInt) throws Exception {
/*  666 */     Properties properties = new Properties();
/*  667 */     List<String> list = getParameterNames(paramHttpServletRequest);
/*  668 */     byte b = 0;
/*  669 */     int i = 0;
/*  670 */     i = list.size();
/*  671 */     String str1 = null;
/*  672 */     String str2 = null;
/*  673 */     for (b = 0; b < i; b++) {
/*  674 */       str2 = list.get(b);
/*  675 */       str1 = paramHttpServletRequest.getParameter(str2);
/*  676 */       str1 = trim(str1);
/*  677 */       properties.setProperty(str2, str1);
/*      */     } 
/*      */     
/*  680 */     return properties;
/*      */   }
/*      */   
/*      */   public List getParameterNames(HttpServletRequest paramHttpServletRequest) {
/*  684 */     Enumeration enumeration = paramHttpServletRequest.getParameterNames();
/*  685 */     ArrayList arrayList = new ArrayList();
/*  686 */     for (; enumeration.hasMoreElements(); arrayList.add(enumeration.nextElement()));
/*  687 */     enumeration = null;
/*  688 */     return arrayList;
/*      */   }
/*      */   
/*      */   public String trim(String paramString) throws Exception {
/*  692 */     if (paramString == null) {
/*  693 */       return null;
/*      */     }
/*  695 */     return paramString.trim();
/*      */   }
/*      */   
/*      */   public String null2String(String paramString) {
/*  699 */     return (paramString == null || "null".equals(paramString)) ? "" : paramString;
/*      */   }
/*      */ 
/*      */   
/*      */   public String getString(String paramString1, String paramString2) {
/*  704 */     return (paramString1 != null && !"null".equals(paramString1) && !"".equals(paramString1)) ? paramString1 : paramString2;
/*      */   }
/*      */   
/*      */   public int getIntValue(String paramString, int paramInt) {
/*      */     try {
/*  709 */       return Integer.parseInt(paramString);
/*  710 */     } catch (Exception exception) {
/*  711 */       return paramInt;
/*      */     } 
/*      */   }
/*      */   
/*      */   public String getNowTime() {
/*  716 */     SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
/*  717 */     Date date = new Date();
/*  718 */     return simpleDateFormat.format(date);
/*      */   }
/*      */ 
/*      */   
/*      */   public String getDate() {
/*  723 */     SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
/*  724 */     Date date = new Date();
/*  725 */     return simpleDateFormat.format(date);
/*      */   }
/*      */ 
/*      */   
/*      */   public String getYearOption(String paramString) throws Exception {
/*  730 */     String str = "";
/*  731 */     int i = getYear(getNowDate() + "");
/*  732 */     StringBuffer stringBuffer = new StringBuffer(100);
/*  733 */     char c1 = 'ߐ';
/*  734 */     int j = i + 5;
/*  735 */     for (char c2 = c1; c2 <= j; c2++) {
/*  736 */       stringBuffer.append(c2 + ",");
/*      */     }
/*  738 */     str = stringBuffer.toString().substring(0, stringBuffer.toString().lastIndexOf(","));
/*  739 */     return getOption(str, str, paramString);
/*      */   }
/*      */   
/*      */   public String getYearOption2(String paramString) throws Exception {
/*  743 */     String str = "";
/*  744 */     int i = getYear(getNowDate() + "");
/*  745 */     StringBuffer stringBuffer = new StringBuffer(100);
/*  746 */     char c1 = 'ߐ';
/*  747 */     int j = i + 5;
/*  748 */     for (char c2 = c1; c2 <= j; c2++) {
/*  749 */       stringBuffer.append(c2 + ",");
/*      */     }
/*  751 */     str = stringBuffer.toString().substring(0, stringBuffer.toString().lastIndexOf(","));
/*  752 */     return getOption("," + str, "" + SystemEnv.getHtmlLabelName(82857, ThreadVarLanguage.getLang()) + "," + str, paramString);
/*      */   }
/*      */   
/*      */   public String getMonthOption(String paramString) throws Exception {
/*  756 */     String str1 = "";
/*  757 */     String str2 = "";
/*  758 */     int i = Util.getIntValue(paramString);
/*  759 */     StringBuffer stringBuffer1 = new StringBuffer(100);
/*  760 */     StringBuffer stringBuffer2 = new StringBuffer(100);
/*  761 */     stringBuffer1.append("" + SystemEnv.getHtmlLabelName(82857, ThreadVarLanguage.getLang()) + ",");
/*  762 */     stringBuffer2.append("0,");
/*  763 */     for (byte b = 1; b <= 12; b++) {
/*  764 */       stringBuffer1.append(b + ",");
/*  765 */       stringBuffer2.append(b + ",");
/*      */     } 
/*  767 */     str1 = stringBuffer1.toString().substring(0, stringBuffer1.toString().lastIndexOf(","));
/*  768 */     str2 = stringBuffer2.toString().substring(0, stringBuffer2.toString().lastIndexOf(","));
/*  769 */     return getOption(str2, str1, i + "");
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int getMonth(String paramString) {
/*  776 */     Calendar calendar = Calendar.getInstance();
/*  777 */     calendar.setTime(getDateTime(paramString));
/*  778 */     return calendar.get(2) + 1;
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public int getYear(String paramString) {
/*  784 */     Calendar calendar = Calendar.getInstance();
/*  785 */     calendar.setTime(getDateTime(paramString));
/*  786 */     return calendar.get(1);
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public Date getDateTime(String paramString) {
/*  792 */     return Date.valueOf(paramString);
/*      */   }
/*      */ 
/*      */   
/*      */   public Date getNowDate() {
/*  797 */     Date date = null;
/*  798 */     date = new Date(System.currentTimeMillis());
/*  799 */     return date;
/*      */   }
/*      */ 
/*      */   
/*      */   public String towDateXJ(String paramString1, String paramString2) throws ParseException {
/*  804 */     Date date1 = (new SimpleDateFormat("yyyy-MM-dd")).parse(paramString1);
/*  805 */     Date date2 = (new SimpleDateFormat("yyyy-MM-dd")).parse(paramString2);
/*      */     
/*  807 */     return ((date2.getTime() - date1.getTime()) / 86400000L > 0L) ? (((date2.getTime() - date1.getTime()) / 86400000L) + "") : ("" + ((date2.getTime() - date1.getTime()) / 86400000L));
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int towDateXJI(String paramString1, String paramString2) throws ParseException {
/*  814 */     Date date1 = (new SimpleDateFormat("yyyy-MM-dd")).parse(paramString1);
/*  815 */     Date date2 = (new SimpleDateFormat("yyyy-MM-dd")).parse(paramString2);
/*      */ 
/*      */ 
/*      */     
/*  819 */     return Integer.parseInt(((date1.getTime() - date2.getTime()) / 86400000L) + "");
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String subTime(String paramString, int paramInt1, int paramInt2) {
/*  829 */     if (paramString == null) return paramString; 
/*  830 */     if (paramInt2 < 1) return ""; 
/*  831 */     int i = paramString.length() - 1;
/*  832 */     if (paramInt1 > i) return ""; 
/*  833 */     int j = paramInt1 + paramInt2;
/*  834 */     if (j > i) return paramString.substring(paramInt1);
/*      */     
/*  836 */     return paramString.substring(paramInt1, j);
/*      */   }
/*      */ 
/*      */   
/*      */   public Properties map2Property(HashMap paramHashMap, Properties paramProperties) {
/*  841 */     Iterator<Object> iterator = paramHashMap.keySet().iterator();
/*  842 */     while (iterator.hasNext()) {
/*  843 */       Object object = iterator.next();
/*  844 */       Object object1 = paramHashMap.get(object);
/*  845 */       paramProperties.put(object, object1);
/*      */     } 
/*  847 */     return paramProperties;
/*      */   }
/*      */ 
/*      */   
/*      */   public String iso2gbk(String paramString) throws Exception {
/*  852 */     if (paramString == null)
/*      */     {
/*  854 */       return null;
/*      */     }
/*      */ 
/*      */     
/*  858 */     return paramString;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getProKeyNodeStatus(String paramString1, String paramString2, Object paramObject, Map<String, String> paramMap1, String paramString3, Map<String, String> paramMap2, int paramInt) {
/*  870 */     String str1 = "<img src='/strateOperat/img/O_36_wev8.png' title='" + SystemEnv.getHtmlLabelName(10003391, ThreadVarLanguage.getLang()) + "' onclick='doProknd(" + paramString1 + "," + paramString2 + ")' style='cursor: pointer;'>";
/*  871 */     String str2 = "";
/*  872 */     String str3 = "";
/*  873 */     String str4 = "";
/*  874 */     String str5 = "0";
/*  875 */     if (!"".equals(paramString3)) {
/*  876 */       str1 = "";
/*      */     }
/*  878 */     if (paramObject != null) {
/*  879 */       str2 = (new StringBuilder()).append(((Map)paramObject).get("completestatus")).append("").toString();
/*  880 */       str3 = (new StringBuilder()).append(((Map)paramObject).get("planvetime")).append("").toString();
/*  881 */       str4 = (new StringBuilder()).append(((Map)paramObject).get("realcomptime")).append("").toString();
/*  882 */       paramMap1.put(str2, (Util.getIntValue((new StringBuilder()).append(paramMap1.get(str2)).append("").toString()) + 1) + "");
/*  883 */       if ("".equals(paramString3)) {
/*  884 */         if (!"".equals(str2) && str2.equals("1")) {
/*  885 */           str1 = "<img src='/strateOperat/img/O_36_wev8.png' title='" + SystemEnv.getHtmlLabelName(10003391, ThreadVarLanguage.getLang()) + ";" + SystemEnv.getHtmlLabelName(17092, ThreadVarLanguage.getLang()) + ":" + str3 + "' onclick='doProknd(" + paramString1 + "," + paramString2 + ")' style='cursor: pointer;' >";
/*  886 */         } else if (!"".equals(str2) && str2.equals("2")) {
/*  887 */           str1 = "<img src='/strateOperat/img/G_37_wev8.gif' title='" + SystemEnv.getHtmlLabelName(10003392, ThreadVarLanguage.getLang()) + ";" + SystemEnv.getHtmlLabelName(17092, ThreadVarLanguage.getLang()) + ":" + str3 + "' onclick='doProknd(" + paramString1 + "," + paramString2 + ")' style='cursor: pointer;'>";
/*  888 */         } else if (!"".equals(str2) && str2.equals("3")) {
/*  889 */           str1 = "<img src='/strateOperat/img/G_38_wev8.gif' title='" + SystemEnv.getHtmlLabelName(10003393, ThreadVarLanguage.getLang()) + ";" + SystemEnv.getHtmlLabelName(17092, ThreadVarLanguage.getLang()) + ":" + str3 + "' onclick='doProknd(" + paramString1 + "," + paramString2 + ")' style='cursor: pointer;'>";
/*  890 */         } else if (!"".equals(str2) && str2.equals("4")) {
/*  891 */           str1 = "<img src='/strateOperat/img/O_39_wev8.png' title='" + SystemEnv.getHtmlLabelName(1961, ThreadVarLanguage.getLang()) + ";" + SystemEnv.getHtmlLabelName(1036, ThreadVarLanguage.getLang()) + ":" + str4 + "' onclick='doProknd(" + paramString1 + "," + paramString2 + ")' style='cursor: pointer;'>";
/*      */         }
/*      */       
/*  894 */       } else if (!"".equals(str2) && str2.equals("1") && str2.equals(paramString3)) {
/*  895 */         str1 = "<img src='/strateOperat/img/O_36_wev8.png' title='" + SystemEnv.getHtmlLabelName(10003391, ThreadVarLanguage.getLang()) + ";" + SystemEnv.getHtmlLabelName(17092, ThreadVarLanguage.getLang()) + ":" + str3 + "' onclick='doProknd(" + paramString1 + "," + paramString2 + ")' style='cursor: pointer;' >";
/*  896 */       } else if (!"".equals(str2) && str2.equals("2") && str2.equals(paramString3)) {
/*  897 */         str1 = "<img src='/strateOperat/img/G_37_wev8.gif' title='" + SystemEnv.getHtmlLabelName(10003392, ThreadVarLanguage.getLang()) + ";" + SystemEnv.getHtmlLabelName(17092, ThreadVarLanguage.getLang()) + ":" + str3 + "' onclick='doProknd(" + paramString1 + "," + paramString2 + ")' style='cursor: pointer;'>";
/*  898 */       } else if (!"".equals(str2) && str2.equals("3") && str2.equals(paramString3)) {
/*  899 */         str1 = "<img src='/strateOperat/img/G_38_wev8.gif' title='" + SystemEnv.getHtmlLabelName(10003393, ThreadVarLanguage.getLang()) + ";" + SystemEnv.getHtmlLabelName(17092, ThreadVarLanguage.getLang()) + ":" + str3 + "' onclick='doProknd(" + paramString1 + "," + paramString2 + ")' style='cursor: pointer;'>";
/*  900 */       } else if (!"".equals(str2) && str2.equals("4") && str2.equals(paramString3)) {
/*  901 */         str1 = "<img src='/strateOperat/img/O_39_wev8.png' title='" + SystemEnv.getHtmlLabelName(1961, ThreadVarLanguage.getLang()) + ";" + SystemEnv.getHtmlLabelName(1036, ThreadVarLanguage.getLang()) + ":" + str4 + "' onclick='doProknd(" + paramString1 + "," + paramString2 + ")' style='cursor: pointer;'>";
/*      */       } else {
/*  903 */         str1 = "";
/*      */       } 
/*      */       
/*  906 */       str5 = (new StringBuilder()).append(((Map)paramObject).get("affectoccu")).append("").toString();
/*  907 */       if (str5.equals("1") && "".equals(str4) && !"".equals(str1))
/*      */       {
/*  909 */         paramMap2.put("affectoccuFlag", (new StringBuilder()).append(paramMap2.get(paramMap2)).append("").append("1").toString());
/*      */       }
/*      */     } 
/*      */     
/*  913 */     if (paramInt == 12 && !"".equals(null2String((new StringBuilder()).append(paramMap2.get("affectoccuFlag")).append("").toString()))) {
/*  914 */       str1 = str1 + "<IMG src='/images/BacoError_wev8.gif' title='" + SystemEnv.getHtmlLabelName(10003394, ThreadVarLanguage.getLang()) + "' align='absMiddle' />";
/*      */     }
/*  916 */     return str1;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getProKeyNodeStatus(int paramInt1, int paramInt2, Object paramObject) {
/*  925 */     String str1 = "";
/*  926 */     String str2 = "";
/*  927 */     if (paramInt2 < 10) {
/*  928 */       str2 = "0" + paramInt2;
/*      */     } else {
/*  930 */       str2 = paramInt2 + "";
/*      */     } 
/*  932 */     String str3 = paramInt1 + "" + "-" + str2 + "";
/*  933 */     String str4 = "";
/*  934 */     String str5 = "";
/*  935 */     String str6 = "";
/*  936 */     String str7 = "";
/*  937 */     byte b = 0;
/*  938 */     Map map = null;
/*      */     
/*  940 */     if (paramObject != null) {
/*  941 */       int i = ((List)paramObject).size();
/*  942 */       for (b = 0; b < i; b++) {
/*  943 */         map = ((List<Map>)paramObject).get(b);
/*  944 */         str4 = (new StringBuilder()).append(map.get("completestatus")).append("").toString();
/*  945 */         str5 = (new StringBuilder()).append(map.get("planvetime")).append("").toString();
/*  946 */         str7 = (new StringBuilder()).append(map.get("realcomptime")).append("").toString();
/*  947 */         str6 = (new StringBuilder()).append(map.get("keynid")).append("").toString();
/*  948 */         if (str6.length() < 2) str6 = "0" + str6; 
/*  949 */         if (str3.equals(subTime(str5, 0, 7)) && "".equals(str7)) {
/*  950 */           str1 = str1 + "<span class='OHeaderNaN png' title='" + map.get("keyname") + "' onclick='doShowProKndTimeList(" + map.get("proid") + "," + str6 + ")'>" + str6 + "</span>" + subTime(str5, 8, 10) + "日<br>";
/*      */         }
/*  952 */         if (str4.equals("4") && 
/*  953 */           str3.equals(subTime(str7, 0, 7))) {
/*  954 */           str1 = str1 + "<span class='OHeaderNaN png' title='" + map.get("keyname") + "'  onclick='doShowProKndTimeList(" + map.get("proid") + "," + str6 + ")'>" + str6 + "</span>" + subTime(str7, 8, 10) + "日<br>";
/*      */         }
/*      */       } 
/*      */     } 
/*      */     
/*  959 */     return str1;
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public String getFlagUserLookFile(String paramString1, String paramString2) throws Exception {
/*  965 */     String str = "";
/*  966 */     if (!"".equals(paramString2) && paramString2.indexOf(paramString1) > -1) {
/*  967 */       str = "<img src='/images/BDNew_wev8.gif' id='img_" + paramString1 + "_new'/>";
/*      */     }
/*  969 */     return str;
/*      */   }
/*      */ 
/*      */   
/*      */   public static int getTemp() {
/*  974 */     return temp;
/*      */   }
/*      */   
/*      */   public static void setTemp(int paramInt) {
/*  978 */     temp = paramInt;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static String Strim(String paramString) {
/*  991 */     String str = "";
/*  992 */     String[] arrayOfString = paramString.split(","); int i;
/*  993 */     for (i = 0; i < arrayOfString.length; i++) {
/*  994 */       if (!"".equals(arrayOfString[i])) {
/*  995 */         str = str + arrayOfString[i] + ",";
/*      */       }
/*      */     } 
/*  998 */     i = str.length();
/*  999 */     byte b1 = 0;
/* 1000 */     byte b2 = 0;
/* 1001 */     char[] arrayOfChar = str.toCharArray();
/* 1002 */     while (b1 < i && arrayOfChar[b2 + b1] <= ',') {
/* 1003 */       b1++;
/*      */     }
/* 1005 */     while (b1 < i && arrayOfChar[b2 + i - 1] <= ',') {
/* 1006 */       i--;
/*      */     }
/* 1008 */     return (b1 > 0 || i < str.length()) ? str.substring(b1, i) : str;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static boolean CheckStr(String paramString1, String paramString2) {
/* 1022 */     boolean bool = false;
/* 1023 */     if (null == paramString1 && null == paramString2) {
/* 1024 */       bool = true;
/* 1025 */     } else if (!"".equals(paramString1) && !"".equals(paramString2)) {
/* 1026 */       String[] arrayOfString1 = paramString1.split(",");
/* 1027 */       String[] arrayOfString2 = paramString2.split(",");
/* 1028 */       Arrays.sort((Object[])arrayOfString1);
/* 1029 */       Arrays.sort((Object[])arrayOfString2);
/* 1030 */       bool = Arrays.equals((Object[])arrayOfString1, (Object[])arrayOfString2);
/* 1031 */     } else if ("".equals(paramString1) && "".equals(paramString2)) {
/* 1032 */       bool = true;
/*      */     } 
/* 1034 */     return bool;
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpcompanyinfo/JspUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */