/*    */ package weaver.cpcompanyinfo;
/*    */ 
/*    */ 
/*    */ 
/*    */ public class CpShareOfficers
/*    */ {
/*    */   private int shareofficerid;
/*    */   private int shareid;
/*    */   private String officername;
/*    */   private String isstop;
/*    */   private String aggregateinvest;
/*    */   private String aggregatedate;
/*    */   private String investment;
/*    */   
/*    */   public int getShareofficerid() {
/* 16 */     return this.shareofficerid;
/*    */   }
/*    */   public void setShareofficerid(int paramInt) {
/* 19 */     this.shareofficerid = paramInt;
/*    */   }
/*    */   public int getShareid() {
/* 22 */     return this.shareid;
/*    */   }
/*    */   public void setShareid(int paramInt) {
/* 25 */     this.shareid = paramInt;
/*    */   }
/*    */   public String getOfficername() {
/* 28 */     return this.officername;
/*    */   }
/*    */   public void setOfficername(String paramString) {
/* 31 */     this.officername = paramString;
/*    */   }
/*    */   public String getIsstop() {
/* 34 */     return this.isstop;
/*    */   }
/*    */   public void setIsstop(String paramString) {
/* 37 */     this.isstop = paramString;
/*    */   }
/*    */   public String getAggregateinvest() {
/* 40 */     return this.aggregateinvest;
/*    */   }
/*    */   public void setAggregateinvest(String paramString) {
/* 43 */     this.aggregateinvest = paramString;
/*    */   }
/*    */   public String getAggregatedate() {
/* 46 */     return this.aggregatedate;
/*    */   }
/*    */   public void setAggregatedate(String paramString) {
/* 49 */     this.aggregatedate = paramString;
/*    */   }
/*    */   public String getInvestment() {
/* 52 */     return this.investment;
/*    */   }
/*    */   public void setInvestment(String paramString) {
/* 55 */     this.investment = paramString;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpcompanyinfo/CpShareOfficers.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */