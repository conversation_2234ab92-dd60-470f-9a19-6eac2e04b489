/*     */ package weaver.cpcompanyinfo;
/*     */ 
/*     */ public class CpConstitution {
/*     */   private int constitutionid;
/*     */   private int companyid;
/*     */   private String aggregateinvest;
/*     */   private String theboard;
/*     */   private String stitubegindate;
/*     */   private String stituenddate;
/*     */   private String isvisitors;
/*     */   private String boardvisitors;
/*     */   private String visitbegindate;
/*     */   private String visitenddate;
/*     */   private String appointduetime;
/*     */   private String isreappoint;
/*     */   private String generalmanager;
/*     */   private String effectbegindate;
/*     */   private String effectenddate;
/*     */   private String constituaffix;
/*     */   private String createdatetime;
/*     */   private String updatetime;
/*     */   private int versionid;
/*     */   private String isdel;
/*     */   private String currencyid;
/*     */   private String currencyname;
/*     */   private String imgid;
/*     */   private String imgname;
/*     */   
/*     */   public String getCurrencyname() {
/*  30 */     return this.currencyname;
/*     */   }
/*     */   public void setCurrencyname(String paramString) {
/*  33 */     this.currencyname = paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public String getImgid() {
/*  39 */     return this.imgid;
/*     */   }
/*     */   public void setImgid(String paramString) {
/*  42 */     this.imgid = paramString;
/*     */   }
/*     */   public String getImgname() {
/*  45 */     return this.imgname;
/*     */   }
/*     */   public void setImgname(String paramString) {
/*  48 */     this.imgname = paramString;
/*     */   }
/*     */   public int getConstitutionid() {
/*  51 */     return this.constitutionid;
/*     */   }
/*     */   public void setConstitutionid(int paramInt) {
/*  54 */     this.constitutionid = paramInt;
/*     */   }
/*     */   
/*     */   public int getCompanyid() {
/*  58 */     return this.companyid;
/*     */   }
/*     */   public void setCompanyid(int paramInt) {
/*  61 */     this.companyid = paramInt;
/*     */   }
/*     */   public String getAggregateinvest() {
/*  64 */     return this.aggregateinvest;
/*     */   }
/*     */   public void setAggregateinvest(String paramString) {
/*  67 */     this.aggregateinvest = paramString;
/*     */   }
/*     */   public String getTheboard() {
/*  70 */     return this.theboard;
/*     */   }
/*     */   public void setTheboard(String paramString) {
/*  73 */     this.theboard = paramString;
/*     */   }
/*     */   public String getStitubegindate() {
/*  76 */     return this.stitubegindate;
/*     */   }
/*     */   public void setStitubegindate(String paramString) {
/*  79 */     this.stitubegindate = paramString;
/*     */   }
/*     */   public String getStituenddate() {
/*  82 */     return this.stituenddate;
/*     */   }
/*     */   public void setStituenddate(String paramString) {
/*  85 */     this.stituenddate = paramString;
/*     */   }
/*     */   public String getIsvisitors() {
/*  88 */     return this.isvisitors;
/*     */   }
/*     */   public void setIsvisitors(String paramString) {
/*  91 */     this.isvisitors = paramString;
/*     */   }
/*     */   public String getBoardvisitors() {
/*  94 */     return this.boardvisitors;
/*     */   }
/*     */   public void setBoardvisitors(String paramString) {
/*  97 */     this.boardvisitors = paramString;
/*     */   }
/*     */   public String getVisitbegindate() {
/* 100 */     return this.visitbegindate;
/*     */   }
/*     */   public void setVisitbegindate(String paramString) {
/* 103 */     this.visitbegindate = paramString;
/*     */   }
/*     */   public String getVisitenddate() {
/* 106 */     return this.visitenddate;
/*     */   }
/*     */   public void setVisitenddate(String paramString) {
/* 109 */     this.visitenddate = paramString;
/*     */   }
/*     */   public String getAppointduetime() {
/* 112 */     return this.appointduetime;
/*     */   }
/*     */   public void setAppointduetime(String paramString) {
/* 115 */     this.appointduetime = paramString;
/*     */   }
/*     */   public String getIsreappoint() {
/* 118 */     return this.isreappoint;
/*     */   }
/*     */   public void setIsreappoint(String paramString) {
/* 121 */     this.isreappoint = paramString;
/*     */   }
/*     */   public String getGeneralmanager() {
/* 124 */     return this.generalmanager;
/*     */   }
/*     */   public void setGeneralmanager(String paramString) {
/* 127 */     this.generalmanager = paramString;
/*     */   }
/*     */   public String getEffectbegindate() {
/* 130 */     return this.effectbegindate;
/*     */   }
/*     */   public void setEffectbegindate(String paramString) {
/* 133 */     this.effectbegindate = paramString;
/*     */   }
/*     */   public String getEffectenddate() {
/* 136 */     return this.effectenddate;
/*     */   }
/*     */   public void setEffectenddate(String paramString) {
/* 139 */     this.effectenddate = paramString;
/*     */   }
/*     */   public String getConstituaffix() {
/* 142 */     return this.constituaffix;
/*     */   }
/*     */   public void setConstituaffix(String paramString) {
/* 145 */     this.constituaffix = paramString;
/*     */   }
/*     */   public String getCreatedatetime() {
/* 148 */     return this.createdatetime;
/*     */   }
/*     */   public void setCreatedatetime(String paramString) {
/* 151 */     this.createdatetime = paramString;
/*     */   }
/*     */   public String getUpdatetime() {
/* 154 */     return this.updatetime;
/*     */   }
/*     */   public void setUpdatetime(String paramString) {
/* 157 */     this.updatetime = paramString;
/*     */   }
/*     */   public int getVersionid() {
/* 160 */     return this.versionid;
/*     */   }
/*     */   public void setVersionid(int paramInt) {
/* 163 */     this.versionid = paramInt;
/*     */   }
/*     */   public String getIsdel() {
/* 166 */     return this.isdel;
/*     */   }
/*     */   public void setIsdel(String paramString) {
/* 169 */     this.isdel = paramString;
/*     */   }
/*     */   public String getCurrencyid() {
/* 172 */     return this.currencyid;
/*     */   }
/*     */   public void setCurrencyid(String paramString) {
/* 175 */     this.currencyid = paramString;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpcompanyinfo/CpConstitution.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */