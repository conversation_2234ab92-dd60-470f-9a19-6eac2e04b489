/*      */ package weaver.cpcompanyinfo;
/*      */ 
/*      */ import java.text.ParseException;
/*      */ import java.text.SimpleDateFormat;
/*      */ import java.util.ArrayList;
/*      */ import java.util.Calendar;
/*      */ import java.util.Date;
/*      */ import java.util.GregorianCalendar;
/*      */ import java.util.List;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.general.BaseBean;
/*      */ import weaver.general.ThreadVarLanguage;
/*      */ import weaver.general.TimeUtil;
/*      */ import weaver.general.Util;
/*      */ import weaver.systeminfo.SystemEnv;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ public class CompanyInfoTransMethod
/*      */   extends BaseBean
/*      */ {
/*      */   public String getIsShowBox(String paramString) {
/*   33 */     return (!CheckStale(paramString) ? 1 : 0) + "";
/*      */   }
/*      */ 
/*      */   
/*      */   public String getOperating(String paramString1, String paramString2) {
/*   38 */     boolean bool = false;
/*   39 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/*   40 */     String str1 = "," + arrayOfString[0] + ",";
/*   41 */     String str2 = arrayOfString[1];
/*   42 */     String str3 = arrayOfString[2];
/*   43 */     if (",ALL,".equals(str1)) {
/*   44 */       bool = true;
/*   45 */     } else if (str1.lastIndexOf("," + paramString1 + ",") != -1) {
/*   46 */       bool = true;
/*      */     } 
/*      */     
/*   49 */     String str4 = "";
/*   50 */     str4 = str4 + "<a  title = \"" + SystemEnv.getHtmlLabelName(367, Util.getIntValue(str3)) + "\" style=\"text-decoration:none;cursor:pointer\"  onclick=getOperating(1," + paramString1 + ")>" + SystemEnv.getHtmlLabelName(367, Util.getIntValue(str3)) + "</a>&nbsp;";
/*   51 */     if (bool) {
/*   52 */       str4 = str4 + "<a  title = \"" + SystemEnv.getHtmlLabelName(26473, Util.getIntValue(str3)) + "\" style=\"text-decoration:none;cursor:pointer\"  onclick=getOperating(2," + paramString1 + ")>" + SystemEnv.getHtmlLabelName(26473, Util.getIntValue(str3)) + "</a>&nbsp;";
/*   53 */       if (!CheckStale(paramString1)) {
/*   54 */         str4 = str4 + "<a  title = \"" + SystemEnv.getHtmlLabelName(23777, Util.getIntValue(str3)) + "\" style=\"text-decoration:none;cursor:pointer\"  onclick=getOperating(3," + paramString1 + ")>" + SystemEnv.getHtmlLabelName(23777, Util.getIntValue(str3)) + "</a>&nbsp;";
/*      */       }
/*      */     } 
/*   57 */     if (!"HAVE".equals(str2)) {
/*   58 */       str4 = str4 + "<a  title = \"" + SystemEnv.getHtmlLabelName(83, Util.getIntValue(str3)) + "\" style=\"text-decoration:none;cursor:pointer\"  onclick=getOperating(4," + paramString1 + ")>" + SystemEnv.getHtmlLabelName(83, Util.getIntValue(str3)) + "</a>";
/*      */     }
/*   60 */     return str4;
/*      */   }
/*      */ 
/*      */   
/*      */   public ArrayList getOperating4E8(String paramString1, String paramString2) {
/*   65 */     boolean bool = false;
/*      */     
/*   67 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/*   68 */     String str1 = "," + arrayOfString[0] + ",";
/*   69 */     String str2 = arrayOfString[1];
/*   70 */     String str3 = arrayOfString[2];
/*   71 */     if (",ALL,".equals(str1)) {
/*   72 */       bool = true;
/*   73 */     } else if (str1.lastIndexOf("," + paramString1 + ",") != -1) {
/*   74 */       bool = true;
/*      */     } 
/*      */     
/*   77 */     ArrayList<String> arrayList = new ArrayList();
/*   78 */     arrayList.add("true");
/*   79 */     if (bool) {
/*   80 */       arrayList.add("true");
/*   81 */       if (!CheckStale(paramString1)) {
/*   82 */         arrayList.add("true");
/*      */       } else {
/*   84 */         arrayList.add("false");
/*      */       } 
/*      */     } else {
/*   87 */       arrayList.add("false");
/*   88 */       arrayList.add("false");
/*      */     } 
/*   90 */     if (!"HAVE".equals(str2)) {
/*   91 */       arrayList.add("true");
/*      */     } else {
/*   93 */       arrayList.add("false");
/*      */     } 
/*      */     
/*   96 */     return arrayList;
/*      */   }
/*      */ 
/*      */   
/*      */   public String getLicenseOperating(String paramString1, String paramString2) {
/*  101 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/*  102 */     String str1 = arrayOfString[0];
/*  103 */     String str2 = arrayOfString[1];
/*  104 */     String str3 = "";
/*  105 */     str3 = str3 + "<a  title = \"" + SystemEnv.getHtmlLabelName(367, Util.getIntValue(str2)) + "\" style=\"text-decoration:none;cursor:pointer\"  onclick=getOperating(1," + paramString1 + ")>" + SystemEnv.getHtmlLabelName(367, Util.getIntValue(str2)) + "</a>&nbsp;";
/*  106 */     if ("1".equals(str1)) {
/*  107 */       str3 = str3 + "<a  title = \"" + SystemEnv.getHtmlLabelName(26473, Util.getIntValue(str2)) + "\" style=\"text-decoration:none;cursor:pointer\"  onclick=getOperating(2," + paramString1 + ")>" + SystemEnv.getHtmlLabelName(26473, Util.getIntValue(str2)) + "</a>&nbsp;";
/*  108 */       str3 = str3 + "<a  title = \"" + SystemEnv.getHtmlLabelName(23777, Util.getIntValue(str2)) + "\" style=\"text-decoration:none;cursor:pointer\"  onclick=getOperating(3," + paramString1 + ")>" + SystemEnv.getHtmlLabelName(23777, Util.getIntValue(str2)) + "</a>&nbsp;";
/*      */     } 
/*  110 */     return str3;
/*      */   }
/*      */ 
/*      */   
/*      */   public ArrayList getLicenseOperating4E8(String paramString1, String paramString2) {
/*  115 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/*  116 */     String str1 = arrayOfString[0];
/*  117 */     String str2 = arrayOfString[1];
/*      */     
/*  119 */     ArrayList<String> arrayList = new ArrayList();
/*  120 */     arrayList.add("true");
/*  121 */     if ("1".equals(str1)) {
/*  122 */       arrayList.add("true");
/*  123 */       arrayList.add("true");
/*      */     } else {
/*  125 */       arrayList.add("false");
/*  126 */       arrayList.add("false");
/*      */     } 
/*      */ 
/*      */     
/*  130 */     return arrayList;
/*      */   }
/*      */   
/*      */   public String getAffixindex(String paramString) {
/*  134 */     if ("".equals(paramString)) return "-1"; 
/*  135 */     return paramString;
/*      */   }
/*      */   public String getCompanyRegion(String paramString) {
/*  138 */     String str1 = "";
/*  139 */     String str2 = "select cityname from hrmcity where id = " + paramString;
/*  140 */     RecordSet recordSet = new RecordSet();
/*  141 */     recordSet.execute(str2);
/*  142 */     if (recordSet.next())
/*      */     {
/*  144 */       str1 = recordSet.getString("cityname");
/*      */     }
/*  146 */     return str1;
/*      */   }
/*      */   
/*      */   public String getLicensename(String paramString1, String paramString2) {
/*  150 */     return "<a  title = \"" + paramString2 + "\" style=\"text-decoration:none;cursor:pointer\"  onclick=openConter(" + paramString1 + ")>" + paramString2 + "</a>";
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String setCompanyNameRef(String paramString1, String paramString2) {
/*  158 */     boolean bool = false;
/*  159 */     String str1 = "0";
/*  160 */     String[] arrayOfString = Util.TokenizerString2(paramString2, "+");
/*  161 */     String str2 = "," + arrayOfString[1] + ",";
/*  162 */     String str3 = arrayOfString[0];
/*  163 */     String str4 = arrayOfString[2];
/*  164 */     String str5 = "";
/*  165 */     if (!"HAVE".equals(str4)) {
/*  166 */       if (",ALL,".equals(str2)) {
/*  167 */         bool = true;
/*  168 */         str1 = "1";
/*  169 */       } else if (str2.lastIndexOf("," + str3 + ",") != -1) {
/*  170 */         bool = true;
/*  171 */         str1 = "1";
/*      */       } 
/*  173 */       str5 = "<a  title = \"" + paramString1 + "\" style=\"text-decoration:none;cursor:pointer\"  onclick=openConter(" + str3 + "," + str1 + ")>" + paramString1 + "</a>";
/*      */     } else {
/*  175 */       str5 = "" + paramString1 + "";
/*      */     } 
/*  177 */     return str5;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getToCompairLicenseDate(String paramString1, String paramString2) {
/*  193 */     String str1 = "";
/*  194 */     String str2 = "";
/*  195 */     String str3 = paramString2.split("_")[0];
/*  196 */     String str4 = paramString2.split("_")[1];
/*  197 */     String str5 = "";
/*  198 */     String str6 = "";
/*  199 */     String str7 = TimeUtil.getCurrentDateString();
/*  200 */     RecordSet recordSet1 = new RecordSet();
/*  201 */     RecordSet recordSet2 = new RecordSet();
/*  202 */     if (recordSet1.getDBType().equals("oracle") || recordSet1.getDBType().equals("postgresql")) {
/*  203 */       recordSet1.execute("select count(*) m from  CPBUSINESSLICENSE where  companyid=" + paramString1 + " and isdel='T'");
/*  204 */       if (recordSet1.next() && recordSet1.getInt("m") <= 0) {
/*  205 */         str2 = "";
/*      */       } else {
/*      */         
/*  208 */         str1 = "select min(to_char(to_date(USEFULENDDATE,'yyyy-mm-dd')-" + str3 + ",'yyyy-mm-dd')) s  from ";
/*  209 */         str1 = str1 + " CPBUSINESSLICENSE where  companyid=" + paramString1 + " and isdel='T' ";
/*  210 */         recordSet1.execute(str1);
/*  211 */         if (recordSet1.next()) {
/*  212 */           str5 = recordSet1.getString("s");
/*  213 */           int i = TimeUtil.dateInterval(str7, str5);
/*  214 */           if (i >= 0) {
/*      */             
/*  216 */             recordSet2.execute("select max(to_char(to_date(USEFULENDDATE,'yyyy-mm-dd'),'yyyy-mm-dd')) s  from CPBUSINESSLICENSE where  companyid=" + paramString1 + " and isdel='T'");
/*  217 */             if (recordSet2.next()) {
/*  218 */               str2 = "<em title='" + SystemEnv.getHtmlLabelName(31765, Util.getIntValue(str4)) + "：" + recordSet2.getString("s") + "' class='ModeG png'/>";
/*      */             }
/*      */           } 
/*      */         } 
/*  222 */         if ("".equals(str2)) {
/*      */           
/*  224 */           str1 = "select min(to_char(to_date(USEFULENDDATE,'yyyy-mm-dd'),'yyyy-mm-dd')) s  from ";
/*  225 */           str1 = str1 + " CPBUSINESSLICENSE where  companyid=" + paramString1 + " and isdel='T' ";
/*  226 */           recordSet1.execute(str1);
/*  227 */           if (recordSet1.next()) {
/*  228 */             str6 = recordSet1.getString("s");
/*  229 */             int i = TimeUtil.dateInterval(str7, str6);
/*  230 */             if (i < 0)
/*      */             {
/*  232 */               str2 = "<em title='" + SystemEnv.getHtmlLabelName(31765, Util.getIntValue(str4)) + "：" + str6 + "' class='ModeORed png'/>";
/*      */             }
/*      */           } 
/*      */         } 
/*  236 */         if ("".equals(str2)) {
/*  237 */           str1 = "select min(to_char(to_date(USEFULENDDATE,'yyyy-mm-dd'),'yyyy-mm-dd')) s  from ";
/*  238 */           str1 = str1 + " CPBUSINESSLICENSE where  companyid=" + paramString1 + " and isdel='T' ";
/*  239 */           recordSet1.execute(str1);
/*  240 */           if (recordSet1.next()) {
/*  241 */             str6 = recordSet1.getString("s");
/*      */             
/*  243 */             str2 = "<em title='" + SystemEnv.getHtmlLabelName(31765, Util.getIntValue(str4)) + "：" + str6 + "' class='ModeO png'/>";
/*      */           } 
/*      */         } 
/*      */       } 
/*  247 */     } else if ("sqlserver".equals(recordSet1.getDBType())) {
/*      */       
/*  249 */       recordSet1.execute("select count(*) m from  CPBUSINESSLICENSE where  companyid=" + paramString1 + " and isdel='T'");
/*  250 */       if (recordSet1.next() && recordSet1.getInt("m") <= 0) {
/*  251 */         str2 = "";
/*      */       } else {
/*      */         
/*  254 */         str1 = "select  min(CONVERT(varchar(10),dateadd(dd,-" + str3 + ",CONVERT(varchar(10),USEFULENDDATE,23)),23)) s from CPBUSINESSLICENSE  ";
/*  255 */         str1 = str1 + "where  companyid=" + paramString1 + " and isdel='T'   ";
/*  256 */         recordSet1.execute(str1);
/*  257 */         if (recordSet1.next()) {
/*  258 */           str5 = recordSet1.getString("s");
/*  259 */           int i = TimeUtil.dateInterval(str7, str5);
/*  260 */           if (i >= 0) {
/*      */             
/*  262 */             recordSet2.execute("select max(CONVERT(varchar(10),USEFULENDDATE,23)) s from CPBUSINESSLICENSE where  companyid=" + paramString1 + " and isdel='T'");
/*  263 */             if (recordSet2.next()) {
/*  264 */               str2 = "<em title='" + SystemEnv.getHtmlLabelName(31765, Util.getIntValue(str4)) + "：" + recordSet2.getString("s") + "' class='ModeG png'/>";
/*      */             }
/*      */           } 
/*      */         } 
/*  268 */         if ("".equals(str2)) {
/*      */           
/*  270 */           str1 = " select min(CONVERT(varchar(10),USEFULENDDATE,23)) s  from ";
/*  271 */           str1 = str1 + " CPBUSINESSLICENSE where  companyid=" + paramString1 + " and isdel='T'  ";
/*  272 */           recordSet1.execute(str1);
/*  273 */           if (recordSet1.next()) {
/*  274 */             str6 = recordSet1.getString("s");
/*  275 */             int i = TimeUtil.dateInterval(str7, str6);
/*  276 */             if (i < 0)
/*      */             {
/*  278 */               str2 = "<em title='" + SystemEnv.getHtmlLabelName(31765, Util.getIntValue(str4)) + "：" + str6 + "' class='ModeORed png'/>";
/*      */             }
/*      */           } 
/*      */         } 
/*  282 */         if ("".equals(str2)) {
/*  283 */           str1 = " select min(CONVERT(varchar(10),USEFULENDDATE,23)) s  from ";
/*  284 */           str1 = str1 + " CPBUSINESSLICENSE where  companyid=" + paramString1 + " and isdel='T'  ";
/*  285 */           recordSet1.execute(str1);
/*  286 */           if (recordSet1.next()) {
/*  287 */             str6 = recordSet1.getString("s");
/*      */             
/*  289 */             str2 = "<em title='" + SystemEnv.getHtmlLabelName(31765, Util.getIntValue(str4)) + "：" + str6 + "' class='ModeO png'/>";
/*      */           }
/*      */         
/*      */         } 
/*      */       } 
/*  294 */     } else if ("mysql".equals(recordSet1.getDBType())) {
/*  295 */       recordSet1.execute("select count(*) m from  CPBUSINESSLICENSE where  companyid=" + paramString1 + " and isdel='T'");
/*  296 */       if (recordSet1.next() && recordSet1.getInt("m") <= 0) {
/*  297 */         str2 = "";
/*      */       } else {
/*      */         
/*  300 */         str1 = "select  min(DATE_FORMAT(DATE_ADD(DATE_FORMAT(USEFULENDDATE,'%Y-%m-%d %H:%i:%s'),INTERVAL (-" + str3 + ") DAY ),'%Y-%m-%d %H:%i:%s' )) s from CPBUSINESSLICENSE  ";
/*  301 */         str1 = str1 + "where  companyid=" + paramString1 + " and isdel='T'   ";
/*  302 */         recordSet1.execute(str1);
/*  303 */         if (recordSet1.next()) {
/*  304 */           str5 = recordSet1.getString("s");
/*  305 */           int i = TimeUtil.dateInterval(str7, str5);
/*  306 */           if (i >= 0) {
/*      */             
/*  308 */             recordSet2.execute("select max(DATE_FORMAT(USEFULENDDATE,'%Y-%m-%d %H:%i:%s')) s from CPBUSINESSLICENSE where  companyid=" + paramString1 + " and isdel='T'");
/*  309 */             if (recordSet2.next()) {
/*  310 */               str2 = "<em title='" + SystemEnv.getHtmlLabelName(31765, Util.getIntValue(str4)) + "：" + recordSet2.getString("s") + "' class='ModeG png'/>";
/*      */             }
/*      */           } 
/*      */         } 
/*  314 */         if ("".equals(str2)) {
/*      */           
/*  316 */           str1 = " select min(DATE_FORMAT(USEFULENDDATE,'%Y-%m-%d %H:%i:%s')) s  from ";
/*  317 */           str1 = str1 + " CPBUSINESSLICENSE where  companyid=" + paramString1 + " and isdel='T'  ";
/*  318 */           recordSet1.execute(str1);
/*  319 */           if (recordSet1.next()) {
/*  320 */             str6 = recordSet1.getString("s");
/*  321 */             int i = TimeUtil.dateInterval(str7, str6);
/*  322 */             if (i < 0)
/*      */             {
/*  324 */               str2 = "<em title='" + SystemEnv.getHtmlLabelName(31765, Util.getIntValue(str4)) + "：" + str6 + "' class='ModeORed png'/>";
/*      */             }
/*      */           } 
/*      */         } 
/*  328 */         if ("".equals(str2)) {
/*  329 */           str1 = " select min(DATE_FORMAT(USEFULENDDATE,'%Y-%m-%d %H:%i:%s')) s  from ";
/*  330 */           str1 = str1 + " CPBUSINESSLICENSE where  companyid=" + paramString1 + " and isdel='T'  ";
/*  331 */           recordSet1.execute(str1);
/*  332 */           if (recordSet1.next()) {
/*  333 */             str6 = recordSet1.getString("s");
/*      */             
/*  335 */             str2 = "<em title='" + SystemEnv.getHtmlLabelName(31765, Util.getIntValue(str4)) + "：" + str6 + "' class='ModeO png'/>";
/*      */           } 
/*      */         } 
/*      */       } 
/*      */     } 
/*      */ 
/*      */     
/*  342 */     return str2;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getToCompairDate(String paramString1, String paramString2) {
/*  432 */     String str1 = paramString2.split("_")[0];
/*  433 */     String str2 = paramString2.split("_")[1];
/*  434 */     String str3 = "";
/*  435 */     String str4 = Util.date(1);
/*  436 */     int i = 0;
/*  437 */     if (!paramString1.equals("")) {
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  444 */       i = countDays(str4, paramString1);
/*      */       
/*  446 */       if (i == -1) {
/*      */         
/*  448 */         str3 = "<em title='" + SystemEnv.getHtmlLabelName(31765, Util.getIntValue(str2)) + "：" + paramString1 + "' class='ModeORed png'/>";
/*      */       }
/*  450 */       else if (i <= Util.getIntValue(str1)) {
/*      */         
/*  452 */         str3 = "<em title='" + SystemEnv.getHtmlLabelName(31765, Util.getIntValue(str2)) + "：" + paramString1 + "' class='ModeO png'/>";
/*      */       }
/*      */       else {
/*      */         
/*  456 */         str3 = "<em title='" + SystemEnv.getHtmlLabelName(31765, Util.getIntValue(str2)) + "：" + paramString1 + "' class='ModeG png'/>";
/*      */       } 
/*      */     } 
/*  459 */     return str3;
/*      */   }
/*      */ 
/*      */   
/*      */   public String getToGd(String paramString1, String paramString2) {
/*  464 */     String str1 = "";
/*  465 */     String str2 = "";
/*  466 */     RecordSet recordSet = new RecordSet();
/*      */ 
/*      */     
/*  469 */     recordSet.execute("select shareid from CPSHAREHOLDER where companyid='" + paramString1 + "'");
/*      */     
/*  471 */     if (recordSet.next()) {
/*  472 */       if (recordSet.getDBType().equals("oracle")) {
/*  473 */         str2 = " select sum(t1.investment) as investment,t2.companyid from CPSHAREOFFICERS t1,CPSHAREHOLDER t2 where t2.companyid=" + paramString1 + " and t2.shareid=t1.shareid group by t2.companyid";
/*  474 */       } else if ("mysql".equals(recordSet.getDBType())) {
/*  475 */         str2 = " select sum(convert(t1.investment,DECIMAL)) as investment,t2.companyid from CPSHAREOFFICERS t1,CPSHAREHOLDER t2 where t2.companyid=" + paramString1 + " and t2.shareid=t1.shareid group by t2.companyid";
/*      */       }
/*  477 */       else if (recordSet.getDBType().equals("postgresql")) {
/*  478 */         str2 = " select sum(cast(t1.investment as float)) as investment,t2.companyid from CPSHAREOFFICERS t1,CPSHAREHOLDER t2 where t2.companyid=" + paramString1 + " and t2.shareid=t1.shareid group by t2.companyid";
/*      */       } else {
/*      */         
/*  481 */         str2 = " select sum(convert(float,t1.investment)) as investment,t2.companyid from CPSHAREOFFICERS t1,CPSHAREHOLDER t2 where t2.companyid=" + paramString1 + " and t2.shareid=t1.shareid group by t2.companyid";
/*      */       } 
/*  483 */       recordSet.execute(str2);
/*  484 */       float f = 0.0F;
/*  485 */       if (recordSet.next()) {
/*  486 */         f = Util.getFloatValue(recordSet.getString("investment"));
/*      */       }
/*  488 */       if (f == Util.getFloatValue("100")) { str1 = "<em title='" + SystemEnv.getHtmlLabelName(31766, Util.getIntValue(paramString2)) + ":" + f + "' class='ModeG png'/>"; }
/*  489 */       else { str1 = "<em title='" + SystemEnv.getHtmlLabelName(31766, Util.getIntValue(paramString2)) + ":" + f + "' class='ModeORed png'/>"; }
/*      */     
/*  491 */     }  return str1;
/*      */   }
/*      */   
/*      */   public String getAffixDown(String paramString) {
/*  495 */     RecordSet recordSet = new RecordSet();
/*  496 */     String str1 = "";
/*  497 */     String str2 = "";
/*  498 */     if (!Util.null2String(paramString).equals("")) {
/*  499 */       recordSet.execute("select * from imagefile where imagefileid in(" + paramString.substring(0, paramString.length() - 1) + ")");
/*      */       
/*  501 */       for (; recordSet.next(); str1 = str1 + recordSet.getString("imagefileid") + ",");
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  509 */       str2 = "<A href='/weaver/weaver.file.FileDownload?fieldids=" + str1 + "&download=1&onlydownloadfj=1&labelid=30905' class='aContent0 FL'>" + SystemEnv.getHtmlLabelName(31156, ThreadVarLanguage.getLang()) + "</A> ";
/*      */     } 
/*  511 */     return str2;
/*      */   }
/*      */   
/*      */   public String getismilti(String paramString) {
/*  515 */     if (paramString.equals("1")) {
/*  516 */       return "" + SystemEnv.getHtmlLabelName(163, ThreadVarLanguage.getLang()) + "";
/*      */     }
/*      */     
/*  519 */     return "" + SystemEnv.getHtmlLabelName(25105, ThreadVarLanguage.getLang()) + "";
/*      */   }
/*      */ 
/*      */   
/*      */   private static int countDays(String paramString1, String paramString2) {
/*  524 */     int i = 0;
/*  525 */     Calendar calendar1 = createCalendar(paramString1);
/*  526 */     Calendar calendar2 = createCalendar(paramString2);
/*  527 */     if (calendar2.after(calendar1)) {
/*  528 */       while (calendar2.after(calendar1))
/*  529 */       { calendar2.add(5, -1);
/*  530 */         i++; } 
/*  531 */     } else if (calendar2.equals(calendar1)) {
/*  532 */       i = 0;
/*      */     } else {
/*      */       
/*  535 */       i = -1;
/*      */     } 
/*  537 */     return i;
/*      */   }
/*      */   private static Calendar createCalendar(String paramString) {
/*  540 */     GregorianCalendar gregorianCalendar = null;
/*  541 */     SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
/*      */     try {
/*  543 */       if (paramString != null)
/*  544 */       { Date date = simpleDateFormat.parse(paramString);
/*  545 */         gregorianCalendar = new GregorianCalendar();
/*  546 */         gregorianCalendar.setTime(date); } 
/*  547 */     } catch (ParseException parseException) {
/*  548 */       parseException.printStackTrace();
/*      */     } 
/*  550 */     return gregorianCalendar;
/*      */   }
/*      */   
/*      */   public static List setStale() {
/*  554 */     String str1 = TimeUtil.getCurrentDateString().substring(0, 4);
/*  555 */     String str2 = TimeUtil.getCurrentDateString().substring(5, 7);
/*  556 */     byte b1 = 1;
/*  557 */     ArrayList<String> arrayList = new ArrayList();
/*  558 */     String str3 = "select * from CPCOMPANYTIMEOVER";
/*  559 */     RecordSet recordSet = new RecordSet();
/*  560 */     recordSet.execute(str3);
/*  561 */     String str4 = "";
/*  562 */     String str5 = "";
/*  563 */     int i = 0;
/*  564 */     String str6 = "";
/*  565 */     String str7 = "";
/*  566 */     int j = 0;
/*  567 */     if (recordSet.next()) {
/*  568 */       str4 = recordSet.getString("tofaren");
/*  569 */       str5 = recordSet.getString("todsh");
/*  570 */       i = recordSet.getInt("tozhzh") - 1;
/*  571 */       str6 = recordSet.getString("togd");
/*  572 */       str7 = recordSet.getString("tozhch");
/*  573 */       j = recordSet.getInt("tonjian") - 1;
/*      */     } 
/*  575 */     if ("sqlserver".equals(recordSet.getDBType())) {
/*  576 */       b1 = 2;
/*      */     }
/*      */     
/*  579 */     if ("mysql".equals(recordSet.getDBType())) {
/*  580 */       b1 = 3;
/*      */     }
/*      */     
/*  583 */     String str8 = "";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  599 */     str8 = "select count(*)  count from   cpcchangenotice  where  c_type=1  and c_year='" + str1 + "' and c_month='" + str2 + "'";
/*      */     
/*  601 */     recordSet.execute(str8);
/*  602 */     String str9 = "";
/*  603 */     if (recordSet.next()) str9 = Util.null2String(recordSet.getString("count")); 
/*  604 */     if (str9.equals("")) str9 = "0";
/*      */     
/*  606 */     String str10 = "";
/*  607 */     String str11 = "";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  616 */     str11 = "select count(*)  count from   cpcchangenotice  where  c_type=2  and c_year='" + str1 + "' and c_month='" + str2 + "'";
/*      */     
/*  618 */     recordSet.execute(str11);
/*  619 */     if (recordSet.next()) str10 = Util.null2String(recordSet.getString("count")); 
/*  620 */     if (str10.equals("")) str10 = "0";
/*      */ 
/*      */ 
/*      */     
/*  624 */     byte b2 = 0;
/*  625 */     String str12 = TimeUtil.getCurrentDateString();
/*  626 */     String str13 = "select USEFULENDDATE endday ,to_char(TRUNC(to_date(USEFULENDDATE,'yyyy-mm-dd')-" + i + "),'yyyy-mm-dd') startday  from CPBUSINESSLICENSE   where  ISDEL='T'  and companyid in(select companyid from CPCOMPANYINFO ) ";
/*  627 */     if (b1 == 2)
/*  628 */       str13 = "select USEFULENDDATE endday ,CONVERT(varchar(100), DATEADD(day,-" + i + ",USEFULENDDATE),23) startday from CPBUSINESSLICENSE where  ISDEL='T'  and companyid in(select companyid from CPCOMPANYINFO ) "; 
/*  629 */     if (b1 == 3) {
/*  630 */       str13 = "select USEFULENDDATE endday ,DATE_FORMAT(DATE_ADD(USEFULENDDATE,INTERVAL (-" + i + ") DAY),'%Y-%m-%d %H:%i:%s') startday from CPBUSINESSLICENSE where  ISDEL='T'  and companyid in(select companyid from CPCOMPANYINFO ) ";
/*      */     }
/*  632 */     recordSet.execute(str13);
/*  633 */     while (recordSet.next()) {
/*  634 */       String str17 = recordSet.getString("startday");
/*  635 */       String str18 = recordSet.getString("endday");
/*  636 */       if (TimeUtil.dateInterval(str17, str12) >= 0 && TimeUtil.dateInterval(str18, str12) <= 0) {
/*  637 */         b2++;
/*      */       }
/*      */     } 
/*      */ 
/*      */ 
/*      */     
/*  643 */     String str14 = "";
/*  644 */     String str15 = "";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  654 */     str15 = "select count(*) count  from   cpcchangenotice  where  c_type=3 and c_year='" + str1 + "' and c_month='" + str2 + "'";
/*      */     
/*  656 */     recordSet.execute(str15);
/*  657 */     if (recordSet.next()) str14 = Util.null2String(recordSet.getString("count")); 
/*  658 */     if (str14.equals("")) str14 = "0";
/*      */     
/*  660 */     byte b3 = 0;
/*  661 */     String str16 = "select ANNUALINSPECTION endday ,to_char(TRUNC(to_date(ANNUALINSPECTION,'yyyy-mm-dd')-" + j + "),'yyyy-mm-dd') startday  from CPBUSINESSLICENSE   where  ISDEL='T'  and companyid in(select companyid from CPCOMPANYINFO )";
/*  662 */     if (b1 == 2) {
/*  663 */       str16 = "select ANNUALINSPECTION endday ,CONVERT(varchar(100), DATEADD(day,-" + j + ",ANNUALINSPECTION),23) startday from CPBUSINESSLICENSE where  ISDEL='T'  and companyid in(select companyid from CPCOMPANYINFO ) ";
/*      */     }
/*  665 */     if (b1 == 3) {
/*  666 */       str16 = "select ANNUALINSPECTION endday ,DATE_FORMAT( DATE_ADD(ANNUALINSPECTION,INTERVAL (-" + j + ") DAY),'%Y-%m-%d %H:%i:%s') startday from CPBUSINESSLICENSE where  ISDEL='T'  and companyid in(select companyid from CPCOMPANYINFO ) ";
/*      */     }
/*  668 */     recordSet.execute(str16);
/*  669 */     while (recordSet.next()) {
/*  670 */       String str17 = recordSet.getString("startday");
/*  671 */       String str18 = recordSet.getString("endday");
/*  672 */       if (TimeUtil.dateInterval(str17, str12) >= 0 && TimeUtil.dateInterval(str18, str12) <= 0) {
/*  673 */         b3++;
/*      */       }
/*      */     } 
/*      */ 
/*      */     
/*  678 */     arrayList.add(str9);
/*  679 */     arrayList.add(str10);
/*  680 */     arrayList.add(Integer.valueOf(b2));
/*  681 */     arrayList.add(str14);
/*  682 */     arrayList.add(Integer.valueOf(b3));
/*  683 */     return arrayList;
/*      */   }
/*      */ 
/*      */   
/*      */   public static List setStale(String paramString) {
/*  688 */     String str1 = TimeUtil.getCurrentDateString().substring(0, 4);
/*  689 */     String str2 = TimeUtil.getCurrentDateString().substring(5, 7);
/*  690 */     ArrayList<String> arrayList = new ArrayList();
/*  691 */     String str3 = "select * from CPCOMPANYTIMEOVER";
/*  692 */     RecordSet recordSet = new RecordSet();
/*  693 */     recordSet.execute(str3);
/*  694 */     String str4 = "";
/*  695 */     String str5 = "";
/*  696 */     int i = 0;
/*  697 */     String str6 = "";
/*  698 */     String str7 = "";
/*  699 */     int j = 0;
/*  700 */     if (recordSet.next()) {
/*  701 */       str4 = recordSet.getString("tofaren");
/*  702 */       str5 = recordSet.getString("todsh");
/*  703 */       i = recordSet.getInt("tozhzh") - 1;
/*  704 */       str6 = recordSet.getString("togd");
/*  705 */       str7 = recordSet.getString("tozhch");
/*  706 */       j = recordSet.getInt("tonjian") - 1;
/*      */     } 
/*  708 */     byte b1 = 1;
/*  709 */     if ("sqlserver".equals(recordSet.getDBType())) {
/*  710 */       b1 = 2;
/*      */     }
/*  712 */     else if ("mysql".equals(recordSet.getDBType())) {
/*  713 */       b1 = 3;
/*      */     }
/*  715 */     else if ("postgresql".equals(recordSet.getDBType())) {
/*  716 */       b1 = 4;
/*      */     } 
/*  718 */     String str8 = "";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  729 */     str8 = "select count(*) count  from   cpcchangenotice  where  c_type=1 and  c_companyid='" + paramString + "' and c_year='" + str1 + "' and c_month='" + str2 + "'";
/*      */ 
/*      */ 
/*      */     
/*  733 */     recordSet.execute(str8);
/*  734 */     String str9 = "";
/*  735 */     if (recordSet.next()) str9 = Util.null2String(recordSet.getString("count")); 
/*  736 */     if (str9.equals("")) str9 = "0";
/*      */     
/*  738 */     String str10 = "";
/*  739 */     String str11 = "";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  747 */     str11 = "select count(*) count  from   cpcchangenotice  where  c_type=2 and  c_companyid='" + paramString + "' and c_year='" + str1 + "' and c_month='" + str2 + "'";
/*  748 */     recordSet.execute(str11);
/*  749 */     if (recordSet.next()) str10 = Util.null2String(recordSet.getString("count")); 
/*  750 */     if (str10.equals("")) str10 = "0";
/*      */     
/*  752 */     byte b2 = 0;
/*  753 */     String str12 = "";
/*  754 */     if (b1 == 2) {
/*      */       
/*  756 */       str12 = "select USEFULENDDATE endday ,CONVERT(varchar(100), DATEADD(day,-" + i + ",USEFULENDDATE),23) startday from CPBUSINESSLICENSE where  ISDEL='T'  and companyid=" + paramString + " ";
/*  757 */     } else if (b1 == 3) {
/*      */       
/*  759 */       str12 = "select USEFULENDDATE endday ,DATE_FORMAT( DATE_ADD(USEFULENDDATE,INTERVAL (-" + i + ") DAY),'%Y-%m-%d %H:%i:%s') startday from CPBUSINESSLICENSE where  ISDEL='T'  and companyid=" + paramString + " ";
/*  760 */     } else if (b1 == 1) {
/*  761 */       str12 = "select USEFULENDDATE endday ,to_char(TRUNC(to_date(USEFULENDDATE,'yyyy-mm-dd')-" + i + "),'yyyy-mm-dd') startday  from CPBUSINESSLICENSE   where  ISDEL='T'  and companyid=" + paramString + "  ";
/*      */     } 
/*  763 */     String str13 = TimeUtil.getCurrentDateString();
/*  764 */     recordSet.execute(str12);
/*  765 */     while (recordSet.next()) {
/*  766 */       String str23 = recordSet.getString("startday");
/*  767 */       String str24 = recordSet.getString("endday");
/*  768 */       if (TimeUtil.dateInterval(str23, str13) >= 0 && TimeUtil.dateInterval(str24, str13) <= 0) {
/*  769 */         b2++;
/*      */       }
/*      */     } 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  776 */     String str14 = "";
/*  777 */     String str15 = "";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  786 */     str15 = "select count(*) count  from   cpcchangenotice  where  c_type=3 and  c_companyid='" + paramString + "' and c_year='" + str1 + "' and c_month='" + str2 + "'";
/*      */     
/*  788 */     recordSet.execute(str15);
/*  789 */     if (recordSet.next()) str14 = Util.null2String(recordSet.getString("count")); 
/*  790 */     if (str14.equals("")) str14 = "0";
/*      */     
/*  792 */     byte b3 = 0;
/*  793 */     String str16 = "select ANNUALINSPECTION endday ,to_char(TRUNC(to_date(ANNUALINSPECTION,'yyyy-mm-dd')-" + j + "),'yyyy-mm-dd') startday  from CPBUSINESSLICENSE   where  ISDEL='T'  and companyid=" + paramString + " ";
/*  794 */     if (b1 == 2) {
/*  795 */       str16 = "select ANNUALINSPECTION endday ,CONVERT(varchar(100), DATEADD(day,-" + j + ",ANNUALINSPECTION),23) startday from CPBUSINESSLICENSE where  ISDEL='T'  and companyid=" + paramString + "  ";
/*      */     }
/*  797 */     if (b1 == 3) {
/*  798 */       str16 = "select ANNUALINSPECTION endday ,DATE_FORMAT(DATE_ADD(ANNUALINSPECTION,INTERVAL (-" + j + ") DAY),'%Y-%m-%d %H:%i:%s') startday from CPBUSINESSLICENSE where  ISDEL='T'  and companyid=" + paramString + "  ";
/*      */     }
/*  800 */     recordSet.execute(str16);
/*  801 */     while (recordSet.next()) {
/*  802 */       String str23 = recordSet.getString("startday");
/*  803 */       String str24 = recordSet.getString("endday");
/*      */ 
/*      */ 
/*      */       
/*  807 */       if (TimeUtil.dateInterval(str23, str13) >= 0 && TimeUtil.dateInterval(str24, str13) <= 0) {
/*  808 */         b3++;
/*      */       }
/*      */     } 
/*      */ 
/*      */     
/*  813 */     String str17 = "";
/*  814 */     String str18 = "";
/*  815 */     if (b1 == 2) {
/*  816 */       str18 = "select top 1 substring(createdatetime,0,12)as createdatetime,versionnum  from CPCONSTITUTIONVERSION where companyid=" + paramString + " order by CONVERT(float,versionnum)  desc ";
/*  817 */     } else if (b1 == 3) {
/*  818 */       str18 = "select substring(createdatetime,0,12)as createdatetime,versionnum  from CPCONSTITUTIONVERSION where companyid=" + paramString + " order by CONVERT(versionnum,DECIMAL)  desc limit 1";
/*      */     }
/*  820 */     else if (b1 == 4) {
/*  821 */       str18 = "select substring(createdatetime,0,12)as createdatetime,versionnum  from CPCONSTITUTIONVERSION where companyid=" + paramString + " order by to_number(versionnum)  desc limit 1";
/*      */     } else {
/*      */       
/*  824 */       str18 = "select * from (select substr(createdatetime,0,12)as createdatetime,versionnum  from CPCONSTITUTIONVERSION where companyid=" + paramString + " order by to_number(versionnum) desc) where rownum<2 ";
/*      */     } 
/*      */     
/*  827 */     recordSet.execute(str18);
/*  828 */     if (recordSet.next()) { str17 = recordSet.getString("createdatetime") + "/" + recordSet.getString("versionnum"); }
/*  829 */     else { str17 = "0/0"; }
/*      */ 
/*      */     
/*  832 */     String str19 = "";
/*  833 */     String str20 = "";
/*  834 */     if (b1 == 2) {
/*  835 */       str20 = "select top 1 substring(createdatetime,0,12)as createdatetime,versionnum  from CPSHAREHOLDERVERSION where companyid=" + paramString + " order by CONVERT(float,versionnum)  desc";
/*  836 */     } else if (b1 == 3) {
/*  837 */       str20 = "select substring(createdatetime,0,12)as createdatetime,versionnum  from CPSHAREHOLDERVERSION where companyid=" + paramString + " order by CONVERT(versionnum,DECIMAL)  desc limit 1";
/*      */     }
/*  839 */     else if (b1 == 4) {
/*  840 */       str20 = "select substring(createdatetime,0,12)as createdatetime,versionnum  from CPSHAREHOLDERVERSION where companyid=" + paramString + " order by to_number(versionnum)  desc limit 1";
/*      */     } else {
/*      */       
/*  843 */       str20 = "select * from (select substr(createdatetime,0,12)as createdatetime,versionnum  from CPSHAREHOLDERVERSION where companyid=" + paramString + " order by to_number(versionnum) desc) where rownum<2 ";
/*      */     } 
/*      */     
/*  846 */     recordSet.execute(str20);
/*  847 */     if (recordSet.next()) { str19 = recordSet.getString("createdatetime") + "/" + recordSet.getString("versionnum"); }
/*  848 */     else { str19 = "0/0"; }
/*      */ 
/*      */     
/*  851 */     String str21 = "";
/*  852 */     String str22 = "";
/*  853 */     if (b1 == 2) {
/*  854 */       str22 = "select  top 1 substring(createdatetime,0,12)as createdatetime,versionnum  from CPBOARDVERSION where companyid=" + paramString + " order by CONVERT(float,versionnum)  desc ";
/*  855 */     } else if (b1 == 3) {
/*  856 */       str22 = "select substring(createdatetime,0,12)as createdatetime,versionnum  from CPBOARDVERSION where companyid=" + paramString + " order by CONVERT(versionnum,DECIMAL)  desc limit 1";
/*      */     }
/*  858 */     else if (b1 == 4) {
/*  859 */       str22 = "select substring(createdatetime,0,12)as createdatetime,versionnum  from CPBOARDVERSION where companyid=" + paramString + " order by to_number(versionnum)  desc limit 1";
/*      */     } else {
/*      */       
/*  862 */       str22 = "select * from (select substr(createdatetime,0,12)as createdatetime,versionnum  from CPBOARDVERSION where companyid=" + paramString + " order by to_number(versionnum) desc) where rownum<2 ";
/*      */     } 
/*      */     
/*  865 */     recordSet.execute(str22);
/*  866 */     if (recordSet.next()) { str21 = recordSet.getString("createdatetime") + "/" + recordSet.getString("versionnum"); }
/*  867 */     else { str21 = "0/0"; }
/*      */ 
/*      */     
/*  870 */     arrayList.add(str9);
/*  871 */     arrayList.add(str10);
/*  872 */     arrayList.add(Integer.valueOf(b2));
/*  873 */     arrayList.add(str14);
/*  874 */     arrayList.add(Integer.valueOf(b3));
/*  875 */     arrayList.add(str17);
/*  876 */     arrayList.add(str19);
/*  877 */     arrayList.add(str21);
/*  878 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static List getStaleValue(int paramInt1, int paramInt2, int paramInt3) {
/*  889 */     byte b = 1;
/*  890 */     ArrayList<CompanyKeyValue> arrayList = new ArrayList();
/*  891 */     String str1 = "select * from CPCOMPANYTIMEOVER";
/*  892 */     RecordSet recordSet = new RecordSet();
/*  893 */     recordSet.execute(str1);
/*  894 */     String str2 = "";
/*  895 */     String str3 = "";
/*  896 */     int i = 0;
/*  897 */     String str4 = "";
/*  898 */     String str5 = "";
/*  899 */     int j = 0;
/*  900 */     if (recordSet.next()) {
/*  901 */       str2 = recordSet.getString("tofaren");
/*  902 */       str3 = recordSet.getString("todsh");
/*  903 */       i = recordSet.getInt("tozhzh") - 1;
/*  904 */       str4 = recordSet.getString("togd");
/*  905 */       str5 = recordSet.getString("tozhch");
/*  906 */       j = recordSet.getInt("tonjian") - 1;
/*      */     } 
/*  908 */     if ("sqlserver".equals(recordSet.getDBType())) {
/*  909 */       b = 2;
/*      */     }
/*  911 */     if ("mysql".equals(recordSet.getDBType())) {
/*  912 */       b = 3;
/*      */     }
/*  914 */     if (paramInt1 == 1)
/*  915 */     { String str6 = TimeUtil.getCurrentDateString().substring(0, 4);
/*  916 */       String str7 = TimeUtil.getCurrentDateString().substring(5, 7);
/*  917 */       str1 = "select a.*,b.companyname from cpcchangenotice a left join  cpcompanyinfo b  on a.c_companyid=b.companyid where    c_type=1 and c_year='" + str6 + "' and c_month='" + str7 + "'";
/*  918 */       if (1 == paramInt2) {
/*  919 */         str1 = str1 + " and c_companyid= " + paramInt3 + "";
/*      */       }
/*  921 */       str1 = str1 + " order by c_time"; }
/*  922 */     else if (paramInt1 == 2)
/*  923 */     { String str6 = TimeUtil.getCurrentDateString().substring(0, 4);
/*  924 */       String str7 = TimeUtil.getCurrentDateString().substring(5, 7);
/*  925 */       str1 = "select a.*,b.companyname from cpcchangenotice a left join  cpcompanyinfo b  on a.c_companyid=b.companyid where    c_type=2 and c_year='" + str6 + "' and c_month='" + str7 + "'";
/*  926 */       if (1 == paramInt2) {
/*  927 */         str1 = str1 + " and c_companyid= " + paramInt3 + "";
/*      */       }
/*  929 */       str1 = str1 + " order by c_time"; }
/*  930 */     else { if (paramInt1 == 3) {
/*  931 */         if (b == 2) {
/*      */           
/*  933 */           str1 = "   select licensename as name ,t3.companyname ,USEFULENDDATE endday ,CONVERT(varchar(100), DATEADD(day,-" + i + ",USEFULENDDATE),23) startday from ";
/*  934 */           str1 = str1 + " CPBUSINESSLICENSE t1, CPLMLICENSEAFFIX t2,cpcompanyinfo t3";
/*  935 */           str1 = str1 + " where t1.licenseaffixid = t2.licenseaffixid and t1.companyid=t3.companyid and t1.isdel='T'  ";
/*  936 */           if (1 == paramInt2) {
/*  937 */             str1 = str1 + " and t1.companyid='" + paramInt3 + "' ";
/*      */           }
/*  939 */         } else if (b == 3) {
/*      */           
/*  941 */           str1 = "   select licensename as name ,t3.companyname ,USEFULENDDATE endday ,DATE_FORMAT( DATE_ADD(USEFULENDDATE,INTERVAL (-" + i + ") DAY),'%Y-%m-%d %H:%i:%s' ) startday from ";
/*  942 */           str1 = str1 + " CPBUSINESSLICENSE t1, CPLMLICENSEAFFIX t2,cpcompanyinfo t3";
/*  943 */           str1 = str1 + " where t1.licenseaffixid = t2.licenseaffixid and t1.companyid=t3.companyid and t1.isdel='T'  ";
/*  944 */           if (1 == paramInt2) {
/*  945 */             str1 = str1 + " and t1.companyid='" + paramInt3 + "' ";
/*      */           }
/*      */         } else {
/*  948 */           str1 = "   select licensename as name ,t3.companyname , USEFULENDDATE endday ,to_char(TRUNC(to_date(USEFULENDDATE,'yyyy-mm-dd')-" + i + "),'yyyy-mm-dd') startday from ";
/*  949 */           str1 = str1 + " CPBUSINESSLICENSE t1, CPLMLICENSEAFFIX t2,cpcompanyinfo t3";
/*  950 */           str1 = str1 + " where t1.licenseaffixid = t2.licenseaffixid and t1.companyid=t3.companyid and t1.isdel='T'  ";
/*  951 */           if (1 == paramInt2) {
/*  952 */             str1 = str1 + " and t1.companyid='" + paramInt3 + "' ";
/*      */           }
/*      */         } 
/*  955 */         String str = TimeUtil.getCurrentDateString();
/*  956 */         recordSet.execute(str1);
/*  957 */         while (recordSet.next()) {
/*  958 */           String str6 = recordSet.getString("startday");
/*  959 */           String str7 = recordSet.getString("endday");
/*  960 */           if (TimeUtil.dateInterval(str6, str) >= 0 && TimeUtil.dateInterval(str7, str) <= 0) {
/*  961 */             CompanyKeyValue companyKeyValue = new CompanyKeyValue();
/*  962 */             companyKeyValue.setDesc(recordSet.getString("name"));
/*  963 */             companyKeyValue.setValue(recordSet.getString("companyname"));
/*  964 */             companyKeyValue.setDatetime(recordSet.getString("endday"));
/*  965 */             arrayList.add(companyKeyValue);
/*      */           } 
/*      */         } 
/*  968 */         return arrayList;
/*      */       } 
/*  970 */       if (paramInt1 == 4) {
/*  971 */         String str6 = TimeUtil.getCurrentDateString().substring(0, 4);
/*  972 */         String str7 = TimeUtil.getCurrentDateString().substring(5, 7);
/*  973 */         str1 = "select a.*,b.companyname from cpcchangenotice a left join  cpcompanyinfo b  on a.c_companyid=b.companyid where   c_type=3 and c_year='" + str6 + "' and c_month='" + str7 + "'";
/*  974 */         if (1 == paramInt2) {
/*  975 */           str1 = str1 + " and c_companyid= " + paramInt3 + "";
/*      */         }
/*  977 */         str1 = str1 + " order by c_time";
/*  978 */       } else if (paramInt1 == 5) {
/*  979 */         String str = TimeUtil.getCurrentDateString();
/*  980 */         if (b == 2) {
/*  981 */           str1 = "select licensename  as name,t3.companyname,ANNUALINSPECTION endday ,CONVERT(varchar(100), DATEADD(day,-" + j + ",ANNUALINSPECTION),23) startday ";
/*  982 */           str1 = str1 + " from CPBUSINESSLICENSE t1 , CPLMLICENSEAFFIX t2,cpcompanyinfo t3";
/*  983 */           str1 = str1 + " where t1.licenseaffixid = t2.licenseaffixid and t1.companyid=t3.companyid  and  t1.ISDEL='T'";
/*  984 */           if (1 == paramInt2) {
/*  985 */             str1 = str1 + " and t1.companyid='" + paramInt3 + "' ";
/*      */           }
/*  987 */         } else if (b == 3) {
/*  988 */           str1 = "select licensename  as name,t3.companyname,ANNUALINSPECTION endday ,DATE_FORMAT( DATE_ADD(ANNUALINSPECTION,INTERVAL (-" + j + ") DAY),'%Y-%m-%d %H:%i:%s') startday ";
/*  989 */           str1 = str1 + " from CPBUSINESSLICENSE t1 , CPLMLICENSEAFFIX t2,cpcompanyinfo t3";
/*  990 */           str1 = str1 + " where t1.licenseaffixid = t2.licenseaffixid and t1.companyid=t3.companyid  and  t1.ISDEL='T'";
/*  991 */           if (1 == paramInt2) {
/*  992 */             str1 = str1 + " and t1.companyid='" + paramInt3 + "' ";
/*      */           }
/*      */         } else {
/*  995 */           str1 = "select licensename  as name,t3.companyname,ANNUALINSPECTION endday ,to_char(TRUNC(to_date(ANNUALINSPECTION,'yyyy-mm-dd')-" + j + "),'yyyy-mm-dd') startday ";
/*  996 */           str1 = str1 + " from CPBUSINESSLICENSE t1 , CPLMLICENSEAFFIX t2,cpcompanyinfo t3";
/*  997 */           str1 = str1 + " where t1.licenseaffixid = t2.licenseaffixid and t1.companyid=t3.companyid and  t1.ISDEL='T' ";
/*  998 */           if (1 == paramInt2) {
/*  999 */             str1 = str1 + " and t1.companyid='" + paramInt3 + "' ";
/*      */           }
/*      */         } 
/* 1002 */         recordSet.execute(str1);
/* 1003 */         while (recordSet.next()) {
/* 1004 */           String str6 = recordSet.getString("startday");
/* 1005 */           String str7 = recordSet.getString("endday");
/* 1006 */           if (TimeUtil.dateInterval(str6, str) >= 0 && TimeUtil.dateInterval(str7, str) <= 0) {
/* 1007 */             CompanyKeyValue companyKeyValue = new CompanyKeyValue();
/* 1008 */             companyKeyValue.setDesc(recordSet.getString("name"));
/* 1009 */             companyKeyValue.setValue(recordSet.getString("companyname"));
/* 1010 */             companyKeyValue.setDatetime(recordSet.getString("endday"));
/* 1011 */             arrayList.add(companyKeyValue);
/*      */           } 
/*      */         } 
/* 1014 */         return arrayList;
/*      */       }  }
/*      */     
/* 1017 */     recordSet.execute(str1);
/* 1018 */     while (recordSet.next()) {
/* 1019 */       CompanyKeyValue companyKeyValue = new CompanyKeyValue();
/* 1020 */       companyKeyValue.setValue(recordSet.getString("companyname"));
/* 1021 */       companyKeyValue.setDatetime(recordSet.getString("c_time"));
/* 1022 */       companyKeyValue.setDesc(recordSet.getString("c_desc"));
/* 1023 */       arrayList.add(companyKeyValue);
/*      */     } 
/* 1025 */     return arrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static boolean CheckPack(String paramString) {
/* 1037 */     boolean bool = false;
/* 1038 */     RecordSet recordSet = new RecordSet();
/* 1039 */     String str = "select * from  mytrainaccessoriestype where  accessoriesname  ";
/* 1040 */     if ("1".equals(paramString)) {
/* 1041 */       str = str + " ='lmlicense'";
/* 1042 */     } else if ("2".equals(paramString)) {
/* 1043 */       str = str + " ='lmconstitution'";
/* 1044 */     } else if ("3".equals(paramString)) {
/* 1045 */       str = str + " ='lmshare'";
/* 1046 */     } else if ("4".equals(paramString)) {
/* 1047 */       str = str + " ='lmdirectors'";
/*      */     } 
/* 1049 */     recordSet.execute(str);
/* 1050 */     if (recordSet.next()) {
/* 1051 */       String str1 = "";
/* 1052 */       String str2 = "";
/* 1053 */       String str3 = "";
/* 1054 */       str1 = recordSet.getString("mainid");
/* 1055 */       str2 = recordSet.getString("subid");
/* 1056 */       str3 = recordSet.getString("secid");
/* 1057 */       if (!"0".equals(str1) && !"0".equals(str2) && !"0".equals(str3)) {
/* 1058 */         return true;
/*      */       }
/*      */     } 
/* 1061 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static boolean CheckSION(String paramString1, String paramString2) {
/* 1071 */     boolean bool = false;
/* 1072 */     String str = "select count(licenseaffixid) s from CPBUSINESSLICENSE   where isdel='T' and licenseaffixid='" + paramString2 + "'";
/* 1073 */     if ("2".equals(paramString1)) {
/* 1074 */       str = "select count(businesstype) s from CPCOMPANYINFO where   isdel='T' and  businesstype='" + paramString2 + "'";
/* 1075 */     } else if ("3".equals(paramString1)) {
/* 1076 */       str = "select count(companyvestin) s from CPCOMPANYINFO where  isdel='T'  and  companyvestin='" + paramString2 + "'";
/*      */     } 
/* 1078 */     RecordSet recordSet = new RecordSet();
/* 1079 */     recordSet.execute(str);
/*      */     
/* 1081 */     if (recordSet.next() && recordSet.getInt("s") > 0) {
/* 1082 */       return true;
/*      */     }
/* 1084 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean CheckStale(String paramString) {
/* 1094 */     boolean bool = false;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/* 1103 */     String str = "select count(*) s from CPBUSINESSLICENSE  where isdel='T' and  companyid ='" + paramString + "'";
/* 1104 */     RecordSet recordSet = new RecordSet();
/* 1105 */     recordSet.execute(str);
/* 1106 */     if (recordSet.next() && recordSet.getInt("s") > 0) {
/* 1107 */       return true;
/*      */     }
/* 1109 */     str = "select count(*) s from CPCONSTITUTIONVERSION  where   companyid ='" + paramString + "'";
/* 1110 */     recordSet.execute(str);
/* 1111 */     if (recordSet.next() && recordSet.getInt("s") > 0) {
/* 1112 */       return true;
/*      */     }
/* 1114 */     str = "select count(*) s from CPSHAREHOLDERVERSION  where   companyid ='" + paramString + "'";
/* 1115 */     recordSet.execute(str);
/* 1116 */     if (recordSet.next() && recordSet.getInt("s") > 0) {
/* 1117 */       return true;
/*      */     }
/* 1119 */     str = "select count(*) s from CPBOARDVERSION  where   companyid ='" + paramString + "'";
/* 1120 */     recordSet.execute(str);
/* 1121 */     if (recordSet.next() && recordSet.getInt("s") > 0) {
/* 1122 */       return true;
/*      */     }
/*      */ 
/*      */ 
/*      */     
/* 1127 */     if ("sqlserver".equals(recordSet.getDBType()) || "mysql".equals(recordSet.getDBType())) {
/* 1128 */       str = "select  count(*)  s from (select (','+cpparentid) sb  from CPCOMPANYINFO ) m  where sb like '%," + paramString + ",%' ";
/*      */     } else {
/* 1130 */       str = "select count(*) s  from (select (','||cpparentid) sb  from CPCOMPANYINFO ) m  where sb like '%," + paramString + ",%' ";
/*      */     } 
/* 1132 */     recordSet.execute(str);
/* 1133 */     if (recordSet.next() && recordSet.getInt("s") > 0) {
/* 1134 */       return true;
/*      */     }
/* 1136 */     return bool;
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpcompanyinfo/CompanyInfoTransMethod.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */