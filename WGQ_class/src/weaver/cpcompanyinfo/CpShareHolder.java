/*    */ package weaver.cpcompanyinfo;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class CpShareHolder
/*    */ {
/*    */   private int shareid;
/*    */   private int companyid;
/*    */   private String boardshareholder;
/*    */   private String established;
/*    */   private String shareaffix;
/* 14 */   private String imgid = "";
/* 15 */   private String imgname = "";
/*    */   public String getImgid() {
/* 17 */     return this.imgid;
/*    */   }
/*    */   public void setImgid(String paramString) {
/* 20 */     this.imgid = paramString;
/*    */   }
/*    */   public String getImgname() {
/* 23 */     return this.imgname;
/*    */   }
/*    */   public void setImgname(String paramString) {
/* 26 */     this.imgname = paramString;
/*    */   }
/*    */   public int getShareid() {
/* 29 */     return this.shareid;
/*    */   }
/*    */   public void setShareid(int paramInt) {
/* 32 */     this.shareid = paramInt;
/*    */   }
/*    */   public int getCompanyid() {
/* 35 */     return this.companyid;
/*    */   }
/*    */   public void setCompanyid(int paramInt) {
/* 38 */     this.companyid = paramInt;
/*    */   }
/*    */   public String getBoardshareholder() {
/* 41 */     return this.boardshareholder;
/*    */   }
/*    */   public void setBoardshareholder(String paramString) {
/* 44 */     this.boardshareholder = paramString;
/*    */   }
/*    */   public String getEstablished() {
/* 47 */     return this.established;
/*    */   }
/*    */   public void setEstablished(String paramString) {
/* 50 */     this.established = paramString;
/*    */   }
/*    */   public String getShareaffix() {
/* 53 */     return this.shareaffix;
/*    */   }
/*    */   public void setShareaffix(String paramString) {
/* 56 */     this.shareaffix = paramString;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpcompanyinfo/CpShareHolder.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */