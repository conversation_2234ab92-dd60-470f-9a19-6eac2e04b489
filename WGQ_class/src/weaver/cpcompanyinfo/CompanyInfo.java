/*    */ package weaver.cpcompanyinfo;
/*    */ 
/*    */ 
/*    */ 
/*    */ public class CompanyInfo
/*    */ {
/*    */   private int companyid;
/*    */   private String companyname;
/*    */   private String companyename;
/*    */   private String companyaddress;
/*    */   private String archivenum;
/*    */   private int companyregion;
/*    */   private int businesstype;
/*    */   private String loancard;
/*    */   private String companystatu;
/*    */   private String createdatetime;
/*    */   private String lastupdatetime;
/*    */   private String isdel;
/*    */   private String companyvestin;
/*    */   
/*    */   public int getCompanyid() {
/* 22 */     return this.companyid;
/*    */   }
/*    */   public void setCompanyid(int paramInt) {
/* 25 */     this.companyid = paramInt;
/*    */   }
/*    */   public String getCompanyname() {
/* 28 */     return this.companyname;
/*    */   }
/*    */   public void setCompanyname(String paramString) {
/* 31 */     this.companyname = paramString;
/*    */   }
/*    */   public String getCompanyename() {
/* 34 */     return this.companyename;
/*    */   }
/*    */   public void setCompanyename(String paramString) {
/* 37 */     this.companyename = paramString;
/*    */   }
/*    */   public String getCompanyaddress() {
/* 40 */     return this.companyaddress;
/*    */   }
/*    */   public void setCompanyaddress(String paramString) {
/* 43 */     this.companyaddress = paramString;
/*    */   }
/*    */   public String getArchivenum() {
/* 46 */     return this.archivenum;
/*    */   }
/*    */   public void setArchivenum(String paramString) {
/* 49 */     this.archivenum = paramString;
/*    */   }
/*    */   public int getCompanyregion() {
/* 52 */     return this.companyregion;
/*    */   }
/*    */   public void setCompanyregion(int paramInt) {
/* 55 */     this.companyregion = paramInt;
/*    */   }
/*    */   public int getBusinesstype() {
/* 58 */     return this.businesstype;
/*    */   }
/*    */   public void setBusinesstype(int paramInt) {
/* 61 */     this.businesstype = paramInt;
/*    */   }
/*    */   public String getLoancard() {
/* 64 */     return this.loancard;
/*    */   }
/*    */   public void setLoancard(String paramString) {
/* 67 */     this.loancard = paramString;
/*    */   }
/*    */   public String getCompanystatu() {
/* 70 */     return this.companystatu;
/*    */   }
/*    */   public void setCompanystatu(String paramString) {
/* 73 */     this.companystatu = paramString;
/*    */   }
/*    */   public String getCreatedatetime() {
/* 76 */     return this.createdatetime;
/*    */   }
/*    */   public void setCreatedatetime(String paramString) {
/* 79 */     this.createdatetime = paramString;
/*    */   }
/*    */   public String getLastupdatetime() {
/* 82 */     return this.lastupdatetime;
/*    */   }
/*    */   public void setLastupdatetime(String paramString) {
/* 85 */     this.lastupdatetime = paramString;
/*    */   }
/*    */   public String getIsdel() {
/* 88 */     return this.isdel;
/*    */   }
/*    */   public void setIsdel(String paramString) {
/* 91 */     this.isdel = paramString;
/*    */   }
/*    */   public String getCompanyvestin() {
/* 94 */     return this.companyvestin;
/*    */   }
/*    */   public void setCompanyvestin(String paramString) {
/* 97 */     this.companyvestin = paramString;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/cpcompanyinfo/CompanyInfo.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */