/*     */ package weaver.wxinterface;
/*     */ 
/*     */ import java.lang.reflect.Method;
/*     */ import java.text.ParseException;
/*     */ import java.text.SimpleDateFormat;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Calendar;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.Set;
/*     */ import net.sf.json.JSONArray;
/*     */ import net.sf.json.JSONObject;
/*     */ import weaver.conn.ConnStatement;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.file.Prop;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ScanDocAndSend
/*     */   implements Runnable
/*     */ {
/*     */   private String reg;
/*     */   private FlowAndDoc fad;
/*     */   private SimpleDateFormat sdf;
/*     */   private BaseBean bb;
/*     */   
/*     */   public ScanDocAndSend(FlowAndDoc fad) {
/*  35 */     this.reg = "/weaver/weaver.file.FileDownload?fileid=";
/*  36 */     this.fad = fad;
/*  37 */     this.sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
/*  38 */     this.bb = new BaseBean();
/*     */   }
/*     */ 
/*     */   
/*     */   public void run() {
/*     */     try {
/*  44 */       String lastscantime = this.fad.getLastscantime();
/*  45 */       Calendar calendar = Calendar.getInstance();
/*  46 */       calendar.set(12, calendar.get(12) - 2);
/*     */       
/*  48 */       String nowDate = this.sdf.format(calendar.getTime());
/*  49 */       RecordSet rs = new RecordSet();
/*  50 */       RecordSet rs2 = new RecordSet();
/*     */       
/*  52 */       rs.execute("update WX_MsgRuleSetting set lastscantime='" + nowDate + "' where id =" + this.fad.getId());
/*  53 */       this.fad.setLastscantime(nowDate);
/*     */       
/*  55 */       if ("-1".equals(this.fad.getFlowsordocs()) && this.fad.getFlowtype() == 1) {
/*     */         return;
/*     */       }
/*     */       
/*  59 */       if (lastscantime != null && !lastscantime.equals("")) {
/*  60 */         String datetime = "", docpubtime = "";
/*  61 */         if ("sqlserver".equalsIgnoreCase(rs.getDBType())) {
/*  62 */           docpubtime = "(a.docvaliddate+' '+a.docvalidtime)";
/*  63 */           if (this.fad.getIfrepeat() == 1) {
/*  64 */             datetime = "(a.doclastmoddate+' '+a.doclastmodtime)";
/*     */           } else {
/*  66 */             datetime = "(a.doccreatedate+' '+a.doccreatetime)";
/*     */           } 
/*  68 */         } else if ("mysql".equalsIgnoreCase(rs.getDBType())) {
/*  69 */           docpubtime = "(CONCAT(a.docvaliddate,' ',a.docvalidtime))";
/*  70 */           if (this.fad.getIfrepeat() == 1) {
/*  71 */             datetime = "(CONCAT(a.doclastmoddate,' ',a.doclastmodtime))";
/*     */           } else {
/*  73 */             datetime = "(CONCAT(a.doccreatedate,' ',a.doccreatetime))";
/*     */           } 
/*     */         } else {
/*  76 */           docpubtime = "(CONCAT(CONCAT(a.docvaliddate,' '),a.docvalidtime))";
/*  77 */           if (this.fad.getIfrepeat() == 1) {
/*  78 */             datetime = "(CONCAT(CONCAT(a.doclastmoddate,' '),a.doclastmodtime))";
/*     */           } else {
/*  80 */             datetime = "(CONCAT(CONCAT(a.doccreatedate,' '),a.doccreatetime))";
/*     */           } 
/*     */         } 
/*     */         
/*  84 */         String sql = "";
/*     */         
/*  86 */         if ("sqlserver".equalsIgnoreCase(rs.getDBType())) {
/*  87 */           sql = "select a.id,a.doccreaterid,a.docsubject," + datetime + " as datetime," + docpubtime + " as docpubtime,a.doccontent" + 
/*  88 */             " from DocDetail a where 1=1 ";
/*     */         } else {
/*  90 */           sql = "select a.id,a.doccreaterid,a.docsubject," + datetime + " as datetime," + docpubtime + " as docpubtime,b.doccontent" + 
/*  91 */             " from DocDetail a,DocDetailContent b where a.id = b.docid ";
/*     */         } 
/*  93 */         sql = String.valueOf(sql) + " and (a.docstatus = 1 or a.docstatus = 2 or a.docstatus = 5) and (a.isreply!=1 or a.isreply is null) and (a.ishistory is null or a.ishistory = 0)";
/*     */         
/*  95 */         if (!"-1".equals(this.fad.getFlowsordocs())) {
/*  96 */           if (this.fad.getFlowsordocs().startsWith(",")) {
/*  97 */             this.fad.setFlowsordocs(this.fad.getFlowsordocs().substring(1));
/*     */           }
/*  99 */           if (this.fad.getFlowsordocs().endsWith(",")) {
/* 100 */             this.fad.setFlowsordocs(this.fad.getFlowsordocs().substring(0, this.fad.getFlowsordocs().length() - 1));
/*     */           }
/* 102 */           if (this.fad.getFlowtype() == 0) {
/* 103 */             sql = String.valueOf(sql) + " and seccategory in (" + this.fad.getFlowsordocs() + ")";
/*     */           } else {
/* 105 */             sql = String.valueOf(sql) + " and seccategory not in (" + this.fad.getFlowsordocs() + ")";
/*     */           } 
/*     */         } 
/* 108 */         sql = String.valueOf(sql) + " and ((" + datetime + ">='" + lastscantime + "' and " + datetime + "<'" + nowDate + "')";
/* 109 */         sql = String.valueOf(sql) + " or (" + docpubtime + ">='" + lastscantime + "' and " + docpubtime + "<'" + nowDate + "'))";
/* 110 */         this.bb.writeLog("文档扫描SQL====nowDate[" + nowDate + "]====" + sql);
/*     */         
/* 112 */         rs.execute(sql);
/* 113 */         while (rs.next()) {
/* 114 */           int id = Util.getIntValue(rs.getString("id"));
/* 115 */           String datetime2 = Util.null2String(rs.getString("datetime"));
/* 116 */           String docpubtime2 = Util.null2String(rs.getString("docpubtime"));
/* 117 */           String maxTime = "";
/* 118 */           if (datetime2.equals("")) {
/* 119 */             maxTime = docpubtime2;
/* 120 */           } else if (docpubtime2.equals("")) {
/* 121 */             maxTime = datetime2;
/*     */           }
/* 123 */           else if (TimeUtil.timeInterval(datetime2, docpubtime2) > 0L) {
/* 124 */             maxTime = docpubtime2;
/*     */           } else {
/* 126 */             maxTime = datetime2;
/*     */           } 
/*     */ 
/*     */           
/* 130 */           if (TimeUtil.timeInterval(lastscantime, maxTime) >= 0L && TimeUtil.timeInterval(maxTime, nowDate) > 0L) {
/* 131 */             boolean ifNeedSend = false;
/* 132 */             if (this.fad.getIfrepeat() == 1) {
/* 133 */               ifNeedSend = true;
/*     */             } else {
/* 135 */               rs2.executeSql("select * from WX_SCANLOG where reourceid=" + id + " and type=2");
/* 136 */               if (!rs2.next()) {
/* 137 */                 ifNeedSend = true;
/*     */               }
/*     */             } 
/* 140 */             this.bb.writeLog("文档ID[" + id + "]====ifNeedSend====" + ifNeedSend);
/* 141 */             if (ifNeedSend) {
/* 142 */               getSendUser(id, Util.getIntValue(rs.getString("doccreaterid")), 
/* 143 */                   Util.null2String(rs.getString("docsubject")), 
/* 144 */                   maxTime, Util.null2String(rs.getString("doccontent")));
/*     */             }
/*     */           } 
/*     */         } 
/*     */       } 
/* 149 */     } catch (Exception exception) {}
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void getSendUser(int docid, int doccreaterid, String docsubject, String datetime, String doccontent) {
/*     */     try {
/* 161 */       List<List<String>> sendIdList = new ArrayList<List<String>>();
/* 162 */       if ("3".equals(Integer.valueOf(this.fad.getIftoall()))) {
/* 163 */         List<String> idList = new ArrayList<String>();
/* 164 */         idList.add("@all");
/* 165 */         sendIdList.add(idList);
/*     */       } else {
/*     */         
/* 168 */         Set<String> list = null;
/*     */         try {
/* 170 */           Class<?> prepareForAll = Class.forName("weaver.system.PrepareForAll");
/* 171 */           Method m = prepareForAll.getDeclaredMethod("getUseridsSet", new Class[] { String.class, String.class });
/* 172 */           list = (Set<String>)m.invoke(prepareForAll, new Object[] { (new StringBuilder(String.valueOf(docid))).toString(), "" });
/* 173 */           if (list != null) {
/* 174 */             this.bb.writeLog("文档ID[" + docid + "],调用知识模块接口获取文档人员大小为:" + list.size());
/*     */           } else {
/* 176 */             this.bb.writeLog("文档ID[" + docid + "],调用知识模块接口获取文档人员结果为null");
/*     */           } 
/* 178 */         } catch (Exception e) {
/* 179 */           e.printStackTrace();
/* 180 */           this.bb.writeLog("文档ID[" + docid + "],调用知识模块接口获取文档人员失败,原因:" + e.getMessage());
/*     */           
/* 182 */           list = GetCanViewDocUser.getUseridsSet((new StringBuilder(String.valueOf(docid))).toString(), "");
/* 183 */           if (list != null) {
/* 184 */             this.bb.writeLog("文档ID[" + docid + "],调用云桥接口获取文档人员大小为:" + list.size());
/*     */           } else {
/* 186 */             this.bb.writeLog("文档ID[" + docid + "],调用云桥接口获取文档人员结果为null");
/*     */           } 
/*     */         } 
/* 189 */         if (list != null && list.size() > 0) {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */           
/* 201 */           if (this.fad.getIftoall() == 2) {
/* 202 */             RecordSet rs = new RecordSet();
/* 203 */             String sql = "select operateuserid from DocDetailLog l where l.docid = " + docid + " and l.operatetype=0";
/* 204 */             rs.execute(sql);
/* 205 */             while (rs.next()) {
/* 206 */               String u = rs.getString("operateuserid");
/* 207 */               if (list.contains(u)) {
/* 208 */                 list.remove(u);
/*     */               }
/*     */             } 
/*     */           } 
/*     */           
/* 213 */           if (!list.contains(Integer.valueOf(doccreaterid))) {
/* 214 */             list.add((new StringBuilder(String.valueOf(doccreaterid))).toString());
/*     */           }
/* 216 */           List<String> idList = new ArrayList<String>(list);
/* 217 */           if (this.fad.getIfbatchsend() == 1) {
/* 218 */             sendIdList = splitList(idList, (this.fad.getBatchsendnum() == 0 || this.fad.getBatchsendnum() > 898) ? 898 : this.fad.getBatchsendnum());
/*     */           } else {
/* 220 */             sendIdList = splitList(idList, 898);
/*     */           } 
/*     */         } else {
/* 223 */           this.bb.writeLog("文档ID[" + docid + "],获取要推送的人员为空");
/*     */         } 
/*     */       } 
/* 226 */       if (sendIdList.size() > 0) {
/* 227 */         this.bb.writeLog("文档ID[" + docid + "]开始执行推送");
/* 228 */         sendMsg(sendIdList, (new StringBuilder(String.valueOf(docid))).toString(), docsubject, doccontent);
/*     */       } else {
/* 230 */         this.bb.writeLog("文档ID[" + docid + "]不推送,sendIdList长度为0");
/*     */       } 
/* 232 */     } catch (Exception e) {
/* 233 */       this.bb.writeLog("文档ID[" + docid + "]getSendUser方法异常:" + e.getMessage());
/* 234 */       e.printStackTrace();
/*     */     } 
/*     */   }
/*     */   
/*     */   public static List<List<String>> splitList(List<String> list, int splitCount) {
/* 239 */     List<List<String>> retrunList = new ArrayList<List<String>>();
/* 240 */     List<String> tempList = new ArrayList<String>();
/* 241 */     if (list != null && splitCount > 0) {
/* 242 */       for (String str : list) {
/* 243 */         if (tempList.size() >= splitCount) {
/* 244 */           retrunList.add(tempList);
/* 245 */           tempList = new ArrayList<String>();
/*     */         } 
/* 247 */         if (!"".equals(str)) {
/* 248 */           tempList.add(str);
/*     */         }
/*     */       } 
/* 251 */       retrunList.add(tempList);
/*     */     } 
/* 253 */     return retrunList;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private void sendMsg(List<List<String>> sendIdList, String docid, String docsubject, String doccontent) {
/* 259 */     if (sendIdList != null && sendIdList.size() > 0) {
/* 260 */       String tpids = "";
/* 261 */       if (this.fad.getMsgtpids() != null && !"".equals(this.fad.getMsgtpids())) {
/* 262 */         String[] mtps = this.fad.getMsgtpids().split(",");
/* 263 */         if (mtps != null && mtps.length > 0) {
/* 264 */           byte b; int i; String[] arrayOfString; for (i = (arrayOfString = mtps).length, b = 0; b < i; ) { String mtp = arrayOfString[b];
/* 265 */             if (mtp != null && !"".equals(mtp))
/* 266 */               tpids = String.valueOf(tpids) + "," + mtp; 
/*     */             b++; }
/*     */         
/*     */         } 
/*     */       } 
/* 271 */       if (!tpids.equals("")) {
/* 272 */         tpids = tpids.substring(1);
/*     */       }
/* 274 */       Map<String, Object> map = new HashMap<String, Object>();
/* 275 */       map.put("ifsendsub", (new StringBuilder(String.valueOf(this.fad.getIfsendsub()))).toString());
/*     */       
/* 277 */       List<String> allUserList = new ArrayList<String>();
/* 278 */       for (List<String> sendIds : sendIdList) {
/* 279 */         allUserList.addAll(sendIds);
/*     */       }
/* 281 */       map.put("allUserList", allUserList);
/*     */       
/* 283 */       if (this.fad.getIfcover() == 1) {
/* 284 */         String fileid = dealContent(doccontent);
/* 285 */         String fileUrl = "";
/* 286 */         if (!fileid.equals("")) {
/*     */           
/* 288 */           InterfaceUtil.saveFile(fileid);
/*     */           
/* 290 */           fileUrl = InterfaceUtil.uploadFileToWx(fileid, 2);
/*     */         } 
/* 292 */         map.put("imgurl", fileUrl);
/*     */       } 
/*     */       
/* 295 */       docsubject = formatTitle(docsubject);
/* 296 */       docsubject = FormatMultiLang.format(docsubject, "7");
/*     */ 
/*     */       
/* 299 */       int ifFdSSo = Util.getIntValue(Prop.getPropValue("fdcsdev", "ifFdSSo"), 0);
/* 300 */       if (ifFdSSo == 1) {
/* 301 */         RecordSet rs = new RecordSet();
/* 302 */         rs.executeSql("select gsbm,csdw,zsdw,wzzy from cus_fielddata where id = " + docid + " and scope = 'DocCustomFieldBySecCategory'");
/* 303 */         if (rs.next()) {
/* 304 */           String gsbm = Util.null2String(rs.getString("gsbm"));
/* 305 */           String csdw = Util.null2String(rs.getString("csdw"));
/* 306 */           String zsdw = Util.null2String(rs.getString("zsdw"));
/* 307 */           String wzzy = Util.null2String(rs.getString("wzzy"));
/* 308 */           map.put("gsbm", gsbm);
/* 309 */           map.put("csdw", csdw);
/* 310 */           map.put("zsdw", zsdw);
/* 311 */           map.put("wzzy", wzzy);
/*     */         } 
/*     */       } 
/*     */       
/* 315 */       int ifZYZL = Util.getIntValue(Prop.getPropValue("zyzl_csdev", "ifZYZL"), 0);
/* 316 */       if (ifZYZL == 1) {
/*     */         try {
/* 318 */           RecordSet rs = new RecordSet();
/* 319 */           rs.executeSql("select doccreaterid,doccreatedate,doccreatetime from DocDetail where id = " + docid);
/* 320 */           if (rs.next()) {
/* 321 */             String doccreaterid = Util.null2String(rs.getString("doccreaterid"));
/* 322 */             String doccreatedate = Util.null2String(rs.getString("doccreatedate"));
/* 323 */             String doccreatetime = Util.null2String(rs.getString("doccreatetime"));
/* 324 */             String doc_content = "";
/* 325 */             if (doccontent.length() > 100) {
/* 326 */               doc_content = doccontent.substring(0, 100);
/*     */             } else {
/* 328 */               doc_content = doccontent;
/*     */             } 
/* 330 */             ResourceComInfo rc = new ResourceComInfo();
/* 331 */             String deptId = rc.getDepartmentID(doccreaterid);
/* 332 */             DepartmentComInfo dc = new DepartmentComInfo();
/* 333 */             String deptName = dc.getDepartmentname(deptId);
/* 334 */             JSONObject jo = new JSONObject();
/* 335 */             jo.put("creater", rc.getLastname(doccreaterid));
/* 336 */             jo.put("createrDept", deptName);
/* 337 */             jo.put("createTime", String.valueOf(doccreatedate) + " " + doccreatetime);
/* 338 */             jo.put("content", doc_content);
/* 339 */             map.put("csdev_zyzl", jo.toString());
/*     */           } 
/* 341 */         } catch (Exception exception) {}
/*     */       }
/*     */ 
/*     */       
/* 345 */       String sendtime = "";
/* 346 */       for (List<String> idList : sendIdList) {
/* 347 */         if (idList != null && idList.size() > 0 && 
/* 348 */           !tpids.equals("")) {
/* 349 */           if (this.fad.getIfbatchsend() == 1 && sendIdList.size() > 1) {
/* 350 */             String receiveusers = JSONArray.fromObject(idList).toString();
/* 351 */             String otherinfo = JSONObject.fromObject(map).toString();
/* 352 */             SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
/* 353 */             String nowDate = sdf.format(Long.valueOf(System.currentTimeMillis()));
/* 354 */             if ("".equals(sendtime)) {
/* 355 */               sendtime = sdf.format(Long.valueOf(System.currentTimeMillis()));
/*     */             } else {
/*     */               try {
/* 358 */                 int batchsendinterval = (this.fad.getBatchsendinterval() < 5) ? 5 : this.fad.getBatchsendinterval();
/* 359 */                 sendtime = sdf.format(Long.valueOf(sdf.parse(sendtime).getTime() + (batchsendinterval * 60 * 1000)));
/* 360 */               } catch (ParseException e) {
/* 361 */                 e.printStackTrace();
/*     */               } 
/*     */             } 
/* 364 */             String sql = "insert into WX_MsgBatchRecord(receiveusers, tpids, dataid, content, type, otherinfo, createtime, sendtime) values ('" + 
/* 365 */               receiveusers + "','" + tpids + "','" + docid + "','" + docsubject + "',2,'" + otherinfo + "','" + nowDate + "','" + sendtime + "')";
/* 366 */             ConnStatement ConnStatement = null;
/*     */             try {
/* 368 */               ConnStatement = new ConnStatement();
/* 369 */               ConnStatement.setStatementSql(sql);
/* 370 */               ConnStatement.executeUpdate();
/* 371 */             } catch (Exception e) {
/* 372 */               this.bb.writeLog("保存分批推送记录出错---数据id：" + docid + " 数据内容：" + Util.getMoreStr(docsubject, 5, "...") + " 模板：" + tpids + " 异常信息：" + e.getMessage()); continue;
/*     */             } finally {
/* 374 */               if (ConnStatement != null)
/* 375 */                 ConnStatement.close(); 
/*     */             } 
/*     */             continue;
/*     */           } 
/* 379 */           InterfaceUtil.sendMsg(idList, tpids, docid, docsubject, 2, map);
/*     */           
/*     */           try {
/* 382 */             synchronized (this) {
/* 383 */               wait(30000L);
/*     */             }
/*     */           
/* 386 */           } catch (Exception e) {
/* 387 */             e.printStackTrace();
/*     */           } 
/*     */         } 
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public String dealContent(String content) {
/* 397 */     String fileid = "";
/* 398 */     int i = content.indexOf(this.reg);
/* 399 */     if (i > -1) {
/* 400 */       String lastContent = content.substring(i + this.reg.length(), content.length());
/* 401 */       int j = lastContent.indexOf("\"");
/* 402 */       if (j == -1) {
/* 403 */         j = lastContent.indexOf("'");
/*     */       }
/* 405 */       if (j != -1) {
/* 406 */         fileid = lastContent.substring(0, j);
/* 407 */         RecordSet rs = new RecordSet();
/* 408 */         rs.executeSql("select * from imagefile where imagefileid = " + fileid);
/* 409 */         if (rs.next()) {
/* 410 */           String fileName = Util.null2String(rs.getString("imagefilename")).toUpperCase();
/* 411 */           if (fileName.endsWith("PNG") || fileName.endsWith("JPG") || fileName.endsWith("JPEG")) {
/* 412 */             return fileid;
/*     */           }
/*     */         } 
/* 415 */         content = lastContent.substring(j);
/* 416 */         fileid = dealContent(content);
/*     */       } 
/*     */     } 
/* 419 */     return fileid;
/*     */   }
/*     */   
/*     */   public String formatTitle(String resourceStr) {
/* 423 */     String retStr = "";
/*     */     try {
/* 425 */       retStr = Util.StringReplace(resourceStr, "\\", "\\\\");
/* 426 */       retStr = Util.StringReplace(retStr, "&lt;", "<");
/* 427 */       retStr = Util.StringReplace(retStr, "&gt;", ">");
/* 428 */       retStr = Util.StringReplace(retStr, "&quot;", "\"");
/* 429 */       retStr = Util.StringReplace(retStr, "\n", "\n");
/* 430 */       retStr = Util.StringReplace(retStr, "\r", "\r");
/* 431 */       retStr = Util.StringReplace(retStr, "&#8226;", "·");
/* 432 */       retStr = Util.StringReplace(retStr, "&#8226", "·");
/* 433 */     } catch (Exception e) {
/* 434 */       retStr = resourceStr;
/*     */     } 
/* 436 */     return retStr;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/wxinterface/ScanDocAndSend.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */