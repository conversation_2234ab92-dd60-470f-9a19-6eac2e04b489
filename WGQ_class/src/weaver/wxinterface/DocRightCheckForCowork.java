/*    */ package weaver.wxinterface;
/*    */ 
/*    */ import weaver.cowork.CoworkDAO;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class DocRightCheckForCowork
/*    */ {
/*    */   public static boolean checkDocRight(User user, int docid, String coworkid) {
/* 14 */     boolean canReader = false;
/*    */     try {
/* 16 */       CoworkDAO cd = new CoworkDAO();
/* 17 */       if (cd.haveViewCoworkDocRight(user.getUID(), coworkid, (new StringBuilder(String.valueOf(docid))).toString())) {
/* 18 */         cd.shareCoworkRelateddoc(Util.getIntValue(user.getLogintype()), docid, user.getUID());
/* 19 */         canReader = true;
/*    */       } 
/* 21 */     } catch (Exception exception) {}
/*    */ 
/*    */     
/* 24 */     return canReader;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/wxinterface/DocRightCheckForCowork.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */