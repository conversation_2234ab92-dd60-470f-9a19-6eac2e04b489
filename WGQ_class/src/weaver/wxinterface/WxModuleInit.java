/*     */ package weaver.wxinterface;
/*     */ 
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class WxModuleInit
/*     */   implements WxInitInterface
/*     */ {
/*     */   public static boolean ifMobileHide = false;
/*     */   public static boolean ifLastContract = false;
/*     */   public static boolean ifSeclevelmax = false;
/*     */   public static boolean ifNewQRCodeTable = false;
/*     */   public static boolean ifMeetingRemind = false;
/*     */   public static boolean ifMeetingRepeat = false;
/*     */   public static boolean ifMeetingService = false;
/*     */   public static boolean ifCoworkApprove = false;
/*     */   public static boolean ifMeetingAddressMulti = false;
/*     */   public static boolean ifMeetingRoomShare = false;
/*     */   public static boolean ifMeetingTypeShare = false;
/*     */   public static boolean ifMeetingMember = false;
/*     */   public static boolean ifMeetingDspOrder = false;
/*     */   public static boolean ifIncludesub = false;
/*     */   
/*     */   public void start() {
/*  38 */     RecordSet rs = new RecordSet();
/*     */     
/*  40 */     if ("sqlserver".equalsIgnoreCase(rs.getDBType())) {
/*  41 */       rs.execute("select 1 from syscolumns where name='mobileShowType' and id=object_id('HrmResource')");
/*  42 */     } else if ("mysql".equalsIgnoreCase(rs.getDBType())) {
/*  43 */       ifMobileHide = true;
/*     */     } else {
/*  45 */       rs.execute("select 1 from user_tab_cols where table_name='HRMRESOURCE' and column_name='MOBILESHOWTYPE'");
/*     */     } 
/*  47 */     if (!ifMobileHide && rs.next()) {
/*  48 */       ifMobileHide = true;
/*     */     }
/*     */ 
/*     */     
/*  52 */     if ("sqlserver".equalsIgnoreCase(rs.getDBType())) {
/*  53 */       rs.executeSql("select 1 from sysobjects where id = object_id('HrmResourceSelectRecord')");
/*  54 */     } else if ("mysql".equalsIgnoreCase(rs.getDBType())) {
/*  55 */       ifLastContract = true;
/*     */     } else {
/*  57 */       rs.executeSql("select 1 from user_tables where table_name = 'HRMRESOURCESELECTRECORD'");
/*     */     } 
/*  59 */     if (!ifLastContract && rs.next()) {
/*  60 */       ifLastContract = true;
/*     */     }
/*     */ 
/*     */     
/*  64 */     if ("sqlserver".equalsIgnoreCase(rs.getDBType())) {
/*  65 */       rs.execute("select 1 from syscolumns where name='seclevelmax' and id=object_id('ShareinnerDoc')");
/*  66 */     } else if ("mysql".equalsIgnoreCase(rs.getDBType())) {
/*  67 */       ifSeclevelmax = true;
/*     */     } else {
/*  69 */       rs.execute("select 1 from user_tab_cols where table_name='SHAREINNERDOC' and column_name='SECLEVELMAX'");
/*     */     } 
/*  71 */     if (!ifSeclevelmax && rs.next()) {
/*  72 */       ifSeclevelmax = true;
/*     */     }
/*     */ 
/*     */     
/*  76 */     if ("sqlserver".equalsIgnoreCase(rs.getDBType())) {
/*  77 */       rs.execute("select 1 from syscolumns where name='loginid' and id=object_id('QRCodeComInfo')");
/*  78 */     } else if ("mysql".equalsIgnoreCase(rs.getDBType())) {
/*  79 */       ifNewQRCodeTable = true;
/*     */     } else {
/*  81 */       rs.execute("select 1 from user_tab_cols where table_name='QRCODECOMINFO' and column_name='LOGINID'");
/*     */     } 
/*  83 */     if (!ifNewQRCodeTable && rs.next()) {
/*  84 */       ifNewQRCodeTable = true;
/*     */     }
/*     */ 
/*     */     
/*  88 */     if ("sqlserver".equalsIgnoreCase(rs.getDBType())) {
/*  89 */       rs.execute("select 1 from syscolumns where name='createMeetingRemindChk' and id=object_id('MeetingSet')");
/*  90 */     } else if ("mysql".equalsIgnoreCase(rs.getDBType())) {
/*  91 */       ifMeetingRemind = true;
/*     */     } else {
/*  93 */       rs.execute("select 1 from user_tab_cols where table_name='MEETINGSET' and column_name='CREATEMEETINGREMINDCHK'");
/*     */     } 
/*  95 */     if (!ifMeetingRemind && rs.next()) {
/*  96 */       ifMeetingRemind = true;
/*     */     }
/*     */ 
/*     */     
/* 100 */     if ("sqlserver".equalsIgnoreCase(rs.getDBType())) {
/* 101 */       rs.execute("select 1 from syscolumns where name='repeatType' and id=object_id('Meeting')");
/* 102 */     } else if ("mysql".equalsIgnoreCase(rs.getDBType())) {
/* 103 */       ifMeetingRepeat = true;
/*     */     } else {
/* 105 */       rs.execute("select 1 from user_tab_cols where table_name='MEETING' and column_name='REPEATTYPE'");
/*     */     } 
/* 107 */     if (!ifMeetingRepeat && rs.next()) {
/* 108 */       ifMeetingRepeat = true;
/*     */     }
/*     */ 
/*     */     
/* 112 */     if ("sqlserver".equalsIgnoreCase(rs.getDBType())) {
/* 113 */       rs.executeSql("select 1 from sysobjects where id = object_id('Meeting_Service_New')");
/* 114 */     } else if ("mysql".equalsIgnoreCase(rs.getDBType())) {
/* 115 */       ifMeetingService = true;
/*     */     } else {
/* 117 */       rs.executeSql("select 1 from user_tables where table_name = 'MEETING_SERVICE_NEW'");
/*     */     } 
/* 119 */     if (!ifMeetingService && rs.next()) {
/* 120 */       ifMeetingService = true;
/*     */     }
/*     */ 
/*     */     
/* 124 */     if ("sqlserver".equalsIgnoreCase(rs.getDBType())) {
/* 125 */       rs.execute("select 1 from syscolumns where name='approvalAtatus' and id=object_id('cowork_items')");
/* 126 */     } else if ("mysql".equalsIgnoreCase(rs.getDBType())) {
/* 127 */       ifCoworkApprove = true;
/*     */     } else {
/* 129 */       rs.execute("select 1 from user_tab_cols where table_name='COWORK_ITEMS' and column_name='APPROVALATATUS'");
/*     */     } 
/* 131 */     if (!ifCoworkApprove && rs.next()) {
/* 132 */       ifCoworkApprove = true;
/*     */     }
/*     */ 
/*     */     
/* 136 */     if ("sqlserver".equalsIgnoreCase(rs.getDBType())) {
/* 137 */       rs.execute("select t.name from sysobjects o,syscolumns c,systypes t where o.id=c.id and c.xtype=t.xtype and o.name='Meeting' and c.name='address'");
/*     */     }
/* 139 */     else if ("mysql".equalsIgnoreCase(rs.getDBType())) {
/* 140 */       ifMeetingAddressMulti = true;
/*     */     } else {
/* 142 */       rs.execute("select data_type from user_tab_cols where table_name='MEETING' and column_name='ADDRESS'");
/*     */     } 
/* 144 */     if (!ifMeetingAddressMulti && rs.next()) {
/* 145 */       String dataType = Util.null2String(rs.getString(1)).toLowerCase();
/* 146 */       if (dataType.indexOf("varchar") > -1) {
/* 147 */         ifMeetingAddressMulti = true;
/*     */       }
/*     */     } 
/*     */ 
/*     */     
/* 152 */     if ("sqlserver".equalsIgnoreCase(rs.getDBType())) {
/* 153 */       rs.executeSql("select 1 from sysobjects where id = object_id('MeetingRoom_share')");
/* 154 */     } else if ("mysql".equalsIgnoreCase(rs.getDBType())) {
/* 155 */       ifMeetingRoomShare = true;
/*     */     } else {
/* 157 */       rs.executeSql("select 1 from user_tables where table_name = 'MEETINGROOM_SHARE'");
/*     */     } 
/* 159 */     if (!ifMeetingRoomShare && rs.next()) {
/* 160 */       ifMeetingRoomShare = true;
/*     */     }
/*     */     
/* 163 */     if ("sqlserver".equalsIgnoreCase(rs.getDBType())) {
/* 164 */       rs.executeSql("select 1 from sysobjects where id = object_id('MeetingType_share')");
/* 165 */     } else if ("mysql".equalsIgnoreCase(rs.getDBType())) {
/* 166 */       ifMeetingTypeShare = true;
/*     */     } else {
/* 168 */       rs.executeSql("select 1 from user_tables where table_name = 'MEETINGTYPE_SHARE'");
/*     */     } 
/* 170 */     if (!ifMeetingTypeShare && rs.next()) {
/* 171 */       ifMeetingTypeShare = true;
/*     */     }
/*     */     
/* 174 */     if ("sqlserver".equalsIgnoreCase(rs.getDBType())) {
/* 175 */       rs.execute("select 1 from syscolumns where name='recRemark' and id=object_id('Meeting_Member2')");
/* 176 */     } else if ("mysql".equalsIgnoreCase(rs.getDBType())) {
/* 177 */       ifMeetingMember = true;
/*     */     } else {
/* 179 */       rs.execute("select 1 from user_tab_cols where table_name='MEETING_MEMBER2' and column_name='RECREMARK'");
/*     */     } 
/* 181 */     if (!ifMeetingMember && rs.next()) {
/* 182 */       ifMeetingMember = true;
/*     */     }
/*     */     
/* 185 */     if ("sqlserver".equalsIgnoreCase(rs.getDBType())) {
/* 186 */       rs.execute("select 1 from syscolumns where name='dsporder' and id=object_id('MeetingRoom')");
/* 187 */     } else if ("mysql".equalsIgnoreCase(rs.getDBType())) {
/* 188 */       ifMeetingDspOrder = true;
/*     */     } else {
/* 190 */       rs.execute("select 1 from user_tab_cols where table_name='MEETINGROOM' and column_name='DSPORDER'");
/*     */     } 
/* 192 */     if (!ifMeetingDspOrder && rs.next()) {
/* 193 */       ifMeetingDspOrder = true;
/*     */     }
/*     */     
/* 196 */     if ("sqlserver".equalsIgnoreCase(rs.getDBType())) {
/* 197 */       rs.execute("select 1 from syscolumns where name='includesub' and id=object_id('docshare')");
/* 198 */     } else if ("mysql".equalsIgnoreCase(rs.getDBType())) {
/* 199 */       ifIncludesub = true;
/*     */     } else {
/* 201 */       rs.execute("select 1 from user_tab_cols where table_name='DOCSHARE' and column_name='INCLUDESUB'");
/*     */     } 
/* 203 */     if (!ifIncludesub && rs.next())
/* 204 */       ifIncludesub = true; 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/wxinterface/WxModuleInit.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */