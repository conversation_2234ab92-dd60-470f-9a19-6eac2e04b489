/*     */ package weaver.wxinterface;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashSet;
/*     */ import java.util.List;
/*     */ import java.util.Set;
/*     */ import org.apache.commons.lang.StringUtils;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ public class GetCanViewDocUser
/*     */ {
/*     */   public static Set<String> getUseridsSet(String docid, String shareIds) {
/*  14 */     String sharetypetemp = "";
/*  15 */     String useridtemp = "";
/*  16 */     String doccreater = "";
/*  17 */     String id = "";
/*  18 */     RecordSet rs1 = new RecordSet();
/*  19 */     RecordSet rs2 = new RecordSet();
/*  20 */     String userids = "";
/*  21 */     String sqltemp = "";
/*  22 */     if ("".equals(shareIds)) {
/*  23 */       sqltemp = "select * from DocShare where docid=" + docid;
/*     */     } else {
/*  25 */       sqltemp = "select * from DocShare where docid=" + docid + " and id in (" + shareIds + ")";
/*     */     } 
/*  27 */     rs1.executeSql(sqltemp);
/*  28 */     while (rs1.next()) {
/*  29 */       sharetypetemp = rs1.getString("sharetype");
/*  30 */       id = rs1.getString("id");
/*  31 */       useridtemp = rs1.getString("userid");
/*  32 */       if ("1".equals(sharetypetemp)) {
/*  33 */         String sqltemp1 = "select s.type,s.content,s.seclevel from shareinnerdoc s,docshare d where s.sourceid=d.docid and d.sharetype=s.type and d.sharelevel=s.sharelevel and d.docid=" + docid + " and d.id=" + id + " and s.content=" + useridtemp;
/*  34 */         rs2.executeSql(sqltemp1);
/*  35 */         userids = String.valueOf(userids) + "," + getUserid(rs2, docid); continue;
/*  36 */       }  if ("2".equals(sharetypetemp)) {
/*  37 */         String str = "select s.type,s.content,s.seclevel";
/*  38 */         if (WxModuleInit.ifIncludesub) {
/*  39 */           str = String.valueOf(str) + ",d.includesub";
/*     */         }
/*  41 */         str = String.valueOf(str) + " from shareinnerdoc s,docshare d where s.sourceid=d.docid and d.sharetype=s.type and d.sharelevel=s.sharelevel and d.docid=" + docid + " and d.id=" + id + " and s.content=d.subcompanyid ";
/*  42 */         rs2.executeSql(str);
/*  43 */         userids = String.valueOf(userids) + "," + getUserid(rs2, docid); continue;
/*  44 */       }  if ("3".equals(sharetypetemp)) {
/*  45 */         String str = "select s.type,s.content,s.seclevel";
/*  46 */         if (WxModuleInit.ifIncludesub) {
/*  47 */           str = String.valueOf(str) + ",d.includesub";
/*     */         }
/*  49 */         str = String.valueOf(str) + " from shareinnerdoc s,docshare d where s.sourceid=d.docid and d.sharetype=s.type and d.sharelevel=s.sharelevel and d.docid=" + docid + " and d.id=" + id + " and s.content=d.departmentid ";
/*  50 */         rs2.executeSql(str);
/*  51 */         userids = String.valueOf(userids) + "," + getUserid(rs2, docid); continue;
/*  52 */       }  if ("4".equals(sharetypetemp)) {
/*  53 */         String str = "select s.type,s.content,s.seclevel from shareinnerdoc s,docshare d where s.sourceid=d.docid and d.sharetype=s.type and d.sharelevel=s.sharelevel and d.docid=" + docid + " and d.id=" + id + " and s.opuser=d.roleid ";
/*  54 */         rs2.executeSql(str);
/*  55 */         userids = String.valueOf(userids) + "," + getUserid(rs2, docid); continue;
/*  56 */       }  if ("5".equals(sharetypetemp)) {
/*  57 */         String str = "select s.type,s.content,s.seclevel from shareinnerdoc s,docshare d where s.sourceid=d.docid and d.sharetype=s.type and d.sharelevel=s.sharelevel and d.docid=" + docid + " and d.id=" + id;
/*  58 */         rs2.executeSql(str);
/*  59 */         userids = String.valueOf(userids) + "," + getUserid(rs2, docid); continue;
/*  60 */       }  if ("6".equals(sharetypetemp)) {
/*  61 */         String str = "select s.type,s.content,s.seclevel from shareinnerdoc s,docshare d where s.sourceid=d.docid and d.sharetype=s.type and d.sharelevel=s.sharelevel and d.docid=" + docid + " and d.id=" + id + " and s.content=d.orgGroupId ";
/*  62 */         rs2.executeSql(str);
/*  63 */         userids = String.valueOf(userids) + "," + getUserid(rs2, docid); continue;
/*  64 */       }  if ("10".equals(sharetypetemp)) {
/*  65 */         String str = "select s.type,s.content,s.seclevel,s.joblevel,s.jobdepartment,s.jobsubcompany from shareinnerdoc s,docshare d where s.sourceid=d.docid and d.sharetype=s.type and d.sharelevel=s.sharelevel and d.docid=" + docid + " and d.id=" + id + " and s.opuser=d.jobids and d.joblevel=s.joblevel and d.jobdepartment=s.jobdepartment and d.jobsubcompany=s.jobsubcompany";
/*  66 */         rs2.executeSql(str);
/*  67 */         userids = String.valueOf(userids) + "," + getUserid(rs2, docid); continue;
/*  68 */       }  if ("80".equals(sharetypetemp)) {
/*  69 */         String str = "select s.type,s.content,s.seclevel,s.srcfrom from shareinnerdoc s,docshare d where s.sourceid=d.docid and d.sharetype=s.srcfrom and d.sharelevel=s.sharelevel and d.docid=" + docid + " and d.id=" + id;
/*  70 */         rs2.executeSql(str);
/*     */         
/*  72 */         doccreater = getUserid(rs2, docid); continue;
/*  73 */       }  if ("81".equals(sharetypetemp)) {
/*  74 */         String allmanagers = rs1.getString("allmanagers");
/*  75 */         String str1 = "";
/*  76 */         if ("1".equals(allmanagers)) {
/*  77 */           str1 = "select s.type,s.content,s.seclevel,s.srcfrom from shareinnerdoc s,docshare d where s.sourceid=d.docid and d.sharetype=s.srcfrom and d.sharelevel=s.sharelevel and d.allmanagers=1 and s.sharesource !=0 and d.docid=" + docid + " and d.id=" + id;
/*     */         } else {
/*  79 */           str1 = "select s.type,s.content,s.seclevel,s.srcfrom from shareinnerdoc s,docshare d where s.sourceid=d.docid and d.sharetype=s.srcfrom and d.sharelevel=s.sharelevel and s.sharesource =0 and d.docid=" + docid + " and d.id=" + id;
/*     */         } 
/*  81 */         rs2.executeSql(str1);
/*  82 */         userids = String.valueOf(userids) + "," + getUserid(rs2, docid); continue;
/*  83 */       }  if ("84".equals(sharetypetemp)) {
/*  84 */         String str = "select s.type,s.content,s.seclevel,s.srcfrom from shareinnerdoc s,docshare d where s.sourceid=d.docid and d.sharetype=s.srcfrom and d.sharelevel=s.sharelevel and d.docid=" + docid + " and d.id=" + id;
/*  85 */         rs2.executeSql(str);
/*  86 */         userids = String.valueOf(userids) + "," + getUserid(rs2, docid); continue;
/*  87 */       }  if ("85".equals(sharetypetemp)) {
/*  88 */         String str = "select s.type,s.content,s.seclevel,s.srcfrom from shareinnerdoc s,docshare d where s.sourceid=d.docid and d.sharetype=s.srcfrom and d.sharelevel=s.sharelevel and d.docid=" + docid + " and d.id=" + id;
/*  89 */         rs2.executeSql(str);
/*  90 */         userids = String.valueOf(userids) + "," + getUserid(rs2, docid); continue;
/*     */       } 
/*  92 */       String sqltemp2 = "select s.type,s.content,s.seclevel from shareinnerdoc s,docshare d where s.sourceid=d.docid and d.sharetype=s.type and d.sharelevel=s.sharelevel and d.docid=" + docid + " and d.id=" + id;
/*  93 */       rs2.executeSql(sqltemp2);
/*  94 */       userids = String.valueOf(userids) + "," + getUserid(rs2, docid);
/*     */     } 
/*     */     
/*  97 */     Set<String> useridsset = null;
/*  98 */     if (!"".equals(userids)) {
/*  99 */       useridsset = new HashSet<String>();
/* 100 */       userids = userids.substring(1);
/* 101 */       String[] useridarr = userids.split(",");
/* 102 */       for (int i = 0; i < useridarr.length; i++) {
/* 103 */         if (!"".equals(useridarr[i]) && !doccreater.equals(useridarr[i])) {
/* 104 */           useridsset.add(useridarr[i]);
/*     */         }
/*     */       } 
/*     */     } 
/* 108 */     return useridsset;
/*     */   }
/*     */   
/*     */   public static String getUserid(RecordSet recordSet, String docid) {
/* 112 */     RecordSet rs = new RecordSet();
/* 113 */     String hrmStatusSql = " and a.status in (0,1,2,3,10) ";
/* 114 */     String sharetype = "";
/* 115 */     String shareContent = "";
/* 116 */     String shareSeclevel = "";
/* 117 */     String includesub = "";
/* 118 */     String sqlstr = "select a.id,b.docsubject from hrmresource a,DocDetail b where 1 = 2" + hrmStatusSql;
/* 119 */     while (recordSet.next()) {
/* 120 */       sharetype = recordSet.getString("type");
/* 121 */       shareContent = recordSet.getString("content");
/* 122 */       shareSeclevel = recordSet.getString("seclevel");
/* 123 */       if ("1".equals(sharetype)) {
/* 124 */         sqlstr = String.valueOf(sqlstr) + " union select a.id,b.docsubject from hrmresource a,DocDetail b where a.id=" + shareContent + " and b.id=" + docid + hrmStatusSql; continue;
/* 125 */       }  if ("2".equals(sharetype)) {
/* 126 */         includesub = recordSet.getString("includesub");
/* 127 */         if (Integer.valueOf(shareContent).intValue() < 0) {
/* 128 */           if ("1".equals(includesub)) {
/* 129 */             sqlstr = String.valueOf(sqlstr) + " union select a.resourceid,b.docsubject from HrmResourceVirtual a,DocDetail b where  b.id=" + docid + " " + getAllvirtualSubCompany(shareContent); continue;
/*     */           } 
/* 131 */           sqlstr = String.valueOf(sqlstr) + " union select a.resourceid,b.docsubject from HrmResourceVirtual a,DocDetail b where a.subcompanyid=" + shareContent + " and b.id=" + docid;
/*     */           continue;
/*     */         } 
/* 134 */         if ("1".equals(includesub)) {
/* 135 */           sqlstr = String.valueOf(sqlstr) + " union select a.id,b.docsubject from hrmresource a,DocDetail b where (a.seclevel >= " + shareSeclevel + " " + getAllSubCompany(shareContent) + ") and b.id=" + docid + hrmStatusSql; continue;
/*     */         } 
/* 137 */         sqlstr = String.valueOf(sqlstr) + " union select a.id,b.docsubject from hrmresource a,DocDetail b where (a.seclevel >= " + shareSeclevel + " and a.subcompanyid1=" + shareContent + ") and b.id=" + docid + hrmStatusSql;
/*     */         continue;
/*     */       } 
/* 140 */       if ("3".equals(sharetype)) {
/* 141 */         includesub = recordSet.getString("includesub");
/* 142 */         if (Integer.valueOf(shareContent).intValue() < 0) {
/* 143 */           if ("1".equals(includesub)) {
/* 144 */             sqlstr = String.valueOf(sqlstr) + " union select a.resourceid,b.docsubject from HrmResourceVirtual a,DocDetail b where  b.id=" + docid + " " + getAllvirtualSubDepartment(shareContent); continue;
/*     */           } 
/* 146 */           sqlstr = String.valueOf(sqlstr) + " union select a.resourceid,b.docsubject from HrmResourceVirtual a,DocDetail b where a.departmentid=" + shareContent + " and b.id=" + docid;
/*     */           continue;
/*     */         } 
/* 149 */         if ("1".equals(includesub)) {
/* 150 */           sqlstr = String.valueOf(sqlstr) + " union select a.id,b.docsubject from hrmresource a,DocDetail b where (a.seclevel >= " + shareSeclevel + " " + getAllSubDepartment(shareContent) + ") and b.id=" + docid + hrmStatusSql; continue;
/*     */         } 
/* 152 */         sqlstr = String.valueOf(sqlstr) + " union select a.id,b.docsubject from hrmresource a,DocDetail b where (a.seclevel >= " + shareSeclevel + "and a.departmentid=" + shareContent + ") and b.id=" + docid + hrmStatusSql;
/*     */         continue;
/*     */       } 
/* 155 */       if ("4".equals(sharetype)) {
/*     */         
/* 157 */         StringBuffer sb = new StringBuffer(shareContent);
/* 158 */         String str = sb.reverse().toString().trim();
/* 159 */         String fourstr = str.substring(0, 1);
/* 160 */         StringBuffer nb = new StringBuffer(fourstr);
/* 161 */         String fanstr = nb.reverse().toString();
/*     */         
/* 163 */         sqlstr = String.valueOf(sqlstr) + " union select a.id,b.docsubject from hrmresource a,DocDetail b where a.id in(select resourceid from hrmrolemembers where roleid =" + shareContent.substring(0, shareContent.length() - 1) + " and rolelevel=" + fanstr + ")  and b.id =" + docid + hrmStatusSql; continue;
/* 164 */       }  if ("5".equals(sharetype)) {
/* 165 */         sqlstr = String.valueOf(sqlstr) + " union select a.id,b.docsubject from hrmresource a,DocDetail b where a.seclevel >= " + shareSeclevel + " and b.id =" + docid + hrmStatusSql; continue;
/* 166 */       }  if ("6".equals(sharetype)) {
/* 167 */         rs.executeSql("select type,content from HrmOrgGroupRelated where orgGroupId=" + shareContent);
/* 168 */         while (rs.next()) {
/* 169 */           String type = rs.getString("type");
/* 170 */           String content = rs.getString("content");
/* 171 */           if ("2".equals(type)) {
/* 172 */             sqlstr = String.valueOf(sqlstr) + " union select a.id,b.docsubject from hrmresource a,DocDetail b where (a.subcompanyid1=" + content + " and a.seclevel >= " + shareSeclevel + ") and b.id=" + docid + hrmStatusSql; continue;
/*     */           } 
/* 174 */           sqlstr = String.valueOf(sqlstr) + " union select a.id,b.docsubject from hrmresource a,DocDetail b where (a.departmentid=" + content + " and a.seclevel >= " + shareSeclevel + ") and b.id=" + docid + hrmStatusSql;
/*     */         }  continue;
/*     */       } 
/* 177 */       if ("10".equals(sharetype)) {
/* 178 */         String joblevel = recordSet.getString("joblevel");
/* 179 */         String jobdepartment = recordSet.getString("jobdepartment");
/* 180 */         String jobsubcompany = recordSet.getString("jobsubcompany");
/* 181 */         if ("3".equals(joblevel)) {
/* 182 */           sqlstr = String.valueOf(sqlstr) + " union select a.id,b.docsubject from hrmresource a,DocDetail b where (a.departmentid=" + jobdepartment + " and a.seclevel >= " + shareSeclevel + ") and a.jobtitle=" + shareContent + " and b.id=" + docid + hrmStatusSql; continue;
/* 183 */         }  if ("2".equals(joblevel)) {
/* 184 */           sqlstr = String.valueOf(sqlstr) + " union select a.id,b.docsubject from hrmresource a,DocDetail b where (a.subcompanyid1=" + jobsubcompany + " and a.seclevel >= " + shareSeclevel + ") and a.jobtitle=" + shareContent + " and b.id=" + docid + hrmStatusSql; continue;
/*     */         } 
/* 186 */         sqlstr = String.valueOf(sqlstr) + " union select a.id,b.docsubject from hrmresource a,DocDetail b where (a.jobtitle=" + shareContent + " and a.seclevel >= " + shareSeclevel + ") and b.id=" + docid + hrmStatusSql;
/*     */         continue;
/*     */       } 
/* 189 */       sqlstr = String.valueOf(sqlstr) + " union select a.id,b.docsubject from hrmresource a,DocDetail b where a.seclevel >= " + shareSeclevel + " and b.id =" + docid + hrmStatusSql;
/*     */     } 
/*     */     
/* 192 */     recordSet.executeSql(sqlstr);
/* 193 */     List<String> useridsList = new ArrayList<String>();
/* 194 */     while (recordSet.next()) {
/* 195 */       useridsList.add(recordSet.getString("id"));
/*     */     }
/* 197 */     String userid = StringUtils.join(useridsList, ",");
/* 198 */     return userid;
/*     */   }
/*     */ 
/*     */   
/*     */   public static String getAllvirtualSubCompany(String subcompanyid) {
/* 203 */     RecordSet rs = new RecordSet();
/* 204 */     StringBuffer subcompanyidstemp = new StringBuffer();
/* 205 */     String subcompanyids = "";
/* 206 */     subcompanyidstemp.append("");
/* 207 */     boolean isoracle = rs.getDBType().equals("oracle");
/* 208 */     String sql = "";
/* 209 */     if (isoracle) {
/* 210 */       sql = "select id,supsubcomid from HrmSubCompanyVirtual start with id=" + subcompanyid + " connect by prior id=supsubcomid";
/*     */     } else {
/* 212 */       sql = " with cte(id,supsubcomid) as (select id,supsubcomid from HrmSubCompanyVirtual where id = " + subcompanyid + " union all select t.id,t.supsubcomid from HrmSubCompanyVirtual as t inner join cte as c on t.supsubcomid = c.id) select id,supsubcomid from cte ";
/*     */     } 
/* 214 */     rs.executeSql(sql);
/* 215 */     while (rs.next()) {
/* 216 */       subcompanyidstemp.append("," + rs.getString("id"));
/*     */     }
/* 218 */     String append = "";
/* 219 */     if (!"".equals(subcompanyidstemp.toString())) {
/* 220 */       subcompanyids = subcompanyidstemp.toString().substring(1);
/*     */     }
/* 222 */     String[] secidtemps = Util.TokenizerString2(subcompanyids, ",");
/* 223 */     List<String> ls = new ArrayList<String>(); byte b; int i; String[] arrayOfString1;
/* 224 */     for (i = (arrayOfString1 = secidtemps).length, b = 0; b < i; ) { String tmpid = arrayOfString1[b]; ls.add(tmpid); b++; }
/* 225 */      StringBuilder builder = new StringBuilder();
/* 226 */     int len = secidtemps.length;
/* 227 */     if (len < 1000) {
/* 228 */       append = "  and a.subcompanyid  in (" + subcompanyids + ")";
/*     */     } else {
/* 230 */       int count = 1000;
/* 231 */       int size = len % count;
/* 232 */       if (size == 0) {
/* 233 */         size = len / count;
/*     */       } else {
/* 235 */         size = len / count + 1;
/*     */       } 
/*     */       
/* 238 */       for (int j = 0; j < size; j++) {
/* 239 */         int fromIndex = j * count;
/* 240 */         int toIndex = Math.min(fromIndex + count, len);
/* 241 */         List<String> tmpls = ls.subList(fromIndex, toIndex);
/* 242 */         if (j != 0) {
/* 243 */           builder.append(" or ");
/*     */         } else {
/* 245 */           builder.append(" and ");
/*     */         } 
/* 247 */         builder.append("  a.subcompanyid in (").append(tmpls.toString().replace("[", "").replace("]", "")).append(")");
/*     */       } 
/* 249 */       append = builder.toString();
/*     */     } 
/* 251 */     return append;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getAllSubCompany(String subcompanyid) {
/* 257 */     RecordSet rs = new RecordSet();
/* 258 */     StringBuffer subcompanyidstemp = new StringBuffer();
/* 259 */     String subcompanyids = "";
/* 260 */     subcompanyidstemp.append("");
/* 261 */     boolean isoracle = rs.getDBType().equals("oracle");
/* 262 */     String sql = "";
/* 263 */     if (isoracle) {
/* 264 */       sql = "select id,supsubcomid from hrmsubcompany start with id=" + subcompanyid + " connect by prior id=supsubcomid";
/*     */     } else {
/* 266 */       sql = " with cte(id,supsubcomid) as (select id,supsubcomid from HrmSubCompany where id = " + subcompanyid + " union all select t.id,t.supsubcomid from HrmSubCompany as t inner join cte as c on t.supsubcomid = c.id) select id,supsubcomid from cte ";
/*     */     } 
/* 268 */     rs.executeSql(sql);
/* 269 */     while (rs.next()) {
/* 270 */       subcompanyidstemp.append("," + rs.getString("id"));
/*     */     }
/* 272 */     String append = "";
/* 273 */     if (!"".equals(subcompanyidstemp.toString())) {
/* 274 */       subcompanyids = subcompanyidstemp.toString().substring(1);
/*     */     }
/* 276 */     String[] secidtemps = Util.TokenizerString2(subcompanyids, ",");
/* 277 */     List<String> ls = new ArrayList<String>(); byte b; int i; String[] arrayOfString1;
/* 278 */     for (i = (arrayOfString1 = secidtemps).length, b = 0; b < i; ) { String tmpid = arrayOfString1[b]; ls.add(tmpid); b++; }
/* 279 */      StringBuilder builder = new StringBuilder();
/* 280 */     int len = secidtemps.length;
/* 281 */     if (len < 1000) {
/* 282 */       append = "  and a.subcompanyid1  in (" + subcompanyids + ")";
/*     */     } else {
/* 284 */       int count = 1000;
/* 285 */       int size = len % count;
/* 286 */       if (size == 0) {
/* 287 */         size = len / count;
/*     */       } else {
/* 289 */         size = len / count + 1;
/*     */       } 
/*     */       
/* 292 */       for (int j = 0; j < size; j++) {
/* 293 */         int fromIndex = j * count;
/* 294 */         int toIndex = Math.min(fromIndex + count, len);
/* 295 */         List<String> tmpls = ls.subList(fromIndex, toIndex);
/* 296 */         if (j != 0) {
/* 297 */           builder.append(" or ");
/*     */         } else {
/* 299 */           builder.append(" and ");
/*     */         } 
/* 301 */         builder.append("  a.subcompanyid1 in (").append(tmpls.toString().replace("[", "").replace("]", "")).append(")");
/*     */       } 
/* 303 */       append = builder.toString();
/*     */     } 
/* 305 */     return append;
/*     */   }
/*     */   
/*     */   public static String getAllvirtualSubDepartment(String departmentid) {
/* 309 */     RecordSet rs = new RecordSet();
/* 310 */     StringBuffer departmentidstemp = new StringBuffer();
/* 311 */     String departmentids = "";
/* 312 */     departmentidstemp.append("");
/* 313 */     boolean isoracle = rs.getDBType().equals("oracle");
/* 314 */     String sql = "";
/* 315 */     if (isoracle) {
/* 316 */       sql = "select id,supdepid from hrmdepartmentvirtual start with id=" + departmentid + " connect by prior id=supdepid";
/*     */     } else {
/* 318 */       sql = " with cte(id,supdepid) as (select id,supdepid from hrmdepartmentvirtual where id = " + departmentid + " union all select t.id,t.supdepid from hrmdepartmentvirtual as t inner join cte as c on t.supdepid = c.id) select id,supdepid from cte ";
/*     */     } 
/* 320 */     rs.executeSql(sql);
/* 321 */     while (rs.next()) {
/* 322 */       departmentidstemp.append("," + rs.getString("id"));
/*     */     }
/* 324 */     String append = "";
/* 325 */     if (!"".equals(departmentidstemp.toString())) {
/* 326 */       departmentids = departmentidstemp.toString().substring(1);
/*     */     }
/* 328 */     String[] secidtemps = Util.TokenizerString2(departmentids, ",");
/* 329 */     List<String> ls = new ArrayList<String>(); byte b; int i; String[] arrayOfString1;
/* 330 */     for (i = (arrayOfString1 = secidtemps).length, b = 0; b < i; ) { String tmpid = arrayOfString1[b]; ls.add(tmpid); b++; }
/* 331 */      StringBuilder builder = new StringBuilder();
/* 332 */     int len = secidtemps.length;
/* 333 */     if (len < 1000) {
/* 334 */       append = "  and a.departmentid  in (" + departmentids + ")";
/*     */     } else {
/* 336 */       int count = 1000;
/* 337 */       int size = len % count;
/* 338 */       if (size == 0) {
/* 339 */         size = len / count;
/*     */       } else {
/* 341 */         size = len / count + 1;
/*     */       } 
/*     */       
/* 344 */       for (int j = 0; j < size; j++) {
/* 345 */         int fromIndex = j * count;
/* 346 */         int toIndex = Math.min(fromIndex + count, len);
/* 347 */         List<String> tmpls = ls.subList(fromIndex, toIndex);
/* 348 */         if (j != 0) {
/* 349 */           builder.append(" or ");
/*     */         } else {
/* 351 */           builder.append(" and ");
/*     */         } 
/* 353 */         builder.append("  a.departmentid in (").append(tmpls.toString().replace("[", "").replace("]", "")).append(")");
/*     */       } 
/* 355 */       append = builder.toString();
/*     */     } 
/* 357 */     return append;
/*     */   }
/*     */   
/*     */   public static String getAllSubDepartment(String departmentid) {
/* 361 */     RecordSet rs = new RecordSet();
/* 362 */     StringBuffer departmentidstemp = new StringBuffer();
/* 363 */     String departmentids = "";
/* 364 */     departmentidstemp.append("");
/* 365 */     boolean isoracle = rs.getDBType().equals("oracle");
/* 366 */     String sql = "";
/* 367 */     if (isoracle) {
/* 368 */       sql = "select id,supdepid from hrmdepartment start with id=" + departmentid + " connect by prior id=supdepid";
/*     */     } else {
/* 370 */       sql = " with cte(id,supdepid) as (select id,supdepid from hrmdepartment where id = " + departmentid + " union all select t.id,t.supdepid from hrmdepartment as t inner join cte as c on t.supdepid = c.id) select id,supdepid from cte ";
/*     */     } 
/* 372 */     rs.executeSql(sql);
/* 373 */     while (rs.next()) {
/* 374 */       departmentidstemp.append("," + rs.getString("id"));
/*     */     }
/* 376 */     String append = "";
/* 377 */     if (!"".equals(departmentidstemp.toString())) {
/* 378 */       departmentids = departmentidstemp.toString().substring(1);
/*     */     }
/* 380 */     String[] secidtemps = Util.TokenizerString2(departmentids, ",");
/* 381 */     List<String> ls = new ArrayList<String>(); byte b; int i; String[] arrayOfString1;
/* 382 */     for (i = (arrayOfString1 = secidtemps).length, b = 0; b < i; ) { String tmpid = arrayOfString1[b]; ls.add(tmpid); b++; }
/* 383 */      StringBuilder builder = new StringBuilder();
/* 384 */     int len = secidtemps.length;
/* 385 */     if (len < 1000) {
/* 386 */       append = "  and a.departmentid  in (" + departmentids + ")";
/*     */     } else {
/* 388 */       int count = 1000;
/* 389 */       int size = len % count;
/* 390 */       if (size == 0) {
/* 391 */         size = len / count;
/*     */       } else {
/* 393 */         size = len / count + 1;
/*     */       } 
/*     */       
/* 396 */       for (int j = 0; j < size; j++) {
/* 397 */         int fromIndex = j * count;
/* 398 */         int toIndex = Math.min(fromIndex + count, len);
/* 399 */         List<String> tmpls = ls.subList(fromIndex, toIndex);
/* 400 */         if (j != 0) {
/* 401 */           builder.append(" or ");
/*     */         } else {
/* 403 */           builder.append(" and ");
/*     */         } 
/* 405 */         builder.append("  a.departmentid in (").append(tmpls.toString().replace("[", "").replace("]", "")).append(")");
/*     */       } 
/* 407 */       append = builder.toString();
/*     */     } 
/* 409 */     return append;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/wxinterface/GetCanViewDocUser.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */