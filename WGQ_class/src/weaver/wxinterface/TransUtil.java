/*     */ package weaver.wxinterface;
/*     */ 
/*     */ import java.io.File;
/*     */ import java.util.List;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.crm.Maint.CustomerInfoComInfo;
/*     */ import weaver.docs.docs.DocComInfo;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.company.SubCompanyComInfo;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.workflow.request.RequestComInfo;
/*     */ 
/*     */ public class TransUtil
/*     */   extends BaseBean
/*     */ {
/*  19 */   private ResourceComInfo rc = null;
/*  20 */   private CustomerInfoComInfo ci = null;
/*  21 */   private DocComInfo doc = null;
/*  22 */   private RequestComInfo request = null;
/*  23 */   private SubCompanyComInfo subcompany = null;
/*  24 */   private DepartmentComInfo department = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCoWechatStatus(String value) {
/*  37 */     if ("1".equals(value)) {
/*  38 */       return "启用";
/*     */     }
/*  40 */     return "关闭";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCoWeChatLink(String id, String name) {
/*  51 */     String link = "";
/*  52 */     if (id != null && !"".equals(id)) {
/*  53 */       if (name == null || "".equals(name)) name = "未命名"; 
/*  54 */       link = "<a href=\"javascript:doSetCoWechat('" + id + "')\">" + name + "</a>";
/*     */     } 
/*  56 */     return link;
/*     */   }
/*     */   
/*     */   public String getOperate(String key, String returnStr) {
/*  60 */     if (!"".equals(returnStr)) {
/*  61 */       return returnStr.replace("${key}", key);
/*     */     }
/*  63 */     return key;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getName(String key, String type, String jsfuntcion) {
/*  68 */     String returnstr = "";
/*  69 */     if (!"cowechat".equals(type))
/*     */     {
/*     */       
/*  72 */       if ("agent".equals(type)) {
/*     */         
/*  74 */         if (Util.getIntValue(key, 0) != 0) {
/*  75 */           RecordSet rs = new RecordSet();
/*  76 */           rs.execute("select name from cowechat_agent where id='" + key + "'");
/*  77 */           if (rs.next())
/*  78 */             returnstr = rs.getString("name"); 
/*     */         } 
/*     */       } else {
/*  81 */         if ("yesorno".equals(type)) {
/*     */           
/*  83 */           if ("1".equals(key)) {
/*  84 */             return "是";
/*     */           }
/*  86 */           return "否";
/*     */         } 
/*  88 */         if ("userstatus".equals(type)) {
/*     */           
/*  90 */           if ("1".equals(key)) {
/*  91 */             returnstr = "已关注";
/*  92 */           } else if ("2".equals(key)) {
/*  93 */             returnstr = "已冻结";
/*  94 */           } else if ("4".equals(key)) {
/*  95 */             returnstr = "未关注";
/*     */           } else {
/*  97 */             returnstr = "未绑定";
/*     */           } 
/*  99 */         } else if ("function".equals(type)) {
/* 100 */           if (Util.getIntValue(key, 0) != 0) {
/* 101 */             RecordSet rs = new RecordSet();
/* 102 */             rs.execute("select label from cowechat_emobilemodule where id='" + key + "'");
/* 103 */             if (rs.next()) {
/* 104 */               returnstr = rs.getString("label");
/*     */             }
/*     */           }
/*     */         
/* 108 */         } else if (!"6".equals(type)) {
/*     */ 
/*     */           
/* 111 */           returnstr = key;
/*     */         } 
/* 113 */       }  }  if ("".equals(returnstr)) returnstr = "未命名"; 
/* 114 */     if (!"0".equals(jsfuntcion)) {
/* 115 */       returnstr = "<span class='class_" + type + "'><a href='javascript:" + jsfuntcion + "('" + key + "')'>" + returnstr + "</a></span>";
/*     */     }
/* 117 */     System.out.println("type:" + type);
/* 118 */     return returnstr;
/*     */   }
/*     */   
/*     */   public String getStatus(String key, String type) {
/* 122 */     String returnstr = "";
/* 123 */     if ("userstatus".equals(type))
/*     */     {
/* 125 */       if ("1".equals(key)) {
/* 126 */         returnstr = "已关注";
/* 127 */       } else if ("2".equals(key)) {
/* 128 */         returnstr = "已冻结";
/* 129 */       } else if ("4".equals(key)) {
/* 130 */         returnstr = "未关注";
/*     */       } else {
/* 132 */         returnstr = "未绑定";
/*     */       } 
/*     */     }
/* 135 */     return returnstr;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCoWechatOperate(String id, String imagefielid) {
/* 144 */     String returnstr = "";
/* 145 */     if (id != null && !"".equals(id)) {
/* 146 */       returnstr = "<span class='operatespan'><a class='a_op' href='javascript:doSetCoWechat(" + id + ")'>设置</a>&nbsp;" + 
/* 147 */         "<a class='a_op' href='javascript:doGetQrCode('" + id + "'," + imagefielid + ")'>关注</a></span>";
/*     */     }
/* 149 */     return returnstr;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCoWechatAgentOperate(String id) {
/* 160 */     String returnstr = "";
/* 161 */     if (id != null && !"".equals(id)) {
/* 162 */       returnstr = "<span class='operatespan'><a class='a_op' href=\"javascript:doSetCoWechatAgent('" + id + "')\">设置</a>";
/*     */     }
/* 164 */     return returnstr;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCoWeChatAgentLink(String id, String name) {
/* 173 */     String link = "";
/* 174 */     if (id != null && !"".equals(id)) {
/* 175 */       if (name == null || "".equals(name)) name = "未命名"; 
/* 176 */       link = "<a href=\"javascript:doSetCoWechatAgent('" + id + "')\">" + name + "</a>";
/*     */     } 
/* 178 */     return link;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCoWechatFunctionOperate(String id) {
/* 188 */     String returnstr = "";
/* 189 */     if (id != null && !"".equals(id)) {
/* 190 */       returnstr = "<span class='operatespan'><a class='a_op' href=\"javascript:doSetCoWechatFunction('" + id + "')\">设置</a>";
/*     */     }
/* 192 */     return returnstr;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCoWeChatFunctionLink(String id, String name) {
/* 201 */     String link = "";
/* 202 */     if (id != null && !"".equals(id)) {
/* 203 */       if (name == null || "".equals(name)) name = "未命名"; 
/* 204 */       link = "<a href=\"javascript:doSetCoWechatFunction('" + id + "')\">" + name + "</a>";
/*     */     } 
/* 206 */     return link;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCoWechatFunctionHascontext(String value) {
/* 216 */     if ("1".equals(value)) {
/* 217 */       return "是";
/*     */     }
/* 219 */     return "否";
/*     */   }
/*     */ 
/*     */   
/*     */   public String getAgentName(String agentid) {
/* 224 */     String name = "";
/* 225 */     if (!agentid.equals("")) {
/* 226 */       RecordSet rs = new RecordSet();
/* 227 */       rs.execute("select name from cowechat_agent where id='" + agentid + "'");
/* 228 */       if (rs.next()) {
/* 229 */         name = rs.getString("name");
/*     */       }
/*     */     } 
/* 232 */     return name;
/*     */   }
/*     */   
/*     */   public String getFuncType(String funcType) {
/* 236 */     if (funcType.equals("0"))
/* 237 */       return "泛微手机平台应用"; 
/* 238 */     if (funcType.equals("1")) {
/* 239 */       return "泛微EC平台应用";
/*     */     }
/* 241 */     return "";
/*     */   }
/*     */   
/*     */   public String getMsgFunctionName(String bindfunction, String functype) {
/* 245 */     String name = "";
/* 246 */     if (!bindfunction.equals("") && !"".equals(functype)) {
/* 247 */       RecordSet rs = new RecordSet();
/* 248 */       String sql = "";
/* 249 */       if (functype.equals("0")) {
/* 250 */         sql = "select label as name from cowechat_emobilemodule where id='" + bindfunction + "'";
/* 251 */       } else if (functype.equals("1")) {
/* 252 */         sql = "select name from cowechat_mobileconfig where id='" + bindfunction + "'";
/*     */       } 
/* 254 */       rs.execute(sql);
/* 255 */       if (rs.next()) {
/* 256 */         name = rs.getString("name");
/*     */       }
/*     */     } 
/* 259 */     return name;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getYesOrNo(String value) {
/* 264 */     if ("1".equals(value)) {
/* 265 */       return "是";
/*     */     }
/* 267 */     return "否";
/*     */   }
/*     */   
/*     */   public String editMsgTp(String strs, String name) {
/* 271 */     String link = "";
/* 272 */     String[] str = strs.split("\\+");
/* 273 */     if (str != null && str.length == 6) {
/* 274 */       link = "<a href=\"javascript:editMsgTp('" + str[0] + "','" + str[1] + "','" + str[2] + "','" + str[3] + "','" + str[4] + "')\">" + name + "</a>";
/*     */     }
/* 276 */     return link;
/*     */   }
/*     */   
/*     */   public boolean getCanDelMsgTp(String msgtype) {
/* 280 */     if ("0".equals(msgtype))
/* 281 */       return true; 
/* 282 */     return false;
/*     */   }
/*     */   
/*     */   public String getUserOperate(String id, String status) {
/* 286 */     String returnstr = "";
/* 287 */     if ("1".equals(status) || "4".equals(status)) {
/* 288 */       returnstr = "<a href=\"javascript:forbidUser('" + id + "')\">禁用</a>&nbsp;&nbsp;" + 
/* 289 */         "<a href=\"javascript:delUser('" + id + "')\">删除</a>";
/* 290 */     } else if ("2".equals(status)) {
/* 291 */       returnstr = "<a href=\"javascript:openUser('" + id + "')\">启用</a>&nbsp;&nbsp;" + 
/* 292 */         "<a href=\"javascript:delUser('" + id + "')\">删除</a>";
/*     */     } else {
/* 294 */       returnstr = "<a href=\"javascript:bindUser('" + id + "')\">绑定</a>";
/*     */     } 
/* 296 */     return returnstr;
/*     */   }
/*     */   
/*     */   public String getMConfigModuleName(String id) {
/* 300 */     String name = "";
/* 301 */     if (id.equals("1")) {
/* 302 */       name = "代办";
/* 303 */     } else if (id.equals("2")) {
/* 304 */       name = "新闻";
/* 305 */     } else if (id.equals("3")) {
/* 306 */       name = "公告";
/* 307 */     } else if (id.equals("4")) {
/* 308 */       name = "日程";
/* 309 */     } else if (id.equals("5")) {
/* 310 */       name = "会议";
/* 311 */     } else if (id.equals("6")) {
/* 312 */       name = "通信录";
/* 313 */     } else if (id.equals("7")) {
/* 314 */       name = "办结事宜";
/* 315 */     } else if (id.equals("8")) {
/* 316 */       name = "已办事宜";
/* 317 */     } else if (id.equals("9")) {
/* 318 */       name = "我的请求";
/* 319 */     } else if (id.equals("10")) {
/* 320 */       name = "抄送事宜";
/*     */     } 
/* 322 */     return name;
/*     */   }
/*     */   
/*     */   public String getMConfigVisible(String visible) {
/* 326 */     if (visible.equals("1")) {
/* 327 */       return "是";
/*     */     }
/* 329 */     return "否";
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public String getWxDeptName(String deptid, String cowechatid) {
/* 335 */     if (!"".equals(deptid)) {
/* 336 */       RecordSet rs = new RecordSet();
/* 337 */       rs.execute("select orgname from cowechat_org where cowechatorgid=" + deptid + " and cowechatid='" + cowechatid + "'");
/* 338 */       if (rs.next()) {
/* 339 */         return rs.getString(1);
/*     */       }
/*     */     } 
/* 342 */     return "";
/*     */   }
/*     */   
/*     */   public String getWxPDeptName(String deptid, String cowechatid) {
/* 346 */     if (!"".equals(deptid)) {
/* 347 */       RecordSet rs = new RecordSet();
/* 348 */       rs.execute("select orgname from cowechat_org where cowechatorgid=(select cowechatorgpid from cowechat_org where cowechatorgid =" + 
/* 349 */           deptid + " and cowechatid='" + cowechatid + "')" + 
/* 350 */           " and cowechatid='" + cowechatid + "'");
/* 351 */       if (rs.next()) {
/* 352 */         return rs.getString(1);
/*     */       }
/*     */     } 
/* 355 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getWDTypeName(String type) {
/* 364 */     if (type.equals("1"))
/* 365 */       return "流程"; 
/* 366 */     if (type.equals("2"))
/* 367 */       return "文档"; 
/* 368 */     if (type.equals("3"))
/* 369 */       return "消息"; 
/* 370 */     if (type.equals("4"))
/* 371 */       return "协作"; 
/* 372 */     if (type.equals("5"))
/* 373 */       return "日程"; 
/* 374 */     if (type.equals("6"))
/* 375 */       return "会议"; 
/* 376 */     if (type.equals("7"))
/* 377 */       return "项目"; 
/* 378 */     if (type.equals("8"))
/* 379 */       return "客户"; 
/* 380 */     if (type.equals("9"))
/* 381 */       return "任务"; 
/* 382 */     if (type.equals("10"))
/* 383 */       return "微博"; 
/* 384 */     if (type.equals("11"))
/* 385 */       return "邮件"; 
/* 386 */     if (type.equals("12"))
/* 387 */       return "微信新闻群发"; 
/* 388 */     if (type.equals("13"))
/* 389 */       return "文档群发"; 
/* 390 */     if (type.equals("14"))
/* 391 */       return "调查问卷"; 
/* 392 */     if (type.equals("15")) {
/* 393 */       return "移动建模";
/*     */     }
/* 395 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getResourceType(String resourcetype) {
/* 403 */     int type = Util.getIntValue(resourcetype, 0);
/* 404 */     if (type == 1)
/* 405 */       return "分部"; 
/* 406 */     if (type == 2)
/* 407 */       return "部门"; 
/* 408 */     if (type == 3) {
/* 409 */       return "人员";
/*     */     }
/* 411 */     return "全部";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getResourceNames(String resourcetype, String resourceids) {
/* 420 */     int type = Util.getIntValue(resourcetype, 0);
/* 421 */     if (type == 1)
/* 422 */       return getSubCompany(resourceids); 
/* 423 */     if (type == 2)
/* 424 */       return getDepartment(resourceids); 
/* 425 */     if (type == 3) {
/* 426 */       return getHrm(resourceids);
/*     */     }
/* 428 */     return "";
/*     */   }
/*     */   
/*     */   public String getNames(String ids, String type) {
/* 432 */     StringBuffer names = new StringBuffer();
/* 433 */     if (ids != null && !"".equals(ids)) {
/*     */       try {
/* 435 */         List<String> idList = Util.TokenizerString(ids, ",");
/* 436 */         String hrmurl = "";
/* 437 */         if ("hrm".equals(type)) {
/* 438 */           File bf = new File(String.valueOf(GCONST.getRootPath()) + "hrm" + File.separatorChar + "HrmTab.jsp");
/* 439 */           if (bf.exists()) {
/* 440 */             hrmurl = "/hrm/HrmTab.jsp?_fromURL=HrmResource&id=";
/*     */           } else {
/* 442 */             hrmurl = "/hrm/resource/HrmResource.jsp?id=";
/*     */           } 
/*     */         } 
/* 445 */         for (int i = 0; i < idList.size(); i++) {
/* 446 */           if ("crm".equals(type)) {
/* 447 */             if (this.ci == null) this.ci = new CustomerInfoComInfo(); 
/* 448 */             names.append("<a href=javascript:openFullWindowHaveBar('/CRM/data/ViewCustomer.jsp?log=n&CustomerID=" + (String)idList.get(i) + "') >" + 
/* 449 */                 this.ci.getCustomerInfoname(idList.get(i)) + "</a> ");
/* 450 */           } else if ("person".equals(type)) {
/* 451 */             if (this.rc == null) this.rc = new ResourceComInfo(); 
/* 452 */             names.append("<a href='javaScript:openhrm(" + idList.get(i) + ");' onclick='pointerXY(event);'>" + this.rc.getResourcename(idList.get(i)) + "</a> ");
/* 453 */           } else if ("hrm".equals(type)) {
/* 454 */             if (this.rc == null) this.rc = new ResourceComInfo(); 
/* 455 */             names.append("<a href=javaScript:openFullWindowForXtable('" + hrmurl + idList.get(i) + "') >" + this.rc.getResourcename(idList.get(i)) + "</a> ");
/* 456 */           } else if ("doc".equals(type)) {
/* 457 */             if (this.doc == null) this.doc = new DocComInfo(); 
/* 458 */             names.append("<a href=javaScript:openFullWindowHaveBar('/docs/docs/DocDsp.jsp?id=" + idList.get(i) + "') >" + this.doc.getDocname(idList.get(i)) + "</a> ");
/* 459 */           } else if ("request".equals(type)) {
/* 460 */             if (this.request == null) this.request = new RequestComInfo(); 
/* 461 */             names.append("<a href=javaScript:openFullWindowHaveBar('/workflow/request/ViewRequest.jsp?requestid=" + idList.get(i) + "') >" + this.request.getRequestname(idList.get(i)) + "</a> ");
/* 462 */           } else if ("subcompany".equals(type)) {
/* 463 */             if (this.subcompany == null) this.subcompany = new SubCompanyComInfo(); 
/* 464 */             names.append("," + this.subcompany.getSubCompanyname(idList.get(i)));
/* 465 */           } else if ("department".equals(type)) {
/* 466 */             if (this.department == null) this.department = new DepartmentComInfo(); 
/* 467 */             names.append("," + this.department.getDepartmentname(idList.get(i)));
/*     */           } 
/*     */         } 
/* 470 */       } catch (Exception ex) {
/* 471 */         writeLog(ex);
/*     */       } 
/*     */     }
/* 474 */     String namestr = names.toString();
/* 475 */     if (namestr.startsWith(",")) namestr = namestr.substring(1); 
/* 476 */     return namestr;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCustomer(String ids) {
/* 485 */     return getNames(ids, "crm");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getPerson(String ids) {
/* 494 */     return getNames(ids, "person");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getHrm(String ids) {
/* 502 */     return getNames(ids, "hrm");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getPerson2(String ids) {
/* 510 */     String names = "";
/* 511 */     if (ids != null && !"".equals(ids)) {
/* 512 */       List<String> idList = Util.TokenizerString(ids, ",");
/* 513 */       for (int i = 0; i < idList.size(); i++) {
/* 514 */         names = String.valueOf(names) + this.rc.getResourcename(idList.get(i)) + " ";
/*     */       }
/*     */     } 
/* 517 */     return names;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDoc(String ids) {
/* 525 */     return getNames(ids, "doc");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getRequest(String ids) {
/* 533 */     return getNames(ids, "request");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSubCompany(String ids) {
/* 541 */     return getNames(ids, "subcompany");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDepartment(String ids) {
/* 549 */     return getNames(ids, "department");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDateTime(String date, String time) {
/* 559 */     return String.valueOf(date) + " " + time;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/wxinterface/TransUtil.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */