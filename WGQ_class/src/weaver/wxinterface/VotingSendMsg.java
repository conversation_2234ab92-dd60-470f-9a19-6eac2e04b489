/*     */ package weaver.wxinterface;
/*     */ 
/*     */ import java.text.SimpleDateFormat;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Date;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import net.sf.json.JSONObject;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.file.Prop;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ public class VotingSendMsg
/*     */   implements Runnable
/*     */ {
/*     */   private SimpleDateFormat sdf;
/*     */   private BaseBean bb;
/*     */   private FlowAndDoc fad;
/*     */   
/*     */   public VotingSendMsg(FlowAndDoc fad) {
/*  27 */     this.fad = fad;
/*  28 */     this.bb = new BaseBean();
/*  29 */     this.sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
/*     */   }
/*     */ 
/*     */   
/*     */   public void run() {
/*  34 */     scanAndSend();
/*     */   }
/*     */   
/*     */   private FlowAndDoc scanAndSend() {
/*     */     try {
/*  39 */       Map<String, Object> map = new HashMap<String, Object>();
/*  40 */       map.put("ifsendsub", Integer.valueOf(this.fad.getIfsendsub()));
/*     */       
/*  42 */       String nowDate = this.sdf.format(new Date());
/*  43 */       String lastscantime = this.fad.getLastscantime();
/*  44 */       if (lastscantime == null || lastscantime.equals("")) {
/*  45 */         lastscantime = nowDate;
/*     */       }
/*  47 */       this.fad.setLastscantime(nowDate);
/*  48 */       RecordSet rs = new RecordSet();
/*  49 */       rs.execute("update WX_MsgRuleSetting set lastscantime='" + nowDate + "' where id =" + this.fad.getId());
/*     */       
/*  51 */       String begintime = "", endtime = "";
/*  52 */       if (!"oracle".equals(rs.getDBType())) {
/*     */ 
/*     */         
/*  55 */         begintime = "(begindate+' '+begintime)";
/*  56 */         endtime = "(enddate+' '+endtime)";
/*     */       }
/*     */       else {
/*     */         
/*  60 */         begintime = "(CONCAT(CONCAT(begindate,' '),begintime))";
/*  61 */         endtime = "(CONCAT(CONCAT(enddate,' '),endtime))";
/*     */       } 
/*  63 */       String sql = "select * from voting v where v.status = 1 and (v.istemplate <> '1' or v.istemplate is null) and " + 
/*  64 */         begintime + "<='" + nowDate + "' and " + endtime + ">='" + nowDate + "'" + 
/*  65 */         " and not exists (select 1 from WX_SCANLOG l where v.id = l.reourceid  and l.type = 14)";
/*     */       
/*  67 */       rs.executeSql(sql);
/*  68 */       while (rs.next()) {
/*  69 */         int votingid = rs.getInt("id");
/*  70 */         String subject = Util.null2String(rs.getString("subject"));
/*  71 */         subject = FormatMultiLang.format(subject, "7");
/*  72 */         List<String> userList = getUndoUserSet(votingid);
/*     */         
/*  74 */         if (userList != null && userList.size() > 0) {
/*     */           
/*  76 */           int ifZYZL = Util.getIntValue(Prop.getPropValue("zyzl_csdev", "ifZYZL"), 0);
/*  77 */           if (ifZYZL == 1) {
/*  78 */             String creater = Util.null2String(rs.getString("createrid"));
/*  79 */             ResourceComInfo rc = new ResourceComInfo();
/*  80 */             String deptId = rc.getDepartmentID(creater);
/*  81 */             DepartmentComInfo dc = new DepartmentComInfo();
/*  82 */             String deptName = dc.getDepartmentname(deptId);
/*  83 */             JSONObject jo = new JSONObject();
/*  84 */             jo.put("creater", rc.getLastname(creater));
/*  85 */             jo.put("createrDept", deptName);
/*  86 */             jo.put("createTime", String.valueOf(rs.getString("createdate")) + " " + rs.getString("createtime"));
/*  87 */             jo.put("content", "");
/*  88 */             map.put("csdev_zyzl", jo.toString());
/*     */           } 
/*  90 */           InterfaceUtil.sendMsg(userList, this.fad.getMsgtpids(), (new StringBuilder(String.valueOf(votingid))).toString(), "您有一个新的问卷调查:" + subject, 14, map);
/*     */         } 
/*     */       } 
/*  93 */     } catch (Exception e) {
/*  94 */       this.bb.writeLog("定时扫描问卷调查并提醒程序异常", e);
/*  95 */       e.printStackTrace();
/*     */     } 
/*  97 */     return this.fad;
/*     */   }
/*     */ 
/*     */   
/*     */   public static List<String> getUndoUserSet(int votingid) {
/* 102 */     RecordSet rs = new RecordSet();
/* 103 */     RecordSet rs2 = new RecordSet();
/* 104 */     List<String> undoUserList = new ArrayList<String>();
/* 105 */     List<String> doUserList = new ArrayList<String>();
/* 106 */     rs.executeSql("select resourceid from votingremark where votingid=" + votingid);
/* 107 */     while (rs.next()) {
/* 108 */       doUserList.add(rs.getString("resourceid"));
/*     */     }
/* 110 */     String hrmStatusSql = " and status in (0,1,2,3) ";
/* 111 */     String hrmStatusSql2 = " and a.status in (0,1,2,3) ";
/* 112 */     rs.executeSql("select * from votingshare where votingid=" + votingid);
/* 113 */     while (rs.next()) {
/* 114 */       String tmptsharetype = Util.null2String(rs.getString("sharetype"));
/* 115 */       int seclevel = Util.getIntValue(rs.getString("seclevel"), 0);
/* 116 */       int seclevelmax = Util.getIntValue(rs.getString("seclevelmax"));
/* 117 */       String seclevelmaxstr = "";
/* 118 */       String seclevelmaxstr2 = "";
/* 119 */       if (seclevelmax > 0) {
/* 120 */         seclevelmaxstr = " and seclevel<=" + seclevelmax;
/* 121 */         seclevelmaxstr2 = " and a.seclevel<=" + seclevelmax;
/*     */       } 
/* 123 */       String sql = "";
/*     */       
/* 125 */       if (tmptsharetype.equals("1")) {
/* 126 */         String tmptresourceid = rs.getString("resourceid");
/* 127 */         sql = "select id from hrmresource where id=" + tmptresourceid + hrmStatusSql;
/*     */       
/*     */       }
/* 130 */       else if (tmptsharetype.equals("2")) {
/* 131 */         int subcompanyid = Util.getIntValue(rs.getString("subcompanyid"), 0);
/* 132 */         if (subcompanyid > 0) {
/* 133 */           sql = "select id from hrmresource where seclevel>=" + seclevel + seclevelmaxstr + " and subcompanyid1=" + subcompanyid + hrmStatusSql;
/*     */         } else {
/* 135 */           sql = "select a.id from hrmresource a inner join HrmResourceVirtual b on a.id=b.resourceid where a.seclevel>=" + seclevel + seclevelmaxstr2 + " and b.subcompanyid=" + subcompanyid + hrmStatusSql2;
/*     */         }
/*     */       
/*     */       }
/* 139 */       else if (tmptsharetype.equals("3")) {
/* 140 */         int departmentid = Util.getIntValue(rs.getString("departmentid"), 0);
/* 141 */         if (departmentid > 0) {
/* 142 */           sql = "select id from hrmresource where seclevel>=" + seclevel + seclevelmaxstr + " and departmentid=" + departmentid + hrmStatusSql;
/*     */         } else {
/* 144 */           sql = "select a.id from hrmresource a inner join HrmResourceVirtual b on a.id=b.resourceid where a.seclevel>=" + seclevel + seclevelmaxstr2 + " and b.departmentid=" + departmentid + hrmStatusSql2;
/*     */         }
/*     */       
/*     */       }
/* 148 */       else if (tmptsharetype.equals("4")) {
/* 149 */         String roleid = rs.getString("roleid");
/* 150 */         String tmptrolelevel = rs.getString("rolelevel");
/* 151 */         sql = "select distinct a.id from hrmresource a join hrmrolemembers b on b.resourceid=a.id where a.seclevel>=" + seclevel + seclevelmaxstr2 + " and b.roleid=" + roleid + " and b.rolelevel>=" + tmptrolelevel + hrmStatusSql2;
/*     */       
/*     */       }
/* 154 */       else if (tmptsharetype.equals("5")) {
/* 155 */         sql = "select id from hrmresource where seclevel>=" + seclevel + seclevelmaxstr + hrmStatusSql;
/*     */       
/*     */       }
/* 158 */       else if (tmptsharetype.equals("6")) {
/* 159 */         String jobtitles = rs.getString("jobtitles");
/* 160 */         String joblevel = rs.getString("joblevel");
/* 161 */         String jobdepartment = rs.getString("jobdepartment");
/* 162 */         String jobsubcompany = rs.getString("jobsubcompany");
/* 163 */         String jobsql = "";
/* 164 */         if (joblevel.equals("1")) {
/* 165 */           jobsql = " and  subcompanyid1=" + jobsubcompany;
/* 166 */         } else if (joblevel.equals("2")) {
/* 167 */           jobsql = " and  departmentid=" + jobdepartment;
/*     */         } 
/* 169 */         sql = "select id from hrmresource where jobtitle=" + jobtitles + jobsql + hrmStatusSql;
/*     */       } 
/* 171 */       rs2.executeSql(sql);
/* 172 */       while (rs2.next()) {
/* 173 */         String id = rs2.getString("id");
/* 174 */         if (doUserList.contains(id))
/* 175 */           continue;  undoUserList.add(id);
/*     */       } 
/*     */     } 
/* 178 */     return undoUserList;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/wxinterface/VotingSendMsg.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */