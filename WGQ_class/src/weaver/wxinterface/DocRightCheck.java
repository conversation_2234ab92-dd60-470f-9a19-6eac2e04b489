/*    */ package weaver.wxinterface;
/*    */ 
/*    */ import java.util.List;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ import weaver.splitepage.operate.SpopForDoc;
/*    */ import weaver.systeminfo.setting.HrmUserSettingComInfo;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class DocRightCheck
/*    */ {
/*    */   public static boolean checkDocRight(User user, String docid) {
/* 17 */     boolean canReader = false;
/*    */     try {
/* 19 */       HrmUserSettingComInfo userSetting = new HrmUserSettingComInfo();
/* 20 */       String belongtoshow = userSetting.getBelongtoshowByUserId((new StringBuilder(String.valueOf(user.getUID()))).toString());
/* 21 */       String belongtoids = user.getBelongtoids();
/* 22 */       String account_type = user.getAccount_type();
/* 23 */       if (belongtoshow.equals("1") && account_type.equals("0") && !belongtoids.equals("")) {
/* 24 */         String[] votingshareids = Util.TokenizerString2(belongtoids, ",");
/* 25 */         SpopForDoc spopForDoc = new SpopForDoc();
/* 26 */         for (int i = 0; i < votingshareids.length; i++) {
/* 27 */           String str1, str2; User user1 = new User(Integer.parseInt(votingshareids[i]));
/* 28 */           String logintype1 = user1.getLogintype();
/* 29 */           String userSeclevel1 = user1.getSeclevel();
/* 30 */           int j = user1.getType();
/* 31 */           int k = user1.getUserDepartment();
/* 32 */           int m = user1.getUserSubCompany1();
/* 33 */           if ("2".equals(logintype1)) {
/* 34 */             str1 = "0";
/* 35 */             str2 = "0";
/* 36 */             userSeclevel1 = "0";
/*    */           } 
/* 38 */           String userInfo1 = String.valueOf(logintype1) + "_" + votingshareids[i] + "_" + userSeclevel1 + "_" + j + "_" + str1 + "_" + str2;
/* 39 */           List<String> PdocList = spopForDoc.getDocOpratePopedom(docid, userInfo1);
/* 40 */           if (((String)PdocList.get(0)).equals("true") && !canReader) {
/* 41 */             canReader = true;
/*    */           }
/* 43 */           if (((String)PdocList.get(1)).equals("true")) {
/* 44 */             canReader = true;
/*    */           }
/* 46 */           if (((String)PdocList.get(3)).equals("true")) canReader = true; 
/* 47 */           if (canReader) {
/*    */             break;
/*    */           }
/*    */         } 
/*    */       } 
/* 52 */     } catch (Exception e) {
/* 53 */       e.printStackTrace();
/*    */     } 
/* 55 */     return canReader;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/wxinterface/DocRightCheck.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */