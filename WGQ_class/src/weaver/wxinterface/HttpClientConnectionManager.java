/*    */ package weaver.wxinterface;
/*    */ 
/*    */ import org.apache.commons.httpclient.HttpClient;
/*    */ import org.apache.commons.httpclient.HttpConnectionManager;
/*    */ import org.apache.commons.httpclient.MultiThreadedHttpConnectionManager;
/*    */ import org.apache.commons.httpclient.params.HttpClientParams;
/*    */ import org.apache.commons.httpclient.params.HttpConnectionManagerParams;
/*    */ import weaver.general.BaseBean;
/*    */ 
/*    */ public final class HttpClientConnectionManager
/*    */ {
/*    */   private static MultiThreadedHttpConnectionManager connectionManager;
/* 13 */   private static HttpConnectionMonitorThread scanThread = null;
/*    */ 
/*    */ 
/*    */ 
/*    */   
/* 18 */   private static BaseBean bb = new BaseBean();
/*    */   static {
/* 20 */     if (HttpConnectionManagerConstants.isUserConnectionManager()) {
/*    */       
/*    */       try {
/* 23 */         connectionManager = new MultiThreadedHttpConnectionManager();
/* 24 */         HttpConnectionManagerParams params = connectionManager.getParams();
/* 25 */         params.setDefaultMaxConnectionsPerHost(HttpConnectionManagerConstants.DEFAULT_MAX_PER_ROUTE);
/* 26 */         params.setMaxTotalConnections(HttpConnectionManagerConstants.MAX_TOTAL_CONNECTIONS);
/*    */ 
/*    */         
/* 29 */         scanThread = new HttpConnectionMonitorThread(connectionManager);
/* 30 */         scanThread.start();
/*    */         
/* 32 */         bb.writeLog("初始化云桥httpclient连接池成功，DEFAULT_MAX_PER_ROUTE：" + HttpConnectionManagerConstants.DEFAULT_MAX_PER_ROUTE + "---MAX_TOTAL_CONNECTIONS：" + HttpConnectionManagerConstants.MAX_TOTAL_CONNECTIONS + "---------");
/* 33 */       } catch (Throwable e) {
/* 34 */         e.printStackTrace();
/* 35 */         connectionManager = null;
/*    */       } 
/*    */     }
/*    */   }
/*    */   
/*    */   public static final HttpClient getHttpClient() {
/* 41 */     if (connectionManager != null && HttpConnectionManagerConstants.isUserConnectionManager()) {
/* 42 */       return new HttpClient((HttpConnectionManager)connectionManager);
/*    */     }
/* 44 */     return new HttpClient();
/*    */   }
/*    */ 
/*    */   
/*    */   public static final HttpClient getHttpClient(HttpClientParams params) {
/* 49 */     if (connectionManager != null && HttpConnectionManagerConstants.isUserConnectionManager()) {
/* 50 */       return new HttpClient(params, (HttpConnectionManager)connectionManager);
/*    */     }
/* 52 */     return new HttpClient(params);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public static void close() {
/* 60 */     if (scanThread != null)
/* 61 */       scanThread.shutdown(); 
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/wxinterface/HttpClientConnectionManager.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */