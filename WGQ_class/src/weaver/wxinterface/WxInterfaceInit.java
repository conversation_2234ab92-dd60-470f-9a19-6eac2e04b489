/*     */ package weaver.wxinterface;
/*     */ 
/*     */ import java.io.File;
/*     */ import java.io.IOException;
/*     */ import java.net.InetAddress;
/*     */ import java.net.NetworkInterface;
/*     */ import java.net.SocketException;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Calendar;
/*     */ import java.util.Enumeration;
/*     */ import java.util.List;
/*     */ import java.util.Timer;
/*     */ import java.util.concurrent.ScheduledThreadPoolExecutor;
/*     */ import java.util.concurrent.TimeUnit;
/*     */ import javax.servlet.ServletConfig;
/*     */ import javax.servlet.ServletException;
/*     */ import javax.servlet.http.HttpServlet;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.file.Prop;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.StaticObj;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class WxInterfaceInit
/*     */   extends HttpServlet
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private static ScheduledThreadPoolExecutor stp;
/*     */   private static ScheduledThreadPoolExecutor stp2;
/*     */   private static BaseBean bb;
/*     */   private static List<Timer> docTimerList;
/*     */   private static List<Timer> otherTimerList;
/*     */   private static List<FlowAndDoc> flowList;
/*     */   private static List<FlowAndDoc> docList;
/*     */   private static List<FlowAndDoc> msgList;
/*  51 */   public static int ifInit = 0;
/*     */   
/*     */   public static boolean isutf8 = false;
/*     */   
/*     */   public static boolean isCluster = false;
/*     */   
/*     */   static {
/*  58 */     if (ifInit == 0) {
/*  59 */       String charset = Util.null2String(Prop.getPropValue("wxinterface", "charset"));
/*  60 */       if (charset.equals("")) {
/*     */         
/*  62 */         File bf = new File(String.valueOf(GCONST.getRootPath()) + "systeminfo" + File.separatorChar + "init_wev8.jsp");
/*  63 */         if (bf.exists()) {
/*  64 */           isutf8 = true;
/*     */         }
/*     */       }
/*  67 */       else if ("GBK".equalsIgnoreCase(charset)) {
/*  68 */         isutf8 = false;
/*     */       } else {
/*  70 */         isutf8 = true;
/*     */       } 
/*     */     } 
/*     */     
/*  74 */     bb = new BaseBean();
/*     */   }
/*     */   
/*     */   public static List<Timer> getDocTimerList() {
/*  78 */     if (ifInit == 0) {
/*  79 */       initAll();
/*     */     }
/*  81 */     return docTimerList;
/*     */   }
/*     */   
/*     */   public static List<Timer> getOtherTimerList() {
/*  85 */     if (ifInit == 0) {
/*  86 */       initAll();
/*     */     }
/*  88 */     return otherTimerList;
/*     */   }
/*     */   
/*     */   public static List<FlowAndDoc> getFlowList() {
/*  92 */     if (ifInit == 0) {
/*  93 */       initAll();
/*     */     }
/*  95 */     if (isCluster()) initData(); 
/*  96 */     return flowList;
/*     */   }
/*     */   
/*     */   public static List<FlowAndDoc> getDocList() {
/* 100 */     if (ifInit == 0) {
/* 101 */       initAll();
/*     */     }
/* 103 */     if (isCluster()) initData(); 
/* 104 */     return docList;
/*     */   }
/*     */   
/*     */   public static List<FlowAndDoc> getMsgList() {
/* 108 */     if (ifInit == 0) {
/* 109 */       initAll();
/*     */     }
/* 111 */     if (isCluster()) initData(); 
/* 112 */     return msgList;
/*     */   }
/*     */   
/*     */   public static boolean isIsutf8() {
/* 116 */     return isutf8;
/*     */   }
/*     */   
/*     */   public static boolean isCluster() {
/* 120 */     StaticObj staticobj = StaticObj.getInstance();
/* 121 */     isCluster = staticobj.isCluster();
/* 122 */     int nocluster = Util.getIntValue(Prop.getPropValue("wxinterface", "nocluster"), 0);
/* 123 */     if (nocluster == 1) { isCluster = false; }
/* 124 */     else if (nocluster == 2) { isCluster = true; }
/*     */     
/* 126 */     return isCluster;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {}
/*     */ 
/*     */ 
/*     */   
/*     */   protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {}
/*     */ 
/*     */ 
/*     */   
/*     */   public void destroy() {
/* 141 */     super.destroy();
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 146 */     if (stp != null) {
/* 147 */       stp.shutdown();
/* 148 */       bb.writeLog("销毁微信推送扫描定时器完成---------");
/*     */     } 
/* 150 */     if (stp2 != null) {
/* 151 */       stp2.shutdown();
/* 152 */       bb.writeLog("销毁微信批量推送扫描定时器完成---------");
/*     */     } 
/*     */     
/* 155 */     HttpClientConnectionManager.close();
/*     */   }
/*     */ 
/*     */   
/*     */   public void init(ServletConfig config) throws ServletException {
/* 160 */     super.init(config);
/* 161 */     if (ifInit == 0) {
/* 162 */       initAll();
/*     */     }
/*     */   }
/*     */   
/*     */   public static void initAll() {
/* 167 */     initMainScan();
/* 168 */     initBatchSendScan();
/* 169 */     initClass();
/*     */   }
/*     */   
/*     */   public static void initClass() {
/* 173 */     if (ifInit == 0) {
/* 174 */       ifInit = 1;
/* 175 */       StaticObj staticobj = StaticObj.getInstance();
/* 176 */       isCluster = staticobj.isCluster();
/* 177 */       int nocluster = Util.getIntValue(Prop.getPropValue("wxinterface", "nocluster"), 0);
/* 178 */       if (nocluster == 1) { isCluster = false; }
/* 179 */       else if (nocluster == 2) { isCluster = true; }
/*     */       
/* 181 */       initFlowAndDoc();
/*     */       
/* 183 */       RecordSet rs = new RecordSet();
/* 184 */       rs.executeSql("select * from wx_initclass");
/* 185 */       while (rs.next()) {
/* 186 */         String classpath = rs.getString("classpath");
/*     */         try {
/* 188 */           Class<WxInitInterface> clazz = (Class)Class.forName(classpath);
/* 189 */           WxInitInterface wif = clazz.newInstance();
/* 190 */           wif.start();
/* 191 */         } catch (Exception exception) {}
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void initFlowAndDoc() {
/* 202 */     initData();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void initData() {
/* 210 */     RecordSet rs = new RecordSet();
/* 211 */     rs.execute("select * from WX_MsgRuleSetting where isenable=1");
/* 212 */     flowList = new ArrayList<FlowAndDoc>();
/* 213 */     docList = new ArrayList<FlowAndDoc>();
/* 214 */     msgList = new ArrayList<FlowAndDoc>();
/* 215 */     while (rs.next()) {
/* 216 */       FlowAndDoc fad = new FlowAndDoc();
/* 217 */       fad.setId(rs.getString("id"));
/* 218 */       fad.setFlowsordocs(rs.getString("flowsordocs"));
/* 219 */       fad.setFreqtime(Util.getIntValue(rs.getString("freqtime"), 1));
/* 220 */       fad.setIfrepeat(Util.getIntValue(rs.getString("ifrepeat"), 1));
/* 221 */       fad.setIftoall(Util.getIntValue(rs.getString("iftoall"), 1));
/* 222 */       fad.setIfcover(Util.getIntValue(rs.getString("ifcover"), 1));
/* 223 */       fad.setMsgtpids(rs.getString("msgtpids"));
/* 224 */       fad.setMsgtpnames(rs.getString("msgtpnames"));
/* 225 */       fad.setName(rs.getString("name"));
/* 226 */       int type = Util.getIntValue(rs.getString("type"), 1);
/* 227 */       fad.setType(type);
/* 228 */       fad.setTypeids(rs.getString("typeids"));
/* 229 */       fad.setLastscantime(Util.null2String(rs.getString("lastscantime")));
/* 230 */       fad.setWfsettype(Util.getIntValue(rs.getString("wfsettype"), 0));
/* 231 */       fad.setFlowtype(Util.getIntValue(rs.getString("flowtype"), 0));
/* 232 */       fad.setIfwftodo(Util.getIntValue(rs.getString("ifwftodo"), 0));
/* 233 */       fad.setIfwffinish(Util.getIntValue(rs.getString("ifwffinish"), 0));
/* 234 */       fad.setIfwftimeout(Util.getIntValue(rs.getString("ifwftimeout"), 0));
/* 235 */       fad.setIfwfreject(Util.getIntValue(rs.getString("ifwfreject"), 0));
/* 236 */       fad.setRequestlevel(Util.null2String(rs.getString("requestlevel")));
/* 237 */       fad.setStartdate(Util.null2String(rs.getString("startdate")));
/* 238 */       fad.setStartcentext(Util.null2String(rs.getString("startcentext")));
/* 239 */       fad.setEnddate(Util.null2String(rs.getString("enddate")));
/* 240 */       fad.setEndcentext(Util.null2String(rs.getString("endcentext")));
/* 241 */       fad.setResourceids(Util.null2String(rs.getString("resourceids")));
/* 242 */       fad.setResourcenames(Util.null2String(rs.getString("resourcenames")));
/* 243 */       fad.setResourcetype(Util.null2String(rs.getString("resourcetype")));
/* 244 */       fad.setIfsendsub(Util.getIntValue(rs.getString("ifsendsub"), 1));
/* 245 */       fad.setIfbatchsend(Util.getIntValue(rs.getString("ifbatchsend"), 0));
/* 246 */       fad.setBatchsendnum(Util.getIntValue(rs.getString("batchsendnum"), 898));
/* 247 */       fad.setBatchsendinterval(Util.getIntValue(rs.getString("batchsendinterval"), 5));
/* 248 */       if (type == 1) {
/* 249 */         flowList.add(fad); continue;
/* 250 */       }  if (type == 2) {
/* 251 */         docList.add(fad); continue;
/*     */       } 
/* 253 */       msgList.add(fad);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void initMainScan() {
/* 262 */     if (ifMainControlIP()) {
/*     */       try {
/* 264 */         int interval = Util.getIntValue(Prop.getPropValue("wxinterface", "scaninterval"), 1);
/*     */ 
/*     */         
/* 267 */         MainScanAndSend msas = new MainScanAndSend();
/*     */         
/* 269 */         stp = new ScheduledThreadPoolExecutor(20);
/* 270 */         stp.scheduleAtFixedRate(msas, 1000L, (interval * 60 * 1000), TimeUnit.MILLISECONDS);
/* 271 */         bb.writeLog("初始化微信推送扫描线程成功，扫描间隔" + interval + "分钟---------");
/* 272 */       } catch (Exception e) {
/* 273 */         bb.writeLog("初始化微信推送扫描线程失败，异常信息" + e.getMessage() + "---------");
/*     */       } 
/* 275 */       int ifFdSSo = Util.getIntValue(Prop.getPropValue("fdcsdev", "ifFdSSo"), 0);
/* 276 */       if (ifFdSSo == 1) {
/*     */         try {
/* 278 */           FdWFMsgSend fdms = new FdWFMsgSend();
/* 279 */           Calendar calendar = Calendar.getInstance();
/* 280 */           calendar.set(11, 17);
/* 281 */           calendar.set(12, 0);
/* 282 */           calendar.set(13, 0);
/* 283 */           long sendTime = calendar.getTimeInMillis();
/* 284 */           long nowTime = System.currentTimeMillis();
/* 285 */           long initialDelay = 0L;
/* 286 */           if (nowTime > sendTime) {
/* 287 */             calendar.add(6, 1);
/* 288 */             long needSendTime = calendar.getTimeInMillis();
/* 289 */             initialDelay = needSendTime - nowTime;
/*     */           } else {
/* 291 */             initialDelay = sendTime - nowTime;
/*     */           } 
/* 293 */           stp.scheduleAtFixedRate(fdms, initialDelay, 86400000L, TimeUnit.MILLISECONDS);
/* 294 */         } catch (Exception e) {
/* 295 */           bb.writeLog("初始化复地17点定时推送流程扫描线程失败，异常信息" + e.getMessage() + "---------");
/*     */         } 
/*     */       }
/*     */     } else {
/* 299 */       bb.writeLog("初始化微信推送扫描线程记录日志,不是主服务器---------");
/*     */     } 
/*     */   }
/*     */   
/*     */   public static void initBatchSendScan() {
/* 304 */     if (ifMainControlIP()) {
/*     */       try {
/* 306 */         BatchSendThread bst = new BatchSendThread();
/* 307 */         stp = new ScheduledThreadPoolExecutor(20);
/* 308 */         stp.scheduleAtFixedRate(bst, 1000L, 120000L, TimeUnit.MILLISECONDS);
/* 309 */         bb.writeLog("初始化微信批量推送扫描线程成功，扫描间隔2分钟---------");
/* 310 */       } catch (Exception e) {
/* 311 */         bb.writeLog("初始化微信批量推送扫描线程失败，异常信息" + e.getMessage() + "---------");
/*     */       } 
/*     */     } else {
/* 314 */       bb.writeLog("初始化微信批量推送扫描线程记录日志,不是主服务器---------");
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void initOtherSend() {
/* 322 */     destoryOtherSend();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void destoryOtherSend() {
/* 342 */     if (otherTimerList != null && otherTimerList.size() > 0) {
/* 343 */       for (Timer t : otherTimerList) {
/* 344 */         if (t != null) {
/* 345 */           t.cancel();
/*     */         }
/*     */       } 
/*     */     }
/* 349 */     otherTimerList = new ArrayList<Timer>();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void initDocSend() {
/* 356 */     destoryDocSend();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void destoryDocSend() {
/* 373 */     if (docTimerList != null && docTimerList.size() > 0) {
/* 374 */       for (Timer t : docTimerList) {
/* 375 */         if (t != null) {
/* 376 */           t.cancel();
/*     */         }
/*     */       } 
/*     */     }
/* 380 */     docTimerList = new ArrayList<Timer>();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean hasRule(int type) {
/* 389 */     if (ifInit == 0) {
/* 390 */       initClass();
/*     */     }
/* 392 */     if (type == 1)
/* 393 */       return (flowList != null && flowList.size() > 0); 
/* 394 */     if (type == 2) {
/* 395 */       return (docList != null && docList.size() > 0);
/*     */     }
/* 397 */     if (msgList != null) {
/* 398 */       for (FlowAndDoc fad : msgList) {
/* 399 */         if (fad.getType() == type) return true;
/*     */       
/*     */       } 
/*     */     }
/* 403 */     return false;
/*     */   }
/*     */   
/*     */   public static boolean ifMainControlIP() {
/* 407 */     String mainControlIp = Util.null2String(Prop.getPropValue("weaver", "MainControlIP"));
/* 408 */     ArrayList<String> hostIps = getRealIp();
/* 409 */     if ((!"".equals(mainControlIp) && hostIps.contains(mainControlIp)) || "".equals(mainControlIp)) {
/* 410 */       return true;
/*     */     }
/* 412 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static ArrayList<String> getRealIp() {
/* 421 */     String localip = null;
/* 422 */     String netip = null;
/* 423 */     ArrayList<String> ips = new ArrayList<String>();
/*     */     
/* 425 */     Enumeration<NetworkInterface> netInterfaces = null;
/*     */     try {
/* 427 */       netInterfaces = NetworkInterface.getNetworkInterfaces();
/*     */       
/* 429 */       InetAddress ip = null;
/* 430 */       while (netInterfaces.hasMoreElements()) {
/* 431 */         NetworkInterface ni = netInterfaces.nextElement();
/* 432 */         Enumeration<InetAddress> address = ni.getInetAddresses();
/* 433 */         while (address.hasMoreElements()) {
/* 434 */           ip = address.nextElement();
/* 435 */           if (!ip.isSiteLocalAddress() && !ip.isLoopbackAddress() && ip.getHostAddress().indexOf(":") == -1) {
/* 436 */             netip = ip.getHostAddress();
/* 437 */             if (netip != null && !"".equals(netip))
/* 438 */               ips.add(netip);  continue;
/*     */           } 
/* 440 */           if (ip.isSiteLocalAddress() && !ip.isLoopbackAddress() && ip.getHostAddress().indexOf(":") == -1) {
/* 441 */             localip = ip.getHostAddress();
/* 442 */             ips.add(localip);
/*     */           } 
/*     */         } 
/*     */       } 
/* 446 */     } catch (SocketException e) {
/* 447 */       e.printStackTrace();
/*     */     } 
/* 449 */     return ips;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/wxinterface/WxInterfaceInit.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */