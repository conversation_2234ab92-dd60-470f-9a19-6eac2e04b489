/*     */ package weaver.wxinterface;
/*     */ 
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.file.Prop;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SendDocWxMsgUtil
/*     */ {
/*     */   public static boolean isDocSend(String docid, String seccategory, int type) {
/*  21 */     boolean issend = false;
/*  22 */     List<FlowAndDoc> docscanlist = WxInterfaceInit.getDocList();
/*  23 */     for (FlowAndDoc fad : docscanlist) {
/*  24 */       issend = isSendByFad(docid, seccategory, type, fad);
/*  25 */       if (issend) {
/*     */         break;
/*     */       }
/*     */     } 
/*  29 */     return issend;
/*     */   }
/*     */   
/*     */   private static boolean isSendByFad(String docid, String seccategory, int type, FlowAndDoc fad) {
/*  33 */     boolean issend = false;
/*  34 */     RecordSet rs = new RecordSet();
/*  35 */     if (fad.getFlowsordocs().equals("-1")) {
/*  36 */       if (fad.getFlowtype() == 0) {
/*  37 */         issend = true;
/*     */       }
/*     */     }
/*  40 */     else if (("," + fad.getFlowsordocs() + ",").indexOf("," + seccategory + ",") > -1) {
/*  41 */       if (fad.getFlowtype() == 0) {
/*  42 */         issend = true;
/*     */       }
/*  44 */     } else if (fad.getFlowtype() == 1) {
/*  45 */       issend = true;
/*     */     } 
/*     */ 
/*     */     
/*  49 */     if (issend && type == 2 && fad.getIfrepeat() != 1) {
/*  50 */       rs.executeSql("select * from WX_SCANLOG where reourceid=" + docid + " and type=2");
/*  51 */       if (rs.next()) {
/*  52 */         issend = false;
/*     */       }
/*     */     } 
/*  55 */     return issend;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void sendDocMsg(String docid, String docsubject, String seccategory, List<String> userList, int type) {
/*  66 */     List<FlowAndDoc> docscanlist = WxInterfaceInit.getDocList();
/*  67 */     for (FlowAndDoc fad : docscanlist) {
/*  68 */       if (isSendByFad(docid, seccategory, type, fad)) {
/*  69 */         Map<String, Object> map = new HashMap<String, Object>();
/*  70 */         map.put("ifsendsub", (new StringBuilder(String.valueOf(fad.getIfsendsub()))).toString());
/*  71 */         if (fad.getIfcover() == 1) {
/*  72 */           RecordSet rs = new RecordSet();
/*  73 */           String sql = "";
/*     */           
/*  75 */           if ("sqlserver".equalsIgnoreCase(rs.getDBType())) {
/*  76 */             sql = "select a.doccontent from DocDetail a where a.id=" + docid;
/*     */           } else {
/*  78 */             sql = "select b.doccontent from DocDetail a,DocDetailContent b where a.id = b.docid and a.id=" + docid;
/*     */           } 
/*  80 */           rs.executeSql(sql);
/*  81 */           String doccontent = "";
/*  82 */           if (rs.next()) {
/*  83 */             doccontent = Util.null2String(rs.getString("doccontent"));
/*     */           }
/*  85 */           String fileid = dealContent(doccontent);
/*  86 */           String fileUrl = "";
/*  87 */           if (!fileid.equals("")) {
/*     */             
/*  89 */             InterfaceUtil.saveFile(fileid);
/*     */             
/*  91 */             fileUrl = InterfaceUtil.uploadFileToWx(fileid, 2);
/*     */           } 
/*  93 */           map.put("imgurl", fileUrl);
/*     */         } 
/*  95 */         docsubject = formatTitle(docsubject);
/*  96 */         docsubject = FormatMultiLang.format(docsubject, "7");
/*     */         
/*  98 */         int ifFdSSo = Util.getIntValue(Prop.getPropValue("fdcsdev", "ifFdSSo"), 0);
/*  99 */         if (ifFdSSo == 1) {
/* 100 */           RecordSet rs = new RecordSet();
/* 101 */           rs.executeSql("select gsbm,csdw,zsdw,wzzy from cus_fielddata where id = " + docid + " and scope = 'DocCustomFieldBySecCategory'");
/* 102 */           if (rs.next()) {
/* 103 */             String gsbm = Util.null2String(rs.getString("gsbm"));
/* 104 */             String csdw = Util.null2String(rs.getString("csdw"));
/* 105 */             String zsdw = Util.null2String(rs.getString("zsdw"));
/* 106 */             String wzzy = Util.null2String(rs.getString("wzzy"));
/* 107 */             map.put("gsbm", gsbm);
/* 108 */             map.put("csdw", csdw);
/* 109 */             map.put("zsdw", zsdw);
/* 110 */             map.put("wzzy", wzzy);
/*     */           } 
/*     */         } 
/* 113 */         InterfaceUtil.sendMsg(userList, fad.getMsgtpids(), docid, docsubject, 2, map);
/*     */       } 
/*     */     } 
/*     */   }
/*     */   
/* 118 */   private static String reg = "/weaver/weaver.file.FileDownload?fileid=";
/*     */   
/*     */   private static String dealContent(String content) {
/* 121 */     String fileid = "";
/* 122 */     int i = content.indexOf(reg);
/* 123 */     if (i > -1) {
/* 124 */       String lastContent = content.substring(i + reg.length(), content.length());
/* 125 */       int j = lastContent.indexOf("\"");
/* 126 */       if (j == -1) {
/* 127 */         j = lastContent.indexOf("'");
/*     */       }
/* 129 */       if (j != -1) {
/* 130 */         fileid = lastContent.substring(0, j);
/* 131 */         RecordSet rs = new RecordSet();
/* 132 */         rs.executeSql("select * from imagefile where imagefileid = " + fileid);
/* 133 */         if (rs.next()) {
/* 134 */           String fileName = Util.null2String(rs.getString("imagefilename")).toUpperCase();
/* 135 */           if (fileName.endsWith("PNG") || fileName.endsWith("JPG") || fileName.endsWith("JPEG")) {
/* 136 */             return fileid;
/*     */           }
/*     */         } 
/* 139 */         content = lastContent.substring(j);
/* 140 */         fileid = dealContent(content);
/*     */       } 
/*     */     } 
/* 143 */     return fileid;
/*     */   }
/*     */   
/*     */   private static String formatTitle(String resourceStr) {
/* 147 */     String retStr = "";
/*     */     try {
/* 149 */       retStr = Util.StringReplace(resourceStr, "\\", "\\\\");
/* 150 */       retStr = Util.StringReplace(retStr, "&lt;", "<");
/* 151 */       retStr = Util.StringReplace(retStr, "&gt;", ">");
/* 152 */       retStr = Util.StringReplace(retStr, "&quot;", "\"");
/* 153 */       retStr = Util.StringReplace(retStr, "\n", "\n");
/* 154 */       retStr = Util.StringReplace(retStr, "\r", "\r");
/* 155 */       retStr = Util.StringReplace(retStr, "\"", "\\\"");
/* 156 */       retStr = Util.StringReplace(retStr, "&#8226;", "·");
/* 157 */       retStr = Util.StringReplace(retStr, "&#8226", "·");
/* 158 */     } catch (Exception e) {
/* 159 */       retStr = resourceStr;
/*     */     } 
/* 161 */     return retStr;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/wxinterface/SendDocWxMsgUtil.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */