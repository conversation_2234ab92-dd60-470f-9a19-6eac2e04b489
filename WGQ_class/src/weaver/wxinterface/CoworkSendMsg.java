/*     */ package weaver.wxinterface;
/*     */ 
/*     */ import java.text.SimpleDateFormat;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import net.sf.json.JSONObject;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.cowork.CoworkShareManager;
/*     */ import weaver.file.Prop;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CoworkSendMsg
/*     */   implements Runnable
/*     */ {
/*     */   private SimpleDateFormat sdf;
/*     */   private BaseBean bb;
/*     */   private FlowAndDoc fad;
/*     */   private ResourceComInfo rc;
/*     */   
/*     */   public CoworkSendMsg(FlowAndDoc fad) {
/*  29 */     this.fad = fad;
/*  30 */     this.sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
/*  31 */     this.bb = new BaseBean();
/*     */     try {
/*  33 */       this.rc = new ResourceComInfo();
/*  34 */     } catch (Exception exception) {}
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void run() {
/*  41 */     scanAndSend();
/*     */   }
/*     */   
/*     */   public FlowAndDoc scanAndSend() {
/*     */     try {
/*  46 */       Map<String, Object> sendParamMap = new HashMap<String, Object>();
/*  47 */       sendParamMap.put("ifsendsub", (new StringBuilder(String.valueOf(this.fad.getIfsendsub()))).toString());
/*     */       
/*  49 */       String lastscantime = this.fad.getLastscantime();
/*  50 */       String nowDate = this.sdf.format(Long.valueOf(System.currentTimeMillis()));
/*  51 */       if (lastscantime == null || lastscantime.equals("")) {
/*  52 */         lastscantime = nowDate;
/*     */       }
/*  54 */       RecordSet rs = new RecordSet();
/*  55 */       this.fad.setLastscantime(nowDate);
/*  56 */       rs.execute("update WX_MsgRuleSetting set lastscantime='" + nowDate + "' where id =" + this.fad.getId());
/*     */       
/*  58 */       String createdate = "", modifydate = "";
/*  59 */       if ("sqlserver".equalsIgnoreCase(rs.getDBType())) {
/*  60 */         createdate = "(t.createdate+' '+t.createtime)";
/*  61 */         modifydate = "(modifydate+' '+modifytime)";
/*  62 */       } else if ("mysql".equalsIgnoreCase(rs.getDBType())) {
/*  63 */         createdate = "(CONCAT(t.createdate,' ',t.createtime))";
/*  64 */         modifydate = "(CONCAT(modifydate,' ',modifytime))";
/*     */       } else {
/*  66 */         createdate = "(CONCAT(CONCAT(t.createdate,' '),t.createtime))";
/*  67 */         modifydate = "(CONCAT(CONCAT(modifydate,' '),modifytime))";
/*     */       } 
/*  69 */       String types = this.fad.getTypeids();
/*  70 */       List<Cowork> cwList = new ArrayList<Cowork>();
/*  71 */       if (types.indexOf(",1,") > -1) {
/*  72 */         String sql = "select t.id,t.name,t.creater,t.createdate,t.createtime from cowork_items t where " + 
/*  73 */           createdate + "> '" + lastscantime + ":59' and " + createdate + " <= '" + nowDate + ":59'";
/*  74 */         if (WxModuleInit.ifCoworkApprove) {
/*  75 */           sql = String.valueOf(sql) + " and approvalAtatus = 0";
/*     */         }
/*     */         
/*  78 */         setCoworkList(sql, cwList, 1);
/*     */       } 
/*  80 */       if (types.indexOf(",2,") > -1) {
/*  81 */         String sql = "select distinct i.id,i.name,i.creater,max" + createdate + " as createdate " + 
/*  82 */           "from cowork_items i,cowork_discuss t " + 
/*  83 */           "where i.id = t.coworkid and i.status =1 and t.replayid = 0 group by i.id,i.name,i.creater " + 
/*  84 */           "having max" + createdate + "> '" + lastscantime + ":59' and " + 
/*  85 */           "max" + createdate + " <= '" + nowDate + ":59'";
/*     */         
/*  87 */         setCoworkList(sql, cwList, 2);
/*     */       } 
/*  89 */       int ifkyzq = Util.getIntValue(Prop.getPropValue("kyzqcsdev", "ifkyzq"), 0);
/*  90 */       if (cwList.size() > 0) {
/*     */         
/*  92 */         int noSendMsg = Util.getIntValue(Prop.getPropValue("cowork_hx", "noSendMsg"), 0);
/*  93 */         for (Cowork co : cwList) {
/*     */           try {
/*  95 */             CoworkShareManager csm = new CoworkShareManager();
/*  96 */             List<String> list = null;
/*  97 */             String desc = "";
/*  98 */             if (co.getType() == 1) {
/*     */               
/* 100 */               list = csm.getNoreadUseridList((new StringBuilder(String.valueOf(co.getCoworkid()))).toString());
/* 101 */               if (ifkyzq == 1) {
/* 102 */                 desc = "您有一个新的帖子:【" + co.getName() + "】请关注";
/*     */               } else {
/* 104 */                 desc = "您有一个新的协作:【" + co.getName() + "】请关注";
/*     */               } 
/* 106 */             } else if (co.getType() == 2) {
/*     */               
/* 108 */               list = csm.getShareList("parter", (new StringBuilder(String.valueOf(co.getCoworkid()))).toString());
/* 109 */               if (noSendMsg == 1) {
/* 110 */                 rs.executeSql("select userid from cowork_hidden where coworkid =" + co.getCoworkid());
/* 111 */                 while (rs.next()) {
/* 112 */                   String hideUserid = Util.null2String(rs.getString("userid"));
/* 113 */                   if (!"".equals(hideUserid) && list.contains(hideUserid)) {
/* 114 */                     list.remove(hideUserid);
/*     */                   }
/*     */                 } 
/*     */               } 
/* 118 */               rs.executeSql("select distinct modifier from cowork_log where coworkid = " + 
/* 119 */                   co.getCoworkid() + " and " + modifydate + ">='" + co.getCreatedate() + "'");
/* 120 */               while (rs.next()) {
/* 121 */                 String userid = Util.null2String(rs.getString("modifier"));
/* 122 */                 if (list.contains(userid)) {
/* 123 */                   list.remove(userid);
/*     */                 }
/*     */               } 
/* 126 */               rs.executeSql("select discussant from cowork_discuss t where coworkid = " + 
/* 127 */                   co.getCoworkid() + " and " + createdate + ">='" + co.getCreatedate() + "'");
/* 128 */               while (rs.next()) {
/* 129 */                 String userid = Util.null2String(rs.getString("discussant"));
/* 130 */                 if (list.contains(userid)) {
/* 131 */                   list.remove(userid);
/*     */                 }
/*     */               } 
/* 134 */               if (ifkyzq == 1) {
/* 135 */                 desc = "您的帖子:【" + co.getName() + "】有新交流,请查看";
/*     */               } else {
/* 137 */                 desc = "您的协作:【" + co.getName() + "】有新交流,请查看";
/*     */               } 
/*     */             } 
/*     */ 
/*     */ 
/*     */             
/* 143 */             if (list.size() > 0) {
/*     */               
/* 145 */               int ifZYZL = Util.getIntValue(Prop.getPropValue("zyzl_csdev", "ifZYZL"), 0);
/* 146 */               if (ifZYZL == 1) {
/* 147 */                 String deptId = this.rc.getDepartmentID(co.getCreater());
/* 148 */                 DepartmentComInfo dc = new DepartmentComInfo();
/* 149 */                 String deptName = dc.getDepartmentname(deptId);
/* 150 */                 JSONObject jo = new JSONObject();
/* 151 */                 jo.put("creater", this.rc.getLastname(co.getCreater()));
/* 152 */                 jo.put("createrDept", deptName);
/* 153 */                 jo.put("createTime", String.valueOf(co.getCreatedate()) + " " + co.getCreatetime());
/* 154 */                 jo.put("content", "");
/* 155 */                 sendParamMap.put("csdev_zyzl", jo.toString());
/*     */               } 
/* 157 */               InterfaceUtil.sendMsg(list, this.fad.getMsgtpids(), (new StringBuilder(String.valueOf(co.getCoworkid()))).toString(), desc, 4, sendParamMap);
/*     */             } 
/* 159 */           } catch (Exception e) {
/* 160 */             this.bb.writeLog("协作发送微信提醒失败", e);
/* 161 */             e.printStackTrace();
/*     */           } 
/*     */         } 
/*     */       } 
/* 165 */       if (types.indexOf(",3,") > -1) {
/* 166 */         String sql = "select i.name,i.creater,t.coworkid,t.remark,t.discussant,d.discussant as touser  from cowork_discuss t,cowork_discuss d,cowork_items i  where t.replayid = d.id and t.coworkid = i.id and " + 
/*     */ 
/*     */           
/* 169 */           createdate + "> '" + lastscantime + ":59' and " + 
/* 170 */           createdate + " <= '" + nowDate + ":59'";
/* 171 */         rs.executeSql(sql);
/*     */         
/* 173 */         Map<String, String> map = new HashMap<String, String>();
/* 174 */         while (rs.next()) {
/* 175 */           String coworkid = rs.getString("coworkid");
/* 176 */           String coworkname = rs.getString("name");
/* 177 */           String fromUser = rs.getString("discussant");
/* 178 */           String toUser = rs.getString("touser");
/* 179 */           String remark = rs.getString("remark");
/* 180 */           String key = String.valueOf(coworkid) + "-" + toUser;
/* 181 */           String desc = "";
/* 182 */           if (map.containsKey(key)) {
/* 183 */             desc = map.get(key);
/*     */           }
/* 185 */           else if (ifkyzq == 1) {
/* 186 */             desc = "您的帖子:【" + coworkname + "】收到新回复:\n";
/*     */           } else {
/* 188 */             desc = "您的协作:【" + coworkname + "】收到新回复:\n";
/*     */           } 
/*     */           
/* 191 */           desc = String.valueOf(desc) + "来自【" + FormatMultiLang.format(this.rc.getLastname(fromUser), "7") + "】的回复:“" + remark + "”\n";
/* 192 */           map.put(key, desc);
/*     */         } 
/*     */ 
/*     */         
/* 196 */         for (Map.Entry<String, String> detail : map.entrySet()) {
/* 197 */           String key = detail.getKey();
/* 198 */           String desc = detail.getValue();
/* 199 */           String[] keys = key.split("-");
/* 200 */           if (keys != null && keys.length == 2) {
/* 201 */             List<String> useridList = new ArrayList<String>();
/* 202 */             useridList.add(keys[1]);
/*     */             
/* 204 */             InterfaceUtil.sendMsg(useridList, this.fad.getMsgtpids(), keys[0], desc, 4, sendParamMap);
/*     */           }
/*     */         
/*     */         } 
/*     */       } 
/* 209 */     } catch (Exception e) {
/* 210 */       this.bb.writeLog("定时扫描协作并提醒程序异常", e);
/* 211 */       e.printStackTrace();
/*     */     } 
/* 213 */     return this.fad;
/*     */   }
/*     */   
/*     */   private static void setCoworkList(String sql, List<Cowork> cwList, int type) {
/* 217 */     RecordSet rs = new RecordSet();
/* 218 */     rs.executeSql(sql);
/* 219 */     while (rs.next()) {
/* 220 */       Cowork cw = new Cowork();
/* 221 */       cw.setCoworkid(rs.getInt("id"));
/* 222 */       cw.setName(rs.getString("name"));
/* 223 */       cw.setCreater(rs.getString("creater"));
/* 224 */       cw.setCreatedate(rs.getString("createdate"));
/* 225 */       cw.setCreatetime(rs.getString("createtime"));
/* 226 */       cw.setType(type);
/* 227 */       cwList.add(cw);
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   static class Cowork
/*     */   {
/*     */     private int coworkid;
/*     */     
/*     */     private String name;
/*     */     
/*     */     private int type;
/*     */     
/*     */     private String creater;
/*     */     private String createdate;
/*     */     private String createtime;
/*     */     
/*     */     public int getCoworkid() {
/* 245 */       return this.coworkid;
/*     */     }
/*     */     
/*     */     public void setCoworkid(int coworkid) {
/* 249 */       this.coworkid = coworkid;
/*     */     }
/*     */     
/*     */     public String getName() {
/* 253 */       return this.name;
/*     */     }
/*     */     
/*     */     public void setName(String name) {
/* 257 */       this.name = name;
/*     */     }
/*     */     
/*     */     public int getType() {
/* 261 */       return this.type;
/*     */     }
/*     */     
/*     */     public void setType(int type) {
/* 265 */       this.type = type;
/*     */     }
/*     */     
/*     */     public String getCreatedate() {
/* 269 */       return this.createdate;
/*     */     }
/*     */     
/*     */     public void setCreatedate(String createdate) {
/* 273 */       this.createdate = createdate;
/*     */     }
/*     */     
/*     */     public String getCreatetime() {
/* 277 */       return this.createtime;
/*     */     }
/*     */     
/*     */     public void setCreatetime(String createtime) {
/* 281 */       this.createtime = createtime;
/*     */     }
/*     */     
/*     */     public String getCreater() {
/* 285 */       return this.creater;
/*     */     }
/*     */     
/*     */     public void setCreater(String creater) {
/* 289 */       this.creater = creater;
/*     */     }
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/wxinterface/CoworkSendMsg.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */