/*     */ package weaver.wxinterface;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class FlowAndDoc
/*     */ {
/*     */   private String id;
/*     */   private String name;
/*     */   private int type;
/*     */   private int ifrepeat;
/*     */   private int iftoall;
/*     */   private String typeids;
/*     */   private String flowsordocs;
/*     */   private String names;
/*     */   private String msgtpids;
/*     */   private String msgtpnames;
/*     */   private int freqtime;
/*     */   private String lastscantime;
/*     */   private int ifcover;
/*     */   private int wfsettype;
/*     */   private int flowtype;
/*     */   private int ifwftodo;
/*     */   private int ifwffinish;
/*     */   private int ifwftimeout;
/*     */   private int ifwfreject;
/*     */   private String requestlevel;
/*     */   private String startdate;
/*     */   private String startcentext;
/*     */   private String enddate;
/*     */   private String endcentext;
/*     */   private String resourceids;
/*     */   private String resourcenames;
/*     */   private String resourcetype;
/*     */   private int ifsendsub;
/*     */   private int ifbatchsend;
/*     */   private int batchsendnum;
/*     */   private int batchsendinterval;
/*     */   
/*     */   public String getStartdate() {
/*  60 */     return this.startdate;
/*     */   }
/*     */   
/*     */   public void setStartdate(String startdate) {
/*  64 */     this.startdate = startdate;
/*     */   }
/*     */   
/*     */   public String getStartcentext() {
/*  68 */     return this.startcentext;
/*     */   }
/*     */   
/*     */   public void setStartcentext(String startcentext) {
/*  72 */     this.startcentext = startcentext;
/*     */   }
/*     */   
/*     */   public String getEnddate() {
/*  76 */     return this.enddate;
/*     */   }
/*     */   
/*     */   public void setEnddate(String enddate) {
/*  80 */     this.enddate = enddate;
/*     */   }
/*     */   
/*     */   public String getEndcentext() {
/*  84 */     return this.endcentext;
/*     */   }
/*     */   
/*     */   public void setEndcentext(String endcentext) {
/*  88 */     this.endcentext = endcentext;
/*     */   }
/*     */   
/*     */   public String getResourceids() {
/*  92 */     return this.resourceids;
/*     */   }
/*     */   
/*     */   public void setResourceids(String resourceids) {
/*  96 */     this.resourceids = resourceids;
/*     */   }
/*     */   
/*     */   public String getResourcenames() {
/* 100 */     return this.resourcenames;
/*     */   }
/*     */   
/*     */   public void setResourcenames(String resourcenames) {
/* 104 */     this.resourcenames = resourcenames;
/*     */   }
/*     */   
/*     */   public String getResourcetype() {
/* 108 */     return this.resourcetype;
/*     */   }
/*     */   
/*     */   public void setResourcetype(String resourcetype) {
/* 112 */     this.resourcetype = resourcetype;
/*     */   }
/*     */   
/*     */   public String getId() {
/* 116 */     return this.id;
/*     */   }
/*     */   
/*     */   public void setId(String id) {
/* 120 */     this.id = id;
/*     */   }
/*     */   
/*     */   public String getName() {
/* 124 */     return this.name;
/*     */   }
/*     */   
/*     */   public void setName(String name) {
/* 128 */     this.name = name;
/*     */   }
/*     */   
/*     */   public int getType() {
/* 132 */     return this.type;
/*     */   }
/*     */   
/*     */   public void setType(int type) {
/* 136 */     this.type = type;
/*     */   }
/*     */   
/*     */   public int getIfrepeat() {
/* 140 */     return this.ifrepeat;
/*     */   }
/*     */   
/*     */   public void setIfrepeat(int ifrepeat) {
/* 144 */     this.ifrepeat = ifrepeat;
/*     */   }
/*     */   
/*     */   public String getTypeids() {
/* 148 */     return this.typeids;
/*     */   }
/*     */   
/*     */   public void setTypeids(String typeids) {
/* 152 */     this.typeids = typeids;
/*     */   }
/*     */   
/*     */   public String getFlowsordocs() {
/* 156 */     return this.flowsordocs;
/*     */   }
/*     */   
/*     */   public void setFlowsordocs(String flowsordocs) {
/* 160 */     this.flowsordocs = flowsordocs;
/*     */   }
/*     */   
/*     */   public String getNames() {
/* 164 */     return this.names;
/*     */   }
/*     */   
/*     */   public void setNames(String names) {
/* 168 */     this.names = names;
/*     */   }
/*     */   
/*     */   public String getMsgtpids() {
/* 172 */     return this.msgtpids;
/*     */   }
/*     */   
/*     */   public void setMsgtpids(String msgtpids) {
/* 176 */     this.msgtpids = msgtpids;
/*     */   }
/*     */   
/*     */   public String getMsgtpnames() {
/* 180 */     return this.msgtpnames;
/*     */   }
/*     */   
/*     */   public void setMsgtpnames(String msgtpnames) {
/* 184 */     this.msgtpnames = msgtpnames;
/*     */   }
/*     */   
/*     */   public int getFreqtime() {
/* 188 */     return this.freqtime;
/*     */   }
/*     */   
/*     */   public void setFreqtime(int freqtime) {
/* 192 */     this.freqtime = freqtime;
/*     */   }
/*     */   
/*     */   public String getLastscantime() {
/* 196 */     return this.lastscantime;
/*     */   }
/*     */   
/*     */   public void setLastscantime(String lastscantime) {
/* 200 */     this.lastscantime = lastscantime;
/*     */   }
/*     */   
/*     */   public int getIftoall() {
/* 204 */     return this.iftoall;
/*     */   }
/*     */   
/*     */   public void setIftoall(int iftoall) {
/* 208 */     this.iftoall = iftoall;
/*     */   }
/*     */   
/*     */   public int getIfcover() {
/* 212 */     return this.ifcover;
/*     */   }
/*     */   
/*     */   public void setIfcover(int ifcover) {
/* 216 */     this.ifcover = ifcover;
/*     */   }
/*     */   
/*     */   public int getWfsettype() {
/* 220 */     return this.wfsettype;
/*     */   }
/*     */   
/*     */   public void setWfsettype(int wfsettype) {
/* 224 */     this.wfsettype = wfsettype;
/*     */   }
/*     */   
/*     */   public int getFlowtype() {
/* 228 */     return this.flowtype;
/*     */   }
/*     */   
/*     */   public void setFlowtype(int flowtype) {
/* 232 */     this.flowtype = flowtype;
/*     */   }
/*     */   
/*     */   public int getIfwftodo() {
/* 236 */     return this.ifwftodo;
/*     */   }
/*     */   
/*     */   public void setIfwftodo(int ifwftodo) {
/* 240 */     this.ifwftodo = ifwftodo;
/*     */   }
/*     */   
/*     */   public int getIfwffinish() {
/* 244 */     return this.ifwffinish;
/*     */   }
/*     */   
/*     */   public void setIfwffinish(int ifwffinish) {
/* 248 */     this.ifwffinish = ifwffinish;
/*     */   }
/*     */   
/*     */   public int getIfwftimeout() {
/* 252 */     return this.ifwftimeout;
/*     */   }
/*     */   
/*     */   public void setIfwftimeout(int ifwftimeout) {
/* 256 */     this.ifwftimeout = ifwftimeout;
/*     */   }
/*     */   
/*     */   public int getIfwfreject() {
/* 260 */     return this.ifwfreject;
/*     */   }
/*     */   
/*     */   public void setIfwfreject(int ifwfreject) {
/* 264 */     this.ifwfreject = ifwfreject;
/*     */   }
/*     */   
/*     */   public String getRequestlevel() {
/* 268 */     return this.requestlevel;
/*     */   }
/*     */   
/*     */   public void setRequestlevel(String requestlevel) {
/* 272 */     this.requestlevel = requestlevel;
/*     */   }
/*     */   
/*     */   public int getIfsendsub() {
/* 276 */     return this.ifsendsub;
/*     */   }
/*     */   
/*     */   public void setIfsendsub(int ifsendsub) {
/* 280 */     this.ifsendsub = ifsendsub;
/*     */   }
/*     */   
/*     */   public int getIfbatchsend() {
/* 284 */     return this.ifbatchsend;
/*     */   }
/*     */   
/*     */   public void setIfbatchsend(int ifbatchsend) {
/* 288 */     this.ifbatchsend = ifbatchsend;
/*     */   }
/*     */   
/*     */   public int getBatchsendnum() {
/* 292 */     return this.batchsendnum;
/*     */   }
/*     */   
/*     */   public void setBatchsendnum(int batchsendnum) {
/* 296 */     this.batchsendnum = batchsendnum;
/*     */   }
/*     */   
/*     */   public int getBatchsendinterval() {
/* 300 */     return this.batchsendinterval;
/*     */   }
/*     */   
/*     */   public void setBatchsendinterval(int batchsendinterval) {
/* 304 */     this.batchsendinterval = batchsendinterval;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/wxinterface/FlowAndDoc.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */