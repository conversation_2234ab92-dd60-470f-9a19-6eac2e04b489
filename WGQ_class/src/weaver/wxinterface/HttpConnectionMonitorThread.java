/*    */ package weaver.wxinterface;
/*    */ 
/*    */ import org.apache.commons.httpclient.MultiThreadedHttpConnectionManager;
/*    */ 
/*    */ public class HttpConnectionMonitorThread
/*    */   extends Thread {
/*    */   private final MultiThreadedHttpConnectionManager connMgr;
/*    */   private volatile boolean shutdown;
/*    */   
/*    */   public HttpConnectionMonitorThread(MultiThreadedHttpConnectionManager connMgr) {
/* 11 */     this.connMgr = connMgr;
/*    */   }
/*    */ 
/*    */   
/*    */   public void run() {
/*    */     try {
/* 17 */       while (!this.shutdown) {
/* 18 */         synchronized (this) {
/* 19 */           wait(5000L);
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */           
/* 25 */           this.connMgr.closeIdleConnections(120000L);
/*    */         }
/*    */       
/*    */       }
/*    */     
/* 30 */     } catch (InterruptedException interruptedException) {}
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public void shutdown() {
/* 36 */     this.shutdown = true;
/* 37 */     synchronized (this) {
/* 38 */       notifyAll();
/*    */     } 
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/wxinterface/HttpConnectionMonitorThread.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */