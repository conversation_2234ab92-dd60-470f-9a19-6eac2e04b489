/*      */ package weaver.wxinterface;
/*      */ 
/*      */ import java.net.InetAddress;
/*      */ import java.net.URLEncoder;
/*      */ import java.net.UnknownHostException;
/*      */ import java.text.SimpleDateFormat;
/*      */ import java.util.ArrayList;
/*      */ import java.util.HashMap;
/*      */ import java.util.List;
/*      */ import java.util.Map;
/*      */ import org.apache.commons.httpclient.HttpClient;
/*      */ import org.apache.commons.httpclient.HttpMethod;
/*      */ import org.apache.commons.httpclient.NameValuePair;
/*      */ import org.apache.commons.httpclient.methods.PostMethod;
/*      */ import org.apache.commons.lang.math.NumberUtils;
/*      */ import org.json.JSONArray;
/*      */ import org.json.JSONException;
/*      */ import org.json.JSONObject;
/*      */ import weaver.conn.ConnStatement;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.docs.docs.DocImageManager;
/*      */ import weaver.file.Prop;
/*      */ import weaver.general.BaseBean;
/*      */ import weaver.general.GCONST;
/*      */ import weaver.general.Util;
/*      */ import weaver.hrm.User;
/*      */ import weaver.hrm.resource.ResourceComInfo;
/*      */ import weaver.mobile.plugin.ecology.service.HrmResourceService;
/*      */ import weaver.systeminfo.SysMaintenanceLog;
/*      */ 
/*      */ 
/*      */ public class InterfaceUtil
/*      */ {
/*   34 */   private static String wxsysurl = "";
/*   35 */   private static String token = "";
/*   36 */   private static String outsysid = "";
/*   37 */   private static String userkeytype = "";
/*   38 */   private static String uuid = "";
/*      */   
/*   40 */   private static int ctimeout = 8000;
/*   41 */   private static int stimeout = 1200000;
/*   42 */   public static BaseBean bb = new BaseBean(); private static final String reg = "/weaver/weaver.file.FileDownload?fileid=";
/*      */   
/*      */   static {
/*   45 */     loadProp();
/*      */   }
/*      */   
/*      */   public static String getWxsysurl() {
/*   49 */     if (WxInterfaceInit.isCluster()) loadProp(); 
/*   50 */     return wxsysurl;
/*      */   }
/*      */   
/*      */   public static void setWxsysurl(String wxsysurl2) {
/*   54 */     wxsysurl = wxsysurl2;
/*      */   }
/*      */   
/*      */   public static String getToken() {
/*   58 */     if (WxInterfaceInit.isCluster()) loadProp(); 
/*   59 */     return token;
/*      */   }
/*      */   
/*      */   public static String getOutsysid() {
/*   63 */     if (WxInterfaceInit.isCluster()) loadProp(); 
/*   64 */     return outsysid;
/*      */   }
/*      */   
/*      */   public static String getUserkeytype() {
/*   68 */     if (WxInterfaceInit.isCluster()) loadProp(); 
/*   69 */     return userkeytype;
/*      */   }
/*      */   
/*      */   public static String getUUID() {
/*   73 */     if (WxInterfaceInit.isCluster()) loadProp(); 
/*   74 */     return uuid;
/*      */   }
/*      */   
/*      */   public static int getCtimeout() {
/*   78 */     return ctimeout;
/*      */   }
/*      */   
/*      */   public static void setCtimeout(int ctimeout) {
/*   82 */     InterfaceUtil.ctimeout = ctimeout;
/*      */   }
/*      */   
/*      */   public static int getStimeout() {
/*   86 */     return stimeout;
/*      */   }
/*      */   
/*      */   public static void setStimeout(int stimeout) {
/*   90 */     InterfaceUtil.stimeout = stimeout;
/*      */   }
/*      */   
/*      */   public static void loadProp() {
/*   94 */     RecordSet rs = new RecordSet();
/*   95 */     rs.executeSql("select * from wx_basesetting");
/*   96 */     if (rs.next()) {
/*   97 */       wxsysurl = Util.null2String(rs.getString("wxsysurl"));
/*   98 */       token = Util.null2String(rs.getString("accesstoken"));
/*   99 */       outsysid = Util.null2String(rs.getString("outsysid"));
/*  100 */       userkeytype = Util.null2String(rs.getString("userkeytype"));
/*      */ 
/*      */       
/*  103 */       uuid = Util.null2String(rs.getString("uuid"));
/*      */     } else {
/*  105 */       wxsysurl = Util.null2String(Prop.getPropValue("wxinterface", "wxsysurl"));
/*  106 */       token = Util.null2String(Prop.getPropValue("wxinterface", "accesstoken"));
/*  107 */       outsysid = Util.null2String(Prop.getPropValue("wxinterface", "outsysid"));
/*  108 */       userkeytype = Util.null2String(Prop.getPropValue("wxinterface", "userkeytype"));
/*      */ 
/*      */       
/*  111 */       uuid = "";
/*  112 */       StringBuffer sb = new StringBuffer();
/*  113 */       sb.append("insert into wx_basesetting (wxsysurl,accesstoken,userkeytype,outsysid,ctimeout,stimeout,uuid)");
/*  114 */       sb.append(" values ('" + wxsysurl + "','" + token + "','" + userkeytype + "',");
/*  115 */       sb.append("'" + outsysid + "','" + ctimeout + "','" + stimeout + "','')");
/*  116 */       rs.executeSql(sb.toString());
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static boolean wxCheckLogin(String ticket) {
/*  128 */     String result = "";
/*  129 */     PostMethod postMethod = null;
/*      */     try {
/*  131 */       postMethod = new PostMethod(String.valueOf(getWxsysurl()) + "/wxthirdapi/checkUserLogin");
/*  132 */       NameValuePair[] param = new NameValuePair[2];
/*  133 */       param[0] = new NameValuePair("accesstoken", getToken());
/*  134 */       param[1] = new NameValuePair("ticket", ticket);
/*  135 */       postMethod.setRequestBody(param);
/*  136 */       postMethod.getParams().setParameter("http.protocol.content-charset", "UTF8");
/*      */ 
/*      */       
/*  139 */       HttpClient http = HttpClientConnectionManager.getHttpClient();
/*  140 */       http.getHttpConnectionManager().getParams().setConnectionTimeout(ctimeout);
/*  141 */       http.getHttpConnectionManager().getParams().setSoTimeout(stimeout);
/*      */       
/*  143 */       http.executeMethod((HttpMethod)postMethod);
/*  144 */       result = postMethod.getResponseBodyAsString();
/*      */       
/*  146 */       JSONObject rjson = new JSONObject(result);
/*  147 */       if ("1".equals(rjson.getString("flag"))) {
/*  148 */         return true;
/*      */       }
/*  150 */       return false;
/*      */     }
/*  152 */     catch (Exception e) {
/*  153 */       bb.writeLog("调用云桥系统接口异常---异常信息：" + e);
/*  154 */       return false;
/*      */     } finally {
/*      */       try {
/*  157 */         if (postMethod != null)
/*  158 */           postMethod.releaseConnection(); 
/*  159 */       } catch (Exception exception) {}
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static JSONArray getMsgTp() {
/*  171 */     String result = "";
/*  172 */     PostMethod postMethod = null;
/*      */     try {
/*  174 */       postMethod = new PostMethod(String.valueOf(getWxsysurl()) + "/wxthirdapi/getWxMsgApiList");
/*  175 */       NameValuePair[] param = new NameValuePair[2];
/*  176 */       param[0] = new NameValuePair("accesstoken", getToken());
/*  177 */       param[1] = new NameValuePair("outsysid", getOutsysid());
/*  178 */       postMethod.setRequestBody(param);
/*  179 */       postMethod.getParams().setParameter("http.protocol.content-charset", "UTF8");
/*      */ 
/*      */       
/*  182 */       HttpClient http = HttpClientConnectionManager.getHttpClient();
/*  183 */       http.getHttpConnectionManager().getParams().setConnectionTimeout(ctimeout);
/*  184 */       http.getHttpConnectionManager().getParams().setSoTimeout(stimeout);
/*      */       
/*  186 */       http.executeMethod((HttpMethod)postMethod);
/*  187 */       result = postMethod.getResponseBodyAsString();
/*      */       
/*  189 */       JSONObject rjson = new JSONObject(result);
/*  190 */       if ("0".equals(rjson.getString("errcode")))
/*      */       {
/*  192 */         return rjson.getJSONArray("apilist");
/*      */       }
/*  194 */       return null;
/*      */     }
/*  196 */     catch (Exception e) {
/*  197 */       bb.writeLog("调用云桥系统接口获取消息模板异常---异常信息：" + e);
/*  198 */       return null;
/*      */     } finally {
/*      */       try {
/*  201 */         if (postMethod != null)
/*  202 */           postMethod.releaseConnection(); 
/*  203 */       } catch (Exception exception) {}
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static boolean sendMsg(String userid, int type, String content) {
/*  218 */     List<String> useridList = new ArrayList<String>();
/*  219 */     useridList.add(userid);
/*  220 */     return sendMsg(useridList, type, content);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static boolean sendMsg(List<String> useridList, int type, String content) {
/*  230 */     return sendMsg(useridList, type, "", content);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static boolean sendMsg(String userid, int type, String dataid, String content) {
/*  241 */     List<String> useridList = new ArrayList<String>();
/*  242 */     useridList.add(userid);
/*  243 */     return sendMsg(useridList, type, dataid, content);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static boolean sendMsg(List<String> useridList, int type, String dataid, String content) {
/*  254 */     Map<String, String> msgmap = sendMsg2(useridList, type, dataid, content);
/*  255 */     boolean flag = (Util.getIntValue(msgmap.get("msgcode"), -1) == 0);
/*  256 */     return flag;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static Map<String, String> sendMsg2(String userid, int type, String content) {
/*  268 */     List<String> useridList = new ArrayList<String>();
/*  269 */     useridList.add(userid);
/*  270 */     return sendMsg2(useridList, type, content);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static Map<String, String> sendMsg2(List<String> useridList, int type, String content) {
/*  280 */     return sendMsg2(useridList, type, "", content);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static Map<String, String> sendMsg2(String userid, int type, String dataid, String content) {
/*  293 */     List<String> useridList = new ArrayList<String>();
/*  294 */     useridList.add(userid);
/*  295 */     return sendMsg2(useridList, type, dataid, content);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static Map<String, String> sendMsg2(List<String> useridList, int type, String dataid, String content) {
/*  309 */     String tpids = "";
/*  310 */     if (type == 1) {
/*  311 */       List<FlowAndDoc> flowList = WxInterfaceInit.getFlowList();
/*  312 */       if (flowList != null && flowList.size() > 0)
/*  313 */         for (FlowAndDoc fad : flowList) {
/*  314 */           if (fad != null) {
/*  315 */             String msgtpids = fad.getMsgtpids();
/*  316 */             if (msgtpids != null && !"".equals(msgtpids)) {
/*  317 */               String[] mtps = msgtpids.split(",");
/*  318 */               if (mtps != null && mtps.length > 0) {
/*  319 */                 byte b; int i; String[] arrayOfString; for (i = (arrayOfString = mtps).length, b = 0; b < i; ) { String mtp = arrayOfString[b];
/*  320 */                   if (mtp != null && !"".equals(mtp)) {
/*  321 */                     tpids = String.valueOf(tpids) + "," + mtp;
/*      */                   }
/*      */                   b++; }
/*      */               
/*      */               } 
/*      */             } 
/*      */           } 
/*      */         }  
/*  329 */     } else if (type == 2) {
/*  330 */       List<FlowAndDoc> docList = WxInterfaceInit.getDocList();
/*  331 */       if (docList != null && docList.size() > 0)
/*  332 */         for (FlowAndDoc fad : docList) {
/*  333 */           if (fad != null) {
/*  334 */             String msgtpids = fad.getMsgtpids();
/*  335 */             if (msgtpids != null && !"".equals(msgtpids)) {
/*  336 */               String[] mtps = msgtpids.split(",");
/*  337 */               if (mtps != null && mtps.length > 0) {
/*  338 */                 byte b; int i; String[] arrayOfString; for (i = (arrayOfString = mtps).length, b = 0; b < i; ) { String mtp = arrayOfString[b];
/*  339 */                   if (mtp != null && !"".equals(mtp)) {
/*  340 */                     tpids = String.valueOf(tpids) + "," + mtp;
/*      */                   }
/*      */                   b++; }
/*      */               
/*      */               } 
/*      */             } 
/*      */           } 
/*      */         }  
/*      */     } else {
/*  349 */       List<FlowAndDoc> msgList = WxInterfaceInit.getMsgList();
/*  350 */       if (msgList != null && msgList.size() > 0) {
/*  351 */         for (FlowAndDoc fad : msgList) {
/*  352 */           if (fad != null && 
/*  353 */             fad.getType() == type) {
/*  354 */             String msgtpids = fad.getMsgtpids();
/*  355 */             if (msgtpids != null && !"".equals(msgtpids)) {
/*  356 */               String[] mtps = msgtpids.split(",");
/*  357 */               if (mtps != null && mtps.length > 0) {
/*  358 */                 byte b; int i; String[] arrayOfString; for (i = (arrayOfString = mtps).length, b = 0; b < i; ) { String mtp = arrayOfString[b];
/*  359 */                   if (mtp != null && !"".equals(mtp)) {
/*  360 */                     tpids = String.valueOf(tpids) + "," + mtp;
/*      */                   }
/*      */                   
/*      */                   b++; }
/*      */               
/*      */               } 
/*      */             } 
/*      */           } 
/*      */         } 
/*      */       }
/*      */     } 
/*  371 */     if (!tpids.equals("")) {
/*  372 */       tpids = tpids.substring(1);
/*      */     }
/*      */     
/*  375 */     if (type == 9 && tpids.equals("")) {
/*  376 */       return null;
/*      */     }
/*  378 */     return sendMsg(useridList, tpids, dataid, content, type);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static Map<String, String> sendMsg(List<String> userList, String tpids, String dataid, String content, int type) {
/*  391 */     return sendMsg(userList, tpids, dataid, content, type, null);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static Map<String, String> sendMsgForMobileMode(List<String> userList, String dataid, String content, String url, String typeid) {
/*  398 */     String tpids = "";
/*  399 */     List<FlowAndDoc> msgList = WxInterfaceInit.getMsgList();
/*  400 */     if (msgList != null && msgList.size() > 0) {
/*  401 */       for (FlowAndDoc fad : msgList) {
/*  402 */         if (fad != null && fad.getType() == 15 && (
/*  403 */           typeid.equals("-1") || fad.getFlowsordocs().equals("-1") || ("," + fad.getFlowsordocs() + ",").indexOf("," + typeid + ",") > -1)) {
/*  404 */           String msgtpids = fad.getMsgtpids();
/*  405 */           if (msgtpids != null && !"".equals(msgtpids)) {
/*  406 */             String[] mtps = msgtpids.split(",");
/*  407 */             if (mtps != null && mtps.length > 0) {
/*  408 */               byte b; int i; String[] arrayOfString; for (i = (arrayOfString = mtps).length, b = 0; b < i; ) { String mtp = arrayOfString[b];
/*  409 */                 if (mtp != null && !"".equals(mtp)) {
/*  410 */                   tpids = String.valueOf(tpids) + "," + mtp;
/*      */                 }
/*      */                 b++; }
/*      */             
/*      */             } 
/*      */           } 
/*      */         } 
/*      */       } 
/*      */     }
/*  419 */     if (!tpids.equals("")) {
/*  420 */       tpids = tpids.substring(1);
/*      */     }
/*  422 */     Map<String, Object> map = new HashMap<String, Object>();
/*  423 */     map.put("msgurl", url);
/*  424 */     return sendMsg(userList, tpids, dataid, content, 15, map);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static Map<String, String> sendMsgForMobileMode(List<String> userList, String dataid, String content, String typeid, Map<String, Object> map) {
/*  431 */     String tpids = "";
/*  432 */     List<FlowAndDoc> msgList = WxInterfaceInit.getMsgList();
/*  433 */     if (msgList != null && msgList.size() > 0) {
/*  434 */       for (FlowAndDoc fad : msgList) {
/*  435 */         if (fad != null && fad.getType() == 15 && (
/*  436 */           typeid.equals("-1") || fad.getFlowsordocs().equals("-1") || ("," + fad.getFlowsordocs() + ",").indexOf("," + typeid + ",") > -1)) {
/*  437 */           String msgtpids = fad.getMsgtpids();
/*  438 */           if (msgtpids != null && !"".equals(msgtpids)) {
/*  439 */             String[] mtps = msgtpids.split(",");
/*  440 */             if (mtps != null && mtps.length > 0) {
/*  441 */               byte b; int i; String[] arrayOfString; for (i = (arrayOfString = mtps).length, b = 0; b < i; ) { String mtp = arrayOfString[b];
/*  442 */                 if (mtp != null && !"".equals(mtp)) {
/*  443 */                   tpids = String.valueOf(tpids) + "," + mtp;
/*      */                 }
/*      */                 b++; }
/*      */             
/*      */             } 
/*      */           } 
/*      */         } 
/*      */       } 
/*      */     }
/*  452 */     if (!tpids.equals("")) {
/*  453 */       tpids = tpids.substring(1);
/*      */     }
/*  455 */     return sendMsg(userList, tpids, dataid, content, 15, map);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static Map<String, String> sendMsg(List<String> userList, String tpids, String dataid, String content, int type, Map<String, Object> map) {
/*      */     ConnStatement ConnStatement;
/*      */     SimpleDateFormat sdf;
/*      */     String nowDate, logdate, attendStatus, sql;
/*  470 */     List<String> allUserList = (map == null || map.get("allUserList") == null) ? userList : (List<String>)map.get("allUserList");
/*  471 */     List<List<String>> sendIdList = new ArrayList<List<String>>();
/*  472 */     List<String> idList = new ArrayList<String>();
/*  473 */     if (userList != null) {
/*  474 */       for (String userid : userList) {
/*  475 */         if (idList.size() > 899) {
/*  476 */           sendIdList.add(idList);
/*  477 */           idList = new ArrayList<String>();
/*      */         } 
/*  479 */         if (!"".equals(userid) && !"-1".equals(userid)) idList.add(userid); 
/*      */       } 
/*  481 */       sendIdList.add(idList);
/*      */     } 
/*      */     
/*  484 */     String msgcode = "-1";
/*  485 */     String msginfo = "";
/*  486 */     String errmsg = "";
/*  487 */     StringBuffer receiveUsers = new StringBuffer();
/*  488 */     StringBuffer receiveIds = new StringBuffer();
/*  489 */     String otherTypes = "";
/*  490 */     if (map != null && map.containsKey("otherType")) {
/*  491 */       otherTypes = (String)map.get("otherType");
/*      */     }
/*  493 */     int otherType = Util.getIntValue(otherTypes, 2);
/*      */     
/*  495 */     try { for (List<String> useridList : sendIdList)
/*      */       {
/*  497 */         if (useridList != null && useridList.size() > 0 && !"".equals(tpids)) {
/*  498 */           String userids = "", allUsers = "";
/*  499 */           JSONArray accountlist = new JSONArray();
/*  500 */           JSONObject account = null;
/*  501 */           Map<Object, Object> accounts = new HashMap<Object, Object>();
/*      */           
/*  503 */           String firstUserid = useridList.get(0);
/*  504 */           if (firstUserid.indexOf("@all") < 0) {
/*  505 */             if (GCONST.getMOREACCOUNTLANDING()) {
/*      */ 
/*      */               
/*  508 */               boolean nosendsub = (Util.getIntValue(Prop.getPropValue("wxinterface", "nosendtype_" + type), 0) == 1);
/*      */               
/*  510 */               int ifsendsub = 1;
/*  511 */               if (map != null && map.containsKey("ifsendsub")) {
/*  512 */                 ifsendsub = Util.getIntValue(map.get("ifsendsub").toString(), 1);
/*      */               }
/*      */               
/*  515 */               RecordSet rs = new RecordSet();
/*  516 */               rs.executeSql("select t1.id,t1.loginid,t1.account,t1.workcode,t2.id as mainid,t2.loginid as mainloginid,t2.account as mainaccount,t2.workcode as mainworkcode from HrmResource t1,HrmResource t2 where t1.belongto=t2.id and t1.status in (0,1,2,3) and t1.status != 10 and t1.accounttype=1 and t2.status in (0,1,2,3) and t2.status != 10 and (t2.accounttype=0 or t2.accounttype is null)");
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */               
/*  522 */               while (rs.next()) {
/*  523 */                 String subid = Util.null2String(rs.getString("id"));
/*  524 */                 String subloginid = Util.null2String(rs.getString("loginid"));
/*  525 */                 String subaccount = Util.null2String(rs.getString("account"));
/*  526 */                 String subworkcode = Util.null2String(rs.getString("workcode"));
/*  527 */                 String mainid = Util.null2String(rs.getString("mainid"));
/*  528 */                 String mainloginid = Util.null2String(rs.getString("mainloginid"));
/*  529 */                 String mainaccount = Util.null2String(rs.getString("mainaccount"));
/*  530 */                 String mainworkcode = Util.null2String(rs.getString("mainworkcode"));
/*  531 */                 if (useridList.indexOf(subid) > -1) {
/*      */                   
/*  533 */                   if (allUserList.indexOf(mainid) == -1 || ifsendsub == 1) {
/*  534 */                     String mainuserid = mainid;
/*  535 */                     String subuserid = subid;
/*      */                     
/*  537 */                     if (getUserkeytype().equals("loginid")) {
/*  538 */                       mainuserid = transUserId(mainloginid, mainaccount);
/*  539 */                       subuserid = transUserId(subloginid, subaccount);
/*  540 */                     } else if (getUserkeytype().equals("workcode")) {
/*  541 */                       mainuserid = mainworkcode;
/*  542 */                       subuserid = subworkcode;
/*      */                     } 
/*      */                     
/*  545 */                     if (!"".equals(mainuserid) && !"".equals(subuserid) && !nosendsub) {
/*  546 */                       account = new JSONObject();
/*  547 */                       account.put("mainuserid", mainuserid);
/*  548 */                       account.put("subuserid", subuserid);
/*  549 */                       accountlist.put(account);
/*  550 */                       receiveUsers.append("," + mainuserid + "|" + subuserid);
/*      */                     } 
/*  552 */                     receiveIds.append("," + subid);
/*      */                   } 
/*  554 */                   useridList.remove(subid);
/*      */                 } 
/*      */               } 
/*      */             } 
/*      */ 
/*      */             
/*  560 */             for (String userid : useridList) {
/*  561 */               String id = userid;
/*  562 */               if (getUserkeytype().equals("loginid")) {
/*  563 */                 id = Util.null2String(transUserId(userid, 1));
/*  564 */               } else if (getUserkeytype().equals("workcode")) {
/*  565 */                 id = getUserWorkcode(userid);
/*      */               } 
/*  567 */               if (!"".equals(id)) {
/*  568 */                 userids = String.valueOf(userids) + "," + id;
/*  569 */                 receiveUsers.append("," + id);
/*      */               } 
/*      */               
/*  572 */               receiveIds.append("," + userid);
/*      */             } 
/*      */             
/*  575 */             if (!userids.equals("")) {
/*  576 */               userids = userids.substring(1);
/*  577 */               account = new JSONObject();
/*  578 */               account.put("mainuserid", userids);
/*  579 */               account.put("subuserid", "");
/*  580 */               accountlist.put(account);
/*      */             } 
/*      */           } else {
/*  583 */             allUsers = firstUserid;
/*  584 */             userids = firstUserid;
/*  585 */             receiveIds.append(allUsers);
/*      */           } 
/*  587 */           if (accountlist.length() > 0 || !userids.equals("")) {
/*  588 */             accounts.put("userlist", accountlist);
/*  589 */             if (tpids.startsWith(",")) tpids = tpids.substring(1); 
/*  590 */             if (tpids.endsWith(",")) tpids = tpids.substring(0, tpids.length() - 1);
/*      */             
/*  592 */             PostMethod postMethod = null;
/*      */ 
/*      */ 
/*      */             
/*      */             try {
/*  597 */               postMethod = new PostMethod(String.valueOf(getWxsysurl()) + "/wxthirdapi/sendWxMsg");
/*  598 */               NameValuePair[] param = new NameValuePair[20];
/*  599 */               param[0] = new NameValuePair("accesstoken", getToken());
/*  600 */               param[1] = new NameValuePair("userids", allUsers);
/*  601 */               param[2] = new NameValuePair("tpids", tpids);
/*  602 */               param[3] = new NameValuePair("dataid", dataid);
/*  603 */               param[4] = new NameValuePair("content", Util.replaceHtml(content));
/*  604 */               param[5] = new NameValuePair("outsysid", getOutsysid());
/*  605 */               param[6] = new NameValuePair("userlist", (new JSONObject(accounts)).toString());
/*  606 */               param[9] = new NameValuePair("uuid", getUUID());
/*      */ 
/*      */ 
/*      */               
/*  610 */               String imgurl = "";
/*  611 */               if (map != null && map.containsKey("imgurl")) {
/*  612 */                 imgurl = (String)map.get("imgurl");
/*      */               }
/*  614 */               param[7] = new NameValuePair("imgurl", imgurl);
/*  615 */               param[8] = new NameValuePair("datatype", String.valueOf(type));
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */               
/*  623 */               String msgurl = "", hasurl = "";
/*  624 */               if (map != null && map.containsKey("msgurl")) {
/*  625 */                 msgurl = (String)map.get("msgurl");
/*  626 */                 if (!"".equals(msgurl)) {
/*  627 */                   hasurl = "1";
/*      */                 }
/*      */               } 
/*  630 */               if (msgurl.equals("") && 
/*  631 */                 map != null && map.containsKey("mobilemodeUrl")) {
/*  632 */                 msgurl = (String)map.get("mobilemodeUrl");
/*  633 */                 if (!"".equals(msgurl)) {
/*  634 */                   hasurl = "1";
/*      */                 }
/*      */               } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */               
/*  645 */               String linkurl = "";
/*  646 */               if (map != null && map.containsKey("linkurl")) {
/*  647 */                 linkurl = (String)map.get("linkurl");
/*  648 */                 hasurl = "1";
/*      */               } 
/*  650 */               param[10] = new NameValuePair("msgurl", msgurl);
/*  651 */               param[12] = new NameValuePair("linkurl", linkurl);
/*  652 */               String docCustom = "";
/*  653 */               if (type == 2) {
/*  654 */                 int ifFdSSo = Util.getIntValue(Prop.getPropValue("fdcsdev", "ifFdSSo"), 0);
/*  655 */                 if (ifFdSSo == 1) {
/*  656 */                   String gsbm = "", csdw = "", zsdw = "", wzzy = "";
/*  657 */                   if (map != null) {
/*  658 */                     if (map.containsKey("gsbm")) {
/*  659 */                       gsbm = (String)map.get("gsbm");
/*      */                     }
/*  661 */                     if (map.containsKey("csdw")) {
/*  662 */                       csdw = (String)map.get("csdw");
/*      */                     }
/*  664 */                     if (map.containsKey("zsdw")) {
/*  665 */                       zsdw = (String)map.get("zsdw");
/*      */                     }
/*  667 */                     if (map.containsKey("wzzy")) {
/*  668 */                       wzzy = (String)map.get("wzzy");
/*      */                     }
/*      */                   } 
/*  671 */                   JSONObject wd = new JSONObject();
/*  672 */                   wd.put("gsbm", gsbm);
/*  673 */                   wd.put("csdw", csdw);
/*  674 */                   wd.put("zsdw", zsdw);
/*  675 */                   wd.put("wzzy", wzzy);
/*  676 */                   docCustom = wd.toString();
/*      */                 } 
/*      */               } 
/*  679 */               param[13] = new NameValuePair("docCustom", docCustom);
/*  680 */               String csdev_zyzl = "";
/*  681 */               int ifZYZL = Util.getIntValue(Prop.getPropValue("zyzl_csdev", "ifZYZL"), 0);
/*  682 */               if (ifZYZL == 1 && 
/*  683 */                 map != null && map.containsKey("csdev_zyzl")) {
/*  684 */                 csdev_zyzl = (String)map.get("csdev_zyzl");
/*      */               }
/*      */               
/*  687 */               param[17] = new NameValuePair("csdev_zyzl", csdev_zyzl);
/*  688 */               String msgPcUrl = "";
/*  689 */               if (map != null && map.containsKey("msgPcUrl")) {
/*  690 */                 msgPcUrl = (String)map.get("msgPcUrl");
/*  691 */                 hasurl = "1";
/*      */               } 
/*  693 */               param[11] = new NameValuePair("hasurl", hasurl);
/*  694 */               param[14] = new NameValuePair("msgPcUrl", msgPcUrl);
/*      */               
/*  696 */               String mpNewsJSONArray = "";
/*  697 */               if (map != null && map.containsKey("mpNewsJSONArray")) {
/*  698 */                 mpNewsJSONArray = (String)map.get("mpNewsJSONArray");
/*      */               }
/*  700 */               param[15] = new NameValuePair("mpNewsJSONArray", mpNewsJSONArray);
/*      */               
/*  702 */               String requesttype = "";
/*  703 */               if (map != null && map.containsKey("requesttype")) {
/*  704 */                 requesttype = (String)map.get("requesttype");
/*      */               }
/*  706 */               param[16] = new NameValuePair("requesttype", requesttype);
/*  707 */               String ddTodoJSONArray = "";
/*  708 */               if (map != null && map.containsKey("ddTodoJSONArray")) {
/*  709 */                 ddTodoJSONArray = (String)map.get("ddTodoJSONArray");
/*      */               }
/*  711 */               param[18] = new NameValuePair("ddTodoJSONArray", ddTodoJSONArray);
/*      */               
/*  713 */               String wfJson = "";
/*  714 */               if (map != null && map.containsKey("wfJson")) {
/*  715 */                 wfJson = (String)map.get("wfJson");
/*      */               }
/*  717 */               param[19] = new NameValuePair("wfJson", wfJson);
/*  718 */               postMethod.setRequestBody(param);
/*  719 */               postMethod.getParams().setParameter("http.protocol.content-charset", "UTF8");
/*  720 */               postMethod.setRequestHeader("Connection", "close");
/*      */               
/*  722 */               HttpClient http = HttpClientConnectionManager.getHttpClient();
/*  723 */               http.getHttpConnectionManager().getParams().setConnectionTimeout(ctimeout);
/*  724 */               http.getHttpConnectionManager().getParams().setSoTimeout(stimeout);
/*      */               
/*  726 */               http.executeMethod((HttpMethod)postMethod);
/*  727 */               String result = postMethod.getResponseBodyAsString();
/*      */               
/*  729 */               JSONObject rjson = new JSONObject(result);
/*  730 */               if (!"0".equals(rjson.getString("errcode"))) {
/*  731 */                 msgcode = "-1";
/*  732 */                 msginfo = rjson.getString("errmsg");
/*  733 */                 errmsg = "调用云桥(" + getWxsysurl() + ")发送失败：" + rjson.getString("errmsg");
/*  734 */                 bb.writeLog("调用云桥(" + getWxsysurl() + ")接口发送消息失败---数据id：" + dataid + " 数据内容：" + Util.getMoreStr(content, 5, "...") + "  用户列表：" + userids + " 模板：" + tpids + " 错误信息：" + rjson.getString("errmsg"));
/*      */               } else {
/*  736 */                 msgcode = "0";
/*  737 */                 msginfo = "发送完成";
/*  738 */                 errmsg = rjson.getString("errmsg");
/*      */               } 
/*  740 */             } catch (Exception e) {
/*  741 */               errmsg = "调用云桥(" + getWxsysurl() + ")接口异常：" + e;
/*  742 */               bb.writeLog("调用云桥(" + getWxsysurl() + ")接口异常---数据id：" + dataid + " 数据内容：" + Util.getMoreStr(content, 5, "...") + "  用户列表：" + userids + " 模板：" + tpids + " 异常信息：" + e);
/*  743 */               msgcode = "-1";
/*  744 */               msginfo = "发送失败"; continue;
/*      */             } finally {
/*      */               try {
/*  747 */                 if (postMethod != null)
/*  748 */                   postMethod.releaseConnection(); 
/*  749 */               } catch (Exception exception) {}
/*      */             } 
/*      */             continue;
/*      */           } 
/*  753 */           msginfo = String.valueOf(msginfo) + "没有接收人";
/*  754 */           errmsg = msginfo;
/*  755 */           bb.writeLog("调用云桥(" + getWxsysurl() + ")接口发送消息失败---数据id：" + dataid + " 数据内容：" + Util.getMoreStr(content, 5, "...") + " 模板：" + tpids + " 异常信息：" + msginfo);
/*      */           continue;
/*      */         } 
/*  758 */         if (useridList == null || useridList.size() <= 0) msginfo = String.valueOf(msginfo) + "没有接收人"; 
/*  759 */         if ("".equals(tpids)) msginfo = String.valueOf(msginfo) + "没有设置发送规则，请联系管理员进行设置"; 
/*  760 */         errmsg = msginfo;
/*  761 */         bb.writeLog("调用云桥(" + getWxsysurl() + ")接口发送消息失败---数据id：" + dataid + " 数据内容：" + Util.getMoreStr(content, 5, "...") + " 模板：" + tpids + " 异常信息：" + msginfo);
/*      */       
/*      */       }
/*      */        }
/*      */     
/*  766 */     catch (Exception e)
/*  767 */     { msginfo = String.valueOf(msginfo) + "发送消息出现异常";
/*  768 */       errmsg = "发送消息出现异常：" + e.getMessage();
/*  769 */       bb.writeLog("调用云桥(" + getWxsysurl() + ")接口发送消息失败---数据id：" + dataid + " 数据内容：" + Util.getMoreStr(content, 5, "...") + " 模板：" + tpids + " 异常信息：" + e.getMessage());
/*      */       
/*  771 */       ConnStatement connStatement = null; } finally { ConnStatement connStatement = null;
/*  772 */       SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
/*  773 */       String str1 = simpleDateFormat.format(Long.valueOf(System.currentTimeMillis()));
/*  774 */       String str2 = "", str3 = "";
/*  775 */       if (map != null && map.containsKey("logdate")) {
/*  776 */         str2 = (String)map.get("logdate");
/*      */       }
/*  778 */       if (map != null && map.containsKey("attendStatus")) {
/*  779 */         str3 = (String)map.get("attendStatus");
/*      */       }
/*  781 */       String str4 = "insert into WX_SCANLOG (type,othertypes,reourceid,otherid,scantime,receiveusers,content,resultstatus,resultcontent,logdate,status) values (" + 
/*      */         
/*  783 */         type + "," + otherType + ",'" + Util.getIntValue(dataid, 0) + "',0,'" + str1 + "',?,?,?,?,'" + str2 + "','" + str3 + "')";
/*      */       
/*      */       try {
/*  786 */         errmsg = String.valueOf(errmsg) + "----模板ID：" + tpids + ((receiveIds.length() > 0) ? ("----接收人ids:" + receiveIds.toString().substring(1)) : "");
/*  787 */         if (map != null && map.containsKey("imgurl")) errmsg = String.valueOf(errmsg) + "----图片地址：" + (String)map.get("imgurl"); 
/*  788 */         errmsg = String.valueOf(errmsg) + "----系统IP：" + getLocalIP();
/*  789 */         errmsg = String.valueOf(errmsg) + "----系统UUID：" + getUUID();
/*      */         
/*  791 */         connStatement = new ConnStatement();
/*  792 */         connStatement.setStatementSql(str4);
/*  793 */         if (receiveUsers.length() > 0) {
/*  794 */           connStatement.setString(1, receiveUsers.toString().substring(1));
/*      */         } else {
/*  796 */           connStatement.setString(1, receiveUsers.toString());
/*      */         } 
/*  798 */         connStatement.setString(2, (type == 3) ? Util.getMoreStr(content, 5, "***") : content);
/*  799 */         connStatement.setString(3, msgcode);
/*  800 */         connStatement.setString(4, errmsg);
/*  801 */         connStatement.executeUpdate();
/*      */       }
/*  803 */       catch (Exception ex) {
/*  804 */         bb.writeLog("调用云桥(" + getWxsysurl() + ")接口发送消息后保存推送日志出错---数据id：" + dataid + " 数据内容：" + Util.getMoreStr(content, 5, "...") + " 模板：" + tpids + " 异常信息：" + ex.getMessage());
/*      */       } finally {
/*  806 */         if (connStatement != null)
/*      */         {
/*  808 */           connStatement.close();
/*      */         }
/*      */       }  }
/*      */ 
/*      */     
/*  813 */     Map<Object, Object> msgmap = new HashMap<Object, Object>();
/*  814 */     msgmap.put("msgcode", msgcode);
/*  815 */     msgmap.put("msginfo", msginfo);
/*  816 */     msgmap.put("errmsg", errmsg);
/*  817 */     return (Map)msgmap;
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   private static Map<String, String[]> getSubUserMap() {
/*  823 */     Map<String, String[]> subUserMap = (Map)new HashMap<String, String>();
/*  824 */     if (GCONST.getMOREACCOUNTLANDING()) {
/*  825 */       RecordSet rs = new RecordSet();
/*  826 */       rs.executeSql("select t1.id,t1.loginid,t1.account,t2.id as mainid,t2.loginid as mainloginid,t2.account as mainaccount from HrmResource t1,HrmResource t2 where t1.belongto=t2.id and t1.status in (0,1,2,3) and t1.status != 10 and t1.accounttype=1 and t2.status in (0,1,2,3) and t2.status != 10 and (t2.accounttype=0 or t2.accounttype is null)");
/*      */ 
/*      */       
/*  829 */       while (rs.next()) {
/*  830 */         String subid = Util.null2String(rs.getString("id"));
/*  831 */         String mainid = Util.null2String(rs.getString("mainid"));
/*  832 */         String mainloginid = Util.null2String(rs.getString("mainloginid"));
/*  833 */         String mainaccount = Util.null2String(rs.getString("mainaccount"));
/*  834 */         String[] strs = { mainid, mainloginid, mainaccount };
/*  835 */         subUserMap.put(subid, strs);
/*      */       } 
/*      */     } 
/*      */ 
/*      */ 
/*      */     
/*  841 */     return subUserMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static Map<String, String> setMsgFlag(String requestid, String workflowid, List<String> userList) {
/*  851 */     return setMsgFlag(requestid, workflowid, userList, 0, "");
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static Map<String, String> setMsgFlag(String requestid, String workflowid, List<String> userList, int dataType) {
/*  861 */     return setMsgFlag(requestid, workflowid, userList, 0, "", dataType);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static Map<String, String> setQSMFMsgFlag(String requestid, String workflowid, List<String> userList) {
/*  871 */     return setQSMFMsgFlag(requestid, workflowid, userList, 0, "");
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static Map<String, String> setMsgFlag(int dataid) {
/*  880 */     RecordSet rs = new RecordSet();
/*  881 */     rs.executeSql("select * from WX_SETMSGLOG where id = " + dataid);
/*  882 */     if (rs.next()) {
/*  883 */       String userstrs = Util.null2String(rs.getString("userstrs"));
/*  884 */       String requestid = Util.null2String(rs.getString("requestid"));
/*  885 */       String workflowid = Util.null2String(rs.getString("workflowid"));
/*  886 */       String errormsg = Util.null2String(rs.getString("errormsg"));
/*  887 */       Integer type = Integer.valueOf(rs.getInt("type"));
/*  888 */       String[] users = userstrs.split(",");
/*  889 */       List<String> userList = new ArrayList<String>();
/*  890 */       if (users != null && users.length > 0) {
/*  891 */         byte b; int i; String[] arrayOfString; for (i = (arrayOfString = users).length, b = 0; b < i; ) { String user = arrayOfString[b];
/*  892 */           if (user != null && !user.equals("")) {
/*  893 */             userList.add(user);
/*      */           }
/*      */           b++; }
/*      */       
/*      */       } 
/*  898 */       if ((new Integer(2)).equals(type)) {
/*  899 */         return deleteYZJMsg(requestid, workflowid, userList, dataid);
/*      */       }
/*      */       
/*  902 */       if ((new Integer(3)).equals(type)) {
/*  903 */         return deleteDDDBMsg(requestid, workflowid, userList, dataid);
/*      */       }
/*      */       
/*  906 */       if ((new Integer(5)).equals(type)) {
/*  907 */         return archiveDDMsg(requestid, workflowid, userstrs, dataid);
/*      */       }
/*  909 */       if ((new Integer(4)).equals(type)) {
/*  910 */         return deleteYZJDocMsg(requestid, workflowid, userList, dataid);
/*      */       }
/*  912 */       return setMsgFlag(requestid, workflowid, userList, dataid, errormsg);
/*      */     } 
/*  914 */     return null;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static Map<String, String> setMsgFlag(String requestid, String workflowid, List<String> userList, int dataid, String oldmsg) {
/*  923 */     return setMsgFlag(requestid, workflowid, userList, dataid, oldmsg, 1);
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public static Map<String, String> setMsgFlag(String requestid, String workflowid, List<String> userList, int dataid, String oldmsg, int dataType) {
/*      */     SimpleDateFormat sdf;
/*      */     String nowDate;
/*      */     ConnStatement ConnStatement;
/*  932 */     String sql, msgcode = "0";
/*  933 */     StringBuffer errmsg = new StringBuffer("调用云桥(" + getWxsysurl() + ")接口设置已办操作\n<br/>流程ID：" + requestid + "  路径ID：" + workflowid);
/*  934 */     StringBuffer userstrs = new StringBuffer();
/*      */ 
/*      */     
/*  937 */     try { List<List<String>> sendIdList = new ArrayList<List<String>>();
/*  938 */       List<String> idList = new ArrayList<String>();
/*  939 */       if (userList != null && userList.size() > 0) {
/*      */ 
/*      */ 
/*      */         
/*  943 */         for (String userid : userList) {
/*  944 */           if (idList.size() > 999) {
/*  945 */             sendIdList.add(idList);
/*  946 */             idList = new ArrayList<String>();
/*      */           } 
/*  948 */           if (!"".equals(userid) && !"-1".equals(userid)) {
/*  949 */             idList.add(userid);
/*      */           }
/*  951 */           userstrs.append("," + userid);
/*      */         } 
/*  953 */         sendIdList.add(idList);
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  959 */         List<FlowAndDoc> flowList = WxInterfaceInit.getFlowList();
/*  960 */         boolean issend = false;
/*  961 */         boolean msgSetIgnoreWorkflowId = (Util.getIntValue(Prop.getPropValue("msgSetIgnoreWorkflowId", "ignore"), 0) == 1);
/*  962 */         if (!msgSetIgnoreWorkflowId) {
/*  963 */           for (FlowAndDoc fad : flowList) {
/*  964 */             if (fad.getFlowsordocs().equals("-1")) {
/*  965 */               if (fad.getFlowtype() == 0)
/*  966 */                 issend = true;  continue;
/*      */             } 
/*  968 */             if (("," + fad.getFlowsordocs() + ",").indexOf("," + workflowid + ",") > -1) {
/*  969 */               if (fad.getFlowtype() == 1) {
/*      */                 continue;
/*      */               }
/*  972 */               issend = true;
/*      */               break;
/*      */             } 
/*  975 */             if (fad.getFlowtype() == 1) {
/*  976 */               issend = true;
/*      */               break;
/*      */             } 
/*      */           } 
/*      */         }
/*  981 */         if (issend || msgSetIgnoreWorkflowId) {
/*  982 */           Map<String, String[]> subUserMap = getSubUserMap();
/*  983 */           for (List<String> useridList : sendIdList) {
/*  984 */             PostMethod postMethod = null;
/*      */             try {
/*  986 */               if (useridList != null && useridList.size() > 0) {
/*  987 */                 StringBuffer oldUserids = new StringBuffer();
/*  988 */                 StringBuffer mainUserids = new StringBuffer();
/*  989 */                 StringBuffer receiveUsers = new StringBuffer();
/*  990 */                 for (String userid : useridList) {
/*  991 */                   String id = userid;
/*  992 */                   oldUserids.append("," + userid);
/*  993 */                   if (subUserMap.containsKey(userid)) {
/*  994 */                     String[] strs = subUserMap.get(userid);
/*  995 */                     id = strs[0];
/*      */                   } 
/*  997 */                   mainUserids.append("," + id);
/*  998 */                   if (getUserkeytype().equals("loginid")) {
/*  999 */                     id = Util.null2String(transUserId(id, 1));
/* 1000 */                   } else if (getUserkeytype().equals("workcode")) {
/* 1001 */                     id = getUserWorkcode(userid);
/*      */                   } 
/* 1003 */                   if ((receiveUsers + ",").indexOf("," + id + ",") < 0) {
/* 1004 */                     receiveUsers.append("," + id);
/*      */                   }
/*      */                 } 
/* 1007 */                 errmsg.append("\n<br/>原始传入用户ID：" + ((oldUserids.length() > 0) ? oldUserids.toString().substring(1) : oldUserids.toString()));
/* 1008 */                 if (GCONST.getMOREACCOUNTLANDING())
/* 1009 */                   errmsg.append("\n<br/>次账号转换成主账号后用户ID：" + ((mainUserids.length() > 0) ? mainUserids.toString().substring(1) : mainUserids.toString())); 
/* 1010 */                 errmsg.append("\n<br/>调用接口发送的用户标识：" + ((receiveUsers.length() > 0) ? receiveUsers.toString().substring(1) : receiveUsers.toString()));
/* 1011 */                 if (receiveUsers.length() > 0) {
/* 1012 */                   String allUsers = receiveUsers.toString().substring(1);
/*      */                   
/* 1014 */                   postMethod = new PostMethod(String.valueOf(getWxsysurl()) + "/wxthirdapi/dealYZJMsg");
/* 1015 */                   NameValuePair[] param = new NameValuePair[6];
/* 1016 */                   param[0] = new NameValuePair("accesstoken", getToken());
/* 1017 */                   param[1] = new NameValuePair("userids", allUsers);
/* 1018 */                   param[2] = new NameValuePair("msgdataid", requestid);
/* 1019 */                   param[3] = new NameValuePair("outsysid", getOutsysid());
/* 1020 */                   param[4] = new NameValuePair("uuid", getUUID());
/* 1021 */                   param[5] = new NameValuePair("msgdatatype", dataType);
/*      */                   
/* 1023 */                   postMethod.setRequestBody(param);
/* 1024 */                   postMethod.getParams().setParameter("http.protocol.content-charset", "UTF8");
/* 1025 */                   HttpClient http = HttpClientConnectionManager.getHttpClient();
/* 1026 */                   http.getHttpConnectionManager().getParams().setConnectionTimeout(ctimeout);
/* 1027 */                   http.getHttpConnectionManager().getParams().setSoTimeout(stimeout);
/*      */                   
/* 1029 */                   http.executeMethod((HttpMethod)postMethod);
/* 1030 */                   String result = postMethod.getResponseBodyAsString();
/*      */                   
/* 1032 */                   JSONObject rjson = new JSONObject(result);
/* 1033 */                   if (!"0".equals(rjson.getString("errcode"))) {
/* 1034 */                     msgcode = "-1";
/* 1035 */                     errmsg.append("\n<br/>失败：云桥返回信息-" + rjson.getString("errmsg"));
/*      */                   } else {
/* 1037 */                     errmsg.append("\n<br/>成功：云桥返回信息-" + rjson.getString("errmsg"));
/*      */                   } 
/*      */                 } else {
/*      */                   
/* 1041 */                   errmsg.append("\n<br/>提示：receiveUsers为空");
/*      */                 } 
/*      */               } else {
/*      */                 
/* 1045 */                 errmsg.append("\n<br/>提示：useridList为空");
/*      */               } 
/* 1047 */             } catch (Exception e) {
/* 1048 */               msgcode = "-1";
/* 1049 */               errmsg.append("\n<br/>失败：调用云桥接口程序异常:" + e.getMessage()); continue;
/*      */             } finally {
/*      */               
/* 1052 */               try { if (postMethod != null)
/* 1053 */                   postMethod.releaseConnection();  }
/* 1054 */               catch (Exception e) { e.printStackTrace(); }
/*      */             
/*      */             } 
/*      */           } 
/*      */         } else {
/* 1059 */           errmsg.append("\n<br/>提示：该workflowid没有设置消息推送");
/*      */         }
/*      */       
/*      */       } else {
/*      */         
/* 1064 */         errmsg.append("\n<br/>提示：没有接收人");
/*      */       }
/*      */        }
/* 1067 */     catch (Exception e)
/* 1068 */     { errmsg.append("\n<br/>失败：程序异常:" + e.getMessage());
/*      */       
/* 1070 */       SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); } finally { SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
/* 1071 */       String str1 = simpleDateFormat.format(Long.valueOf(System.currentTimeMillis()));
/* 1072 */       errmsg.append("\n<br/>系统IP：" + getLocalIP());
/* 1073 */       errmsg.append("\n<br/>系统UUID：" + getUUID());
/* 1074 */       errmsg.append("\n<br/>发送时间：" + str1);
/*      */       
/* 1076 */       ConnStatement connStatement = null;
/* 1077 */       String str2 = "insert into WX_SETMSGLOG (userstrs,errormsg,requestid,workflowid,msgcode,createtime) values (?,?,'" + 
/* 1078 */         requestid + "','" + workflowid + "','" + msgcode + "','" + str1 + "')";
/* 1079 */       if (dataid != 0) {
/* 1080 */         str2 = "update WX_SETMSGLOG set errormsg=?,msgcode=" + msgcode + ",createtime='" + str1 + "' where id = " + dataid;
/*      */       }
/*      */       try {
/* 1083 */         connStatement = new ConnStatement();
/* 1084 */         connStatement.setStatementSql(str2);
/* 1085 */         if (dataid != 0) {
/* 1086 */           connStatement.setString(1, String.valueOf(oldmsg) + "\n<br/>----------------------\n<br/>" + errmsg);
/*      */         } else {
/* 1088 */           connStatement.setString(1, (userstrs.length() > 0) ? userstrs.toString().substring(1) : "");
/* 1089 */           connStatement.setString(2, errmsg.toString());
/*      */         } 
/* 1091 */         connStatement.executeUpdate();
/* 1092 */       } catch (Exception ex) {
/* 1093 */         bb.writeLog("调用云桥(" + getWxsysurl() + ")接口设置流程已办状态保存日志出错---requestid：" + 
/* 1094 */             requestid + "，workflowid：" + workflowid + " 异常信息：\n" + ex.getMessage());
/*      */       } finally {
/* 1096 */         if (connStatement != null) connStatement.close(); 
/*      */       }  }
/*      */     
/* 1099 */     Map<String, String> msgmap = new HashMap<String, String>();
/* 1100 */     msgmap.put("msgcode", msgcode);
/* 1101 */     msgmap.put("msginfo", errmsg.toString());
/* 1102 */     return msgmap;
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public static Map<String, String> deleteYZJMsg(String requestid, String workflowid, List<String> userList, int dataid) {
/*      */     SimpleDateFormat sdf;
/*      */     String nowDate;
/*      */     ConnStatement ConnStatement;
/* 1111 */     String sql, msgcode = "0";
/* 1112 */     StringBuffer errmsg = new StringBuffer("调用云桥(" + getWxsysurl() + ")接口删除待办操作\n<br/>流程ID：" + requestid + "  路径ID：" + workflowid);
/* 1113 */     StringBuffer userstrs = new StringBuffer();
/*      */ 
/*      */     
/* 1116 */     try { List<List<String>> sendIdList = new ArrayList<List<String>>();
/* 1117 */       List<String> idList = new ArrayList<String>();
/* 1118 */       if (userList != null && userList.size() > 0) {
/*      */ 
/*      */ 
/*      */         
/* 1122 */         for (String userid : userList) {
/* 1123 */           if (idList.size() > 999) {
/* 1124 */             sendIdList.add(idList);
/* 1125 */             idList = new ArrayList<String>();
/*      */           } 
/* 1127 */           if (!"".equals(userid) && !"-1".equals(userid)) {
/* 1128 */             idList.add(userid);
/*      */           }
/* 1130 */           userstrs.append("," + userid);
/*      */         } 
/* 1132 */         sendIdList.add(idList);
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/* 1138 */         List<FlowAndDoc> flowList = WxInterfaceInit.getFlowList();
/* 1139 */         boolean issend = false;
/* 1140 */         boolean msgSetIgnoreWorkflowId = (Util.getIntValue(Prop.getPropValue("msgSetIgnoreWorkflowId", "ignore"), 0) == 1);
/* 1141 */         if (!msgSetIgnoreWorkflowId) {
/* 1142 */           for (FlowAndDoc fad : flowList) {
/* 1143 */             if (fad.getFlowsordocs().equals("-1")) {
/* 1144 */               if (fad.getFlowtype() == 0)
/* 1145 */                 issend = true;  continue;
/*      */             } 
/* 1147 */             if (("," + fad.getFlowsordocs() + ",").indexOf("," + workflowid + ",") > -1) {
/* 1148 */               if (fad.getFlowtype() == 1) {
/*      */                 continue;
/*      */               }
/* 1151 */               issend = true;
/*      */               break;
/*      */             } 
/* 1154 */             if (fad.getFlowtype() == 1) {
/* 1155 */               issend = true;
/*      */               break;
/*      */             } 
/*      */           } 
/*      */         }
/* 1160 */         if (issend || msgSetIgnoreWorkflowId) {
/* 1161 */           Map<String, String[]> subUserMap = getSubUserMap();
/* 1162 */           for (List<String> useridList : sendIdList) {
/* 1163 */             PostMethod postMethod = null;
/*      */             try {
/* 1165 */               if (useridList != null && useridList.size() > 0) {
/* 1166 */                 StringBuffer oldUserids = new StringBuffer();
/* 1167 */                 StringBuffer mainUserids = new StringBuffer();
/* 1168 */                 StringBuffer receiveUsers = new StringBuffer();
/* 1169 */                 for (String userid : useridList) {
/* 1170 */                   String id = userid;
/* 1171 */                   oldUserids.append("," + userid);
/* 1172 */                   if (subUserMap.containsKey(userid)) {
/* 1173 */                     String[] strs = subUserMap.get(userid);
/* 1174 */                     id = strs[0];
/*      */                   } 
/* 1176 */                   mainUserids.append("," + id);
/* 1177 */                   if (getUserkeytype().equals("loginid")) {
/* 1178 */                     id = Util.null2String(transUserId(id, 1));
/* 1179 */                   } else if (getUserkeytype().equals("workcode")) {
/* 1180 */                     id = getUserWorkcode(userid);
/*      */                   } 
/* 1182 */                   if ((receiveUsers + ",").indexOf("," + id + ",") < 0) {
/* 1183 */                     receiveUsers.append("," + id);
/*      */                   }
/*      */                 } 
/* 1186 */                 errmsg.append("\n<br/>原始传入用户ID：" + ((oldUserids.length() > 0) ? oldUserids.toString().substring(1) : oldUserids.toString()));
/* 1187 */                 if (GCONST.getMOREACCOUNTLANDING())
/* 1188 */                   errmsg.append("\n<br/>次账号转换成主账号后用户ID：" + ((mainUserids.length() > 0) ? mainUserids.toString().substring(1) : mainUserids.toString())); 
/* 1189 */                 errmsg.append("\n<br/>调用接口发送的用户标识：" + ((receiveUsers.length() > 0) ? receiveUsers.toString().substring(1) : receiveUsers.toString()));
/* 1190 */                 if (receiveUsers.length() > 0) {
/* 1191 */                   String allUsers = receiveUsers.toString().substring(1);
/*      */                   
/* 1193 */                   postMethod = new PostMethod(String.valueOf(getWxsysurl()) + "/wxthirdapi/deleteYZJMsg");
/* 1194 */                   NameValuePair[] param = new NameValuePair[5];
/* 1195 */                   param[0] = new NameValuePair("accesstoken", getToken());
/* 1196 */                   param[1] = new NameValuePair("userids", allUsers);
/* 1197 */                   param[2] = new NameValuePair("msgdataid", requestid);
/* 1198 */                   param[3] = new NameValuePair("outsysid", getOutsysid());
/* 1199 */                   param[4] = new NameValuePair("uuid", getUUID());
/*      */                   
/* 1201 */                   postMethod.setRequestBody(param);
/* 1202 */                   postMethod.getParams().setParameter("http.protocol.content-charset", "UTF8");
/* 1203 */                   HttpClient http = HttpClientConnectionManager.getHttpClient();
/* 1204 */                   http.getHttpConnectionManager().getParams().setConnectionTimeout(ctimeout);
/* 1205 */                   http.getHttpConnectionManager().getParams().setSoTimeout(stimeout);
/*      */                   
/* 1207 */                   http.executeMethod((HttpMethod)postMethod);
/* 1208 */                   String result = postMethod.getResponseBodyAsString();
/*      */                   
/* 1210 */                   JSONObject rjson = new JSONObject(result);
/* 1211 */                   if (!"0".equals(rjson.getString("errcode"))) {
/* 1212 */                     msgcode = "-1";
/* 1213 */                     errmsg.append("\n<br/>失败：云桥返回信息-" + rjson.getString("errmsg"));
/*      */                   } else {
/* 1215 */                     errmsg.append("\n<br/>成功：云桥返回信息-" + rjson.getString("errmsg"));
/*      */                   } 
/*      */                 } else {
/*      */                   
/* 1219 */                   errmsg.append("\n<br/>提示：receiveUsers为空");
/*      */                 } 
/*      */               } else {
/*      */                 
/* 1223 */                 errmsg.append("\n<br/>提示：useridList为空");
/*      */               } 
/* 1225 */             } catch (Exception e) {
/* 1226 */               msgcode = "-1";
/* 1227 */               errmsg.append("\n<br/>失败：调用云桥接口程序异常:" + e.getMessage()); continue;
/*      */             } finally {
/*      */               
/* 1230 */               try { if (postMethod != null)
/* 1231 */                   postMethod.releaseConnection();  }
/* 1232 */               catch (Exception e) { e.printStackTrace(); }
/*      */             
/*      */             } 
/*      */           } 
/*      */         } else {
/* 1237 */           errmsg.append("\n<br/>提示：该workflowid没有设置消息推送");
/*      */         }
/*      */       
/*      */       } else {
/*      */         
/* 1242 */         errmsg.append("\n<br/>提示：没有接收人");
/*      */       }
/*      */        }
/* 1245 */     catch (Exception e)
/* 1246 */     { errmsg.append("\n<br/>失败：程序异常:" + e.getMessage());
/*      */       
/* 1248 */       SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); } finally { SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
/* 1249 */       String str1 = simpleDateFormat.format(Long.valueOf(System.currentTimeMillis()));
/* 1250 */       errmsg.append("\n<br/>系统IP：" + getLocalIP());
/* 1251 */       errmsg.append("\n<br/>系统UUID：" + getUUID());
/* 1252 */       errmsg.append("\n<br/>发送时间：" + str1);
/*      */       
/* 1254 */       ConnStatement connStatement = null;
/* 1255 */       String str2 = "insert into WX_SETMSGLOG (userstrs,errormsg,requestid,workflowid,msgcode,createtime,type) values (?,?,'" + 
/* 1256 */         requestid + "','" + workflowid + "','" + msgcode + "','" + str1 + "',2)";
/* 1257 */       if (dataid != 0) {
/* 1258 */         str2 = "update WX_SETMSGLOG set errormsg=?,msgcode=" + msgcode + ",createtime='" + str1 + "' where id = " + dataid;
/*      */       }
/*      */       try {
/* 1261 */         connStatement = new ConnStatement();
/* 1262 */         connStatement.setStatementSql(str2);
/* 1263 */         if (dataid != 0) {
/* 1264 */           connStatement.setString(1, "\n<br/>----------------------\n<br/>" + errmsg);
/*      */         } else {
/* 1266 */           connStatement.setString(1, (userstrs.length() > 0) ? userstrs.toString().substring(1) : "");
/* 1267 */           connStatement.setString(2, errmsg.toString());
/*      */         } 
/* 1269 */         connStatement.executeUpdate();
/* 1270 */       } catch (Exception ex) {
/* 1271 */         bb.writeLog("调用云桥(" + getWxsysurl() + ")接口删除待办流程消息保存日志出错---requestid：" + 
/* 1272 */             requestid + "，workflowid：" + workflowid + " 异常信息：\n" + ex.getMessage());
/*      */       } finally {
/* 1274 */         if (connStatement != null) connStatement.close(); 
/*      */       }  }
/*      */     
/* 1277 */     Map<String, String> msgmap = new HashMap<String, String>();
/* 1278 */     msgmap.put("msgcode", msgcode);
/* 1279 */     msgmap.put("msginfo", errmsg.toString());
/* 1280 */     return msgmap;
/*      */   }
/*      */   
/*      */   public static Map<String, String> deleteYZJDocMsg(String docid, String seccategoryid, List<String> userList, int dataid) {
/*      */     SimpleDateFormat sdf;
/*      */     String nowDate;
/*      */     ConnStatement ConnStatement;
/* 1287 */     String sql, msgcode = "0";
/* 1288 */     StringBuffer errmsg = new StringBuffer("调用云桥(" + getWxsysurl() + ")接口删除待办操作\n<br/>文档ID：" + docid + "  路径ID：" + seccategoryid);
/* 1289 */     StringBuffer userstrs = new StringBuffer();
/*      */ 
/*      */     
/* 1292 */     try { List<List<String>> sendIdList = new ArrayList<List<String>>();
/* 1293 */       List<String> idList = new ArrayList<String>();
/* 1294 */       if (userList != null && userList.size() > 0) {
/*      */ 
/*      */ 
/*      */         
/* 1298 */         for (String userid : userList) {
/* 1299 */           if (idList.size() > 999) {
/* 1300 */             sendIdList.add(idList);
/* 1301 */             idList = new ArrayList<String>();
/*      */           } 
/* 1303 */           if (!"".equals(userid) && !"-1".equals(userid)) {
/* 1304 */             idList.add(userid);
/*      */           }
/* 1306 */           userstrs.append("," + userid);
/*      */         } 
/* 1308 */         sendIdList.add(idList);
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/* 1314 */         List<FlowAndDoc> flowList = WxInterfaceInit.getDocList();
/* 1315 */         boolean issend = false;
/* 1316 */         for (FlowAndDoc fad : flowList) {
/* 1317 */           if (fad.getFlowsordocs().equals("-1")) {
/* 1318 */             if (fad.getFlowtype() == 0)
/* 1319 */               issend = true;  continue;
/*      */           } 
/* 1321 */           if (("," + fad.getFlowsordocs() + ",").indexOf("," + seccategoryid + ",") > -1) {
/* 1322 */             if (fad.getFlowtype() == 1) {
/*      */               continue;
/*      */             }
/* 1325 */             issend = true;
/*      */             break;
/*      */           } 
/* 1328 */           if (fad.getFlowtype() == 1) {
/* 1329 */             issend = true;
/*      */             break;
/*      */           } 
/*      */         } 
/* 1333 */         if (issend) {
/* 1334 */           Map<String, String[]> subUserMap = getSubUserMap();
/* 1335 */           for (List<String> useridList : sendIdList) {
/* 1336 */             PostMethod postMethod = null;
/*      */             try {
/* 1338 */               if (useridList != null && useridList.size() > 0) {
/* 1339 */                 StringBuffer oldUserids = new StringBuffer();
/* 1340 */                 StringBuffer mainUserids = new StringBuffer();
/* 1341 */                 StringBuffer receiveUsers = new StringBuffer();
/* 1342 */                 for (String userid : useridList) {
/* 1343 */                   String id = userid;
/* 1344 */                   oldUserids.append("," + userid);
/* 1345 */                   if (subUserMap.containsKey(userid)) {
/* 1346 */                     String[] strs = subUserMap.get(userid);
/* 1347 */                     id = strs[0];
/*      */                   } 
/* 1349 */                   mainUserids.append("," + id);
/* 1350 */                   if (getUserkeytype().equals("loginid")) {
/* 1351 */                     id = Util.null2String(transUserId(id, 1));
/* 1352 */                   } else if (getUserkeytype().equals("workcode")) {
/* 1353 */                     id = getUserWorkcode(userid);
/*      */                   } 
/* 1355 */                   if ((receiveUsers + ",").indexOf("," + id + ",") < 0) {
/* 1356 */                     receiveUsers.append("," + id);
/*      */                   }
/*      */                 } 
/* 1359 */                 errmsg.append("\n<br/>原始传入用户ID：" + ((oldUserids.length() > 0) ? oldUserids.toString().substring(1) : oldUserids.toString()));
/* 1360 */                 if (GCONST.getMOREACCOUNTLANDING())
/* 1361 */                   errmsg.append("\n<br/>次账号转换成主账号后用户ID：" + ((mainUserids.length() > 0) ? mainUserids.toString().substring(1) : mainUserids.toString())); 
/* 1362 */                 errmsg.append("\n<br/>调用接口发送的用户标识：" + ((receiveUsers.length() > 0) ? receiveUsers.toString().substring(1) : receiveUsers.toString()));
/* 1363 */                 if (receiveUsers.length() > 0) {
/* 1364 */                   String allUsers = receiveUsers.toString().substring(1);
/*      */                   
/* 1366 */                   postMethod = new PostMethod(String.valueOf(getWxsysurl()) + "/wxthirdapi/deleteYZJMsg");
/* 1367 */                   NameValuePair[] param = new NameValuePair[6];
/* 1368 */                   param[0] = new NameValuePair("accesstoken", getToken());
/* 1369 */                   param[1] = new NameValuePair("userids", allUsers);
/* 1370 */                   param[2] = new NameValuePair("msgdataid", docid);
/* 1371 */                   param[3] = new NameValuePair("outsysid", getOutsysid());
/* 1372 */                   param[4] = new NameValuePair("uuid", getUUID());
/* 1373 */                   param[5] = new NameValuePair("msgdatatype", "2");
/*      */                   
/* 1375 */                   postMethod.setRequestBody(param);
/* 1376 */                   postMethod.getParams().setParameter("http.protocol.content-charset", "UTF8");
/* 1377 */                   HttpClient http = HttpClientConnectionManager.getHttpClient();
/* 1378 */                   http.getHttpConnectionManager().getParams().setConnectionTimeout(ctimeout);
/* 1379 */                   http.getHttpConnectionManager().getParams().setSoTimeout(stimeout);
/*      */                   
/* 1381 */                   http.executeMethod((HttpMethod)postMethod);
/* 1382 */                   String result = postMethod.getResponseBodyAsString();
/*      */                   
/* 1384 */                   JSONObject rjson = new JSONObject(result);
/* 1385 */                   if (!"0".equals(rjson.getString("errcode"))) {
/* 1386 */                     msgcode = "-1";
/* 1387 */                     errmsg.append("\n<br/>失败：云桥返回信息-" + rjson.getString("errmsg"));
/*      */                   } else {
/* 1389 */                     errmsg.append("\n<br/>成功：云桥返回信息-" + rjson.getString("errmsg"));
/*      */                   } 
/*      */                 } else {
/*      */                   
/* 1393 */                   errmsg.append("\n<br/>提示：receiveUsers为空");
/*      */                 } 
/*      */               } else {
/*      */                 
/* 1397 */                 errmsg.append("\n<br/>提示：useridList为空");
/*      */               } 
/* 1399 */             } catch (Exception e) {
/* 1400 */               msgcode = "-1";
/* 1401 */               errmsg.append("\n<br/>失败：调用云桥接口程序异常:" + e.getMessage()); continue;
/*      */             } finally {
/*      */               
/* 1404 */               try { if (postMethod != null)
/* 1405 */                   postMethod.releaseConnection();  }
/* 1406 */               catch (Exception e) { e.printStackTrace(); }
/*      */             
/*      */             } 
/*      */           } 
/*      */         } else {
/* 1411 */           errmsg.append("\n<br/>提示：该seccategoryid没有设置消息推送");
/*      */         }
/*      */       
/*      */       } else {
/*      */         
/* 1416 */         errmsg.append("\n<br/>提示：没有接收人");
/*      */       }
/*      */        }
/* 1419 */     catch (Exception e)
/* 1420 */     { errmsg.append("\n<br/>失败：程序异常:" + e.getMessage());
/*      */       
/* 1422 */       SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); } finally { SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
/* 1423 */       String str1 = simpleDateFormat.format(Long.valueOf(System.currentTimeMillis()));
/* 1424 */       errmsg.append("\n<br/>系统IP：" + getLocalIP());
/* 1425 */       errmsg.append("\n<br/>系统UUID：" + getUUID());
/* 1426 */       errmsg.append("\n<br/>发送时间：" + str1);
/*      */       
/* 1428 */       ConnStatement connStatement = null;
/* 1429 */       String str2 = "insert into WX_SETMSGLOG (userstrs,errormsg,requestid,workflowid,msgcode,createtime,type) values (?,?,'" + 
/* 1430 */         docid + "','" + seccategoryid + "','" + msgcode + "','" + str1 + "',4)";
/* 1431 */       if (dataid != 0) {
/* 1432 */         str2 = "update WX_SETMSGLOG set errormsg=?,msgcode=" + msgcode + ",createtime='" + str1 + "' where id = " + dataid;
/*      */       }
/*      */       try {
/* 1435 */         connStatement = new ConnStatement();
/* 1436 */         connStatement.setStatementSql(str2);
/* 1437 */         if (dataid != 0) {
/* 1438 */           connStatement.setString(1, "\n<br/>----------------------\n<br/>" + errmsg);
/*      */         } else {
/* 1440 */           connStatement.setString(1, (userstrs.length() > 0) ? userstrs.toString().substring(1) : "");
/* 1441 */           connStatement.setString(2, errmsg.toString());
/*      */         } 
/* 1443 */         connStatement.executeUpdate();
/* 1444 */       } catch (Exception ex) {
/* 1445 */         bb.writeLog("调用云桥(" + getWxsysurl() + ")接口删除待办文档消息保存日志出错---docid：" + 
/* 1446 */             docid + "，seccategoryid：" + seccategoryid + " 异常信息：\n" + ex.getMessage());
/*      */       } finally {
/* 1448 */         if (connStatement != null) connStatement.close(); 
/*      */       }  }
/*      */     
/* 1451 */     Map<String, String> msgmap = new HashMap<String, String>();
/* 1452 */     msgmap.put("msgcode", msgcode);
/* 1453 */     msgmap.put("msginfo", errmsg.toString());
/* 1454 */     return msgmap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static Map<String, String> deleteDDDBMsgOld(String requestid, String workflowid, List<String> userList, int dataid) {
/*      */     SimpleDateFormat sdf;
/*      */     String nowDate;
/*      */     ConnStatement ConnStatement;
/* 1464 */     String sql, msgcode = "0";
/* 1465 */     StringBuffer errmsg = new StringBuffer("调用云桥(" + getWxsysurl() + ")接口删除钉钉待办操作\n<br/>流程ID：" + requestid + "  路径ID：" + workflowid);
/* 1466 */     StringBuffer userstrs = new StringBuffer();
/*      */ 
/*      */     
/* 1469 */     try { List<List<String>> sendIdList = new ArrayList<List<String>>();
/* 1470 */       List<String> idList = new ArrayList<String>();
/* 1471 */       if (userList != null && userList.size() > 0) {
/*      */ 
/*      */ 
/*      */         
/* 1475 */         for (String userid : userList) {
/* 1476 */           if (idList.size() > 999) {
/* 1477 */             sendIdList.add(idList);
/* 1478 */             idList = new ArrayList<String>();
/*      */           } 
/* 1480 */           if (!"".equals(userid) && !"-1".equals(userid)) {
/* 1481 */             idList.add(userid);
/*      */           }
/* 1483 */           userstrs.append("," + userid);
/*      */         } 
/* 1485 */         sendIdList.add(idList);
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/* 1491 */         List<FlowAndDoc> flowList = WxInterfaceInit.getFlowList();
/* 1492 */         boolean issend = false;
/* 1493 */         boolean msgSetIgnoreWorkflowId = (Util.getIntValue(Prop.getPropValue("msgSetIgnoreWorkflowId", "ignore"), 0) == 1);
/* 1494 */         if (!msgSetIgnoreWorkflowId) {
/* 1495 */           for (FlowAndDoc fad : flowList) {
/* 1496 */             if (fad.getFlowsordocs().equals("-1")) {
/* 1497 */               if (fad.getFlowtype() == 0)
/* 1498 */                 issend = true;  continue;
/*      */             } 
/* 1500 */             if (("," + fad.getFlowsordocs() + ",").indexOf("," + workflowid + ",") > -1) {
/* 1501 */               if (fad.getFlowtype() == 1) {
/*      */                 continue;
/*      */               }
/* 1504 */               issend = true;
/*      */               break;
/*      */             } 
/* 1507 */             if (fad.getFlowtype() == 1) {
/* 1508 */               issend = true;
/*      */               break;
/*      */             } 
/*      */           } 
/*      */         }
/* 1513 */         if (issend || msgSetIgnoreWorkflowId) {
/* 1514 */           Map<String, String[]> subUserMap = getSubUserMap();
/* 1515 */           for (List<String> useridList : sendIdList) {
/* 1516 */             PostMethod postMethod = null;
/*      */             try {
/* 1518 */               if (useridList != null && useridList.size() > 0) {
/* 1519 */                 StringBuffer oldUserids = new StringBuffer();
/* 1520 */                 StringBuffer mainUserids = new StringBuffer();
/* 1521 */                 StringBuffer receiveUsers = new StringBuffer();
/* 1522 */                 for (String userid : useridList) {
/* 1523 */                   String id = userid;
/* 1524 */                   oldUserids.append("," + userid);
/* 1525 */                   if (subUserMap.containsKey(userid)) {
/* 1526 */                     String[] strs = subUserMap.get(userid);
/* 1527 */                     id = strs[0];
/*      */                   } 
/* 1529 */                   mainUserids.append("," + id);
/* 1530 */                   if (getUserkeytype().equals("loginid")) {
/* 1531 */                     id = Util.null2String(transUserId(id, 1));
/* 1532 */                   } else if (getUserkeytype().equals("workcode")) {
/* 1533 */                     id = getUserWorkcode(userid);
/*      */                   } 
/* 1535 */                   if ((receiveUsers + ",").indexOf("," + id + ",") < 0) {
/* 1536 */                     receiveUsers.append("," + id);
/*      */                   }
/*      */                 } 
/* 1539 */                 errmsg.append("\n<br/>原始传入用户ID：" + ((oldUserids.length() > 0) ? oldUserids.toString().substring(1) : oldUserids.toString()));
/* 1540 */                 if (GCONST.getMOREACCOUNTLANDING())
/* 1541 */                   errmsg.append("\n<br/>次账号转换成主账号后用户ID：" + ((mainUserids.length() > 0) ? mainUserids.toString().substring(1) : mainUserids.toString())); 
/* 1542 */                 errmsg.append("\n<br/>调用接口发送的用户标识：" + ((receiveUsers.length() > 0) ? receiveUsers.toString().substring(1) : receiveUsers.toString()));
/* 1543 */                 if (receiveUsers.length() > 0) {
/* 1544 */                   String allUsers = receiveUsers.toString().substring(1);
/*      */                   
/* 1546 */                   postMethod = new PostMethod(String.valueOf(getWxsysurl()) + "/wxthirdapi/deleteDDDBMsg");
/* 1547 */                   NameValuePair[] param = new NameValuePair[5];
/* 1548 */                   param[0] = new NameValuePair("accesstoken", getToken());
/* 1549 */                   param[1] = new NameValuePair("userids", allUsers);
/* 1550 */                   param[2] = new NameValuePair("msgdataid", requestid);
/* 1551 */                   param[3] = new NameValuePair("outsysid", getOutsysid());
/* 1552 */                   param[4] = new NameValuePair("uuid", getUUID());
/*      */                   
/* 1554 */                   postMethod.setRequestBody(param);
/* 1555 */                   postMethod.getParams().setParameter("http.protocol.content-charset", "UTF8");
/* 1556 */                   HttpClient http = HttpClientConnectionManager.getHttpClient();
/* 1557 */                   http.getHttpConnectionManager().getParams().setConnectionTimeout(ctimeout);
/* 1558 */                   http.getHttpConnectionManager().getParams().setSoTimeout(stimeout);
/*      */                   
/* 1560 */                   http.executeMethod((HttpMethod)postMethod);
/* 1561 */                   String result = postMethod.getResponseBodyAsString();
/*      */                   
/* 1563 */                   JSONObject rjson = new JSONObject(result);
/* 1564 */                   if (!"0".equals(rjson.getString("errcode"))) {
/* 1565 */                     msgcode = "-1";
/* 1566 */                     errmsg.append("\n<br/>失败：云桥返回信息-" + rjson.getString("errmsg"));
/*      */                   } else {
/* 1568 */                     errmsg.append("\n<br/>成功：云桥返回信息-" + rjson.getString("errmsg"));
/*      */                   } 
/*      */                 } else {
/*      */                   
/* 1572 */                   errmsg.append("\n<br/>提示：receiveUsers为空");
/*      */                 } 
/*      */               } else {
/*      */                 
/* 1576 */                 errmsg.append("\n<br/>提示：useridList为空");
/*      */               } 
/* 1578 */             } catch (Exception e) {
/* 1579 */               msgcode = "-1";
/* 1580 */               errmsg.append("\n<br/>失败：调用云桥接口程序异常:" + e.getMessage()); continue;
/*      */             } finally {
/*      */               
/* 1583 */               try { if (postMethod != null)
/* 1584 */                   postMethod.releaseConnection();  }
/* 1585 */               catch (Exception e) { e.printStackTrace(); }
/*      */             
/*      */             } 
/*      */           } 
/*      */         } else {
/* 1590 */           errmsg.append("\n<br/>提示：该workflowid没有设置消息推送");
/*      */         }
/*      */       
/*      */       } else {
/*      */         
/* 1595 */         errmsg.append("\n<br/>提示：没有接收人");
/*      */       }
/*      */        }
/* 1598 */     catch (Exception e)
/* 1599 */     { errmsg.append("\n<br/>失败：程序异常:" + e.getMessage());
/*      */       
/* 1601 */       SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); } finally { SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
/* 1602 */       String str1 = simpleDateFormat.format(Long.valueOf(System.currentTimeMillis()));
/* 1603 */       errmsg.append("\n<br/>系统IP：" + getLocalIP());
/* 1604 */       errmsg.append("\n<br/>系统UUID：" + getUUID());
/* 1605 */       errmsg.append("\n<br/>发送时间：" + str1);
/*      */       
/* 1607 */       ConnStatement connStatement = null;
/* 1608 */       String str2 = "insert into WX_SETMSGLOG (userstrs,errormsg,requestid,workflowid,msgcode,createtime,type) values (?,?,'" + 
/* 1609 */         requestid + "','" + workflowid + "','" + msgcode + "','" + str1 + "',3)";
/* 1610 */       if (dataid != 0) {
/* 1611 */         str2 = "update WX_SETMSGLOG set errormsg=?,msgcode=" + msgcode + ",createtime='" + str1 + "' where id = " + dataid;
/*      */       }
/*      */       try {
/* 1614 */         connStatement = new ConnStatement();
/* 1615 */         connStatement.setStatementSql(str2);
/* 1616 */         if (dataid != 0) {
/* 1617 */           connStatement.setString(1, "\n<br/>----------------------\n<br/>" + errmsg);
/*      */         } else {
/* 1619 */           connStatement.setString(1, (userstrs.length() > 0) ? userstrs.toString().substring(1) : "");
/* 1620 */           connStatement.setString(2, errmsg.toString());
/*      */         } 
/* 1622 */         connStatement.executeUpdate();
/* 1623 */       } catch (Exception ex) {
/* 1624 */         bb.writeLog("调用云桥(" + getWxsysurl() + ")接口删除钉钉待办流程消息保存日志出错---requestid：" + 
/* 1625 */             requestid + "，workflowid：" + workflowid + " 异常信息：\n" + ex.getMessage());
/*      */       } finally {
/* 1627 */         if (connStatement != null) connStatement.close(); 
/*      */       }  }
/*      */     
/* 1630 */     Map<String, String> msgmap = new HashMap<String, String>();
/* 1631 */     msgmap.put("msgcode", msgcode);
/* 1632 */     msgmap.put("msginfo", errmsg.toString());
/* 1633 */     return msgmap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static Map<String, String> deleteDDDBMsg(String requestid, String workflowid, List<String> userList, int dataid) {
/*      */     SimpleDateFormat sdf;
/*      */     String nowDate;
/*      */     ConnStatement ConnStatement;
/* 1643 */     String sql, msgcode = "0";
/* 1644 */     StringBuffer errmsg = new StringBuffer("调用云桥(" + getWxsysurl() + ")接口删除钉钉待办(新)操作\n流程ID：" + requestid + "  路径ID：" + workflowid);
/* 1645 */     StringBuffer userstrs = new StringBuffer();
/*      */ 
/*      */     
/* 1648 */     try { List<List<String>> sendIdList = new ArrayList<List<String>>();
/* 1649 */       List<String> idList = new ArrayList<String>();
/* 1650 */       if (userList != null && userList.size() > 0) {
/*      */ 
/*      */ 
/*      */         
/* 1654 */         for (String userid : userList) {
/* 1655 */           if (idList.size() > 999) {
/* 1656 */             sendIdList.add(idList);
/* 1657 */             idList = new ArrayList<String>();
/*      */           } 
/* 1659 */           if (!"".equals(userid) && !"-1".equals(userid)) {
/* 1660 */             idList.add(userid);
/*      */           }
/* 1662 */           userstrs.append("," + userid);
/*      */         } 
/* 1664 */         sendIdList.add(idList);
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/* 1670 */         List<FlowAndDoc> flowList = WxInterfaceInit.getFlowList();
/* 1671 */         boolean issend = false;
/* 1672 */         boolean msgSetIgnoreWorkflowId = (Util.getIntValue(Prop.getPropValue("msgSetIgnoreWorkflowId", "ignore"), 0) == 1);
/* 1673 */         if (!msgSetIgnoreWorkflowId) {
/* 1674 */           for (FlowAndDoc fad : flowList) {
/* 1675 */             if (fad.getFlowsordocs().equals("-1")) {
/* 1676 */               if (fad.getFlowtype() == 0)
/* 1677 */                 issend = true;  continue;
/*      */             } 
/* 1679 */             if (("," + fad.getFlowsordocs() + ",").indexOf("," + workflowid + ",") > -1) {
/* 1680 */               if (fad.getFlowtype() == 1) {
/*      */                 continue;
/*      */               }
/* 1683 */               issend = true;
/*      */               break;
/*      */             } 
/* 1686 */             if (fad.getFlowtype() == 1) {
/* 1687 */               issend = true;
/*      */               break;
/*      */             } 
/*      */           } 
/*      */         }
/* 1692 */         if (issend || msgSetIgnoreWorkflowId) {
/* 1693 */           Map<String, String[]> subUserMap = getSubUserMap();
/* 1694 */           for (List<String> useridList : sendIdList) {
/* 1695 */             PostMethod postMethod = null;
/*      */             try {
/* 1697 */               if (useridList != null && useridList.size() > 0) {
/* 1698 */                 StringBuffer oldUserids = new StringBuffer();
/* 1699 */                 StringBuffer mainUserids = new StringBuffer();
/* 1700 */                 StringBuffer receiveUsers = new StringBuffer();
/* 1701 */                 for (String userid : useridList) {
/* 1702 */                   String id = userid;
/* 1703 */                   oldUserids.append("," + userid);
/* 1704 */                   if (subUserMap.containsKey(userid)) {
/* 1705 */                     String[] strs = subUserMap.get(userid);
/* 1706 */                     id = strs[0];
/*      */                   } 
/* 1708 */                   mainUserids.append("," + id);
/* 1709 */                   if (getUserkeytype().equals("loginid")) {
/* 1710 */                     id = Util.null2String(transUserId(id, 1));
/* 1711 */                   } else if (getUserkeytype().equals("workcode")) {
/* 1712 */                     id = getUserWorkcode(userid);
/*      */                   } 
/* 1714 */                   if ((receiveUsers + ",").indexOf("," + id + ",") < 0) {
/* 1715 */                     receiveUsers.append("," + id);
/*      */                   }
/*      */                 } 
/* 1718 */                 errmsg.append("\n原始传入用户ID：" + ((oldUserids.length() > 0) ? oldUserids.toString().substring(1) : oldUserids.toString()));
/* 1719 */                 if (GCONST.getMOREACCOUNTLANDING())
/* 1720 */                   errmsg.append("\n次账号转换成主账号后用户ID：" + ((mainUserids.length() > 0) ? mainUserids.toString().substring(1) : mainUserids.toString())); 
/* 1721 */                 errmsg.append("\n调用接口发送的用户标识：" + ((receiveUsers.length() > 0) ? receiveUsers.toString().substring(1) : receiveUsers.toString()));
/* 1722 */                 if (receiveUsers.length() > 0) {
/* 1723 */                   String allUsers = receiveUsers.toString().substring(1);
/*      */                   
/* 1725 */                   postMethod = new PostMethod(String.valueOf(getWxsysurl()) + "/wxthirdapi/dealDDMsg");
/* 1726 */                   NameValuePair[] param = new NameValuePair[5];
/* 1727 */                   param[0] = new NameValuePair("accesstoken", getToken());
/* 1728 */                   param[1] = new NameValuePair("userids", allUsers);
/* 1729 */                   param[2] = new NameValuePair("msgdataid", requestid);
/* 1730 */                   param[3] = new NameValuePair("outsysid", getOutsysid());
/* 1731 */                   param[4] = new NameValuePair("uuid", getUUID());
/*      */                   
/* 1733 */                   postMethod.setRequestBody(param);
/* 1734 */                   postMethod.getParams().setParameter("http.protocol.content-charset", "UTF8");
/* 1735 */                   HttpClient http = HttpClientConnectionManager.getHttpClient();
/* 1736 */                   http.getHttpConnectionManager().getParams().setConnectionTimeout(ctimeout);
/* 1737 */                   http.getHttpConnectionManager().getParams().setSoTimeout(stimeout);
/*      */                   
/* 1739 */                   http.executeMethod((HttpMethod)postMethod);
/* 1740 */                   String result = postMethod.getResponseBodyAsString();
/*      */                   
/* 1742 */                   JSONObject rjson = new JSONObject(result);
/* 1743 */                   if (!"0".equals(rjson.getString("errcode"))) {
/* 1744 */                     msgcode = "-1";
/* 1745 */                     errmsg.append("\n失败：云桥返回信息-" + rjson.getString("errmsg"));
/*      */                   } else {
/* 1747 */                     errmsg.append("\n成功：云桥返回信息-" + rjson.getString("errmsg"));
/*      */                   } 
/*      */                 } else {
/*      */                   
/* 1751 */                   errmsg.append("\n提示：receiveUsers为空");
/*      */                 } 
/*      */               } else {
/*      */                 
/* 1755 */                 errmsg.append("\n提示：useridList为空");
/*      */               } 
/* 1757 */             } catch (Exception e) {
/* 1758 */               msgcode = "-1";
/* 1759 */               errmsg.append("\n失败：调用云桥接口程序异常:" + e.getMessage()); continue;
/*      */             } finally {
/*      */               
/* 1762 */               try { if (postMethod != null)
/* 1763 */                   postMethod.releaseConnection();  }
/* 1764 */               catch (Exception e) { e.printStackTrace(); }
/*      */             
/*      */             } 
/*      */           } 
/*      */         } else {
/* 1769 */           errmsg.append("\n提示：该workflowid没有设置消息推送");
/*      */         }
/*      */       
/*      */       } else {
/*      */         
/* 1774 */         errmsg.append("\n提示：没有接收人");
/*      */       }
/*      */        }
/* 1777 */     catch (Exception e)
/* 1778 */     { errmsg.append("\n失败：程序异常:" + e.getMessage());
/*      */       
/* 1780 */       SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); } finally { SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
/* 1781 */       String str1 = simpleDateFormat.format(Long.valueOf(System.currentTimeMillis()));
/* 1782 */       errmsg.append("\n系统IP：" + getLocalIP());
/* 1783 */       errmsg.append("\n系统UUID：" + getUUID());
/* 1784 */       errmsg.append("\n发送时间：" + str1);
/*      */       
/* 1786 */       ConnStatement connStatement = null;
/* 1787 */       String str2 = "insert into WX_SETMSGLOG (userstrs,errormsg,requestid,workflowid,msgcode,createtime,type) values (?,?,'" + 
/* 1788 */         requestid + "','" + workflowid + "','" + msgcode + "','" + str1 + "',3)";
/* 1789 */       if (dataid != 0) {
/* 1790 */         str2 = "update WX_SETMSGLOG set errormsg=?,msgcode=" + msgcode + ",createtime='" + str1 + "' where id = " + dataid;
/*      */       }
/*      */       try {
/* 1793 */         connStatement = new ConnStatement();
/* 1794 */         connStatement.setStatementSql(str2);
/* 1795 */         if (dataid != 0) {
/* 1796 */           connStatement.setString(1, errmsg.toString());
/*      */         } else {
/* 1798 */           connStatement.setString(1, (userstrs.length() > 0) ? userstrs.toString().substring(1) : "");
/* 1799 */           connStatement.setString(2, errmsg.toString());
/*      */         } 
/* 1801 */         connStatement.executeUpdate();
/* 1802 */       } catch (Exception ex) {
/* 1803 */         bb.writeLog("调用云桥(" + getWxsysurl() + ")接口删除钉钉待办流程(新)消息保存日志出错---requestid：" + 
/* 1804 */             requestid + "，workflowid：" + workflowid + " 异常信息：\n" + ex.getMessage());
/*      */       } finally {
/* 1806 */         if (connStatement != null) connStatement.close(); 
/*      */       }  }
/*      */     
/* 1809 */     Map<String, String> msgmap = new HashMap<String, String>();
/* 1810 */     msgmap.put("msgcode", msgcode);
/* 1811 */     msgmap.put("msginfo", errmsg.toString());
/* 1812 */     return msgmap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static Map<String, String> archiveDDMsg(String requestid, String workflowid, String status, int dataid) {
/*      */     SimpleDateFormat sdf;
/*      */     String nowDate;
/*      */     ConnStatement ConnStatement;
/* 1823 */     String sql, msgcode = "0";
/* 1824 */     StringBuffer errmsg = new StringBuffer("调用云桥(" + getWxsysurl() + ")接口删除钉钉待办实例(新)操作\n流程ID：" + requestid + "  路径ID：" + workflowid);
/*      */ 
/*      */     
/* 1827 */     try { List<FlowAndDoc> flowList = WxInterfaceInit.getFlowList();
/* 1828 */       boolean issend = false;
/* 1829 */       boolean msgSetIgnoreWorkflowId = (Util.getIntValue(Prop.getPropValue("msgSetIgnoreWorkflowId", "ignore"), 0) == 1);
/* 1830 */       if (!msgSetIgnoreWorkflowId) {
/* 1831 */         for (FlowAndDoc fad : flowList) {
/* 1832 */           if (fad.getFlowsordocs().equals("-1")) {
/* 1833 */             if (fad.getFlowtype() == 0)
/* 1834 */               issend = true;  continue;
/*      */           } 
/* 1836 */           if (("," + fad.getFlowsordocs() + ",").indexOf("," + workflowid + ",") > -1) {
/* 1837 */             if (fad.getFlowtype() == 1) {
/*      */               continue;
/*      */             }
/* 1840 */             issend = true;
/*      */             break;
/*      */           } 
/* 1843 */           if (fad.getFlowtype() == 1) {
/* 1844 */             issend = true;
/*      */             break;
/*      */           } 
/*      */         } 
/*      */       }
/* 1849 */       if (issend || msgSetIgnoreWorkflowId)
/* 1850 */       { PostMethod postMethod = null;
/*      */         
/*      */         try {
/* 1853 */           postMethod = new PostMethod(String.valueOf(getWxsysurl()) + "/wxthirdapi/archiveDDMsg");
/* 1854 */           NameValuePair[] param = new NameValuePair[5];
/* 1855 */           param[0] = new NameValuePair("accesstoken", getToken());
/* 1856 */           param[1] = new NameValuePair("msgdataid", requestid);
/* 1857 */           param[2] = new NameValuePair("outsysid", getOutsysid());
/* 1858 */           param[3] = new NameValuePair("uuid", getUUID());
/* 1859 */           param[4] = new NameValuePair("status", status);
/*      */           
/* 1861 */           postMethod.setRequestBody(param);
/* 1862 */           postMethod.getParams().setParameter("http.protocol.content-charset", "UTF8");
/* 1863 */           HttpClient http = HttpClientConnectionManager.getHttpClient();
/* 1864 */           http.getHttpConnectionManager().getParams().setConnectionTimeout(ctimeout);
/* 1865 */           http.getHttpConnectionManager().getParams().setSoTimeout(stimeout);
/*      */           
/* 1867 */           http.executeMethod((HttpMethod)postMethod);
/* 1868 */           String result = postMethod.getResponseBodyAsString();
/*      */           
/* 1870 */           JSONObject rjson = new JSONObject(result);
/* 1871 */           if (!"0".equals(rjson.getString("errcode"))) {
/* 1872 */             msgcode = "-1";
/* 1873 */             errmsg.append("\n失败：云桥返回信息-" + rjson.getString("errmsg"));
/*      */           } else {
/* 1875 */             errmsg.append("\n成功：云桥返回信息-" + rjson.getString("errmsg"));
/*      */           } 
/* 1877 */         } catch (Exception e) {
/* 1878 */           msgcode = "-1";
/* 1879 */           errmsg.append("\n失败：调用云桥接口程序异常:" + e.getMessage());
/*      */         } finally {
/*      */           
/* 1882 */           try { if (postMethod != null)
/* 1883 */               postMethod.releaseConnection();  }
/* 1884 */           catch (Exception e) { e.printStackTrace(); }
/*      */         
/*      */         }  }
/* 1887 */       else { errmsg.append("\n提示：该workflowid没有设置消息推送"); }
/*      */        }
/* 1889 */     catch (Exception e)
/* 1890 */     { errmsg.append("\n失败：程序异常:" + e.getMessage());
/*      */       
/* 1892 */       SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); } finally { SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
/* 1893 */       String str1 = simpleDateFormat.format(Long.valueOf(System.currentTimeMillis()));
/* 1894 */       errmsg.append("\n系统IP：" + getLocalIP());
/* 1895 */       errmsg.append("\n系统UUID：" + getUUID());
/* 1896 */       errmsg.append("\n发送时间：" + str1);
/*      */       
/* 1898 */       ConnStatement connStatement = null;
/* 1899 */       String str2 = "insert into WX_SETMSGLOG (userstrs,errormsg,requestid,workflowid,msgcode,createtime,type) values ('" + 
/* 1900 */         status + "',?,'" + requestid + "','" + workflowid + "','" + msgcode + "','" + str1 + "',5)";
/* 1901 */       if (dataid != 0) {
/* 1902 */         str2 = "update WX_SETMSGLOG set errormsg=?,msgcode=" + msgcode + ",createtime='" + str1 + "' where id = " + dataid;
/*      */       }
/*      */       try {
/* 1905 */         connStatement = new ConnStatement();
/* 1906 */         connStatement.setStatementSql(str2);
/* 1907 */         connStatement.setString(1, errmsg.toString());
/* 1908 */         connStatement.executeUpdate();
/* 1909 */       } catch (Exception ex) {
/* 1910 */         bb.writeLog("调用云桥(" + getWxsysurl() + ")接口删除钉钉待办实例(新)消息保存日志出错---requestid：" + 
/* 1911 */             requestid + "，workflowid：" + workflowid + " 异常信息：\n" + ex.getMessage());
/*      */       } finally {
/* 1913 */         if (connStatement != null) connStatement.close(); 
/*      */       }  }
/*      */     
/* 1916 */     Map<String, String> msgmap = new HashMap<String, String>();
/* 1917 */     msgmap.put("msgcode", msgcode);
/* 1918 */     msgmap.put("msginfo", errmsg.toString());
/* 1919 */     return msgmap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static Map<String, String> setQSMFMsgFlag(String requestid, String workflowid, List<String> userList, int dataid, String oldmsg) {
/*      */     SimpleDateFormat sdf;
/*      */     String nowDate;
/*      */     ConnStatement ConnStatement;
/* 1929 */     String sql, msgcode = "0";
/* 1930 */     StringBuffer errmsg = new StringBuffer("调用云桥(" + getWxsysurl() + ")接口设置已办操作\n<br/>流程ID：" + requestid + "  路径ID：" + workflowid);
/* 1931 */     StringBuffer userstrs = new StringBuffer();
/*      */ 
/*      */     
/* 1934 */     try { List<List<String>> sendIdList = new ArrayList<List<String>>();
/* 1935 */       List<String> idList = new ArrayList<String>();
/* 1936 */       if (userList != null && userList.size() > 0) {
/*      */ 
/*      */ 
/*      */         
/* 1940 */         for (String userid : userList) {
/* 1941 */           if (idList.size() > 999) {
/* 1942 */             sendIdList.add(idList);
/* 1943 */             idList = new ArrayList<String>();
/*      */           } 
/* 1945 */           if (!"".equals(userid) && !"-1".equals(userid)) {
/* 1946 */             idList.add(userid);
/*      */           }
/* 1948 */           userstrs.append("," + userid);
/*      */         } 
/* 1950 */         sendIdList.add(idList);
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/* 1956 */         List<FlowAndDoc> flowList = WxInterfaceInit.getFlowList();
/* 1957 */         boolean issend = false;
/* 1958 */         for (FlowAndDoc fad : flowList) {
/* 1959 */           if (fad.getFlowsordocs().equals("-1")) {
/* 1960 */             if (fad.getFlowtype() == 0)
/* 1961 */               issend = true;  continue;
/*      */           } 
/* 1963 */           if (("," + fad.getFlowsordocs() + ",").indexOf("," + workflowid + ",") > -1) {
/* 1964 */             if (fad.getFlowtype() == 1) {
/*      */               continue;
/*      */             }
/* 1967 */             issend = true;
/*      */             break;
/*      */           } 
/* 1970 */           if (fad.getFlowtype() == 1) {
/* 1971 */             issend = true;
/*      */             break;
/*      */           } 
/*      */         } 
/* 1975 */         if (issend)
/* 1976 */         { Map<String, String[]> subUserMap = getSubUserMap();
/* 1977 */           for (List<String> useridList : sendIdList) {
/* 1978 */             PostMethod postMethod = null;
/*      */             try {
/* 1980 */               if (useridList != null && useridList.size() > 0) {
/* 1981 */                 StringBuffer oldUserids = new StringBuffer();
/* 1982 */                 StringBuffer mainUserids = new StringBuffer();
/* 1983 */                 StringBuffer receiveUsers = new StringBuffer();
/* 1984 */                 for (String userid : useridList) {
/* 1985 */                   String id = userid;
/* 1986 */                   oldUserids.append("," + userid);
/* 1987 */                   if (subUserMap.containsKey(userid)) {
/* 1988 */                     String[] strs = subUserMap.get(userid);
/* 1989 */                     id = strs[0];
/*      */                   } 
/* 1991 */                   mainUserids.append("," + id);
/* 1992 */                   if (getUserkeytype().equals("loginid")) {
/* 1993 */                     id = Util.null2String(transUserId(id, 1));
/* 1994 */                   } else if (getUserkeytype().equals("workcode")) {
/* 1995 */                     id = getUserWorkcode(userid);
/*      */                   } 
/* 1997 */                   if ((receiveUsers + ",").indexOf("," + id + ",") < 0) {
/* 1998 */                     receiveUsers.append("," + id);
/*      */                   }
/*      */                 } 
/* 2001 */                 errmsg.append("\n<br/>原始传入用户ID：" + ((oldUserids.length() > 0) ? oldUserids.toString().substring(1) : oldUserids.toString()));
/* 2002 */                 if (GCONST.getMOREACCOUNTLANDING())
/* 2003 */                   errmsg.append("\n<br/>次账号转换成主账号后用户ID：" + ((mainUserids.length() > 0) ? mainUserids.toString().substring(1) : mainUserids.toString())); 
/* 2004 */                 errmsg.append("\n<br/>调用接口发送的用户标识：" + ((receiveUsers.length() > 0) ? receiveUsers.toString().substring(1) : receiveUsers.toString()));
/* 2005 */                 if (receiveUsers.length() > 0) {
/* 2006 */                   String allUsers = receiveUsers.toString().substring(1);
/*      */                   
/* 2008 */                   postMethod = new PostMethod(String.valueOf(getWxsysurl()) + "/wxthirdapi/dealQSMFMsg");
/* 2009 */                   NameValuePair[] param = new NameValuePair[5];
/* 2010 */                   param[0] = new NameValuePair("accesstoken", getToken());
/* 2011 */                   param[1] = new NameValuePair("userids", allUsers);
/* 2012 */                   param[2] = new NameValuePair("msgdataid", requestid);
/* 2013 */                   param[3] = new NameValuePair("outsysid", getOutsysid());
/* 2014 */                   param[4] = new NameValuePair("uuid", getUUID());
/*      */                   
/* 2016 */                   postMethod.setRequestBody(param);
/* 2017 */                   postMethod.getParams().setParameter("http.protocol.content-charset", "UTF8");
/* 2018 */                   HttpClient http = HttpClientConnectionManager.getHttpClient();
/* 2019 */                   http.getHttpConnectionManager().getParams().setConnectionTimeout(ctimeout);
/* 2020 */                   http.getHttpConnectionManager().getParams().setSoTimeout(stimeout);
/*      */                   
/* 2022 */                   http.executeMethod((HttpMethod)postMethod);
/* 2023 */                   String result = postMethod.getResponseBodyAsString();
/*      */                   
/* 2025 */                   JSONObject rjson = new JSONObject(result);
/* 2026 */                   if (!"0".equals(rjson.getString("errcode"))) {
/* 2027 */                     msgcode = "-1";
/* 2028 */                     errmsg.append("\n<br/>失败：云桥返回信息-" + rjson.getString("errmsg"));
/*      */                   } else {
/* 2030 */                     errmsg.append("\n<br/>成功：云桥返回信息-" + rjson.getString("errmsg"));
/*      */                   } 
/*      */                 } else {
/* 2033 */                   msgcode = "-1";
/* 2034 */                   errmsg.append("\n<br/>失败：receiveUsers为空");
/*      */                 } 
/*      */               } else {
/* 2037 */                 msgcode = "-1";
/* 2038 */                 errmsg.append("\n<br/>失败：useridList为空");
/*      */               } 
/* 2040 */             } catch (Exception e) {
/* 2041 */               msgcode = "-1";
/* 2042 */               errmsg.append("\n<br/>失败：调用云桥接口程序异常:" + e.getMessage()); continue;
/*      */             } finally {
/*      */               
/* 2045 */               try { if (postMethod != null)
/* 2046 */                   postMethod.releaseConnection();  }
/* 2047 */               catch (Exception e) { e.printStackTrace(); }
/*      */             
/*      */             } 
/*      */           }  }
/* 2051 */         else { msgcode = "-1";
/* 2052 */           errmsg.append("\n<br/>失败：该workflowid没有设置消息推送"); }
/*      */       
/*      */       } else {
/*      */         
/* 2056 */         msgcode = "-1";
/* 2057 */         errmsg.append("\n<br/>失败：没有接收人");
/*      */       }
/*      */        }
/* 2060 */     catch (Exception e)
/* 2061 */     { errmsg.append("\n<br/>失败：程序异常:" + e.getMessage());
/*      */       
/* 2063 */       SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); } finally { SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
/* 2064 */       String str1 = simpleDateFormat.format(Long.valueOf(System.currentTimeMillis()));
/* 2065 */       errmsg.append("\n<br/>系统IP：" + getLocalIP());
/* 2066 */       errmsg.append("\n<br/>系统UUID：" + getUUID());
/* 2067 */       errmsg.append("\n<br/>发送时间：" + str1);
/*      */       
/* 2069 */       ConnStatement connStatement = null;
/* 2070 */       String str2 = "insert into WX_SETMSGLOG (userstrs,errormsg,requestid,workflowid,msgcode,createtime) values (?,?,'" + 
/* 2071 */         requestid + "','" + workflowid + "','" + msgcode + "','" + str1 + "')";
/* 2072 */       if (dataid != 0) {
/* 2073 */         str2 = "update WX_SETMSGLOG set errormsg=?,msgcode=" + msgcode + ",createtime='" + str1 + "' where id = " + dataid;
/*      */       }
/*      */       try {
/* 2076 */         connStatement = new ConnStatement();
/* 2077 */         connStatement.setStatementSql(str2);
/* 2078 */         if (dataid != 0) {
/* 2079 */           connStatement.setString(1, String.valueOf(oldmsg) + "\n<br/>----------------------\n<br/>" + errmsg);
/*      */         } else {
/* 2081 */           connStatement.setString(1, (userstrs.length() > 0) ? userstrs.toString().substring(1) : "");
/* 2082 */           connStatement.setString(2, errmsg.toString());
/*      */         } 
/* 2084 */         connStatement.executeUpdate();
/* 2085 */       } catch (Exception ex) {
/* 2086 */         bb.writeLog("调用云桥(" + getWxsysurl() + ")接口设置流程已办状态保存日志出错---requestid：" + 
/* 2087 */             requestid + "，workflowid：" + workflowid + " 异常信息：\n" + ex.getMessage());
/*      */       } finally {
/* 2089 */         if (connStatement != null) connStatement.close(); 
/*      */       }  }
/*      */     
/* 2092 */     Map<String, String> msgmap = new HashMap<String, String>();
/* 2093 */     msgmap.put("msgcode", msgcode);
/* 2094 */     msgmap.put("msginfo", errmsg.toString());
/* 2095 */     return msgmap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static String transUserId(String id, int idtype) {
/* 2105 */     String userid = "";
/*      */     try {
/* 2107 */       boolean isldap = "ldap".equals(Prop.getPropValue(GCONST.getConfigFile(), "authentic"));
/*      */ 
/*      */       
/* 2110 */       boolean noaccount = (Util.getIntValue(Prop.getPropValue("wxinterface", "noaccount"), 0) == 1);
/*      */       
/* 2112 */       ResourceComInfo rc = new ResourceComInfo();
/*      */       
/* 2114 */       String sql = "";
/* 2115 */       if (idtype == 1) {
/* 2116 */         if (id.equals("1")) {
/* 2117 */           userid = "sysadmin";
/*      */         } else {
/* 2119 */           if (isldap && !noaccount) {
/* 2120 */             userid = Util.null2String(rc.getAccount(id));
/* 2121 */             if ("".equals(userid)) userid = Util.null2String(rc.getLoginID(id)); 
/*      */           } else {
/* 2123 */             userid = Util.null2String(rc.getLoginID(id));
/*      */           } 
/*      */           
/* 2126 */           if ("".equals(userid)) userid = transUserId2(id, idtype); 
/*      */         } 
/* 2128 */       } else if (idtype == 2) {
/* 2129 */         RecordSet rs = new RecordSet();
/* 2130 */         sql = "select id from hrmresource where workcode='" + id + "' and (status = 0 or status = 1 or status = 2 or status = 3)";
/* 2131 */         rs.executeSql(sql);
/* 2132 */         if (rs.next()) {
/* 2133 */           userid = Util.null2String(rs.getString(1));
/*      */         }
/*      */ 
/*      */         
/* 2137 */         if ("".equals(userid)) userid = Util.null2String(Prop.getPropValue("wxinterface", "demouserid")); 
/*      */       } else {
/* 2139 */         RecordSet rs = new RecordSet();
/* 2140 */         if (isldap && !noaccount) {
/* 2141 */           sql = "select id from hrmresource where (loginid='" + id + "' or account='" + id + "') and (status = 0 or status = 1 or status = 2 or status = 3)";
/*      */         } else {
/* 2143 */           sql = "select id from hrmresource where loginid='" + id + "' and (status = 0 or status = 1 or status = 2 or status = 3)";
/*      */         } 
/* 2145 */         rs.executeSql(sql);
/* 2146 */         if (rs.next()) {
/* 2147 */           userid = Util.null2String(rs.getString(1));
/*      */         }
/*      */ 
/*      */         
/* 2151 */         if ("".equals(userid)) userid = Util.null2String(Prop.getPropValue("wxinterface", "demouserid"));
/*      */       
/*      */       } 
/* 2154 */     } catch (Exception e) {
/*      */       
/* 2156 */       e.printStackTrace();
/*      */     } 
/* 2158 */     return userid;
/*      */   }
/*      */   
/*      */   public static String getUserWorkcode(String userid) {
/* 2162 */     RecordSet rs = new RecordSet();
/* 2163 */     String sql = "select workcode from hrmresource where id=" + userid + " and (status = 0 or status = 1 or status = 2 or status = 3)";
/* 2164 */     rs.executeSql(sql);
/* 2165 */     String workcode = "";
/* 2166 */     if (rs.next()) {
/* 2167 */       workcode = Util.null2String(rs.getString(1));
/*      */     }
/* 2169 */     return workcode;
/*      */   }
/*      */   
/*      */   public static String transUserId(String loginid, String account) {
/* 2173 */     String userid = "";
/* 2174 */     boolean isldap = "ldap".equals(Prop.getPropValue(GCONST.getConfigFile(), "authentic"));
/*      */ 
/*      */     
/* 2177 */     boolean noaccount = (Util.getIntValue(Prop.getPropValue("wxinterface", "noaccount"), 0) == 1);
/*      */     
/* 2179 */     if (isldap && !noaccount) {
/* 2180 */       userid = Util.null2String(account);
/* 2181 */       if ("".equals(userid)) userid = Util.null2String(loginid); 
/*      */     } else {
/* 2183 */       userid = Util.null2String(loginid);
/*      */     } 
/* 2185 */     return userid;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static String transUserId2(String id, int idtype) {
/* 2195 */     String userid = "";
/*      */     try {
/* 2197 */       boolean isldap = "ldap".equals(Prop.getPropValue(GCONST.getConfigFile(), "authentic"));
/*      */ 
/*      */       
/* 2200 */       boolean noaccount = (Util.getIntValue(Prop.getPropValue("wxinterface", "noaccount"), 0) == 1);
/*      */       
/* 2202 */       RecordSet rs = new RecordSet();
/* 2203 */       String sql = "";
/* 2204 */       if (idtype == 1) {
/* 2205 */         sql = "select loginid,account from hrmresource where id = " + Util.getIntValue(id);
/* 2206 */         rs.executeSql(sql);
/* 2207 */         if (rs.next()) {
/* 2208 */           if (isldap && !noaccount) {
/* 2209 */             userid = Util.null2String(rs.getString("account"));
/* 2210 */             if ("".equals(userid)) userid = Util.null2String(rs.getString("loginid")); 
/*      */           } else {
/* 2212 */             userid = Util.null2String(rs.getString("loginid"));
/*      */           } 
/*      */         }
/*      */       } else {
/* 2216 */         if (isldap && !noaccount) {
/* 2217 */           sql = "select id from hrmresource where loginid='" + id + "' or account='" + id + "'";
/*      */         } else {
/* 2219 */           sql = "select id from hrmresource where loginid='" + id + "'";
/*      */         } 
/* 2221 */         rs.executeSql(sql);
/* 2222 */         if (rs.next()) {
/* 2223 */           userid = Util.null2String(rs.getString(1));
/*      */         }
/*      */       }
/*      */     
/* 2227 */     } catch (Exception e) {
/*      */       
/* 2229 */       e.printStackTrace();
/*      */     } 
/* 2231 */     return userid;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static boolean updateProp(String _wxsysurl, String _accesstoken) {
/* 2241 */     Map<Object, Object> propmap = new HashMap<Object, Object>();
/* 2242 */     propmap.put("wxsysurl", _wxsysurl);
/* 2243 */     propmap.put("accesstoken", _accesstoken);
/* 2244 */     return updateProp(propmap);
/*      */   }
/*      */   public static boolean updateProp(Map map) {
/*      */     try {
/* 2248 */       StringBuffer sb = new StringBuffer();
/* 2249 */       RecordSet rs = new RecordSet();
/* 2250 */       rs.executeSql("select * from wx_basesetting");
/* 2251 */       String sql = "";
/* 2252 */       if (rs.next()) {
/* 2253 */         sb.append("update wx_basesetting set ");
/* 2254 */         for (Object entry : map.entrySet()) {
/* 2255 */           String key = (String)((Map.Entry)entry).getKey();
/* 2256 */           String value = (String)((Map.Entry)entry).getValue();
/* 2257 */           sb.append(String.valueOf(key) + "='" + value + "',");
/*      */         } 
/* 2259 */         sql = sb.toString();
/* 2260 */         sql = sql.substring(0, sql.length() - 1);
/*      */       } else {
/* 2262 */         bb.writeLog("=========wx_basesetting表没有查询到数据==========");
/* 2263 */         return false;
/*      */       } 
/* 2265 */       bb.writeLog("wx_basesetting保存配置的SQL==========" + sql);
/*      */       
/* 2267 */       boolean flag = rs.executeSql(sql);
/* 2268 */       if (flag) {
/* 2269 */         loadProp();
/*      */       }
/* 2271 */       return flag;
/* 2272 */     } catch (Exception e) {
/* 2273 */       bb.writeLog("wx_basesetting保存配置异常==========" + e.getMessage());
/* 2274 */       return false;
/*      */     } 
/*      */   }
/*      */   public static String updateProp2(Map map) {
/* 2278 */     String msg = "-1";
/*      */     try {
/* 2280 */       StringBuffer sb = new StringBuffer();
/* 2281 */       RecordSet rs = new RecordSet();
/* 2282 */       rs.executeSql("select * from wx_basesetting");
/* 2283 */       if (rs.next()) {
/* 2284 */         sb.append("update wx_basesetting set ");
/* 2285 */         for (Object entry : map.entrySet()) {
/* 2286 */           String key = (String)((Map.Entry)entry).getKey();
/* 2287 */           String value = (String)((Map.Entry)entry).getValue();
/* 2288 */           sb.append(String.valueOf(key) + "='" + value + "',");
/*      */         } 
/* 2290 */         String sql = sb.toString();
/* 2291 */         sql = sql.substring(0, sql.length() - 1);
/* 2292 */         bb.writeLog("wx_basesetting保存配置的SQL==========" + sql);
/*      */         
/* 2294 */         boolean flag = rs.executeSql(sql);
/* 2295 */         if (flag) {
/* 2296 */           loadProp();
/* 2297 */           msg = "";
/*      */         } else {
/* 2299 */           msg = "wx_basesetting保存配置SQL执行失败,请查看OA日志中的错误信息";
/*      */         } 
/*      */       } else {
/* 2302 */         bb.writeLog("=========wx_basesetting表没有查询到数据==========");
/* 2303 */         msg = "wx_basesetting表没有查询到数据,请检查OA升级云桥补丁包是否有问题";
/*      */       } 
/* 2305 */     } catch (Exception e) {
/* 2306 */       bb.writeLog("wx_basesetting保存配置异常==========" + e.getMessage());
/* 2307 */       msg = "wx_basesetting保存配置程序异常：" + e.getMessage();
/*      */     } 
/* 2309 */     return msg;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static JSONArray getMpList() {
/* 2318 */     String result = "";
/* 2319 */     PostMethod postMethod = null;
/*      */     try {
/* 2321 */       postMethod = new PostMethod(String.valueOf(getWxsysurl()) + "/wxthirdapi/getMpList");
/* 2322 */       NameValuePair[] param = new NameValuePair[2];
/* 2323 */       param[0] = new NameValuePair("accesstoken", getToken());
/* 2324 */       param[1] = new NameValuePair("outsysid", getOutsysid());
/* 2325 */       postMethod.setRequestBody(param);
/* 2326 */       postMethod.getParams().setParameter("http.protocol.content-charset", "UTF8");
/*      */ 
/*      */       
/* 2329 */       HttpClient http = HttpClientConnectionManager.getHttpClient();
/* 2330 */       http.getHttpConnectionManager().getParams().setConnectionTimeout(ctimeout);
/* 2331 */       http.getHttpConnectionManager().getParams().setSoTimeout(stimeout);
/*      */       
/* 2333 */       http.executeMethod((HttpMethod)postMethod);
/* 2334 */       result = postMethod.getResponseBodyAsString();
/*      */       
/* 2336 */       JSONObject rjson = new JSONObject(result);
/* 2337 */       if ("0".equals(rjson.getString("status"))) {
/* 2338 */         return rjson.getJSONArray("mpList");
/*      */       }
/* 2340 */       return null;
/*      */     }
/* 2342 */     catch (Exception e) {
/* 2343 */       bb.writeLog("调用云桥系统接口获取公众号异常---异常信息：" + e);
/* 2344 */       return null;
/*      */     } finally {
/*      */       try {
/* 2347 */         if (postMethod != null)
/* 2348 */           postMethod.releaseConnection(); 
/* 2349 */       } catch (Exception exception) {}
/*      */     } 
/*      */   }
/*      */ 
/*      */   
/*      */   public static String sendDocMsg(String docids, Map<String, String[]> map, String userids, int ifSafe, int ifReback, String tpids, int sendType) {
/* 2355 */     String msg = "";
/*      */     try {
/* 2357 */       if (!tpids.equals("")) {
/*      */         
/* 2359 */         String[] ds = docids.split(",");
/* 2360 */         if (ds != null && ds.length <= 10) {
/* 2361 */           RecordSet rs = new RecordSet();
/* 2362 */           JSONArray array = new JSONArray();
/* 2363 */           DocImageManager docImageManager = new DocImageManager(); byte b; int i; String[] arrayOfString;
/* 2364 */           for (i = (arrayOfString = ds).length, b = 0; b < i; ) { String docid = arrayOfString[b];
/* 2365 */             if ("sqlserver".equalsIgnoreCase(rs.getDBType())) {
/* 2366 */               rs.executeSql("select a.docsubject as title,a.doccontent as content,a.parentids from DOCDETAIL a where a.id=" + 
/* 2367 */                   docid);
/*      */             } else {
/* 2369 */               StringBuffer sb = new StringBuffer();
/* 2370 */               sb.append("select a.docsubject as title,b.doccontent as content,a.parentids from DOCDETAIL a,");
/* 2371 */               sb.append("DocDetailContent b where a.id = b.docid and a.id=" + docid);
/* 2372 */               rs.executeSql(sb.toString());
/*      */             } 
/* 2374 */             if (rs.next()) {
/* 2375 */               String[] temp = map.get(docid);
/* 2376 */               JSONObject json = new JSONObject();
/* 2377 */               String title = Util.getMoreStr(Util.null2String(rs.getString("title")), 32, "...");
/* 2378 */               if (temp != null && temp.length == 4 && !temp[0].equals("")) {
/* 2379 */                 title = temp[0];
/*      */               }
/* 2381 */               String parentids = Util.null2String(rs.getString("parentids"));
/* 2382 */               json.put("docid", docid);
/* 2383 */               json.put("parentids", parentids);
/* 2384 */               String author = "";
/* 2385 */               if (temp != null && temp.length == 4 && !temp[1].equals("")) {
/* 2386 */                 author = temp[1];
/*      */               }
/* 2388 */               json.put("author", author);
/* 2389 */               String desc = "";
/* 2390 */               if (temp != null && temp.length == 4 && !temp[2].equals("")) {
/* 2391 */                 desc = temp[2];
/*      */               } else {
/* 2393 */                 desc = title;
/*      */               } 
/* 2395 */               json.put("desc", desc);
/* 2396 */               String coverpic = "";
/* 2397 */               if (temp != null && temp.length == 4 && !temp[3].equals("")) {
/* 2398 */                 coverpic = temp[3];
/* 2399 */                 saveFile(coverpic);
/*      */               } 
/* 2401 */               json.put("coverpic", coverpic);
/* 2402 */               json.put("url", "");
/* 2403 */               String content = Util.null2String(rs.getString("content"));
/* 2404 */               if (sendType == 12 && 
/* 2405 */                 content.equals("")) {
/* 2406 */                 msg = "标题：“" + title + "”的文档内容为空";
/*      */                 
/*      */                 break;
/*      */               } 
/* 2410 */               json.put("title", title);
/* 2411 */               int tmppos = content.indexOf("!@#$%^&*");
/* 2412 */               if (tmppos != -1) content = content.substring(tmppos + 8, content.length()); 
/* 2413 */               content = Util.replace(content, "&amp;", "&", 0);
/* 2414 */               content = content.replaceAll("<title>((.|\n)*?)</title>", "");
/* 2415 */               content = content.replaceAll("　", "#SBCnbsp;");
/* 2416 */               content = content.replace("http://WeaverReservedURL", "");
/* 2417 */               content = content.replace("#SBCnbsp;", "　");
/* 2418 */               json.put("content", content);
/*      */               
/* 2420 */               String fileids = dealContent(content, "");
/* 2421 */               if (!fileids.equals("")) {
/* 2422 */                 fileids = fileids.substring(1);
/* 2423 */                 String[] fileidss = fileids.split(","); byte b1; int j; String[] arrayOfString1;
/* 2424 */                 for (j = (arrayOfString1 = fileidss).length, b1 = 0; b1 < j; ) { String fileid = arrayOfString1[b1];
/* 2425 */                   saveFile(fileid); b1++; }
/*      */               
/*      */               } 
/* 2428 */               json.put("fileids", fileids);
/*      */               
/* 2430 */               JSONArray js = new JSONArray();
/* 2431 */               int docImageId = 0;
/* 2432 */               docImageManager.resetParameter();
/* 2433 */               docImageManager.setDocid(Integer.parseInt(docid));
/* 2434 */               docImageManager.selectDocImageInfo();
/* 2435 */               while (docImageManager.next()) {
/* 2436 */                 int temdiid = docImageManager.getId();
/* 2437 */                 if (temdiid == docImageId) {
/*      */                   continue;
/*      */                 }
/* 2440 */                 docImageId = temdiid;
/* 2441 */                 String curimgid = docImageManager.getImagefileid();
/* 2442 */                 String docFileType = docImageManager.getDocfiletype();
/* 2443 */                 long docImagefileSize = docImageManager.getImageFileSize(Util.getIntValue(curimgid));
/* 2444 */                 String docImagefilename = Util.null2String(docImageManager.getImagefilename());
/* 2445 */                 String docImagefileSizeStr = "";
/* 2446 */                 if (docImagefileSize / 1048576L > 0L) {
/* 2447 */                   docImagefileSizeStr = String.valueOf(docImagefileSize / 1024L / 1024L) + "M";
/* 2448 */                 } else if (docImagefileSize / 1024L > 0L) {
/* 2449 */                   docImagefileSizeStr = String.valueOf(docImagefileSize / 1024L) + "K";
/*      */                 } else {
/* 2451 */                   docImagefileSizeStr = String.valueOf(docImagefileSize) + "B";
/*      */                 } 
/*      */                 
/* 2454 */                 int idx = docImagefilename.length() - 200;
/* 2455 */                 String urlfilename = (idx > 0) ? docImagefilename.substring(idx, docImagefilename.length()) : docImagefilename;
/* 2456 */                 urlfilename = URLEncoder.encode(urlfilename, "UTF-8");
/* 2457 */                 JSONObject fj = new JSONObject();
/* 2458 */                 fj.put("name", docImagefilename);
/* 2459 */                 fj.put("urlfilename", urlfilename);
/* 2460 */                 fj.put("size", docImagefileSizeStr);
/* 2461 */                 fj.put("fileid", curimgid);
/* 2462 */                 fj.put("docFileType", docFileType);
/* 2463 */                 js.put(fj);
/*      */               } 
/* 2465 */               json.put("fj", js);
/* 2466 */               array.put(json);
/*      */             }  b++; }
/*      */           
/* 2469 */           if (msg.equals("")) {
/*      */             
/* 2471 */             String users = "";
/* 2472 */             if (userids.indexOf("@all") < 0) {
/* 2473 */               if (getUserkeytype().equals("loginid")) {
/* 2474 */                 String[] us = userids.split(","); byte b1; int j; String[] arrayOfString1;
/* 2475 */                 for (j = (arrayOfString1 = us).length, b1 = 0; b1 < j; ) { String u = arrayOfString1[b1];
/* 2476 */                   if (!"".equals(u))
/* 2477 */                     users = String.valueOf(users) + "," + Util.null2String(transUserId(u, 1)); 
/*      */                   b1++; }
/*      */                 
/* 2480 */                 users = users.substring(1);
/* 2481 */               } else if (getUserkeytype().equals("workcode")) {
/* 2482 */                 String[] us = userids.split(","); byte b1; int j; String[] arrayOfString1;
/* 2483 */                 for (j = (arrayOfString1 = us).length, b1 = 0; b1 < j; ) { String u = arrayOfString1[b1];
/* 2484 */                   if (!"".equals(u))
/* 2485 */                     users = String.valueOf(users) + "," + getUserWorkcode(u); 
/*      */                   b1++; }
/*      */                 
/* 2488 */                 users = users.substring(1);
/*      */               } else {
/* 2490 */                 users = userids;
/*      */               } 
/*      */             } else {
/* 2493 */               users = userids;
/*      */             } 
/*      */ 
/*      */ 
/*      */             
/* 2498 */             PostMethod postMethod = null;
/*      */             try {
/* 2500 */               postMethod = new PostMethod(String.valueOf(getWxsysurl()) + "/wxthirdapi/sendWxNewsMsg");
/* 2501 */               NameValuePair[] param = new NameValuePair[8];
/* 2502 */               param[0] = new NameValuePair("news", array.toString());
/* 2503 */               param[1] = new NameValuePair("tpids", tpids);
/* 2504 */               param[2] = new NameValuePair("userids", users);
/* 2505 */               param[3] = new NameValuePair("outsysid", getOutsysid());
/* 2506 */               param[4] = new NameValuePair("ifSafe", (new StringBuilder(String.valueOf(ifSafe))).toString());
/* 2507 */               param[5] = new NameValuePair("ifReback", (new StringBuilder(String.valueOf(ifReback))).toString());
/* 2508 */               param[6] = new NameValuePair("sendType", (new StringBuilder(String.valueOf(sendType))).toString());
/* 2509 */               param[7] = new NameValuePair("accesstoken", getToken());
/* 2510 */               postMethod.setRequestBody(param);
/* 2511 */               postMethod.getParams().setParameter("http.protocol.content-charset", "UTF-8");
/*      */ 
/*      */               
/* 2514 */               HttpClient http = HttpClientConnectionManager.getHttpClient();
/* 2515 */               http.getHttpConnectionManager().getParams().setConnectionTimeout(180000);
/* 2516 */               http.getHttpConnectionManager().getParams().setSoTimeout(180000);
/*      */               
/* 2518 */               http.executeMethod((HttpMethod)postMethod);
/* 2519 */               String result = postMethod.getResponseBodyAsString();
/* 2520 */               JSONObject rjson = new JSONObject(result);
/* 2521 */               if ("0".equals(rjson.getString("status"))) {
/* 2522 */                 String details = rjson.getString("details");
/* 2523 */                 if (!details.equals("")) {
/* 2524 */                   msg = details;
/*      */                 }
/*      */               } else {
/* 2527 */                 msg = rjson.getString("msg");
/*      */               } 
/* 2529 */             } catch (Exception e) {
/* 2530 */               e.printStackTrace();
/* 2531 */               msg = "发送消息程序异常:" + e.getMessage();
/*      */             } finally {
/*      */               try {
/* 2534 */                 if (postMethod != null)
/* 2535 */                   postMethod.releaseConnection(); 
/* 2536 */               } catch (Exception exception) {}
/*      */             } 
/*      */           } 
/*      */         } else {
/*      */           
/* 2541 */           msg = "没有获取到文档或者文档个数超过10个";
/*      */         } 
/*      */       } else {
/* 2544 */         msg = "没有获取到配置的文档群发模板";
/*      */       } 
/* 2546 */     } catch (Exception e) {
/* 2547 */       e.printStackTrace();
/* 2548 */       msg = "文档消息推送失败:" + e.getMessage();
/*      */     } 
/* 2550 */     return msg;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static String uploadFileToWx(String fileid, int returnType) {
/* 2558 */     String rst = "";
/* 2559 */     PostMethod postMethod = null;
/*      */     try {
/* 2561 */       postMethod = new PostMethod(String.valueOf(getWxsysurl()) + "/wxthirdapi/uploadFile");
/* 2562 */       NameValuePair[] param = new NameValuePair[4];
/* 2563 */       param[0] = new NameValuePair("fileid", fileid);
/* 2564 */       param[1] = new NameValuePair("outsysid", getOutsysid());
/* 2565 */       param[2] = new NameValuePair("ifReturnUrl", (returnType == 1) ? "0" : "1");
/* 2566 */       param[3] = new NameValuePair("accesstoken", getToken());
/* 2567 */       postMethod.setRequestBody(param);
/* 2568 */       postMethod.getParams().setParameter("http.protocol.content-charset", "UTF-8");
/*      */ 
/*      */       
/* 2571 */       HttpClient http = HttpClientConnectionManager.getHttpClient();
/* 2572 */       http.getHttpConnectionManager().getParams().setConnectionTimeout(60000);
/* 2573 */       http.getHttpConnectionManager().getParams().setSoTimeout(60000);
/*      */       
/* 2575 */       http.executeMethod((HttpMethod)postMethod);
/* 2576 */       String result = postMethod.getResponseBodyAsString();
/* 2577 */       JSONObject rjson = new JSONObject(result);
/* 2578 */       if (rjson.getString("errmsg").equals("")) {
/* 2579 */         if (returnType == 1) {
/* 2580 */           rst = rjson.getString("fileid");
/*      */         } else {
/* 2582 */           rst = rjson.getString("fileUrl");
/*      */         } 
/*      */       }
/* 2585 */     } catch (Exception e) {
/* 2586 */       e.printStackTrace();
/*      */     } finally {
/*      */       try {
/* 2589 */         if (postMethod != null)
/* 2590 */           postMethod.releaseConnection(); 
/* 2591 */       } catch (Exception exception) {}
/*      */     } 
/*      */     
/* 2594 */     return rst;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static Map<String, String> saveOutsysApp(String name, String url, int savetype) {
/* 2607 */     String msgcode = "";
/* 2608 */     String msginfo = "";
/* 2609 */     PostMethod postMethod = null;
/*      */     try {
/* 2611 */       postMethod = new PostMethod(String.valueOf(getWxsysurl()) + "/wxthirdapi/saveOutsysApp");
/* 2612 */       NameValuePair[] param = new NameValuePair[5];
/* 2613 */       param[0] = new NameValuePair("accesstoken", getToken());
/* 2614 */       param[1] = new NameValuePair("outsysid", getOutsysid());
/* 2615 */       param[2] = new NameValuePair("page_name", name);
/* 2616 */       param[3] = new NameValuePair("page_target", url);
/* 2617 */       param[4] = new NameValuePair("savetype", String.valueOf(savetype));
/*      */       
/* 2619 */       postMethod.setRequestBody(param);
/* 2620 */       postMethod.getParams().setParameter("http.protocol.content-charset", "UTF8");
/*      */ 
/*      */       
/* 2623 */       HttpClient http = HttpClientConnectionManager.getHttpClient();
/* 2624 */       http.getHttpConnectionManager().getParams().setConnectionTimeout(ctimeout);
/* 2625 */       http.getHttpConnectionManager().getParams().setSoTimeout(stimeout);
/*      */       
/* 2627 */       http.executeMethod((HttpMethod)postMethod);
/* 2628 */       String result = postMethod.getResponseBodyAsString();
/*      */       
/* 2630 */       JSONObject rjson = new JSONObject(result);
/*      */       
/* 2632 */       msgcode = Util.null2String(rjson.getString("errcode"));
/* 2633 */       msginfo = Util.null2String(rjson.getString("errmsg"));
/* 2634 */       if (!"0".equals(msgcode)) {
/* 2635 */         bb.writeLog("调用集成平台系统接口创建功能模块失败---模块名称：" + name + " 链接地址：" + url + " 错误信息：" + msginfo);
/*      */       }
/* 2637 */     } catch (Exception e) {
/* 2638 */       bb.writeLog("调用集成平台系统接口创建功能模块失败---模块名称：" + name + " 链接地址：" + url + " 错误信息：" + e.getMessage());
/* 2639 */       msgcode = "1";
/* 2640 */       msginfo = "保存失败" + e.getMessage();
/*      */     } finally {
/*      */       try {
/* 2643 */         if (postMethod != null)
/* 2644 */           postMethod.releaseConnection(); 
/* 2645 */       } catch (Exception exception) {}
/*      */     } 
/*      */ 
/*      */     
/* 2649 */     Map<String, String> msgmap = new HashMap<String, String>();
/* 2650 */     msgmap.put("msgcode", msgcode);
/* 2651 */     msgmap.put("msginfo", msginfo);
/* 2652 */     return msgmap;
/*      */   }
/*      */ 
/*      */   
/*      */   public static String dealContent(String content, String fileids) {
/* 2657 */     int i = content.indexOf("/weaver/weaver.file.FileDownload?fileid=");
/* 2658 */     if (i > -1) {
/* 2659 */       String lastContent = content.substring(i + "/weaver/weaver.file.FileDownload?fileid=".length(), content.length());
/* 2660 */       int j = lastContent.indexOf("\"");
/* 2661 */       if (j == -1) {
/* 2662 */         j = lastContent.indexOf("'");
/*      */       }
/* 2664 */       if (j != -1) {
/* 2665 */         String fileid = lastContent.substring(0, j);
/* 2666 */         fileids = String.valueOf(fileids) + "," + fileid;
/* 2667 */         content = lastContent.substring(j);
/* 2668 */         fileids = dealContent(content, fileids);
/*      */       } 
/*      */     } 
/* 2671 */     return fileids;
/*      */   }
/*      */   public static void saveFile(String fileid) {
/* 2674 */     RecordSet rs = new RecordSet();
/* 2675 */     rs.executeSql("insert into DocPicUpload (imagefileid) values (" + fileid + ")");
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static String getLocalIP() {
/*      */     try {
/* 2684 */       InetAddress addr = null;
/*      */       try {
/* 2686 */         addr = InetAddress.getLocalHost();
/* 2687 */       } catch (UnknownHostException e) {
/*      */         
/* 2689 */         e.printStackTrace();
/*      */       } 
/*      */       
/* 2692 */       byte[] ipAddr = addr.getAddress();
/* 2693 */       String ipAddrStr = "";
/* 2694 */       for (int i = 0; i < ipAddr.length; i++) {
/* 2695 */         if (i > 0) {
/* 2696 */           ipAddrStr = String.valueOf(ipAddrStr) + ".";
/*      */         }
/* 2698 */         ipAddrStr = String.valueOf(ipAddrStr) + (ipAddr[i] & 0xFF);
/*      */       } 
/*      */       
/* 2701 */       return ipAddrStr;
/* 2702 */     } catch (Exception e) {
/* 2703 */       return "";
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static void writeLoginLog(String userid) {
/* 2712 */     writeLoginLog(userid, "ebridge", "ebridge");
/*      */   }
/*      */   public static void writeLoginLog(String userid, String ipaddress, String clienttype) {
/*      */     try {
/* 2716 */       String clientAddress = (clienttype == null || "".equals(clienttype)) ? "ebridge" : clienttype;
/* 2717 */       int loginlogtype = Util.getIntValue(Prop.getPropValue("wxinterface", "loginlogtype"), 1);
/* 2718 */       if (loginlogtype == 2) clientAddress = (ipaddress == null || "".equals(ipaddress)) ? "ebridge" : ipaddress; 
/* 2719 */       HrmResourceService hrs = new HrmResourceService();
/* 2720 */       User user = hrs.getUserById(NumberUtils.toInt(userid));
/* 2721 */       if (user != null)
/*      */       {
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/* 2736 */         SysMaintenanceLog log = new SysMaintenanceLog();
/* 2737 */         log.resetParameter();
/* 2738 */         log.setRelatedId(user.getUID());
/* 2739 */         log.setRelatedName((String.valueOf(user.getFirstname()) + " " + user.getLastname()).trim());
/* 2740 */         log.setOperateType("6");
/* 2741 */         log.setOperateDesc("");
/* 2742 */         log.setOperateItem("60");
/* 2743 */         log.setOperateUserid(user.getUID());
/* 2744 */         log.setClientAddress(clientAddress);
/* 2745 */         log.setSysLogInfo();
/*      */       }
/*      */     
/* 2748 */     } catch (Exception exception) {}
/*      */   }
/*      */ 
/*      */   
/*      */   public static Map<String, String> userVerify(String loginid, String password) {
/* 2753 */     Map<String, String> map = new HashMap<String, String>();
/*      */     try {
/* 2755 */       String classPath = Util.null2String(Prop.getPropValue("wx_userverify", "classpath"));
/* 2756 */       if ("".equals(classPath)) {
/* 2757 */         UserVerifyInterface uvi = new EcologyUserVerify();
/* 2758 */         map = uvi.userVerify(loginid, password);
/*      */       } else {
/*      */         try {
/* 2761 */           Class<UserVerifyInterface> clazz = (Class)Class.forName(classPath);
/* 2762 */           UserVerifyInterface uvi = clazz.newInstance();
/* 2763 */           map = uvi.userVerify(loginid, password);
/* 2764 */         } catch (Exception e) {
/* 2765 */           map.put("result", "0");
/* 2766 */           map.put("msg", "自定义验证身份路径【" + classPath + "】错误,或者文件不存在");
/* 2767 */           bb.writeLog("InterfaceUtil自定义验证用户身份程序异常:" + e.getMessage());
/*      */         } 
/*      */       } 
/* 2770 */     } catch (Exception e) {
/* 2771 */       map.put("result", "0");
/* 2772 */       map.put("msg", "验证用户身份程序异常,请联系系统管理员");
/* 2773 */       bb.writeLog("InterfaceUtil验证用户身份程序异常:" + e.getMessage());
/*      */     } 
/* 2775 */     return map;
/*      */   }
/*      */   
/*      */   public static void reloadProp() {
/* 2779 */     loadProp();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static JSONObject getDDUserInfo(String userid, String ddid) {
/* 2789 */     JSONObject rjson = new JSONObject();
/* 2790 */     PostMethod postMethod = null;
/*      */     try {
/* 2792 */       postMethod = new PostMethod(String.valueOf(getWxsysurl()) + "/wxthirdapi/getDDUserInfo");
/* 2793 */       NameValuePair[] param = new NameValuePair[4];
/* 2794 */       param[0] = new NameValuePair("accesstoken", getToken());
/* 2795 */       param[1] = new NameValuePair("outsysid", getOutsysid());
/* 2796 */       param[2] = new NameValuePair("userid", userid);
/* 2797 */       param[3] = new NameValuePair("ddid", ddid);
/*      */       
/* 2799 */       postMethod.setRequestBody(param);
/* 2800 */       postMethod.getParams().setParameter("http.protocol.content-charset", "UTF8");
/*      */ 
/*      */       
/* 2803 */       HttpClient http = HttpClientConnectionManager.getHttpClient();
/* 2804 */       http.getHttpConnectionManager().getParams().setConnectionTimeout(ctimeout);
/* 2805 */       http.getHttpConnectionManager().getParams().setSoTimeout(stimeout);
/*      */       
/* 2807 */       http.executeMethod((HttpMethod)postMethod);
/* 2808 */       String result = postMethod.getResponseBodyAsString();
/* 2809 */       rjson = new JSONObject(result);
/* 2810 */     } catch (Exception e) {
/* 2811 */       e.printStackTrace();
/*      */       try {
/* 2813 */         rjson.put("status", "1");
/* 2814 */         rjson.put("msg", "OA程序异常:" + e.getMessage());
/* 2815 */       } catch (JSONException e1) {
/* 2816 */         e1.printStackTrace();
/*      */       } 
/*      */     } finally {
/*      */       try {
/* 2820 */         if (postMethod != null)
/* 2821 */           postMethod.releaseConnection(); 
/* 2822 */       } catch (Exception exception) {}
/*      */     } 
/*      */     
/* 2825 */     return rjson;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static JSONObject getOAUserInfoByDDID(String dduserid, String ddid) {
/* 2835 */     JSONObject rjson = new JSONObject();
/* 2836 */     PostMethod postMethod = null;
/*      */     try {
/* 2838 */       postMethod = new PostMethod(String.valueOf(getWxsysurl()) + "/wxthirdapi/getOAUserInfoByDDID");
/* 2839 */       NameValuePair[] param = new NameValuePair[4];
/* 2840 */       param[0] = new NameValuePair("accesstoken", getToken());
/* 2841 */       param[1] = new NameValuePair("outsysid", getOutsysid());
/* 2842 */       param[2] = new NameValuePair("dduserid", dduserid);
/* 2843 */       param[3] = new NameValuePair("ddid", ddid);
/*      */       
/* 2845 */       postMethod.setRequestBody(param);
/* 2846 */       postMethod.getParams().setParameter("http.protocol.content-charset", "UTF8");
/*      */ 
/*      */       
/* 2849 */       HttpClient http = HttpClientConnectionManager.getHttpClient();
/* 2850 */       http.getHttpConnectionManager().getParams().setConnectionTimeout(ctimeout);
/* 2851 */       http.getHttpConnectionManager().getParams().setSoTimeout(stimeout);
/*      */       
/* 2853 */       http.executeMethod((HttpMethod)postMethod);
/* 2854 */       String result = postMethod.getResponseBodyAsString();
/* 2855 */       rjson = new JSONObject(result);
/* 2856 */     } catch (Exception e) {
/* 2857 */       e.printStackTrace();
/*      */       try {
/* 2859 */         rjson.put("status", "1");
/* 2860 */         rjson.put("msg", "OA程序异常:" + e.getMessage());
/* 2861 */       } catch (JSONException e1) {
/* 2862 */         e1.printStackTrace();
/*      */       } 
/*      */     } finally {
/*      */       try {
/* 2866 */         if (postMethod != null)
/* 2867 */           postMethod.releaseConnection(); 
/* 2868 */       } catch (Exception exception) {}
/*      */     } 
/*      */     
/* 2871 */     return rjson;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static JSONObject calendarCreate(String json, String sysCorpId) {
/* 2881 */     JSONObject rjson = new JSONObject();
/* 2882 */     PostMethod postMethod = null;
/*      */     try {
/* 2884 */       bb.writeLog("====调用云桥接口创建日程开始=====");
/* 2885 */       JSONObject params = new JSONObject(json);
/* 2886 */       bb.writeLog("调用方法传递的参数为=====" + json);
/* 2887 */       String userids = params.getString("userids");
/* 2888 */       String creater = params.getString("creater");
/* 2889 */       String[] useridss = userids.split(",");
/* 2890 */       String ebUserids = ""; byte b; int i;
/*      */       String[] arrayOfString1;
/* 2892 */       for (i = (arrayOfString1 = useridss).length, b = 0; b < i; ) { String u = arrayOfString1[b];
/* 2893 */         String id = u;
/* 2894 */         if (getUserkeytype().equals("loginid")) {
/* 2895 */           id = Util.null2String(transUserId(u, 1));
/* 2896 */         } else if (getUserkeytype().equals("workcode")) {
/* 2897 */           id = getUserWorkcode(u);
/*      */         } 
/* 2899 */         if (!"".equals(id))
/* 2900 */           ebUserids = String.valueOf(ebUserids) + "," + id; 
/*      */         b++; }
/*      */       
/* 2903 */       if (!ebUserids.equals("")) {
/* 2904 */         ebUserids = ebUserids.substring(1);
/*      */       }
/* 2906 */       params.put("userids", ebUserids);
/*      */       
/* 2908 */       if (getUserkeytype().equals("loginid")) {
/* 2909 */         creater = Util.null2String(transUserId(creater, 1));
/* 2910 */       } else if (getUserkeytype().equals("workcode")) {
/* 2911 */         creater = getUserWorkcode(creater);
/*      */       } 
/* 2913 */       params.put("creater", creater);
/* 2914 */       bb.writeLog("经过转换后调用云桥的参数为=====" + params.toString());
/* 2915 */       postMethod = new PostMethod(String.valueOf(getWxsysurl()) + "/wxthirdapi/calendarCreate");
/* 2916 */       NameValuePair[] param = new NameValuePair[4];
/* 2917 */       param[0] = new NameValuePair("accesstoken", getToken());
/* 2918 */       param[1] = new NameValuePair("outsysid", getOutsysid());
/* 2919 */       param[2] = new NameValuePair("sysCorpId", sysCorpId);
/* 2920 */       param[3] = new NameValuePair("params", params.toString());
/*      */       
/* 2922 */       postMethod.setRequestBody(param);
/* 2923 */       postMethod.getParams().setParameter("http.protocol.content-charset", "UTF8");
/*      */       
/* 2925 */       HttpClient http = HttpClientConnectionManager.getHttpClient();
/* 2926 */       http.getHttpConnectionManager().getParams().setConnectionTimeout(ctimeout);
/* 2927 */       http.getHttpConnectionManager().getParams().setSoTimeout(stimeout);
/*      */       
/* 2929 */       http.executeMethod((HttpMethod)postMethod);
/* 2930 */       String result = postMethod.getResponseBodyAsString();
/* 2931 */       rjson = new JSONObject(result);
/* 2932 */       bb.writeLog("调用云桥接口返回的结果为=====" + rjson.toString());
/*      */     }
/* 2934 */     catch (Exception e) {
/* 2935 */       e.printStackTrace();
/*      */       try {
/* 2937 */         rjson.put("status", "1");
/* 2938 */         rjson.put("msg", "OA程序异常:" + e.getMessage());
/* 2939 */       } catch (JSONException e1) {
/* 2940 */         e1.printStackTrace();
/*      */       } 
/* 2942 */       bb.writeLog("调用InterfaceUtil接口程序异常=====" + e.getMessage());
/*      */     } finally {
/*      */       try {
/* 2945 */         if (postMethod != null)
/* 2946 */           postMethod.releaseConnection(); 
/* 2947 */       } catch (Exception exception) {}
/*      */     } 
/*      */     
/* 2950 */     bb.writeLog("====调用云桥接口创建日程结束=====");
/* 2951 */     return rjson;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static JSONObject updateModuleSetting(String settings) {
/* 2958 */     JSONObject rjson = new JSONObject();
/* 2959 */     PostMethod postMethod = null;
/*      */     try {
/* 2961 */       postMethod = new PostMethod(String.valueOf(getWxsysurl()) + "/wxthirdapi/updateModuleSetting");
/* 2962 */       NameValuePair[] param = new NameValuePair[4];
/* 2963 */       param[0] = new NameValuePair("accesstoken", getToken());
/* 2964 */       param[1] = new NameValuePair("outsysid", getOutsysid());
/* 2965 */       String modules = Util.null2String(Prop.getPropValue("csdev_rbjf", "modules"));
/* 2966 */       param[2] = new NameValuePair("modules", modules);
/* 2967 */       param[3] = new NameValuePair("settings", settings);
/* 2968 */       postMethod.setRequestBody(param);
/* 2969 */       postMethod.getParams().setParameter("http.protocol.content-charset", "UTF8");
/* 2970 */       HttpClient http = HttpClientConnectionManager.getHttpClient();
/* 2971 */       http.getHttpConnectionManager().getParams().setConnectionTimeout(ctimeout);
/* 2972 */       http.getHttpConnectionManager().getParams().setSoTimeout(stimeout);
/* 2973 */       http.executeMethod((HttpMethod)postMethod);
/* 2974 */       String result = postMethod.getResponseBodyAsString();
/* 2975 */       rjson = new JSONObject(result);
/* 2976 */     } catch (Exception e) {
/* 2977 */       e.printStackTrace();
/*      */       try {
/* 2979 */         rjson.put("status", "1");
/* 2980 */         rjson.put("msg", "OA程序异常:" + e.getMessage());
/* 2981 */       } catch (JSONException e1) {
/* 2982 */         e1.printStackTrace();
/*      */       } 
/*      */     } finally {
/*      */       try {
/* 2986 */         if (postMethod != null)
/* 2987 */           postMethod.releaseConnection(); 
/* 2988 */       } catch (Exception exception) {}
/*      */     } 
/*      */     
/* 2991 */     return rjson;
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/wxinterface/InterfaceUtil.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */