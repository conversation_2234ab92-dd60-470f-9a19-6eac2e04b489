/*      */ package weaver.wxinterface;
/*      */ 
/*      */ import java.util.ArrayList;
/*      */ import java.util.HashMap;
/*      */ import java.util.List;
/*      */ import java.util.Map;
/*      */ import org.apache.commons.lang.StringUtils;
/*      */ import org.apache.commons.lang.math.NumberUtils;
/*      */ import weaver.conn.BatchRecordSet;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.file.Prop;
/*      */ import weaver.general.BaseBean;
/*      */ import weaver.general.GCONST;
/*      */ import weaver.general.Util;
/*      */ import weaver.hrm.HrmUserVarify;
/*      */ import weaver.hrm.User;
/*      */ import weaver.hrm.company.CompanyComInfo;
/*      */ import weaver.hrm.company.DepartmentComInfo;
/*      */ import weaver.hrm.company.SubCompanyComInfo;
/*      */ import weaver.hrm.group.GroupAction;
/*      */ import weaver.hrm.job.JobTitlesComInfo;
/*      */ import weaver.hrm.location.LocationComInfo;
/*      */ import weaver.hrm.resource.ResourceComInfo;
/*      */ import weaver.systeminfo.SystemEnv;
/*      */ import weaver.workflow.workflow.WorkTypeComInfo;
/*      */ 
/*      */ 
/*      */ public class HrmResourceService
/*      */   extends BaseBean
/*      */ {
/*   31 */   private static String formids = "7,13,46,49,74,79,158,181,182,200,10,11,156,28,180,14,159,38,85,18,19,201,224,17,21,163,157";
/*      */   
/*      */   public int getUserId(String loginId) {
/*      */     try {
/*   35 */       String sql = "";
/*      */       
/*   37 */       String mode = Prop.getPropValue(GCONST.getConfigFile(), "authentic");
/*   38 */       if ("ldap".equals(mode)) {
/*      */         
/*   40 */         sql = "select id from HrmResource where loginid='" + loginId + "' and (accounttype is null  or accounttype=0) ";
/*      */       } else {
/*      */         
/*   43 */         sql = "select id from HrmResource where loginid='" + loginId + "' and (accounttype is null  or accounttype=0) ";
/*      */       } 
/*      */       
/*   46 */       sql = String.valueOf(sql) + " union select id from HrmResourcemanager where loginid='" + loginId + "'";
/*   47 */       RecordSet rs = new RecordSet();
/*   48 */       rs.executeSql(sql);
/*   49 */       if (rs.next() && Util.getIntValue(rs.getString(1), 0) > 0) {
/*   50 */         return Util.getIntValue(rs.getString(1));
/*      */       }
/*   52 */     } catch (Exception e) {
/*   53 */       e.printStackTrace();
/*      */     } 
/*   55 */     return 0;
/*      */   }
/*      */   
/*      */   public User getUserById(int userId) {
/*   59 */     RecordSet rs = new RecordSet();
/*   60 */     User user = null;
/*   61 */     String sql = "select * from HrmResource where id=" + userId;
/*   62 */     rs.executeSql(sql);
/*   63 */     if (rs.getCounts() == 0) {
/*   64 */       sql = "select * from HrmResourceManager where id=" + userId;
/*   65 */       rs.executeSql(sql);
/*      */     } 
/*   67 */     if (rs.next()) {
/*   68 */       user = new User();
/*   69 */       user.setUid(rs.getInt("id"));
/*   70 */       user.setLoginid(rs.getString("loginid"));
/*   71 */       user.setFirstname(rs.getString("firstname"));
/*   72 */       user.setLastname(rs.getString("lastname"));
/*   73 */       user.setAliasname(rs.getString("aliasname"));
/*   74 */       user.setTitle(rs.getString("title"));
/*   75 */       user.setTitlelocation(rs.getString("titlelocation"));
/*   76 */       user.setSex(rs.getString("sex"));
/*      */       
/*   78 */       user.setStatus(rs.getInt("status"));
/*      */       
/*   80 */       String languageidweaver = rs.getString("systemlanguage");
/*   81 */       user.setLanguage(Util.getIntValue(languageidweaver, 7));
/*      */       
/*   83 */       user.setTelephone(rs.getString("telephone"));
/*   84 */       user.setMobile(rs.getString("mobile"));
/*   85 */       user.setMobilecall(rs.getString("mobilecall"));
/*   86 */       user.setEmail(rs.getString("email"));
/*   87 */       user.setCountryid(rs.getString("countryid"));
/*   88 */       user.setLocationid(rs.getString("locationid"));
/*   89 */       user.setResourcetype(rs.getString("resourcetype"));
/*   90 */       user.setContractdate(rs.getString("contractdate"));
/*   91 */       user.setJobtitle(rs.getString("jobtitle"));
/*   92 */       user.setJobgroup(rs.getString("jobgroup"));
/*   93 */       user.setJobactivity(rs.getString("jobactivity"));
/*   94 */       user.setJoblevel(rs.getString("joblevel"));
/*   95 */       user.setSeclevel(rs.getString("seclevel"));
/*   96 */       user.setUserDepartment(Util.getIntValue(rs.getString("departmentid"), 0));
/*   97 */       user.setUserSubCompany1(Util.getIntValue(rs.getString("subcompanyid1"), 0));
/*   98 */       user.setUserSubCompany2(Util.getIntValue(rs.getString("subcompanyid2"), 0));
/*   99 */       user.setUserSubCompany3(Util.getIntValue(rs.getString("subcompanyid3"), 0));
/*  100 */       user.setUserSubCompany4(Util.getIntValue(rs.getString("subcompanyid4"), 0));
/*  101 */       user.setManagerid(rs.getString("managerid"));
/*  102 */       user.setAssistantid(rs.getString("assistantid"));
/*  103 */       user.setPurchaselimit(rs.getString("purchaselimit"));
/*  104 */       user.setCurrencyid(rs.getString("currencyid"));
/*  105 */       user.setLogintype("1");
/*  106 */       user.setAccount(rs.getString("account"));
/*  107 */       user.setRemark(rs.getString("workcode"));
/*      */     } 
/*  109 */     return user;
/*      */   }
/*      */   
/*      */   public List<String> getRelativeUser(int userId) throws Exception {
/*  113 */     List<String> result = new ArrayList<String>();
/*  114 */     String master = (new StringBuilder(String.valueOf(userId))).toString();
/*      */     
/*  116 */     RecordSet rs = new RecordSet();
/*  117 */     String sql = "select belongto from HrmResource where id=" + userId;
/*  118 */     rs.executeSql(sql);
/*  119 */     if (rs.next()) {
/*  120 */       String belongto = rs.getString("belongto");
/*  121 */       if (NumberUtils.toInt(belongto, -1) > 0) {
/*  122 */         master = belongto;
/*      */       }
/*      */     } 
/*  125 */     result.add(master);
/*      */     
/*  127 */     sql = "select id from HrmResource where belongto=" + master + " and (status = 0 or status = 1 or status = 2 or status = 3) and status != 10";
/*  128 */     rs.executeSql(sql);
/*  129 */     while (rs.next()) {
/*  130 */       String id = rs.getString("id");
/*  131 */       if (NumberUtils.toInt(id, -1) > 0) {
/*  132 */         result.add(id);
/*      */       }
/*      */     } 
/*  135 */     return result;
/*      */   }
/*      */ 
/*      */   
/*      */   public Map getUser(int userId, User user) throws Exception {
/*  140 */     ResourceComInfo rci = new ResourceComInfo();
/*  141 */     LocationComInfo lci = new LocationComInfo();
/*  142 */     JobTitlesComInfo jci = new JobTitlesComInfo();
/*  143 */     DepartmentComInfo dci = new DepartmentComInfo();
/*  144 */     SubCompanyComInfo sci = new SubCompanyComInfo();
/*      */     
/*  146 */     User touser = getUserById(userId);
/*      */     
/*  148 */     Map<Object, Object> data = new HashMap<Object, Object>();
/*  149 */     data.put("jobtitle", jci.getJobTitlesname(touser.getJobtitle()));
/*  150 */     data.put("name", touser.getLastname());
/*  151 */     data.put("id", (new StringBuilder(String.valueOf(touser.getUID()))).toString());
/*  152 */     data.put("subcom", sci.getSubCompanyname((new StringBuilder(String.valueOf(touser.getUserSubCompany1()))).toString()));
/*  153 */     data.put("dept", dci.getDepartmentname((new StringBuilder(String.valueOf(touser.getUserDepartment()))).toString()));
/*  154 */     data.put("iscur", (touser.getUID() == user.getUID()) ? "1" : "0");
/*      */ 
/*      */     
/*  157 */     data.put("sex", touser.getSex());
/*      */     
/*  159 */     String manager = Util.null2String(touser.getManagerid());
/*  160 */     String managername = rci.getLastname(manager);
/*  161 */     data.put("manager", managername);
/*      */     
/*  163 */     String status = Util.null2String((new StringBuilder(String.valueOf(touser.getStatus()))).toString());
/*  164 */     String statusname = "";
/*      */     
/*  166 */     if (status.equals("")) status = "8"; 
/*  167 */     if (status.equals("9")) statusname = SystemEnv.getHtmlLabelName(332, user.getLanguage()); 
/*  168 */     if (status.equals("0")) statusname = SystemEnv.getHtmlLabelName(15710, user.getLanguage()); 
/*  169 */     if (status.equals("1")) statusname = SystemEnv.getHtmlLabelName(15711, user.getLanguage()); 
/*  170 */     if (status.equals("2")) statusname = SystemEnv.getHtmlLabelName(480, user.getLanguage()); 
/*  171 */     if (status.equals("3")) statusname = SystemEnv.getHtmlLabelName(15844, user.getLanguage()); 
/*  172 */     if (status.equals("4")) statusname = SystemEnv.getHtmlLabelName(6094, user.getLanguage()); 
/*  173 */     if (status.equals("5")) statusname = SystemEnv.getHtmlLabelName(6091, user.getLanguage()); 
/*  174 */     if (status.equals("6")) statusname = SystemEnv.getHtmlLabelName(6092, user.getLanguage()); 
/*  175 */     if (status.equals("7")) statusname = SystemEnv.getHtmlLabelName(2245, user.getLanguage()); 
/*  176 */     if (status.equals("8")) statusname = SystemEnv.getHtmlLabelName(1831, user.getLanguage()); 
/*  177 */     data.put("status", statusname);
/*      */     
/*  179 */     String location = Util.null2String(touser.getLocationid());
/*  180 */     String locationname = lci.getLocationname(location);
/*  181 */     data.put("location", locationname);
/*      */     
/*  183 */     data.put("lastname", Util.null2String(touser.getLastname()));
/*      */     
/*  185 */     data.put("headerpic", Util.null2String(rci.getMessagerUrls((new StringBuilder(String.valueOf(touser.getUID()))).toString())));
/*      */     
/*  187 */     data.put("id", Util.null2String((new StringBuilder(String.valueOf(touser.getUID()))).toString()));
/*      */     
/*  189 */     data.put("email", Util.null2String(touser.getEmail()));
/*      */     
/*  191 */     data.put("telephone", Util.null2String(touser.getTelephone()));
/*      */     
/*  193 */     data.put("mobile", Util.null2String(touser.getMobile()));
/*      */     
/*  195 */     data.put("workcode", Util.null2String(touser.getRemark()));
/*      */     
/*  197 */     data.put("isadmin", "0");
/*  198 */     if (HrmUserVarify.checkUserRight("Mobile:Setting", touser)) {
/*  199 */       data.put("isadmin", "1");
/*      */     }
/*      */     
/*  202 */     data.put("departmentid", rci.getDepartmentID(userId));
/*      */     
/*  204 */     data.put("subcompanyid", rci.getSubCompanyID(userId));
/*      */     
/*  206 */     return data;
/*      */   }
/*      */   
/*      */   public Map getUserList(List<String> conditions, int pageIndex, int pageSize, int hrmorder, User user) throws Exception {
/*  210 */     Map<Object, Object> result = new HashMap<Object, Object>();
/*  211 */     List<Map<Object, Object>> list = new ArrayList();
/*  212 */     int count = 0;
/*  213 */     int pageCount = 0;
/*  214 */     int isHavePre = 0;
/*  215 */     int isHaveNext = 0;
/*  216 */     if (user != null) {
/*  217 */       RecordSet rs = new RecordSet();
/*      */       
/*  219 */       ResourceComInfo rci = new ResourceComInfo();
/*  220 */       LocationComInfo lci = new LocationComInfo();
/*  221 */       JobTitlesComInfo jci = new JobTitlesComInfo();
/*  222 */       DepartmentComInfo dci = new DepartmentComInfo();
/*  223 */       SubCompanyComInfo sci = new SubCompanyComInfo();
/*      */       
/*  225 */       String sql = " from hrmresource ";
/*  226 */       sql = String.valueOf(sql) + " where (status = 0 or status = 1 or status = 2 or status = 3) and status != 10 ";
/*      */       int i;
/*  228 */       for (i = 0; conditions != null && conditions.size() > 0 && i < conditions.size(); i++) {
/*  229 */         String condition = conditions.get(i);
/*  230 */         if (StringUtils.isNotEmpty(condition)) {
/*  231 */           sql = String.valueOf(sql) + " and " + condition + " ";
/*      */         }
/*      */       } 
/*      */       
/*  235 */       sql = " select count(*) as c " + sql;
/*      */       
/*  237 */       rs.executeSql(sql);
/*      */       
/*  239 */       if (rs.next()) {
/*  240 */         count = rs.getInt("c");
/*      */       }
/*  242 */       if (count <= 0) pageCount = 0; 
/*  243 */       pageCount = count / pageSize + ((count % pageSize > 0) ? 1 : 0);
/*      */       
/*  245 */       isHaveNext = (pageIndex + 1 <= pageCount) ? 1 : 0;
/*      */       
/*  247 */       isHavePre = (pageIndex - 1 >= 1) ? 1 : 0;
/*      */ 
/*      */       
/*  250 */       sql = " * from hrmresource ";
/*  251 */       sql = String.valueOf(sql) + " where (status = 0 or status = 1 or status = 2 or status = 3) and status != 10 ";
/*      */       
/*  253 */       for (i = 0; conditions != null && conditions.size() > 0 && i < conditions.size(); i++) {
/*  254 */         String condition = conditions.get(i);
/*  255 */         if (StringUtils.isNotEmpty(condition)) {
/*  256 */           sql = String.valueOf(sql) + " and " + condition + " ";
/*      */         }
/*      */       } 
/*      */       
/*  260 */       String orderBy = " order by dsporder asc,lastname asc,id asc ";
/*  261 */       String descOrderBy = " order by dsporder desc,lastname desc,id desc ";
/*  262 */       if (hrmorder == 1) {
/*  263 */         orderBy = " order by pinyinlastname asc,dsporder asc,id asc ";
/*  264 */         descOrderBy = " order by pinyinlastname desc,dsporder desc,id desc ";
/*  265 */       } else if (hrmorder == 2) {
/*  266 */         orderBy = " order by id asc,dsporder asc,lastname asc ";
/*  267 */         descOrderBy = " order by id desc,dsporder desc,lastname desc ";
/*      */       } 
/*  269 */       sql = String.valueOf(sql) + orderBy;
/*      */       
/*  271 */       if (pageIndex > 0 && pageSize > 0) {
/*      */         
/*  273 */         if ("sqlserver".equalsIgnoreCase(rs.getDBType())) {
/*  274 */           if (pageIndex > 1) {
/*  275 */             int topSize = pageSize;
/*  276 */             if (pageSize * pageIndex > count) {
/*  277 */               topSize = count - pageSize * (pageIndex - 1);
/*      */             }
/*  279 */             sql = " select top " + topSize + " * from ( select top  " + topSize + " * from ( select top " + (pageIndex * pageSize) + sql + " ) tbltemp1 " + descOrderBy + ") tbltemp2 " + orderBy;
/*      */           } else {
/*  281 */             sql = " select top " + pageSize + sql;
/*      */           } 
/*  283 */         } else if ("mysql".equalsIgnoreCase(rs.getDBType())) {
/*  284 */           int offset = pageSize * (pageIndex - 1);
/*  285 */           sql = " select " + sql + " limit " + offset + "," + pageSize;
/*      */         } else {
/*  287 */           sql = " select " + sql;
/*  288 */           sql = "select * from ( select row_.*, rownum rownum_ from ( " + sql + " ) row_ where rownum <= " + (pageIndex * pageSize) + ") where rownum_ > " + ((pageIndex - 1) * pageSize);
/*      */         } 
/*      */       } else {
/*  291 */         sql = " select " + sql;
/*      */       } 
/*      */       
/*  294 */       rs.executeSql(sql);
/*  295 */       while (rs.next()) {
/*      */ 
/*      */         
/*  298 */         Map<Object, Object> map = new HashMap<Object, Object>();
/*      */         
/*  300 */         map.put("sex", Util.null2String(rs.getString("sex")));
/*      */         
/*  302 */         String manager = Util.null2String(rs.getString("managerid"));
/*  303 */         String managername = rci.getLastname(manager);
/*  304 */         map.put("manager", managername);
/*      */         
/*  306 */         String status = Util.null2String(rs.getString("status"));
/*  307 */         String statusname = "";
/*  308 */         if (status.equals("")) status = "8"; 
/*  309 */         if (status.equals("9")) statusname = SystemEnv.getHtmlLabelName(332, user.getLanguage()); 
/*  310 */         if (status.equals("0")) statusname = SystemEnv.getHtmlLabelName(15710, user.getLanguage()); 
/*  311 */         if (status.equals("1")) statusname = SystemEnv.getHtmlLabelName(15711, user.getLanguage()); 
/*  312 */         if (status.equals("2")) statusname = SystemEnv.getHtmlLabelName(480, user.getLanguage()); 
/*  313 */         if (status.equals("3")) statusname = SystemEnv.getHtmlLabelName(15844, user.getLanguage()); 
/*  314 */         if (status.equals("4")) statusname = SystemEnv.getHtmlLabelName(6094, user.getLanguage()); 
/*  315 */         if (status.equals("5")) statusname = SystemEnv.getHtmlLabelName(6091, user.getLanguage()); 
/*  316 */         if (status.equals("6")) statusname = SystemEnv.getHtmlLabelName(6092, user.getLanguage()); 
/*  317 */         if (status.equals("7")) statusname = SystemEnv.getHtmlLabelName(2245, user.getLanguage()); 
/*  318 */         if (status.equals("8")) statusname = SystemEnv.getHtmlLabelName(1831, user.getLanguage()); 
/*  319 */         map.put("status", statusname);
/*      */         
/*  321 */         String location = Util.null2String(rs.getString("locationid"));
/*  322 */         String locationname = lci.getLocationname(location);
/*  323 */         map.put("location", locationname);
/*      */         
/*  325 */         map.put("lastname", Util.null2String(rs.getString("lastname")));
/*      */         
/*  327 */         map.put("msgerurl", Util.null2String(rs.getString("messagerurl")));
/*      */         
/*  329 */         map.put("id", Util.null2String(rs.getString("id")));
/*      */         
/*  331 */         String jobtitle = Util.null2String(rs.getString("jobtitle"));
/*  332 */         String jobtitlename = jci.getJobTitlesname(jobtitle);
/*  333 */         map.put("jobtitle", jobtitlename);
/*      */         
/*  335 */         map.put("email", Util.null2String(rs.getString("email")));
/*      */         
/*  337 */         String dept = Util.null2String(rs.getString("departmentid"));
/*  338 */         String deptname = dci.getDepartmentname(dept);
/*  339 */         map.put("dept", deptname);
/*      */         
/*  341 */         String subcom = Util.null2String(rs.getString("subcompanyid1"));
/*  342 */         String subcomname = sci.getSubCompanyname(subcom);
/*  343 */         map.put("subcom", subcomname);
/*      */         
/*  345 */         map.put("telephone", Util.null2String(rs.getString("telephone")));
/*      */         
/*  347 */         map.put("mobile", Util.null2String(rs.getString("mobile")));
/*      */         
/*  349 */         map.put("pinyinlastname", Util.null2String(rs.getString("pinyinlastname")));
/*      */         
/*  351 */         list.add(map);
/*      */       } 
/*      */ 
/*      */       
/*  355 */       result.put("result", "list");
/*      */       
/*  357 */       result.put("pagesize", (new StringBuilder(String.valueOf(pageSize))).toString());
/*  358 */       result.put("pageindex", (new StringBuilder(String.valueOf(pageIndex))).toString());
/*  359 */       result.put("count", (new StringBuilder(String.valueOf(count))).toString());
/*  360 */       result.put("pagecount", (new StringBuilder(String.valueOf(pageCount))).toString());
/*  361 */       result.put("ishavepre", (new StringBuilder(String.valueOf(isHavePre))).toString());
/*  362 */       result.put("ishavenext", (new StringBuilder(String.valueOf(isHaveNext))).toString());
/*      */       
/*  364 */       result.put("list", list);
/*      */     } 
/*      */     
/*  367 */     return result;
/*      */   }
/*      */   
/*      */   private String getStatusName(String status, User user) {
/*  371 */     String statusname = "";
/*  372 */     if (status.equals("")) status = "8"; 
/*  373 */     if (status.equals("9")) statusname = SystemEnv.getHtmlLabelName(332, user.getLanguage()); 
/*  374 */     if (status.equals("0")) statusname = SystemEnv.getHtmlLabelName(15710, user.getLanguage()); 
/*  375 */     if (status.equals("1")) statusname = SystemEnv.getHtmlLabelName(15711, user.getLanguage()); 
/*  376 */     if (status.equals("2")) statusname = SystemEnv.getHtmlLabelName(480, user.getLanguage()); 
/*  377 */     if (status.equals("3")) statusname = SystemEnv.getHtmlLabelName(15844, user.getLanguage()); 
/*  378 */     if (status.equals("4")) statusname = SystemEnv.getHtmlLabelName(6094, user.getLanguage()); 
/*  379 */     if (status.equals("5")) statusname = SystemEnv.getHtmlLabelName(6091, user.getLanguage()); 
/*  380 */     if (status.equals("6")) statusname = SystemEnv.getHtmlLabelName(6092, user.getLanguage()); 
/*  381 */     if (status.equals("7")) statusname = SystemEnv.getHtmlLabelName(2245, user.getLanguage()); 
/*  382 */     if (status.equals("8")) statusname = SystemEnv.getHtmlLabelName(1831, user.getLanguage());
/*      */     
/*  384 */     return statusname;
/*      */   }
/*      */   
/*      */   public Map getUserCount(List<String> conditions, User user) throws Exception {
/*  388 */     Map<Object, Object> result = new HashMap<Object, Object>();
/*  389 */     int count = 0;
/*  390 */     if (user != null) {
/*  391 */       RecordSet rs = new RecordSet();
/*      */       
/*  393 */       String sql = " from hrmresource ";
/*  394 */       sql = String.valueOf(sql) + " where (status = 0 or status = 1 or status = 2 or status = 3) and status != 10 ";
/*      */       
/*  396 */       for (int i = 0; conditions != null && conditions.size() > 0 && i < conditions.size(); i++) {
/*  397 */         String condition = conditions.get(i);
/*  398 */         if (StringUtils.isNotEmpty(condition)) {
/*  399 */           sql = String.valueOf(sql) + " and " + condition + " ";
/*      */         }
/*      */       } 
/*      */       
/*  403 */       sql = " select count(*) as c " + sql;
/*      */       
/*  405 */       rs.executeSql(sql);
/*      */       
/*  407 */       if (rs.next()) {
/*  408 */         count = rs.getInt("c");
/*      */       }
/*      */       
/*  411 */       result.put("result", "count");
/*      */       
/*  413 */       result.put("count", (new StringBuilder(String.valueOf(count))).toString());
/*  414 */       result.put("unread", (new StringBuilder(String.valueOf(count))).toString());
/*      */     } 
/*      */     
/*  417 */     return result;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Map getAllUser(User user, List<Map> auths, boolean isalluser) {
/*  428 */     Map<Object, Object> result = new HashMap<Object, Object>();
/*  429 */     List<Map<String, String>> allUser = new ArrayList<Map<String, String>>();
/*  430 */     RecordSet rs = new RecordSet();
/*  431 */     String languageType = getLanguageType();
/*      */     try {
/*  433 */       ResourceComInfo rci = new ResourceComInfo();
/*  434 */       JobTitlesComInfo jci = new JobTitlesComInfo();
/*  435 */       DepartmentComInfo dci = new DepartmentComInfo();
/*  436 */       SubCompanyComInfo sci = new SubCompanyComInfo();
/*  437 */       LocationComInfo lci = new LocationComInfo();
/*      */ 
/*      */ 
/*      */       
/*  441 */       boolean isldap = "ldap".equals(Prop.getPropValue(GCONST.getConfigFile(), "authentic"));
/*  442 */       String loginid = "";
/*      */       
/*  444 */       String sql = "select hrmresource.id,lastname,pinyinlastname,messagerurl,subcompanyid1,departmentid,mobile,telephone,email,jobtitle,managerid,status,loginid,account,dsporder,locationid,workcode,mobilecall,sex,birthday";
/*      */ 
/*      */ 
/*      */       
/*  448 */       int ifBsite = Util.getIntValue(Prop.getPropValue("wx_hrmbrowser", "ifBsite"), 0);
/*  449 */       int ifxmly = Util.getIntValue(Prop.getPropValue("wx_hrmbrowser", "ifxmly"), 0);
/*  450 */       int ifyd = Util.getIntValue(Prop.getPropValue("wx_hrmbrowser", "ifyd"), 0);
/*  451 */       int ifjjds = Util.getIntValue(Prop.getPropValue("wx_hrmbrowser", "ifjjds"), 0);
/*      */       
/*  453 */       boolean hasCustomFieldSetting = false;
/*  454 */       String[] customFieldSettingSplit = null;
/*  455 */       if (auths != null && auths.size() > 0) {
/*  456 */         for (int i = 0; i < auths.size(); i++) {
/*  457 */           Map m = auths.get(i);
/*  458 */           if (m != null && m.containsKey("customFieldSetting")) {
/*  459 */             String customFieldSetting = "";
/*  460 */             hasCustomFieldSetting = true;
/*  461 */             customFieldSettingSplit = m.get("customFieldSetting").toString().split("\\|");
/*  462 */             for (int j = 0; j < customFieldSettingSplit.length; j++) {
/*  463 */               if (customFieldSettingSplit[j] != null && customFieldSettingSplit[j].startsWith("field")) {
/*  464 */                 customFieldSetting = String.valueOf(customFieldSetting) + ", c." + customFieldSettingSplit[j];
/*      */               }
/*      */             } 
/*  467 */             sql = String.valueOf(sql) + customFieldSetting;
/*      */           } 
/*      */         } 
/*      */       } else {
/*  471 */         if (ifBsite == 1) {
/*  472 */           sql = String.valueOf(sql) + ",c.field27";
/*      */         }
/*  474 */         if (ifxmly == 1 || ifyd == 1) {
/*  475 */           sql = String.valueOf(sql) + ",c.field0";
/*      */         }
/*  477 */         if (ifjjds == 1) {
/*  478 */           sql = String.valueOf(sql) + ",c.field0, c.field1, c.field2, c.field3, c.field4, c.field5, c.field6, c.field7, c.field8, c.field9, c.field10, c.field11, c.field12 ";
/*      */         }
/*      */       } 
/*  481 */       int ifwfsw = Util.getIntValue(Prop.getPropValue("wx_hrmbrowser", "ifwfsw"), 0);
/*  482 */       if (ifwfsw == 1) {
/*  483 */         sql = String.valueOf(sql) + ",textfield1";
/*      */       }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  490 */       sql = String.valueOf(sql) + " from hrmresource ";
/*  491 */       if (ifBsite == 1 || ifxmly == 1 || ifyd == 1 || ifjjds == 1 || hasCustomFieldSetting) {
/*  492 */         sql = String.valueOf(sql) + " left join cus_fielddata c on hrmresource.id = c.id and c.scope = 'HrmCustomFieldByInfoType' and c.scopeid = -1 ";
/*      */       }
/*  494 */       sql = String.valueOf(sql) + " where (status = 0 or status = 1 or status = 2 or status = 3) and (accounttype is null or accounttype=0) ";
/*      */ 
/*      */       
/*  497 */       if (auths != null && auths.size() > 0 && (!hasCustomFieldSetting || auths.size() != 1)) {
/*  498 */         sql = String.valueOf(sql) + " and (1=2";
/*  499 */         Map conditionMap = null;
/*  500 */         for (int i = 0; i < auths.size(); i++) {
/*  501 */           conditionMap = auths.get(i);
/*      */           
/*  503 */           int authtype = Util.getIntValue((String)conditionMap.get("authtype"), 0);
/*  504 */           String authvalue = (String)conditionMap.get("authvalue");
/*  505 */           int authseclevel = Util.getIntValue((String)conditionMap.get("authseclevel"), 0);
/*  506 */           if (authvalue != null)
/*  507 */             if (authtype == 0) {
/*  508 */               sql = String.valueOf(sql) + " or hrmresource.id=" + authvalue;
/*  509 */             } else if (authtype == 1) {
/*  510 */               sql = String.valueOf(sql) + " or (subcompanyid1=" + authvalue + " and seclevel>=" + authseclevel + ")";
/*  511 */             } else if (authtype == 2) {
/*  512 */               sql = String.valueOf(sql) + " or (departmentid=" + authvalue + " and seclevel>=" + authseclevel + ")";
/*  513 */             } else if (authtype == 3) {
/*  514 */               String setroleid = authvalue.substring(0, authvalue.length() - 1);
/*  515 */               String setrolelevel = authvalue.substring(authvalue.length() - 1);
/*  516 */               sql = String.valueOf(sql) + " or exists (select 1 from hrmrolemembers where hrmrolemembers.resourceid = hrmresource.id and hrmrolemembers.rolelevel >= '" + 
/*  517 */                 setrolelevel + "' and hrmrolemembers.roleid = " + setroleid + 
/*  518 */                 " and hrmresource.seclevel>=" + authseclevel + ")";
/*  519 */             } else if (authtype == 4) {
/*  520 */               sql = String.valueOf(sql) + " or seclevel>=" + authseclevel;
/*  521 */             } else if (authtype == 9) {
/*  522 */               int fieldtype = Util.getIntValue((String)conditionMap.get("fieldtype"), 0);
/*  523 */               String conditionValue = "";
/*  524 */               String[] arrs = authvalue.split(",");
/*  525 */               for (int j = 0; j < arrs.length; j++) {
/*  526 */                 if (j == 0) { conditionValue = String.valueOf(conditionValue) + "'" + arrs[j] + "'"; }
/*  527 */                 else { conditionValue = String.valueOf(conditionValue) + ",'" + arrs[j] + "'"; }
/*      */               
/*  529 */               }  String filedname = "id";
/*  530 */               if (fieldtype == 1) { filedname = "loginid"; }
/*  531 */               else if (fieldtype == 2) { filedname = "workcode"; }
/*  532 */               else if (fieldtype == 3) { filedname = "mobile"; }
/*  533 */               else if (fieldtype == 4) { filedname = "email"; }
/*  534 */               else if (fieldtype == 5) { filedname = "lastname"; }
/*  535 */                sql = String.valueOf(sql) + " or hrmresource." + filedname + " in ( " + conditionValue + " )";
/*      */             }  
/*      */         } 
/*  538 */         sql = String.valueOf(sql) + ")";
/*      */       } 
/*  540 */       sql = String.valueOf(sql) + " order by hrmresource.id";
/*  541 */       writeLog("getAllUser===SQL====" + sql);
/*      */       
/*  543 */       rs.executeSql(sql);
/*  544 */       while (rs.next()) {
/*  545 */         Map<String, String> userMap = new HashMap<String, String>();
/*  546 */         String id = rs.getString("id");
/*  547 */         userMap.put("ID", id);
/*  548 */         userMap.put("Name", FormatMultiLang.format(rs.getString("lastname"), languageType));
/*  549 */         userMap.put("PYName", FormatMultiLang.format(rs.getString("pinyinlastname"), languageType));
/*  550 */         userMap.put("HeaderURL", rs.getString("messagerurl"));
/*      */         
/*  552 */         String subcom = rs.getString("subcompanyid1");
/*  553 */         userMap.put("SubCompanyID", subcom);
/*  554 */         userMap.put("SubCompanyName", FormatMultiLang.format(sci.getSubCompanyname(subcom), languageType));
/*      */         
/*  556 */         String dept = rs.getString("departmentid");
/*  557 */         userMap.put("DepartmentID", dept);
/*  558 */         userMap.put("DepartmentName", FormatMultiLang.format(dci.getDepartmentname(dept), languageType));
/*      */         
/*  560 */         userMap.put("mobile", rs.getString("mobile"));
/*  561 */         userMap.put("tel", rs.getString("telephone"));
/*  562 */         userMap.put("telephone", rs.getString("telephone"));
/*  563 */         userMap.put("email", rs.getString("email"));
/*      */         
/*  565 */         if (ifwfsw == 1) {
/*  566 */           userMap.put("workcode", rs.getString("textfield1"));
/*      */         } else {
/*  568 */           userMap.put("workcode", rs.getString("workcode"));
/*      */         } 
/*      */         
/*  571 */         String jobtitle = rs.getString("jobtitle");
/*  572 */         userMap.put("title", FormatMultiLang.format(jci.getJobTitlesname(jobtitle), languageType));
/*      */         
/*  574 */         String manager = rs.getString("managerid");
/*  575 */         userMap.put("managerID", manager);
/*  576 */         userMap.put("managerName", rci.getLastname(manager));
/*      */         
/*  578 */         userMap.put("statusName", getStatusName(rs.getString("status"), user));
/*      */         
/*  580 */         if (isldap) {
/*  581 */           loginid = Util.null2String(rs.getString("account"));
/*  582 */           if ("".equals(loginid)) loginid = Util.null2String(rs.getString("loginid")); 
/*      */         } else {
/*  584 */           loginid = Util.null2String(rs.getString("loginid"));
/*      */         } 
/*      */         
/*  587 */         if ("".equals(loginid) && !isalluser)
/*      */           continue; 
/*  589 */         userMap.put("loginid", loginid);
/*      */         
/*  591 */         String showorder = Util.null2String(rs.getString("dsporder"));
/*  592 */         if ("".equals(showorder)) {
/*  593 */           showorder = "0";
/*      */         }
/*      */         
/*  596 */         userMap.put("showorder", showorder);
/*      */         
/*  598 */         String locationid = rs.getString("locationid");
/*  599 */         userMap.put("locationID", locationid);
/*  600 */         userMap.put("locationName", lci.getLocationname(locationid));
/*      */         
/*  602 */         userMap.put("mobilecall", rs.getString("mobilecall"));
/*  603 */         userMap.put("sex", rs.getString("sex"));
/*  604 */         userMap.put("birthday", rs.getString("birthday"));
/*  605 */         if (ifBsite == 1) {
/*  606 */           String field27 = Util.null2String(rs.getString("field27"));
/*  607 */           userMap.put("field27", field27);
/*      */         } 
/*  609 */         if (ifxmly == 1 || ifyd == 1) {
/*  610 */           String field0 = Util.null2String(rs.getString("field0"));
/*  611 */           userMap.put("field0", field0);
/*      */         } 
/*  613 */         if (ifjjds == 1) {
/*  614 */           for (int i = 0; i <= 12; i++) {
/*  615 */             userMap.put("field" + i, Util.null2String(rs.getString("field" + i)));
/*      */           }
/*      */         }
/*  618 */         if (hasCustomFieldSetting && customFieldSettingSplit != null) {
/*  619 */           for (int i = 0; i < customFieldSettingSplit.length; i++) {
/*  620 */             if (customFieldSettingSplit[i] != null && customFieldSettingSplit[i].startsWith("field")) {
/*  621 */               userMap.put(customFieldSettingSplit[i], Util.null2String(rs.getString(customFieldSettingSplit[i])));
/*      */             }
/*      */           } 
/*      */         }
/*  625 */         allUser.add(userMap);
/*      */       } 
/*      */       
/*  628 */       Map<String, List<String[]>> subUserMap = new HashMap<String, List<String[]>>();
/*  629 */       rs.executeSql("select departmentid,belongto,dsporder from hrmresource where (status = 0 or status = 1 or status = 2 or status = 3) and accounttype =1");
/*  630 */       while (rs.next()) {
/*  631 */         List<String[]> subDepartmentIDList; String dept = Util.null2String(rs.getString("departmentid"));
/*  632 */         String belongto = Util.null2String(rs.getString("belongto"));
/*  633 */         String dsporder = Util.null2String(rs.getString("dsporder"));
/*  634 */         if ("".equals(dsporder)) {
/*  635 */           dsporder = "0";
/*      */         }
/*      */         
/*  638 */         if (subUserMap.containsKey(belongto)) {
/*  639 */           subDepartmentIDList = subUserMap.get(belongto);
/*      */         } else {
/*  641 */           subDepartmentIDList = (List)new ArrayList<String>();
/*      */         } 
/*  643 */         String[] s = { dept, dsporder };
/*  644 */         subDepartmentIDList.add(s);
/*  645 */         subUserMap.put(belongto, subDepartmentIDList);
/*      */       } 
/*  647 */       for (Map<String, String> m : allUser) {
/*  648 */         String id = m.get("ID");
/*  649 */         String mainDept = m.get("DepartmentID");
/*  650 */         List<String[]> subDepartmentIDList = subUserMap.get(id);
/*  651 */         String departmentids = "", dsporders = "";
/*  652 */         if (subDepartmentIDList != null && subDepartmentIDList.size() > 0) {
/*  653 */           List<String> existList = new ArrayList<String>();
/*  654 */           existList.add(mainDept);
/*  655 */           for (String[] s : subDepartmentIDList) {
/*  656 */             String dept = s[0];
/*  657 */             String dsporder = s[1];
/*  658 */             if (!existList.contains(dept)) {
/*  659 */               existList.add(dept);
/*  660 */               departmentids = String.valueOf(departmentids) + "," + dept;
/*  661 */               dsporders = String.valueOf(dsporders) + "," + dsporder;
/*      */             } 
/*      */           } 
/*  664 */           if (!departmentids.equals("")) {
/*  665 */             departmentids = departmentids.substring(1);
/*  666 */             dsporders = dsporders.substring(1);
/*      */           } 
/*      */         } 
/*  669 */         m.put("subDepartmentIDs", departmentids);
/*  670 */         m.put("subDsporders", dsporders);
/*      */       } 
/*  672 */       result.put("timestamp", getTableTimestamp("HrmResource"));
/*  673 */       result.put("data", allUser);
/*      */     }
/*  675 */     catch (Exception e) {
/*  676 */       writeLog(e);
/*  677 */       result.put("error", e.getMessage());
/*      */     } 
/*  679 */     return result;
/*      */   }
/*      */   public Map getUserList(User user, List<Map> auths, boolean isalluser, int pageIndex, int pageSize, int hrmorder, List<String> conditions) throws Exception {
/*  682 */     Map<Object, Object> result = new HashMap<Object, Object>();
/*  683 */     List<Map<Object, Object>> list = new ArrayList();
/*  684 */     int count = 0;
/*  685 */     int pageCount = 0;
/*  686 */     int isHavePre = 0;
/*  687 */     int isHaveNext = 0;
/*  688 */     String languageType = getLanguageType();
/*      */     try {
/*  690 */       if (user != null) {
/*  691 */         RecordSet rs = new RecordSet();
/*      */         
/*  693 */         ResourceComInfo rci = new ResourceComInfo();
/*  694 */         LocationComInfo lci = new LocationComInfo();
/*  695 */         JobTitlesComInfo jci = new JobTitlesComInfo();
/*  696 */         DepartmentComInfo dci = new DepartmentComInfo();
/*  697 */         SubCompanyComInfo sci = new SubCompanyComInfo();
/*      */         
/*  699 */         boolean isldap = "ldap".equals(Prop.getPropValue(GCONST.getConfigFile(), "authentic"));
/*  700 */         String loginid = "";
/*      */         
/*  702 */         String backFields = " id,lastname,pinyinlastname,messagerurl,subcompanyid1,departmentid,mobile,telephone,email,jobtitle,managerid,status,loginid,account,dsporder,locationid,workcode,mobilecall,sex";
/*  703 */         String sqlFrom = " from hrmresource where (status = 0 or status = 1 or status = 2 or status = 3) and status != 10  and (accounttype is null or accounttype=0) ";
/*      */         
/*  705 */         if (auths != null && auths.size() > 0) {
/*  706 */           sqlFrom = String.valueOf(sqlFrom) + " and (1=2";
/*  707 */           Map conditionMap = null;
/*  708 */           for (int j = 0; j < auths.size(); j++) {
/*  709 */             conditionMap = auths.get(j);
/*      */             
/*  711 */             int authtype = Util.getIntValue((String)conditionMap.get("authtype"), 0);
/*  712 */             String authvalue = (String)conditionMap.get("authvalue");
/*  713 */             int authseclevel = Util.getIntValue((String)conditionMap.get("authseclevel"), 0);
/*      */             
/*  715 */             if (authtype == 0) {
/*  716 */               sqlFrom = String.valueOf(sqlFrom) + " or id=" + authvalue;
/*  717 */             } else if (authtype == 1) {
/*  718 */               sqlFrom = String.valueOf(sqlFrom) + " or (subcompanyid1=" + authvalue + " and seclevel>=" + authseclevel + ")";
/*  719 */             } else if (authtype == 2) {
/*  720 */               sqlFrom = String.valueOf(sqlFrom) + " or (departmentid=" + authvalue + " and seclevel>=" + authseclevel + ")";
/*  721 */             } else if (authtype == 3) {
/*  722 */               String setroleid = authvalue.substring(0, authvalue.length() - 1);
/*  723 */               String setrolelevel = authvalue.substring(authvalue.length() - 1);
/*  724 */               sqlFrom = String.valueOf(sqlFrom) + " or exists (select 1 from hrmrolemembers where hrmrolemembers.resourceid = hrmresource.id and hrmrolemembers.rolelevel >= '" + 
/*  725 */                 setrolelevel + "' and hrmrolemembers.roleid = " + setroleid + 
/*  726 */                 " and hrmresource.seclevel>=" + authseclevel + ")";
/*  727 */             } else if (authtype == 4) {
/*  728 */               sqlFrom = String.valueOf(sqlFrom) + " or seclevel>=" + authseclevel;
/*      */             } 
/*      */           } 
/*      */           
/*  732 */           sqlFrom = String.valueOf(sqlFrom) + ")";
/*      */         } 
/*  734 */         for (int i = 0; conditions != null && conditions.size() > 0 && i < conditions.size(); i++) {
/*  735 */           String condition = conditions.get(i);
/*  736 */           if (StringUtils.isNotEmpty(condition)) {
/*  737 */             sqlFrom = String.valueOf(sqlFrom) + " and " + condition + " ";
/*      */           }
/*      */         } 
/*      */         
/*  741 */         String sql = " select count(*) as c " + sqlFrom;
/*      */         
/*  743 */         rs.executeSql(sql);
/*      */         
/*  745 */         if (rs.next()) {
/*  746 */           count = rs.getInt("c");
/*      */         }
/*  748 */         if (count <= 0) pageCount = 0; 
/*  749 */         pageCount = count / pageSize + ((count % pageSize > 0) ? 1 : 0);
/*      */         
/*  751 */         isHaveNext = (pageIndex + 1 <= pageCount) ? 1 : 0;
/*      */         
/*  753 */         isHavePre = (pageIndex - 1 >= 1) ? 1 : 0;
/*      */         
/*  755 */         String orderBy = " order by dsporder asc,id asc ";
/*  756 */         String descOrderBy = " order by dsporder desc,id desc ";
/*  757 */         if (hrmorder == 1) {
/*  758 */           orderBy = " order by pinyinlastname asc,dsporder asc,id asc ";
/*  759 */           descOrderBy = " order by pinyinlastname desc,dsporder desc,id desc ";
/*  760 */         } else if (hrmorder == 2) {
/*  761 */           orderBy = " order by id asc,dsporder asc,lastname asc ";
/*  762 */           descOrderBy = " order by id desc,dsporder desc,lastname desc ";
/*      */         } 
/*      */         
/*  765 */         sql = String.valueOf(backFields) + sqlFrom + orderBy;
/*      */         
/*  767 */         if (pageIndex > 0 && pageSize > 0) {
/*  768 */           if ("sqlserver".equalsIgnoreCase(rs.getDBType())) {
/*  769 */             if (pageIndex > 1) {
/*  770 */               int topSize = pageSize;
/*  771 */               if (pageSize * pageIndex > count) {
/*  772 */                 topSize = count - pageSize * (pageIndex - 1);
/*      */               }
/*  774 */               sql = " select top " + topSize + " * from ( select top  " + topSize + " * from ( select top " + (pageIndex * pageSize) + sql + " ) tbltemp1 " + descOrderBy + ") tbltemp2 " + orderBy;
/*      */             } else {
/*  776 */               sql = " select top " + pageSize + sql;
/*      */             } 
/*  778 */           } else if ("mysql".equalsIgnoreCase(rs.getDBType())) {
/*  779 */             int offset = pageSize * (pageIndex - 1);
/*  780 */             sql = " select " + sql + " limit " + offset + "," + pageSize;
/*      */           } else {
/*  782 */             sql = " select " + sql;
/*  783 */             sql = "select * from ( select row_.*, rownum rownum_ from ( " + sql + " ) row_ where rownum <= " + (pageIndex * pageSize) + ") where rownum_ > " + ((pageIndex - 1) * pageSize);
/*      */           } 
/*      */         } else {
/*  786 */           sql = " select " + sql;
/*      */         } 
/*      */         
/*  789 */         rs.executeSql(sql);
/*  790 */         while (rs.next()) {
/*  791 */           Map<Object, Object> userMap = new HashMap<Object, Object>();
/*  792 */           userMap.put("ID", rs.getString("id"));
/*  793 */           userMap.put("Name", rs.getString("lastname"));
/*  794 */           userMap.put("PYName", rs.getString("pinyinlastname"));
/*  795 */           userMap.put("HeaderURL", rs.getString("messagerurl"));
/*      */           
/*  797 */           String subcom = rs.getString("subcompanyid1");
/*  798 */           userMap.put("SubCompanyID", subcom);
/*  799 */           userMap.put("SubCompanyName", FormatMultiLang.format(sci.getSubCompanyname(subcom), languageType));
/*      */           
/*  801 */           String dept = rs.getString("departmentid");
/*  802 */           userMap.put("DepartmentID", dept);
/*  803 */           userMap.put("DepartmentName", FormatMultiLang.format(dci.getDepartmentname(dept), languageType));
/*      */           
/*  805 */           userMap.put("mobile", rs.getString("mobile"));
/*  806 */           userMap.put("tel", rs.getString("telephone"));
/*  807 */           userMap.put("email", rs.getString("email"));
/*      */           
/*  809 */           userMap.put("workcode", rs.getString("workcode"));
/*      */           
/*  811 */           String jobtitle = rs.getString("jobtitle");
/*  812 */           userMap.put("title", jci.getJobTitlesname(jobtitle));
/*      */           
/*  814 */           String manager = rs.getString("managerid");
/*  815 */           userMap.put("managerID", manager);
/*  816 */           userMap.put("managerName", rci.getLastname(manager));
/*      */           
/*  818 */           userMap.put("statusName", getStatusName(rs.getString("status"), user));
/*      */           
/*  820 */           if (isldap) {
/*  821 */             loginid = Util.null2String(rs.getString("account"));
/*  822 */             if ("".equals(loginid)) loginid = Util.null2String(rs.getString("loginid")); 
/*      */           } else {
/*  824 */             loginid = Util.null2String(rs.getString("loginid"));
/*      */           } 
/*      */           
/*  827 */           if ("".equals(loginid) && !isalluser)
/*      */             continue; 
/*  829 */           userMap.put("loginid", loginid);
/*      */           
/*  831 */           userMap.put("showorder", rs.getString("dsporder"));
/*      */           
/*  833 */           String locationid = rs.getString("locationid");
/*  834 */           userMap.put("locationID", locationid);
/*  835 */           userMap.put("locationName", lci.getLocationname(locationid));
/*      */           
/*  837 */           userMap.put("mobilecall", rs.getString("mobilecall"));
/*  838 */           userMap.put("sex", rs.getString("sex"));
/*      */ 
/*      */           
/*  841 */           list.add(userMap);
/*      */         } 
/*      */ 
/*      */         
/*  845 */         result.put("pagesize", (new StringBuilder(String.valueOf(pageSize))).toString());
/*  846 */         result.put("pageindex", (new StringBuilder(String.valueOf(pageIndex))).toString());
/*  847 */         result.put("count", (new StringBuilder(String.valueOf(count))).toString());
/*  848 */         result.put("pagecount", (new StringBuilder(String.valueOf(pageCount))).toString());
/*  849 */         result.put("ishavepre", (new StringBuilder(String.valueOf(isHavePre))).toString());
/*  850 */         result.put("ishavenext", (new StringBuilder(String.valueOf(isHaveNext))).toString());
/*  851 */         result.put("data", list);
/*      */       }
/*      */     
/*      */     }
/*  855 */     catch (Exception e) {
/*  856 */       writeLog(e);
/*  857 */       result.put("error", e.getMessage());
/*      */     } 
/*  859 */     return result;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Map getDeptsByCondition(int depttype, int querytype, String values) {
/*  870 */     Map<Object, Object> result = new HashMap<Object, Object>();
/*  871 */     return result;
/*      */   }
/*      */   
/*      */   public Map getAllDepartment(User user, String conddeptids, String condsubids) {
/*  875 */     Map<Object, Object> result = new HashMap<Object, Object>();
/*  876 */     List<Map<Object, Object>> allDept = new ArrayList();
/*  877 */     RecordSet rs = new RecordSet();
/*  878 */     String languageType = getLanguageType();
/*      */     
/*  880 */     int lang = Util.getIntValue(Prop.getPropValue("wx_hrm_language", "deparment_type"), 7);
/*  881 */     if (lang == 8) languageType = "8"; 
/*      */     try {
/*  883 */       DepartmentComInfo dci = new DepartmentComInfo();
/*  884 */       SubCompanyComInfo sci = new SubCompanyComInfo();
/*      */       
/*  886 */       String sql = "select * from HrmDepartment where (canceled is null or canceled<>'1') " + (("sqlserver".equalsIgnoreCase(rs.getDBType()) || "mysql".equalsIgnoreCase(rs.getDBType())) ? " and departmentname<>''" : "") + " and departmentname is not null";
/*      */ 
/*      */       
/*  889 */       if (!"".equals(conddeptids)) {
/*  890 */         sql = String.valueOf(sql) + " and id in (" + conddeptids + ")";
/*      */       }
/*  892 */       if (!"".equals(condsubids)) {
/*  893 */         sql = String.valueOf(sql) + " and subcompanyid1 in (" + condsubids + ")";
/*      */       }
/*  895 */       sql = String.valueOf(sql) + " order by id";
/*  896 */       rs.executeSql(sql);
/*      */       
/*  898 */       while (rs.next()) {
/*  899 */         Map<Object, Object> deptMap = new HashMap<Object, Object>();
/*  900 */         deptMap.put("ID", rs.getString("id"));
/*  901 */         deptMap.put("Name", FormatMultiLang.format(rs.getString("departmentname"), languageType));
/*      */         
/*  903 */         String supdepid = rs.getString("supdepid");
/*  904 */         deptMap.put("supDepartmentID", supdepid);
/*  905 */         deptMap.put("supDepartmentName", FormatMultiLang.format(dci.getDepartmentname(supdepid), languageType));
/*      */         
/*  907 */         String subcompanyid = rs.getString("subcompanyid1");
/*  908 */         deptMap.put("SubCompanyID", subcompanyid);
/*  909 */         deptMap.put("SubCompanyName", FormatMultiLang.format(sci.getSubCompanyname(subcompanyid), languageType));
/*  910 */         deptMap.put("showorder", rs.getString("showorder"));
/*      */         
/*  912 */         allDept.add(deptMap);
/*      */       } 
/*      */       
/*  915 */       result.put("timestamp", getTableTimestamp("HrmDepartment"));
/*  916 */       result.put("data", allDept);
/*      */     }
/*  918 */     catch (Exception e) {
/*  919 */       writeLog(e);
/*  920 */       result.put("error", e.getMessage());
/*      */     } 
/*      */     
/*  923 */     return result;
/*      */   }
/*      */   
/*      */   public Map getAllSubCompany(User user, String condsubids) {
/*  927 */     Map<Object, Object> result = new HashMap<Object, Object>();
/*  928 */     List<Map<Object, Object>> allSubcom = new ArrayList();
/*  929 */     RecordSet rs = new RecordSet();
/*  930 */     String languageType = getLanguageType();
/*      */     
/*  932 */     int lang = Util.getIntValue(Prop.getPropValue("wx_hrm_language", "deparment_type"), 7);
/*  933 */     if (lang == 8) languageType = "8"; 
/*      */     try {
/*  935 */       SubCompanyComInfo sci = new SubCompanyComInfo();
/*      */       
/*  937 */       String sql = "select * from HrmSubCompany where (canceled is null or canceled<>'1')" + (("sqlserver".equalsIgnoreCase(rs.getDBType()) || "mysql".equalsIgnoreCase(rs.getDBType())) ? " and subcompanyname<>''" : "") + " and subcompanyname is not null";
/*      */       
/*  939 */       if (!"".equals(condsubids)) {
/*  940 */         sql = String.valueOf(sql) + " and id in (" + condsubids + ")";
/*      */       }
/*  942 */       sql = String.valueOf(sql) + " order by id";
/*  943 */       rs.executeSql(sql);
/*      */       
/*  945 */       while (rs.next()) {
/*  946 */         Map<Object, Object> subcomMap = new HashMap<Object, Object>();
/*  947 */         subcomMap.put("ID", rs.getString("id"));
/*  948 */         subcomMap.put("Name", FormatMultiLang.format(rs.getString("subcompanyname"), languageType));
/*      */         
/*  950 */         String supsubcomid = rs.getString("supsubcomid");
/*  951 */         subcomMap.put("supSubCompanyID", supsubcomid);
/*  952 */         subcomMap.put("supSubCompanyName", FormatMultiLang.format(sci.getSubCompanyname(supsubcomid), languageType));
/*      */         
/*  954 */         subcomMap.put("companyID", rs.getString("companyid"));
/*  955 */         subcomMap.put("showorder", rs.getString("showorder"));
/*      */         
/*  957 */         allSubcom.add(subcomMap);
/*      */       } 
/*      */       
/*  960 */       result.put("timestamp", getTableTimestamp("HrmSubCompany"));
/*  961 */       result.put("data", allSubcom);
/*      */     }
/*  963 */     catch (Exception e) {
/*  964 */       writeLog(e);
/*  965 */       result.put("error", e.getMessage());
/*      */     } 
/*      */     
/*  968 */     return result;
/*      */   }
/*      */   
/*      */   public Map getAllCompany(User user) {
/*  972 */     Map<Object, Object> result = new HashMap<Object, Object>();
/*  973 */     List<Map<Object, Object>> allCompany = new ArrayList();
/*  974 */     RecordSet rs = new RecordSet();
/*      */     
/*      */     try {
/*  977 */       String sql = "select * from HrmCompany order by id";
/*  978 */       rs.executeSql(sql);
/*      */       
/*  980 */       while (rs.next()) {
/*  981 */         Map<Object, Object> comMap = new HashMap<Object, Object>();
/*  982 */         comMap.put("ID", rs.getString("id"));
/*  983 */         comMap.put("Name", rs.getString("companyname"));
/*      */         
/*  985 */         allCompany.add(comMap);
/*      */       } 
/*      */       
/*  988 */       result.put("timestamp", getTableTimestamp("HrmCompany"));
/*  989 */       result.put("data", allCompany);
/*      */     }
/*  991 */     catch (Exception e) {
/*  992 */       writeLog(e);
/*  993 */       result.put("error", e.getMessage());
/*      */     } 
/*      */     
/*  996 */     return result;
/*      */   }
/*      */   
/*      */   public Map getUserGroups(User user) {
/* 1000 */     Map<Object, Object> result = new HashMap<Object, Object>();
/*      */     
/* 1002 */     List<Map<Object, Object>> allGroup = new ArrayList();
/* 1003 */     RecordSet rs = new RecordSet();
/*      */     
/*      */     try {
/* 1006 */       GroupAction groupAct = new GroupAction();
/*      */       
/* 1008 */       List groups = groupAct.getGroupsByUser(user);
/* 1009 */       for (Object group : groups) {
/* 1010 */         Map groupMap = (HashMap)group;
/*      */         
/* 1012 */         Map<Object, Object> gMap = new HashMap<Object, Object>();
/* 1013 */         gMap.put("ID", groupMap.get("id"));
/* 1014 */         gMap.put("Name", groupMap.get("name"));
/* 1015 */         gMap.put("groupType", groupMap.get("type"));
/* 1016 */         gMap.put("UserID", user.getUID());
/*      */         
/* 1018 */         List<String> userList = new ArrayList<String>();
/* 1019 */         rs.executeSql("select * from HrmGroupMembers where groupid=" + groupMap.get("id"));
/* 1020 */         while (rs.next()) {
/* 1021 */           userList.add(rs.getString("userid"));
/*      */         }
/* 1023 */         gMap.put("userList", userList);
/*      */         
/* 1025 */         allGroup.add(gMap);
/*      */       } 
/*      */       
/* 1028 */       result.put("timestamp", getTableTimestamp("HrmGroup"));
/* 1029 */       result.put("data", allGroup);
/*      */     }
/* 1031 */     catch (Exception e) {
/* 1032 */       writeLog(e);
/* 1033 */       result.put("error", e.getMessage());
/*      */     } 
/*      */     
/* 1036 */     return result;
/*      */   }
/*      */   
/* 1039 */   private static Map HrmGroupMember_Cache = null;
/*      */   
/*      */   public Map getGroupMember(User user) {
/* 1042 */     Map<Object, Object> result = new HashMap<Object, Object>();
/*      */     
/*      */     try {
/* 1045 */       String cur_timestamp = Util.null2String(getTableTimestamp("HrmGroupMember"));
/* 1046 */       if (HrmGroupMember_Cache == null || !cur_timestamp.equals(HrmGroupMember_Cache.get("timestamp"))) {
/* 1047 */         HrmGroupMember_Cache = new HashMap<Object, Object>();
/*      */         
/* 1049 */         List<Map<Object, Object>> members = new ArrayList();
/* 1050 */         RecordSet rs = new RecordSet();
/* 1051 */         String sql = "select * from HrmGroupMembers order by groupid";
/* 1052 */         rs.executeSql(sql);
/*      */         
/* 1054 */         while (rs.next()) {
/* 1055 */           Map<Object, Object> memberMap = new HashMap<Object, Object>();
/* 1056 */           memberMap.put("GroupID", rs.getString("groupid"));
/* 1057 */           memberMap.put("UserID", rs.getString("userid"));
/*      */           
/* 1059 */           members.add(memberMap);
/*      */         } 
/*      */         
/* 1062 */         HrmGroupMember_Cache.put("timestamp", cur_timestamp);
/* 1063 */         HrmGroupMember_Cache.put("data", members);
/*      */       } 
/*      */       
/* 1066 */       result = HrmGroupMember_Cache;
/* 1067 */     } catch (Exception e) {
/* 1068 */       writeLog(e);
/* 1069 */       result.put("error", e.getMessage());
/*      */     } 
/*      */     
/* 1072 */     return result;
/*      */   }
/*      */   
/*      */   private String getTableTimestamp(String tableName) {
/* 1076 */     List Timestamps = new ArrayList();
/*      */     
/*      */     try {
/* 1079 */       RecordSet rs = new RecordSet();
/*      */       
/* 1081 */       String sql = "select syncLastTime from mobileSyncInfo where syncTable='" + tableName + "'";
/* 1082 */       rs.executeSql(sql);
/* 1083 */       if (rs.next()) {
/* 1084 */         return rs.getString("syncLastTime");
/*      */       }
/*      */     }
/* 1087 */     catch (Exception e) {
/* 1088 */       writeLog(e);
/*      */     } 
/*      */     
/* 1091 */     return "0";
/*      */   }
/*      */   
/*      */   public Map getTableStatus(String[] tablename, String[] timestamp) {
/* 1095 */     Map<Object, Object> result = new HashMap<Object, Object>();
/*      */     
/* 1097 */     if (tablename == null || tablename.length == 0) {
/* 1098 */       result.put("error", "Illegal Argument!");
/* 1099 */       return result;
/*      */     } 
/*      */     
/* 1102 */     RecordSet rs = new RecordSet();
/* 1103 */     List<Map<Object, Object>> tblStatus = new ArrayList();
/*      */     
/*      */     try {
/* 1106 */       for (int i = 0; i < tablename.length; i++) {
/* 1107 */         String tbname = tablename[i];
/* 1108 */         long tstamp = (timestamp != null && i < timestamp.length) ? NumberUtils.toLong(timestamp[i], 0L) : 0L;
/*      */         
/* 1110 */         if ("HrmGroup".equalsIgnoreCase(tbname)) {
/* 1111 */           Map<Object, Object> tbl = new HashMap<Object, Object>();
/* 1112 */           tbl.put("tablename", tbname);
/* 1113 */           tbl.put("hasSync", "1");
/* 1114 */           tblStatus.add(tbl);
/*      */         } else {
/* 1116 */           String sql = "select syncLastTime from mobileSyncInfo where syncTable='" + tbname + "'";
/* 1117 */           rs.executeSql(sql);
/*      */           
/* 1119 */           if (rs.next()) {
/* 1120 */             Map<Object, Object> tbl = new HashMap<Object, Object>();
/* 1121 */             tbl.put("tablename", tbname);
/* 1122 */             tbl.put("hasSync", "1");
/*      */             
/* 1124 */             long syncLastTime = NumberUtils.toLong(rs.getString("syncLastTime"));
/* 1125 */             if (syncLastTime > 0L && syncLastTime == tstamp) {
/* 1126 */               tbl.put("hasSync", "0");
/*      */             }
/*      */             
/* 1129 */             tblStatus.add(tbl);
/*      */           } 
/*      */         } 
/*      */       } 
/*      */       
/* 1134 */       result.put("data", tblStatus);
/* 1135 */     } catch (Exception e) {
/* 1136 */       writeLog(e);
/* 1137 */       result.put("error", e.getMessage());
/*      */     } 
/*      */     
/* 1140 */     return result;
/*      */   }
/*      */   
/*      */   public Map<String, Object> getWorkPlanType(User user) {
/* 1144 */     Map<String, Object> result = new HashMap<String, Object>();
/* 1145 */     List<Map<String, String>> workPlanTypeForNewList = new ArrayList<Map<String, String>>();
/* 1146 */     RecordSet recordSet = new RecordSet();
/*      */     
/*      */     try {
/* 1149 */       recordSet.executeSql("select * from OverWorkPlan where wavailable = '1' order by id");
/* 1150 */       while (recordSet.next()) {
/* 1151 */         Map<String, String> item = new HashMap<String, String>();
/* 1152 */         item.put("id", "-" + recordSet.getString("id"));
/* 1153 */         item.put("name", recordSet.getString("workPlanName"));
/* 1154 */         item.put("color", recordSet.getString("workPlanColor"));
/* 1155 */         workPlanTypeForNewList.add(item);
/*      */       } 
/* 1157 */       recordSet.executeSql("SELECT * FROM WorkPlanType WHERE available = '1' ORDER BY displayOrder ASC");
/* 1158 */       while (recordSet.next()) {
/* 1159 */         Map<String, String> item = new HashMap<String, String>();
/* 1160 */         item.put("id", recordSet.getString("workPlanTypeID"));
/* 1161 */         item.put("name", recordSet.getString("workPlanTypeName"));
/* 1162 */         item.put("color", recordSet.getString("workPlanTypeColor"));
/* 1163 */         workPlanTypeForNewList.add(item);
/*      */       } 
/*      */       
/* 1166 */       result.put("timestamp", getTableTimestamp("WorkPlanType"));
/* 1167 */       result.put("data", workPlanTypeForNewList);
/* 1168 */     } catch (Exception e) {
/* 1169 */       writeLog(e);
/* 1170 */       result.put("error", e.getMessage());
/*      */     } 
/*      */     
/* 1173 */     return result;
/*      */   }
/*      */   
/*      */   public Map<String, Object> getWorkFlowType(User user) {
/* 1177 */     Map<String, Object> result = new HashMap<String, Object>();
/*      */     
/*      */     try {
/* 1180 */       List<Map<String, String>> workFlowTypeList = new ArrayList<Map<String, String>>();
/* 1181 */       RecordSet recordSet = new RecordSet();
/* 1182 */       WorkTypeComInfo workTypeComInfo = new WorkTypeComInfo();
/*      */       
/* 1184 */       int workflowtype = 0;
/*      */       
/* 1186 */       recordSet.executeSql("select id,workflowname,workflowtype from workflow_base where isvalid='1' and (isbill=0 or (isbill=1 and formid<0) or (isbill=1 and formid in (" + formids + "))) order by workflowtype,id");
/* 1187 */       while (recordSet.next()) {
/* 1188 */         String curworkflowtype = recordSet.getString("workflowtype");
/* 1189 */         int wfType = 1000000 + Util.getIntValue(curworkflowtype);
/* 1190 */         if (workflowtype != wfType) {
/* 1191 */           Map<String, String> map = new HashMap<String, String>();
/* 1192 */           map.put("id", wfType);
/* 1193 */           map.put("name", workTypeComInfo.getWorkTypename(curworkflowtype));
/* 1194 */           map.put("parent", "0");
/* 1195 */           map.put("isParent", "1");
/* 1196 */           map.put("dsporder", workTypeComInfo.getWorkDsporder(curworkflowtype));
/* 1197 */           workFlowTypeList.add(map);
/*      */           
/* 1199 */           workflowtype = wfType;
/*      */         } 
/*      */         
/* 1202 */         Map<String, String> item = new HashMap<String, String>();
/* 1203 */         item.put("id", recordSet.getString("id"));
/* 1204 */         item.put("name", recordSet.getString("workflowname"));
/* 1205 */         item.put("parent", wfType);
/* 1206 */         item.put("isParent", "0");
/* 1207 */         item.put("dsporder", "0");
/* 1208 */         workFlowTypeList.add(item);
/*      */       } 
/*      */       
/* 1211 */       result.put("timestamp", getTableTimestamp("WorkFlowType"));
/* 1212 */       result.put("data", workFlowTypeList);
/* 1213 */     } catch (Exception e) {
/* 1214 */       writeLog(e);
/* 1215 */       result.put("error", e.getMessage());
/*      */     } 
/*      */     
/* 1218 */     return result;
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public Map<String, Object> getHrmSubCompanyTree(User user) {
/* 1224 */     Map<String, Object> result = new HashMap<String, Object>();
/* 1225 */     List<Map<String, String>> data = new ArrayList<Map<String, String>>();
/*      */     
/*      */     try {
/* 1228 */       CompanyComInfo cci = new CompanyComInfo();
/* 1229 */       SubCompanyComInfo scci = new SubCompanyComInfo();
/*      */       
/* 1231 */       Map<String, String> root = new HashMap<String, String>();
/* 1232 */       String companyname = cci.getCompanyname("1");
/* 1233 */       root.put("id", "0");
/* 1234 */       root.put("pId", "-1");
/* 1235 */       root.put("name", companyname);
/* 1236 */       root.put("open", "true");
/* 1237 */       data.add(root);
/*      */       
/* 1239 */       while (scci.next()) {
/* 1240 */         if ("1".equals(scci.getCompanyiscanceled()))
/*      */           continue; 
/* 1242 */         String supsubcomid = scci.getSupsubcomid();
/* 1243 */         if ("".equals(supsubcomid)) supsubcomid = "0";
/*      */         
/* 1245 */         Map<String, String> node = new HashMap<String, String>();
/* 1246 */         node.put("id", scci.getSubCompanyid());
/* 1247 */         node.put("pId", supsubcomid);
/* 1248 */         node.put("name", scci.getSubCompanyname());
/* 1249 */         data.add(node);
/*      */       } 
/* 1251 */     } catch (Exception e) {
/* 1252 */       writeLog(e);
/*      */     } 
/*      */     
/* 1255 */     result.put("data", data);
/*      */     
/* 1257 */     return result;
/*      */   }
/*      */   
/*      */   public Map<String, Object> getBlackWorkFlow(User user) {
/* 1261 */     Map<String, Object> result = new HashMap<String, Object>();
/*      */     
/*      */     try {
/* 1264 */       List<String> wflist = new ArrayList<String>();
/* 1265 */       RecordSet rs = new RecordSet();
/*      */       
/* 1267 */       rs.executeSql("select workflowid from workflowBlacklist where userid=" + user.getUID());
/* 1268 */       while (rs.next()) {
/* 1269 */         wflist.add(rs.getString("workflowid"));
/*      */       }
/*      */       
/* 1272 */       result.put("data", wflist);
/* 1273 */     } catch (Exception e) {
/* 1274 */       writeLog(e);
/* 1275 */       result.put("error", e.getMessage());
/*      */     } 
/*      */     
/* 1278 */     return result;
/*      */   }
/*      */   
/*      */   public Map<String, Object> setBlackWorkFlow(User user, String workflows) {
/* 1282 */     Map<String, Object> result = new HashMap<String, Object>();
/*      */     
/*      */     try {
/* 1285 */       RecordSet recordSet = new RecordSet();
/*      */       
/* 1287 */       recordSet.executeSql("delete from workflowBlacklist where userid=" + user.getUID());
/*      */       
/* 1289 */       String[] selwf = StringUtils.splitByWholeSeparator(workflows, ",");
/* 1290 */       if (selwf != null && selwf.length > 0) {
/* 1291 */         List<String> paraList = new ArrayList<String>(); byte b; int i;
/*      */         String[] arrayOfString;
/* 1293 */         for (i = (arrayOfString = selwf).length, b = 0; b < i; ) { String wf = arrayOfString[b];
/* 1294 */           paraList.add(user.getUID() + Util.getSeparator() + wf);
/*      */           b++; }
/*      */         
/* 1297 */         BatchRecordSet brs = new BatchRecordSet();
/* 1298 */         brs.executeSqlBatch("insert into workflowBlacklist(userid,workflowid) values(?,?)", paraList);
/*      */       } 
/*      */       
/* 1301 */       result.put("result", "1");
/* 1302 */     } catch (Exception e) {
/* 1303 */       writeLog(e);
/* 1304 */       result.put("error", e.getMessage());
/*      */     } 
/*      */     
/* 1307 */     return result;
/*      */   }
/*      */   
/*      */   public Map<String, Object> getHideModule(User user) {
/* 1311 */     Map<String, Object> result = new HashMap<String, Object>();
/*      */     
/*      */     try {
/* 1314 */       List<String> wflist = new ArrayList<String>();
/* 1315 */       RecordSet rs = new RecordSet();
/*      */       
/* 1317 */       rs.executeSql("select moduleid from mobileHideModule where userid=" + user.getUID());
/* 1318 */       while (rs.next()) {
/* 1319 */         wflist.add(rs.getString("moduleid"));
/*      */       }
/*      */       
/* 1322 */       result.put("data", wflist);
/* 1323 */     } catch (Exception e) {
/* 1324 */       writeLog(e);
/* 1325 */       result.put("error", e.getMessage());
/*      */     } 
/* 1327 */     return result;
/*      */   }
/*      */   
/*      */   public Map<String, Object> setHideModule(User user, String hidemodule) {
/* 1331 */     Map<String, Object> result = new HashMap<String, Object>();
/*      */     
/*      */     try {
/* 1334 */       RecordSet recordSet = new RecordSet();
/*      */       
/* 1336 */       recordSet.executeSql("delete from mobileHideModule where userid=" + user.getUID());
/*      */       
/* 1338 */       String[] modules = StringUtils.splitByWholeSeparator(hidemodule, ",");
/* 1339 */       if (modules != null && modules.length > 0) {
/* 1340 */         byte b; int i; String[] arrayOfString; for (i = (arrayOfString = modules).length, b = 0; b < i; ) { String module = arrayOfString[b];
/* 1341 */           recordSet.executeSql("insert into workflowBlacklist(userid, moduleid) values(" + user.getUID() + ", " + module + ")");
/*      */           b++; }
/*      */       
/*      */       } 
/* 1345 */       result.put("result", "1");
/* 1346 */     } catch (Exception e) {
/* 1347 */       writeLog(e);
/* 1348 */       result.put("error", e.getMessage());
/*      */     } 
/*      */     
/* 1351 */     return result;
/*      */   }
/*      */   
/*      */   public boolean ifEqlTarget(String val, String target) {
/* 1355 */     if (val == null || val.equals("")) {
/* 1356 */       return false;
/*      */     }
/* 1358 */     if (!val.equals(target)) {
/* 1359 */       return false;
/*      */     }
/* 1361 */     return true;
/*      */   }
/*      */ 
/*      */   
/*      */   public String getLanguageType() {
/* 1366 */     int lang = Util.getIntValue(Prop.getPropValue("wx_hrm_language", "type"), 7);
/* 1367 */     return (lang == 7) ? "7" : "8";
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/wxinterface/HrmResourceService.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */