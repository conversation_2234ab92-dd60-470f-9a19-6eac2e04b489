/*    */ package weaver.wxinterface;
/*    */ 
/*    */ import java.util.regex.Matcher;
/*    */ import java.util.regex.Pattern;
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.resource.ResourceComInfo;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class FormatMultiLang
/*    */ {
/*    */   private static final String LANG_CONTENT_PREFIX = "~`~`";
/*    */   private static final String LANG_CONTENT_SUFFIX = "`~`~";
/*    */   private static final String LANG_CONTENT_SPLITTER1 = "`~`";
/*    */   private static final String langRegP = "(?<!value {0,3}= {0,3}('|\"))(~`~`(([\\d &nbsp;]{1,2}[^`]*?`~`)+[\\d &nbsp;]{1,2}[^`]*?)`~`~).*?";
/*    */   private static ResourceComInfo rc;
/*    */   
/*    */   static {
/*    */     try {
/* 24 */       rc = new ResourceComInfo();
/* 25 */     } catch (Exception exception) {}
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public static String formatByUserid(String s, String userid) {
/* 31 */     String lang = rc.getSystemLanguage(userid);
/* 32 */     return format(s, lang);
/*    */   }
/*    */   
/*    */   public static String format(String s, String lang) {
/* 36 */     lang = Util.null2String(lang);
/* 37 */     if (s == null || "".equals(s) || s.indexOf("~`~`") == -1)
/* 38 */       return s; 
/* 39 */     Pattern p = Pattern.compile("(?<!value {0,3}= {0,3}('|\"))(~`~`(([\\d &nbsp;]{1,2}[^`]*?`~`)+[\\d &nbsp;]{1,2}[^`]*?)`~`~).*?", 2);
/* 40 */     Matcher m = p.matcher(s);
/* 41 */     StringBuffer sb = new StringBuffer();
/* 42 */     boolean result = m.find();
/* 43 */     if (lang.equals("")) lang = "7"; 
/* 44 */     while (result) {
/* 45 */       String var = m.group(2);
/* 46 */       String value = transLang(var, lang);
/* 47 */       String sb2 = value;
/* 48 */       value = value.replace("$", "\\$");
/*    */       try {
/* 50 */         m.appendReplacement(sb, value);
/* 51 */       } catch (Exception e) {
/* 52 */         (new BaseBean()).writeLog("Exception Error===" + e.getMessage() + " sb2==" + sb2 + "  value===" + value);
/*    */       } 
/* 54 */       result = m.find();
/*    */     } 
/* 56 */     m.appendTail(sb);
/* 57 */     return sb.toString();
/*    */   }
/*    */   
/*    */   public static String transLang(String var, String lang) {
/* 61 */     String oriLang = lang;
/* 62 */     if (lang.length() < 2) lang = String.valueOf(lang) + " "; 
/* 63 */     var = var.replaceAll("&nbsp;", " ");
/* 64 */     int from = var.indexOf("~`~`");
/* 65 */     int to = var.lastIndexOf("`~`~");
/* 66 */     if (from != -1 && to != -1 && from + 4 < to) {
/* 67 */       String[] ls = var.substring(from + 4, to).split("`~`"); byte b; int i; String[] arrayOfString1;
/* 68 */       for (i = (arrayOfString1 = ls).length, b = 0; b < i; ) { String lg = arrayOfString1[b];
/* 69 */         if (lg.startsWith(lang))
/* 70 */           return lg.substring(lang.length()); 
/* 71 */         if (lg.startsWith(oriLang))
/* 72 */           return lg.substring(oriLang.length()); 
/*    */         b++; }
/*    */     
/*    */     } 
/* 76 */     return var;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/wxinterface/FormatMultiLang.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */