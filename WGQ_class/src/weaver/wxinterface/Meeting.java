/*     */ package weaver.wxinterface;
/*     */ 
/*     */ import java.text.ParseException;
/*     */ import java.text.SimpleDateFormat;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class Meeting
/*     */   implements Comparable<Meeting>
/*     */ {
/*     */   private int mid;
/*     */   private String name;
/*     */   private String sDate;
/*     */   private String sTime;
/*     */   private String eDate;
/*     */   private String eTime;
/*     */   private int ifConflict;
/*     */   
/*     */   public int compareTo(Meeting o) {
/*  24 */     SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
/*     */     try {
/*  26 */       long sTimes = sdf.parse(String.valueOf(this.sDate) + " " + this.sTime).getTime();
/*  27 */       long eTimes = sdf.parse(String.valueOf(this.eDate) + " " + this.eTime).getTime();
/*  28 */       long time1 = eTimes - sTimes;
/*     */       
/*  30 */       long osTimes = sdf.parse(String.valueOf(o.getsDate()) + " " + o.getsTime()).getTime();
/*  31 */       long oeTimes = sdf.parse(String.valueOf(o.geteDate()) + " " + o.geteTime()).getTime();
/*  32 */       long time2 = oeTimes - osTimes;
/*     */       
/*  34 */       return (time2 > time1) ? 1 : -1;
/*  35 */     } catch (ParseException e) {
/*  36 */       e.printStackTrace();
/*     */       
/*  38 */       return 0;
/*     */     } 
/*     */   }
/*     */   public Meeting(int mid, String name, String sDate, String sTime, String eDate, String eTime, int ifConflict) {
/*  42 */     this.mid = mid;
/*  43 */     this.name = name;
/*  44 */     this.sDate = sDate;
/*  45 */     this.sTime = sTime;
/*  46 */     this.eDate = eDate;
/*  47 */     this.eTime = eTime;
/*  48 */     this.ifConflict = ifConflict;
/*     */   }
/*     */   
/*     */   public int getMid() {
/*  52 */     return this.mid;
/*     */   }
/*     */   
/*     */   public void setMid(int mid) {
/*  56 */     this.mid = mid;
/*     */   }
/*     */   
/*     */   public String getName() {
/*  60 */     return this.name;
/*     */   }
/*     */   
/*     */   public void setName(String name) {
/*  64 */     this.name = name;
/*     */   }
/*     */   
/*     */   public String getsDate() {
/*  68 */     return this.sDate;
/*     */   }
/*     */   
/*     */   public void setsDate(String sDate) {
/*  72 */     this.sDate = sDate;
/*     */   }
/*     */   
/*     */   public String getsTime() {
/*  76 */     return this.sTime;
/*     */   }
/*     */   
/*     */   public void setsTime(String sTime) {
/*  80 */     this.sTime = sTime;
/*     */   }
/*     */   
/*     */   public String geteDate() {
/*  84 */     return this.eDate;
/*     */   }
/*     */   
/*     */   public void seteDate(String eDate) {
/*  88 */     this.eDate = eDate;
/*     */   }
/*     */   
/*     */   public String geteTime() {
/*  92 */     return this.eTime;
/*     */   }
/*     */   
/*     */   public void seteTime(String eTime) {
/*  96 */     this.eTime = eTime;
/*     */   }
/*     */   
/*     */   public int getIfConflict() {
/* 100 */     return this.ifConflict;
/*     */   }
/*     */   
/*     */   public void setIfConflict(int ifConflict) {
/* 104 */     this.ifConflict = ifConflict;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/wxinterface/Meeting.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */