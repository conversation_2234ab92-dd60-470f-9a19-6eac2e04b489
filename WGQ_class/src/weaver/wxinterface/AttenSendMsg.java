/*     */ package weaver.wxinterface;
/*     */ 
/*     */ import java.text.SimpleDateFormat;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Date;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.report.schedulediff.HrmScheduleDiffUtil;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class AttenSendMsg
/*     */   implements Runnable
/*     */ {
/*     */   private SimpleDateFormat sdf;
/*     */   private BaseBean bb;
/*     */   private FlowAndDoc fad;
/*     */   private HrmScheduleDiffUtil hsd;
/*     */   private String nowDate;
/*     */   
/*     */   public AttenSendMsg(FlowAndDoc fad) {
/*  29 */     this.fad = fad;
/*  30 */     this.bb = new BaseBean();
/*  31 */     this.sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
/*  32 */     this.hsd = new HrmScheduleDiffUtil();
/*  33 */     this.nowDate = TimeUtil.getCurrentDateString();
/*     */   }
/*     */ 
/*     */   
/*     */   public void run() {
/*  38 */     attSend();
/*     */   }
/*     */   
/*     */   private FlowAndDoc attSend() {
/*     */     try {
/*  43 */       if (this.hsd.getIsWorkday(this.nowDate)) {
/*  44 */         RecordSet rs = new RecordSet();
/*  45 */         String currentDate = TimeUtil.getCurrentDateString();
/*  46 */         long ts = System.currentTimeMillis();
/*  47 */         long stime = 0L;
/*  48 */         long etime = 0L;
/*  49 */         if (!"".equals(this.fad.getStartdate())) {
/*  50 */           Date sd = this.sdf.parse(String.valueOf(currentDate) + " " + this.fad.getStartdate());
/*  51 */           stime = sd.getTime();
/*     */         } 
/*  53 */         if (!"".equals(this.fad.getEnddate())) {
/*  54 */           Date ed = this.sdf.parse(String.valueOf(currentDate) + " " + this.fad.getEnddate());
/*  55 */           etime = ed.getTime();
/*     */         } 
/*  57 */         long t1 = ts - stime;
/*  58 */         if (stime != 0L && t1 > 0L && t1 < 300000L) {
/*  59 */           boolean flag = rs.executeSql("select 1 from wx_scanlog where logdate='" + currentDate + "' and reourceid = " + this.fad.getId() + " and status = 1");
/*  60 */           if (flag && rs.getCounts() == 0) {
/*  61 */             sendMsg(this.fad, 1, currentDate);
/*     */           }
/*     */         } 
/*  64 */         long t2 = ts - etime;
/*  65 */         if (etime != 0L && t2 > 0L && t2 < 300000L) {
/*  66 */           boolean flag = rs.executeSql("select 1 from wx_scanlog where logdate='" + currentDate + "' and reourceid = '" + this.fad.getId() + "' and status = 2");
/*  67 */           if (flag && rs.getCounts() == 0) {
/*  68 */             sendMsg(this.fad, 2, currentDate);
/*     */           }
/*     */         } 
/*     */       } 
/*  72 */     } catch (Exception e) {
/*  73 */       this.bb.writeLog("定时推送考勤消息程序异常", e);
/*  74 */       e.printStackTrace();
/*     */     } 
/*  76 */     return this.fad;
/*     */   }
/*     */   
/*     */   private static void sendMsg(FlowAndDoc fad, int status, String currentDate) {
/*  80 */     RecordSet rs = new RecordSet();
/*     */     try {
/*  82 */       int resourcetype = Util.getIntValue(fad.getResourcetype(), 0);
/*  83 */       String ids = fad.getResourceids();
/*  84 */       if (ids.startsWith(",")) {
/*  85 */         ids = ids.substring(1);
/*     */       }
/*  87 */       if (ids.endsWith(",")) {
/*  88 */         ids = ids.substring(0, ids.length() - 1);
/*     */       }
/*  90 */       StringBuffer sql = new StringBuffer();
/*  91 */       sql.append("select t1.id from HrmResource t1 where t1.status in (0,1,2,3) and t1.loginid is not null");
/*  92 */       if (resourcetype == 1) {
/*  93 */         sql.append(" and t1.subcompanyid1 in (" + ids + ")");
/*  94 */       } else if (resourcetype == 2) {
/*  95 */         sql.append(" and t1.departmentid in (" + ids + ")");
/*     */       } 
/*  97 */       sql.append(" and not exists (select 1 from HrmScheduleSign h where h.userid = t1.id and h.signDate='" + currentDate + "' and h.signType =" + status + ")");
/*     */       
/*  99 */       rs.executeSql(sql.toString());
/* 100 */       List<String> userList = new ArrayList<String>();
/* 101 */       while (rs.next()) {
/* 102 */         String id = Util.null2String(rs.getString("id"));
/* 103 */         userList.add(id);
/*     */       } 
/* 105 */       String content = "";
/* 106 */       if (status == 1) {
/* 107 */         content = fad.getStartcentext();
/*     */       } else {
/* 109 */         content = fad.getEndcentext();
/*     */       } 
/* 111 */       if (userList.size() > 0) {
/* 112 */         Map<String, Object> map = new HashMap<String, Object>();
/* 113 */         map.put("logdate", currentDate);
/* 114 */         map.put("attendStatus", (new StringBuilder(String.valueOf(status))).toString());
/* 115 */         map.put("ifsendsub", Integer.valueOf(fad.getIfsendsub()));
/* 116 */         InterfaceUtil.sendMsg(userList, fad.getMsgtpids(), fad.getId(), content, 20, map);
/*     */       } 
/* 118 */     } catch (Exception exception) {}
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/wxinterface/AttenSendMsg.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */