/*    */ package weaver.wxinterface;
/*    */ 
/*    */ import java.util.TimerTask;
/*    */ 
/*    */ public class ScanOtherAndSend
/*    */   extends TimerTask {
/*    */   private FlowAndDoc fad;
/*    */   
/*    */   public ScanOtherAndSend(FlowAndDoc fad) {
/* 10 */     this.fad = fad;
/*    */   }
/*    */   
/*    */   public void run() {}
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/wxinterface/ScanOtherAndSend.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */