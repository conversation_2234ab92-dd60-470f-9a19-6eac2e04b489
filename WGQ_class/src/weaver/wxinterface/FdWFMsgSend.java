/*    */ package weaver.wxinterface;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.List;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.file.Prop;
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.general.Util;
/*    */ import weaver.mobile.webservices.workflow.WorkflowServiceImpl;
/*    */ 
/*    */ public class FdWFMsgSend
/*    */   implements Runnable
/*    */ {
/* 14 */   private BaseBean bb = null;
/*    */   
/*    */   public FdWFMsgSend() {
/* 17 */     this.bb = new BaseBean();
/*    */   }
/*    */ 
/*    */   
/*    */   public void run() {
/*    */     try {
/* 23 */       this.bb.writeLog("开始执行复地流程推送=====" + System.currentTimeMillis());
/* 24 */       WorkflowServiceImpl ws = new WorkflowServiceImpl();
/* 25 */       RecordSet rs = new RecordSet();
/* 26 */       rs.executeSql("select * from FdWFMsgSet where settype = 3");
/* 27 */       while (rs.next()) {
/* 28 */         int userid = Util.getIntValue(rs.getString("userid"));
/* 29 */         int wfnum = ws.getToDoWorkflowRequestCount(userid, false, null, true);
/* 30 */         String content = "您有" + wfnum + "条待办需要处理";
/* 31 */         List<String> userList = new ArrayList<String>();
/* 32 */         userList.add((new StringBuilder(String.valueOf(userid))).toString());
/* 33 */         String tpid = Util.null2String(Prop.getPropValue("fdcsdev", "tpid"));
/* 34 */         InterfaceUtil.sendMsg(userList, tpid, "0", content, 16);
/*    */       } 
/* 36 */     } catch (Exception e) {
/* 37 */       e.printStackTrace();
/* 38 */       this.bb.writeLog("复地流程推送程序出现异常---" + e.getMessage());
/*    */     } 
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/wxinterface/FdWFMsgSend.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */