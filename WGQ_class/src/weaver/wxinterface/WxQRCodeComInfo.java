/*     */ package weaver.wxinterface;
/*     */ 
/*     */ import java.io.ByteArrayInputStream;
/*     */ import java.io.ByteArrayOutputStream;
/*     */ import java.io.IOException;
/*     */ import java.io.InputStream;
/*     */ import java.io.ObjectInputStream;
/*     */ import java.io.ObjectOutputStream;
/*     */ import java.sql.SQLException;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Hashtable;
/*     */ import weaver.cluster.CacheMessage;
/*     */ import weaver.conn.ConnStatement;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.StaticObj;
/*     */ import weaver.hrm.User;
/*     */ 
/*     */ public class WxQRCodeComInfo
/*     */   extends BaseBean
/*     */ {
/*  22 */   private int current_index = -1;
/*  23 */   private int array_size = 0;
/*  24 */   private static Object lock = new Object();
/*     */   
/*  26 */   private StaticObj staticobj = null;
/*  27 */   private ArrayList keyList = null;
/*  28 */   private ArrayList userList = null;
/*     */ 
/*     */ 
/*     */   
/*     */   public WxQRCodeComInfo() {
/*  33 */     this.staticobj = StaticObj.getInstance();
/*  34 */     getQRCodeComInfo();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void insertUserToDb(String loginkey, User testUser) {
/*  41 */     if (WxModuleInit.ifNewQRCodeTable) {
/*  42 */       RecordSet rs = new RecordSet();
/*  43 */       String sql = "insert into QRCodeComInfo (loginid,loginkey) values('" + testUser.getLoginid() + "','" + loginkey + "')";
/*  44 */       rs.execute(sql);
/*     */     } else {
/*  46 */       ConnStatement statement = new ConnStatement();
/*     */       
/*     */       try {
/*  49 */         ByteArrayOutputStream bout = new ByteArrayOutputStream();
/*  50 */         String strSqlIn = "insert into WX_QRCodeComInfo (userobject,loginkey) values(?,?)";
/*     */         
/*  52 */         ObjectOutputStream out = new ObjectOutputStream(bout);
/*  53 */         out.writeObject(testUser);
/*     */         
/*  55 */         ByteArrayInputStream in = new ByteArrayInputStream(bout.toByteArray());
/*     */         
/*  57 */         statement.setStatementSql(strSqlIn);
/*  58 */         statement.setBinaryStream(1, in, in.available());
/*  59 */         statement.setString(2, loginkey);
/*  60 */         statement.executeUpdate();
/*  61 */       } catch (IOException e) {
/*  62 */         e.printStackTrace();
/*  63 */       } catch (SQLException e) {
/*  64 */         e.printStackTrace();
/*  65 */       } catch (Exception e) {
/*  66 */         e.printStackTrace();
/*     */       } finally {
/*  68 */         statement.close();
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public User getUserFromDb(String loginkey) {
/*  76 */     ConnStatement statement = new ConnStatement();
/*  77 */     User newUser = null;
/*  78 */     String strSqlIn = "select userobject from WX_QRCodeComInfo where loginkey=?";
/*     */     try {
/*  80 */       statement.setStatementSql(strSqlIn);
/*     */       
/*  82 */       statement.setString(1, loginkey);
/*  83 */       statement.executeQuery();
/*  84 */       if (statement.next()) {
/*  85 */         ObjectInputStream oin = null;
/*     */         
/*  87 */         InputStream ins = statement.getBinaryStream("userobject");
/*  88 */         oin = new ObjectInputStream(ins);
/*  89 */         newUser = (User)oin.readObject();
/*     */       } 
/*  91 */     } catch (SQLException e) {
/*  92 */       e.printStackTrace();
/*  93 */     } catch (IOException e) {
/*  94 */       e.printStackTrace();
/*  95 */     } catch (ClassNotFoundException e) {
/*  96 */       e.printStackTrace();
/*  97 */     } catch (Exception e) {
/*  98 */       e.printStackTrace();
/*     */     } finally {
/* 100 */       statement.close();
/*     */     } 
/* 102 */     return newUser;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void delUserFromDb(String loginkey) {
/* 108 */     ConnStatement statement = new ConnStatement();
/* 109 */     String strSqlIn = "delete from WX_QRCodeComInfo where loginkey=?";
/*     */     try {
/* 111 */       statement.setStatementSql(strSqlIn);
/* 112 */       statement.setString(1, loginkey);
/* 113 */       statement.executeUpdate();
/* 114 */     } catch (SQLException e) {
/* 115 */       e.printStackTrace();
/* 116 */     } catch (IOException e) {
/* 117 */       e.printStackTrace();
/* 118 */     } catch (ClassNotFoundException e) {
/* 119 */       e.printStackTrace();
/* 120 */     } catch (Exception e) {
/* 121 */       e.printStackTrace();
/*     */     } finally {
/* 123 */       statement.close();
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void getQRCodeComInfo() {
/* 131 */     synchronized (lock) {
/* 132 */       if (this.staticobj.getObject("QRCodeComInfo") != null) {
/* 133 */         this.keyList = (ArrayList)this.staticobj.getRecordFromObj("QRCodeComInfo", "keyList");
/* 134 */         this.userList = (ArrayList)this.staticobj.getRecordFromObj("QRCodeComInfo", "userList");
/*     */       } else {
/* 136 */         this.keyList = new ArrayList();
/* 137 */         this.userList = new ArrayList();
/*     */       } 
/*     */     } 
/*     */   }
/*     */   
/*     */   public void addQRCodeComInfo(String key, Object user) {
/* 143 */     this.keyList.add(key);
/* 144 */     this.userList.add(user);
/*     */     
/* 146 */     this.staticobj.putRecordToObj("QRCodeComInfo", "keyList", this.keyList);
/* 147 */     this.staticobj.putRecordToObj("QRCodeComInfo", "userList", this.userList);
/*     */     
/* 149 */     if (this.staticobj.isCluster()) {
/* 150 */       CacheMessage msg = new CacheMessage();
/* 151 */       Hashtable<Object, Object> ht = new Hashtable<Object, Object>();
/* 152 */       ht.put("keyList", key);
/* 153 */       ht.put("userList", user);
/*     */       
/* 155 */       msg.setAction("add");
/*     */       
/* 157 */       msg.setCacheType("QRCodeComInfo");
/* 158 */       msg.setRowKey("keyList");
/* 159 */       msg.setRow(ht);
/* 160 */       this.staticobj.sendNotification(msg);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void removeQRCodeComInfo(String key) {
/* 167 */     int index = this.keyList.indexOf(key);
/* 168 */     if (index != -1) {
/* 169 */       this.keyList.remove(index);
/* 170 */       this.userList.remove(index);
/*     */       
/* 172 */       if (this.staticobj.isCluster()) {
/* 173 */         CacheMessage msg = new CacheMessage();
/* 174 */         Hashtable<Object, Object> ht = new Hashtable<Object, Object>();
/* 175 */         ht.put("keyList", key);
/* 176 */         msg.setAction("delete");
/* 177 */         msg.setCacheType("QRCodeComInfo");
/* 178 */         msg.setRowKey("keyList");
/* 179 */         msg.setRow(ht);
/* 180 */         this.staticobj.sendNotification(msg);
/*     */       } 
/*     */     } 
/* 183 */     this.array_size = this.keyList.size();
/*     */   }
/*     */   
/*     */   public Object getUser(String key) {
/* 187 */     int index = this.keyList.indexOf(key);
/* 188 */     if (index >= 0) {
/* 189 */       return this.userList.get(index);
/*     */     }
/* 191 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public String getKey() {
/* 197 */     return this.keyList.get(this.current_index);
/*     */   }
/*     */   
/*     */   public Object getUser() {
/* 201 */     return this.userList.get(this.current_index);
/*     */   }
/*     */   public int getAssetNum() {
/* 204 */     return this.array_size;
/*     */   }
/*     */   
/*     */   public void setTofirstRow() {
/* 208 */     this.current_index = -1;
/*     */   }
/*     */   
/*     */   public boolean next() {
/* 212 */     if (this.current_index + 1 < this.array_size) {
/* 213 */       this.current_index++;
/* 214 */       return true;
/*     */     } 
/* 216 */     this.current_index = -1;
/* 217 */     return false;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/wxinterface/WxQRCodeComInfo.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */