/*    */ package weaver.wxinterface;
/*    */ 
/*    */ import weaver.file.Prop;
/*    */ import weaver.general.Util;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public final class HttpConnectionManagerConstants
/*    */ {
/* 13 */   public static final int DEFAULT_MAX_PER_ROUTE = Util.getIntValue(Prop.getPropValue("wxinterface", "default_max_per_route"), 500);
/* 14 */   public static final int MAX_TOTAL_CONNECTIONS = Util.getIntValue(Prop.getPropValue("wxinterface", "max_total_connections"), 500);
/*    */ 
/*    */   
/*    */   public static boolean isUserConnectionManager() {
/* 18 */     return (Util.getIntValue(Prop.getPropValue("wxinterface", "use_connection_manager"), 1) == 1);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/wxinterface/HttpConnectionManagerConstants.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */