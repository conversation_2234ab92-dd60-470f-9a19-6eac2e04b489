/*     */ package weaver.wxinterface;
/*     */ 
/*     */ import com.weaver.general.TimeUtil;
/*     */ import com.weaver.general.Util;
/*     */ import java.text.SimpleDateFormat;
/*     */ import java.util.Calendar;
/*     */ import weaver.file.Prop;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.hrm.User;
/*     */ import weaver.workflow.webservices.WorkflowBaseInfo;
/*     */ import weaver.workflow.webservices.WorkflowMainTableInfo;
/*     */ import weaver.workflow.webservices.WorkflowRequestInfo;
/*     */ import weaver.workflow.webservices.WorkflowRequestTableField;
/*     */ import weaver.workflow.webservices.WorkflowRequestTableRecord;
/*     */ import weaver.workflow.webservices.WorkflowServiceImpl;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CsjtSignUtil
/*     */ {
/*     */   public static void doCreatWF(User user, int signType, String addr, String comment) {
/*  23 */     BaseBean bb = new BaseBean();
/*     */     
/*     */     try {
/*  26 */       WorkflowRequestTableField[] wrti = new WorkflowRequestTableField[7];
/*  27 */       wrti[0] = new WorkflowRequestTableField();
/*  28 */       wrti[0].setFieldName("sqr");
/*  29 */       wrti[0].setFieldValue((new StringBuilder(String.valueOf(user.getUID()))).toString());
/*  30 */       wrti[0].setView(true);
/*  31 */       wrti[0].setEdit(true);
/*     */       
/*  33 */       wrti[1] = new WorkflowRequestTableField();
/*  34 */       wrti[1].setFieldName("sqrq");
/*  35 */       wrti[1].setFieldValue(TimeUtil.getCurrentDateString());
/*  36 */       wrti[1].setView(true);
/*  37 */       wrti[1].setEdit(true);
/*     */       
/*  39 */       wrti[2] = new WorkflowRequestTableField();
/*  40 */       wrti[2].setFieldName("sqsj");
/*  41 */       SimpleDateFormat SDF = new SimpleDateFormat("HH:mm");
/*  42 */       Calendar calendar = Calendar.getInstance();
/*  43 */       wrti[2].setFieldValue(SDF.format(calendar.getTime()));
/*  44 */       wrti[2].setView(true);
/*  45 */       wrti[2].setEdit(true);
/*     */       
/*  47 */       wrti[3] = new WorkflowRequestTableField();
/*  48 */       wrti[3].setFieldName("ssgs");
/*  49 */       wrti[3].setFieldValue((new StringBuilder(String.valueOf(user.getUserSubCompany1()))).toString());
/*  50 */       wrti[3].setView(true);
/*  51 */       wrti[3].setEdit(true);
/*     */       
/*  53 */       wrti[4] = new WorkflowRequestTableField();
/*  54 */       wrti[4].setFieldName("bm");
/*  55 */       wrti[4].setFieldValue((new StringBuilder(String.valueOf(user.getUserDepartment()))).toString());
/*  56 */       wrti[4].setView(true);
/*  57 */       wrti[4].setEdit(true);
/*     */       
/*  59 */       wrti[5] = new WorkflowRequestTableField();
/*  60 */       wrti[5].setFieldName("qdwz");
/*  61 */       wrti[5].setFieldValue(addr);
/*  62 */       wrti[5].setView(true);
/*  63 */       wrti[5].setEdit(true);
/*     */       
/*  65 */       wrti[6] = new WorkflowRequestTableField();
/*  66 */       wrti[6].setFieldName("yysm");
/*  67 */       wrti[6].setFieldValue(comment);
/*  68 */       wrti[6].setView(true);
/*  69 */       wrti[6].setEdit(true);
/*     */       
/*  71 */       WorkflowRequestTableRecord[] wrtri = new WorkflowRequestTableRecord[1];
/*  72 */       wrtri[0] = new WorkflowRequestTableRecord();
/*  73 */       wrtri[0].setWorkflowRequestTableFields(wrti);
/*     */       
/*  75 */       WorkflowMainTableInfo wmi = new WorkflowMainTableInfo();
/*  76 */       wmi.setRequestRecords(wrtri);
/*     */ 
/*     */       
/*  79 */       WorkflowBaseInfo wbi = new WorkflowBaseInfo();
/*     */       
/*  81 */       String workflowid = Util.null2String(Prop.getPropValue("csjtcsdev", "workflowid"));
/*  82 */       wbi.setWorkflowId(workflowid);
/*     */       
/*  84 */       WorkflowRequestInfo wri = new WorkflowRequestInfo();
/*  85 */       wri.setCreatorId((new StringBuilder(String.valueOf(user.getUID()))).toString());
/*  86 */       wri.setRequestLevel("0");
/*  87 */       String requestName = "考勤申请流程(";
/*  88 */       if (signType == 1) {
/*  89 */         requestName = String.valueOf(requestName) + "签到";
/*     */       } else {
/*  91 */         requestName = String.valueOf(requestName) + "签退";
/*     */       } 
/*  93 */       requestName = String.valueOf(requestName) + ")-" + user.getLastname() + "-" + TimeUtil.getCurrentDateString();
/*  94 */       wri.setRequestName(requestName);
/*  95 */       wri.setWorkflowMainTableInfo(wmi);
/*  96 */       wri.setWorkflowBaseInfo(wbi);
/*     */ 
/*     */       
/*  99 */       WorkflowServiceImpl workflowServiceImpl = new WorkflowServiceImpl();
/* 100 */       String requestid = workflowServiceImpl.doCreateWorkflowRequest(wri, user.getUID());
/* 101 */       log(bb, user, "触发考勤申请流程成功,生成的requestid====" + requestid);
/* 102 */     } catch (Exception e) {
/* 103 */       e.printStackTrace();
/* 104 */       bb.writeLog("触发考勤申请流程失败======" + e.getMessage());
/*     */     } 
/*     */   }
/*     */   
/*     */   public static void log(BaseBean bb, User user, String log) {
/* 109 */     bb.writeLog("\n\n用户ID:" + user.getUID() + ",姓名:" + user.getLastname() + ",时间:" + 
/* 110 */         TimeUtil.getCurrentTimeString() + ",触发考勤申请流程日志记录\n\n" + log);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/wxinterface/CsjtSignUtil.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */