/*    */ package weaver.wxinterface;
/*    */ 
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.Util;
/*    */ import weaver.hrm.User;
/*    */ import weaver.workflow.request.WFUrgerManager;
/*    */ 
/*    */ public class DocRightCheckForWorkFlow
/*    */ {
/*    */   public static boolean checkDocRight(User user, int docid, int requestid) {
/* 11 */     boolean canReader = false;
/*    */     try {
/* 13 */       WFUrgerManager wfu = new WFUrgerManager();
/* 14 */       canReader = wfu.OperHaveDocViewRight(requestid, user.getUID(), Util.getIntValue(user.getLogintype(), 1), docid);
/*    */       
/* 16 */       if (!canReader) {
/* 17 */         RecordSet rs = new RecordSet();
/* 18 */         rs.executeQuery("SELECT 1 FROM workflow_requestbase WHERE requestid=" + requestid + " and creater=" + user.getUID(), new Object[0]);
/* 19 */         if (rs.next()) {
/* 20 */           canReader = true;
/*    */         }
/*    */       } 
/* 23 */     } catch (Exception exception) {}
/*    */ 
/*    */     
/* 26 */     return canReader;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/wxinterface/DocRightCheckForWorkFlow.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */