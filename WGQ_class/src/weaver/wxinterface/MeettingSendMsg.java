/*     */ package weaver.wxinterface;
/*     */ 
/*     */ import java.text.SimpleDateFormat;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.file.Prop;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.meeting.Maint.MeetingRoomComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ public class MeettingSendMsg
/*     */   implements Runnable
/*     */ {
/*     */   private SimpleDateFormat sdf;
/*     */   private BaseBean bb;
/*     */   private FlowAndDoc fad;
/*     */   
/*     */   public MeettingSendMsg(FlowAndDoc fad) {
/*  24 */     this.fad = fad;
/*  25 */     this.bb = new BaseBean();
/*  26 */     this.sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
/*     */   }
/*     */ 
/*     */   
/*     */   public void run() {
/*  31 */     scanAndSend();
/*     */   }
/*     */   
/*     */   private FlowAndDoc scanAndSend() {
/*     */     try {
/*  36 */       String lastscantime = this.fad.getLastscantime();
/*  37 */       String nowDate = this.sdf.format(Long.valueOf(System.currentTimeMillis()));
/*  38 */       if (lastscantime == null || lastscantime.equals("")) {
/*  39 */         lastscantime = nowDate;
/*     */       }
/*  41 */       RecordSet rs = new RecordSet();
/*  42 */       RecordSet rs2 = new RecordSet();
/*     */       
/*  44 */       this.fad.setLastscantime(nowDate);
/*  45 */       rs.execute("update WX_MsgRuleSetting set lastscantime='" + nowDate + "' where id =" + this.fad.getId());
/*     */       
/*  47 */       String createdate = "";
/*  48 */       String begindate = "";
/*  49 */       String enddate = "";
/*  50 */       if ("sqlserver".equalsIgnoreCase(rs.getDBType())) {
/*  51 */         createdate = "(m.createdate+' '+m.createtime)";
/*  52 */         begindate = "(m.begindate+' '+m.begintime)";
/*  53 */         enddate = "(m.enddate+' '+m.endtime)";
/*  54 */       } else if ("mysql".equalsIgnoreCase(rs.getDBType())) {
/*  55 */         createdate = "(CONCAT(m.createdate,' ',m.createtime))";
/*  56 */         begindate = "(CONCAT(m.begindate,' ',m.begintime))";
/*  57 */         enddate = "(CONCAT(m.enddate,' ',m.endtime))";
/*     */       } else {
/*  59 */         createdate = "(CONCAT(CONCAT(m.createdate,' '),m.createtime))";
/*  60 */         begindate = "(CONCAT(CONCAT(m.begindate,' '),m.begintime))";
/*  61 */         enddate = "(CONCAT(CONCAT(m.enddate,' '),m.endtime))";
/*     */       } 
/*  63 */       String sql = "select m.* from Meeting m where m.meetingstatus = 2 and ";
/*  64 */       if (this.fad.getTypeids().equals("1")) {
/*  65 */         sql = String.valueOf(sql) + createdate + " > '2016-01-01 00:00:00' and " + enddate + ">='" + nowDate + "'";
/*     */       } else {
/*  67 */         int beforeTime = this.fad.getIfrepeat();
/*  68 */         String dateLimit = this.sdf.format(Long.valueOf(System.currentTimeMillis() + (beforeTime * 60 * 1000)));
/*  69 */         sql = String.valueOf(sql) + begindate + " >= '" + lastscantime + "' and " + begindate + " <= '" + dateLimit + "'";
/*     */       } 
/*  71 */       if (WxModuleInit.ifMeetingRepeat) {
/*  72 */         sql = String.valueOf(sql) + " and m.repeatType = 0";
/*     */       }
/*  74 */       sql = String.valueOf(sql) + " and not exists (select 1 from WX_SCANLOG l where  m.id = l.reourceid and l.type = 6 and l.othertypes=" + 
/*  75 */         this.fad.getTypeids() + ")";
/*  76 */       rs.execute(sql);
/*     */       
/*  78 */       MeetingRoomComInfo mrc = new MeetingRoomComInfo();
/*  79 */       int meetingtime = Util.getIntValue(Prop.getPropValue("wxinterface", "meetingtime"), 0);
/*  80 */       while (rs.next()) {
/*     */         try {
/*  82 */           int id = rs.getInt("id");
/*  83 */           String desc = "您有一个新的会议，请关注\n";
/*  84 */           if (!this.fad.getTypeids().equals("1")) {
/*  85 */             desc = "您有一个会议即将开始\n";
/*     */           }
/*  87 */           desc = String.valueOf(desc) + "会议名称：" + FormatMultiLang.format(rs.getString("name"), "7") + "\n开始时间：" + 
/*  88 */             rs.getString("begindate") + " " + rs.getString("begintime") + "\n结束时间：" + 
/*  89 */             rs.getString("enddate") + " " + rs.getString("endtime") + "\n会议地点：" + 
/*  90 */             FormatMultiLang.format(String.valueOf(mrc.getMeetingRoomInfoname(rs.getString("address"))) + " " + rs.getString("customizeAddress"), "7");
/*  91 */           if (meetingtime == 1) {
/*  92 */             desc = String.valueOf(desc) + "会议名称：" + FormatMultiLang.format(rs.getString("name"), "7") + "\n会议地点：" + 
/*  93 */               FormatMultiLang.format(String.valueOf(mrc.getMeetingRoomInfoname(rs.getString("address"))) + " " + rs.getString("customizeAddress"), "7");
/*     */           }
/*  95 */           List<String> useridList = new ArrayList<String>();
/*  96 */           String sql2 = "SELECT memberid FROM Meeting_Member2 WHERE meetingId = " + id + " AND memberType = 1";
/*     */           
/*  98 */           rs2.executeSql(sql2);
/*  99 */           while (rs2.next()) {
/* 100 */             useridList.add(rs2.getString("memberid"));
/*     */           }
/*     */           
/* 103 */           if (WxModuleInit.ifMeetingService) {
/* 104 */             rs2.executeSql("select * from Meeting_Service_New where meetingid=" + id);
/* 105 */             while (rs2.next()) {
/* 106 */               String hrmids = Util.null2String(rs2.getString("hrmids"));
/* 107 */               String[] hrmidss = hrmids.split(",");
/* 108 */               if (hrmidss != null && hrmidss.length > 0) {
/* 109 */                 byte b; int i; String[] arrayOfString; for (i = (arrayOfString = hrmidss).length, b = 0; b < i; ) { String hrmid = arrayOfString[b];
/* 110 */                   if (!"".equals(hrmid) && !useridList.contains(hrmid)) {
/* 111 */                     useridList.add(hrmid);
/*     */                   }
/*     */                   b++; }
/*     */               
/*     */               } 
/*     */             } 
/*     */           } 
/* 118 */           Map<String, Object> map = new HashMap<String, Object>();
/* 119 */           map.put("otherType", this.fad.getTypeids());
/* 120 */           map.put("ifsendsub", Integer.valueOf(this.fad.getIfsendsub()));
/* 121 */           InterfaceUtil.sendMsg(useridList, this.fad.getMsgtpids(), (new StringBuilder(String.valueOf(id))).toString(), desc, 6, map);
/* 122 */         } catch (Exception e) {
/* 123 */           this.bb.writeLog("会议：" + rs.getString("name") + "发送微信提醒失败", e);
/* 124 */           e.printStackTrace();
/*     */         }
/*     */       
/*     */       } 
/* 128 */     } catch (Exception e) {
/* 129 */       this.bb.writeLog("定时扫描会议并提醒程序异常", e);
/* 130 */       e.printStackTrace();
/*     */     } 
/* 132 */     return this.fad;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/wxinterface/MeettingSendMsg.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */