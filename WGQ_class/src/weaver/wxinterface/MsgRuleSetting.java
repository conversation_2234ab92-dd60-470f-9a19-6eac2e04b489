/*     */ package weaver.wxinterface;
/*     */ 
/*     */ import weaver.conn.ConnStatement;
/*     */ import weaver.workflow.workflow.WorkTypeComInfo;
/*     */ import weaver.workflow.workflow.WorkflowComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class MsgRuleSetting
/*     */ {
/*     */   public static String[] saveOrUpdateMsgRule(String fdid, String name, int type, int ifrepeat, int iftoall, int freqtime, String msgtpids, String msgtpnames, String types, String flows, String names, int isenable, int remindtype, int timeBefore, String remindTypeForXz, boolean isWX, int ifcover, int wfsettype, int flowtype, int ifwftodo, int ifwffinish, int ifwftimeout, int ifwfreject, String requestlevel, String startDate, String startCentext, String endDate, String endCentext, String resourceids, String resourceNames, String resourcetype, int ifsendsub, int ifbatchsend, int batchsendnum, int batchsendinterval) {
/*  15 */     int status = 1; String msg = "";
/*     */     try {
/*  17 */       if (type == 5 || type == 6) {
/*  18 */         types = (new StringBuilder(String.valueOf(remindtype))).toString();
/*  19 */         ifrepeat = timeBefore;
/*  20 */       } else if (type == 4) {
/*  21 */         types = remindTypeForXz;
/*  22 */       } else if (type == 1 && 
/*  23 */         isWX) {
/*  24 */         if (flows.equals("-1")) {
/*  25 */           names = "全部流程";
/*     */         } else {
/*  27 */           String flowids = flows;
/*  28 */           String[] flowidss = flowids.split(",");
/*  29 */           WorkflowComInfo wfci = new WorkflowComInfo();
/*  30 */           WorkTypeComInfo wftc = new WorkTypeComInfo();
/*  31 */           StringBuffer sb = new StringBuffer();
/*  32 */           String lastType = "";
/*  33 */           types = ""; byte b; int i; String[] arrayOfString1;
/*  34 */           for (i = (arrayOfString1 = flowidss).length, b = 0; b < i; ) { String f = arrayOfString1[b];
/*  35 */             if (!f.equals("")) {
/*  36 */               String workflowname = wfci.getWorkflowname(f);
/*  37 */               String workflowtype = wfci.getWorkflowtype(f);
/*  38 */               String typename = wftc.getWorkTypename(workflowtype);
/*  39 */               if (!lastType.equals(workflowtype)) {
/*  40 */                 types = String.valueOf(types) + "," + workflowtype;
/*  41 */                 if (!sb.toString().equals("")) {
/*  42 */                   sb.append("<br>");
/*     */                 }
/*  44 */                 sb.append("<span class=\"typeSpan\">" + typename + "：</span>&nbsp;");
/*  45 */                 lastType = workflowtype;
/*     */               } else {
/*  47 */                 sb.append("&nbsp;|&nbsp;");
/*     */               } 
/*  49 */               sb.append("<span class=\"flowSpan\">" + workflowname + "</span>");
/*     */             }  b++; }
/*     */           
/*  52 */           types = String.valueOf(types) + ",";
/*  53 */           names = sb.toString();
/*  54 */           if (!flows.startsWith(",")) {
/*  55 */             flows = "," + flows;
/*     */           }
/*  57 */           if (!flows.endsWith(",")) {
/*  58 */             flows = String.valueOf(flows) + ",";
/*     */           }
/*     */         } 
/*     */       } 
/*     */       
/*  63 */       if (!"".equals(name) && (!"".equals(flows) || (type != 1 && type != 2 && type != 15))) {
/*  64 */         String sql = "";
/*  65 */         if (flows.startsWith(",")) {
/*  66 */           flows = flows.substring(1);
/*     */         }
/*  68 */         if (flows.endsWith(",")) {
/*  69 */           flows = flows.substring(0, flows.length() - 1);
/*     */         }
/*  71 */         if (fdid.equals("")) {
/*  72 */           sql = "insert into WX_MsgRuleSetting (name,type,ifrepeat,typeids,flowsordocs,names,msgtpids,msgtpnames,freqtime,isenable,iftoall,ifcover,wfsettype,flowtype,ifwftodo,ifwffinish,ifwftimeout,ifwfreject,requestlevel,startdate,startcentext,enddate,endcentext,resourceids,resourcenames,resourcetype,ifsendsub, ifbatchsend, batchsendnum, batchsendinterval) values ('" + 
/*  73 */             name + "'," + type + "," + ifrepeat + ",'" + types + "',?,?,'" + msgtpids + "','" + msgtpnames + "'," + freqtime + ",'" + isenable + "','" + iftoall + "','" + ifcover + "','" + wfsettype + "'," + flowtype + "," + ifwftodo + "," + ifwffinish + "," + ifwftimeout + "," + ifwfreject + ",'" + requestlevel + "','" + startDate + "','" + startCentext + "','" + endDate + "','" + endCentext + "','" + resourceids + "','" + resourceNames + "','" + resourcetype + "','" + ifsendsub + "','" + ifbatchsend + "','" + batchsendnum + "','" + batchsendinterval + "')";
/*     */         } else {
/*  75 */           sql = "update WX_MsgRuleSetting set name='" + name + "',type=" + type + ",ifrepeat=" + ifrepeat + ",typeids='" + types + 
/*  76 */             "',flowsordocs=?,names=?" + 
/*  77 */             ",msgtpids='" + msgtpids + "',msgtpnames='" + msgtpnames + 
/*  78 */             "',freqtime=" + freqtime + ",isenable='" + isenable + "',iftoall='" + iftoall + "',ifcover='" + ifcover + 
/*  79 */             "',wfsettype='" + wfsettype + "',flowtype=" + flowtype + 
/*  80 */             ",ifwftodo=" + ifwftodo + ",ifwffinish=" + ifwffinish + ",ifwftimeout=" + ifwftimeout + ",ifwfreject=" + ifwfreject + ",requestlevel='" + requestlevel + "',startdate='" + startDate + "',startcentext='" + startCentext + "',enddate='" + endDate + "',endcentext='" + endCentext + "',resourceids='" + resourceids + "',resourcenames='" + resourceNames + "',resourcetype='" + resourcetype + "',ifsendsub=" + ifsendsub + ",ifbatchsend=" + ifbatchsend + ",batchsendnum=" + batchsendnum + ",batchsendinterval=" + batchsendinterval;
/*  81 */           if (isenable != 1) {
/*  82 */             sql = String.valueOf(sql) + ",lastscantime=''";
/*     */           }
/*  84 */           sql = String.valueOf(sql) + " where id = '" + fdid + "'";
/*     */         } 
/*  86 */         ConnStatement ConnStatement = null;
/*  87 */         boolean flag = false;
/*     */         try {
/*  89 */           ConnStatement = new ConnStatement();
/*  90 */           ConnStatement.setStatementSql(sql);
/*  91 */           ConnStatement.setString(1, flows);
/*  92 */           ConnStatement.setString(2, names);
/*  93 */           ConnStatement.executeUpdate();
/*  94 */           flag = true;
/*     */         }
/*  96 */         catch (Exception ex) {
/*  97 */           ex.printStackTrace();
/*  98 */           flag = false;
/*     */         } finally {
/* 100 */           if (ConnStatement != null) {
/* 101 */             ConnStatement.close();
/*     */           }
/*     */         } 
/* 104 */         if (flag) {
/* 105 */           status = 0;
/* 106 */           WxInterfaceInit.initFlowAndDoc();
/*     */         } else {
/* 108 */           msg = "执行SQL失败";
/*     */         } 
/*     */       } else {
/* 111 */         msg = "相关参数不完整";
/*     */       } 
/* 113 */     } catch (Exception e) {
/* 114 */       msg = "ECOLOGY中保存程序类出现异常:" + e.getMessage();
/*     */     } 
/* 116 */     String[] result = { (new StringBuilder(String.valueOf(status))).toString(), msg };
/* 117 */     return result;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/wxinterface/MsgRuleSetting.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */