/*    */ package weaver.wxinterface;
/*    */ 
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import weaver.file.Prop;
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.general.Util;
/*    */ import weaver.mobile.plugin.ecology.service.HrmResourceService;
/*    */ 
/*    */ public class EcologyUserVerify
/*    */   implements UserVerifyInterface
/*    */ {
/* 13 */   private static BaseBean bb = new BaseBean();
/*    */ 
/*    */   
/*    */   public Map<String, String> userVerify(String loginid, String password) {
/* 17 */     Map<String, String> map = new HashMap<String, String>();
/* 18 */     String result = "0", msg = "";
/*    */     try {
/* 20 */       HrmResourceService hrs = new HrmResourceService();
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */       
/* 27 */       result = (new StringBuilder(String.valueOf(hrs.checkLogin(loginid, password, "", "", 0)))).toString();
/* 28 */       if (!result.equals("1"))
/*    */       {
/* 30 */         if (result.equals("2")) {
/* 31 */           msg = "密码错误";
/* 32 */         } else if (result.equals("3")) {
/*    */           
/* 34 */           int ifyswl = Util.getIntValue(Prop.getPropValue("wx_yswl", "ifyswl"), 0);
/* 35 */           if (ifyswl == 1) {
/* 36 */             msg = "没有在数据库中查询到该人员数据，请上报IT服务台(请拨打021-3129 0988或手机钉钉下方的\"工作\"-IT服务台)";
/*    */           } else {
/* 38 */             msg = "登录账号不存在";
/*    */           }
/*    */         
/* 41 */         } else if (result.equals("5")) {
/* 42 */           msg = "HrmResourceService中的checkLogin方法程序异常";
/*    */         } else {
/* 44 */           msg = "HrmResourceService中的checkLogin方法返回的验证结果为【" + result + "】";
/*    */         }  } 
/* 46 */     } catch (Exception e) {
/* 47 */       msg = "EcologyUserVerify账号密码验证程序异常:" + e.getMessage();
/* 48 */       bb.writeLog("EcologyUserVerify账号密码验证程序异常:" + e.getMessage());
/*    */     } 
/* 50 */     map.put("result", result);
/* 51 */     map.put("msg", msg);
/* 52 */     map.put("loginid", loginid);
/* 53 */     return map;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/wxinterface/EcologyUserVerify.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */