/*     */ package weaver.wxinterface;
/*     */ 
/*     */ import java.text.SimpleDateFormat;
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ import net.sf.json.JSONArray;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ 
/*     */ 
/*     */ 
/*     */ public class MeetingUtils
/*     */ {
/*  15 */   private static SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static JSONArray getMeetings(String sDate, String sTime, String eDate, String eTime, User user) {
/*  22 */     RecordSet rs = new RecordSet();
/*  23 */     StringBuffer sql = new StringBuffer();
/*  24 */     if (WxModuleInit.ifMeetingRoomShare) {
/*  25 */       sql.append("select distinct a.* from MeetingRoom a,MeetingRoom_share b where  a.id = b.mid ");
/*  26 */       sql.append("and ((b.departmentid=" + user.getUserDepartment() + " and b.deptlevel<=" + user.getSeclevel());
/*  27 */       sql.append(" and b.deptlevelMax>=" + user.getSeclevel() + ")");
/*  28 */       sql.append(" or (b.subcompanyid in (" + user.getUserSubCompany1() + "," + user.getUserSubCompany2());
/*  29 */       sql.append("," + user.getUserSubCompany3() + "," + user.getUserSubCompany4() + ")");
/*  30 */       sql.append(" and b.sublevel<=" + user.getSeclevel() + " and b.sublevelMax>=");
/*  31 */       sql.append(String.valueOf(user.getSeclevel()) + ") or (b.seclevel<=" + user.getSeclevel() + " and b.seclevelMax>=");
/*  32 */       sql.append(String.valueOf(user.getSeclevel()) + ") or (b.userid =" + user.getUID() + "))");
/*     */     } else {
/*  34 */       sql.append("select distinct a.* from MeetingRoom a");
/*     */     } 
/*  36 */     if (WxModuleInit.ifMeetingDspOrder) {
/*  37 */       sql.append(" order by a.dsporder,a.name");
/*     */     } else {
/*  39 */       sql.append(" order by a.id");
/*     */     } 
/*     */     
/*  42 */     rs.execute(sql.toString());
/*  43 */     List<Meeting> list = new ArrayList<Meeting>();
/*  44 */     while (rs.next()) {
/*  45 */       int id = rs.getInt("id");
/*  46 */       String name = Util.null2String(rs.getString("name"));
/*  47 */       String status = Util.null2String(rs.getString("status"));
/*  48 */       if ("2".equals(status))
/*  49 */         continue;  Meeting m = getMeetingsById(id, name, sDate, sTime, eDate, eTime);
/*  50 */       if (m != null) {
/*  51 */         list.add(m);
/*     */       }
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  59 */     return JSONArray.fromObject(list);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Meeting getMeetingsById(int mid, String name, String sDate, String sTime, String eDate, String eTime) {
/*  66 */     Meeting m = null;
/*     */     
/*     */     try {
/*  69 */       StringBuffer sql = new StringBuffer();
/*  70 */       sql.append("select count(*) as amount from meeting where (meetingstatus = 2 or meetingstatus = 1) ");
/*  71 */       RecordSet rs = new RecordSet();
/*  72 */       String begindate = "", enddate = "", address = "";
/*  73 */       if ("sqlserver".equalsIgnoreCase(rs.getDBType())) {
/*  74 */         begindate = "begindate+' '+begintime";
/*  75 */         enddate = "enddate+' '+endtime";
/*  76 */         address = "(','+address+',')";
/*  77 */       } else if ("mysql".equalsIgnoreCase(rs.getDBType())) {
/*  78 */         begindate = "CONCAT(begindate,' ',begintime)";
/*  79 */         enddate = "CONCAT(enddate,' ',endtime)";
/*  80 */         address = "CONCAT(',',address,',')";
/*     */       } else {
/*  82 */         begindate = "CONCAT(CONCAT(begindate,' '),begintime)";
/*  83 */         enddate = "CONCAT(CONCAT(enddate,' '),endtime)";
/*  84 */         address = "CONCAT(CONCAT(',',address),',')";
/*     */       } 
/*  86 */       sql.append("and (((" + begindate + ") >= ('" + sDate + " " + sTime + "') ");
/*  87 */       sql.append("and (" + begindate + ") <= ('" + eDate + " " + eTime + "')) ");
/*  88 */       sql.append("or ((" + enddate + ") <= ('" + eDate + " " + eTime + "') ");
/*  89 */       sql.append("and (" + enddate + ") >= ('" + sDate + " " + sTime + "')) ");
/*  90 */       sql.append("or ((" + begindate + ") < ('" + sDate + " " + sTime + "') ");
/*  91 */       sql.append("and (" + enddate + ") > ('" + eDate + " " + eTime + "'))) ");
/*  92 */       if (WxModuleInit.ifMeetingAddressMulti) {
/*  93 */         sql.append("and " + address + " like '%," + mid + ",%'");
/*     */       } else {
/*  95 */         sql.append("and address =" + mid);
/*     */       } 
/*  97 */       if (WxModuleInit.ifMeetingRepeat) {
/*  98 */         sql.append(" AND repeatType = 0");
/*     */       }
/*     */       
/* 101 */       rs.executeSql(sql.toString());
/* 102 */       int ifConflict = 0;
/* 103 */       if (rs.next() && 
/* 104 */         rs.getInt(1) > 0) {
/* 105 */         ifConflict = 1;
/*     */       }
/*     */       
/* 108 */       m = new Meeting(mid, name, sDate, sTime, eDate, eTime, ifConflict);
/* 109 */     } catch (Exception e) {
/* 110 */       e.printStackTrace();
/*     */     } 
/* 112 */     return m;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean timeCompare(String sd1, String st1, String sd2, String st2) {
/* 120 */     boolean ifMax = false;
/*     */     try {
/* 122 */       long time1 = sdf.parse(String.valueOf(sd1) + " " + st1).getTime();
/* 123 */       long time2 = sdf.parse(String.valueOf(sd2) + " " + st2).getTime();
/* 124 */       if (time2 > time1) {
/* 125 */         ifMax = true;
/*     */       }
/* 127 */     } catch (Exception e) {
/* 128 */       e.printStackTrace();
/*     */     } 
/* 130 */     return ifMax;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/wxinterface/MeetingUtils.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */