/*     */ package weaver.wxinterface;
/*     */ 
/*     */ import java.text.SimpleDateFormat;
/*     */ import java.util.Calendar;
/*     */ import java.util.List;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ public class MainScanAndSend
/*     */   implements Runnable
/*     */ {
/*  13 */   private BaseBean bb = null;
/*     */   
/*     */   private SimpleDateFormat sdf;
/*     */   
/*     */   public static boolean ifSendingEmail = false;
/*     */   
/*     */   public MainScanAndSend() {
/*  20 */     this.bb = new BaseBean();
/*  21 */     this.sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
/*     */   }
/*     */ 
/*     */   
/*     */   public void run() {
/*     */     try {
/*  27 */       String nowtime = TimeUtil.getCurrentTimeString();
/*     */       
/*  29 */       List<FlowAndDoc> docscanlist = WxInterfaceInit.getDocList();
/*  30 */       for (FlowAndDoc fad : docscanlist) {
/*  31 */         if (fad != null) {
/*     */           try {
/*  33 */             String lastscantime = Util.null2String(fad.getLastscantime());
/*  34 */             if (lastscantime.length() == 16) lastscantime = String.valueOf(lastscantime) + ":00"; 
/*  35 */             long freqtime = (fad.getFreqtime() * 60);
/*  36 */             Calendar calendar = Calendar.getInstance();
/*  37 */             calendar.set(12, calendar.get(12) - 2);
/*  38 */             String nowDate = this.sdf.format(calendar.getTime());
/*  39 */             long intime = TimeUtil.timeInterval(lastscantime, nowDate);
/*  40 */             if (freqtime > 0L && (lastscantime.equals("") || intime >= freqtime)) {
/*  41 */               ScanDocAndSend sdas = new ScanDocAndSend(fad);
/*  42 */               (new Thread(sdas)).start();
/*     */             }
/*     */           
/*  45 */           } catch (Exception e) {
/*  46 */             this.bb.writeLog("微信扫描【" + fad.getName() + "】推送出现异常---" + e.getMessage());
/*     */           } 
/*     */         }
/*     */       } 
/*     */       
/*  51 */       List<FlowAndDoc> otherscanlist = WxInterfaceInit.getMsgList();
/*  52 */       for (FlowAndDoc fad : otherscanlist) {
/*  53 */         if (fad != null) {
/*     */           try {
/*  55 */             String lastscantime = Util.null2String(fad.getLastscantime());
/*  56 */             if (lastscantime.length() == 16) lastscantime = String.valueOf(lastscantime) + ":00"; 
/*  57 */             long freqtime = (fad.getFreqtime() * 60);
/*  58 */             long intime = TimeUtil.timeInterval(lastscantime, nowtime);
/*  59 */             if (freqtime > 0L && (lastscantime.equals("") || intime >= freqtime))
/*  60 */               if (fad.getType() == 4) {
/*  61 */                 CoworkSendMsg csm = new CoworkSendMsg(fad);
/*  62 */                 (new Thread(csm)).start();
/*  63 */               } else if (fad.getType() == 5) {
/*  64 */                 WorkPlanSendMsg wsm = new WorkPlanSendMsg(fad);
/*  65 */                 (new Thread(wsm)).start();
/*  66 */               } else if (fad.getType() == 6) {
/*  67 */                 MeettingSendMsg msm = new MeettingSendMsg(fad);
/*  68 */                 (new Thread(msm)).start();
/*  69 */               } else if (fad.getType() != 7) {
/*     */                 
/*  71 */                 if (fad.getType() == 8) {
/*  72 */                   CustomerSendMsg csm = new CustomerSendMsg(fad);
/*  73 */                   (new Thread(csm)).start();
/*  74 */                 } else if (fad.getType() == 10) {
/*  75 */                   BlogSendMsg bsm = new BlogSendMsg(fad);
/*  76 */                   (new Thread(bsm)).start();
/*  77 */                 } else if (fad.getType() == 14) {
/*  78 */                   VotingSendMsg vsm = new VotingSendMsg(fad);
/*  79 */                   (new Thread(vsm)).start();
/*     */                 }
/*  81 */                 else if (fad.getType() == 20) {
/*  82 */                   AttenSendMsg asm = new AttenSendMsg(fad);
/*  83 */                   (new Thread(asm)).start();
/*     */                 } 
/*     */               }  
/*  86 */             if (fad.getType() == 11) {
/*  87 */               Calendar calendar = Calendar.getInstance();
/*  88 */               calendar.set(6, calendar.get(6) - 2);
/*  89 */               String nowDate = this.sdf.format(calendar.getTime());
/*  90 */               long intime2 = TimeUtil.timeInterval(lastscantime, nowDate);
/*  91 */               if (freqtime > 0L && (lastscantime.equals("") || intime2 >= freqtime) && !ifSendingEmail) {
/*  92 */                 ifSendingEmail = true;
/*  93 */                 EmailSendMsg esm = new EmailSendMsg(fad);
/*  94 */                 (new Thread(esm)).start();
/*     */               } 
/*     */             } 
/*  97 */           } catch (Exception e) {
/*  98 */             this.bb.writeLog("微信扫描【" + fad.getId() + "-" + fad.getName() + "】推送出现异常---" + e.getMessage());
/*     */           }
/*     */         
/*     */         }
/*     */       } 
/* 103 */     } catch (Exception e) {
/* 104 */       this.bb.writeLog("微信扫描MainScanAndSend程序出现异常---" + e.getMessage());
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/wxinterface/MainScanAndSend.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */