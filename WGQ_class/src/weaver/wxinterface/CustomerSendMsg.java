/*    */ package weaver.wxinterface;
/*    */ 
/*    */ import java.text.SimpleDateFormat;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.BaseBean;
/*    */ 
/*    */ 
/*    */ 
/*    */ public class CustomerSendMsg
/*    */   implements Runnable
/*    */ {
/*    */   private SimpleDateFormat sdf;
/*    */   private BaseBean bb;
/*    */   private FlowAndDoc fad;
/*    */   
/*    */   public CustomerSendMsg(FlowAndDoc fad) {
/* 21 */     this.fad = fad;
/* 22 */     this.bb = new BaseBean();
/* 23 */     this.sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
/*    */   }
/*    */ 
/*    */   
/*    */   public void run() {
/* 28 */     scanAndSend();
/*    */   }
/*    */   
/*    */   private FlowAndDoc scanAndSend() {
/*    */     try {
/* 33 */       Map<String, Object> map = new HashMap<String, Object>();
/* 34 */       map.put("ifsendsub", Integer.valueOf(this.fad.getIfsendsub()));
/*    */       
/* 36 */       String lastscantime = this.fad.getLastscantime();
/* 37 */       String nowDate = this.sdf.format(Long.valueOf(System.currentTimeMillis()));
/* 38 */       if (lastscantime == null || lastscantime.equals("")) {
/* 39 */         lastscantime = nowDate;
/*    */       }
/* 41 */       RecordSet rs = new RecordSet();
/* 42 */       RecordSet rs2 = new RecordSet();
/*    */       
/* 44 */       this.fad.setLastscantime(nowDate);
/* 45 */       rs.execute("update WX_MsgRuleSetting set lastscantime='" + nowDate + "' where id =" + this.fad.getId());
/*    */       
/* 47 */       String sql = "select t1.id,t1.name,t1.type,t1.manager from CRM_CustomerInfo t1,CRM_ViewLog2 t2 where  (t1.deleted=0 or t1.deleted is null) and t1.id=t2.customerid";
/*    */ 
/*    */       
/* 50 */       rs.execute(sql);
/*    */       
/* 52 */       while (rs.next()) {
/*    */         try {
/* 54 */           String id = rs.getString("id");
/* 55 */           rs2.executeSql("select * from WX_SCANLOG where reourceid=" + id + " and type=8");
/* 56 */           if (!rs2.next()) {
/* 57 */             String name = rs.getString("name");
/* 58 */             String manager = rs.getString("manager");
/* 59 */             String desc = "您有一个新的客户【" + name + "】";
/*    */ 
/*    */ 
/*    */             
/* 63 */             List<String> useridList = new ArrayList<String>();
/* 64 */             useridList.add(manager);
/* 65 */             InterfaceUtil.sendMsg(useridList, this.fad.getMsgtpids(), id, desc, 8, map);
/*    */           } 
/* 67 */         } catch (Exception e) {
/* 68 */           this.bb.writeLog("客户发送微信提醒失败", e);
/* 69 */           e.printStackTrace();
/*    */         } 
/*    */       } 
/* 72 */     } catch (Exception e) {
/* 73 */       this.bb.writeLog("定时扫描客户并提醒程序异常", e);
/* 74 */       e.printStackTrace();
/*    */     } 
/* 76 */     return this.fad;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/wxinterface/CustomerSendMsg.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */