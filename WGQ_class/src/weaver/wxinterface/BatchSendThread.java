/*    */ package weaver.wxinterface;
/*    */ 
/*    */ import java.text.SimpleDateFormat;
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import net.sf.json.JSONArray;
/*    */ import net.sf.json.JSONObject;
/*    */ import net.sf.json.JsonConfig;
/*    */ import weaver.conn.ConnStatement;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.BaseBean;
/*    */ 
/*    */ public class BatchSendThread
/*    */   implements Runnable
/*    */ {
/* 16 */   private BaseBean bb = null;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void run() {
/*    */     try {
/* 24 */       SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
/* 25 */       String nowDate = sdf.format(Long.valueOf(System.currentTimeMillis()));
/* 26 */       RecordSet rs = new RecordSet();
/* 27 */       rs.execute("select * from WX_MsgBatchRecord where sendtime < '" + nowDate + "' and (sendstatus = 0 or sendstatus is null)");
/* 28 */       while (rs.next()) {
/* 29 */         String receiveusers = rs.getString("receiveusers");
/* 30 */         JSONArray receiveusersArray = JSONArray.fromObject(receiveusers);
/* 31 */         List<String> receiveusersList = JSONArray.toList(receiveusersArray, new String(), new JsonConfig());
/* 32 */         String tpids = rs.getString("tpids");
/* 33 */         String dataid = rs.getString("dataid");
/* 34 */         String content = rs.getString("content");
/* 35 */         int type = rs.getInt("type");
/* 36 */         String otherinfo = rs.getString("otherinfo");
/* 37 */         JSONObject otherinfoObject = JSONObject.fromObject(otherinfo);
/* 38 */         JSONObject jSONObject1 = otherinfoObject;
/* 39 */         InterfaceUtil.sendMsg(receiveusersList, tpids, dataid, content, type, (Map<String, Object>)jSONObject1);
/* 40 */         setSendStatus(rs.getString("id"));
/*    */       } 
/* 42 */     } catch (Exception e) {
/* 43 */       e.printStackTrace();
/* 44 */       this.bb.writeLog("批量推送消息失败，异常信息" + e.getMessage());
/*    */     } 
/*    */   }
/*    */   
/*    */   public void setSendStatus(String recordId) {
/* 49 */     ConnStatement ConnStatement = null;
/* 50 */     String sql = "update WX_MsgBatchRecord set sendstatus = 1 where id = '" + recordId + "'";
/*    */     try {
/* 52 */       ConnStatement = new ConnStatement();
/* 53 */       ConnStatement.setStatementSql(sql);
/* 54 */       ConnStatement.executeUpdate();
/* 55 */     } catch (Exception e) {
/* 56 */       (new BaseBean()).writeLog("更新分批推送记录发送消息状态出错---分批推送记录id：" + recordId + " 异常信息：" + e.getMessage());
/*    */     } finally {
/* 58 */       if (ConnStatement != null)
/* 59 */         ConnStatement.close(); 
/*    */     } 
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/wxinterface/BatchSendThread.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */