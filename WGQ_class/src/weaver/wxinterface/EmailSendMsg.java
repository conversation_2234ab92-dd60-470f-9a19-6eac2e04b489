/*     */ package weaver.wxinterface;
/*     */ 
/*     */ import java.text.SimpleDateFormat;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Calendar;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class EmailSendMsg
/*     */   implements Runnable
/*     */ {
/*     */   private SimpleDateFormat sdf;
/*     */   private BaseBean bb;
/*     */   private FlowAndDoc fad;
/*     */   private ResourceComInfo rc;
/*     */   
/*     */   public EmailSendMsg(FlowAndDoc fad) {
/*  26 */     this.fad = fad;
/*  27 */     this.sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
/*  28 */     this.bb = new BaseBean();
/*     */     try {
/*  30 */       this.rc = new ResourceComInfo();
/*  31 */     } catch (Exception exception) {}
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void run() {
/*  38 */     scanAndSend();
/*     */   }
/*     */   
/*     */   private FlowAndDoc scanAndSend() {
/*     */     try {
/*  43 */       Map<String, Object> map = new HashMap<String, Object>();
/*  44 */       map.put("ifsendsub", Integer.valueOf(this.fad.getIfsendsub()));
/*     */       
/*  46 */       String lastscantime = this.fad.getLastscantime();
/*     */ 
/*     */ 
/*     */       
/*  50 */       Calendar calendar = Calendar.getInstance();
/*     */       
/*  52 */       calendar.set(6, calendar.get(6) - 2);
/*  53 */       String nowDate = this.sdf.format(calendar.getTime());
/*     */       
/*  55 */       if (lastscantime == null || lastscantime.equals("")) {
/*  56 */         lastscantime = nowDate;
/*     */       }
/*  58 */       RecordSet rs = new RecordSet();
/*     */       
/*  60 */       this.fad.setLastscantime(nowDate);
/*  61 */       rs.execute("update WX_MsgRuleSetting set lastscantime='" + nowDate + "' where id =" + this.fad.getId());
/*  62 */       String sql = "select * from mailresource m where canview=1  and folderid = '0' and status ='0'  and senddate > '" + 
/*  63 */         lastscantime + ":00'" + 
/*  64 */         " and not exists (select 1 from WX_SCANLOG l where " + 
/*  65 */         " m.id = l.reourceid and l.type = 11)" + 
/*  66 */         " and not exists (select 1 from WX_SCANLOG l where " + 
/*  67 */         " m.originalMailId = l.reourceid and l.type = 11)" + 
/*  68 */         " order by originalMailId";
/*  69 */       long time1 = System.currentTimeMillis();
/*  70 */       this.bb.writeLog("EmailSendMsg扫描SQL,nowDate[" + nowDate + "],lastscantime[" + lastscantime + "]======\n" + sql);
/*  71 */       rs.execute(sql);
/*  72 */       long time2 = System.currentTimeMillis();
/*  73 */       this.bb.writeLog("EmailSendMsg扫描SQL执行耗时,nowDate[" + nowDate + "],lastscantime[" + lastscantime + "]======\n" + (time2 - time1) + "毫秒");
/*  74 */       String oldMailId = "", id = "", oldDesc = "";
/*  75 */       List<String> userList = new ArrayList<String>();
/*  76 */       while (rs.next()) {
/*     */         try {
/*  78 */           id = Util.null2String(rs.getString("id"));
/*     */           
/*  80 */           String originalMailId = Util.null2String(rs.getString("originalMailId"));
/*  81 */           String resourceid = rs.getString("resourceid");
/*  82 */           String sendfrom = Util.null2String(rs.getString("sendfrom"));
/*     */           
/*  84 */           String subject = Util.null2String(rs.getString("subject"));
/*  85 */           String isInternal = Util.null2String(rs.getString("isInternal"));
/*  86 */           String desc = "您有一封来自";
/*  87 */           if (isInternal.equals("1") && Util.getIntValue(sendfrom, 0) != 0) {
/*  88 */             desc = String.valueOf(desc) + "【" + FormatMultiLang.format(this.rc.getLastname(sendfrom), "7") + "】";
/*     */           } else {
/*     */             
/*  91 */             desc = String.valueOf(desc) + "【" + FormatMultiLang.format(getEmailRealName(sendfrom, resourceid), "7") + "】";
/*     */           } 
/*  93 */           desc = String.valueOf(desc) + "的新邮件【" + FormatMultiLang.format(subject, "7") + "】";
/*     */           
/*  95 */           if (originalMailId.equals("")) {
/*     */             
/*  97 */             List<String> useridList = new ArrayList<String>();
/*  98 */             useridList.add(resourceid);
/*     */             
/* 100 */             InterfaceUtil.sendMsg(useridList, this.fad.getMsgtpids(), id, desc, 11, map);
/*     */             continue;
/*     */           } 
/* 103 */           if (oldMailId.equals(originalMailId)) {
/* 104 */             userList.add(resourceid); continue;
/*     */           } 
/* 106 */           if (userList.size() > 0) {
/*     */ 
/*     */             
/* 109 */             InterfaceUtil.sendMsg(userList, this.fad.getMsgtpids(), oldMailId, oldDesc, 11, map);
/* 110 */             userList = new ArrayList<String>();
/*     */           } 
/* 112 */           oldDesc = desc;
/* 113 */           oldMailId = originalMailId;
/* 114 */           userList.add(resourceid);
/*     */         }
/* 116 */         catch (Exception e) {
/* 117 */           this.bb.writeLog("邮件发送微信提醒失败", e);
/* 118 */           e.printStackTrace();
/*     */         } 
/*     */       } 
/*     */ 
/*     */       
/* 123 */       if (userList.size() > 0) {
/*     */         
/*     */         try {
/*     */           
/* 127 */           InterfaceUtil.sendMsg(userList, this.fad.getMsgtpids(), oldMailId, oldDesc, 11, map);
/* 128 */         } catch (Exception e) {
/* 129 */           this.bb.writeLog("邮件发送微信提醒失败", e);
/* 130 */           e.printStackTrace();
/*     */         } 
/*     */       }
/* 133 */       MainScanAndSend.ifSendingEmail = false;
/*     */     
/*     */     }
/* 136 */     catch (Exception e) {
/* 137 */       this.bb.writeLog("定时扫描邮件并提醒程序异常", e);
/* 138 */       e.printStackTrace();
/*     */     } 
/* 140 */     return this.fad;
/*     */   }
/*     */   
/*     */   public static String getEmailRealName(String mailAddress, String userid) {
/* 144 */     String realName = "";
/*     */     try {
/* 146 */       ResourceComInfo resourceComInfo = new ResourceComInfo();
/* 147 */       realName = resourceComInfo.getLastnameByEmail(mailAddress);
/* 148 */       String sql = "";
/* 149 */       RecordSet rs = new RecordSet();
/* 150 */       if (realName.equals("")) {
/* 151 */         sql = "select mailUserName from MailUserAddress where mailAddress ='" + mailAddress + "'" + (userid.equals("") ? "" : (" and userid=" + userid));
/* 152 */         rs.execute(sql);
/* 153 */         if (rs.next()) {
/* 154 */           realName = rs.getString("mailUserName");
/*     */         }
/*     */       } 
/* 157 */       if (realName.equals("")) {
/* 158 */         sql = "SELECT accountName FROM MailAccount WHERE accountMailAddress='" + mailAddress + "'";
/* 159 */         rs.execute(sql);
/* 160 */         if (rs.next()) {
/* 161 */           realName = rs.getString("accountName");
/*     */         } else {
/* 163 */           realName = mailAddress;
/*     */         } 
/*     */       } 
/* 166 */     } catch (Exception e) {
/* 167 */       e.printStackTrace();
/*     */     } 
/* 169 */     return realName;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/wxinterface/EmailSendMsg.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */