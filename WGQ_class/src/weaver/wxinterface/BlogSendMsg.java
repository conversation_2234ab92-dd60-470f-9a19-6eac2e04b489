/*    */ package weaver.wxinterface;
/*    */ 
/*    */ import java.text.SimpleDateFormat;
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.hrm.resource.ResourceComInfo;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class BlogSendMsg
/*    */   implements Runnable
/*    */ {
/*    */   private SimpleDateFormat sdf;
/*    */   private BaseBean bb;
/*    */   private FlowAndDoc fad;
/*    */   private ResourceComInfo rc;
/*    */   
/*    */   public BlogSendMsg(FlowAndDoc fad) {
/* 24 */     this.fad = fad;
/* 25 */     this.sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
/* 26 */     this.bb = new BaseBean();
/*    */     try {
/* 28 */       this.rc = new ResourceComInfo();
/* 29 */     } catch (Exception exception) {}
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void run() {
/* 36 */     scanAndSend();
/*    */   }
/*    */   
/*    */   private FlowAndDoc scanAndSend() {
/*    */     try {
/* 41 */       Map<String, Object> map = new HashMap<String, Object>();
/* 42 */       map.put("ifsendsub", Integer.valueOf(this.fad.getIfsendsub()));
/*    */       
/* 44 */       String lastscantime = this.fad.getLastscantime();
/* 45 */       String nowDate = this.sdf.format(Long.valueOf(System.currentTimeMillis()));
/* 46 */       if (lastscantime == null || lastscantime.equals("")) {
/* 47 */         lastscantime = nowDate;
/*    */       }
/* 49 */       RecordSet rs = new RecordSet();
/* 50 */       this.fad.setLastscantime(nowDate);
/* 51 */       rs.execute("update WX_MsgRuleSetting set lastscantime='" + nowDate + "' where id =" + this.fad.getId());
/*    */       
/* 53 */       String createdate = "";
/* 54 */       if ("sqlserver".equalsIgnoreCase(rs.getDBType())) {
/* 55 */         createdate = "(createdate+' '+createtime)";
/* 56 */       } else if ("mysql".equalsIgnoreCase(rs.getDBType())) {
/* 57 */         createdate = "(CONCAT(createdate,' ',createtime))";
/*    */       } else {
/* 59 */         createdate = "(CONCAT(CONCAT(createdate,' '),createtime))";
/*    */       } 
/* 61 */       String sql = "select * from blog_reply where userid != bediscussantid and " + 
/* 62 */         createdate + "> '" + lastscantime + ":59' and " + createdate + " <= '" + nowDate + ":59'";
/* 63 */       rs.execute(sql);
/*    */       
/* 65 */       while (rs.next()) {
/*    */         
/*    */         try {
/* 68 */           String workdate = rs.getString("workdate");
/* 69 */           String userid = rs.getString("userid");
/* 70 */           String content = rs.getString("content");
/* 71 */           String bediscussantid = rs.getString("bediscussantid");
/* 72 */           String desc = "您‘" + workdate + "’日的工作微博有来自‘" + FormatMultiLang.format(this.rc.getLastname(userid), "7") + "’的评论:" + 
/* 73 */             FormatMultiLang.formatByUserid(content, bediscussantid);
/*    */ 
/*    */ 
/*    */           
/* 77 */           List<String> useridList = new ArrayList<String>();
/* 78 */           useridList.add(bediscussantid);
/* 79 */           InterfaceUtil.sendMsg(useridList, this.fad.getMsgtpids(), bediscussantid, desc, 10, map);
/* 80 */         } catch (Exception e) {
/* 81 */           this.bb.writeLog("微博发送微信提醒失败", e);
/* 82 */           e.printStackTrace();
/*    */         }
/*    */       
/*    */       } 
/* 86 */     } catch (Exception e) {
/* 87 */       this.bb.writeLog("定时扫描微博并提醒程序异常", e);
/* 88 */       e.printStackTrace();
/*    */     } 
/* 90 */     return this.fad;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/wxinterface/BlogSendMsg.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */