/*     */ package weaver.wxinterface;
/*     */ 
/*     */ import java.lang.reflect.Method;
/*     */ import java.text.ParseException;
/*     */ import java.text.SimpleDateFormat;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import net.sf.json.JSONArray;
/*     */ import net.sf.json.JSONObject;
/*     */ import weaver.conn.ConnStatement;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.file.Prop;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ import weaver.workflow.request.MailAndMessage;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SendMsgThread
/*     */   extends BaseBean
/*     */   implements Runnable
/*     */ {
/*     */   private String ecmodule;
/*     */   private String ecmoduledataId;
/*     */   private String content;
/*     */   private List touserloginidList;
/*     */   private int workflowid;
/*  33 */   private int requesttype = 0;
/*     */   
/*     */   public SendMsgThread(String ecmodule, String ecmoduledataId, String content, List uList, int useridType) {
/*  36 */     this.ecmodule = ecmodule;
/*  37 */     this.ecmoduledataId = ecmoduledataId;
/*  38 */     this.content = content;
/*  39 */     this.touserloginidList = uList;
/*     */   }
/*     */   
/*     */   public SendMsgThread(String ecmodule, String ecmoduledataId, String content, List uList, Integer useridType) {
/*  43 */     this.ecmodule = ecmodule;
/*  44 */     this.ecmoduledataId = ecmoduledataId;
/*  45 */     this.content = content;
/*  46 */     this.touserloginidList = uList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public SendMsgThread(String ecmodule, String ecmoduledataId, String content, List uList, Integer useridType, Integer workflowid) {
/*  58 */     this.ecmodule = ecmodule;
/*  59 */     this.ecmoduledataId = ecmoduledataId;
/*  60 */     this.content = content;
/*  61 */     this.touserloginidList = uList;
/*  62 */     this.workflowid = workflowid.intValue();
/*  63 */     this.requesttype = 0;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public SendMsgThread(String requestid, String content, List uList, Integer requesttype, String logintype, Integer workflowid) {
/*  75 */     if ("0".equals(logintype)) {
/*  76 */       this.ecmodule = "-2";
/*  77 */       this.ecmoduledataId = requestid;
/*  78 */       this.content = content;
/*  79 */       this.touserloginidList = uList;
/*  80 */       this.workflowid = workflowid.intValue();
/*  81 */       this.requesttype = requesttype.intValue();
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public void writeLog(String str) {
/*  87 */     int ifLog = Util.getIntValue(Prop.getPropValue("wx_wfset", "ifLog"), 0);
/*  88 */     if (ifLog == 1) {
/*  89 */       writeLog(str);
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public SendMsgThread(List<HashMap> list) {
/*  96 */     writeLog("=======进入SendMsgThread的构造方法参数为LIST=========");
/*  97 */     if (list != null && list.size() > 0) {
/*  98 */       List<String> userList = new ArrayList();
/*  99 */       String requestName = "";
/* 100 */       int requestid = 0;
/* 101 */       for (int i = 0; i < list.size(); i++) {
/* 102 */         Map map = list.get(i);
/* 103 */         if (i == 0) {
/* 104 */           requestid = Util.getIntValue((String)map.get("requestid"), 0);
/* 105 */           requestName = Util.null2String((String)map.get("requestname"));
/* 106 */           this.workflowid = Util.getIntValue((String)map.get("workflowid"), 0);
/* 107 */           this.requesttype = Util.getIntValue((String)map.get("type"), 0);
/*     */         } 
/* 109 */         String logintype = Util.null2String((String)map.get("logintype"));
/* 110 */         if (logintype.equals("0")) {
/* 111 */           String userid = Util.null2String((String)map.get("userid"));
/* 112 */           writeLog("=======this.userid=========" + userid);
/* 113 */           if (!"".equals(userid) && !"-1".equals(userid)) userList.add(userid); 
/*     */         } 
/*     */       } 
/* 116 */       this.ecmodule = "-2";
/* 117 */       this.ecmoduledataId = (new StringBuilder(String.valueOf(requestid))).toString();
/* 118 */       this.content = requestName;
/* 119 */       this.touserloginidList = userList;
/* 120 */       writeLog("=======this.workflowid=========" + this.workflowid);
/* 121 */       writeLog("=======this.requesttype=========" + this.requesttype);
/* 122 */       writeLog("=======this.ecmoduledataId=========" + this.ecmoduledataId);
/* 123 */       writeLog("=======this.content=========" + this.content);
/*     */     } else {
/* 125 */       writeLog("=======进SendMsgThread的构造方法参数为LIST的参数大小为0=========");
/*     */     } 
/*     */   }
/*     */   
/*     */   public static List<List<String>> splitList(List<String> list, int splitCount) {
/* 130 */     List<List<String>> retrunList = new ArrayList<List<String>>();
/* 131 */     List<String> tempList = new ArrayList<String>();
/* 132 */     if (list != null && splitCount > 0) {
/* 133 */       for (String str : list) {
/* 134 */         if (tempList.size() >= splitCount) {
/* 135 */           retrunList.add(tempList);
/* 136 */           tempList = new ArrayList<String>();
/*     */         } 
/* 138 */         if (!"".equals(str)) {
/* 139 */           tempList.add(str);
/*     */         }
/*     */       } 
/* 142 */       retrunList.add(tempList);
/*     */     } 
/* 144 */     return retrunList;
/*     */   }
/*     */   
/*     */   public void run() {
/*     */     try {
/* 149 */       this.content = FormatMultiLang.format(this.content, "7");
/* 150 */       writeLog("=======this.content===FORMAT后======" + this.content);
/* 151 */       if (this.touserloginidList != null) this.touserloginidList.remove("-1"); 
/* 152 */       writeLog("=======this.touserloginidList=========" + this.touserloginidList);
/* 153 */       writeLog("=======this.touserloginidList.size=========" + this.touserloginidList.size());
/* 154 */       writeLog("=======Util.null2String(ecmodule)======" + Util.null2String(this.ecmodule));
/* 155 */       if (Util.null2String(this.ecmodule).equals("-2") && this.touserloginidList != null && this.touserloginidList.size() > 0) {
/* 156 */         if (Util.null2String(this.content).equals("")) {
/* 157 */           this.content = "您有一个流程需要处理";
/*     */         }
/*     */         
/* 160 */         if (this.requesttype == 0 || this.requesttype == 1 || this.requesttype == 10 || this.requesttype == 14) {
/* 161 */           String preContent = "";
/*     */           
/* 163 */           int ifUse = Util.getIntValue(Prop.getPropValue("wx_wfprevtitle", "ifUse"), 0);
/* 164 */           if (ifUse == 1) {
/* 165 */             if (this.requesttype == 10) {
/* 166 */               preContent = Util.null2String(Prop.getPropValue("wx_wfprevtitle", "prevtitle_10"));
/* 167 */             } else if (this.requesttype == 1) {
/* 168 */               preContent = Util.null2String(Prop.getPropValue("wx_wfprevtitle", "prevtitle_1"));
/* 169 */             } else if (this.requesttype == 14) {
/* 170 */               preContent = Util.null2String(Prop.getPropValue("wx_wfprevtitle", "prevtitle_14"));
/* 171 */             } else if (this.requesttype == 0) {
/* 172 */               preContent = Util.null2String(Prop.getPropValue("wx_wfprevtitle", "prevtitle_0"));
/*     */             }
/*     */           
/* 175 */           } else if (this.requesttype == 10 && !this.content.startsWith("【超时】")) {
/* 176 */             preContent = "【超时】";
/* 177 */           } else if (this.requesttype == 1 && !this.content.startsWith("【归档】")) {
/* 178 */             preContent = "【归档】";
/* 179 */           } else if (this.requesttype == 14 && !this.content.startsWith("【退回】")) {
/* 180 */             preContent = "【退回】";
/*     */           } 
/*     */           
/* 183 */           this.content = String.valueOf(preContent) + this.content;
/* 184 */           writeLog("=======this.content带前缀=========" + this.content);
/* 185 */           List<FlowAndDoc> flowList = WxInterfaceInit.getFlowList();
/* 186 */           if (flowList != null && flowList.size() > 0) {
/* 187 */             String tpids = "";
/* 188 */             String requestlevel = "";
/* 189 */             String creater = "", createdate = "", createtime = "";
/* 190 */             RecordSet rs = new RecordSet();
/*     */ 
/*     */ 
/*     */             
/* 194 */             rs.execute("select workflowid,requestname,creater,createdate,createtime,requestlevel from workflow_requestbase where requestid=" + this.ecmoduledataId);
/* 195 */             if (rs.next()) {
/* 196 */               this.workflowid = Util.getIntValue(rs.getString(1), 0);
/* 197 */               requestlevel = Util.null2String(rs.getString("requestlevel"));
/* 198 */               creater = Util.null2String(rs.getString("creater"));
/* 199 */               createdate = Util.null2String(rs.getString("createdate"));
/* 200 */               createtime = Util.null2String(rs.getString("createtime"));
/*     */             } 
/*     */             
/* 203 */             String workflowname = "";
/*     */             
/*     */             try {
/* 206 */               int userlang = 7;
/* 207 */               int isbill = 0;
/* 208 */               int formid = 0;
/* 209 */               rs.execute("select formid,isbill,workflowname from workflow_base where id=" + this.workflowid);
/* 210 */               if (rs.next()) {
/* 211 */                 formid = rs.getInt(1);
/* 212 */                 isbill = rs.getInt(2);
/* 213 */                 workflowname = Util.null2String(rs.getString("workflowname"));
/*     */               } 
/* 215 */               MailAndMessage mailTitle = new MailAndMessage();
/* 216 */               String titles = mailTitle.getTitle(Util.getIntValue(this.ecmoduledataId, -1), this.workflowid, formid, userlang, isbill);
/*     */               
/* 218 */               titles = Util.delHtml(titles);
/*     */               
/* 220 */               if (!titles.equals("")) {
/* 221 */                 titles = FormatMultiLang.format(titles, "7");
/* 222 */                 this.content = String.valueOf(this.content) + "（" + titles + "）";
/*     */               }
/*     */             
/* 225 */             } catch (Exception e) {
/* 226 */               writeLog("微信/钉钉推送获取流程标题字段出现异常:" + e.getMessage());
/*     */             } 
/* 228 */             writeLog("=======this.content带标题字段=========" + this.content);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */             
/* 237 */             String allwfids = (new StringBuilder(String.valueOf(this.workflowid))).toString();
/*     */             try {
/* 239 */               Class<?> WorkflowVersion = Class.forName("weaver.workflow.workflow.WorkflowVersion");
/* 240 */               Method m = WorkflowVersion.getMethod("getAllVersionStringByWFIDs", new Class[] { String.class });
/* 241 */               allwfids = (String)m.invoke(WorkflowVersion, new Object[] { allwfids });
/*     */             }
/* 243 */             catch (Exception e) {
/*     */               
/* 245 */               allwfids = (new StringBuilder(String.valueOf(this.workflowid))).toString();
/*     */             } 
/*     */ 
/*     */             
/* 249 */             List allwfidList = Util.TokenizerString(allwfids, ",");
/*     */ 
/*     */             
/* 252 */             int ifGXZY = Util.getIntValue(Prop.getPropValue("gxzycsdev", "ifGXZY"), 0);
/* 253 */             String nodeName = "";
/* 254 */             rs.executeSql("select wn.nodename from workflow_requestbase wf,workflow_nodebase wn where wf.currentnodeid = wn.id and wf.requestid = " + this.ecmoduledataId);
/* 255 */             if (rs.next()) {
/* 256 */               nodeName = Util.null2String(rs.getString("nodename"));
/*     */             }
/*     */             
/* 259 */             Map<String, Object> map = new HashMap<String, Object>();
/* 260 */             for (FlowAndDoc fad : flowList) {
/* 261 */               if (fad != null) {
/* 262 */                 boolean issend = false;
/* 263 */                 if (fad.getFlowsordocs().equals("-1")) {
/* 264 */                   if (fad.getFlowtype() == 0) {
/* 265 */                     issend = true;
/*     */                   }
/*     */                 } else {
/* 268 */                   for (Object wfid : allwfidList) {
/* 269 */                     if (("," + fad.getFlowsordocs() + ",").indexOf("," + (String)wfid + ",") > -1) {
/* 270 */                       if (fad.getFlowtype() == 1) {
/* 271 */                         issend = false;
/*     */                         break;
/*     */                       } 
/* 274 */                       issend = true;
/*     */                       break;
/*     */                     } 
/* 277 */                     if (fad.getFlowtype() == 1) {
/* 278 */                       issend = true;
/*     */                     }
/*     */                   } 
/*     */                 } 
/* 282 */                 if (issend && !fad.getRequestlevel().equals("") && (
/* 283 */                   "," + fad.getRequestlevel() + ",").indexOf("," + requestlevel + ",") < 0) {
/* 284 */                   issend = false;
/* 285 */                   writeLog("当前流程的紧急程度为:" + requestlevel + ",该推送设置设置的紧急程度为:" + fad.getRequestlevel() + ",不在范围内，不做推送!");
/*     */                 } 
/*     */                 
/* 288 */                 if (issend) {
/*     */                   
/* 290 */                   if ((this.requesttype == 0 && fad.getIfwftodo() == 0) || (
/* 291 */                     this.requesttype == 1 && fad.getIfwffinish() == 0) || (
/* 292 */                     this.requesttype == 10 && fad.getIfwftimeout() == 0) || (
/* 293 */                     this.requesttype == 14 && fad.getIfwfreject() == 0)) {
/*     */                     continue;
/*     */                   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */                   
/* 302 */                   String msgtpids = fad.getMsgtpids();
/* 303 */                   if (msgtpids != null && !"".equals(msgtpids)) {
/* 304 */                     String[] mtps = msgtpids.split(",");
/* 305 */                     if (mtps != null && mtps.length > 0) {
/* 306 */                       byte b; int i; String[] arrayOfString; for (i = (arrayOfString = mtps).length, b = 0; b < i; ) { String mtp = arrayOfString[b];
/* 307 */                         if (mtp != null && !"".equals(mtp))
/* 308 */                           tpids = String.valueOf(tpids) + "," + mtp; 
/*     */                         b++; }
/*     */                     
/*     */                     } 
/*     */                   } 
/*     */                 } 
/*     */               } 
/* 315 */               writeLog("=======tpids=========" + tpids);
/* 316 */               if (!tpids.equals("")) {
/* 317 */                 tpids = tpids.substring(1);
/* 318 */                 int ifFdSSo = Util.getIntValue(Prop.getPropValue("fdcsdev", "ifFdSSo"), 0);
/* 319 */                 map.put("ifsendsub", (new StringBuilder(String.valueOf(fad.getIfsendsub()))).toString());
/* 320 */                 if (ifFdSSo == 1) {
/* 321 */                   List<String> needToSendList = new ArrayList();
/* 322 */                   needToSendList.addAll(this.touserloginidList); int i;
/* 323 */                   for (i = 0; i < this.touserloginidList.size(); i++) {
/* 324 */                     String uid = Util.null2String(this.touserloginidList.get(i));
/* 325 */                     writeLog("=======当前人员ID=========" + uid);
/* 326 */                     int settype = 1;
/* 327 */                     rs.executeSql("select settype from FdWFMsgSet where userid = " + uid);
/* 328 */                     writeLog("=======SQL=========\nselect settype from FdWFMsgSet where userid = " + uid);
/* 329 */                     if (rs.next()) {
/* 330 */                       settype = Util.getIntValue(rs.getString("settype"));
/*     */                     }
/* 332 */                     writeLog("=======当前人员类型=========" + settype);
/* 333 */                     if (settype == 1 || settype == 3) {
/* 334 */                       needToSendList.remove(uid);
/* 335 */                       writeLog("=======不需要推送的人员=========" + uid + ",选择类型===" + settype);
/*     */                     } else {
/* 337 */                       writeLog("=======选择实时推送的人员为=========" + uid);
/*     */                     } 
/* 339 */                     writeLog("\n=================\n");
/*     */                   } 
/* 341 */                   if (needToSendList.size() > 0) {
/* 342 */                     for (i = 0; i < needToSendList.size(); i++) {
/* 343 */                       writeLog("=======还需要推送的人员有=========" + needToSendList.get(i));
/*     */                     }
/*     */                     
/* 346 */                     this.touserloginidList = needToSendList;
/*     */                   } 
/*     */                 } else {
/* 349 */                   if (ifGXZY == 1) {
/* 350 */                     JSONArray ja = new JSONArray();
/* 351 */                     JSONObject jo1 = new JSONObject();
/* 352 */                     jo1.put("value", workflowname);
/* 353 */                     jo1.put("color", "#000");
/* 354 */                     ja.add(jo1);
/* 355 */                     JSONObject jo2 = new JSONObject();
/* 356 */                     jo2.put("value", nodeName);
/* 357 */                     jo2.put("color", "#000");
/* 358 */                     ja.add(jo2);
/* 359 */                     map.put("mpNewsJSONArray", ja.toString());
/*     */                   } 
/* 361 */                   map.put("requesttype", (new StringBuilder(String.valueOf(this.requesttype))).toString());
/*     */                 } 
/*     */ 
/*     */                 
/* 365 */                 ResourceComInfo rc = new ResourceComInfo();
/* 366 */                 JSONObject wfJson = new JSONObject();
/* 367 */                 wfJson.put("wf-creater-id", creater);
/* 368 */                 wfJson.put("wf-creater-loginid", rc.getLoginID(creater));
/* 369 */                 wfJson.put("wf-creater-workcode", "");
/* 370 */                 wfJson.put("wf-createdate", createdate);
/* 371 */                 wfJson.put("wf-createtime", createtime);
/* 372 */                 wfJson.put("wf-nodeName", nodeName);
/* 373 */                 map.put("wfJson", wfJson.toString());
/*     */ 
/*     */                 
/* 376 */                 if (fad.getIfbatchsend() == 1 && this.touserloginidList.size() > fad.getBatchsendnum()) {
/* 377 */                   String sendtime = "";
/* 378 */                   List<List<String>> sendIdList = splitList(this.touserloginidList, (fad.getBatchsendnum() == 0 || fad.getBatchsendnum() > 898) ? 898 : fad.getBatchsendnum());
/* 379 */                   for (List<String> idList : sendIdList) {
/* 380 */                     if (idList != null && idList.size() > 0) {
/* 381 */                       String receiveusers = JSONArray.fromObject(idList).toString();
/* 382 */                       String otherinfo = JSONObject.fromObject(map).toString();
/* 383 */                       SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
/* 384 */                       String nowDate = sdf.format(Long.valueOf(System.currentTimeMillis()));
/* 385 */                       if ("".equals(sendtime)) {
/* 386 */                         sendtime = sdf.format(Long.valueOf(System.currentTimeMillis()));
/*     */                       } else {
/*     */                         try {
/* 389 */                           int batchsendinterval = (fad.getBatchsendinterval() < 5) ? 5 : fad.getBatchsendinterval();
/* 390 */                           sendtime = sdf.format(Long.valueOf(sdf.parse(sendtime).getTime() + (batchsendinterval * 60 * 1000)));
/* 391 */                         } catch (ParseException e) {
/* 392 */                           e.printStackTrace();
/*     */                         } 
/*     */                       } 
/* 395 */                       String sql = "insert into WX_MsgBatchRecord(receiveusers, tpids, dataid, content, type, otherinfo, createtime, sendtime) values ('" + 
/* 396 */                         receiveusers + "','" + tpids + "','" + this.ecmoduledataId + "','" + this.content + "',1,'" + otherinfo + "','" + nowDate + "','" + sendtime + "')";
/* 397 */                       ConnStatement ConnStatement = null;
/*     */                       try {
/* 399 */                         ConnStatement = new ConnStatement();
/* 400 */                         ConnStatement.setStatementSql(sql);
/* 401 */                         ConnStatement.executeUpdate();
/* 402 */                       } catch (Exception e) {
/* 403 */                         (new BaseBean()).writeLog("保存分批推送记录出错---数据id：" + this.ecmoduledataId + " 数据内容：" + Util.getMoreStr(this.content, 5, "...") + " 模板：" + tpids + " 异常信息：" + e.getMessage()); continue;
/*     */                       } finally {
/* 405 */                         if (ConnStatement != null) {
/* 406 */                           ConnStatement.close();
/*     */                         }
/*     */                       } 
/*     */                     } 
/*     */                   } 
/*     */                 } else {
/* 412 */                   InterfaceUtil.sendMsg(this.touserloginidList, tpids, this.ecmoduledataId, this.content, 1, map);
/*     */                 } 
/* 414 */                 tpids = "";
/*     */               } 
/*     */             } 
/*     */           } else {
/* 418 */             writeLog("=======flowList.size=========" + flowList);
/* 419 */             writeLog("=======flowList不符合推送要求,没有配置消息推送设置=========");
/*     */           } 
/*     */         } else {
/* 422 */           writeLog("=======this.requesttype不符合推送要求,当前为:" + this.requesttype + "=========");
/*     */         } 
/*     */       } else {
/* 425 */         writeLog("=======ecmodule不符合推送要求,当前为:" + this.ecmodule + ",或者没有接收人,接收人List长度为:" + this.touserloginidList.size() + "=========");
/*     */       } 
/* 427 */     } catch (Exception e) {
/* 428 */       e.printStackTrace();
/* 429 */       writeLog("=======推送程序异常=========" + e.getMessage());
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/wxinterface/SendMsgThread.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */