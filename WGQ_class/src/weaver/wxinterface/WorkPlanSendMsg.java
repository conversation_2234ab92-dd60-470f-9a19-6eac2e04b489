/*     */ package weaver.wxinterface;
/*     */ 
/*     */ import java.text.SimpleDateFormat;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import net.sf.json.JSONObject;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.file.Prop;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.company.DepartmentComInfo;
/*     */ import weaver.hrm.resource.ResourceComInfo;
/*     */ 
/*     */ 
/*     */ 
/*     */ public class WorkPlanSendMsg
/*     */   implements Runnable
/*     */ {
/*     */   private SimpleDateFormat sdf;
/*     */   private BaseBean bb;
/*     */   private FlowAndDoc fad;
/*     */   
/*     */   public WorkPlanSendMsg(FlowAndDoc fad) {
/*  25 */     this.fad = fad;
/*  26 */     this.bb = new BaseBean();
/*  27 */     this.sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
/*     */   }
/*     */ 
/*     */   
/*     */   public void run() {
/*  32 */     scanAndSend();
/*     */   }
/*     */   
/*     */   private FlowAndDoc scanAndSend() {
/*     */     try {
/*  37 */       String lastscantime = this.fad.getLastscantime();
/*  38 */       String nowDate = this.sdf.format(Long.valueOf(System.currentTimeMillis()));
/*  39 */       if (lastscantime == null || lastscantime.equals("")) {
/*  40 */         lastscantime = nowDate;
/*     */       }
/*  42 */       RecordSet rs = new RecordSet();
/*  43 */       this.fad.setLastscantime(nowDate);
/*  44 */       rs.execute("update WX_MsgRuleSetting set lastscantime='" + nowDate + "' where id =" + this.fad.getId());
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/*  55 */       String enddate = "";
/*  56 */       if ("sqlserver".equalsIgnoreCase(rs.getDBType())) {
/*  57 */         enddate = "(w.enddate+' '+w.endtime)";
/*  58 */       } else if ("mysql".equalsIgnoreCase(rs.getDBType())) {
/*  59 */         enddate = "(CONCAT(w.enddate,' ',w.endtime))";
/*     */       } else {
/*  61 */         enddate = "(w.enddate||' '||w.endtime)";
/*     */       } 
/*     */       
/*  64 */       String lastDate = lastscantime.substring(0, 10);
/*  65 */       String lastTime = lastscantime.substring(11);
/*  66 */       String sql = "select w.* from WorkPlan w where w.status = 0 and w.deleted = 0 and ";
/*  67 */       if (this.fad.getTypeids().equals("1")) {
/*  68 */         sql = String.valueOf(sql) + "(w.createdate>'" + lastDate + "' or (w.createdate='" + lastDate + "' and w.createtime>'" + lastTime + ":00'))";
/*  69 */         sql = String.valueOf(sql) + " and " + enddate + ">='" + nowDate + "'";
/*     */       } else {
/*     */         
/*  72 */         int beforeTime = this.fad.getIfrepeat();
/*  73 */         String dateLimit = this.sdf.format(Long.valueOf(System.currentTimeMillis() + (beforeTime * 60 * 1000)));
/*  74 */         String limitDate = dateLimit.substring(0, 10);
/*  75 */         String limitTime = dateLimit.substring(11);
/*  76 */         sql = String.valueOf(sql) + "(w.begindate >'" + lastDate + "' or (w.begindate='" + lastDate + "' and w.begintime>'" + lastTime + "'))" + 
/*  77 */           " and (w.begindate<'" + limitDate + "' or (w.begindate='" + limitDate + "' and w.begintime<'" + limitTime + "'))";
/*     */       } 
/*     */       
/*  80 */       if (!this.fad.getFlowsordocs().equals("")) {
/*  81 */         sql = String.valueOf(sql) + " and type_n in (" + this.fad.getFlowsordocs() + ")";
/*     */       }
/*  83 */       sql = String.valueOf(sql) + " and not exists (select 1 from WX_SCANLOG l where  l.reourceid = w.id and l.type = 5 and l.othertypes=" + 
/*  84 */         this.fad.getTypeids() + ")";
/*  85 */       rs.execute(sql);
/*     */       
/*  87 */       while (rs.next()) {
/*     */         try {
/*  89 */           int id = rs.getInt("id");
/*  90 */           String desc = "您有一个新的日程，请关注\n";
/*  91 */           if (!this.fad.getTypeids().equals("1")) {
/*  92 */             desc = "您有一个日程即将开始\n";
/*     */           }
/*  94 */           desc = String.valueOf(desc) + "日程名称：" + FormatMultiLang.format(rs.getString("name"), "7") + "\n开始时间：" + 
/*  95 */             rs.getString("begindate") + " " + rs.getString("begintime") + "\n结束时间：" + 
/*  96 */             rs.getString("enddate") + " " + rs.getString("endtime") + "\n内容描述：" + 
/*  97 */             FormatMultiLang.format(rs.getString("description"), "7");
/*  98 */           String content = FormatMultiLang.format(rs.getString("description"), "7");
/*     */ 
/*     */ 
/*     */           
/* 102 */           List<String> useridList = Util.TokenizerString(rs.getString("resourceid"), ",");
/*     */ 
/*     */ 
/*     */           
/* 106 */           Map<String, Object> map = new HashMap<String, Object>();
/* 107 */           map.put("otherType", this.fad.getTypeids());
/* 108 */           map.put("ifsendsub", Integer.valueOf(this.fad.getIfsendsub()));
/*     */           
/* 110 */           int ifZYZL = Util.getIntValue(Prop.getPropValue("zyzl_csdev", "ifZYZL"), 0);
/* 111 */           if (ifZYZL == 1) {
/* 112 */             String creater = Util.null2String(rs.getString("createrid"));
/* 113 */             ResourceComInfo rc = new ResourceComInfo();
/* 114 */             String deptId = rc.getDepartmentID(creater);
/* 115 */             DepartmentComInfo dc = new DepartmentComInfo();
/* 116 */             String deptName = dc.getDepartmentname(deptId);
/* 117 */             JSONObject jo = new JSONObject();
/* 118 */             jo.put("creater", rc.getLastname(creater));
/* 119 */             jo.put("createrDept", deptName);
/* 120 */             jo.put("createTime", String.valueOf(rs.getString("createdate")) + " " + rs.getString("createtime"));
/* 121 */             String doc_content = "";
/* 122 */             if (content.length() > 100) {
/* 123 */               doc_content = content.substring(0, 100);
/*     */             } else {
/* 125 */               doc_content = content;
/*     */             } 
/* 127 */             jo.put("content", doc_content);
/* 128 */             map.put("csdev_zyzl", jo.toString());
/*     */           } 
/* 130 */           InterfaceUtil.sendMsg(useridList, this.fad.getMsgtpids(), (new StringBuilder(String.valueOf(id))).toString(), desc, 5, map);
/* 131 */         } catch (Exception e) {
/* 132 */           this.bb.writeLog("日程：" + rs.getString("name") + "发送微信提醒失败", e);
/* 133 */           e.printStackTrace();
/*     */         }
/*     */       
/*     */       } 
/* 137 */     } catch (Exception e) {
/* 138 */       this.bb.writeLog("定时扫描日程并提醒程序异常", e);
/* 139 */       e.printStackTrace();
/*     */     } 
/* 141 */     return this.fad;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/wxinterface/WorkPlanSendMsg.class
 * Java compiler version: 6 (50.0)
 * JD-Core Version:       1.1.3
 */