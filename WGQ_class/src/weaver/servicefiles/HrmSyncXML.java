/*     */ package weaver.servicefiles;
/*     */ 
/*     */ import java.io.File;
/*     */ import java.io.FileOutputStream;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Hashtable;
/*     */ import java.util.List;
/*     */ import org.jdom.Content;
/*     */ import org.jdom.Document;
/*     */ import org.jdom.Element;
/*     */ import org.jdom.output.Format;
/*     */ import org.jdom.output.XMLOutputter;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.Util;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmSyncXML
/*     */   extends BaseBean
/*     */ {
/*  27 */   private Logger newlog = LoggerFactory.getLogger(HrmSyncXML.class);
/*     */   
/*  29 */   public GetXMLContent objXML = GetXMLContent.getObjXML();
/*     */   
/*  31 */   public String moduleid = "";
/*  32 */   public ArrayList pointArrayList = new ArrayList();
/*  33 */   public Hashtable dataHST = new Hashtable<>();
/*     */   
/*     */   public Element rootNodeElement;
/*     */   
/*     */   public HrmSyncXML() {
/*  38 */     this.rootNodeElement = this.objXML.getFileContent("hrmsyn.xml");
/*  39 */     if (this.rootNodeElement != null) init();
/*     */   
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void init() {
/*     */     try {
/*  51 */       this.moduleid = this.rootNodeElement.getAttributeValue("id");
/*     */       
/*  53 */       List list = this.rootNodeElement.getChildren("service-point");
/*     */       
/*  55 */       for (Element element1 : list) {
/*     */         
/*  57 */         String str1 = element1.getAttributeValue("id");
/*  58 */         this.pointArrayList.add(str1);
/*     */         
/*  60 */         Hashtable<Object, Object> hashtable = new Hashtable<>();
/*  61 */         Element element2 = element1.getChild("invoke-factory").getChild("construct");
/*  62 */         String str2 = element2.getAttributeValue("class");
/*  63 */         hashtable.put("construct", str2);
/*     */         
/*  65 */         this.dataHST.put(str1, hashtable);
/*     */       } 
/*  67 */     } catch (Exception exception) {
/*  68 */       this.newlog.error(exception);
/*  69 */       exception.printStackTrace();
/*     */     } 
/*     */   }
/*     */   
/*     */   public String getModuleId() {
/*  74 */     return this.moduleid;
/*     */   }
/*     */   
/*     */   public ArrayList getPointArrayList() {
/*  78 */     return this.pointArrayList;
/*     */   }
/*     */   
/*     */   public Hashtable getDataHST() {
/*  82 */     return this.dataHST;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void writeToXML(String paramString1, String paramString2) {
/*  89 */     Document document = new Document();
/*  90 */     Element element = new Element("module");
/*  91 */     element.setAttribute("id", "hrmsyn");
/*  92 */     element.setAttribute("version", "1.0.0");
/*     */     byte b;
/*  94 */     for (b = 0; b < this.pointArrayList.size(); b++) {
/*  95 */       String str = this.pointArrayList.get(b);
/*  96 */       if (!str.equals(paramString1)) {
/*     */ 
/*     */ 
/*     */         
/* 100 */         Element element1 = new Element("service-point");
/* 101 */         element1.setAttribute("id", str);
/* 102 */         element1.setAttribute("interface", "weaver.interfaces.hrm.HrmSynService");
/*     */         
/* 104 */         Element element2 = new Element("invoke-factory");
/* 105 */         Element element3 = new Element("construct");
/*     */         
/* 107 */         Hashtable hashtable = (Hashtable)this.dataHST.get(str);
/* 108 */         element3.setAttribute("class", (String)hashtable.get("construct"));
/*     */         
/* 110 */         element2.addContent((Content)element3);
/* 111 */         element1.addContent((Content)element2);
/* 112 */         element.addContent((Content)element1);
/*     */       } 
/*     */     } 
/* 115 */     b = 1;
/* 116 */     if (b != 0 && !"".equals(paramString2)) {
/* 117 */       Element element1 = new Element("service-point");
/* 118 */       element1.setAttribute("id", paramString1);
/* 119 */       element1.setAttribute("interface", "weaver.interfaces.hrm.HrmSynService");
/*     */       
/* 121 */       Element element2 = new Element("invoke-factory");
/* 122 */       Element element3 = new Element("construct");
/* 123 */       element3.setAttribute("class", paramString2);
/*     */ 
/*     */       
/* 126 */       element2.addContent((Content)element3);
/* 127 */       element1.addContent((Content)element2);
/* 128 */       element.addContent((Content)element1);
/*     */     } 
/*     */     
/* 131 */     document.addContent((Content)element);
/*     */     
/*     */     try {
/* 134 */       String str1 = Util.null2String(getPropValue("xmlfile", "xmlfilechart"));
/* 135 */       if ("".equals(str1.trim())) {
/* 136 */         str1 = GCONST.XML_UTF8;
/*     */       }
/* 138 */       String str2 = GetXMLContent.rootpath + File.separatorChar + "hrmsyn.xml";
/* 139 */       Format format = Format.getCompactFormat();
/* 140 */       format.setEncoding(str1);
/* 141 */       format.setIndent("    ");
/* 142 */       XMLOutputter xMLOutputter = new XMLOutputter(format);
/* 143 */       xMLOutputter.output(document, new FileOutputStream(str2));
/* 144 */     } catch (Exception exception) {
/* 145 */       this.newlog.error(exception);
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/servicefiles/HrmSyncXML.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */