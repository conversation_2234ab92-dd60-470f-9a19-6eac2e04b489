/*     */ package weaver.servicefiles;
/*     */ 
/*     */ import com.alibaba.fastjson.JSON;
/*     */ import com.api.integration.util.RecordSetObj;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Hashtable;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.Set;
/*     */ import org.jdom.Element;
/*     */ import org.quartz.CronExpression;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.StaticObj;
/*     */ import weaver.general.ThreadVarLanguage;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ import weaver.interfaces.cache.impl.IntegrationCache4Schedule;
/*     */ import weaver.interfaces.schedule.BaseCronJob;
/*     */ import weaver.interfaces.schedule.CronJob;
/*     */ import weaver.interfaces.schedule.QuartzAPIImpl;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ScheduleXML
/*     */   extends BaseBean
/*     */ {
/*     */   public static final String SCHEDULESETTING_INSERT = "INSERT INTO SCHEDULESETTING (POINTID,CLASSPATH,CRONEXPR,DESC_,RUNSTATUS,CREATEDATE,MODIFYDATE,CREATETIME,MODIFYTIME) VALUES (?,?,?,?,?,?,?,?,?)";
/*     */   public static final String SCHEDULESETTING_DETAIL_INSERT = "INSERT INTO SCHEDULESETTINGDETAIL (SCHEDULEDBID,ATTRNAME,ATTRVALUE,ISDATASOURCE) VALUES (?,?,?,?)";
/*     */   public static final String SCHEDULESETTING_DELETE = "DELETE FROM SCHEDULESETTING WHERE ID = ?";
/*     */   public static final String SCHEDULESETTING_DETAIL_DELETE = "DELETE FROM SCHEDULESETTINGDETAIL WHERE SCHEDULEDBID = ?";
/*     */   public static final String SELECT_SCHEDULESETTINGSTATUS_BY_ID = "SELECT RUNSTATUS FROM SCHEDULESETTING WHERE ID = ?";
/*     */   public static final String SELECT_SCHEDULESETTINGID_BY_POINITID = "SELECT ID FROM SCHEDULESETTING WHERE POINTID = ?";
/*     */   public static final String SELECT_SCHEDULESETTINGPOINITID_BY_ID = "SELECT POINTID FROM SCHEDULESETTING WHERE ID = ?";
/*     */   public static final String SELECT_SCHEDULESETTING_BY_POINITID = "SELECT * FROM SCHEDULESETTING WHERE POINTID = ?";
/*  44 */   private Logger newlog = LoggerFactory.getLogger(ScheduleXML.class);
/*     */ 
/*     */ 
/*     */   
/*  48 */   public GetXMLContent objXML = GetXMLContent.getObjXML();
/*     */ 
/*     */ 
/*     */   
/*  52 */   public String moduleid = "schedule";
/*     */ 
/*     */ 
/*     */   
/*  56 */   private static ArrayList pointArrayList = new ArrayList();
/*     */ 
/*     */ 
/*     */   
/*  60 */   public Hashtable dataHST = new Hashtable<>();
/*     */ 
/*     */ 
/*     */   
/*  64 */   public Hashtable dataHSTByid = new Hashtable<>();
/*     */ 
/*     */   
/*     */   public Element rootNodeElement;
/*     */   
/*     */   private static ScheduleXML scheduleXML;
/*     */ 
/*     */   
/*     */   public ScheduleXML() {
/*  73 */     init();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static ScheduleXML getScheduleXML() {
/*  82 */     if (scheduleXML != null && checkPointListSize()) {
/*  83 */       return scheduleXML;
/*     */     }
/*  85 */     scheduleXML = new ScheduleXML();
/*  86 */     return scheduleXML;
/*     */   }
/*     */   
/*     */   public static boolean checkPointListSize() {
/*  90 */     RecordSet recordSet = new RecordSet();
/*  91 */     recordSet.executeQuery("SELECT count(id) as id FROM SCHEDULESETTING", new Object[0]);
/*  92 */     recordSet.next();
/*  93 */     if (recordSet.getInt(1) != pointArrayList.size()) {
/*  94 */       return false;
/*     */     }
/*  96 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void init() {
/*     */     try {
/* 104 */       RecordSet recordSet = new RecordSet();
/* 105 */       recordSet.executeQuery("SELECT pointid FROM SCHEDULESETTING", new Object[0]);
/* 106 */       pointArrayList.clear();
/* 107 */       while (recordSet.next()) {
/* 108 */         pointArrayList.add(recordSet.getString(1));
/*     */       }
/* 110 */     } catch (Exception exception) {
/* 111 */       this.newlog.error(exception, exception);
/* 112 */       exception.printStackTrace();
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getModuleId() {
/* 122 */     return this.moduleid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public ArrayList getPointArrayList() {
/* 129 */     this; return pointArrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Hashtable getDataHST() {
/* 136 */     return this.dataHST;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Hashtable getDataHSTByid() {
/* 143 */     return this.dataHSTByid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void writeToScheduleXMLAdd(String paramString, Hashtable paramHashtable) {
/* 152 */     writeToScheduleXMLAddNew(paramString, paramHashtable);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String writeToScheduleXMLAddNew(String paramString, Hashtable paramHashtable) {
/* 162 */     return insert(paramString, paramHashtable);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String deleteSchedule(ArrayList paramArrayList) {
/* 170 */     return delete(paramArrayList);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String deleteSchedule(String paramString) {
/* 178 */     return deleteSchedule(Util.TokenizerString(paramString, ","));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String insert(String paramString, Hashtable paramHashtable) {
/* 188 */     long l = System.currentTimeMillis();
/* 189 */     this.newlog.info(">>>>>>>> ScheduleXML开始(insert)计划任务,pointid:" + paramString + ",hst:" + paramHashtable);
/* 190 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     try {
/* 192 */       if (checkExistByPointid(paramString))
/*     */       {
/* 194 */         return update(paramString, paramHashtable);
/*     */       }
/*     */       
/* 197 */       String str1 = Util.null2String(paramHashtable.get("construct"));
/* 198 */       String str2 = Util.null2String(paramHashtable.get("cronExpr"));
/* 199 */       if ("".equals(str1) || "".equals(str2)) {
/* 200 */         hashMap.put("status", Boolean.valueOf(false));
/* 201 */         hashMap.put("result", "Classpath or Cron" + SystemEnv.getHtmlLabelName(10003933, ThreadVarLanguage.getLang()) + "!");
/* 202 */         return JSON.toJSONString(hashMap);
/*     */       } 
/*     */       
/* 205 */       boolean bool = CronExpression.isValidExpression(str2);
/* 206 */       if (!bool) {
/* 207 */         hashMap.put("status", Boolean.valueOf(false));
/* 208 */         hashMap.put("result", "Cron" + SystemEnv.getHtmlLabelName(10003934, ThreadVarLanguage.getLang()) + "");
/* 209 */         this.newlog.error(">>>>>>>> cronexpr表达式不合法:" + str2);
/* 210 */         return JSON.toJSONString(hashMap);
/*     */       } 
/*     */       
/* 213 */       String str3 = checkClasspath(str1);
/* 214 */       if (!"".equals(str3)) {
/* 215 */         hashMap.put("status", Boolean.valueOf(false));
/* 216 */         hashMap.put("result", str3);
/* 217 */         this.newlog.error(">>>>>>>> checkClasspath exception:" + str3);
/* 218 */         return JSON.toJSONString(hashMap);
/*     */       } 
/*     */ 
/*     */       
/* 222 */       String str4 = "".equals(Util.null2String(paramHashtable.get("runstatus"))) ? "0" : (String)paramHashtable.get("runstatus");
/* 223 */       String str5 = Util.null2String(paramHashtable.get("desc_"));
/*     */ 
/*     */       
/* 226 */       String str6 = TimeUtil.getCurrentTimeString();
/* 227 */       String str7 = str6.substring(0, 10);
/* 228 */       String str8 = str6.substring(11);
/* 229 */       String str9 = str7;
/* 230 */       String str10 = str8;
/* 231 */       String str11 = str7;
/* 232 */       String str12 = str8;
/*     */ 
/*     */       
/* 235 */       RecordSet recordSet = new RecordSet();
/* 236 */       recordSet.executeUpdate("INSERT INTO SCHEDULESETTING (POINTID,CLASSPATH,CRONEXPR,DESC_,RUNSTATUS,CREATEDATE,MODIFYDATE,CREATETIME,MODIFYTIME) VALUES (?,?,?,?,?,?,?,?,?)", new Object[] { paramString, str1, str2, str5, str4, str9, str11, str10, str12 });
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 248 */       List<Map> list = (List)paramHashtable.get("detailMap");
/* 249 */       String str13 = getIdByPoint(paramString);
/* 250 */       if (list != null) {
/* 251 */         for (byte b = 0; b < list.size(); b++) {
/* 252 */           RecordSet recordSet1 = new RecordSet();
/* 253 */           Map map = list.get(b);
/* 254 */           String str14 = (String)map.get("attrname");
/* 255 */           String str15 = (String)map.get("attrvalue");
/* 256 */           int i = ((Integer)map.get("isdatasource")).intValue();
/*     */           
/* 258 */           recordSet1.executeUpdate("INSERT INTO SCHEDULESETTINGDETAIL (SCHEDULEDBID,ATTRNAME,ATTRVALUE,ISDATASOURCE) VALUES (?,?,?,?)", new Object[] { str13, str14, str15, 
/*     */ 
/*     */ 
/*     */                 
/* 262 */                 Integer.valueOf(i) });
/*     */         } 
/*     */       }
/*     */ 
/*     */       
/* 267 */       IntegrationCache4Schedule integrationCache4Schedule = new IntegrationCache4Schedule();
/* 268 */       BaseCronJob baseCronJob = (BaseCronJob)integrationCache4Schedule.getObjectFromDB(paramString);
/* 269 */       if (baseCronJob != null) {
/* 270 */         integrationCache4Schedule.setCache(paramString, baseCronJob);
/*     */       }
/* 272 */       QuartzAPIImpl quartzAPIImpl = new QuartzAPIImpl();
/* 273 */       if (!"1".equals(str4)) {
/* 274 */         quartzAPIImpl.startJob(baseCronJob);
/*     */       } else {
/* 276 */         quartzAPIImpl.stopJob(baseCronJob);
/*     */       } 
/* 278 */       hashMap.put("status", Boolean.valueOf(true));
/* 279 */       hashMap.put("result", "" + SystemEnv.getHtmlLabelName(130662, ThreadVarLanguage.getLang()) + "[" + paramString + "]" + SystemEnv.getHtmlLabelName(25008, ThreadVarLanguage.getLang()) + "!");
/* 280 */       hashMap.put("id", str13);
/* 281 */       if (pointArrayList.indexOf(paramString) == -1) {
/* 282 */         pointArrayList.add(paramString);
/*     */       }
/* 284 */     } catch (Exception exception) {
/* 285 */       hashMap.put("status", Boolean.valueOf(false));
/* 286 */       hashMap.put("result", "" + SystemEnv.getHtmlLabelName(130662, ThreadVarLanguage.getLang()) + "[" + paramString + "]" + SystemEnv.getHtmlLabelName(10003935, ThreadVarLanguage.getLang()) + "" + exception);
/* 287 */       this.newlog.error(">>>>>>>> ScheduleXML insert计划任务 exception:" + exception, exception);
/*     */     } 
/* 289 */     this.newlog.info(">>>>>>>> ScheduleXML insert end,耗时:" + (System.currentTimeMillis() - l));
/* 290 */     return JSON.toJSONString(hashMap);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String update(String paramString, Hashtable paramHashtable) {
/* 299 */     long l = System.currentTimeMillis();
/* 300 */     this.newlog.info(">>>>>>>> ScheduleXML开始(update)计划任务,pointid:" + paramString + ",hst:" + paramHashtable);
/* 301 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     try {
/* 303 */       if (paramHashtable.size() == 0) {
/* 304 */         hashMap.put("status", Boolean.valueOf(false));
/* 305 */         hashMap.put("result", "Hashtable" + SystemEnv.getHtmlLabelName(10003936, ThreadVarLanguage.getLang()) + "!");
/* 306 */         return JSON.toJSONString(hashMap);
/*     */       } 
/*     */       
/* 309 */       if (!checkExistByPointid(paramString)) {
/* 310 */         hashMap.put("status", Boolean.valueOf(false));
/* 311 */         hashMap.put("result", "" + SystemEnv.getHtmlLabelName(16539, ThreadVarLanguage.getLang()) + "[" + paramString + "]" + SystemEnv.getHtmlLabelName(23084, ThreadVarLanguage.getLang()) + "!");
/* 312 */         this.newlog.error(">>>>>>>> ScheduleXML 计划任务[" + paramString + "]不存在!");
/* 313 */         return JSON.toJSONString(hashMap);
/*     */       } 
/*     */ 
/*     */       
/* 317 */       String str1 = Util.null2String(paramHashtable.get("construct"));
/* 318 */       String str2 = Util.null2String(paramHashtable.get("cronExpr"));
/* 319 */       if (!"".equals(str1)) {
/* 320 */         String str = checkClasspath(str1);
/* 321 */         if (!"".equals(str)) {
/* 322 */           hashMap.put("status", Boolean.valueOf(false));
/* 323 */           hashMap.put("result", "" + SystemEnv.getHtmlLabelName(10003937, ThreadVarLanguage.getLang()) + "" + str);
/* 324 */           this.newlog.error(">>>>>>>> checkClasspath exception:" + str);
/* 325 */           return JSON.toJSONString(hashMap);
/*     */         } 
/*     */       } 
/* 328 */       if (!"".equals(str2)) {
/* 329 */         boolean bool = CronExpression.isValidExpression(str2);
/* 330 */         if (!bool) {
/* 331 */           hashMap.put("status", Boolean.valueOf(false));
/* 332 */           hashMap.put("result", "Cron" + SystemEnv.getHtmlLabelName(10003934, ThreadVarLanguage.getLang()) + "");
/* 333 */           this.newlog.error(">>>>>>>> Cron表达式不合法:" + str2);
/* 334 */           return JSON.toJSONString(hashMap);
/*     */         } 
/*     */       } 
/*     */       
/* 338 */       RecordSet recordSet = new RecordSet();
/*     */       
/* 340 */       String str3 = updateSQL(paramHashtable);
/* 341 */       recordSet.executeUpdate(str3, new Object[] { paramString });
/*     */ 
/*     */ 
/*     */       
/* 345 */       List<Map> list = (List)paramHashtable.get("detailMap");
/* 346 */       String str4 = getIdByPoint(paramString);
/* 347 */       if (list != null) {
/*     */         
/* 349 */         recordSet.executeUpdate("DELETE FROM SCHEDULESETTINGDETAIL WHERE SCHEDULEDBID = ?", new Object[] { str4 });
/* 350 */         for (byte b = 0; b < list.size(); b++) {
/* 351 */           RecordSet recordSet1 = new RecordSet();
/* 352 */           Map map = list.get(b);
/* 353 */           String str6 = (String)map.get("attrname");
/* 354 */           String str7 = (String)map.get("attrvalue");
/* 355 */           int i = ((Integer)map.get("isdatasource")).intValue();
/*     */           
/* 357 */           recordSet1.executeUpdate("INSERT INTO SCHEDULESETTINGDETAIL (SCHEDULEDBID,ATTRNAME,ATTRVALUE,ISDATASOURCE) VALUES (?,?,?,?)", new Object[] { str4, str6, str7, 
/*     */ 
/*     */ 
/*     */                 
/* 361 */                 Integer.valueOf(i) });
/*     */         } 
/*     */       } 
/*     */ 
/*     */ 
/*     */       
/* 367 */       IntegrationCache4Schedule integrationCache4Schedule = new IntegrationCache4Schedule();
/* 368 */       BaseCronJob baseCronJob = (BaseCronJob)integrationCache4Schedule.getObjectFromDB(paramString);
/* 369 */       if (baseCronJob != null) {
/* 370 */         integrationCache4Schedule.setCache("" + paramString, baseCronJob);
/*     */       }
/* 372 */       QuartzAPIImpl quartzAPIImpl = new QuartzAPIImpl();
/* 373 */       String str5 = getStatusById(str4);
/* 374 */       switch (str5) {
/*     */         case "0":
/* 376 */           quartzAPIImpl.stopJob(baseCronJob);
/* 377 */           quartzAPIImpl.startJob(baseCronJob);
/*     */           break;
/*     */         case "1":
/* 380 */           quartzAPIImpl.stopJob(baseCronJob);
/*     */           break;
/*     */         case "2":
/* 383 */           quartzAPIImpl.pauseJob(baseCronJob);
/*     */           break;
/*     */       } 
/*     */ 
/*     */     
/* 388 */     } catch (Exception exception) {
/* 389 */       hashMap.put("status", Boolean.valueOf(false));
/* 390 */       hashMap.put("result", "" + SystemEnv.getHtmlLabelName(130625, ThreadVarLanguage.getLang()) + "[" + paramString + "]" + SystemEnv.getHtmlLabelName(10003935, ThreadVarLanguage.getLang()) + "" + exception);
/* 391 */       this.newlog.error(">>>>>>>> ScheduleXML update 计划任务 exception:" + exception, exception);
/*     */     } 
/* 393 */     hashMap.put("status", Boolean.valueOf(true));
/* 394 */     hashMap.put("result", "" + SystemEnv.getHtmlLabelName(130625, ThreadVarLanguage.getLang()) + "[" + paramString + "]" + SystemEnv.getHtmlLabelName(25008, ThreadVarLanguage.getLang()) + "!");
/* 395 */     this.newlog.info(">>>>>>>> ScheduleXML update end,耗时:" + (System.currentTimeMillis() - l));
/* 396 */     return JSON.toJSONString(hashMap);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String delete(ArrayList<String> paramArrayList) {
/* 407 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 408 */     ArrayList<HashMap<Object, Object>> arrayList = new ArrayList();
/* 409 */     RecordSet recordSet = new RecordSet();
/* 410 */     if (paramArrayList.size() == 0) {
/* 411 */       hashMap.put("status", Boolean.valueOf(false));
/* 412 */       hashMap.put("result", "" + SystemEnv.getHtmlLabelName(10003938, ThreadVarLanguage.getLang()) + "," + SystemEnv.getHtmlLabelName(10003939, ThreadVarLanguage.getLang()) + "ArrayList" + SystemEnv.getHtmlLabelName(519236, ThreadVarLanguage.getLang()) + "!");
/* 413 */       return JSON.toJSONString(hashMap);
/*     */     } 
/*     */     
/* 416 */     for (byte b = 0; b < paramArrayList.size(); b++) {
/* 417 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 418 */       String str = paramArrayList.get(b);
/*     */       
/* 420 */       try { hashMap1.put("ponitid", str);
/* 421 */         hashMap1.put("status", Boolean.valueOf(true));
/* 422 */         hashMap1.put("result", "" + SystemEnv.getHtmlLabelName(130663, ThreadVarLanguage.getLang()) + "[" + str + "]" + SystemEnv.getHtmlLabelName(25008, ThreadVarLanguage.getLang()) + "!");
/*     */ 
/*     */         
/* 425 */         if (!checkExistByPointid(str))
/* 426 */         { hashMap.put("ponitid", str);
/* 427 */           hashMap.put("status", Boolean.valueOf(false));
/* 428 */           hashMap.put("result", "[" + str + "]" + SystemEnv.getHtmlLabelName(23084, ThreadVarLanguage.getLang()) + "!");
/* 429 */           arrayList.add(hashMap);
/*     */            }
/*     */         
/*     */         else
/*     */         
/* 434 */         { QuartzAPIImpl quartzAPIImpl = new QuartzAPIImpl();
/* 435 */           String str1 = "schedule." + Util.null2String(str);
/* 436 */           BaseCronJob baseCronJob = (BaseCronJob)StaticObj.getServiceByFullname(str1, CronJob.class);
/* 437 */           if (baseCronJob != null) {
/* 438 */             quartzAPIImpl.stopJob(baseCronJob);
/*     */           }
/*     */ 
/*     */           
/* 442 */           String str2 = getIdByPoint(str);
/* 443 */           recordSet.executeUpdate("DELETE FROM SCHEDULESETTINGDETAIL WHERE SCHEDULEDBID = ?", new Object[] { str2 });
/* 444 */           recordSet.executeUpdate("DELETE FROM SCHEDULESETTING WHERE ID = ?", new Object[] { str2 });
/* 445 */           pointArrayList.remove(str);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */           
/* 452 */           arrayList.add(hashMap1); }  } catch (Exception exception) { hashMap1.put("ponitid", str); hashMap1.put("status", Boolean.valueOf(false)); hashMap1.put("result", "delete schedule error ：" + exception); this.newlog.info("delete schedule[" + str + "] error：" + exception, exception); arrayList.add(hashMap1); }
/*     */     
/* 454 */     }  return JSON.toJSONString(hashMap);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String checkClasspath(String paramString) {
/*     */     try {
/* 464 */       Class<?> clazz = Class.forName(paramString);
/* 465 */       Object object = clazz.newInstance();
/* 466 */       if (!(object instanceof BaseCronJob) && !(object instanceof CronJob)) {
/* 467 */         return "" + SystemEnv.getHtmlLabelName(33614, ThreadVarLanguage.getLang()) + "weaver.interfaces.schedule.BaseCronJob" + SystemEnv.getHtmlLabelName(10003940, ThreadVarLanguage.getLang()) + "weaver.interfaces.schedule.CronJob" + SystemEnv.getHtmlLabelName(83001, ThreadVarLanguage.getLang()) + "";
/*     */       }
/* 469 */     } catch (Exception exception) {
/* 470 */       return paramString + "" + SystemEnv.getHtmlLabelName(10003941, ThreadVarLanguage.getLang()) + "" + exception;
/*     */     } 
/* 472 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean checkCron(String paramString) {
/* 481 */     boolean bool = CronExpression.isValidExpression(paramString);
/* 482 */     if (!bool) {
/* 483 */       return false;
/*     */     }
/* 485 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String updateSQL(Hashtable paramHashtable) {
/* 493 */     StringBuilder stringBuilder = new StringBuilder();
/* 494 */     stringBuilder.append(" update ").append("schedulesetting").append(" set ");
/* 495 */     Set set = paramHashtable.keySet();
/* 496 */     for (String str1 : set) {
/* 497 */       if ("detailMap".equals(str1)) {
/*     */         continue;
/*     */       }
/* 500 */       String str2 = (String)paramHashtable.get(str1);
/* 501 */       if ("construct".equals(str1)) {
/* 502 */         str1 = "classpath";
/*     */       }
/* 504 */       stringBuilder.append(str1 + "='" + str2 + "',");
/*     */     } 
/* 506 */     String str = stringBuilder.substring(0, stringBuilder.length() - 1);
/* 507 */     str = str + " where pointid = ?";
/* 508 */     this.newlog.info(">>>>>>>> updateSQL:" + str);
/* 509 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean checkExistByPointid(String paramString) {
/* 518 */     RecordSet recordSet = new RecordSet();
/* 519 */     recordSet.executeQuery("SELECT ID FROM SCHEDULESETTING WHERE POINTID = ?", new Object[] { paramString });
/* 520 */     if (recordSet.next()) {
/* 521 */       return true;
/*     */     }
/* 523 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getPointById(String paramString) {
/* 532 */     RecordSet recordSet = new RecordSet();
/* 533 */     recordSet.executeQuery("SELECT POINTID FROM SCHEDULESETTING WHERE ID = ?", new Object[] { paramString });
/* 534 */     if (recordSet.next()) {
/* 535 */       return recordSet.getString("pointid");
/*     */     }
/* 537 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getIdByPoint(String paramString) {
/* 547 */     RecordSet recordSet = new RecordSet();
/* 548 */     recordSet.executeQuery("SELECT ID FROM SCHEDULESETTING WHERE POINTID = ?", new Object[] { paramString });
/* 549 */     if (recordSet.next()) {
/* 550 */       return recordSet.getString("id");
/*     */     }
/* 552 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getStatusById(String paramString) {
/* 561 */     RecordSet recordSet = new RecordSet();
/* 562 */     recordSet.executeQuery("SELECT RUNSTATUS FROM SCHEDULESETTING WHERE ID = ?", new Object[] { paramString });
/* 563 */     if (recordSet.next()) {
/* 564 */       return recordSet.getString("RUNSTATUS");
/*     */     }
/* 566 */     return "";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, String> getByPointId(String paramString) {
/* 575 */     RecordSetObj recordSetObj = new RecordSetObj();
/* 576 */     recordSetObj.executeQuery("SELECT * FROM SCHEDULESETTING WHERE POINTID = ?", new Object[] { paramString });
/* 577 */     if (recordSetObj.next()) {
/* 578 */       return recordSetObj.getMap();
/*     */     }
/* 580 */     return null;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/servicefiles/ScheduleXML.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */