/*      */ package weaver.servicefiles;
/*      */ 
/*      */ import com.weaver.formmodel.util.StringHelper;
/*      */ import java.io.File;
/*      */ import java.io.FileOutputStream;
/*      */ import java.util.ArrayList;
/*      */ import java.util.HashMap;
/*      */ import java.util.Hashtable;
/*      */ import java.util.LinkedHashMap;
/*      */ import java.util.List;
/*      */ import java.util.Map;
/*      */ import org.jdom.Content;
/*      */ import org.jdom.Document;
/*      */ import org.jdom.Element;
/*      */ import org.jdom.output.Format;
/*      */ import org.jdom.output.XMLOutputter;
/*      */ import weaver.conn.ConnStatement;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.general.BaseBean;
/*      */ import weaver.general.GCONST;
/*      */ import weaver.general.TimeUtil;
/*      */ import weaver.general.Util;
/*      */ import weaver.integration.logging.Logger;
/*      */ import weaver.integration.logging.LoggerFactory;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ public class BrowserXML
/*      */   extends BaseBean
/*      */ {
/*   34 */   private Logger newlog = LoggerFactory.getLogger(BrowserXML.class);
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*   39 */   public GetXMLContent objXML = GetXMLContent.getObjXML();
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*   44 */   public static String moduleid = "";
/*      */ 
/*      */ 
/*      */   
/*   48 */   public static ArrayList pointArrayList = new ArrayList();
/*      */ 
/*      */ 
/*      */   
/*   52 */   public static ArrayList searchPointArrayList = new ArrayList();
/*      */ 
/*      */ 
/*      */   
/*   56 */   public static Hashtable dataHST = new Hashtable<>();
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private static boolean isInit = false;
/*      */ 
/*      */ 
/*      */   
/*      */   public static Element rootNodeElement;
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public BrowserXML(Element paramElement) {
/*   71 */     this; rootNodeElement = paramElement;
/*   72 */     this; if (rootNodeElement != null) initXML();
/*      */   
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public BrowserXML() {
/*   87 */     if (!isInit) {
/*   88 */       this.newlog.info("BrowserXML init start ....");
/*      */       
/*   90 */       init();
/*   91 */       isInit = true;
/*   92 */       this.newlog.info("BrowserXML init end ....");
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private Map<String, List<Map<String, String>>> getAllShowField() {
/*  101 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*      */     
/*  103 */     ArrayList<LinkedHashMap<Object, Object>> arrayList = new ArrayList();
/*  104 */     String str1 = null;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  110 */     String str2 = "select * from datashowparam order by mainid,id";
/*  111 */     RecordSet recordSet = new RecordSet();
/*  112 */     recordSet.executeQuery(str2, new Object[0]);
/*      */     
/*  114 */     while (recordSet.next()) {
/*  115 */       String str = Util.null2String(recordSet.getString("mainid"));
/*      */       
/*  117 */       if (str1 == null) {
/*  118 */         str1 = str;
/*      */       }
/*      */       
/*  121 */       if (!str1.equals(str)) {
/*  122 */         hashMap.put(str1, arrayList);
/*  123 */         arrayList = new ArrayList();
/*  124 */         str1 = str;
/*      */       } 
/*  126 */       LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/*      */       
/*  128 */       linkedHashMap.put("fieldname", Util.null2String(recordSet.getString("fieldname")));
/*  129 */       linkedHashMap.put("searchname", Util.null2String(recordSet.getString("searchname")));
/*  130 */       linkedHashMap.put("transql", Util.null2String(recordSet.getString("transql")));
/*  131 */       linkedHashMap.put("isshowname", Util.null2String(recordSet.getString("isshowname")));
/*      */       
/*  133 */       arrayList.add(linkedHashMap);
/*      */     } 
/*  135 */     if (str1 != null && !str1.isEmpty()) {
/*  136 */       hashMap.put(str1, arrayList);
/*      */     }
/*  138 */     return (Map)hashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private Map<String, List<Map<String, String>>> getAllSearchField() {
/*  147 */     String str1 = null;
/*  148 */     ArrayList<LinkedHashMap<Object, Object>> arrayList = new ArrayList();
/*      */     
/*  150 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  157 */     String str2 = "select * from datasearchparam order by mainid,id";
/*  158 */     RecordSet recordSet = new RecordSet();
/*  159 */     recordSet.executeQuery(str2, new Object[0]);
/*      */     
/*  161 */     while (recordSet.next()) {
/*  162 */       String str = Util.null2String(recordSet.getString("mainid"));
/*  163 */       if (str1 == null) {
/*  164 */         str1 = str;
/*      */       }
/*  166 */       if (!str1.equals(str)) {
/*  167 */         hashMap.put(str1, arrayList);
/*  168 */         arrayList = new ArrayList();
/*  169 */         str1 = str;
/*      */       } 
/*      */       
/*  172 */       LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/*      */       
/*  174 */       linkedHashMap.put("fieldname", Util.null2String(recordSet.getString("fieldname")));
/*  175 */       linkedHashMap.put("searchname", Util.null2String(recordSet.getString("searchname")));
/*  176 */       linkedHashMap.put("fieldtype", Util.null2String(recordSet.getString("fieldtype")));
/*  177 */       linkedHashMap.put("wokflowfieldname", Util.null2String(recordSet.getString("wokflowfieldname")));
/*      */       
/*  179 */       arrayList.add(linkedHashMap);
/*      */     } 
/*  181 */     if (str1 != null && !str1.isEmpty()) {
/*  182 */       hashMap.put(str1, arrayList);
/*      */     }
/*  184 */     return (Map)hashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void init() {
/*      */     try {
/*  192 */       moduleid = "browser";
/*      */       
/*  194 */       boolean bool = false;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  201 */       Map<String, List<Map<String, String>>> map1 = getAllSearchField();
/*      */ 
/*      */       
/*  204 */       Map<String, List<Map<String, String>>> map2 = getAllShowField();
/*      */       
/*  206 */       RecordSet recordSet = new RecordSet();
/*  207 */       recordSet.executeSql("select * from datashowset order by id ");
/*      */ 
/*      */       
/*  210 */       while (recordSet.next()) {
/*      */         
/*  212 */         String str1 = Util.null2String(recordSet.getString("id"));
/*  213 */         String str2 = Util.null2String(recordSet.getString("showname"));
/*  214 */         String str3 = Util.null2String(recordSet.getString("showclass"));
/*      */         
/*  216 */         if ("2".equals(str3)) {
/*  217 */           searchPointArrayList.add(str2);
/*      */           continue;
/*      */         } 
/*  220 */         pointArrayList.add(str2);
/*      */         
/*  222 */         Hashtable<Object, Object> hashtable = new Hashtable<>();
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  227 */         hashtable.put("datafrom", Util.null2String(recordSet.getString("datafrom")));
/*      */         
/*  229 */         hashtable.put("wsurl", Util.null2String(recordSet.getString("wsurl")));
/*      */         
/*  231 */         hashtable.put("wsoperation", Util.null2String(recordSet.getString("wsoperation")));
/*      */         
/*  233 */         hashtable.put("keyfield", Util.null2String(recordSet.getString("keyfield")));
/*      */         
/*  235 */         hashtable.put("customhref", Util.null2String(recordSet.getString("customhref")));
/*      */         
/*  237 */         hashtable.put("wsworkname", Util.null2String(recordSet.getString("wsworkname")));
/*      */ 
/*      */         
/*  240 */         List list = map1.get(str1);
/*      */         
/*  242 */         if (list == null) {
/*  243 */           list = new ArrayList();
/*      */         }
/*      */         
/*  246 */         hashtable.put("searchfieldList", list);
/*      */ 
/*      */         
/*  249 */         list = map2.get(str1);
/*  250 */         if (list == null) {
/*  251 */           list = new ArrayList();
/*      */         }
/*  253 */         hashtable.put("showfieldList", list);
/*  254 */         hashtable.put("id", str1);
/*      */ 
/*      */ 
/*      */         
/*  258 */         String str4 = "2".equals(Util.null2String(recordSet.getString("showtype"))) ? "1" : "0";
/*  259 */         String str5 = "2".equals(Util.null2String(recordSet.getString("selecttype"))) ? "1" : "0";
/*      */         
/*  261 */         hashtable.put("ds", Util.null2String(recordSet.getString("datasourceid")));
/*  262 */         hashtable.put("name", Util.null2String(recordSet.getString("name")));
/*  263 */         hashtable.put("search", Util.null2String(recordSet.getString("sqltext")));
/*  264 */         hashtable.put("searchById", Util.null2s(recordSet.getString("searchById"), Util.null2String(recordSet.getString("sqltext1"))));
/*  265 */         hashtable.put("searchByName", Util.null2s(recordSet.getString("searchByName"), Util.null2String(recordSet.getString("sqltext2"))));
/*  266 */         hashtable.put("nameHeader", Util.null2String(recordSet.getString("nameHeader")));
/*  267 */         hashtable.put("descriptionHeader", Util.null2String(recordSet.getString("descriptionHeader")));
/*  268 */         hashtable.put("outPageURL", Util.null2String(recordSet.getString("showpageurl")));
/*  269 */         hashtable.put("href", Util.null2String(recordSet.getString("detailpageurl")));
/*  270 */         hashtable.put("from", Util.null2String(recordSet.getString("browserfrom")));
/*  271 */         hashtable.put("showtree", str4);
/*  272 */         hashtable.put("nodename", Util.null2String(recordSet.getString("showfield")));
/*  273 */         hashtable.put("parentid", Util.null2String(recordSet.getString("parentfield")));
/*  274 */         hashtable.put("ismutil", str5);
/*  275 */         hashtable.put("customid", Util.null2String(recordSet.getString("customid")));
/*      */         
/*  277 */         dataHST.put(str2, hashtable);
/*      */       } 
/*  279 */     } catch (Exception exception) {
/*  280 */       this.newlog.error(exception);
/*  281 */       exception.printStackTrace();
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void initXML() {
/*      */     try {
/*  290 */       moduleid = rootNodeElement.getAttributeValue("id");
/*      */       
/*  292 */       List list = rootNodeElement.getChildren("service-point");
/*      */       
/*  294 */       for (Element element1 : list) {
/*      */         
/*  296 */         String str1 = element1.getAttributeValue("id");
/*  297 */         pointArrayList.add(str1);
/*  298 */         Element element2 = element1.getChild("invoke-factory").getChild("construct");
/*      */         
/*  300 */         Hashtable<Object, Object> hashtable = new Hashtable<>();
/*  301 */         Element element3 = element2.getChild("set-service");
/*  302 */         String str2 = element3.getAttributeValue("property");
/*  303 */         String str3 = element3.getAttributeValue("service-id");
/*  304 */         hashtable.put(str2, str3);
/*      */         
/*  306 */         List list1 = element2.getChildren("set");
/*  307 */         for (Element element : list1) {
/*      */           
/*  309 */           String str4 = element.getAttributeValue("property");
/*  310 */           String str5 = element.getAttributeValue("value");
/*  311 */           hashtable.put(str4, str5);
/*      */         } 
/*  313 */         dataHST.put(str1, hashtable);
/*      */       } 
/*  315 */     } catch (Exception exception) {
/*  316 */       this.newlog.error(exception);
/*  317 */       exception.printStackTrace();
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getModuleId() {
/*  326 */     this; return moduleid;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public ArrayList getPointArrayList() {
/*  334 */     this; return pointArrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Hashtable getDataHST() {
/*  342 */     this; return dataHST;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<String> writeBrowserToBrowserXML(BrowserXML paramBrowserXML, Map paramMap) {
/*  352 */     Document document = new Document();
/*  353 */     Element element = new Element("module");
/*  354 */     element.setAttribute("id", "browser");
/*  355 */     element.setAttribute("version", "1.0.0");
/*  356 */     ArrayList<String> arrayList1 = paramBrowserXML.getPointArrayList();
/*  357 */     Hashtable hashtable = paramBrowserXML.getDataHST();
/*  358 */     ArrayList<String> arrayList2 = new ArrayList(); byte b;
/*  359 */     for (b = 0; b < pointArrayList.size(); b++) {
/*  360 */       String str1 = pointArrayList.get(b);
/*  361 */       if (arrayList1.contains(str1)) {
/*  362 */         arrayList2.add(str1);
/*      */       }
/*      */       
/*  365 */       Hashtable hashtable1 = (Hashtable)dataHST.get(str1);
/*  366 */       Element element1 = new Element("service-point");
/*  367 */       element1.setAttribute("id", str1);
/*  368 */       String str2 = Util.null2String((String)hashtable1.get("customid"));
/*  369 */       if (str2.equals("")) {
/*  370 */         String str = Util.null2String((String)hashtable1.get("outPageURL"));
/*  371 */         if (!str.equals("") && str.indexOf("customid=") != -1) {
/*  372 */           str2 = str.substring(str.indexOf("customid=") + "customid=".length());
/*  373 */           if (str2.indexOf("&") != -1) {
/*  374 */             str2 = str2.substring(0, str2.indexOf("&"));
/*      */           }
/*      */         } 
/*      */       } 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  382 */       element1.setAttribute("interface", "weaver.interfaces.workflow.browser.Browser");
/*      */       
/*  384 */       Element element2 = new Element("invoke-factory");
/*  385 */       Element element3 = new Element("construct");
/*  386 */       element3.setAttribute("class", "weaver.interfaces.workflow.browser.BaseBrowser");
/*      */       
/*  388 */       if (null != hashtable1) {
/*      */ 
/*      */         
/*  391 */         Element element4 = new Element("set");
/*  392 */         element4.setAttribute("property", "name");
/*  393 */         element4.setAttribute("value", Util.null2String((String)hashtable1.get("name")));
/*  394 */         element3.addContent((Content)element4);
/*      */         
/*  396 */         element4 = new Element("set-service");
/*  397 */         element4.setAttribute("property", "ds");
/*  398 */         element4.setAttribute("service-id", Util.null2String((String)hashtable1.get("ds")));
/*  399 */         element3.addContent((Content)element4);
/*      */         
/*  401 */         element4 = new Element("set");
/*  402 */         element4.setAttribute("property", "search");
/*  403 */         element4.setAttribute("value", Util.null2String((String)hashtable1.get("search")));
/*  404 */         element3.addContent((Content)element4);
/*      */         
/*  406 */         element4 = new Element("set");
/*  407 */         element4.setAttribute("property", "searchById");
/*  408 */         element4.setAttribute("value", Util.null2String((String)hashtable1.get("searchById")));
/*  409 */         element3.addContent((Content)element4);
/*      */         
/*  411 */         element4 = new Element("set");
/*  412 */         element4.setAttribute("property", "searchByName");
/*  413 */         element4.setAttribute("value", Util.null2String((String)hashtable1.get("searchByName")));
/*  414 */         element3.addContent((Content)element4);
/*      */         
/*  416 */         element4 = new Element("set");
/*  417 */         element4.setAttribute("property", "nameHeader");
/*  418 */         element4.setAttribute("value", Util.null2String((String)hashtable1.get("nameHeader")));
/*  419 */         element3.addContent((Content)element4);
/*      */         
/*  421 */         element4 = new Element("set");
/*  422 */         element4.setAttribute("property", "descriptionHeader");
/*  423 */         element4.setAttribute("value", Util.null2String((String)hashtable1.get("descriptionHeader")));
/*  424 */         element3.addContent((Content)element4);
/*      */         
/*  426 */         element4 = new Element("set");
/*  427 */         element4.setAttribute("property", "outPageURL");
/*  428 */         element4.setAttribute("value", Util.null2String((String)hashtable1.get("outPageURL")));
/*  429 */         element3.addContent((Content)element4);
/*      */         
/*  431 */         element4 = new Element("set");
/*  432 */         element4.setAttribute("property", "href");
/*  433 */         element4.setAttribute("value", Util.null2String((String)hashtable1.get("href")));
/*  434 */         element3.addContent((Content)element4);
/*      */         
/*  436 */         element4 = new Element("set");
/*  437 */         element4.setAttribute("property", "from");
/*  438 */         element4.setAttribute("value", Util.null2String((String)hashtable1.get("from")));
/*  439 */         element3.addContent((Content)element4);
/*      */         
/*  441 */         element4 = new Element("set");
/*  442 */         element4.setAttribute("property", "showtree");
/*  443 */         element4.setAttribute("value", Util.null2String((String)hashtable1.get("showtree")));
/*  444 */         element3.addContent((Content)element4);
/*      */         
/*  446 */         element4 = new Element("set");
/*  447 */         element4.setAttribute("property", "nodename");
/*  448 */         element4.setAttribute("value", Util.null2String((String)hashtable1.get("nodename")));
/*  449 */         element3.addContent((Content)element4);
/*      */         
/*  451 */         element4 = new Element("set");
/*  452 */         element4.setAttribute("property", "parentid");
/*  453 */         element4.setAttribute("value", Util.null2String((String)hashtable1.get("parentid")));
/*  454 */         element3.addContent((Content)element4);
/*      */         
/*  456 */         element4 = new Element("set");
/*  457 */         element4.setAttribute("property", "ismutil");
/*  458 */         element4.setAttribute("value", Util.null2String((String)hashtable1.get("ismutil")));
/*  459 */         element3.addContent((Content)element4);
/*      */         
/*  461 */         element4 = new Element("set");
/*  462 */         element4.setAttribute("property", "customid");
/*  463 */         element4.setAttribute("value", str2);
/*  464 */         element3.addContent((Content)element4);
/*      */ 
/*      */         
/*  467 */         element2.addContent((Content)element3);
/*  468 */         element1.addContent((Content)element2);
/*  469 */         element.addContent((Content)element1);
/*      */       } 
/*      */     } 
/*  472 */     for (b = 0; b < arrayList1.size(); b++) {
/*  473 */       String str = arrayList1.get(b);
/*  474 */       if (!arrayList2.contains(str)) {
/*      */ 
/*      */         
/*  477 */         Hashtable<String, String> hashtable1 = (Hashtable)hashtable.get(str);
/*  478 */         String str1 = Util.null2String(paramMap.get(hashtable1.get("customid")));
/*  479 */         if (!StringHelper.isEmpty(str1))
/*  480 */           hashtable1.put("customid", str1); 
/*  481 */         rootmoduleAddBrowserXML(element, str, hashtable1);
/*      */       } 
/*      */     } 
/*  484 */     document.addContent((Content)element);
/*      */     
/*      */     try {
/*  487 */       String str1 = Util.null2String(getPropValue("xmlfile", "xmlfilechart"));
/*  488 */       if ("".equals(str1.trim())) {
/*  489 */         str1 = GCONST.XML_UTF8;
/*      */       }
/*  491 */       String str2 = GetXMLContent.rootpath + File.separatorChar + "browser.xml";
/*  492 */       Format format = Format.getCompactFormat();
/*  493 */       format.setEncoding(str1);
/*  494 */       format.setIndent("    ");
/*  495 */       XMLOutputter xMLOutputter = new XMLOutputter(format);
/*  496 */       xMLOutputter.output(document, new FileOutputStream(str2));
/*      */       
/*  498 */       for (byte b1 = 0; b1 < arrayList1.size(); b1++) {
/*  499 */         String str = arrayList1.get(b1);
/*  500 */         if (!arrayList2.contains(str)) {
/*  501 */           this; pointArrayList.add(str);
/*      */ 
/*      */ 
/*      */           
/*  505 */           Hashtable<String, String> hashtable1 = (Hashtable)hashtable.get(str);
/*  506 */           String str3 = Util.null2String(paramMap.get(hashtable1.get("customid")));
/*  507 */           if (!StringHelper.isEmpty(str3))
/*  508 */             hashtable1.put("customid", str3); 
/*  509 */           this; dataHST.put(str, hashtable1);
/*  510 */           updateData(str);
/*      */         } 
/*  512 */       }  isInit = true;
/*      */     }
/*  514 */     catch (Exception exception) {
/*  515 */       this.newlog.error(exception);
/*      */     } 
/*  517 */     return arrayList2;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List<String> writeBrowserToBrowserXML(BrowserXML paramBrowserXML, Map paramMap1, Map paramMap2, Map paramMap3) {
/*  529 */     Document document = new Document();
/*  530 */     Element element = new Element("module");
/*  531 */     element.setAttribute("id", "browser");
/*  532 */     element.setAttribute("version", "1.0.0");
/*  533 */     ArrayList<String> arrayList1 = paramBrowserXML.getPointArrayList();
/*  534 */     Hashtable hashtable = paramBrowserXML.getDataHST();
/*  535 */     ArrayList<String> arrayList2 = new ArrayList();
/*  536 */     for (byte b = 0; b < arrayList1.size(); b++) {
/*  537 */       String str = arrayList1.get(b);
/*  538 */       if (pointArrayList.contains(str)) {
/*  539 */         arrayList2.add(str);
/*      */       
/*      */       }
/*  542 */       else if (!str.equals("")) {
/*  543 */         Map map = (Map)hashtable.get(str);
/*  544 */         ConnStatement connStatement = new ConnStatement();
/*  545 */         if (map != null) {
/*  546 */           String str1 = (String)map.get("ds");
/*  547 */           String str2 = (String)map.get("search");
/*  548 */           String str3 = (String)map.get("searchById");
/*  549 */           String str4 = (String)map.get("searchByName");
/*  550 */           String str5 = (String)map.get("nameHeader");
/*  551 */           String str6 = (String)map.get("descriptionHeader");
/*  552 */           String str7 = Util.null2String((String)map.get("outPageURL"));
/*  553 */           String str8 = Util.null2String((String)map.get("href"));
/*  554 */           String str9 = "";
/*  555 */           String str10 = "";
/*  556 */           String str11 = "";
/*  557 */           if (str8.indexOf("modeId=") > -1) {
/*  558 */             str9 = str8.split("modeId=")[0];
/*  559 */             str10 = str8.split("modeId=")[1];
/*  560 */             if (str10.indexOf("&") > -1) {
/*  561 */               str11 = str10.substring(0, str10.indexOf("&"));
/*  562 */               str10 = str10.substring(str10.indexOf("&"));
/*      */             } else {
/*  564 */               str11 = str10;
/*  565 */               str10 = "";
/*      */             } 
/*      */             
/*  568 */             str8 = str9 + "modeId=" + Util.null2String(paramMap3.get(str11)) + str10;
/*      */           } 
/*  570 */           if (str8.indexOf("formId=") > -1) {
/*  571 */             str9 = str8.split("formId=")[0];
/*  572 */             str10 = str8.split("formId=")[1];
/*  573 */             if (str10.indexOf("&") > -1) {
/*  574 */               str11 = str10.substring(0, str10.indexOf("&"));
/*  575 */               str10 = str10.substring(str10.indexOf("&"));
/*      */             } else {
/*  577 */               str11 = str10;
/*  578 */               str10 = "";
/*      */             } 
/*      */             
/*  581 */             str8 = str9 + "formId=" + Util.null2String(paramMap2.get(str11)) + str10;
/*      */           } 
/*  583 */           String str12 = Util.null2String((String)map.get("from"));
/*  584 */           String str13 = Util.null2String((String)map.get("showtree"));
/*  585 */           String str14 = Util.null2String((String)map.get("nodename"));
/*  586 */           String str15 = Util.null2String((String)map.get("parentid"));
/*  587 */           String str16 = Util.null2String((String)map.get("ismutil"));
/*  588 */           String str17 = Util.null2String((String)map.get("name"));
/*  589 */           String str18 = Util.null2String((String)map.get("customid"));
/*  590 */           str18 = Util.null2String(paramMap1.get(str18));
/*      */           
/*  592 */           String str19 = "1".equals(str13) ? "2" : "1";
/*  593 */           String str20 = "1".equals(str16) ? "2" : "1";
/*      */         } 
/*      */       } 
/*      */     } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  814 */     return arrayList2;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void rootmoduleAddBrowserXML(Element paramElement, String paramString, Hashtable<String, String> paramHashtable) {
/*  825 */     Element element1 = new Element("service-point");
/*  826 */     element1.setAttribute("id", paramString);
/*      */     
/*  828 */     element1.setAttribute("interface", "weaver.interfaces.workflow.browser.Browser");
/*      */     
/*  830 */     Element element2 = new Element("invoke-factory");
/*  831 */     Element element3 = new Element("construct");
/*  832 */     element3.setAttribute("class", "weaver.interfaces.workflow.browser.BaseBrowser");
/*      */     
/*  834 */     Element element4 = new Element("set");
/*  835 */     element4.setAttribute("property", "name");
/*  836 */     element4.setAttribute("value", Util.null2String((String)paramHashtable.get("name")));
/*  837 */     element3.addContent((Content)element4);
/*      */     
/*  839 */     element4 = new Element("set-service");
/*  840 */     element4.setAttribute("property", "ds");
/*  841 */     element4.setAttribute("service-id", Util.null2String((String)paramHashtable.get("ds")));
/*  842 */     element3.addContent((Content)element4);
/*      */     
/*  844 */     element4 = new Element("set");
/*  845 */     element4.setAttribute("property", "search");
/*  846 */     element4.setAttribute("value", Util.null2String((String)paramHashtable.get("search")));
/*  847 */     element3.addContent((Content)element4);
/*      */     
/*  849 */     element4 = new Element("set");
/*  850 */     element4.setAttribute("property", "searchById");
/*  851 */     element4.setAttribute("value", Util.null2String((String)paramHashtable.get("searchById")));
/*  852 */     element3.addContent((Content)element4);
/*      */     
/*  854 */     element4 = new Element("set");
/*  855 */     element4.setAttribute("property", "searchByName");
/*  856 */     element4.setAttribute("value", Util.null2String((String)paramHashtable.get("searchByName")));
/*  857 */     element3.addContent((Content)element4);
/*      */     
/*  859 */     element4 = new Element("set");
/*  860 */     element4.setAttribute("property", "nameHeader");
/*  861 */     element4.setAttribute("value", Util.null2String((String)paramHashtable.get("nameHeader")));
/*  862 */     element3.addContent((Content)element4);
/*      */     
/*  864 */     element4 = new Element("set");
/*  865 */     element4.setAttribute("property", "descriptionHeader");
/*  866 */     element4.setAttribute("value", Util.null2String((String)paramHashtable.get("descriptionHeader")));
/*  867 */     element3.addContent((Content)element4);
/*      */     
/*  869 */     element4 = new Element("set");
/*  870 */     element4.setAttribute("property", "outPageURL");
/*  871 */     String str = Util.null2String((String)paramHashtable.get("outPageURL"));
/*  872 */     if (!str.equals("") && str.indexOf("customid=") != -1) {
/*  873 */       String str1 = str.substring(str.indexOf("customid=") + "customid=".length());
/*  874 */       if (str1.indexOf("&") != -1) {
/*  875 */         str1 = str1.substring(0, str1.indexOf("&"));
/*      */       }
/*  877 */       str = str.replace("customid=" + str1, "customid=" + Util.null2String((String)paramHashtable.get("customid")));
/*      */     } 
/*  879 */     paramHashtable.put("outPageURL", str);
/*  880 */     element4.setAttribute("value", str);
/*  881 */     element3.addContent((Content)element4);
/*      */     
/*  883 */     element4 = new Element("set");
/*  884 */     element4.setAttribute("property", "href");
/*  885 */     element4.setAttribute("value", Util.null2String(paramHashtable.get("href")));
/*  886 */     element3.addContent((Content)element4);
/*      */     
/*  888 */     element4 = new Element("set");
/*  889 */     element4.setAttribute("property", "from");
/*  890 */     element4.setAttribute("value", Util.null2String(paramHashtable.get("from")));
/*  891 */     element3.addContent((Content)element4);
/*      */     
/*  893 */     element4 = new Element("set");
/*  894 */     element4.setAttribute("property", "showtree");
/*  895 */     element4.setAttribute("value", Util.null2String(paramHashtable.get("showtree")));
/*  896 */     element3.addContent((Content)element4);
/*      */     
/*  898 */     element4 = new Element("set");
/*  899 */     element4.setAttribute("property", "nodename");
/*  900 */     element4.setAttribute("value", Util.null2String(paramHashtable.get("nodename")));
/*  901 */     element3.addContent((Content)element4);
/*      */     
/*  903 */     element4 = new Element("set");
/*  904 */     element4.setAttribute("property", "parentid");
/*  905 */     element4.setAttribute("value", Util.null2String(paramHashtable.get("parentid")));
/*  906 */     element3.addContent((Content)element4);
/*      */     
/*  908 */     element4 = new Element("set");
/*  909 */     element4.setAttribute("property", "ismutil");
/*  910 */     element4.setAttribute("value", Util.null2String(paramHashtable.get("ismutil")));
/*  911 */     element3.addContent((Content)element4);
/*      */     
/*  913 */     element4 = new Element("set");
/*  914 */     element4.setAttribute("property", "customid");
/*  915 */     element4.setAttribute("value", Util.null2String(paramHashtable.get("customid")));
/*  916 */     element3.addContent((Content)element4);
/*      */     
/*  918 */     element2.addContent((Content)element3);
/*  919 */     element1.addContent((Content)element2);
/*  920 */     paramElement.addContent((Content)element1);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void writeToBrowserXMLAdd(String paramString, Hashtable paramHashtable) {
/*  929 */     writeToBrowserXMLAddNew(paramString, paramHashtable);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean writeToBrowserXMLAddNew(String paramString, Hashtable paramHashtable) {
/*  937 */     boolean bool = true;
/*  938 */     String str = Thread.currentThread().getStackTrace()[2].getClassName();
/*  939 */     if (paramString.equals("")) {
/*  940 */       return false;
/*      */     }
/*  942 */     if (searchPointArrayList.contains(paramString)) {
/*  943 */       this.newlog.error("Pointid:" + paramString + ",showclass:2");
/*  944 */       return false;
/*      */     } 
/*  946 */     ConnStatement connStatement = new ConnStatement();
/*  947 */     if (paramHashtable != null) {
/*  948 */       RecordSet recordSet = new RecordSet();
/*  949 */       recordSet.executeQuery("select id from datashowset where showname=? ", new Object[] { paramString });
/*  950 */       String str1 = "";
/*  951 */       if (recordSet.next()) {
/*  952 */         this.newlog.info("浏览框（" + paramString + "）,已存在，更新");
/*  953 */         str1 = Util.null2String(recordSet.getString(1));
/*      */       } 
/*  955 */       String str2 = str1;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  961 */       String str3 = (String)paramHashtable.get("ds");
/*  962 */       String str4 = (String)paramHashtable.get("search");
/*  963 */       String str5 = (String)paramHashtable.get("searchById");
/*  964 */       String str6 = (String)paramHashtable.get("searchByName");
/*  965 */       String str7 = (String)paramHashtable.get("nameHeader");
/*  966 */       String str8 = (String)paramHashtable.get("descriptionHeader");
/*  967 */       String str9 = Util.null2String((String)paramHashtable.get("outPageURL"));
/*  968 */       String str10 = Util.null2String((String)paramHashtable.get("href"));
/*  969 */       String str11 = Util.null2String((String)paramHashtable.get("from"));
/*  970 */       String str12 = Util.null2String((String)paramHashtable.get("showtree"));
/*  971 */       String str13 = Util.null2String((String)paramHashtable.get("nodename"));
/*  972 */       String str14 = Util.null2String((String)paramHashtable.get("parentid"));
/*  973 */       String str15 = Util.null2String((String)paramHashtable.get("ismutil"));
/*  974 */       String str16 = Util.null2String((String)paramHashtable.get("name"));
/*  975 */       String str17 = Util.null2String((String)paramHashtable.get("customid"));
/*      */       
/*  977 */       if (!str9.equals("") && str17.equals("") && str9.indexOf("?customid=") > -1) {
/*  978 */         str17 = str9.substring(str9.indexOf("?customid=") + 10, str9.length());
/*      */       }
/*  980 */       if (str16.equals("")) str16 = paramString; 
/*  981 */       this.newlog.error("调用方：" + str + "; 方法：writeToBrowserXMLAdd ; parapointid:" + paramString + "; from:" + str11);
/*  982 */       String str18 = "1".equals(str12) ? "2" : "1";
/*  983 */       String str19 = "1".equals(str15) ? "2" : "1";
/*      */ 
/*      */       
/*  986 */       String str20 = Util.null2String((String)paramHashtable.get("datafrom"));
/*  987 */       if (str20.equals("")) str20 = "1"; 
/*  988 */       String str21 = Util.null2String((String)paramHashtable.get("wsurl"));
/*  989 */       String str22 = Util.null2String((String)paramHashtable.get("wsoperation"));
/*  990 */       String str23 = Util.null2String((String)paramHashtable.get("keyfield"));
/*  991 */       String str24 = Util.null2String((String)paramHashtable.get("customhref"));
/*  992 */       String str25 = Util.null2String((String)paramHashtable.get("wsworkname"));
/*      */ 
/*      */       
/*  995 */       List<Map> list1 = null;
/*  996 */       List<Map> list2 = null;
/*      */       
/*  998 */       if (paramHashtable.containsKey("searchfieldList")) {
/*  999 */         String str26 = Util.null2String(paramHashtable.get("searchfieldList")).trim();
/* 1000 */         if (!"".equals(str26) && !"{}".equals(str26) && !"[]".equals(str26)) {
/* 1001 */           list1 = (List)paramHashtable.get("searchfieldList");
/*      */         }
/*      */       } 
/* 1004 */       if (paramHashtable.containsKey("showfieldList")) {
/* 1005 */         String str26 = Util.null2String(paramHashtable.get("showfieldList")).trim();
/* 1006 */         if (!"".equals(str26) && !"{}".equals(str26) && !"[]".equals(str26)) {
/* 1007 */           list2 = (List)paramHashtable.get("showfieldList");
/*      */         }
/*      */       } 
/*      */ 
/*      */       
/*      */       try {
/* 1013 */         String str26 = "";
/*      */ 
/*      */         
/* 1016 */         if (Util.getIntValue(str2) > 0) {
/*      */ 
/*      */           
/* 1019 */           str26 = "update datashowset set showname=?,name=?,typename=?,showclass=?,datafrom=?,datasourceid=?, sqltext=?,sqltext1=?,sqltext2=?,showtype=?,parentfield=?,showfield=?," + ("".equals(Util.null2String(str23)) ? "" : "keyfield=?,") + "detailpageurl=?,selecttype=?,showpageurl=?,browserfrom=?,customid=?,nameheader=?,descriptionheader=?,searchById=?,searchByName=?, wsurl=?,wsoperation=?,customhref=?,wsworkname=?,modifydate=?,modifytime=? where id=" + str2;
/*      */ 
/*      */         
/*      */         }
/*      */         else {
/*      */ 
/*      */ 
/*      */           
/* 1027 */           str26 = "INSERT INTO datashowset(showname,name,typename,showclass,datafrom,datasourceid,sqltext,sqltext1,sqltext2,showtype,parentfield,showfield," + ("".equals(Util.null2String(str23)) ? "" : "keyfield,") + "detailpageurl,selecttype,showpageurl,browserfrom,customid,nameheader,descriptionheader,searchById,searchByName,wsurl,wsoperation,customhref,wsworkname,modifydate,modifytime,createdate,createtime,unconditionalquery)  values(?,?,?,?,?,?,?,?,?,?,?,?," + ("".equals(Util.null2String(str23)) ? "" : "?,") + "?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,1)";
/*      */         } 
/*      */         
/* 1030 */         connStatement.setStatementSql(str26);
/* 1031 */         connStatement.setString(1, paramString);
/* 1032 */         connStatement.setString(2, str16);
/* 1033 */         connStatement.setString(3, "");
/* 1034 */         connStatement.setString(4, "1");
/* 1035 */         connStatement.setString(5, str20);
/* 1036 */         connStatement.setString(6, str3);
/* 1037 */         connStatement.setString(7, str4);
/* 1038 */         connStatement.setString(8, str5);
/* 1039 */         connStatement.setString(9, str6);
/* 1040 */         connStatement.setString(10, str18);
/* 1041 */         connStatement.setString(11, str14);
/* 1042 */         connStatement.setString(12, str13);
/* 1043 */         byte b = 0;
/* 1044 */         if ("".equals(Util.null2String(str23))) {
/*      */           
/* 1046 */           connStatement.setString(13, str10);
/* 1047 */           connStatement.setString(14, str19);
/* 1048 */           connStatement.setString(15, str9);
/* 1049 */           connStatement.setInt(16, Util.getIntValue(str11, 0));
/* 1050 */           connStatement.setInt(17, Util.getIntValue(str17, 0));
/* 1051 */           connStatement.setString(18, str7);
/* 1052 */           connStatement.setString(19, str8);
/* 1053 */           connStatement.setString(20, str5);
/* 1054 */           connStatement.setString(21, str6);
/* 1055 */           connStatement.setString(22, str21);
/* 1056 */           connStatement.setString(23, str22);
/* 1057 */           connStatement.setString(24, str24);
/* 1058 */           connStatement.setString(25, str25);
/* 1059 */           b = 25;
/*      */         } else {
/* 1061 */           connStatement.setString(13, str23);
/* 1062 */           connStatement.setString(14, str10);
/* 1063 */           connStatement.setString(15, str19);
/* 1064 */           connStatement.setString(16, str9);
/* 1065 */           connStatement.setInt(17, Util.getIntValue(str11, 0));
/* 1066 */           connStatement.setInt(18, Util.getIntValue(str17, 0));
/* 1067 */           connStatement.setString(19, str7);
/* 1068 */           connStatement.setString(20, str8);
/* 1069 */           connStatement.setString(21, str5);
/* 1070 */           connStatement.setString(22, str6);
/* 1071 */           connStatement.setString(23, str21);
/* 1072 */           connStatement.setString(24, str22);
/* 1073 */           connStatement.setString(25, str24);
/* 1074 */           connStatement.setString(26, str25);
/* 1075 */           b = 26;
/*      */         } 
/*      */         
/* 1078 */         String str27 = TimeUtil.getCurrentTimeString();
/* 1079 */         String str28 = str27.substring(0, 10);
/* 1080 */         String str29 = str27.substring(11);
/* 1081 */         connStatement.setString(++b, str28);
/* 1082 */         connStatement.setString(++b, str29);
/* 1083 */         if (Util.getIntValue(str2) <= 0) {
/* 1084 */           connStatement.setString(++b, str28);
/* 1085 */           connStatement.setString(++b, str29);
/*      */         } 
/* 1087 */         connStatement.executeUpdate();
/* 1088 */         if (Util.getIntValue(str2) < 1) {
/* 1089 */           connStatement.setStatementSql("select max(id) from datashowset where showname=?");
/* 1090 */           connStatement.setString(1, paramString);
/* 1091 */           connStatement.executeQuery();
/* 1092 */           if (connStatement.next())
/* 1093 */             str2 = connStatement.getString(1); 
/*      */         } 
/* 1095 */         if (list1 != null && list1.size() > 0 && Util.getIntValue(str2) > 0) {
/*      */           
/* 1097 */           recordSet.executeSql("delete from datasearchparam  where mainid=" + str2);
/* 1098 */           for (byte b1 = 0; b1 < list1.size(); b1++) {
/* 1099 */             Map map = list1.get(b1);
/*      */             
/* 1101 */             if (map != null && map.size() > 0) {
/* 1102 */               connStatement.setStatementSql("insert into datasearchparam(mainid,fieldname,searchname,fieldtype,wokflowfieldname) values(?,?,?,?,?)");
/* 1103 */               connStatement.setInt(1, Util.getIntValue(str2));
/* 1104 */               connStatement.setString(2, Util.null2String(map.get("fieldname")));
/* 1105 */               connStatement.setString(3, Util.null2String(map.get("searchname")));
/* 1106 */               connStatement.setString(4, Util.null2String(map.get("fieldtype")));
/* 1107 */               connStatement.setString(5, Util.null2String(map.get("wokflowfieldname")));
/* 1108 */               connStatement.executeUpdate();
/*      */             } 
/*      */           } 
/*      */         } 
/* 1112 */         if (list2 != null && list2.size() > 0) {
/*      */           
/* 1114 */           recordSet.executeSql("delete from datashowparam where mainid=" + str2);
/* 1115 */           for (byte b1 = 0; b1 < list2.size(); b1++) {
/* 1116 */             Map map = list2.get(b1);
/*      */             
/* 1118 */             if (map != null && map.size() > 0) {
/* 1119 */               connStatement.setStatementSql("insert into datashowparam(mainid,fieldname,searchname,transql,isshowname) values(?,?,?,?,?)");
/* 1120 */               connStatement.setInt(1, Util.getIntValue(str2));
/* 1121 */               connStatement.setString(2, Util.null2String(map.get("fieldname")));
/* 1122 */               connStatement.setString(3, Util.null2String(map.get("searchname")));
/* 1123 */               connStatement.setString(4, Util.null2String(map.get("transql")));
/* 1124 */               connStatement.setString(5, Util.null2String(map.get("isshowname")));
/* 1125 */               connStatement.executeUpdate();
/*      */             } 
/*      */           } 
/*      */         } 
/* 1129 */       } catch (Exception exception) {
/* 1130 */         this.newlog.error(exception);
/* 1131 */         exception.printStackTrace();
/* 1132 */         bool = false;
/*      */       } finally {
/* 1134 */         connStatement.close();
/*      */       } 
/*      */     } 
/*      */     
/* 1138 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void writeToBrowserXMLByProperties(String paramString1, String paramString2, String paramString3) {
/* 1375 */     Document document = new Document();
/* 1376 */     Element element = new Element("module");
/* 1377 */     element.setAttribute("id", "browser");
/* 1378 */     element.setAttribute("version", "1.0.0");
/*      */     
/* 1380 */     for (byte b = 0; b < pointArrayList.size(); b++) {
/* 1381 */       String str = pointArrayList.get(b);
/* 1382 */       Element element1 = new Element("service-point");
/* 1383 */       element1.setAttribute("id", str);
/* 1384 */       element1.setAttribute("interface", "weaver.interfaces.workflow.browser.Browser");
/*      */       
/* 1386 */       Element element2 = new Element("invoke-factory");
/* 1387 */       Element element3 = new Element("construct");
/* 1388 */       element3.setAttribute("class", "weaver.interfaces.workflow.browser.BaseBrowser");
/*      */       
/* 1390 */       Hashtable hashtable = (Hashtable)dataHST.get(str);
/* 1391 */       if (null != hashtable) {
/*      */ 
/*      */ 
/*      */         
/* 1395 */         Element element4 = new Element("set-service");
/* 1396 */         element4.setAttribute("property", "ds");
/* 1397 */         String str1 = Util.null2String((String)hashtable.get("ds"));
/* 1398 */         if (paramString1.equals("ds"))
/*      */         {
/* 1400 */           if (!"".equals(paramString2) && !str1.equals(""))
/*      */           {
/* 1402 */             if (paramString2.compareTo(str1) == 0)
/*      */             {
/* 1404 */               str1 = paramString3;
/*      */             }
/*      */           }
/*      */         }
/* 1408 */         element4.setAttribute("service-id", str1);
/* 1409 */         element3.addContent((Content)element4);
/*      */         
/* 1411 */         element4 = new Element("set");
/* 1412 */         element4.setAttribute("property", "name");
/* 1413 */         element4.setAttribute("value", Util.null2String((String)hashtable.get("name")));
/* 1414 */         element3.addContent((Content)element4);
/*      */         
/* 1416 */         element4 = new Element("set");
/* 1417 */         element4.setAttribute("property", "search");
/* 1418 */         element4.setAttribute("value", Util.null2String((String)hashtable.get("search")));
/* 1419 */         element3.addContent((Content)element4);
/*      */         
/* 1421 */         element4 = new Element("set");
/* 1422 */         element4.setAttribute("property", "searchById");
/* 1423 */         element4.setAttribute("value", Util.null2String((String)hashtable.get("searchById")));
/* 1424 */         element3.addContent((Content)element4);
/*      */         
/* 1426 */         element4 = new Element("set");
/* 1427 */         element4.setAttribute("property", "searchByName");
/* 1428 */         element4.setAttribute("value", Util.null2String((String)hashtable.get("searchByName")));
/* 1429 */         element3.addContent((Content)element4);
/*      */         
/* 1431 */         element4 = new Element("set");
/* 1432 */         element4.setAttribute("property", "nameHeader");
/* 1433 */         element4.setAttribute("value", Util.null2String((String)hashtable.get("nameHeader")));
/* 1434 */         element3.addContent((Content)element4);
/*      */         
/* 1436 */         element4 = new Element("set");
/* 1437 */         element4.setAttribute("property", "descriptionHeader");
/* 1438 */         element4.setAttribute("value", Util.null2String((String)hashtable.get("descriptionHeader")));
/* 1439 */         element3.addContent((Content)element4);
/*      */         
/* 1441 */         element4 = new Element("set");
/* 1442 */         element4.setAttribute("property", "outPageURL");
/* 1443 */         element4.setAttribute("value", Util.null2String((String)hashtable.get("outPageURL")));
/* 1444 */         element3.addContent((Content)element4);
/*      */         
/* 1446 */         element4 = new Element("set");
/* 1447 */         element4.setAttribute("property", "href");
/* 1448 */         element4.setAttribute("value", Util.null2String((String)hashtable.get("href")));
/* 1449 */         element3.addContent((Content)element4);
/*      */         
/* 1451 */         element4 = new Element("set");
/* 1452 */         element4.setAttribute("property", "from");
/* 1453 */         element4.setAttribute("value", Util.null2String((String)hashtable.get("from")));
/* 1454 */         element3.addContent((Content)element4);
/*      */         
/* 1456 */         element4 = new Element("set");
/* 1457 */         element4.setAttribute("property", "showtree");
/* 1458 */         element4.setAttribute("value", Util.null2String((String)hashtable.get("showtree")));
/* 1459 */         element3.addContent((Content)element4);
/*      */         
/* 1461 */         element4 = new Element("set");
/* 1462 */         element4.setAttribute("property", "nodename");
/* 1463 */         element4.setAttribute("value", Util.null2String((String)hashtable.get("nodename")));
/* 1464 */         element3.addContent((Content)element4);
/*      */         
/* 1466 */         element4 = new Element("set");
/* 1467 */         element4.setAttribute("property", "parentid");
/* 1468 */         element4.setAttribute("value", Util.null2String((String)hashtable.get("parentid")));
/* 1469 */         element3.addContent((Content)element4);
/*      */         
/* 1471 */         element4 = new Element("set");
/* 1472 */         element4.setAttribute("property", "ismutil");
/* 1473 */         element4.setAttribute("value", Util.null2String((String)hashtable.get("ismutil")));
/* 1474 */         element3.addContent((Content)element4);
/*      */         
/* 1476 */         element2.addContent((Content)element3);
/* 1477 */         element1.addContent((Content)element2);
/* 1478 */         element.addContent((Content)element1);
/*      */       } 
/*      */     } 
/* 1481 */     document.addContent((Content)element);
/*      */     
/*      */     try {
/* 1484 */       String str1 = Util.null2String(getPropValue("xmlfile", "xmlfilechart"));
/* 1485 */       if ("".equals(str1.trim())) {
/* 1486 */         str1 = GCONST.XML_UTF8;
/*      */       }
/* 1488 */       String str2 = GetXMLContent.rootpath + File.separatorChar + "browser.xml";
/* 1489 */       Format format = Format.getCompactFormat();
/* 1490 */       format.setEncoding(str1);
/* 1491 */       format.setIndent("    ");
/* 1492 */       XMLOutputter xMLOutputter = new XMLOutputter(format);
/* 1493 */       xMLOutputter.output(document, new FileOutputStream(str2));
/* 1494 */     } catch (Exception exception) {
/* 1495 */       this.newlog.error(exception);
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void writeToBrowserXMLEdit(ArrayList<String> paramArrayList1, ArrayList<Hashtable> paramArrayList2) {
/* 1505 */     String str = Thread.currentThread().getStackTrace()[2].getClassName();
/* 1506 */     this.newlog.error("调用方：" + str + "; 方法：writeToBrowserXMLEdit ");
/* 1507 */     for (byte b = 0; b < paramArrayList1.size(); b++) {
/* 1508 */       String str1 = paramArrayList1.get(b);
/* 1509 */       if (!str1.equals("")) {
/* 1510 */         Hashtable hashtable = paramArrayList2.get(b);
/*      */         
/* 1512 */         writeToBrowserXMLAdd(str1, hashtable);
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void writeToBrowserXMLDel(String paramString) {
/*      */     try {
/* 1721 */       RecordSet recordSet = new RecordSet();
/* 1722 */       recordSet.executeSql("delete from datashowset where showname='" + paramString + "' and showclass='1'");
/* 1723 */     } catch (Exception exception) {
/* 1724 */       this.newlog.error(exception);
/* 1725 */       exception.printStackTrace();
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void initData() {}
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void deleteData(String paramString) {
/*      */     try {
/* 2024 */       RecordSet recordSet = new RecordSet();
/* 2025 */       String str = "delete from datashowset where showname='" + paramString + "' and showclass=1";
/* 2026 */       recordSet.executeSql(str);
/* 2027 */     } catch (Exception exception) {
/* 2028 */       this.newlog.error(exception);
/* 2029 */       exception.printStackTrace();
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void updateData(String paramString) {}
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean isUsed(String paramString1, String paramString2, String paramString3) {
/* 2190 */     RecordSet recordSet = new RecordSet();
/* 2191 */     if ("1".equals(paramString2) && ("1".equals(paramString3) || "2".equals(paramString3))) {
/*      */       
/* 2193 */       String str = "select *  from (select b.id,b.workflowname,              (select fieldlable                 from workflow_fieldlable                where fieldid = ff.fieldid                  and ff.formid = formid                  and langurageid = 7) as fieldname         from workflow_base b, workflow_formdict fd, workflow_formfield ff        where fd.id = ff.fieldid          and ff.formid = b.formid          and b.isbill = 0          and fd.fielddbtype = 'browser." + paramString1 + "'       union all       select b.id,b.workflowname,              (select labelname                 from htmllabelinfo                where indexid = bf.fieldlabel                  and languageid = 7) as fieldname         from workflow_billfield bf, workflow_base b        where bf.billid = b.formid          and b.isbill = 1          and bf.fielddbtype = 'browser." + paramString1 + "') r        order by r.id ";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/* 2217 */       recordSet.executeSql(str);
/* 2218 */       if (recordSet.next())
/*      */       {
/* 2220 */         return true;
/*      */       }
/*      */     } 
/* 2223 */     return false;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean updateUseField(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6) {
/* 2237 */     RecordSet recordSet = new RecordSet();
/* 2238 */     if ("1".equals(paramString2) && ("1".equals(paramString3) || "2".equals(paramString3)) && "1".equals(paramString5) && ("1".equals(paramString6) || "2".equals(paramString6))) {
/*      */       
/* 2240 */       String str = "update workflow_formdict set fielddbtype='browser." + paramString4 + "' where fielddbtype = 'browser." + paramString1 + "'";
/* 2241 */       recordSet.executeSql(str);
/* 2242 */       str = "update workflow_billfield set fielddbtype='browser." + paramString4 + "' where fielddbtype = 'browser." + paramString1 + "'";
/*      */       
/* 2244 */       recordSet.executeSql(str);
/*      */     } 
/* 2246 */     return false;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void copyToBrowserXML(List<String> paramList, String paramString) {
/* 2255 */     Document document = new Document();
/* 2256 */     Element element = new Element("module");
/* 2257 */     element.setAttribute("id", "browser");
/* 2258 */     element.setAttribute("version", "1.0.0");
/*      */     
/* 2260 */     for (byte b = 0; b < paramList.size(); b++) {
/* 2261 */       String str = paramList.get(b);
/* 2262 */       for (byte b1 = 0; b1 < pointArrayList.size(); b1++) {
/* 2263 */         String str1 = pointArrayList.get(b1);
/* 2264 */         Hashtable hashtable = (Hashtable)dataHST.get(str1);
/* 2265 */         String str2 = Util.null2String((String)hashtable.get("customid"));
/* 2266 */         if (str2.equals(str)) {
/*      */           
/* 2268 */           Element element1 = new Element("service-point");
/* 2269 */           element1.setAttribute("id", str1);
/* 2270 */           if (str2.equals("")) {
/* 2271 */             String str3 = Util.null2String((String)hashtable.get("outPageURL"));
/* 2272 */             if (!str3.equals("") && str3.indexOf("customid=") != -1) {
/* 2273 */               str2 = str3.substring(str3.indexOf("customid=") + "customid=".length());
/* 2274 */               if (str2.indexOf("&") != -1) {
/* 2275 */                 str2 = str2.substring(0, str2.indexOf("&"));
/*      */               }
/*      */             } 
/*      */           } 
/*      */           
/* 2280 */           element1.setAttribute("interface", "weaver.interfaces.workflow.browser.Browser");
/*      */           
/* 2282 */           Element element2 = new Element("invoke-factory");
/* 2283 */           Element element3 = new Element("construct");
/* 2284 */           element3.setAttribute("class", "weaver.interfaces.workflow.browser.BaseBrowser");
/*      */           
/* 2286 */           if (null != hashtable)
/*      */           
/*      */           { 
/*      */             
/* 2290 */             Element element4 = new Element("set");
/* 2291 */             element4.setAttribute("property", "name");
/* 2292 */             element4.setAttribute("value", Util.null2String((String)hashtable.get("name")));
/* 2293 */             element3.addContent((Content)element4);
/*      */             
/* 2295 */             element4 = new Element("set-service");
/* 2296 */             element4.setAttribute("property", "ds");
/* 2297 */             element4.setAttribute("service-id", Util.null2String((String)hashtable.get("ds")));
/* 2298 */             element3.addContent((Content)element4);
/*      */             
/* 2300 */             element4 = new Element("set");
/* 2301 */             element4.setAttribute("property", "search");
/* 2302 */             element4.setAttribute("value", Util.null2String((String)hashtable.get("search")));
/* 2303 */             element3.addContent((Content)element4);
/*      */             
/* 2305 */             element4 = new Element("set");
/* 2306 */             element4.setAttribute("property", "searchById");
/* 2307 */             element4.setAttribute("value", Util.null2String((String)hashtable.get("searchById")));
/* 2308 */             element3.addContent((Content)element4);
/*      */             
/* 2310 */             element4 = new Element("set");
/* 2311 */             element4.setAttribute("property", "searchByName");
/* 2312 */             element4.setAttribute("value", Util.null2String((String)hashtable.get("searchByName")));
/* 2313 */             element3.addContent((Content)element4);
/*      */             
/* 2315 */             element4 = new Element("set");
/* 2316 */             element4.setAttribute("property", "nameHeader");
/* 2317 */             element4.setAttribute("value", Util.null2String((String)hashtable.get("nameHeader")));
/* 2318 */             element3.addContent((Content)element4);
/*      */             
/* 2320 */             element4 = new Element("set");
/* 2321 */             element4.setAttribute("property", "descriptionHeader");
/* 2322 */             element4.setAttribute("value", Util.null2String((String)hashtable.get("descriptionHeader")));
/* 2323 */             element3.addContent((Content)element4);
/*      */             
/* 2325 */             element4 = new Element("set");
/* 2326 */             element4.setAttribute("property", "outPageURL");
/* 2327 */             element4.setAttribute("value", Util.null2String((String)hashtable.get("outPageURL")));
/* 2328 */             element3.addContent((Content)element4);
/*      */             
/* 2330 */             element4 = new Element("set");
/* 2331 */             element4.setAttribute("property", "href");
/* 2332 */             element4.setAttribute("value", Util.null2String((String)hashtable.get("href")));
/* 2333 */             element3.addContent((Content)element4);
/*      */             
/* 2335 */             element4 = new Element("set");
/* 2336 */             element4.setAttribute("property", "from");
/* 2337 */             element4.setAttribute("value", Util.null2String((String)hashtable.get("from")));
/* 2338 */             element3.addContent((Content)element4);
/*      */             
/* 2340 */             element4 = new Element("set");
/* 2341 */             element4.setAttribute("property", "showtree");
/* 2342 */             element4.setAttribute("value", Util.null2String((String)hashtable.get("showtree")));
/* 2343 */             element3.addContent((Content)element4);
/*      */             
/* 2345 */             element4 = new Element("set");
/* 2346 */             element4.setAttribute("property", "nodename");
/* 2347 */             element4.setAttribute("value", Util.null2String((String)hashtable.get("nodename")));
/* 2348 */             element3.addContent((Content)element4);
/*      */             
/* 2350 */             element4 = new Element("set");
/* 2351 */             element4.setAttribute("property", "parentid");
/* 2352 */             element4.setAttribute("value", Util.null2String((String)hashtable.get("parentid")));
/* 2353 */             element3.addContent((Content)element4);
/*      */             
/* 2355 */             element4 = new Element("set");
/* 2356 */             element4.setAttribute("property", "ismutil");
/* 2357 */             element4.setAttribute("value", Util.null2String((String)hashtable.get("ismutil")));
/* 2358 */             element3.addContent((Content)element4);
/*      */             
/* 2360 */             element4 = new Element("set");
/* 2361 */             element4.setAttribute("property", "customid");
/* 2362 */             element4.setAttribute("value", str2);
/* 2363 */             element3.addContent((Content)element4);
/*      */ 
/*      */             
/* 2366 */             element2.addContent((Content)element3);
/* 2367 */             element1.addContent((Content)element2);
/* 2368 */             element.addContent((Content)element1); } 
/*      */         } 
/*      */       } 
/* 2371 */     }  document.addContent((Content)element);
/*      */     
/*      */     try {
/* 2374 */       String str1 = Util.null2String(getPropValue("xmlfile", "xmlfilechart"));
/* 2375 */       if ("".equals(str1.trim())) {
/* 2376 */         str1 = GCONST.XML_UTF8;
/*      */       }
/* 2378 */       String str2 = paramString + "browser.xml";
/* 2379 */       Format format = Format.getCompactFormat();
/* 2380 */       format.setEncoding(str1);
/* 2381 */       format.setIndent("    ");
/* 2382 */       XMLOutputter xMLOutputter = new XMLOutputter(format);
/* 2383 */       xMLOutputter.output(document, new FileOutputStream(str2));
/*      */ 
/*      */ 
/*      */     
/*      */     }
/* 2388 */     catch (Exception exception) {
/* 2389 */       this.newlog.error(exception);
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean reloadServicePointFromDB() {
/* 2451 */     return true;
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/servicefiles/BrowserXML.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */