/*     */ package weaver.servicefiles;
/*     */ 
/*     */ import java.net.InetAddress;
/*     */ import weaver.file.Prop;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.InitServiceXMLtoDB;
/*     */ import weaver.general.StaticObj;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ResetXMLFileCache
/*     */   extends BaseBean
/*     */ {
/*  33 */   private static Logger newlog = LoggerFactory.getLogger(ResetXMLFileCache.class);
/*     */ 
/*     */   
/*     */   public static void reloadServiceFiles() {
/*  37 */     BrowserXML browserXML = new BrowserXML();
/*  38 */     browserXML.reloadServicePointFromDB();
/*     */   }
/*     */   public static void resetCache() {
/*     */     try {
/*  42 */       String str1 = Prop.getPropValue(GCONST.getConfigFile(), "MainControlIP");
/*  43 */       String str2 = "";
/*     */       
/*     */       try {
/*  46 */         InetAddress inetAddress = InetAddress.getLocalHost();
/*  47 */         str2 = inetAddress.getHostAddress();
/*     */       }
/*  49 */       catch (Exception exception) {
/*     */         
/*  51 */         newlog.error("ResetXMLFileCache.resetCache()", exception);
/*     */       } 
/*     */       
/*  54 */       reloadServiceFiles();
/*  55 */       StaticObj.getInstance().removeObject("registry");
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/*  78 */       InitServiceXMLtoDB initServiceXMLtoDB = new InitServiceXMLtoDB();
/*  79 */       initServiceXMLtoDB.initCache();
/*  80 */     } catch (Exception exception) {
/*  81 */       newlog.error(exception);
/*     */     } 
/*     */   }
/*     */   public static void initCache() {
/*     */     try {
/*  86 */       String str1 = Prop.getPropValue(GCONST.getConfigFile(), "MainControlIP");
/*  87 */       String str2 = "";
/*     */       
/*     */       try {
/*  90 */         InetAddress inetAddress = InetAddress.getLocalHost();
/*  91 */         str2 = inetAddress.getHostAddress();
/*     */       }
/*  93 */       catch (Exception exception) {
/*     */         
/*  95 */         newlog.error("ResetXMLFileCache.initCache()", exception);
/*     */       } 
/*     */       
/*  98 */       reloadServiceFiles();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 121 */       InitServiceXMLtoDB initServiceXMLtoDB = new InitServiceXMLtoDB();
/* 122 */       initServiceXMLtoDB.initCache();
/* 123 */     } catch (Exception exception) {
/* 124 */       newlog.error(exception);
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/servicefiles/ResetXMLFileCache.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */