/*      */ package weaver.servicefiles;
/*      */ 
/*      */ import java.io.File;
/*      */ import java.io.FileOutputStream;
/*      */ import java.sql.Connection;
/*      */ import java.sql.DriverManager;
/*      */ import java.sql.ResultSet;
/*      */ import java.sql.Statement;
/*      */ import java.util.ArrayList;
/*      */ import java.util.HashMap;
/*      */ import java.util.Hashtable;
/*      */ import java.util.List;
/*      */ import java.util.Map;
/*      */ import java.util.Properties;
/*      */ import java.util.Set;
/*      */ import org.jdom.Content;
/*      */ import org.jdom.Document;
/*      */ import org.jdom.Element;
/*      */ import org.jdom.output.Format;
/*      */ import org.jdom.output.XMLOutputter;
/*      */ import weaver.conn.ConnStatement;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.general.BaseBean;
/*      */ import weaver.general.GCONST;
/*      */ import weaver.general.TimeUtil;
/*      */ import weaver.general.Util;
/*      */ import weaver.integration.logging.Logger;
/*      */ import weaver.integration.logging.LoggerFactory;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ public class ActionXML
/*      */   extends BaseBean
/*      */ {
/*   46 */   private Logger newlog = LoggerFactory.getLogger(ActionXML.class);
/*      */ 
/*      */ 
/*      */   
/*   50 */   public GetXMLContent objXML = GetXMLContent.getObjXML();
/*      */ 
/*      */ 
/*      */   
/*   54 */   public String moduleid = "";
/*      */ 
/*      */ 
/*      */   
/*   58 */   public ArrayList pointArrayList = new ArrayList();
/*      */ 
/*      */ 
/*      */   
/*   62 */   public Hashtable dataHST = new Hashtable<>();
/*      */ 
/*      */ 
/*      */   
/*   66 */   public Hashtable datasetHST = new Hashtable<>();
/*      */ 
/*      */ 
/*      */   
/*   70 */   public Hashtable dsHST = new Hashtable<>();
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Element rootNodeElement;
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public ActionXML(Element paramElement) {
/*   81 */     this.rootNodeElement = paramElement;
/*   82 */     if (this.rootNodeElement != null) initXML();
/*      */   
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public ActionXML() {
/*   90 */     init();
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public void init() {
/*      */     try {
/*   97 */       this.moduleid = "action";
/*   98 */       RecordSet recordSet1 = new RecordSet();
/*   99 */       RecordSet recordSet2 = new RecordSet();
/*  100 */       recordSet1.executeSql("select * from actionsetting order by id");
/*  101 */       while (recordSet1.next()) {
/*  102 */         String str1 = recordSet1.getString("id");
/*  103 */         String str2 = recordSet1.getString("actionname");
/*  104 */         String str3 = recordSet1.getString("actionclass");
/*      */         
/*  106 */         Hashtable<Object, Object> hashtable1 = new Hashtable<>();
/*  107 */         Hashtable<Object, Object> hashtable2 = new Hashtable<>();
/*  108 */         recordSet2.executeSql("select * from actionsettingdetail where actionid=" + str1);
/*  109 */         while (recordSet2.next()) {
/*  110 */           String str4 = recordSet2.getString("attrname");
/*  111 */           String str5 = recordSet2.getString("attrvalue");
/*  112 */           String str6 = Util.null2String(recordSet2.getString("isdatasource"));
/*  113 */           if (str6.equals("1")) {
/*  114 */             hashtable2.put(str4, str5); continue;
/*      */           } 
/*  116 */           hashtable1.put(str4, str5);
/*      */         } 
/*  118 */         this.pointArrayList.add(str2);
/*  119 */         this.dataHST.put(str2, str3);
/*  120 */         this.datasetHST.put(str2, hashtable1);
/*  121 */         this.dsHST.put(str2, hashtable2);
/*      */       } 
/*  123 */     } catch (Exception exception) {
/*  124 */       this.newlog.error(exception);
/*  125 */       exception.printStackTrace();
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public void initXML() {
/*      */     try {
/*  133 */       this.moduleid = this.rootNodeElement.getAttributeValue("id");
/*      */       
/*  135 */       List list = this.rootNodeElement.getChildren("service-point");
/*      */       
/*  137 */       for (Element element1 : list) {
/*      */         
/*  139 */         String str1 = element1.getAttributeValue("id");
/*  140 */         this.pointArrayList.add(str1);
/*  141 */         Element element2 = element1.getChild("invoke-factory").getChild("construct");
/*  142 */         String str2 = element2.getAttributeValue("class");
/*  143 */         List list1 = element2.getChildren("set");
/*      */         
/*  145 */         Hashtable<Object, Object> hashtable1 = new Hashtable<>();
/*  146 */         Hashtable<Object, Object> hashtable2 = new Hashtable<>();
/*  147 */         for (Element element : list1) {
/*      */           
/*  149 */           String str3 = element.getAttributeValue("property");
/*  150 */           if ("actionname".equals(str3)) {
/*      */             continue;
/*      */           }
/*  153 */           String str4 = element.getAttributeValue("value");
/*  154 */           hashtable1.put(str3, str4);
/*      */         } 
/*  156 */         List list2 = element1.getChild("invoke-factory").getChild("construct").getChildren("set-service");
/*  157 */         for (Element element : list2) {
/*      */           
/*  159 */           String str3 = element.getAttributeValue("property");
/*  160 */           if ("actionname".equals(str3)) {
/*      */             continue;
/*      */           }
/*  163 */           String str4 = element.getAttributeValue("service-id");
/*  164 */           hashtable2.put(str3, str4);
/*      */         } 
/*  166 */         this.dataHST.put(str1, str2);
/*  167 */         this.datasetHST.put(str1, hashtable1);
/*  168 */         this.dsHST.put(str1, hashtable2);
/*      */       } 
/*  170 */     } catch (Exception exception) {
/*  171 */       this.newlog.error(exception);
/*  172 */       exception.printStackTrace();
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getModuleId() {
/*  180 */     return this.moduleid;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public ArrayList getPointArrayList() {
/*  187 */     return this.pointArrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Hashtable getDataHST() {
/*  194 */     return this.dataHST;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void writeToActionXMLAdd(String paramString1, String paramString2, String paramString3) {
/*  204 */     writeToActionXMLAdd(paramString1, paramString2, paramString3, null, null, null);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void writeActionToActionXML(ActionXML paramActionXML) {
/*  212 */     ArrayList<String> arrayList = paramActionXML.getPointArrayList();
/*  213 */     ArrayList arrayList1 = new ArrayList();
/*  214 */     Hashtable hashtable1 = paramActionXML.getDataHST();
/*  215 */     Hashtable hashtable2 = paramActionXML.getDatasetHST();
/*  216 */     Hashtable hashtable3 = paramActionXML.getDsHST();
/*      */     
/*  218 */     for (byte b = 0; b < arrayList.size(); b++) {
/*  219 */       String str = arrayList.get(b);
/*  220 */       if (!this.pointArrayList.contains(str)) {
/*      */ 
/*      */         
/*  223 */         Hashtable hashtable4 = (Hashtable)hashtable1.get(str);
/*  224 */         String str1 = Util.null2String(hashtable4.get("construct"));
/*  225 */         Hashtable hashtable5 = (Hashtable)hashtable2.get(str);
/*  226 */         Hashtable hashtable6 = (Hashtable)hashtable3.get(str);
/*      */         
/*  228 */         String[] arrayOfString1 = null;
/*  229 */         String[] arrayOfString2 = null;
/*  230 */         String[] arrayOfString3 = null;
/*      */         
/*  232 */         Set set1 = hashtable5.keySet();
/*  233 */         String str2 = "";
/*  234 */         String str3 = "";
/*  235 */         String str4 = "";
/*  236 */         for (String str5 : set1) {
/*  237 */           str2 = str2 + "," + str5;
/*  238 */           str3 = str3 + "," + hashtable5.get(str5);
/*  239 */           str4 = str4 + ",0";
/*      */         } 
/*  241 */         Set set2 = hashtable6.keySet();
/*  242 */         for (String str5 : set2) {
/*  243 */           str2 = str2 + "," + str5;
/*  244 */           str3 = str3 + "," + hashtable6.get(str5);
/*  245 */           str4 = str4 + ",1";
/*      */         } 
/*  247 */         if (!str2.equals("")) {
/*  248 */           arrayOfString1 = Util.TokenizerString2(str2.substring(1), ",");
/*  249 */           arrayOfString2 = Util.TokenizerString2(str3.substring(1), ",");
/*  250 */           arrayOfString3 = Util.TokenizerString2(str4.substring(1), ",");
/*      */         } 
/*  252 */         writeToActionXMLAdd(str, str, str1, arrayOfString1, arrayOfString2, arrayOfString3);
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void rootmoduleAddActionXML(Element paramElement, String paramString1, String paramString2, String paramString3, String[] paramArrayOfString1, String[] paramArrayOfString2) {
/*  360 */     Element element1 = new Element("service-point");
/*  361 */     element1.setAttribute("id", paramString2);
/*  362 */     element1.setAttribute("interface", "weaver.interfaces.workflow.action.Action");
/*      */     
/*  364 */     Element element2 = new Element("invoke-factory");
/*  365 */     Element element3 = new Element("construct");
/*  366 */     element3.setAttribute("class", paramString3);
/*      */     
/*  368 */     if (null != paramArrayOfString1 && paramArrayOfString1.length > 0)
/*      */     {
/*  370 */       for (byte b = 0; b < paramArrayOfString1.length; b++) {
/*  371 */         String str1 = paramArrayOfString1[b];
/*  372 */         String str2 = paramArrayOfString2[b];
/*  373 */         if (!str1.equals(""))
/*      */         {
/*      */ 
/*      */           
/*  377 */           if (!"actionname".equals(str1))
/*      */           {
/*      */ 
/*      */             
/*  381 */             if ("ds".equals(str1)) {
/*  382 */               Element element = new Element("set-service");
/*  383 */               element.setAttribute("property", str1);
/*  384 */               element.setAttribute("service-id", str2);
/*  385 */               element3.addContent((Content)element);
/*      */             } else {
/*  387 */               Element element = new Element("set");
/*  388 */               element.setAttribute("property", str1);
/*  389 */               element.setAttribute("value", str2);
/*  390 */               element3.addContent((Content)element);
/*      */             } 
/*      */           }
/*      */         }
/*      */       } 
/*      */     }
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  400 */     element2.addContent((Content)element3);
/*  401 */     element1.addContent((Content)element2);
/*  402 */     paramElement.addContent((Content)element1);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void writeToActionXMLAdd(String paramString1, String paramString2, String paramString3, String paramString4, String[] paramArrayOfString1, String[] paramArrayOfString2, String[] paramArrayOfString3, String paramString5) {
/*  417 */     writeToActionXMLAddNew(paramString1, paramString2, paramString3, paramString4, paramArrayOfString1, paramArrayOfString2, paramArrayOfString3, paramString5);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean writeToActionXMLAddNew(String paramString1, String paramString2, String paramString3, String paramString4, String[] paramArrayOfString1, String[] paramArrayOfString2, String[] paramArrayOfString3, String paramString5) {
/*  432 */     boolean bool = true;
/*      */     try {
/*  434 */       StringBuffer stringBuffer = new StringBuffer();
/*  435 */       RecordSet recordSet = new RecordSet();
/*      */       
/*  437 */       String str1 = "";
/*  438 */       String str2 = TimeUtil.getCurrentTimeString();
/*  439 */       String str3 = str2.substring(0, 10);
/*  440 */       String str4 = str2.substring(11);
/*      */       
/*  442 */       String str5 = recordSet.getDBType();
/*  443 */       String str6 = "select id from actionsetting where lower(actionname)=?";
/*  444 */       String str7 = "";
/*  445 */       if (!paramString1.equals("")) {
/*  446 */         str7 = paramString1;
/*  447 */       } else if (!paramString2.equals("")) {
/*  448 */         str7 = paramString2;
/*      */       } 
/*  450 */       recordSet.executeQuery(str6, new Object[] { paramString3.toLowerCase() });
/*  451 */       recordSet.next();
/*  452 */       str1 = recordSet.getString(1);
/*      */       
/*  454 */       if (Util.getIntValue(str1, 0) > 0) {
/*      */         
/*  456 */         stringBuffer.append("update actionsetting set ");
/*      */         
/*  458 */         stringBuffer.append(" actionclass='").append(paramString4).append("'");
/*  459 */         stringBuffer.append(" ,actionshowname='").append(paramString2).append("'");
/*      */         
/*  461 */         stringBuffer.append(" ,ModifyDate='").append(str3).append("'");
/*  462 */         stringBuffer.append(" ,ModifyTime='").append(str4).append("'");
/*      */         
/*  464 */         stringBuffer.append(" where id=").append(str1);
/*  465 */         bool = recordSet.executeUpdate(stringBuffer.toString(), new Object[0]);
/*      */         
/*  467 */         if (!bool) {
/*  468 */           return bool;
/*      */         }
/*      */       } else {
/*  471 */         stringBuffer.append("insert into actionsetting (actionname,actionclass,CreateDate,CreateTime,ModifyDate,ModifyTime,actionshowname)");
/*  472 */         stringBuffer.append(" values('").append(paramString3).append("'");
/*  473 */         stringBuffer.append(" ,'").append(paramString4).append("'");
/*      */         
/*  475 */         stringBuffer.append(" ,'").append(str3).append("'");
/*  476 */         stringBuffer.append(" ,'").append(str4).append("'");
/*  477 */         stringBuffer.append(" ,'").append(str3).append("'");
/*  478 */         stringBuffer.append(" ,'").append(str4).append("'");
/*      */         
/*  480 */         stringBuffer.append(" ,'").append(paramString2).append("')");
/*  481 */         bool = recordSet.executeUpdate(stringBuffer.toString(), new Object[0]);
/*      */         
/*  483 */         if (!bool) {
/*  484 */           return bool;
/*      */         }
/*      */         
/*  487 */         recordSet.executeSql("select id from actionsetting where lower(actionname)='" + paramString3.toLowerCase() + "'");
/*  488 */         recordSet.next();
/*  489 */         str1 = recordSet.getString(1);
/*      */       } 
/*  491 */       if (Util.getIntValue(str1) > 0) {
/*      */         
/*  493 */         if (null != paramString5) {
/*  494 */           bool = updateJavacode(str1, paramString5);
/*  495 */           if (!bool) {
/*  496 */             return bool;
/*      */           }
/*      */         } 
/*      */         
/*  500 */         bool = recordSet.executeSql("delete from actionsettingdetail where actionid=" + str1);
/*  501 */         if (!bool) {
/*  502 */           return bool;
/*      */         }
/*      */         
/*  505 */         if (null != paramArrayOfString1 && paramArrayOfString1.length > 0)
/*  506 */           for (byte b = 0; b < paramArrayOfString1.length; b++) {
/*  507 */             String str8 = paramArrayOfString1[b];
/*  508 */             String str9 = " ".equals(paramArrayOfString2[b]) ? "" : paramArrayOfString2[b];
/*  509 */             str8 = str8.replace("'", "''");
/*  510 */             str9 = str9.replace("'", "''");
/*  511 */             String str10 = paramArrayOfString3[b];
/*  512 */             if (!str8.equals("")) {
/*  513 */               stringBuffer = new StringBuffer();
/*  514 */               stringBuffer.append("insert into actionsettingdetail(actionid,attrname,attrvalue,isdatasource) values(");
/*  515 */               stringBuffer.append(str1);
/*  516 */               stringBuffer.append(",'").append(str8).append("'");
/*  517 */               stringBuffer.append(",'").append(str9).append("'");
/*  518 */               stringBuffer.append(",'").append(str10).append("')");
/*  519 */               bool = recordSet.executeSql(stringBuffer.toString());
/*  520 */               if (!bool) {
/*  521 */                 return bool;
/*      */               }
/*      */             } 
/*      */           }  
/*      */       } 
/*  526 */     } catch (Exception exception) {
/*  527 */       this.newlog.error(exception);
/*  528 */       exception.printStackTrace();
/*  529 */       bool = false;
/*      */     } 
/*      */     
/*  532 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private boolean updateJavacode(String paramString1, String paramString2) {
/*  663 */     ConnStatement connStatement = null;
/*      */     try {
/*  665 */       connStatement = new ConnStatement();
/*      */       
/*  667 */       String str = "update actionsetting set javacode=? where id=?";
/*  668 */       connStatement.setStatementSql(str);
/*  669 */       connStatement.setString(1, paramString2);
/*  670 */       connStatement.setInt(2, Util.getIntValue(paramString1));
/*  671 */       connStatement.executeUpdate();
/*      */     
/*      */     }
/*  674 */     catch (Exception exception) {
/*  675 */       this.newlog.error("更新在线编辑aciton javacode error", exception);
/*  676 */       return false;
/*      */     } finally {
/*  678 */       if (null != connStatement) {
/*  679 */         connStatement.close();
/*      */       }
/*      */     } 
/*  682 */     return true;
/*      */   }
/*      */   
/*      */   public Map<String, String> getJavecode(String paramString) {
/*  686 */     Connection connection = null;
/*  687 */     Statement statement = null;
/*  688 */     ResultSet resultSet = null;
/*  689 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*      */     try {
/*  691 */       String str1 = getPropValue(GCONST.getConfigFile(), "DriverClasses");
/*  692 */       String str2 = GCONST.getServerName();
/*  693 */       String str3 = getPropValue(GCONST.getConfigFile(), str2 + ".url");
/*  694 */       String str4 = getPropValue(GCONST.getConfigFile(), str2 + ".user");
/*  695 */       String str5 = getPropValue(GCONST.getConfigFile(), str2 + ".password");
/*  696 */       Class.forName(str1);
/*  697 */       Properties properties = new Properties();
/*  698 */       properties.put("user", str4);
/*  699 */       properties.put("password", str5);
/*  700 */       properties.put("CHARSET", "ISO");
/*  701 */       connection = DriverManager.getConnection(str3, properties);
/*      */       
/*  703 */       statement = connection.createStatement();
/*      */       
/*  705 */       statement.execute(paramString);
/*      */       
/*  707 */       resultSet = statement.getResultSet();
/*  708 */       if (resultSet.next()) {
/*  709 */         hashMap.put("id", resultSet.getString("ID"));
/*  710 */         hashMap.put("actionname", resultSet.getString("ACTIONNAME"));
/*  711 */         hashMap.put("actionshowname", resultSet.getString("ACTIONSHOWNAME"));
/*  712 */         hashMap.put("subcompanyid", resultSet.getString("subcompanyid"));
/*  713 */         hashMap.put("javacode", resultSet.getString("JAVACODE"));
/*      */       }
/*      */     
/*  716 */     } catch (Exception exception) {
/*  717 */       exception.printStackTrace();
/*      */     } finally {
/*      */       try {
/*  720 */         resultSet.close();
/*  721 */         statement.close();
/*  722 */         connection.close();
/*  723 */       } catch (Exception exception) {}
/*      */     } 
/*  725 */     return (Map)hashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void writeToActionXMLAdd(String paramString1, String paramString2, String paramString3, String[] paramArrayOfString1, String[] paramArrayOfString2, String[] paramArrayOfString3) {
/*  737 */     writeToActionXMLAdd(paramString1, paramString2, paramString3, paramArrayOfString1, paramArrayOfString2, paramArrayOfString3);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean writeToActionXMLAddNew(String paramString1, String paramString2, String paramString3, String[] paramArrayOfString1, String[] paramArrayOfString2, String[] paramArrayOfString3) {
/*  751 */     return writeToActionXMLAddNew("", paramString1, paramString2, paramString3, paramArrayOfString1, paramArrayOfString2, paramArrayOfString3, (String)null);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void writeToActionXMLEdit(ArrayList paramArrayList) {
/*  758 */     Document document = new Document();
/*  759 */     Element element1 = new Element("module");
/*  760 */     element1.setAttribute("id", "action");
/*  761 */     element1.setAttribute("version", "1.0.0");
/*      */     
/*  763 */     Element element2 = new Element("dependency");
/*  764 */     element2.setAttribute("module-id", "datasource");
/*  765 */     element2.setAttribute("version", "1.0.0");
/*  766 */     element1.addContent((Content)element2);
/*      */     
/*  768 */     for (byte b = 0; b < this.pointArrayList.size(); b++) {
/*  769 */       String str = this.pointArrayList.get(b);
/*  770 */       if (paramArrayList.indexOf(str) >= 0) {
/*      */ 
/*      */ 
/*      */         
/*  774 */         Element element3 = new Element("service-point");
/*  775 */         element3.setAttribute("id", str);
/*  776 */         element3.setAttribute("interface", "weaver.interfaces.workflow.action.Action");
/*      */         
/*  778 */         Element element4 = new Element("invoke-factory");
/*  779 */         Element element5 = new Element("construct");
/*      */         
/*  781 */         String str1 = (String)this.dataHST.get(str);
/*  782 */         element5.setAttribute("class", str1);
/*      */         
/*  784 */         Hashtable hashtable = (Hashtable)this.datasetHST.get(str);
/*  785 */         if (null != hashtable && hashtable.size() > 0) {
/*      */           
/*  787 */           Set set = hashtable.keySet();
/*  788 */           if (null != set && set.size() > 0)
/*      */           {
/*  790 */             for (String str2 : set) {
/*      */ 
/*      */               
/*  793 */               String str3 = (String)hashtable.get(str2);
/*  794 */               if (str2.equals("")) {
/*      */                 continue;
/*      */               }
/*      */               
/*  798 */               if ("actionname".equals(str2)) {
/*      */                 continue;
/*      */               }
/*      */               
/*  802 */               if ("ds".equals(str2)) {
/*  803 */                 Element element6 = new Element("set-service");
/*  804 */                 element6.setAttribute("property", str2);
/*  805 */                 element6.setAttribute("service-id", str3);
/*  806 */                 element5.addContent((Content)element6); continue;
/*      */               } 
/*  808 */               Element element = new Element("set");
/*  809 */               element.setAttribute("property", str2);
/*  810 */               element.setAttribute("value", str3);
/*  811 */               element5.addContent((Content)element);
/*      */             } 
/*      */           }
/*      */         } 
/*      */ 
/*      */         
/*  817 */         element4.addContent((Content)element5);
/*  818 */         element3.addContent((Content)element4);
/*  819 */         element1.addContent((Content)element3);
/*      */       } 
/*  821 */     }  document.addContent((Content)element1);
/*      */     
/*      */     try {
/*  824 */       String str1 = Util.null2String(getPropValue("xmlfile", "xmlfilechart"));
/*  825 */       if ("".equals(str1.trim())) {
/*  826 */         str1 = GCONST.XML_UTF8;
/*      */       }
/*  828 */       String str2 = GetXMLContent.rootpath + File.separatorChar + "action.xml";
/*  829 */       Format format = Format.getCompactFormat();
/*  830 */       format.setEncoding(str1);
/*  831 */       format.setIndent("    ");
/*  832 */       XMLOutputter xMLOutputter = new XMLOutputter(format);
/*  833 */       xMLOutputter.output(document, new FileOutputStream(str2));
/*  834 */     } catch (Exception exception) {
/*  835 */       this.newlog.error(exception);
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void writeToActionXMLEdit(ArrayList<String> paramArrayList1, ArrayList<Hashtable> paramArrayList2, ArrayList<String[]> paramArrayList3, ArrayList<String[]> paramArrayList4) {
/*  846 */     Document document = new Document();
/*  847 */     Element element1 = new Element("module");
/*  848 */     element1.setAttribute("id", "action");
/*  849 */     element1.setAttribute("version", "1.0.0");
/*  850 */     Element element2 = new Element("dependency");
/*  851 */     element2.setAttribute("module-id", "datasource");
/*  852 */     element2.setAttribute("version", "1.0.0");
/*  853 */     element1.addContent((Content)element2);
/*      */     
/*  855 */     for (byte b = 0; b < paramArrayList1.size(); b++) {
/*  856 */       String str = paramArrayList1.get(b);
/*  857 */       if (!str.equals("")) {
/*  858 */         Hashtable hashtable = paramArrayList2.get(b);
/*      */         
/*  860 */         Element element3 = new Element("service-point");
/*  861 */         element3.setAttribute("id", str);
/*  862 */         element3.setAttribute("interface", "weaver.interfaces.workflow.action.Action");
/*      */         
/*  864 */         Element element4 = new Element("invoke-factory");
/*  865 */         Element element5 = new Element("construct");
/*      */         
/*  867 */         String str1 = (String)hashtable.get("classname");
/*  868 */         element5.setAttribute("class", str1);
/*      */         
/*  870 */         String[] arrayOfString1 = paramArrayList3.get(b);
/*  871 */         String[] arrayOfString2 = paramArrayList4.get(b);
/*  872 */         if (null != arrayOfString1 && arrayOfString1.length > 0)
/*      */         {
/*  874 */           for (byte b1 = 0; b1 < arrayOfString1.length; b1++) {
/*  875 */             String str2 = arrayOfString1[b1];
/*  876 */             String str3 = arrayOfString2[b1];
/*  877 */             if (!str2.equals(""))
/*      */             {
/*      */ 
/*      */               
/*  881 */               if (!"actionname".equals(str2))
/*      */               {
/*      */ 
/*      */                 
/*  885 */                 if ("ds".equals(str2)) {
/*  886 */                   Element element = new Element("set-service");
/*  887 */                   element.setAttribute("property", str2);
/*  888 */                   element.setAttribute("service-id", str3);
/*  889 */                   element5.addContent((Content)element);
/*      */                 } else {
/*  891 */                   Element element = new Element("set");
/*  892 */                   element.setAttribute("property", str2);
/*  893 */                   element.setAttribute("value", str3);
/*  894 */                   element5.addContent((Content)element);
/*      */                 }  }  } 
/*      */           } 
/*      */         }
/*  898 */         element4.addContent((Content)element5);
/*  899 */         element3.addContent((Content)element4);
/*  900 */         element1.addContent((Content)element3);
/*      */       } 
/*      */     } 
/*  903 */     document.addContent((Content)element1);
/*      */     
/*      */     try {
/*  906 */       String str1 = Util.null2String(getPropValue("xmlfile", "xmlfilechart"));
/*  907 */       if ("".equals(str1.trim())) {
/*  908 */         str1 = GCONST.XML_UTF8;
/*      */       }
/*  910 */       String str2 = GetXMLContent.rootpath + File.separatorChar + "action.xml";
/*  911 */       Format format = Format.getCompactFormat();
/*  912 */       format.setEncoding(str1);
/*  913 */       format.setIndent("    ");
/*  914 */       XMLOutputter xMLOutputter = new XMLOutputter(format);
/*  915 */       xMLOutputter.output(document, new FileOutputStream(str2));
/*  916 */     } catch (Exception exception) {
/*  917 */       this.newlog.error(exception);
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void writeToActionXMLDelete(String paramString) {
/*  925 */     Document document = new Document();
/*  926 */     Element element1 = new Element("module");
/*  927 */     element1.setAttribute("id", "action");
/*  928 */     element1.setAttribute("version", "1.0.0");
/*  929 */     Element element2 = new Element("dependency");
/*  930 */     element2.setAttribute("module-id", "datasource");
/*  931 */     element2.setAttribute("version", "1.0.0");
/*  932 */     element1.addContent((Content)element2);
/*      */     
/*  934 */     for (byte b = 0; b < this.pointArrayList.size(); b++) {
/*  935 */       String str = this.pointArrayList.get(b);
/*  936 */       if (!str.equals(paramString)) {
/*      */ 
/*      */ 
/*      */         
/*  940 */         Element element3 = new Element("service-point");
/*  941 */         element3.setAttribute("id", str);
/*  942 */         element3.setAttribute("interface", "weaver.interfaces.workflow.action.Action");
/*      */         
/*  944 */         Element element4 = new Element("invoke-factory");
/*  945 */         Element element5 = new Element("construct");
/*      */         
/*  947 */         String str1 = (String)this.dataHST.get(str);
/*  948 */         element5.setAttribute("class", str1);
/*      */         
/*  950 */         Hashtable hashtable = (Hashtable)this.datasetHST.get(str);
/*  951 */         if (null != hashtable && hashtable.size() > 0) {
/*      */           
/*  953 */           Set set = hashtable.keySet();
/*  954 */           if (null != set && set.size() > 0)
/*      */           {
/*  956 */             for (String str2 : set) {
/*      */ 
/*      */               
/*  959 */               String str3 = (String)hashtable.get(str2);
/*  960 */               if (str2.equals("")) {
/*      */                 continue;
/*      */               }
/*      */               
/*  964 */               if ("actionname".equals(str2)) {
/*      */                 continue;
/*      */               }
/*      */               
/*  968 */               if ("ds".equals(str2)) {
/*  969 */                 Element element6 = new Element("set-service");
/*  970 */                 element6.setAttribute("property", str2);
/*  971 */                 element6.setAttribute("service-id", str3);
/*  972 */                 element5.addContent((Content)element6); continue;
/*      */               } 
/*  974 */               Element element = new Element("set");
/*  975 */               element.setAttribute("property", str2);
/*  976 */               element.setAttribute("value", str3);
/*  977 */               element5.addContent((Content)element);
/*      */             } 
/*      */           }
/*      */         } 
/*      */ 
/*      */         
/*  983 */         element4.addContent((Content)element5);
/*  984 */         element3.addContent((Content)element4);
/*  985 */         element1.addContent((Content)element3);
/*      */       } 
/*      */     } 
/*  988 */     document.addContent((Content)element1);
/*      */     
/*      */     try {
/*  991 */       String str1 = Util.null2String(getPropValue("xmlfile", "xmlfilechart"));
/*  992 */       if ("".equals(str1.trim())) {
/*  993 */         str1 = GCONST.XML_UTF8;
/*      */       }
/*  995 */       String str2 = GetXMLContent.rootpath + File.separatorChar + "action.xml";
/*  996 */       Format format = Format.getCompactFormat();
/*  997 */       format.setEncoding(str1);
/*  998 */       format.setIndent("    ");
/*  999 */       XMLOutputter xMLOutputter = new XMLOutputter(format);
/* 1000 */       xMLOutputter.output(document, new FileOutputStream(str2));
/* 1001 */     } catch (Exception exception) {
/* 1002 */       this.newlog.error(exception);
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Hashtable getDatasetHST() {
/* 1011 */     return this.datasetHST;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setDatasetHST(Hashtable paramHashtable) {
/* 1019 */     this.datasetHST = paramHashtable;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void initAction() {}
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getActionName(String paramString) {
/* 1111 */     String str1 = "select actionshowname from actionsetting where actionname=?";
/* 1112 */     String str2 = "";
/* 1113 */     ConnStatement connStatement = null;
/*      */     
/*      */     try {
/* 1116 */       connStatement = new ConnStatement();
/* 1117 */       connStatement.setStatementSql(str1);
/* 1118 */       connStatement.setString(1, paramString);
/*      */       
/* 1120 */       connStatement.executeQuery();
/*      */       
/* 1122 */       if (connStatement.next())
/*      */       {
/* 1124 */         str2 = connStatement.getString("actionshowname");
/*      */       }
/*      */     }
/* 1127 */     catch (Exception exception) {
/*      */       
/* 1129 */       this.newlog.error(exception);
/*      */     }
/*      */     finally {
/*      */       
/* 1133 */       if (null != connStatement)
/*      */       {
/* 1135 */         connStatement.close();
/*      */       }
/*      */     } 
/* 1138 */     return str2;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int getActionDBid(String paramString) {
/* 1147 */     String str = "select id from actionsetting where actionname=?";
/* 1148 */     int i = 0;
/* 1149 */     ConnStatement connStatement = null;
/*      */     
/*      */     try {
/* 1152 */       connStatement = new ConnStatement();
/* 1153 */       connStatement.setStatementSql(str);
/* 1154 */       connStatement.setString(1, paramString);
/*      */       
/* 1156 */       connStatement.executeQuery();
/*      */       
/* 1158 */       if (connStatement.next())
/*      */       {
/* 1160 */         i = connStatement.getInt("id");
/*      */       }
/*      */     }
/* 1163 */     catch (Exception exception) {
/*      */       
/* 1165 */       this.newlog.error(exception);
/*      */     }
/*      */     finally {
/*      */       
/* 1169 */       if (null != connStatement)
/*      */       {
/* 1171 */         connStatement.close();
/*      */       }
/*      */     } 
/* 1174 */     return i;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String updateAction(String paramString1, String paramString2, String paramString3, String paramString4) {
/* 1186 */     return updateAction(paramString1, paramString2, paramString3, paramString4, (String[])null, (String[])null);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String updateAction(String paramString1, String paramString2, String paramString3, String paramString4, String[] paramArrayOfString1, String[] paramArrayOfString2) {
/* 1202 */     if (Util.getIntValue(paramString2) < 0) {
/* 1203 */       RecordSet recordSet = new RecordSet();
/* 1204 */       recordSet.executeSql("select id from actionsetting where actionname='" + paramString3 + "'");
/* 1205 */       recordSet.next();
/* 1206 */       paramString2 = recordSet.getString(1);
/*      */     } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/* 1324 */     return paramString2;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void copyToActionXML(List<String> paramList, String paramString) {
/* 1333 */     Document document = new Document();
/* 1334 */     Element element1 = new Element("module");
/* 1335 */     element1.setAttribute("id", "action");
/* 1336 */     element1.setAttribute("version", "1.0.0");
/* 1337 */     Element element2 = new Element("dependency");
/* 1338 */     element2.setAttribute("module-id", "datasource");
/* 1339 */     element2.setAttribute("version", "1.0.0");
/* 1340 */     element1.addContent((Content)element2);
/* 1341 */     for (byte b = 0; b < paramList.size(); b++) {
/* 1342 */       String str = paramList.get(b);
/* 1343 */       for (byte b1 = 0; b1 < this.pointArrayList.size(); b1++) {
/* 1344 */         String str1 = this.pointArrayList.get(b1);
/* 1345 */         if (str1.equals(str)) {
/*      */           
/* 1347 */           Element element3 = new Element("service-point");
/* 1348 */           element3.setAttribute("id", str1);
/* 1349 */           element3.setAttribute("interface", "weaver.interfaces.workflow.action.Action");
/* 1350 */           Element element4 = new Element("invoke-factory");
/* 1351 */           Element element5 = new Element("construct");
/*      */           
/* 1353 */           String str2 = (String)this.dataHST.get(str1);
/* 1354 */           element5.setAttribute("class", str2);
/*      */           
/* 1356 */           Hashtable hashtable = (Hashtable)this.datasetHST.get(str1);
/* 1357 */           if (null != hashtable && hashtable.size() > 0) {
/* 1358 */             Set set = hashtable.keySet();
/* 1359 */             if (null != set && set.size() > 0) {
/* 1360 */               for (String str3 : set) {
/*      */                 
/* 1362 */                 String str4 = (String)hashtable.get(str3);
/* 1363 */                 if (str3.equals(""))
/*      */                   continue; 
/* 1365 */                 if ("actionname".equals(str3)) {
/*      */                   continue;
/*      */                 }
/*      */                 
/* 1369 */                 if ("ds".equals(str3)) {
/* 1370 */                   Element element6 = new Element("set-service");
/* 1371 */                   element6.setAttribute("property", str3);
/* 1372 */                   element6.setAttribute("service-id", str4);
/* 1373 */                   element5.addContent((Content)element6); continue;
/*      */                 } 
/* 1375 */                 Element element = new Element("set");
/* 1376 */                 element.setAttribute("property", str3);
/* 1377 */                 element.setAttribute("value", str4);
/* 1378 */                 element5.addContent((Content)element);
/*      */               } 
/*      */             }
/*      */           } 
/*      */           
/* 1383 */           element4.addContent((Content)element5);
/* 1384 */           element3.addContent((Content)element4);
/* 1385 */           element1.addContent((Content)element3);
/*      */         } 
/*      */       } 
/*      */     } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/* 1396 */     document.addContent((Content)element1);
/*      */     
/*      */     try {
/* 1399 */       String str1 = Util.null2String(getPropValue("xmlfile", "xmlfilechart"));
/* 1400 */       if ("".equals(str1.trim())) {
/* 1401 */         str1 = GCONST.XML_UTF8;
/*      */       }
/* 1403 */       String str2 = paramString + "action.xml";
/* 1404 */       Format format = Format.getCompactFormat();
/* 1405 */       format.setEncoding(str1);
/* 1406 */       format.setIndent("    ");
/* 1407 */       XMLOutputter xMLOutputter = new XMLOutputter(format);
/* 1408 */       xMLOutputter.output(document, new FileOutputStream(str2));
/* 1409 */     } catch (Exception exception) {
/* 1410 */       this.newlog.error(exception);
/* 1411 */       exception.printStackTrace();
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean writeToActionXMLAddForFQ(String paramString1, String paramString2, String paramString3, String paramString4, String[] paramArrayOfString1, String[] paramArrayOfString2, String[] paramArrayOfString3, String paramString5, String paramString6) {
/* 1431 */     boolean bool = true;
/*      */     try {
/* 1433 */       StringBuffer stringBuffer = new StringBuffer();
/* 1434 */       RecordSet recordSet = new RecordSet();
/*      */       
/* 1436 */       String str1 = "";
/* 1437 */       String str2 = TimeUtil.getCurrentTimeString();
/* 1438 */       String str3 = str2.substring(0, 10);
/* 1439 */       String str4 = str2.substring(11);
/*      */       
/* 1441 */       String str5 = recordSet.getDBType();
/* 1442 */       String str6 = "select id from actionsetting where lower(actionname)=?";
/* 1443 */       String str7 = "";
/* 1444 */       if (!paramString1.equals("")) {
/* 1445 */         str7 = paramString1;
/* 1446 */       } else if (!paramString2.equals("")) {
/* 1447 */         str7 = paramString2;
/*      */       } 
/* 1449 */       recordSet.executeQuery(str6, new Object[] { paramString3.toLowerCase() });
/* 1450 */       recordSet.next();
/* 1451 */       str1 = recordSet.getString(1);
/*      */       
/* 1453 */       if (Util.getIntValue(str1, 0) > 0) {
/*      */         
/* 1455 */         stringBuffer.append("update actionsetting set ");
/*      */         
/* 1457 */         stringBuffer.append(" actionclass='").append(paramString4).append("'");
/* 1458 */         stringBuffer.append(" ,actionshowname='").append(paramString2).append("'");
/* 1459 */         stringBuffer.append(" ,subcompanyid='").append(paramString6).append("'");
/*      */         
/* 1461 */         stringBuffer.append(" ,ModifyDate='").append(str3).append("'");
/* 1462 */         stringBuffer.append(" ,ModifyTime='").append(str4).append("'");
/*      */         
/* 1464 */         stringBuffer.append(" where id=").append(str1);
/* 1465 */         bool = recordSet.executeUpdate(stringBuffer.toString(), new Object[0]);
/*      */         
/* 1467 */         if (!bool) {
/* 1468 */           return bool;
/*      */         }
/*      */       } else {
/* 1471 */         stringBuffer.append("insert into actionsetting (actionname,actionclass,subcompanyid,CreateDate,CreateTime,ModifyDate,ModifyTime,actionshowname)");
/* 1472 */         stringBuffer.append(" values('").append(paramString3).append("'");
/* 1473 */         stringBuffer.append(" ,'").append(paramString4).append("'");
/* 1474 */         stringBuffer.append(" ,'").append(paramString6).append("'");
/*      */         
/* 1476 */         stringBuffer.append(" ,'").append(str3).append("'");
/* 1477 */         stringBuffer.append(" ,'").append(str4).append("'");
/* 1478 */         stringBuffer.append(" ,'").append(str3).append("'");
/* 1479 */         stringBuffer.append(" ,'").append(str4).append("'");
/*      */         
/* 1481 */         stringBuffer.append(" ,'").append(paramString2).append("')");
/* 1482 */         bool = recordSet.executeUpdate(stringBuffer.toString(), new Object[0]);
/*      */         
/* 1484 */         if (!bool) {
/* 1485 */           return bool;
/*      */         }
/*      */         
/* 1488 */         recordSet.executeSql("select id from actionsetting where lower(actionname)='" + paramString3.toLowerCase() + "'");
/* 1489 */         recordSet.next();
/* 1490 */         str1 = recordSet.getString(1);
/*      */       } 
/* 1492 */       if (Util.getIntValue(str1) > 0) {
/*      */         
/* 1494 */         if (null != paramString5) {
/* 1495 */           bool = updateJavacode(str1, paramString5);
/* 1496 */           if (!bool) {
/* 1497 */             return bool;
/*      */           }
/*      */         } 
/*      */         
/* 1501 */         bool = recordSet.executeSql("delete from actionsettingdetail where actionid=" + str1);
/* 1502 */         if (!bool) {
/* 1503 */           return bool;
/*      */         }
/*      */         
/* 1506 */         if (null != paramArrayOfString1 && paramArrayOfString1.length > 0)
/* 1507 */           for (byte b = 0; b < paramArrayOfString1.length; b++) {
/* 1508 */             String str8 = paramArrayOfString1[b];
/* 1509 */             String str9 = " ".equals(paramArrayOfString2[b]) ? "" : paramArrayOfString2[b];
/* 1510 */             str8 = str8.replace("'", "''");
/* 1511 */             str9 = str9.replace("'", "''");
/* 1512 */             String str10 = paramArrayOfString3[b];
/* 1513 */             if (!str8.equals("")) {
/* 1514 */               stringBuffer = new StringBuffer();
/* 1515 */               stringBuffer.append("insert into actionsettingdetail(actionid,attrname,attrvalue,isdatasource) values(");
/* 1516 */               stringBuffer.append(str1);
/* 1517 */               stringBuffer.append(",'").append(str8).append("'");
/* 1518 */               stringBuffer.append(",'").append(str9).append("'");
/* 1519 */               stringBuffer.append(",'").append(str10).append("')");
/* 1520 */               bool = recordSet.executeSql(stringBuffer.toString());
/* 1521 */               if (!bool) {
/* 1522 */                 return bool;
/*      */               }
/*      */             } 
/*      */           }  
/*      */       } 
/* 1527 */     } catch (Exception exception) {
/* 1528 */       this.newlog.error(exception);
/* 1529 */       exception.printStackTrace();
/* 1530 */       bool = false;
/*      */     } 
/*      */     
/* 1533 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Hashtable getDsHST() {
/* 1541 */     return this.dsHST;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void setDsHST(Hashtable paramHashtable) {
/* 1548 */     this.dsHST = paramHashtable;
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/servicefiles/ActionXML.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */