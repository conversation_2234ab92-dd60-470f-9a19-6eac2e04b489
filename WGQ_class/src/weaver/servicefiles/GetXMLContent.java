/*    */ package weaver.servicefiles;
/*    */ 
/*    */ import java.io.File;
/*    */ import org.jdom.Document;
/*    */ import org.jdom.Element;
/*    */ import org.jdom.input.SAXBuilder;
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.general.GCONST;
/*    */ import weaver.integration.logging.Logger;
/*    */ import weaver.integration.logging.LoggerFactory;
/*    */ import weaver.security.util.SecurityMethodUtil;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class GetXMLContent
/*    */   extends BaseBean
/*    */ {
/* 24 */   private Logger newlog = LoggerFactory.getLogger(GetXMLContent.class);
/*    */   
/* 26 */   public static String rootpath = GCONST.getRootPath() + File.separatorChar + "WEB-INF" + File.separatorChar + "service";
/*    */ 
/*    */ 
/*    */   
/* 30 */   private static GetXMLContent objXML = new GetXMLContent();
/*    */   
/*    */   public static GetXMLContent getObjXML() {
/* 33 */     return objXML;
/*    */   }
/*    */ 
/*    */   
/*    */   public Element getFileContent(String paramString) {
/* 38 */     Element element = null;
/* 39 */     if (paramString.equals("")) return element;
/*    */     
/*    */     try {
/* 42 */       SAXBuilder sAXBuilder = new SAXBuilder();
/*    */       
/* 44 */       this; String str = rootpath + File.separatorChar + paramString;
/*    */       
/* 46 */       File file = new File(str);
/* 47 */       Document document = sAXBuilder.build(file);
/*    */       
/* 49 */       element = document.getRootElement();
/* 50 */     } catch (Exception exception) {
/* 51 */       this.newlog.error(exception);
/*    */     } 
/* 53 */     return element;
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public Element getFileContentByPath(String paramString) {
/* 62 */     Element element = null;
/* 63 */     if (paramString.equals("")) return element;
/*    */     
/*    */     try {
/* 66 */       SAXBuilder sAXBuilder = new SAXBuilder();
/* 67 */       SecurityMethodUtil.setSaxBuilderFeature(sAXBuilder);
/*    */ 
/*    */       
/* 70 */       File file = new File(paramString);
/* 71 */       Document document = sAXBuilder.build(file);
/*    */       
/* 73 */       element = document.getRootElement();
/* 74 */     } catch (Exception exception) {
/* 75 */       this.newlog.error(exception);
/*    */     } 
/* 77 */     return element;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/servicefiles/GetXMLContent.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */