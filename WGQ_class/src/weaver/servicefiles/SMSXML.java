/*     */ package weaver.servicefiles;
/*     */ 
/*     */ import java.util.ArrayList;
/*     */ import java.util.Hashtable;
/*     */ import java.util.Set;
/*     */ import org.jdom.Element;
/*     */ import weaver.conn.ConnStatement;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SMSXML
/*     */   extends BaseBean
/*     */ {
/*  29 */   private Logger newlog = LoggerFactory.getLogger(SMSXML.class);
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  34 */   public GetXMLContent objXML = GetXMLContent.getObjXML();
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  39 */   public String constructClass = "";
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  44 */   public ArrayList propertyArr = new ArrayList();
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  49 */   public ArrayList valueArr = new ArrayList();
/*  50 */   public ArrayList idArr = new ArrayList();
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Element rootNodeElement;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public SMSXML() {
/*  61 */     init();
/*     */   }
/*     */   
/*     */   public SMSXML(String paramString) {
/*  65 */     init(paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void init() {
/*  72 */     RecordSet recordSet = new RecordSet();
/*  73 */     recordSet.executeSql("select * from smspropertis order by id");
/*  74 */     while (recordSet.next()) {
/*  75 */       String str1 = Util.null2String(recordSet.getString("prop"));
/*  76 */       String str2 = Util.null2String(recordSet.getString("val"));
/*  77 */       String str3 = Util.null2String(recordSet.getString("id"));
/*  78 */       if (str1.equals("ecology_sms_class")) {
/*  79 */         this.constructClass = str2; continue;
/*     */       } 
/*  81 */       this.propertyArr.add(str1);
/*  82 */       this.valueArr.add(str2);
/*  83 */       this.idArr.add(str3);
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public void init(String paramString) {
/*  89 */     RecordSet recordSet = new RecordSet();
/*  90 */     recordSet.executeQuery("select * from smspropertiesnew where subcompanyid = ? and prop != 'servertype' and prop != 'smsserver' ", new Object[] { paramString });
/*  91 */     while (recordSet.next()) {
/*  92 */       String str1 = Util.null2String(recordSet.getString("prop"));
/*  93 */       String str2 = Util.null2String(recordSet.getString("val"));
/*  94 */       String str3 = Util.null2String(recordSet.getString("id"));
/*  95 */       if (str1.equals("ecology_sms_class")) {
/*  96 */         this.constructClass = str2; continue;
/*     */       } 
/*  98 */       this.propertyArr.add(str1);
/*  99 */       this.valueArr.add(str2);
/* 100 */       this.idArr.add(str3);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getConstructClass() {
/* 110 */     return this.constructClass;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public ArrayList getPropertyArr() {
/* 118 */     return this.propertyArr;
/*     */   }
/*     */ 
/*     */   
/*     */   public ArrayList getIdArr() {
/* 123 */     return this.idArr;
/*     */   }
/*     */   
/*     */   public void setIdArr(ArrayList paramArrayList) {
/* 127 */     this.idArr = paramArrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public ArrayList getValueArr() {
/* 135 */     return this.valueArr;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void writeDBForSmsXML(Hashtable paramHashtable) {
/* 143 */     writeDBForSmsXMLNew(paramHashtable);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean writeDBForSmsXMLNew(Hashtable paramHashtable) {
/* 151 */     boolean bool = true;
/* 152 */     RecordSet recordSet = new RecordSet();
/* 153 */     ConnStatement connStatement = new ConnStatement();
/*     */     try {
/* 155 */       String str = Util.null2String(paramHashtable.get("construct"));
/* 156 */       if (!str.equals("")) {
/* 157 */         recordSet.execute("delete from smspropertis");
/* 158 */         recordSet.executeSql("insert into smspropertis(prop,val) values('ecology_sms_class','" + str + "')");
/* 159 */         Set set = paramHashtable.keySet();
/* 160 */         for (String str1 : set) {
/* 161 */           if (!str1.equals("construct")) {
/* 162 */             connStatement.setStatementSql("insert into smspropertis(prop,val) values(?,?)");
/* 163 */             connStatement.setString(1, str1);
/* 164 */             connStatement.setString(2, Util.null2String(paramHashtable.get(str1)));
/* 165 */             connStatement.executeUpdate();
/*     */           } 
/*     */         } 
/*     */       } 
/* 169 */     } catch (Exception exception) {
/* 170 */       this.newlog.error(exception);
/* 171 */       exception.printStackTrace();
/* 172 */       bool = false;
/*     */     } finally {
/* 174 */       connStatement.close();
/*     */     } 
/*     */     
/* 177 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void writeToSMSXML(String paramString, ArrayList<String> paramArrayList1, ArrayList<String> paramArrayList2) {
/* 187 */     RecordSet recordSet = new RecordSet();
/* 188 */     ConnStatement connStatement = new ConnStatement();
/*     */     try {
/* 190 */       if (!paramString.equals("")) {
/* 191 */         recordSet.execute("delete from smspropertis");
/* 192 */         recordSet.executeSql("insert into smspropertis(prop,val) values('ecology_sms_class','" + paramString + "')");
/* 193 */         for (byte b = 0; b < paramArrayList1.size(); b++) {
/* 194 */           String str1 = paramArrayList1.get(b);
/* 195 */           String str2 = paramArrayList2.get(b);
/* 196 */           connStatement.setStatementSql("insert into smspropertis(prop,val) values(?,?)");
/* 197 */           connStatement.setString(1, str1);
/* 198 */           connStatement.setString(2, str2);
/* 199 */           connStatement.executeUpdate();
/*     */         } 
/*     */       } 
/* 202 */     } catch (Exception exception) {
/* 203 */       this.newlog.error(exception);
/* 204 */       exception.printStackTrace();
/*     */     } finally {
/* 206 */       connStatement.close();
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void writeToSMSXML(String paramString, ArrayList<String> paramArrayList1, ArrayList<String> paramArrayList2, int paramInt) {
/* 264 */     RecordSet recordSet = new RecordSet();
/* 265 */     ConnStatement connStatement = new ConnStatement();
/*     */     try {
/* 267 */       if (!paramString.equals("")) {
/* 268 */         recordSet.executeUpdate("delete from smspropertiesnew where subcompanyid = ?", new Object[] { Integer.valueOf(paramInt) });
/* 269 */         recordSet.executeUpdate("insert into smspropertiesnew(prop,val,subcompanyid) values('ecology_sms_class',?,?)", new Object[] { paramString, Integer.valueOf(paramInt) });
/* 270 */         for (byte b = 0; b < paramArrayList1.size(); b++) {
/* 271 */           String str1 = paramArrayList1.get(b);
/* 272 */           String str2 = paramArrayList2.get(b);
/* 273 */           connStatement.setStatementSql("insert into smspropertiesnew(prop,val,subcompanyid) values(?,?,?)");
/* 274 */           connStatement.setString(1, str1);
/* 275 */           connStatement.setString(2, str2);
/* 276 */           connStatement.setString(3, paramInt + "");
/* 277 */           connStatement.executeUpdate();
/*     */         } 
/*     */       } 
/* 280 */     } catch (Exception exception) {
/* 281 */       this.newlog.error(exception);
/* 282 */       exception.printStackTrace();
/*     */     } finally {
/* 284 */       connStatement.close();
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/servicefiles/SMSXML.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */