/*     */ package weaver.servicefiles;
/*     */ 
/*     */ import java.io.File;
/*     */ import java.io.FileNotFoundException;
/*     */ import java.io.FileOutputStream;
/*     */ import java.io.FilenameFilter;
/*     */ import java.io.IOException;
/*     */ import java.text.SimpleDateFormat;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Date;
/*     */ import java.util.List;
/*     */ import org.jdom.Content;
/*     */ import org.jdom.Document;
/*     */ import org.jdom.Element;
/*     */ import org.jdom.output.Format;
/*     */ import org.jdom.output.XMLOutputter;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.Util;
/*     */ import weaver.integration.logging.Logger;
/*     */ import weaver.integration.logging.LoggerFactory;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class MergeXmlContent
/*     */   extends BaseBean
/*     */   implements FilenameFilter
/*     */ {
/*  39 */   private Logger newlog = LoggerFactory.getLogger(MergeXmlContent.class);
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  44 */   private final String Filepath = GetXMLContent.rootpath;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  49 */   private final String Filepathinit = this.Filepath + File.separatorChar + "init";
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  54 */   private final String Filepathlog = this.Filepath + File.separatorChar + "initerror";
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  59 */   String extension = "";
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  64 */   public GetXMLContent objXML = GetXMLContent.getObjXML();
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public MergeXmlContent() {
/*  70 */     this.extension = ".xml";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void main(String[] paramArrayOfString) {
/*  78 */     (new MergeXmlContent()).DoMerge();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public ArrayList<String> getInitXmlFileName() {
/*  86 */     ArrayList<String> arrayList = new ArrayList();
/*  87 */     File file = new File(this.Filepathinit);
/*     */ 
/*     */     
/*     */     try {
/*  91 */       if (file.exists() && file.isDirectory()) {
/*  92 */         File[] arrayOfFile = file.listFiles(new MergeXmlContent());
/*  93 */         for (File file1 : arrayOfFile)
/*     */         {
/*  95 */           arrayList.add(file1.getName());
/*     */         }
/*     */       } else {
/*     */         
/*  99 */         this.newlog.error("MergeXmlContent.getInitXmlFileName不存在目录,不处理！");
/*     */       } 
/* 101 */     } catch (Exception exception) {
/* 102 */       this.newlog.error(exception);
/* 103 */       exception.printStackTrace();
/*     */     } 
/* 105 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void getFailDirectory() {
/* 112 */     File file = new File(this.Filepathlog);
/* 113 */     if (!file.isDirectory()) {
/* 114 */       file.delete();
/*     */     }
/* 116 */     if (!file.exists()) {
/* 117 */       file.mkdir();
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void XmlisExists(String paramString1, String paramString2) {
/* 126 */     File file = new File(paramString1);
/* 127 */     if (!file.exists()) {
/* 128 */       Document document = new Document();
/* 129 */       Element element = new Element("module");
/* 130 */       element.setAttribute("id", paramString2);
/* 131 */       element.setAttribute("version", "1.0.0");
/* 132 */       document.addContent((Content)element);
/* 133 */       XMLOutputter xMLOutputter = new XMLOutputter();
/*     */       
/*     */       try {
/* 136 */         xMLOutputter.output(document, new FileOutputStream(paramString1));
/* 137 */       } catch (FileNotFoundException fileNotFoundException) {
/* 138 */         this.newlog.error(fileNotFoundException);
/* 139 */         fileNotFoundException.printStackTrace();
/* 140 */       } catch (IOException iOException) {
/* 141 */         this.newlog.error(iOException);
/* 142 */         iOException.printStackTrace();
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public ArrayList<String> initXml(String paramString) {
/* 154 */     ArrayList<String> arrayList = new ArrayList();
/*     */     
/* 156 */     Element element = this.objXML.getFileContentByPath(this.Filepath + File.separatorChar + paramString + ".xml");
/* 157 */     if (element != null) {
/* 158 */       List list = element.getChildren("service-point");
/* 159 */       for (Element element1 : list) {
/*     */         
/* 161 */         String str = element1.getAttributeValue("id");
/* 162 */         arrayList.add(str);
/*     */       } 
/*     */     } 
/* 165 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean accept(File paramFile, String paramString) {
/* 175 */     return paramString.endsWith(this.extension);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String DoMerge() {
/* 183 */     String str = "";
/* 184 */     getFailDirectory();
/* 185 */     ArrayList<String> arrayList = getInitXmlFileName();
/* 186 */     for (String str1 : arrayList) {
/* 187 */       String str2 = this.Filepathinit + File.separatorChar + str1;
/* 188 */       Element element = this.objXML.getFileContentByPath(str2);
/* 189 */       if (null != element) {
/* 190 */         String str3 = element.getAttributeValue("id");
/* 191 */         XmlisExists(this.Filepath + str3 + ".xml", str3);
/* 192 */         List list = element.getChildren("service-point");
/* 193 */         ArrayList<String> arrayList1 = initXml(str3);
/* 194 */         ArrayList<Element> arrayList2 = new ArrayList();
/* 195 */         ArrayList<Element> arrayList3 = new ArrayList();
/* 196 */         for (Element element1 : list) {
/* 197 */           String str4 = element1.getAttributeValue("id");
/* 198 */           if (!arrayList1.contains(str4)) {
/*     */             
/* 200 */             arrayList3.add(element1); continue;
/*     */           } 
/* 202 */           arrayList2.add(element1);
/*     */         } 
/*     */ 
/*     */         
/* 206 */         if (arrayList3.size() > 0) {
/* 207 */           WriteToXml(str3, arrayList3, 0);
/*     */         }
/*     */         
/* 210 */         if (arrayList2.size() > 0) {
/* 211 */           WriteToXml(str3, arrayList2, 1);
/*     */         }
/*     */         
/* 214 */         (new File(str2)).delete();
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/* 219 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void WriteToXml(String paramString, List<Element> paramList, int paramInt) {
/*     */     try {
/* 230 */       Document document = new Document();
/* 231 */       Element element = new Element("module");
/* 232 */       element.setAttribute("id", "" + paramString);
/* 233 */       element.setAttribute("version", "1.0.0");
/*     */       
/* 235 */       String str1 = Util.null2String(getPropValue("xmlfile", "xmlfilechart"));
/* 236 */       if ("".equals(str1.trim())) {
/* 237 */         str1 = GCONST.XML_UTF8;
/*     */       }
/* 239 */       String str2 = "";
/* 240 */       String str3 = getDateTimeString();
/* 241 */       if (paramInt == 0) {
/* 242 */         str2 = this.Filepath + File.separatorChar + paramString + ".xml";
/* 243 */         File file = new File(str2);
/* 244 */         if (!file.exists()) {
/* 245 */           file.createNewFile();
/*     */         }
/* 247 */         if (!file.canWrite()) {
/* 248 */           file.delete();
/*     */         }
/*     */         
/* 251 */         ArrayList<Element> arrayList = new ArrayList();
/* 252 */         Element element1 = this.objXML.getFileContentByPath(this.Filepath + File.separatorChar + paramString + ".xml");
/* 253 */         if (element1 != null) {
/* 254 */           List list = element1.getChildren("service-point");
/* 255 */           arrayList.addAll(list);
/*     */         } 
/* 257 */         arrayList.addAll(paramList);
/*     */         
/* 259 */         for (Element element2 : arrayList) {
/* 260 */           element.addContent(element2.detach());
/*     */         }
/* 262 */         document.addContent((Content)element);
/*     */         
/* 264 */         Format format = Format.getCompactFormat();
/* 265 */         format.setEncoding(str1);
/* 266 */         format.setIndent("    ");
/* 267 */         XMLOutputter xMLOutputter = new XMLOutputter(format);
/* 268 */         xMLOutputter.output(document, new FileOutputStream(str2));
/*     */       } else {
/* 270 */         str2 = this.Filepathlog + File.separatorChar + paramString + "_" + str3 + ".xml";
/*     */         
/* 272 */         for (Element element1 : paramList) {
/* 273 */           element.addContent(element1.detach());
/*     */         }
/* 275 */         document.addContent((Content)element);
/*     */         
/* 277 */         Format format = Format.getCompactFormat();
/* 278 */         format.setEncoding(str1);
/* 279 */         format.setIndent("    ");
/* 280 */         XMLOutputter xMLOutputter = new XMLOutputter(format);
/* 281 */         xMLOutputter.output(document, new FileOutputStream(str2));
/*     */       }
/*     */     
/* 284 */     } catch (Exception exception) {
/* 285 */       this.newlog.error(exception);
/* 286 */       exception.printStackTrace();
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getDateTimeString() {
/* 295 */     String str = "yyyyMMddHHmmss";
/* 296 */     SimpleDateFormat simpleDateFormat = new SimpleDateFormat(str);
/* 297 */     return simpleDateFormat.format(new Date());
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/servicefiles/MergeXmlContent.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */