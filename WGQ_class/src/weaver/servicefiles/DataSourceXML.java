/*      */ package weaver.servicefiles;
/*      */ 
/*      */ import java.io.FileOutputStream;
/*      */ import java.util.ArrayList;
/*      */ import java.util.Hashtable;
/*      */ import java.util.List;
/*      */ import java.util.Map;
/*      */ import org.jdom.Content;
/*      */ import org.jdom.Document;
/*      */ import org.jdom.Element;
/*      */ import org.jdom.output.Format;
/*      */ import org.jdom.output.XMLOutputter;
/*      */ import weaver.cluster.SyncDataSource;
/*      */ import weaver.conn.DBConnectionPool;
/*      */ import weaver.conn.ExternalDataSourceManager;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.file.Prop;
/*      */ import weaver.general.BaseBean;
/*      */ import weaver.general.GCONST;
/*      */ import weaver.general.MD5Coder;
/*      */ import weaver.general.SecurityHelper;
/*      */ import weaver.general.StaticObj;
/*      */ import weaver.general.Util;
/*      */ import weaver.hrm.User;
/*      */ import weaver.integration.logging.Logger;
/*      */ import weaver.integration.logging.LoggerFactory;
/*      */ import weaver.interfaces.datasource.BaseDataSource;
/*      */ import weaver.systeminfo.SystemEnv;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ public class DataSourceXML
/*      */   extends BaseBean
/*      */ {
/*   47 */   private static Logger newlog = LoggerFactory.getLogger(DataSourceXML.class);
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*   52 */   public GetXMLContent objXML = GetXMLContent.getObjXML();
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*   57 */   public static String SYS_LOCAL_POOLNAME = "$ECOLOGY_SYS_LOCAL_POOLNAME";
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*   62 */   public String moduleid = "";
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*   67 */   public ArrayList pointArrayList = new ArrayList();
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*   72 */   public Hashtable dataHST = new Hashtable<>();
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Element rootNodeElement;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public DataSourceXML(Element paramElement) {
/*   84 */     this.rootNodeElement = paramElement;
/*   85 */     if (this.rootNodeElement != null) initXML();
/*      */   
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public DataSourceXML() {
/*   94 */     init();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public DataSourceXML(boolean paramBoolean) {
/*  102 */     if (paramBoolean) {
/*  103 */       this.rootNodeElement = this.objXML.getFileContent("datasource.xml");
/*  104 */       if (this.rootNodeElement != null) initXML(); 
/*      */     } else {
/*  106 */       init();
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void initXML() {
/*      */     try {
/*  115 */       this.moduleid = this.rootNodeElement.getAttributeValue("id");
/*      */       
/*  117 */       List list = this.rootNodeElement.getChildren("service-point");
/*      */       
/*  119 */       for (Element element1 : list) {
/*      */         
/*  121 */         String str1 = element1.getAttributeValue("id");
/*  122 */         this.pointArrayList.add(str1);
/*  123 */         Element element2 = element1.getChild("invoke-factory").getChild("construct");
/*  124 */         List list1 = element2.getChildren("set");
/*      */         
/*  126 */         Hashtable<Object, Object> hashtable = new Hashtable<>();
/*  127 */         for (Element element : list1) {
/*      */           
/*  129 */           String str4 = Util.null2String(element.getAttributeValue("property"));
/*  130 */           String str5 = Util.null2String(element.getAttributeValue("value"));
/*  131 */           if (!"".equals(str4)) {
/*  132 */             hashtable.put(str4, str5);
/*      */           }
/*      */         } 
/*  135 */         String str2 = (String)hashtable.get("user");
/*  136 */         String str3 = (String)hashtable.get("password");
/*  137 */         if (hashtable.get("iscode").equals("1")) {
/*  138 */           hashtable.put("user", SecurityHelper.decrypt("ecology", str2));
/*  139 */           hashtable.put("password", SecurityHelper.decrypt("ecology", str3));
/*      */         } 
/*      */         
/*  142 */         this.dataHST.put(str1, hashtable);
/*      */       } 
/*  144 */     } catch (Exception exception) {
/*  145 */       newlog.error(exception);
/*  146 */       exception.printStackTrace();
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void init() {
/*      */     try {
/*  155 */       String str = "/c4Q2hAVXFc=";
/*  156 */       this.moduleid = "datasource";
/*  157 */       RecordSet recordSet = new RecordSet();
/*  158 */       recordSet.executeSql("select * from datasourcesetting order by id ");
/*  159 */       while (recordSet.next()) {
/*  160 */         Hashtable<Object, Object> hashtable = new Hashtable<>();
/*      */         
/*  162 */         String str1 = Util.null2String(recordSet.getString("pointid"));
/*  163 */         this.pointArrayList.add(str1);
/*      */         
/*  165 */         String str2 = Util.null2String(recordSet.getString("type"));
/*  166 */         String str3 = Util.null2String(recordSet.getString("classpath"));
/*  167 */         String str4 = Util.null2String(recordSet.getString("datasourcename"));
/*  168 */         String str5 = Util.null2String(recordSet.getString("iscluster"));
/*  169 */         String str6 = Util.null2String(recordSet.getString("typename"));
/*  170 */         String str7 = Util.null2String(recordSet.getString("url"));
/*  171 */         String str8 = Util.null2String(recordSet.getString("host"));
/*  172 */         String str9 = Util.null2String(recordSet.getString("port"));
/*  173 */         String str10 = Util.null2String(recordSet.getString("dbname"));
/*  174 */         String str11 = Util.null2String(recordSet.getString("iscode"));
/*  175 */         String str12 = Util.null2String(recordSet.getString("username"));
/*  176 */         String str13 = Util.null2String(recordSet.getString("password"));
/*  177 */         String str14 = Util.null2String(recordSet.getString("usepool"));
/*  178 */         String str15 = Util.null2String(recordSet.getString("sortid"));
/*  179 */         String str16 = Util.null2String(recordSet.getString("minconn"));
/*  180 */         String str17 = Util.null2String(recordSet.getString("maxconn"));
/*      */         
/*  182 */         str7 = (str7.indexOf(str) > -1) ? SecurityHelper.decrypt("ecology", str7) : str7;
/*  183 */         str8 = (str8.indexOf(str) > -1) ? SecurityHelper.decrypt("ecology", str8) : str8;
/*  184 */         str9 = (str9.indexOf(str) > -1) ? SecurityHelper.decrypt("ecology", str9) : str9;
/*  185 */         str10 = (str10.indexOf(str) > -1) ? SecurityHelper.decrypt("ecology", str10) : str10;
/*  186 */         str12 = (str12.indexOf(str) > -1) ? SecurityHelper.decrypt("ecology", str12) : str12;
/*  187 */         str13 = (str13.indexOf(str) > -1) ? SecurityHelper.decrypt("ecology", str13) : str13;
/*      */         
/*  189 */         hashtable.put("type", str2);
/*  190 */         hashtable.put("classpath", str3);
/*  191 */         hashtable.put("datasourcename", str4);
/*  192 */         hashtable.put("iscluster", str5);
/*  193 */         hashtable.put("typename", str6);
/*  194 */         hashtable.put("url", str7);
/*  195 */         hashtable.put("host", str8);
/*  196 */         hashtable.put("port", str9);
/*  197 */         hashtable.put("dbname", str10);
/*  198 */         hashtable.put("iscode", str11);
/*  199 */         hashtable.put("user", str12);
/*  200 */         hashtable.put("password", str13);
/*  201 */         hashtable.put("usepool", str14);
/*  202 */         hashtable.put("sortid", str15);
/*  203 */         hashtable.put("minconn", str16);
/*  204 */         hashtable.put("maxconn", str17);
/*      */         
/*  206 */         this.dataHST.put(str1, hashtable);
/*      */       } 
/*  208 */     } catch (Exception exception) {
/*  209 */       newlog.error(exception);
/*  210 */       exception.printStackTrace();
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public static Hashtable getDatasourceInfo(String paramString) {
/*  240 */     Hashtable<Object, Object> hashtable = new Hashtable<>();
/*      */     try {
/*  242 */       String str = "/c4Q2hAVXFc=";
/*  243 */       RecordSet recordSet = new RecordSet();
/*  244 */       recordSet.executeQuery("select * from datasourcesetting  where  pointid=?", new Object[] { paramString });
/*  245 */       if (recordSet.next()) {
/*  246 */         String str1 = Util.null2String(recordSet.getString("pointid"));
/*  247 */         String str2 = Util.null2String(recordSet.getString("type"));
/*  248 */         String str3 = Util.null2String(recordSet.getString("classpath"));
/*  249 */         String str4 = Util.null2String(recordSet.getString("datasourcename"));
/*  250 */         String str5 = Util.null2String(recordSet.getString("iscluster"));
/*  251 */         String str6 = Util.null2String(recordSet.getString("typename"));
/*  252 */         String str7 = Util.null2String(recordSet.getString("url"));
/*  253 */         String str8 = Util.null2String(recordSet.getString("host"));
/*  254 */         String str9 = Util.null2String(recordSet.getString("port"));
/*  255 */         String str10 = Util.null2String(recordSet.getString("dbname"));
/*  256 */         String str11 = Util.null2String(recordSet.getString("iscode"));
/*  257 */         String str12 = Util.null2String(recordSet.getString("username"));
/*  258 */         String str13 = Util.null2String(recordSet.getString("password"));
/*  259 */         String str14 = Util.null2String(recordSet.getString("usepool"));
/*  260 */         String str15 = Util.null2String(recordSet.getString("sortid"));
/*  261 */         String str16 = Util.null2String(recordSet.getString("minconn"));
/*  262 */         String str17 = Util.null2String(recordSet.getString("maxconn"));
/*  263 */         str7 = (str7.indexOf(str) > -1) ? SecurityHelper.decrypt("ecology", str7) : str7;
/*  264 */         str8 = (str8.indexOf(str) > -1) ? SecurityHelper.decrypt("ecology", str8) : str8;
/*  265 */         str9 = (str9.indexOf(str) > -1) ? SecurityHelper.decrypt("ecology", str9) : str9;
/*  266 */         str10 = (str10.indexOf(str) > -1) ? SecurityHelper.decrypt("ecology", str10) : str10;
/*  267 */         str12 = (str12.indexOf(str) > -1) ? SecurityHelper.decrypt("ecology", str12) : str12;
/*  268 */         str13 = (str13.indexOf(str) > -1) ? SecurityHelper.decrypt("ecology", str13) : str13;
/*  269 */         hashtable.put("type", str2);
/*  270 */         hashtable.put("classpath", str3);
/*  271 */         hashtable.put("datasourcename", str4);
/*  272 */         hashtable.put("iscluster", str5);
/*  273 */         hashtable.put("typename", str6);
/*  274 */         hashtable.put("url", str7);
/*  275 */         hashtable.put("host", str8);
/*  276 */         hashtable.put("port", str9);
/*  277 */         hashtable.put("dbname", str10);
/*  278 */         hashtable.put("iscode", str11);
/*  279 */         hashtable.put("user", str12);
/*  280 */         hashtable.put("password", str13);
/*  281 */         hashtable.put("usepool", str14);
/*  282 */         hashtable.put("sortid", str15);
/*  283 */         hashtable.put("minconn", str16);
/*  284 */         hashtable.put("maxconn", str17);
/*      */       }
/*      */     
/*  287 */     } catch (Exception exception) {
/*  288 */       exception.printStackTrace();
/*  289 */       newlog.error(exception);
/*      */     } 
/*      */ 
/*      */     
/*  293 */     return hashtable;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void updateDataSourceUsed(String paramString1, String paramString2) {
/*      */     try {
/*  304 */       RecordSet recordSet = new RecordSet();
/*      */       
/*  306 */       String str = "update Workflow_DataInput_main set datasourcename ='" + paramString2 + "' where datasourcename ='" + paramString1 + "'";
/*  307 */       recordSet.executeSql(str);
/*      */       
/*  309 */       str = "update formactionset set datasourceid ='" + paramString2 + "' where datasourceid ='" + paramString1 + "'";
/*  310 */       recordSet.executeSql(str);
/*      */       
/*  312 */       str = "update outerdatawfset set datasourceid ='" + paramString2 + "' where datasourceid ='" + paramString1 + "'";
/*  313 */       recordSet.executeSql(str);
/*      */ 
/*      */ 
/*      */       
/*  317 */       recordSet.executeSql("update datashowset set datasourceid='" + paramString2 + "' where datasourceid='" + paramString1 + "'");
/*  318 */       recordSet.executeSql("update datashowset set datasourceid='datasource." + paramString2 + "' where datasourceid='datasource." + paramString1 + "'");
/*      */       
/*  320 */       str = "update modeDataInputmain set datasourcename ='" + paramString2 + "' where datasourcename ='" + paramString1 + "'";
/*  321 */       recordSet.executeSql(str);
/*      */       
/*  323 */       str = "update mode_dmlactionset set datasourceid ='" + paramString2 + "' where datasourceid ='" + paramString1 + "'";
/*      */ 
/*      */       
/*  326 */       recordSet.execute("update hrsyncset set dbsource='" + paramString2 + "' where dbsource='" + paramString1 + "'");
/*  327 */       recordSet.executeSql(str);
/*  328 */     } catch (Exception exception) {
/*  329 */       newlog.error(exception);
/*  330 */       exception.printStackTrace();
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getModuleId() {
/*  339 */     return this.moduleid;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public ArrayList getPointArrayList() {
/*  347 */     return this.pointArrayList;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Hashtable getDataHST() {
/*  355 */     return this.dataHST;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getDataSourceUsed(String paramString, User paramUser) {
/*  368 */     String str1 = processSpecialChar(paramString);
/*  369 */     StringBuffer stringBuffer = new StringBuffer();
/*  370 */     RecordSet recordSet1 = new RecordSet();
/*  371 */     RecordSet recordSet2 = new RecordSet();
/*  372 */     byte b1 = 0;
/*      */     
/*  374 */     String str2 = "select distinct e.workflowid,b.workflowname from Workflow_DataInput_Entry e,workflow_base b where e.workflowid=b.id and e.id in(select entryid from Workflow_DataInput_main where datasourcename='" + str1 + "')";
/*  375 */     recordSet1.executeSql(str2);
/*  376 */     if (recordSet1.next())
/*      */     {
/*  378 */       stringBuffer.append("<br/>" + SystemEnv.getHtmlLabelName(10000174, Util.getIntValue(paramUser.getLanguage())) + " : ");
/*      */     }
/*  380 */     recordSet1.beforFirst();
/*  381 */     while (recordSet1.next()) {
/*      */       
/*  383 */       String str = recordSet1.getString(2);
/*  384 */       stringBuffer.append(str).append(",");
/*  385 */       b1++;
/*  386 */       if (b1 % 5 == 0) {
/*  387 */         stringBuffer.append("<br/>");
/*      */       }
/*      */     } 
/*      */ 
/*      */     
/*  392 */     b1 = 0;
/*  393 */     str2 = "select distinct s.formid,s.isbill from formactionset s where s.datasourceid ='" + str1 + "'";
/*  394 */     recordSet1.executeSql(str2);
/*  395 */     if (recordSet1.next())
/*      */     {
/*  397 */       stringBuffer.append("<br/>" + SystemEnv.getHtmlLabelName(31923, paramUser.getLanguage()) + " : ");
/*      */     }
/*  399 */     recordSet1.beforFirst();
/*  400 */     while (recordSet1.next()) {
/*      */       
/*  402 */       String str4 = "";
/*  403 */       String str5 = recordSet1.getString("formid");
/*  404 */       String str6 = recordSet1.getString("isbill");
/*  405 */       if (!"".equals(str5)) {
/*      */         
/*  407 */         if ("0".equals(str6)) {
/*  408 */           str2 = "select formname from workflow_formbase where id = " + str5;
/*      */         } else {
/*  410 */           str2 = "select h.labelname from workflow_bill b ,htmllabelinfo h where b.namelabel=h.indexid and h.languageid=7 and b.id=" + str5;
/*  411 */         }  recordSet2.executeSql(str2);
/*  412 */         if (recordSet2.next())
/*      */         {
/*  414 */           str4 = recordSet2.getString(1);
/*      */         }
/*      */       } 
/*  417 */       stringBuffer.append(str4).append(",");
/*  418 */       b1++;
/*  419 */       if (b1 % 5 == 0) {
/*  420 */         stringBuffer.append("<br/>");
/*      */       }
/*      */     } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  432 */     b1 = 0;
/*  433 */     ArrayList arrayList = new ArrayList();
/*  434 */     str2 = "select distinct id,setname from outerdatawfset where datasourceid ='" + str1 + "'";
/*  435 */     recordSet1.executeSql(str2);
/*  436 */     if (recordSet1.next())
/*      */     {
/*  438 */       stringBuffer.append("<br/>" + SystemEnv.getHtmlLabelName(23076, paramUser.getLanguage()) + " : ");
/*      */     }
/*  440 */     recordSet1.beforFirst();
/*  441 */     while (recordSet1.next()) {
/*      */       
/*  443 */       String str = recordSet1.getString(2);
/*  444 */       stringBuffer.append(str).append(",");
/*  445 */       b1++;
/*  446 */       if (b1 % 5 == 0) {
/*  447 */         stringBuffer.append("<br/>");
/*      */       }
/*      */     } 
/*      */ 
/*      */     
/*  452 */     b1 = 0;
/*  453 */     boolean bool = false;
/*  454 */     BrowserXML browserXML = new BrowserXML();
/*  455 */     ArrayList<String> arrayList1 = browserXML.getPointArrayList();
/*  456 */     Hashtable hashtable = browserXML.getDataHST();
/*  457 */     String str3 = "";
/*  458 */     for (byte b2 = 0; b2 < arrayList1.size(); b2++) {
/*  459 */       String str = arrayList1.get(b2);
/*  460 */       if (!str.equals("")) {
/*  461 */         String str4 = "";
/*  462 */         String str5 = "";
/*  463 */         String str6 = "";
/*  464 */         String str7 = "";
/*  465 */         Hashtable hashtable1 = (Hashtable)hashtable.get(str);
/*      */         
/*  467 */         String str8 = (String)hashtable1.get("ds");
/*  468 */         if (hashtable1 != null && ("datasource." + paramString).equals(str8)) {
/*      */           
/*  470 */           if (!bool) {
/*      */             
/*  472 */             stringBuffer.append("<br/>" + SystemEnv.getHtmlLabelName(83327, paramUser.getLanguage()) + " : ");
/*  473 */             bool = true;
/*      */           } 
/*  475 */           stringBuffer.append(str).append(",");
/*  476 */           str3 = str3 + " and showname <> '" + str + "'";
/*  477 */           b1++;
/*  478 */           if (b1 % 5 == 0) {
/*  479 */             stringBuffer.append("<br/>");
/*      */           }
/*      */         } 
/*      */       } 
/*      */     } 
/*      */ 
/*      */     
/*  486 */     b1 = 0;
/*  487 */     str2 = "select distinct id,showname,datasourceid from datashowset where datasourceid ='datasource." + str1 + "'";
/*  488 */     if (!str3.equals(""))
/*  489 */       str2 = str2 + str3; 
/*  490 */     recordSet1.executeSql(str2);
/*  491 */     if (recordSet1.next())
/*      */     {
/*  493 */       stringBuffer.append("<br/>" + SystemEnv.getHtmlLabelName(32303, paramUser.getLanguage()) + " : ");
/*      */     }
/*  495 */     recordSet1.beforFirst();
/*  496 */     while (recordSet1.next()) {
/*      */       
/*  498 */       String str = recordSet1.getString(2);
/*  499 */       stringBuffer.append(str).append(",");
/*  500 */       b1++;
/*  501 */       if (b1 % 5 == 0) {
/*  502 */         stringBuffer.append("<br/>");
/*      */       }
/*      */     } 
/*      */ 
/*      */     
/*  507 */     str2 = "select 1 from hrsyncset where dbsource ='" + str1 + "'";
/*  508 */     recordSet1.executeSql(str2);
/*  509 */     if (recordSet1.next())
/*      */     {
/*  511 */       stringBuffer.append("<br/>HR" + SystemEnv.getHtmlLabelName(32214, paramUser.getLanguage()) + " ,<br/>");
/*      */     }
/*      */ 
/*      */     
/*  515 */     ArrayList<String> arrayList2 = new ArrayList();
/*  516 */     str2 = "select distinct id,financename from financeset where datasourceid = '" + str1 + "'";
/*  517 */     recordSet1.executeSql(str2);
/*  518 */     while (recordSet1.next()) {
/*      */       
/*  520 */       String str = recordSet1.getString(2);
/*  521 */       arrayList2.add(str);
/*      */     } 
/*      */ 
/*      */     
/*  525 */     b1 = 0;
/*  526 */     str2 = "select distinct e.modeid,m.modeName from modeDataInputEntry e,modeinfo m where e.modeid=m.id and e.id in(select m.entryid from modeDataInputmain m where m.datasourcename = '" + str1 + "')";
/*  527 */     recordSet1.executeSql(str2);
/*  528 */     if (recordSet1.next())
/*      */     {
/*  530 */       stringBuffer.append("<br/>" + SystemEnv.getHtmlLabelName(10000175, Util.getIntValue(paramUser.getLanguage())) + " : ");
/*      */     }
/*  532 */     recordSet1.beforFirst();
/*  533 */     while (recordSet1.next()) {
/*      */       
/*  535 */       String str = recordSet1.getString(2);
/*  536 */       stringBuffer.append(str).append(",");
/*  537 */       b1++;
/*  538 */       if (b1 % 5 == 0) {
/*  539 */         stringBuffer.append("<br/>");
/*      */       }
/*      */     } 
/*      */ 
/*      */     
/*  544 */     b1 = 0;
/*  545 */     str2 = "select distinct e.modeid,m.modeName from mode_dmlactionset e,modeinfo m where e.modeid=m.id and datasourceid= '" + str1 + "'";
/*  546 */     recordSet1.executeSql(str2);
/*  547 */     if (recordSet1.next())
/*      */     {
/*  549 */       stringBuffer.append("<br/>" + SystemEnv.getHtmlLabelName(30235, paramUser.getLanguage()) + "DML Action : ");
/*      */     }
/*  551 */     recordSet1.beforFirst();
/*  552 */     while (recordSet1.next()) {
/*      */       
/*  554 */       String str = recordSet1.getString(2);
/*  555 */       stringBuffer.append(str).append(",");
/*  556 */       b1++;
/*  557 */       if (b1 % 5 == 0) {
/*  558 */         stringBuffer.append("<br/>");
/*      */       }
/*      */     } 
/*  561 */     return stringBuffer.toString();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private String processSpecialChar(String paramString) {
/*  570 */     return paramString.replaceAll("'", "''");
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void writeDataSourceToDataSourceXML(DataSourceXML paramDataSourceXML) {
/*  582 */     ArrayList<String> arrayList = paramDataSourceXML.getPointArrayList();
/*  583 */     Hashtable hashtable = paramDataSourceXML.getDataHST();
/*  584 */     ArrayList arrayList1 = new ArrayList();
/*      */     
/*  586 */     for (byte b = 0; b < arrayList.size(); b++) {
/*  587 */       String str = arrayList.get(b);
/*  588 */       if (!this.pointArrayList.contains(str)) {
/*      */ 
/*      */         
/*  591 */         Hashtable hashtable1 = (Hashtable)hashtable.get(str);
/*  592 */         rootmoduleAddDataSourceXML(str, hashtable1);
/*      */       } 
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void rootmoduleAddDataSourceXML(String paramString, Hashtable paramHashtable) {
/*  712 */     writeToDataSourceXMLAdd(paramString, paramHashtable);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void rootmoduleAddDataSourceXML(Element paramElement, String paramString, Hashtable paramHashtable) {
/*  722 */     Element element1 = new Element("service-point");
/*  723 */     element1.setAttribute("id", paramString);
/*  724 */     element1.setAttribute("interface", "weaver.interfaces.datasource.DataSource");
/*      */     
/*  726 */     Element element2 = new Element("invoke-factory");
/*  727 */     Element element3 = new Element("construct");
/*  728 */     element3.setAttribute("class", "weaver.interfaces.datasource.BaseDataSource");
/*      */     
/*  730 */     Element element4 = new Element("set");
/*  731 */     element4.setAttribute("property", "type");
/*  732 */     element4.setAttribute("value", Util.null2String((String)paramHashtable.get("type")));
/*  733 */     element3.addContent((Content)element4);
/*      */     
/*  735 */     Element element5 = new Element("set");
/*  736 */     element5.setAttribute("property", "datasourcename");
/*  737 */     element5.setAttribute("value", Util.null2String((String)paramHashtable.get("datasourcename")));
/*  738 */     element3.addContent((Content)element5);
/*      */     
/*  740 */     Element element6 = new Element("set");
/*  741 */     element6.setAttribute("property", "iscluster");
/*  742 */     element6.setAttribute("value", Util.null2String((String)paramHashtable.get("iscluster")));
/*  743 */     element3.addContent((Content)element6);
/*      */     
/*  745 */     Element element7 = new Element("set");
/*  746 */     element7.setAttribute("property", "typename");
/*  747 */     element7.setAttribute("value", Util.null2String((String)paramHashtable.get("typename")));
/*  748 */     element3.addContent((Content)element7);
/*      */     
/*  750 */     Element element8 = new Element("set");
/*  751 */     element8.setAttribute("property", "url");
/*  752 */     element8.setAttribute("value", Util.null2String((String)paramHashtable.get("url")));
/*  753 */     element3.addContent((Content)element8);
/*      */     
/*  755 */     element4 = new Element("set");
/*  756 */     element4.setAttribute("property", "host");
/*  757 */     element4.setAttribute("value", Util.null2String((String)paramHashtable.get("host")));
/*  758 */     element3.addContent((Content)element4);
/*      */     
/*  760 */     element4 = new Element("set");
/*  761 */     element4.setAttribute("property", "port");
/*  762 */     element4.setAttribute("value", Util.null2String((String)paramHashtable.get("port")));
/*  763 */     element3.addContent((Content)element4);
/*      */     
/*  765 */     element4 = new Element("set");
/*  766 */     element4.setAttribute("property", "dbname");
/*  767 */     element4.setAttribute("value", Util.null2String((String)paramHashtable.get("dbname")));
/*  768 */     element3.addContent((Content)element4);
/*      */     
/*  770 */     element4 = new Element("set");
/*  771 */     element4.setAttribute("property", "user");
/*  772 */     element4.setAttribute("value", Util.null2String((String)paramHashtable.get("user")));
/*  773 */     element3.addContent((Content)element4);
/*      */     
/*  775 */     element4 = new Element("set");
/*  776 */     element4.setAttribute("property", "password");
/*  777 */     element4.setAttribute("value", Util.null2String((String)paramHashtable.get("password")));
/*  778 */     element3.addContent((Content)element4);
/*      */     
/*  780 */     element4 = new Element("set");
/*  781 */     element4.setAttribute("property", "minconn");
/*  782 */     element4.setAttribute("value", Util.null2String((String)paramHashtable.get("minconn")));
/*  783 */     element3.addContent((Content)element4);
/*      */     
/*  785 */     element4 = new Element("set");
/*  786 */     element4.setAttribute("property", "maxconn");
/*  787 */     element4.setAttribute("value", Util.null2String((String)paramHashtable.get("maxconn")));
/*  788 */     element3.addContent((Content)element4);
/*      */     
/*  790 */     element4 = new Element("set");
/*  791 */     element4.setAttribute("property", "iscode");
/*  792 */     element4.setAttribute("value", Util.null2String((String)paramHashtable.get("iscode")));
/*  793 */     element3.addContent((Content)element4);
/*      */     
/*  795 */     element2.addContent((Content)element3);
/*  796 */     element1.addContent((Content)element2);
/*  797 */     paramElement.addContent((Content)element1);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void writeToDataSourceXMLAdd(String paramString, Hashtable paramHashtable) {
/*  807 */     writeToDataSourceXMLAddNew(paramString, paramHashtable);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean writeToDataSourceXMLAddNew(String paramString, Hashtable paramHashtable) {
/*  817 */     boolean bool = true;
/*  818 */     String str1 = Util.null2String((String)paramHashtable.get("construct"));
/*  819 */     String str2 = Util.null2String((String)paramHashtable.get("type"));
/*  820 */     String str3 = Util.null2String((String)paramHashtable.get("datasourcename"));
/*  821 */     String str4 = Util.null2s((String)paramHashtable.get("iscluster"), "1");
/*  822 */     String str5 = Util.null2String((String)paramHashtable.get("typename"));
/*  823 */     String str6 = Util.null2String((String)paramHashtable.get("url"));
/*  824 */     String str7 = Util.null2String((String)paramHashtable.get("host"));
/*  825 */     String str8 = Util.null2String((String)paramHashtable.get("port"));
/*  826 */     String str9 = Util.null2String((String)paramHashtable.get("dbname"));
/*      */     
/*  828 */     String str10 = Util.null2String((String)paramHashtable.get("usepool"), "1");
/*  829 */     String str11 = Util.null2String((String)paramHashtable.get("sortid"), "1");
/*  830 */     String str12 = Util.null2String((String)paramHashtable.get("minconn"));
/*  831 */     String str13 = Util.null2String((String)paramHashtable.get("maxconn"));
/*      */     
/*  833 */     String str14 = Util.null2String((String)paramHashtable.get("iscode"));
/*  834 */     String str15 = Util.null2String((String)paramHashtable.get("user"));
/*  835 */     String str16 = Util.null2String((String)paramHashtable.get("password"));
/*      */     try {
/*  837 */       if (!paramString.trim().equals("")) {
/*  838 */         if (str3.equals("")) {
/*  839 */           str3 = paramString;
/*      */         }
/*  841 */         if (str1.equals("")) {
/*  842 */           str1 = "weaver.interfaces.datasource.BaseDataSource";
/*      */         }
/*  844 */         StringBuffer stringBuffer = new StringBuffer();
/*  845 */         RecordSet recordSet = new RecordSet();
/*  846 */         String str17 = recordSet.getDBType();
/*  847 */         String str18 = "select 1 from datasourcesetting where lower(pointid)=?";
/*  848 */         recordSet.executeQuery(str18, new Object[] { paramString.toLowerCase() });
/*  849 */         if (recordSet.next()) {
/*  850 */           stringBuffer.append("update datasourcesetting set ");
/*  851 */           stringBuffer.append("type='").append(str2).append("',");
/*  852 */           stringBuffer.append("datasourcename='").append(str3).append("',");
/*  853 */           stringBuffer.append("iscluster='").append(str4).append("',");
/*  854 */           stringBuffer.append("typename='").append(str5).append("',");
/*  855 */           stringBuffer.append("url='").append(str6).append("',");
/*  856 */           stringBuffer.append("host='").append(str7).append("',");
/*  857 */           stringBuffer.append("port='").append(str8).append("',");
/*  858 */           stringBuffer.append("dbname='").append(str9).append("',");
/*  859 */           stringBuffer.append("username='").append(str15).append("',");
/*  860 */           stringBuffer.append("password='").append(str16).append("',");
/*  861 */           stringBuffer.append("minconn='").append(str12).append("',");
/*  862 */           stringBuffer.append("maxconn='").append(str13).append("',");
/*  863 */           stringBuffer.append("iscode='").append(str14).append("',");
/*  864 */           stringBuffer.append("usepool='").append(str10).append("',");
/*  865 */           stringBuffer.append("sortid='").append(str11).append("'");
/*  866 */           stringBuffer.append("where ");
/*  867 */           stringBuffer.append("lower(pointid)='").append(paramString.toLowerCase()).append("' ");
/*      */         } else {
/*  869 */           stringBuffer.append("insert into datasourcesetting(");
/*  870 */           stringBuffer.append(" pointid,classpath,type,datasourcename,iscluster,typename,url,host,port,dbname,username,password,minconn,maxconn,iscode,usepool,sortid ");
/*  871 */           stringBuffer.append(" )values( ");
/*  872 */           stringBuffer.append("'").append(paramString).append("',");
/*  873 */           stringBuffer.append("'").append(str1).append("',");
/*  874 */           stringBuffer.append("'").append(str2).append("',");
/*  875 */           stringBuffer.append("'").append(str3).append("',");
/*  876 */           stringBuffer.append("'").append(str4).append("',");
/*  877 */           stringBuffer.append("'").append(str5).append("',");
/*  878 */           stringBuffer.append("'").append(str6).append("',");
/*  879 */           stringBuffer.append("'").append(str7).append("',");
/*  880 */           stringBuffer.append("'").append(str8).append("',");
/*  881 */           stringBuffer.append("'").append(str9).append("',");
/*  882 */           stringBuffer.append("'").append(str15).append("',");
/*  883 */           stringBuffer.append("'").append(str16).append("',");
/*  884 */           stringBuffer.append("'").append(str12).append("',");
/*  885 */           stringBuffer.append("'").append(str13).append("',");
/*  886 */           stringBuffer.append("'").append(str14).append("', ");
/*  887 */           stringBuffer.append("'").append(str10).append("',");
/*  888 */           stringBuffer.append("'").append(str11).append("' ");
/*  889 */           stringBuffer.append(")");
/*      */         } 
/*  891 */         recordSet.executeUpdate(stringBuffer.toString(), new Object[0]);
/*      */       } 
/*  893 */     } catch (Exception exception) {
/*  894 */       newlog.error(exception);
/*  895 */       exception.printStackTrace();
/*  896 */       bool = false;
/*      */     } 
/*      */     
/*  899 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void copyToDataSourceXML(List<String> paramList, String paramString) {
/* 1090 */     Document document = new Document();
/* 1091 */     Element element = new Element("module");
/* 1092 */     element.setAttribute("id", "datasource");
/* 1093 */     element.setAttribute("version", "1.0.0");
/* 1094 */     for (byte b = 0; b < paramList.size(); b++) {
/* 1095 */       String str = paramList.get(b);
/*      */       
/* 1097 */       for (byte b1 = 0; b1 < this.pointArrayList.size(); b1++) {
/* 1098 */         String str1 = this.pointArrayList.get(b1);
/* 1099 */         if (str1.equals(str)) {
/*      */           
/* 1101 */           Element element1 = new Element("service-point");
/* 1102 */           element1.setAttribute("id", str1);
/* 1103 */           element1.setAttribute("interface", "weaver.interfaces.datasource.DataSource");
/*      */           
/* 1105 */           Element element2 = new Element("invoke-factory");
/* 1106 */           Element element3 = new Element("construct");
/* 1107 */           element3.setAttribute("class", "weaver.interfaces.datasource.BaseDataSource");
/*      */           
/* 1109 */           Hashtable hashtable = (Hashtable)this.dataHST.get(str1);
/* 1110 */           Element element4 = new Element("set");
/* 1111 */           element4.setAttribute("property", "type");
/* 1112 */           element4.setAttribute("value", Util.null2String((String)hashtable.get("type")));
/* 1113 */           element3.addContent((Content)element4);
/*      */           
/* 1115 */           Element element5 = new Element("set");
/* 1116 */           element5.setAttribute("property", "datasourcename");
/* 1117 */           element5.setAttribute("value", Util.null2String((String)hashtable.get("datasourcename")));
/* 1118 */           element3.addContent((Content)element5);
/*      */           
/* 1120 */           Element element6 = new Element("set");
/* 1121 */           element6.setAttribute("property", "iscluster");
/* 1122 */           element6.setAttribute("value", Util.null2String((String)hashtable.get("iscluster")));
/* 1123 */           element3.addContent((Content)element6);
/*      */           
/* 1125 */           Element element7 = new Element("set");
/* 1126 */           element7.setAttribute("property", "typename");
/* 1127 */           element7.setAttribute("value", Util.null2String((String)hashtable.get("typename")));
/* 1128 */           element3.addContent((Content)element7);
/*      */           
/* 1130 */           Element element8 = new Element("set");
/* 1131 */           element8.setAttribute("property", "url");
/* 1132 */           element8.setAttribute("value", Util.null2String((String)hashtable.get("url")));
/* 1133 */           element3.addContent((Content)element8);
/*      */           
/* 1135 */           element4 = new Element("set");
/* 1136 */           element4.setAttribute("property", "host");
/* 1137 */           element4.setAttribute("value", Util.null2String((String)hashtable.get("host")));
/* 1138 */           element3.addContent((Content)element4);
/*      */           
/* 1140 */           element4 = new Element("set");
/* 1141 */           element4.setAttribute("property", "port");
/* 1142 */           element4.setAttribute("value", Util.null2String((String)hashtable.get("port")));
/* 1143 */           element3.addContent((Content)element4);
/*      */           
/* 1145 */           element4 = new Element("set");
/* 1146 */           element4.setAttribute("property", "dbname");
/* 1147 */           element4.setAttribute("value", Util.null2String((String)hashtable.get("dbname")));
/* 1148 */           element3.addContent((Content)element4);
/*      */           
/* 1150 */           element4 = new Element("set");
/* 1151 */           element4.setAttribute("property", "user");
/* 1152 */           element4.setAttribute("value", Util.null2String((String)hashtable.get("user")));
/* 1153 */           element3.addContent((Content)element4);
/*      */           
/* 1155 */           element4 = new Element("set");
/* 1156 */           element4.setAttribute("property", "password");
/* 1157 */           element4.setAttribute("value", Util.null2String((String)hashtable.get("password")));
/* 1158 */           element3.addContent((Content)element4);
/*      */           
/* 1160 */           element4 = new Element("set");
/* 1161 */           element4.setAttribute("property", "minconn");
/* 1162 */           element4.setAttribute("value", Util.null2String((String)hashtable.get("minconn")));
/* 1163 */           element3.addContent((Content)element4);
/*      */           
/* 1165 */           element4 = new Element("set");
/* 1166 */           element4.setAttribute("property", "maxconn");
/* 1167 */           element4.setAttribute("value", Util.null2String((String)hashtable.get("maxconn")));
/* 1168 */           element3.addContent((Content)element4);
/*      */           
/* 1170 */           element4 = new Element("set");
/* 1171 */           element4.setAttribute("property", "iscode");
/* 1172 */           element4.setAttribute("value", Util.null2String((String)hashtable.get("iscode")));
/* 1173 */           element3.addContent((Content)element4);
/*      */           
/* 1175 */           element2.addContent((Content)element3);
/* 1176 */           element1.addContent((Content)element2);
/* 1177 */           element.addContent((Content)element1);
/*      */         } 
/*      */       } 
/* 1180 */     }  document.addContent((Content)element);
/*      */     
/*      */     try {
/* 1183 */       String str1 = Util.null2String(getPropValue("xmlfile", "xmlfilechart"));
/* 1184 */       if ("".equals(str1.trim())) {
/* 1185 */         str1 = GCONST.XML_UTF8;
/*      */       }
/* 1187 */       String str2 = paramString + "datasource.xml";
/* 1188 */       Format format = Format.getCompactFormat();
/* 1189 */       format.setEncoding(str1);
/* 1190 */       format.setIndent("    ");
/* 1191 */       XMLOutputter xMLOutputter = new XMLOutputter(format);
/* 1192 */       xMLOutputter.output(document, new FileOutputStream(str2));
/* 1193 */     } catch (Exception exception) {
/* 1194 */       newlog.error(exception);
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void writeToDataSourceXMLEdit(String paramString, Hashtable paramHashtable) {
/* 1204 */     String str1 = "/c4Q2hAVXFc=";
/* 1205 */     String str2 = Util.null2String((String)paramHashtable.get("type"));
/* 1206 */     String str3 = Util.null2s((String)paramHashtable.get("iscluster"), "1");
/* 1207 */     String str4 = Util.null2String((String)paramHashtable.get("url"));
/* 1208 */     String str5 = Util.null2String((String)paramHashtable.get("host"));
/* 1209 */     String str6 = Util.null2String((String)paramHashtable.get("port"));
/* 1210 */     String str7 = Util.null2String((String)paramHashtable.get("dbname"));
/*      */     
/* 1212 */     String str8 = Util.null2String((String)paramHashtable.get("usepool"));
/* 1213 */     String str9 = Util.null2String((String)paramHashtable.get("sortid"));
/* 1214 */     String str10 = Util.null2String((String)paramHashtable.get("minconn"));
/* 1215 */     String str11 = Util.null2String((String)paramHashtable.get("maxconn"));
/*      */     
/* 1217 */     String str12 = Util.null2String((String)paramHashtable.get("iscode"));
/* 1218 */     String str13 = Util.null2String((String)paramHashtable.get("user"));
/* 1219 */     String str14 = Util.null2String((String)paramHashtable.get("password"));
/* 1220 */     String str15 = Util.null2String((String)paramHashtable.get("provider"));
/* 1221 */     if (str15 == null || "".equals(str15)) {
/* 1222 */       str15 = Util.null2s(Prop.getPropValue(GCONST.getConfigFile(), GCONST.getServerName() + ".provider"), "proxool");
/*      */     }
/*      */     try {
/* 1225 */       if (!"".equals(paramString.trim())) {
/* 1226 */         RecordSet recordSet = new RecordSet();
/* 1227 */         String str16 = "update datasourcesetting set type='" + str2 + "',iscluster='" + str3 + "',url='" + str4 + "',host='" + str5 + "',port='" + str6 + "',dbname='" + str7 + "',username='" + str13 + "',password='" + str14 + "', minconn='" + str10 + "',maxconn='" + str11 + "',iscode='" + str12 + "',usepool='" + str8 + "',sortid='" + str9 + "' where pointid='" + paramString + "' ";
/*      */ 
/*      */         
/* 1230 */         recordSet.executeSql(str16);
/*      */         
/* 1232 */         String str17 = str13;
/* 1233 */         String str18 = str14;
/* 1234 */         if (null != str12 && str12.equals("1")) {
/* 1235 */           str17 = (str13.indexOf(str1) > -1) ? SecurityHelper.decrypt("ecology", str13) : str13;
/* 1236 */           str18 = (str14.indexOf(str1) > -1) ? SecurityHelper.decrypt("ecology", str14) : str14;
/*      */         } 
/* 1238 */         Map map1 = ExternalDataSourceManager.getConnectionParam(str2, str6, str5, str7, str3, str4);
/* 1239 */         String str19 = (String)map1.get("url");
/* 1240 */         String str20 = str2 + "_" + str19 + "_" + str3 + "_" + str5 + "_" + str6 + "_" + str7 + "_" + str17 + "_" + str18 + "_" + str15 + "_" + str11 + "_" + str10 + "_" + str8;
/*      */         
/* 1242 */         str20 = MD5Coder.stringMD5(str20);
/* 1243 */         String str21 = Util.null2String(paramString).trim();
/* 1244 */         String str22 = str21 + "_" + str20;
/* 1245 */         StaticObj staticObj = StaticObj.getInstance();
/* 1246 */         boolean bool = staticObj.isCluster();
/* 1247 */         Map map2 = (Map)staticObj.getRecordFromObj("datasourceMap", "datasources");
/* 1248 */         DBConnectionPool dBConnectionPool = ExternalDataSourceManager.getPool(str21);
/* 1249 */         if (dBConnectionPool != null) {
/* 1250 */           if (map2 == null || !map2.containsKey(str21)) {
/* 1251 */             ExternalDataSourceManager.destoryDataSource(str21);
/* 1252 */             if (bool) {
/* 1253 */               (new SyncDataSource()).syncDataSource("destoryDataSource", str21);
/*      */             }
/* 1255 */           } else if (!map2.get(str21).toString().equals(str22)) {
/* 1256 */             ExternalDataSourceManager.destoryDataSource(str21);
/* 1257 */             if (bool) {
/* 1258 */               (new SyncDataSource()).syncDataSource("destoryDataSource", str21);
/*      */             }
/*      */           } 
/*      */         }
/*      */       } 
/* 1263 */     } catch (Exception exception) {
/* 1264 */       newlog.error(exception);
/* 1265 */       exception.printStackTrace();
/*      */     } 
/*      */ 
/*      */     
/* 1269 */     resetCache(paramString);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getDataSourceDBType(String paramString) {
/* 1379 */     if (SYS_LOCAL_POOLNAME.equals(paramString)) {
/* 1380 */       return (new RecordSet()).getDBType();
/*      */     }
/* 1382 */     Hashtable hashtable = (Hashtable)getDataHST().get(paramString);
/* 1383 */     if (hashtable != null) {
/* 1384 */       return Util.null2String(hashtable.get("type"));
/*      */     }
/*      */     
/* 1387 */     return "";
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void initData() {}
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private void resetCache(String paramString) {
/* 1480 */     RecordSet recordSet = new RecordSet();
/* 1481 */     Hashtable<Object, Object> hashtable = new Hashtable<>();
/* 1482 */     String str = "/c4Q2hAVXFc=";
/*      */     
/* 1484 */     recordSet.executeSql("select * from datasourcesetting where pointid = '" + paramString + "' ");
/* 1485 */     if (recordSet.next()) {
/* 1486 */       String str1 = Util.null2String(recordSet.getString("classpath"));
/* 1487 */       String str2 = Util.null2String(recordSet.getString("type"));
/* 1488 */       String str3 = Util.null2String(recordSet.getString("iscluster"));
/* 1489 */       String str4 = Util.null2String(recordSet.getString("typename"));
/* 1490 */       String str5 = Util.null2String(recordSet.getString("datasourcename"));
/* 1491 */       String str6 = Util.null2String(recordSet.getString("url"));
/* 1492 */       String str7 = Util.null2String(recordSet.getString("host"));
/* 1493 */       String str8 = Util.null2String(recordSet.getString("port"));
/* 1494 */       String str9 = Util.null2String(recordSet.getString("dbname"));
/* 1495 */       String str10 = Util.null2String(recordSet.getString("usepool"));
/* 1496 */       String str11 = Util.null2String(recordSet.getString("sortid"));
/* 1497 */       String str12 = Util.null2String(recordSet.getString("minconn"));
/* 1498 */       String str13 = Util.null2String(recordSet.getString("maxconn"));
/* 1499 */       String str14 = Util.null2String(recordSet.getString("username"));
/* 1500 */       String str15 = Util.null2String(recordSet.getString("password"));
/* 1501 */       String str16 = Util.null2String(recordSet.getString("iscode"));
/*      */       
/* 1503 */       String str17 = str6;
/* 1504 */       String str18 = str7;
/* 1505 */       String str19 = str8;
/* 1506 */       String str20 = str9;
/* 1507 */       String str21 = str14;
/* 1508 */       String str22 = str15;
/*      */ 
/*      */       
/* 1511 */       str6 = (str6.indexOf(str) > -1) ? SecurityHelper.decrypt("ecology", str6) : str6;
/* 1512 */       str7 = (str7.indexOf(str) > -1) ? SecurityHelper.decrypt("ecology", str7) : str7;
/* 1513 */       str8 = (str8.indexOf(str) > -1) ? SecurityHelper.decrypt("ecology", str8) : str8;
/* 1514 */       str9 = (str9.indexOf(str) > -1) ? SecurityHelper.decrypt("ecology", str9) : str9;
/* 1515 */       str14 = (str14.indexOf(str) > -1) ? SecurityHelper.decrypt("ecology", str14) : str14;
/* 1516 */       str15 = (str15.indexOf(str) > -1) ? SecurityHelper.decrypt("ecology", str15) : str15;
/*      */       
/* 1518 */       String str23 = "1";
/* 1519 */       if ("2".equals(str3)) {
/* 1520 */         if ("".equals(str6) || "".equals(str14) || "".equals(str15)) {
/* 1521 */           str23 = "0";
/*      */         }
/*      */       }
/* 1524 */       else if (str2.toLowerCase().indexOf("hana") > -1) {
/* 1525 */         if ("".equals(str7) || "".equals(str8) || "".equals(str14) || "".equals(str15)) {
/* 1526 */           str23 = "0";
/*      */         }
/* 1528 */       } else if (str2.toLowerCase().indexOf("odbc") > -1) {
/* 1529 */         if ("".equals(str9) || "".equals(str14) || "".equals(str15)) {
/* 1530 */           str23 = "0";
/*      */         }
/* 1532 */       } else if (str2.toLowerCase().indexOf("access") > -1) {
/* 1533 */         if ("".equals(str9)) {
/* 1534 */           str23 = "0";
/*      */         }
/*      */       }
/* 1537 */       else if ("".equals(str7) || "".equals(str8) || "".equals(str9) || "".equals(str14) || "".equals(str15)) {
/* 1538 */         str23 = "0";
/*      */       } 
/*      */ 
/*      */ 
/*      */       
/* 1543 */       if ("0".equals(str23)) {
/* 1544 */         newlog.error("Datasource加入缓存异常(pointid=" + paramString + ")：数据源停止加载，初始化DB连接数据不完整。 type=" + str2 + ", url=" + str6 + ", url2=" + str17 + ", host=" + str7 + ", host2=" + str18 + ", port=" + str8 + ", port2=" + str19 + ", dbname=" + str9 + ", dbname2=" + str20 + ", username=" + str14 + ", username2=" + str21 + ", password=" + str15 + ", password2=" + str22);
/*      */         
/*      */         return;
/*      */       } 
/*      */       
/* 1549 */       BaseDataSource baseDataSource1 = null;
/*      */       try {
/* 1551 */         Class<?> clazz = Class.forName(str1);
/* 1552 */         baseDataSource1 = (BaseDataSource)clazz.newInstance();
/* 1553 */       } catch (Exception exception) {
/* 1554 */         newlog.error("更新数据源(" + paramString + ")缓存异常：", exception);
/*      */       } 
/* 1556 */       baseDataSource1.setType(str2);
/* 1557 */       baseDataSource1.setIscluster(str3);
/* 1558 */       baseDataSource1.setTypename(str4);
/* 1559 */       baseDataSource1.setDatasourcename(str5);
/* 1560 */       baseDataSource1.setUrl(str6);
/* 1561 */       baseDataSource1.setHost(str7);
/* 1562 */       baseDataSource1.setPort(str8);
/* 1563 */       baseDataSource1.setDbname(str9);
/*      */       
/* 1565 */       baseDataSource1.setUsepool(Util.getIntValue(str10));
/* 1566 */       baseDataSource1.setSortid(Util.getIntValue(str11));
/* 1567 */       baseDataSource1.setMinconn(Util.getIntValue(str12));
/* 1568 */       baseDataSource1.setMaxconn(Util.getIntValue(str13));
/*      */       
/* 1570 */       baseDataSource1.setIscode(str16);
/* 1571 */       baseDataSource1.setUser(str14);
/* 1572 */       baseDataSource1.setPassword(str15);
/*      */       
/* 1574 */       BaseDataSource baseDataSource2 = baseDataSource1;
/* 1575 */       hashtable.put("datasource." + paramString, baseDataSource2);
/*      */     } 
/* 1577 */     StaticObj.getInstance().putObject("registry", hashtable);
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/servicefiles/DataSourceXML.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */