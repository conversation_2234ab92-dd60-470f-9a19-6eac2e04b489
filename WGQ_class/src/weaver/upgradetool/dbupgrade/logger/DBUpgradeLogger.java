/*     */ package weaver.upgradetool.dbupgrade.logger;
/*     */ 
/*     */ import java.io.BufferedWriter;
/*     */ import java.io.File;
/*     */ import java.io.FileWriter;
/*     */ import java.io.IOException;
/*     */ import java.io.PrintWriter;
/*     */ import java.util.regex.Matcher;
/*     */ import java.util.regex.Pattern;
/*     */ import weaver.conn.ConnStatement;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.upgradetool.dbupgrade.upgrade.ToolUtil;
/*     */ 
/*     */ 
/*     */ 
/*     */ public class DBUpgradeLogger
/*     */ {
/*  19 */   private static String LOGFILE = ToolUtil.LOGFILE;
/*  20 */   Pattern pattern = null;
/*  21 */   Matcher matcher = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static synchronized void write2File(String paramString) {
/*  28 */     log2File(paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static synchronized void writeErrorLog2File(String paramString) {
/*  35 */     errorLog2File(paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static synchronized void write2DB(UpgradeLog paramUpgradeLog) {
/*  43 */     ConnStatement connStatement = new ConnStatement();
/*  44 */     String str = "insert into DBUpgradelog(modifyname,modifyfieldname,modifytype,modifycontent,modifystatus,type,errorlog,modifytime) values (?,?,?,?,?,?,?,?)";
/*     */     try {
/*  46 */       connStatement.setStatementSql(str);
/*  47 */       connStatement.setString(1, paramUpgradeLog.getModifyName());
/*  48 */       connStatement.setString(2, paramUpgradeLog.getModifyFieldName());
/*  49 */       connStatement.setString(3, paramUpgradeLog.getModifyType());
/*  50 */       connStatement.setString(4, paramUpgradeLog.getModifyContent());
/*  51 */       connStatement.setString(5, paramUpgradeLog.getModifyStatus());
/*  52 */       connStatement.setString(6, paramUpgradeLog.getType());
/*  53 */       connStatement.setString(7, paramUpgradeLog.getErrorlog());
/*  54 */       connStatement.setString(8, (null == paramUpgradeLog.getModifyTime() || "".equals(Boolean.valueOf((paramUpgradeLog.getModifyTime() == "")))) ? TimeUtil.getCurrentTimeString() : paramUpgradeLog.getModifyTime());
/*     */       
/*  56 */       connStatement.executeUpdate();
/*  57 */       connStatement.close();
/*  58 */     } catch (Exception exception) {
/*  59 */       exception.printStackTrace();
/*     */     } finally {
/*  61 */       connStatement.close();
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static synchronized void log2File(String paramString) {
/*  74 */     FileWriter fileWriter = null;
/*  75 */     BufferedWriter bufferedWriter = null;
/*  76 */     PrintWriter printWriter = null;
/*     */     try {
/*  78 */       String str1 = TimeUtil.getCurrentDateString();
/*  79 */       String str2 = LOGFILE;
/*  80 */       File file1 = new File(str2);
/*  81 */       if (!file1.exists()) {
/*  82 */         file1.mkdirs();
/*     */       }
/*  84 */       File file2 = new File(str2 + "dbupgrade" + str1 + ".log");
/*  85 */       if (!file2.exists()) {
/*  86 */         file2.createNewFile();
/*     */       }
/*     */       
/*  89 */       fileWriter = new FileWriter(file2, true);
/*  90 */       bufferedWriter = new BufferedWriter(fileWriter);
/*  91 */       printWriter = new PrintWriter(bufferedWriter);
/*  92 */       String str3 = TimeUtil.getCurrentTimeString();
/*  93 */       printWriter.println("###[" + str3 + "]:" + paramString);
/*     */       
/*  95 */       printWriter.flush();
/*  96 */       bufferedWriter.flush();
/*  97 */       fileWriter.flush();
/*     */       
/*  99 */       printWriter.close();
/* 100 */       bufferedWriter.close();
/* 101 */       fileWriter.close();
/* 102 */     } catch (IOException iOException) {
/* 103 */       iOException.printStackTrace();
/*     */     } finally {
/* 105 */       if (printWriter != null) {
/* 106 */         printWriter.flush();
/* 107 */         printWriter.close();
/*     */       } 
/* 109 */       if (bufferedWriter != null) {
/*     */         try {
/* 111 */           bufferedWriter.flush();
/* 112 */           bufferedWriter.close();
/* 113 */         } catch (IOException iOException) {}
/*     */       }
/* 115 */       if (bufferedWriter != null) {
/*     */         try {
/* 117 */           fileWriter.flush();
/* 118 */           fileWriter.close();
/* 119 */         } catch (IOException iOException) {}
/*     */       }
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static synchronized void errorLog2File(String paramString) {
/* 130 */     FileWriter fileWriter = null;
/* 131 */     BufferedWriter bufferedWriter = null;
/* 132 */     PrintWriter printWriter = null;
/*     */     try {
/* 134 */       String str1 = TimeUtil.getCurrentDateString();
/* 135 */       String str2 = LOGFILE;
/* 136 */       File file1 = new File(str2);
/* 137 */       if (!file1.exists()) {
/* 138 */         file1.mkdirs();
/*     */       }
/* 140 */       File file2 = new File(str2 + "dbupgrade" + str1 + "_error.log");
/* 141 */       if (!file2.exists()) {
/* 142 */         file2.createNewFile();
/*     */       }
/*     */       
/* 145 */       fileWriter = new FileWriter(file2, true);
/* 146 */       bufferedWriter = new BufferedWriter(fileWriter);
/* 147 */       printWriter = new PrintWriter(bufferedWriter);
/* 148 */       String str3 = TimeUtil.getCurrentTimeString();
/* 149 */       printWriter.println("###[" + str3 + "]:" + paramString);
/*     */       
/* 151 */       printWriter.flush();
/* 152 */       bufferedWriter.flush();
/* 153 */       fileWriter.flush();
/*     */       
/* 155 */       printWriter.close();
/* 156 */       bufferedWriter.close();
/* 157 */       fileWriter.close();
/* 158 */     } catch (IOException iOException) {
/* 159 */       iOException.printStackTrace();
/*     */     } finally {
/* 161 */       if (printWriter != null) {
/* 162 */         printWriter.flush();
/* 163 */         printWriter.close();
/*     */       } 
/* 165 */       if (bufferedWriter != null) {
/*     */         try {
/* 167 */           bufferedWriter.flush();
/* 168 */           bufferedWriter.close();
/* 169 */         } catch (IOException iOException) {}
/*     */       }
/* 171 */       if (bufferedWriter != null) {
/*     */         try {
/* 173 */           fileWriter.flush();
/* 174 */           fileWriter.close();
/* 175 */         } catch (IOException iOException) {}
/*     */       }
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public UpgradeLog getUpgradeLogObj(String paramString) {
/* 185 */     UpgradeLog upgradeLog = new UpgradeLog();
/* 186 */     upgradeLog.setModifyContent(paramString);
/*     */     
/* 188 */     if (checkMatchReg(paramString, "(?i)[ ]+sequence[ ]+")) {
/* 189 */       if (checkMatchReg(paramString, "(?i)^[ ]*?drop[ ]*?")) {
/* 190 */         upgradeLog.setType("80");
/* 191 */       } else if (checkMatchReg(paramString, "(?i)^[ ]*?create[ ]*?")) {
/* 192 */         upgradeLog.setType("81");
/* 193 */       } else if (checkMatchReg(paramString, "(?i)^[ ]*?alter[ ]*?")) {
/* 194 */         upgradeLog.setType("82");
/*     */       } 
/* 196 */       upgradeLog.setModifyName(matchReg(paramString, "(?i)(?:sequence[ ]*)([^\\(\\); ]*)"));
/* 197 */       upgradeLog.setModifyFieldName("");
/* 198 */       upgradeLog.setModifyType("8");
/* 199 */       return upgradeLog;
/*     */     } 
/*     */     
/* 202 */     if (checkMatchReg(paramString, "(?i)[ ]+trigger[ ]+")) {
/* 203 */       if (checkMatchReg(paramString, "(?i)^[ ]*?drop[ ]*?")) {
/* 204 */         upgradeLog.setType("50");
/* 205 */       } else if (checkMatchReg(paramString, "(?i)^[ ]*?create[ ]*?")) {
/* 206 */         upgradeLog.setType("51");
/* 207 */       } else if (checkMatchReg(paramString, "(?i)^[ ]*?alter[ ]*?")) {
/* 208 */         upgradeLog.setType("52");
/*     */       } 
/* 210 */       upgradeLog.setModifyName(matchReg(paramString, "(?i)(?:trigger[ ]*)([^\\(\\); ]*)"));
/* 211 */       upgradeLog.setModifyFieldName("");
/* 212 */       upgradeLog.setModifyType("5");
/* 213 */       return upgradeLog;
/*     */     } 
/*     */     
/* 216 */     if (checkMatchReg(paramString, "(?i)[ ]+PROCEDURE[ ]+")) {
/* 217 */       if (checkMatchReg(paramString, "(?i)^[ ]*?drop[ ]*?")) {
/* 218 */         upgradeLog.setType("40");
/* 219 */       } else if (checkMatchReg(paramString, "(?i)^[ ]*?create[ ]*?")) {
/* 220 */         upgradeLog.setType("41");
/* 221 */       } else if (checkMatchReg(paramString, "(?i)^[ ]*?alter[ ]*?")) {
/* 222 */         upgradeLog.setType("42");
/*     */       } 
/* 224 */       upgradeLog.setModifyName(matchReg(paramString, "(?i)(?:PROCEDURE[ ]*)([^\\(\\); ]*)"));
/* 225 */       upgradeLog.setModifyFieldName("");
/* 226 */       upgradeLog.setModifyType("4");
/* 227 */       return upgradeLog;
/*     */     } 
/*     */     
/* 230 */     if (checkMatchReg(paramString, "(?i)[ ]+function[ ]+")) {
/* 231 */       if (checkMatchReg(paramString, "(?i)^[ ]*?drop[ ]*?")) {
/* 232 */         upgradeLog.setType("60");
/* 233 */       } else if (checkMatchReg(paramString, "(?i)^[ ]*?create[ ]*?")) {
/* 234 */         upgradeLog.setType("61");
/* 235 */       } else if (checkMatchReg(paramString, "(?i)^[ ]*?alter[ ]*?")) {
/* 236 */         upgradeLog.setType("62");
/*     */       } 
/* 238 */       upgradeLog.setModifyName(matchReg(paramString, "(?i)(?:function[ ]*)([^\\(\\); ]*)"));
/* 239 */       upgradeLog.setModifyFieldName("");
/* 240 */       upgradeLog.setModifyType("6");
/* 241 */       return upgradeLog;
/*     */     } 
/*     */     
/* 244 */     if (checkMatchReg(paramString, "(?i)[ ]+view[ ]+")) {
/* 245 */       if (checkMatchReg(paramString, "(?i)^[ ]*?drop[ ]*?")) {
/* 246 */         upgradeLog.setType("70");
/* 247 */       } else if (checkMatchReg(paramString, "(?i)^[ ]*?create[ ]*?")) {
/* 248 */         upgradeLog.setType("71");
/* 249 */       } else if (checkMatchReg(paramString, "(?i)^[ ]*?alter[ ]*?")) {
/* 250 */         upgradeLog.setType("72");
/*     */       } 
/* 252 */       upgradeLog.setModifyName(matchReg(paramString, "(?i)(?:view[ ]*)([^\\(\\); ]*)"));
/* 253 */       upgradeLog.setModifyFieldName("");
/* 254 */       upgradeLog.setModifyType("7");
/* 255 */       return upgradeLog;
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 260 */     if (checkMatchReg(paramString, "(?i)[ ]+index[ ]+")) {
/* 261 */       if (checkMatchReg(paramString, "(?i)^[ ]*?drop[ ]*?")) {
/* 262 */         upgradeLog.setType("90");
/* 263 */         upgradeLog.setModifyName(matchReg(paramString, "(?i)(?:index[ ]*)([^\\(\\); ]*)"));
/* 264 */       } else if (checkMatchReg(paramString, "(?i)^[ ]*?create[ ]*?")) {
/* 265 */         upgradeLog.setType("91");
/* 266 */         upgradeLog.setModifyName(matchReg(paramString, "(?i)(?:index[ ]*)([^\\(\\); ]*)"));
/*     */       } 
/* 268 */       upgradeLog.setModifyFieldName("");
/* 269 */       upgradeLog.setModifyType("9");
/* 270 */       return upgradeLog;
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 277 */     if (checkMatchReg(paramString, "(?i)^[ ]*?update[ ]*?")) {
/* 278 */       upgradeLog.setType("33");
/* 279 */       upgradeLog.setModifyName(matchReg(paramString, "(?i)(?:update[ ]*)(\\S*)(?:[ ]*set)"));
/* 280 */       String str = matchReg(paramString, "(?i)(?:.*set[ ]*)([^\\); ]*)");
/* 281 */       str = str.replaceAll(" ", "").replaceAll("=[^,]*", "");
/* 282 */       upgradeLog.setModifyFieldName(str);
/* 283 */       upgradeLog.setModifyType("3");
/* 284 */     } else if (checkMatchReg(paramString, "(?i)^[ ]*?create[ ]*?")) {
/* 285 */       upgradeLog.setType("11");
/* 286 */       upgradeLog.setModifyName(matchReg(paramString, "(?i)(?:table[ ]*)([^\\(\\); ]*)"));
/* 287 */       upgradeLog.setModifyFieldName("");
/* 288 */       upgradeLog.setModifyType("1");
/* 289 */     } else if (checkMatchReg(paramString, "(?i)^[ ]*?delete[ ]*?")) {
/* 290 */       upgradeLog.setType("30");
/* 291 */       upgradeLog.setModifyName(matchReg(paramString, "(?i)(?:from[ ]*)([^\\); ]*)"));
/* 292 */       upgradeLog.setModifyFieldName("");
/* 293 */       upgradeLog.setModifyType("3");
/* 294 */     } else if (checkMatchReg(paramString, "(?i)^[ ]*?insert[ ]*?")) {
/* 295 */       upgradeLog.setType("31");
/* 296 */       upgradeLog.setModifyName(matchReg(paramString, "(?i)(?:into[ ]*)([^\\); ]*)"));
/* 297 */       upgradeLog.setModifyFieldName("");
/* 298 */       upgradeLog.setModifyType("3");
/* 299 */     } else if (checkMatchReg(paramString, "(?i)^[ ]*?drop[ ]*?")) {
/* 300 */       upgradeLog.setType("10");
/* 301 */       upgradeLog.setModifyName(matchReg(paramString, "(?i)(?:table[ ]*)([^\\); ]*)"));
/* 302 */       upgradeLog.setModifyFieldName("");
/* 303 */       upgradeLog.setModifyType("1");
/* 304 */     } else if (checkMatchReg(paramString, "(?i)^[ ]*?alter[ ]*?")) {
/* 305 */       String str = getDBType();
/*     */       
/* 307 */       if (str.equalsIgnoreCase("oracle")) {
/* 308 */         if (checkMatchReg(paramString, "(?i)[ ]+add[ \\(]+")) {
/* 309 */           if (checkMatchReg(paramString, "(?i)[ ]+add[ \\(]+constraint")) {
/*     */             
/* 311 */             upgradeLog.setType("101");
/* 312 */             upgradeLog.setModifyName(matchReg(paramString, "(?i)(?:constraint[ ]*)([^\\); ]*)"));
/* 313 */             upgradeLog.setModifyType("10");
/*     */           } else {
/*     */             
/* 316 */             upgradeLog.setType("21");
/* 317 */             upgradeLog.setModifyName(matchReg(paramString, "(?i)(?:table[ ]*)([^\\); ]*)"));
/* 318 */             upgradeLog.setModifyFieldName(matchReg(paramString, "(?i)(?:add[ \\(]*)(?:column[ \\(]*)*([^\\); ]*)"));
/* 319 */             upgradeLog.setModifyType("2");
/*     */           } 
/* 321 */         } else if (checkMatchReg(paramString, "(?i)[ ]+modify[ \\(]+")) {
/* 322 */           upgradeLog.setType("22");
/* 323 */           upgradeLog.setModifyName(matchReg(paramString, "(?i)(?:table[ ]*)([^\\); ]*)"));
/* 324 */           upgradeLog.setModifyFieldName(matchReg(paramString, "(?i)(?:modify[ \\(]*)(?:column[ \\(]*)*([^\\); ]*)"));
/* 325 */           upgradeLog.setModifyType("2");
/*     */         }
/* 327 */         else if (checkMatchReg(paramString, "(?i)[ ]+drop[ \\(]+")) {
/* 328 */           if (checkMatchReg(paramString, "(?i)[ ]+drop[ \\(]+constraint")) {
/*     */             
/* 330 */             upgradeLog.setType("100");
/* 331 */             upgradeLog.setModifyName(matchReg(paramString, "(?i)(?:constraint[ ]*)([^\\); ]*)"));
/* 332 */             upgradeLog.setModifyType("10");
/*     */           }
/*     */           else {
/*     */             
/* 336 */             upgradeLog.setType("20");
/* 337 */             upgradeLog.setModifyName(matchReg(paramString, "(?i)(?:table[ ]*)([^\\); ]*)"));
/* 338 */             upgradeLog.setModifyFieldName(matchReg(paramString, "(?i)(?:drop[ \\(]*)(?:column[ \\(]*)*([^\\); ]*)"));
/* 339 */             upgradeLog.setModifyType("2");
/*     */           } 
/* 341 */         } else if (checkMatchReg(paramString, "(?i).*?rename.*?column。*?")) {
/* 342 */           upgradeLog.setType("22");
/* 343 */           upgradeLog.setModifyName(matchReg(paramString, "(?i)(?:table[ ]*)([^\\); ]*)"));
/* 344 */           upgradeLog.setModifyFieldName(matchReg(paramString, "(?i)(?:rename[ \\(]*)(?:column[ \\(]*)*([^\\); ]*)"));
/* 345 */           upgradeLog.setModifyType("2");
/*     */         }
/* 347 */         else if (checkMatchReg(paramString, "(?i).*?rename[ ]*?to.*?")) {
/* 348 */           upgradeLog.setType("10");
/* 349 */           upgradeLog.setModifyName(matchReg(paramString, "(?i)(?:table[ ]*)([^\\); ]*)"));
/* 350 */           upgradeLog.setModifyFieldName("");
/* 351 */           upgradeLog.setModifyType("2");
/*     */         } 
/* 353 */       } else if (str.equalsIgnoreCase("sqlserver")) {
/* 354 */         if (checkMatchReg(paramString, "(?i)[ ]+add[ \\(]+")) {
/* 355 */           if (checkMatchReg(paramString, "(?i)[ ]+add[ \\(]+constraint")) {
/* 356 */             upgradeLog.setType("101");
/* 357 */             upgradeLog.setModifyName(matchReg(paramString, "(?i)(?:constraint[ ]*)([^\\); ]*)"));
/* 358 */             upgradeLog.setModifyType("10");
/*     */           } else {
/*     */             
/* 361 */             upgradeLog.setType("21");
/* 362 */             upgradeLog.setModifyName(matchReg(paramString, "(?i)(?:table[ ]*)([^\\); ]*)"));
/* 363 */             upgradeLog.setModifyFieldName(matchReg(paramString, "(?i)(?:add[ \\(]*)([^\\); ]*)"));
/* 364 */             upgradeLog.setModifyType("2");
/*     */           } 
/* 366 */         } else if (checkMatchReg(paramString, "(?i).*?alter.*?alter.*?")) {
/* 367 */           upgradeLog.setType("22");
/* 368 */           upgradeLog.setModifyName(matchReg(paramString, "(?i)(?:table[ ]*)([^\\); ]*)"));
/* 369 */           upgradeLog.setModifyFieldName(matchReg(paramString, "(?i)(?:column[ ]*)([^\\); ]*)"));
/* 370 */           upgradeLog.setModifyType("2");
/*     */         }
/* 372 */         else if (checkMatchReg(paramString, "(?i)[ ]+drop[ \\(]+")) {
/* 373 */           if (checkMatchReg(paramString, "(?i)[ ]+drop[ \\(]+constraint")) {
/* 374 */             upgradeLog.setType("100");
/* 375 */             upgradeLog.setModifyName(matchReg(paramString, "(?i)(?:constraint[ ]*)([^\\); ]*)"));
/* 376 */             upgradeLog.setModifyType("10");
/*     */           } else {
/* 378 */             upgradeLog.setType("20");
/* 379 */             upgradeLog.setModifyName(matchReg(paramString, "(?i)(?:table[ ]*)([^\\); ]*)"));
/* 380 */             upgradeLog.setModifyFieldName(matchReg(paramString, "(?i)(?:drop[ \\(]*)(?:column[ \\(]*)*([^\\); ]*)"));
/* 381 */             upgradeLog.setModifyType("2");
/*     */           }
/*     */         
/* 384 */         } else if (checkMatchReg(paramString, "(?i)[ ]+rename[ \\(]+")) {
/* 385 */           upgradeLog.setType("22");
/* 386 */           upgradeLog.setModifyName(matchReg(paramString, "(?i)(?:table[ ]*)([^\\); ]*)"));
/* 387 */           upgradeLog.setModifyFieldName(matchReg(paramString, "(?i)(?:rename[ \\(]*)(?:column[ \\(]*)*([^\\); ]*)"));
/* 388 */           upgradeLog.setModifyType("2");
/*     */         }
/*     */       
/*     */       } 
/* 392 */     } else if (checkMatchReg(paramString, "(?i)^[ ]*?truncate[ ]*?")) {
/* 393 */       upgradeLog.setType("32");
/* 394 */       upgradeLog.setModifyName(matchReg(paramString, "(?i)(?:table[ ]*)([^\\); ]*)"));
/* 395 */       upgradeLog.setModifyName("");
/* 396 */       upgradeLog.setModifyType("3");
/* 397 */     } else if (checkMatchReg(paramString, "(?i)^[ ]*?rename[ ]*?")) {
/* 398 */       upgradeLog.setType("10");
/* 399 */       upgradeLog.setModifyName(matchReg(paramString, "(?i)(?:table[ ]*)([^\\); ]*)"));
/* 400 */       upgradeLog.setModifyName("");
/* 401 */       upgradeLog.setModifyType("1");
/*     */     } 
/*     */     
/* 404 */     return upgradeLog;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDBType() {
/* 412 */     RecordSet recordSet = new RecordSet();
/* 413 */     return recordSet.getDBType(null);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String matchReg(String paramString1, String paramString2) {
/* 422 */     String str = "";
/*     */     try {
/* 424 */       this.pattern = Pattern.compile(paramString2);
/* 425 */       this.matcher = this.pattern.matcher(paramString1);
/* 426 */       if (this.matcher.find()) {
/* 427 */         str = this.matcher.group(1);
/*     */       }
/* 429 */     } catch (Exception exception) {
/* 430 */       exception.printStackTrace();
/*     */     } 
/* 432 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean checkMatchReg(String paramString1, String paramString2) {
/* 442 */     boolean bool = false;
/*     */     try {
/* 444 */       this.pattern = Pattern.compile(paramString2);
/* 445 */       this.matcher = this.pattern.matcher(paramString1);
/* 446 */       if (this.matcher.find()) {
/* 447 */         bool = true;
/*     */       }
/* 449 */     } catch (Exception exception) {
/* 450 */       exception.printStackTrace();
/*     */     } 
/* 452 */     return bool;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/upgradetool/dbupgrade/logger/DBUpgradeLogger.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */