/*    */ package weaver.upgradetool.dbupgrade.logger;
/*    */ 
/*    */ public class UpgradeLog {
/*    */   private String modifyName;
/*    */   private String modifyFieldName;
/*    */   private String modifyType;
/*    */   private String modifyContent;
/*    */   private String modifyStatus;
/*    */   private String type;
/*    */   private String errorlog;
/*    */   private String modifyTime;
/*    */   
/*    */   public String getModifyName() {
/* 14 */     return this.modifyName;
/*    */   }
/*    */   public void setModifyName(String paramString) {
/* 17 */     this.modifyName = paramString;
/*    */   }
/*    */   public String getModifyFieldName() {
/* 20 */     return this.modifyFieldName;
/*    */   }
/*    */   public void setModifyFieldName(String paramString) {
/* 23 */     this.modifyFieldName = paramString;
/*    */   }
/*    */   public String getModifyType() {
/* 26 */     return this.modifyType;
/*    */   }
/*    */   public void setModifyType(String paramString) {
/* 29 */     this.modifyType = paramString;
/*    */   }
/*    */   public String getModifyContent() {
/* 32 */     return this.modifyContent;
/*    */   }
/*    */   public void setModifyContent(String paramString) {
/* 35 */     this.modifyContent = paramString;
/*    */   }
/*    */   public String getModifyStatus() {
/* 38 */     return this.modifyStatus;
/*    */   }
/*    */   public void setModifyStatus(String paramString) {
/* 41 */     this.modifyStatus = paramString;
/*    */   }
/*    */   public String getType() {
/* 44 */     return this.type;
/*    */   }
/*    */   public void setType(String paramString) {
/* 47 */     this.type = paramString;
/*    */   }
/*    */   public String getErrorlog() {
/* 50 */     return this.errorlog;
/*    */   }
/*    */   public void setErrorlog(String paramString) {
/* 53 */     this.errorlog = paramString;
/*    */   }
/*    */   public String getModifyTime() {
/* 56 */     return this.modifyTime;
/*    */   }
/*    */   public void setModifyTime(String paramString) {
/* 59 */     this.modifyTime = paramString;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/upgradetool/dbupgrade/logger/UpgradeLog.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */