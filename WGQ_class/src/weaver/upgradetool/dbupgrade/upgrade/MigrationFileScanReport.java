/*     */ package weaver.upgradetool.dbupgrade.upgrade;
/*     */ 
/*     */ import java.io.File;
/*     */ import java.io.FileWriter;
/*     */ import java.util.Hashtable;
/*     */ import java.util.List;
/*     */ import org.jdom.Element;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.file.ExcelFile;
/*     */ import weaver.file.ExcelRow;
/*     */ import weaver.file.ExcelSheet;
/*     */ import weaver.file.ExcelStyle;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.Util;
/*     */ import weaver.servicefiles.GetXMLContent;
/*     */ import weaver.upgradetool.dbupgrade.logger.DBUpgradeLogger;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class MigrationFileScanReport
/*     */ {
/*     */   private ExcelFile ef;
/*     */   private ExcelSheet es;
/*     */   FileWriter fw;
/*     */   
/*     */   public MigrationFileScanReport(String paramString) {
/*  36 */     String str = GCONST.getRootPath() + "system" + File.separatorChar + "upgradetoe9" + File.separatorChar + "report" + File.separatorChar + "migrationFile.txt";
/*  37 */     this.ef = new ExcelFile();
/*  38 */     this.es = new ExcelSheet();
/*  39 */     ExcelRow excelRow = this.es.newExcelRow();
/*  40 */     this.ef.init();
/*  41 */     this.es.addColumnwidth(2000);
/*  42 */     this.es.addColumnwidth(4000);
/*  43 */     this.es.addColumnwidth(4000);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  49 */     ExcelStyle excelStyle1 = this.ef.newExcelStyle("TopInfo");
/*  50 */     excelStyle1.setGroundcolor(ExcelStyle.WeaverLightGroundcolor);
/*  51 */     excelStyle1.setFontcolor(ExcelStyle.WeaverHeaderFontcolor);
/*  52 */     excelStyle1.setFontbold(ExcelStyle.WeaverHeaderFontbold);
/*  53 */     excelStyle1.setAlign(ExcelStyle.ALIGN_LEFT);
/*     */     
/*  55 */     ExcelStyle excelStyle2 = this.ef.newExcelStyle("Header");
/*  56 */     excelStyle2.setGroundcolor(ExcelStyle.WeaverHeaderGroundcolor1);
/*  57 */     excelStyle2.setFontcolor(ExcelStyle.WeaverHeaderFontcolor);
/*  58 */     excelStyle2.setFontbold(ExcelStyle.WeaverHeaderFontbold);
/*  59 */     excelStyle2.setAlign(ExcelStyle.ALIGN_LEFT);
/*     */     
/*  61 */     ExcelStyle excelStyle3 = this.ef.newExcelStyle("dark");
/*  62 */     excelStyle3.setGroundcolor(ExcelStyle.WeaverDarkGroundcolor);
/*  63 */     excelStyle3.setFontcolor(ExcelStyle.WeaverHeaderFontcolor);
/*  64 */     excelStyle3.setAlign(ExcelStyle.ALIGN_LEFT);
/*     */     
/*  66 */     ExcelStyle excelStyle4 = this.ef.newExcelStyle("light");
/*  67 */     excelStyle4.setGroundcolor(ExcelStyle.WeaverLightGroundcolor);
/*  68 */     excelStyle4.setFontcolor(ExcelStyle.WeaverHeaderFontcolor);
/*  69 */     excelStyle4.setAlign(ExcelStyle.ALIGN_LEFT);
/*     */     
/*  71 */     this.ef.setFilename(paramString);
/*  72 */     this.ef.addSheet(paramString, this.es);
/*  73 */     excelRow.addStringValue("迁移对象名", "Header");
/*  74 */     excelRow.addStringValue("迁移文件类型", "Header");
/*  75 */     excelRow.addStringValue("迁移文件路径", "Header");
/*  76 */     this.es.addExcelRow(excelRow);
/*     */ 
/*     */     
/*     */     try {
/*  80 */       File file = new File(str);
/*  81 */       if (file.exists() && file.isFile()) {
/*  82 */         file.delete();
/*     */       } else {
/*  84 */         File file1 = file.getParentFile();
/*  85 */         if (!file1.exists()) {
/*  86 */           file1.mkdir();
/*     */         }
/*     */       } 
/*  89 */       file.createNewFile();
/*  90 */       this.fw = new FileWriter(file);
/*  91 */     } catch (Exception exception) {
/*  92 */       DBUpgradeLogger.write2File("创建日志存放文件失败");
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean ScanXmlFileToExcel(String paramString1, String paramString2, String paramString3) {
/* 102 */     DBUpgradeLogger.write2File("开始执行扫描问题" + paramString3);
/*     */     
/* 104 */     GetXMLContent getXMLContent = GetXMLContent.getObjXML();
/* 105 */     boolean bool = true;
/* 106 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 108 */     Element element = null;
/* 109 */     if (paramString2 == null) {
/* 110 */       element = getXMLContent.getFileContent(paramString3);
/*     */     } else {
/* 112 */       element = getXMLContent.getFileContentByPath(paramString2 + File.separatorChar + paramString3);
/*     */     } 
/* 114 */     if (element == null);
/*     */ 
/*     */     
/* 117 */     List list = element.getChildren("service-point");
/*     */     
/*     */     try {
/* 120 */       for (Element element1 : list) {
/*     */         
/* 122 */         String str1 = element1.getAttributeValue("id");
/*     */         
/* 124 */         Hashtable<Object, Object> hashtable1 = new Hashtable<>();
/* 125 */         Hashtable<Object, Object> hashtable2 = new Hashtable<>();
/* 126 */         Element element2 = element1.getChild("invoke-factory").getChild("construct");
/* 127 */         String str2 = element2.getAttributeValue("class");
/* 128 */         String str3 = "/classbean/" + str2.replace(".", "/") + ".class";
/*     */         try {
/* 130 */           if (Class.forName(str2) == null) {
/* 131 */             bool = false;
/* 132 */             ExcelRow excelRow = this.es.newExcelRow();
/* 133 */             excelRow.addStringValue(Util.null2String(str1), "Border");
/* 134 */             excelRow.addStringValue(Util.null2String(paramString1), "Border");
/* 135 */             excelRow.addStringValue(Util.null2String(str3), "Border");
/* 136 */             this.es.addExcelRow(excelRow);
/* 137 */             this.fw.write(str3 + "\r\n");
/*     */           } 
/* 139 */         } catch (ClassNotFoundException classNotFoundException) {
/* 140 */           bool = false;
/* 141 */           ExcelRow excelRow = this.es.newExcelRow();
/* 142 */           excelRow.addStringValue(Util.null2String(str1), "Border");
/* 143 */           excelRow.addStringValue(Util.null2String(paramString1), "Border");
/* 144 */           excelRow.addStringValue(Util.null2String(str3), "Border");
/* 145 */           this.es.addExcelRow(excelRow);
/* 146 */           this.fw.write(str3 + "\r\n");
/* 147 */         } catch (Throwable throwable) {
/* 148 */           throwable.printStackTrace();
/*     */         } 
/*     */       } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 179 */       if (paramString1.equalsIgnoreCase("action")) {
/* 180 */         recordSet.executeQuery("select * from actionsetting ", new Object[0]);
/* 181 */         while (recordSet.next()) {
/* 182 */           String str1 = Util.null2String(recordSet.getString("actionname"));
/* 183 */           String str2 = Util.null2String(recordSet.getString("actionclass"));
/* 184 */           if (str2.equals("")) {
/*     */             continue;
/*     */           }
/* 187 */           String str3 = "/classbean/" + str2.replace(".", "/") + ".class";
/*     */           try {
/* 189 */             if (Class.forName(str3) == null) {
/* 190 */               bool = false;
/* 191 */               ExcelRow excelRow = this.es.newExcelRow();
/* 192 */               excelRow.addStringValue(Util.null2String(str1), "Border");
/* 193 */               excelRow.addStringValue(Util.null2String(paramString1), "Border");
/* 194 */               excelRow.addStringValue(Util.null2String(str3), "Border");
/* 195 */               this.es.addExcelRow(excelRow);
/* 196 */               this.fw.write(str3 + "\r\n");
/*     */             } 
/* 198 */           } catch (ClassNotFoundException classNotFoundException) {
/* 199 */             bool = false;
/* 200 */             ExcelRow excelRow = this.es.newExcelRow();
/* 201 */             excelRow.addStringValue(Util.null2String(str1), "Border");
/* 202 */             excelRow.addStringValue(Util.null2String(paramString1), "Border");
/* 203 */             excelRow.addStringValue(Util.null2String(str3), "Border");
/* 204 */             this.es.addExcelRow(excelRow);
/* 205 */             this.fw.write(str3 + "\r\n");
/* 206 */           } catch (Throwable throwable) {
/* 207 */             throwable.printStackTrace();
/*     */           } 
/*     */         } 
/* 210 */       } else if (paramString1.equalsIgnoreCase("schedule")) {
/*     */         
/* 212 */         recordSet.executeQuery("select pointid,classpath from schedulesetting ", new Object[0]);
/* 213 */         while (recordSet.next()) {
/* 214 */           String str1 = Util.null2String(recordSet.getString("pointid"));
/* 215 */           String str2 = Util.null2String(recordSet.getString("classpath"));
/* 216 */           if (str2.equals("")) {
/*     */             continue;
/*     */           }
/*     */           try {
/* 220 */             str2 = "/classbean/" + str2.replace(".", "/") + ".class";
/* 221 */             if (Class.forName(str2) == null) {
/* 222 */               bool = false;
/* 223 */               ExcelRow excelRow = this.es.newExcelRow();
/* 224 */               excelRow.addStringValue(Util.null2String(str1), "Border");
/* 225 */               excelRow.addStringValue(Util.null2String(paramString1), "Border");
/* 226 */               excelRow.addStringValue(Util.null2String(str2), "Border");
/* 227 */               this.es.addExcelRow(excelRow);
/* 228 */               this.fw.write(str2 + "\r\n");
/*     */             } 
/* 230 */           } catch (ClassNotFoundException classNotFoundException) {
/* 231 */             bool = false;
/* 232 */             ExcelRow excelRow = this.es.newExcelRow();
/* 233 */             excelRow.addStringValue(Util.null2String(str1), "Border");
/* 234 */             excelRow.addStringValue(Util.null2String(paramString1), "Border");
/* 235 */             excelRow.addStringValue(Util.null2String(str2), "Border");
/* 236 */             this.es.addExcelRow(excelRow);
/* 237 */             this.fw.write(str2 + "\r\n");
/* 238 */           } catch (Throwable throwable) {
/* 239 */             throwable.printStackTrace();
/*     */           } 
/*     */         } 
/*     */       } 
/* 243 */       this.fw.flush();
/* 244 */     } catch (Exception exception) {
/* 245 */       DBUpgradeLogger.write2File("读写文件出现错误");
/* 246 */     } catch (Throwable throwable) {
/* 247 */       DBUpgradeLogger.write2File("读写文件出现错误");
/*     */     } 
/* 249 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean scanTableToExcel(String paramString1, String paramString2, String paramString3) {
/* 261 */     RecordSet recordSet = new RecordSet();
/* 262 */     boolean bool = true;
/* 263 */     String str = GCONST.getRootPath();
/* 264 */     if (paramString1 == null || "".equalsIgnoreCase(paramString1)) {
/* 265 */       return true;
/*     */     }
/* 267 */     if (paramString2 == null || "".equalsIgnoreCase(paramString2)) {
/* 268 */       return true;
/*     */     }
/* 270 */     if (paramString3 == null || "".equalsIgnoreCase(paramString3)) {
/* 271 */       paramString3 = "1";
/*     */     }
/*     */     try {
/* 274 */       recordSet.executeQuery("select " + paramString2 + "," + paramString3 + " as filedname from " + paramString1 + "  where (" + paramString2 + " is not null or " + paramString2 + " <>'')", new Object[0]);
/* 275 */       while (recordSet.next()) {
/* 276 */         String str1 = Util.null2String(recordSet.getString(paramString2));
/* 277 */         DBUpgradeLogger.write2File("文件路径" + str1);
/* 278 */         if (str1.equalsIgnoreCase("")) {
/* 279 */           bool = false;
/*     */           continue;
/*     */         } 
/* 282 */         DBUpgradeLogger.write2File("问号位置:" + str1.indexOf("?"));
/* 283 */         if (str1.indexOf("?") > 0) {
/* 284 */           DBUpgradeLogger.write2File("处理参数2" + str1 + "问号位置:" + str1.indexOf("?"));
/* 285 */           str1 = str1.substring(0, str1.indexOf("?"));
/*     */         } 
/* 287 */         File file = null;
/* 288 */         if (str1.indexOf("/") == 0) {
/* 289 */           file = new File(str + str1);
/*     */         } else {
/* 291 */           file = new File(str1);
/*     */         } 
/*     */         
/* 294 */         if (file.exists() && file.isFile()) {
/*     */           continue;
/*     */         }
/*     */         
/* 298 */         ExcelRow excelRow = this.es.newExcelRow();
/* 299 */         excelRow.addStringValue(Util.null2String(Util.null2String(recordSet.getString("filedname")), "0"), "Border");
/* 300 */         excelRow.addStringValue(Util.null2String("page"), "Border");
/* 301 */         excelRow.addStringValue(Util.null2String(str1), "Border");
/* 302 */         this.es.addExcelRow(excelRow);
/* 303 */         this.fw.write(str1 + "\r\n");
/* 304 */         bool = false;
/*     */       } 
/*     */       
/* 307 */       this.fw.flush();
/* 308 */     } catch (Exception exception) {
/* 309 */       DBUpgradeLogger.write2File("执行扫描数据库表：" + paramString1 + "出现错误！");
/* 310 */       DBUpgradeLogger.write2File(exception.toString());
/* 311 */       bool = false;
/*     */     } 
/*     */     
/* 314 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean scanTableToExcel2(String paramString1, String paramString2, String paramString3) {
/* 327 */     RecordSet recordSet = new RecordSet();
/* 328 */     boolean bool = true;
/* 329 */     String str = GCONST.getRootPath();
/* 330 */     if (paramString1 == null || "".equalsIgnoreCase(paramString1)) {
/* 331 */       return true;
/*     */     }
/* 333 */     if (paramString2 == null || "".equalsIgnoreCase(paramString2)) {
/* 334 */       return true;
/*     */     }
/* 336 */     if (paramString3 == null || "".equalsIgnoreCase(paramString3)) {
/* 337 */       paramString3 = "1";
/*     */     }
/*     */     try {
/* 340 */       recordSet.executeQuery("select " + paramString2 + "," + paramString3 + " as filedname from " + paramString1 + "  where (" + paramString2 + " is not null or " + paramString2 + " <>'')", new Object[0]);
/* 341 */       while (recordSet.next()) {
/* 342 */         String str1 = Util.null2String(recordSet.getString(paramString2));
/* 343 */         String str2 = Util.null2String(recordSet.getString(paramString3));
/* 344 */         DBUpgradeLogger.write2File("文件路径" + str1);
/* 345 */         if (str1.equalsIgnoreCase("")) {
/* 346 */           bool = false;
/*     */           continue;
/*     */         } 
/* 349 */         String str3 = "/classbean/" + str1.replace(".", "/") + ".class";
/* 350 */         if (Class.forName(str3) == null) {
/* 351 */           bool = false;
/* 352 */           ExcelRow excelRow = this.es.newExcelRow();
/* 353 */           excelRow.addStringValue(Util.null2String(str2), "Border");
/* 354 */           excelRow.addStringValue(Util.null2String(paramString1), "Border");
/* 355 */           excelRow.addStringValue(Util.null2String(str3), "Border");
/* 356 */           this.es.addExcelRow(excelRow);
/* 357 */           this.fw.write(str3 + "\r\n");
/*     */         } 
/*     */       } 
/* 360 */       this.fw.flush();
/* 361 */     } catch (Exception exception) {
/* 362 */       DBUpgradeLogger.write2File("执行扫描数据库表：" + paramString1 + "出现错误！");
/* 363 */       DBUpgradeLogger.write2File(exception.toString());
/* 364 */       bool = false;
/*     */     } 
/*     */     
/* 367 */     return bool;
/*     */   }
/*     */   public void closeFW() {
/*     */     try {
/* 371 */       if (this.fw != null) {
/* 372 */         this.fw.close();
/*     */       }
/* 374 */     } catch (Exception exception) {
/* 375 */       DBUpgradeLogger.write2File("关闭文件流出现错误！");
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public ExcelFile getEf() {
/* 381 */     return this.ef;
/*     */   }
/*     */   
/*     */   public void setEf(ExcelFile paramExcelFile) {
/* 385 */     this.ef = paramExcelFile;
/*     */   }
/*     */   
/*     */   public ExcelSheet getEs() {
/* 389 */     return this.es;
/*     */   }
/*     */   
/*     */   public void setEs(ExcelSheet paramExcelSheet) {
/* 393 */     this.es = paramExcelSheet;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/upgradetool/dbupgrade/upgrade/MigrationFileScanReport.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */