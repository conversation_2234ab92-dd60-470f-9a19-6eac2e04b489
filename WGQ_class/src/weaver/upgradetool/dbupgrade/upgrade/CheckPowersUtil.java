/*    */ package weaver.upgradetool.dbupgrade.upgrade;
/*    */ 
/*    */ public class CheckPowersUtil {
/*    */   public static boolean initDataPower() {
/*  5 */     boolean bool = true;
/*  6 */     UpgradeRecordSet upgradeRecordSet = new UpgradeRecordSet();
/*  7 */     String str = " select * from DBUpgradeMain WHERE STATUS not in(2) ";
/*  8 */     upgradeRecordSet.executeQuery(str, new Object[0]);
/*  9 */     if (upgradeRecordSet.next()) {
/* 10 */       bool = false;
/*    */     }
/* 12 */     return bool;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/upgradetool/dbupgrade/upgrade/CheckPowersUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */