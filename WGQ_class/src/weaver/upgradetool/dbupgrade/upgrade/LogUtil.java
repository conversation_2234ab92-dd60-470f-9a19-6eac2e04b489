/*     */ package weaver.upgradetool.dbupgrade.upgrade;
/*     */ 
/*     */ import java.io.File;
/*     */ import java.io.IOException;
/*     */ import java.io.RandomAccessFile;
/*     */ import java.util.concurrent.ConcurrentHashMap;
/*     */ import org.apache.log4j.Logger;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class LogUtil
/*     */ {
/*  19 */   private static LogUtil logutil = null;
/*  20 */   private Logger log = Logger.getLogger(LogUtil.class);
/*  21 */   public ConcurrentHashMap<String, Long> sizemap = new ConcurrentHashMap<>();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static LogUtil getInstance() {
/*  28 */     if (null == logutil)
/*  29 */       logutil = new LogUtil(); 
/*  30 */     return logutil;
/*     */   }
/*     */   
/*     */   public void reset() {
/*  34 */     this.sizemap.clear();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public synchronized String getChangeLog(String paramString) {
/*  42 */     if ("1".equals(paramString)) {
/*  43 */       reset();
/*     */     }
/*  45 */     String str1 = TimeUtil.getCurrentDateString();
/*  46 */     File file = new File(ToolUtil.LOGFILE + "dbupgrade" + str1 + ".log");
/*  47 */     StringBuffer stringBuffer = getChangeLog(file);
/*  48 */     String str2 = Util.null2String(stringBuffer.toString());
/*     */     
/*  50 */     if (str2.length() > 50000) {
/*  51 */       str2 = str2.substring(str2.length() - 50000);
/*     */     }
/*  53 */     return str2;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public synchronized String getLog() {
/*  61 */     String str1 = TimeUtil.getCurrentDateString();
/*  62 */     File file = new File(ToolUtil.LOGFILE + "dbupgrade" + str1 + ".log");
/*  63 */     StringBuffer stringBuffer = getLog(file);
/*  64 */     String str2 = Util.null2String(stringBuffer.toString());
/*     */     
/*  66 */     if (str2.length() > 50000) {
/*  67 */       str2 = str2.substring(str2.length() - 50000);
/*     */     }
/*  69 */     return str2;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public synchronized StringBuffer getLog(File paramFile) {
/*  78 */     StringBuffer stringBuffer = new StringBuffer();
/*  79 */     char c = 'Ǵ';
/*  80 */     long l = 0L;
/*  81 */     if (!paramFile.exists() || paramFile.isDirectory() || !paramFile.canRead()) {
/*  82 */       return stringBuffer;
/*     */     }
/*  84 */     RandomAccessFile randomAccessFile = null;
/*     */     try {
/*  86 */       randomAccessFile = new RandomAccessFile(paramFile, "r");
/*  87 */       long l1 = randomAccessFile.length();
/*  88 */       if (l1 == 0L) {
/*  89 */         return stringBuffer;
/*     */       }
/*  91 */       long l2 = l1 - 1L;
/*  92 */       while (l2 > 0L) {
/*  93 */         l2--;
/*  94 */         randomAccessFile.seek(l2);
/*  95 */         if (randomAccessFile.readByte() == 10) {
/*  96 */           String str = new String(randomAccessFile.readLine().getBytes("ISO-8859-1"));
/*  97 */           stringBuffer.append(str);
/*  98 */           stringBuffer.append("\r\n");
/*  99 */           l++;
/* 100 */           if (l == c) {
/*     */             break;
/*     */           }
/*     */         }
/*     */       
/*     */       } 
/* 106 */     } catch (IOException iOException) {
/* 107 */       iOException.printStackTrace();
/*     */     } finally {
/* 109 */       if (randomAccessFile != null) {
/* 110 */         try { randomAccessFile.close(); } catch (Exception exception) {}
/*     */       }
/*     */     } 
/* 113 */     return stringBuffer;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public synchronized StringBuffer getChangeLog(File paramFile) {
/* 121 */     StringBuffer stringBuffer = new StringBuffer();
/* 122 */     RandomAccessFile randomAccessFile = null;
/*     */     try {
/* 124 */       String str1 = paramFile.getName();
/*     */       
/* 126 */       long l = 0L;
/* 127 */       if (this.sizemap.get(str1) != null && ((Long)this.sizemap.get(str1)).longValue() > 0L) {
/* 128 */         l = ((Long)this.sizemap.get(str1)).longValue();
/*     */       }
/* 130 */       randomAccessFile = new RandomAccessFile(paramFile.getAbsolutePath(), "rw");
/*     */       
/* 132 */       randomAccessFile.seek(l);
/* 133 */       String str2 = "";
/* 134 */       while ((str2 = randomAccessFile.readLine()) != null) {
/* 135 */         stringBuffer.append(new String(str2.getBytes("ISO-8859-1")));
/* 136 */         stringBuffer.append("\r\n");
/*     */       } 
/* 138 */       l = randomAccessFile.length();
/*     */       
/* 140 */       this.sizemap.put(str1, Long.valueOf(l));
/* 141 */     } catch (Exception exception) {
/* 142 */       exception.printStackTrace();
/*     */     } finally {
/*     */       try {
/* 145 */         if (randomAccessFile != null) {
/* 146 */           randomAccessFile.close();
/*     */         }
/* 148 */       } catch (IOException iOException) {}
/*     */     } 
/*     */     
/* 151 */     return stringBuffer;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/upgradetool/dbupgrade/upgrade/LogUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */