/*     */ package weaver.upgradetool.dbupgrade.upgrade;
/*     */ 
/*     */ import java.util.regex.Matcher;
/*     */ import java.util.regex.Pattern;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.upgradetool.dbupgrade.logger.DBUpgradeLogger;
/*     */ import weaver.upgradetool.dbupgrade.logger.UpgradeLog;
/*     */ 
/*     */ public class UpgradeRecordSet
/*     */ {
/*  11 */   RecordSet rs = null;
/*  12 */   UpgradeLog upgradeLog = null;
/*  13 */   Pattern pattern = null;
/*  14 */   Matcher matcher = null;
/*  15 */   DBUpgradeLogger dBUpgradeLogger = null;
/*     */   
/*     */   public UpgradeRecordSet() {
/*  18 */     this.rs = new RecordSet();
/*  19 */     this.dBUpgradeLogger = new DBUpgradeLogger();
/*  20 */     this.upgradeLog = new UpgradeLog();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean executeProc(String paramString1, String paramString2) {
/*  43 */     boolean bool = this.rs.executeProc(paramString1, paramString2);
/*  44 */     if (checkWriteLog(paramString1)) {
/*  45 */       DBUpgradeLogger.write2File(paramString1 + "$result:" + bool);
/*  46 */       this.upgradeLog = getUpgradeLogObj(paramString1);
/*  47 */       if (bool) {
/*  48 */         this.upgradeLog.setModifyStatus("1");
/*     */       } else {
/*  50 */         this.upgradeLog.setModifyStatus("0");
/*     */       } 
/*  52 */       DBUpgradeLogger.write2DB(this.upgradeLog);
/*     */     } 
/*  54 */     return bool;
/*     */   }
/*     */   
/*     */   public boolean executeProc(String paramString1, String paramString2, String paramString3) {
/*  58 */     boolean bool = this.rs.executeProc(paramString1, paramString2, paramString3);
/*  59 */     if (checkWriteLog(paramString1)) {
/*  60 */       DBUpgradeLogger.write2File(paramString1 + "$result:" + bool);
/*  61 */       this.upgradeLog = getUpgradeLogObj(paramString1);
/*  62 */       if (bool) {
/*  63 */         this.upgradeLog.setModifyStatus("1");
/*     */       } else {
/*  65 */         this.upgradeLog.setModifyStatus("0");
/*     */       } 
/*  67 */       DBUpgradeLogger.write2DB(this.upgradeLog);
/*     */     } 
/*  69 */     return bool;
/*     */   }
/*     */   
/*     */   public boolean executeSql(String paramString) {
/*  73 */     boolean bool = this.rs.executeSql(paramString);
/*  74 */     if (checkWriteLog(paramString)) {
/*  75 */       DBUpgradeLogger.write2File(paramString + "$result:" + bool);
/*  76 */       this.upgradeLog = getUpgradeLogObj(paramString);
/*  77 */       if (bool) {
/*  78 */         this.upgradeLog.setModifyStatus("1");
/*     */       } else {
/*  80 */         this.upgradeLog.setModifyStatus("0");
/*     */       } 
/*  82 */       DBUpgradeLogger.write2DB(this.upgradeLog);
/*     */     } 
/*  84 */     return bool;
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean executeQuery(String paramString1, String paramString2, Object... paramVarArgs) {
/*  89 */     return this.rs.executeQuery(paramString1, new Object[] { paramString2, paramVarArgs });
/*     */   }
/*     */   
/*     */   public boolean executeQuery(String paramString, Object... paramVarArgs) {
/*  93 */     return this.rs.executeQuery(paramString, paramVarArgs);
/*     */   }
/*     */   
/*     */   public boolean executeUpdate(String paramString, Object... paramVarArgs) {
/*  97 */     boolean bool = this.rs.executeUpdate(paramString, paramVarArgs);
/*  98 */     if (checkWriteLog(paramString)) {
/*  99 */       DBUpgradeLogger.write2File(paramString + formartParams(paramVarArgs) + "$result:" + bool);
/* 100 */       this.upgradeLog = getUpgradeLogObj(paramString + formartParams(paramVarArgs));
/* 101 */       if (bool) {
/* 102 */         this.upgradeLog.setModifyStatus("1");
/*     */       } else {
/* 104 */         this.upgradeLog.setModifyStatus("0");
/*     */       } 
/* 106 */       DBUpgradeLogger.write2DB(this.upgradeLog);
/*     */     } 
/* 108 */     return bool;
/*     */   }
/*     */   public boolean executeUpdate(String paramString1, String paramString2, Object... paramVarArgs) {
/* 111 */     boolean bool = this.rs.executeUpdate(paramString1, new Object[] { paramString2, paramVarArgs });
/* 112 */     if (checkWriteLog(paramString1)) {
/* 113 */       DBUpgradeLogger.write2File(paramString1 + formartParams(paramVarArgs) + "$result:" + bool);
/* 114 */       this.upgradeLog = getUpgradeLogObj(paramString1 + formartParams(paramVarArgs));
/* 115 */       if (bool) {
/* 116 */         this.upgradeLog.setModifyStatus("1");
/*     */       } else {
/* 118 */         this.upgradeLog.setModifyStatus("0");
/*     */       } 
/* 120 */       DBUpgradeLogger.write2DB(this.upgradeLog);
/*     */     } 
/* 122 */     return bool;
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean executeSql(String paramString1, boolean paramBoolean, String paramString2, Object... paramVarArgs) {
/* 127 */     boolean bool = this.rs.executeSql(paramString1, paramBoolean, paramString2, true, paramVarArgs);
/* 128 */     if (checkWriteLog(paramString1)) {
/* 129 */       DBUpgradeLogger.write2File(paramString1 + formartParams(paramVarArgs) + "$result:" + bool);
/* 130 */       this.upgradeLog = getUpgradeLogObj(paramString1);
/* 131 */       if (bool) {
/* 132 */         this.upgradeLog.setModifyStatus("1");
/*     */       } else {
/* 134 */         this.upgradeLog.setModifyStatus("0");
/*     */       } 
/* 136 */       DBUpgradeLogger.write2DB(this.upgradeLog);
/*     */     } 
/* 138 */     return bool;
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean executeSql(String paramString1, String paramString2) {
/* 143 */     boolean bool = this.rs.executeSql(paramString1, paramString2);
/* 144 */     if (checkWriteLog(paramString1)) {
/* 145 */       DBUpgradeLogger.write2File(paramString1 + "$result:" + bool);
/* 146 */       this.upgradeLog = getUpgradeLogObj(paramString1);
/* 147 */       if (bool) {
/* 148 */         this.upgradeLog.setModifyStatus("1");
/*     */       } else {
/* 150 */         this.upgradeLog.setModifyStatus("0");
/*     */       } 
/* 152 */       DBUpgradeLogger.write2DB(this.upgradeLog);
/*     */     } 
/* 154 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean execute(String paramString) {
/* 178 */     boolean bool = this.rs.execute(paramString);
/* 179 */     if (checkWriteLog(paramString)) {
/* 180 */       DBUpgradeLogger.write2File(paramString + "$result:" + bool);
/* 181 */       this.upgradeLog = getUpgradeLogObj(paramString);
/* 182 */       if (bool) {
/* 183 */         this.upgradeLog.setModifyStatus("1");
/*     */       } else {
/* 185 */         this.upgradeLog.setModifyStatus("0");
/*     */       } 
/* 187 */       DBUpgradeLogger.write2DB(this.upgradeLog);
/*     */     } 
/* 189 */     return bool;
/*     */   }
/*     */   
/*     */   public void setChecksql(boolean paramBoolean) {
/* 193 */     this.rs.setChecksql(paramBoolean);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public UpgradeLog getUpgradeLogObj(String paramString) {
/* 202 */     this.upgradeLog = this.dBUpgradeLogger.getUpgradeLogObj(paramString);
/* 203 */     return this.upgradeLog;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String matchReg(String paramString1, String paramString2) {
/* 212 */     String str = "";
/*     */     try {
/* 214 */       this.pattern = Pattern.compile(paramString2);
/* 215 */       this.matcher = this.pattern.matcher(paramString1);
/* 216 */       if (this.matcher.find()) {
/* 217 */         str = this.matcher.group(1);
/*     */       }
/* 219 */     } catch (Exception exception) {
/* 220 */       exception.printStackTrace();
/*     */     } 
/* 222 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean checkMatchReg(String paramString1, String paramString2) {
/* 232 */     boolean bool = false;
/*     */     try {
/* 234 */       this.pattern = Pattern.compile(paramString2);
/* 235 */       this.matcher = this.pattern.matcher(paramString1);
/* 236 */       if (this.matcher.find()) {
/* 237 */         bool = true;
/*     */       }
/* 239 */     } catch (Exception exception) {
/* 240 */       exception.printStackTrace();
/*     */     } 
/* 242 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String matchModifyFieldName(String paramString1, String paramString2) {
/* 251 */     String str = "";
/*     */     try {
/* 253 */       this.pattern = Pattern.compile(paramString2);
/* 254 */       this.matcher = this.pattern.matcher(paramString1);
/* 255 */       if (this.matcher.find()) {
/* 256 */         str = this.matcher.group(1);
/*     */       }
/* 258 */     } catch (Exception exception) {
/* 259 */       exception.printStackTrace();
/*     */     } 
/* 261 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void write2DB(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6, String paramString7, String paramString8) {
/* 275 */     this.upgradeLog.setModifyName(paramString1);
/* 276 */     this.upgradeLog.setModifyFieldName(paramString2);
/* 277 */     this.upgradeLog.setModifyType(paramString3);
/* 278 */     this.upgradeLog.setModifyContent(paramString4);
/* 279 */     this.upgradeLog.setModifyStatus(paramString5);
/* 280 */     this.upgradeLog.setType(paramString6);
/* 281 */     this.upgradeLog.setErrorlog(paramString7);
/* 282 */     this.upgradeLog.setModifyTime(paramString8);
/*     */     
/* 284 */     DBUpgradeLogger.write2DB(this.upgradeLog);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean checkWriteLog(String paramString) {
/* 291 */     boolean bool = true;
/* 292 */     if (paramString != null) {
/* 293 */       if (paramString.matches("(?i).*?create.*?") || paramString.matches("(?i).*?update.*?") || paramString.matches("(?i).*?delete.*?") || paramString.matches("(?i).*?insert.*?") || paramString.matches("(?i).*?drop.*?") || paramString.matches("(?i).*?alter.*?") || paramString.matches("(?i).*?truncate.*?") || paramString.matches("(?i).*?rename.*?")) {
/* 294 */         bool = true;
/*     */       } else {
/* 296 */         bool = false;
/*     */       } 
/*     */     } else {
/* 299 */       bool = false;
/*     */     } 
/* 301 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDBType() {
/* 310 */     return this.rs.getDBType(null);
/*     */   }
/*     */ 
/*     */   
/*     */   public String getDBType(String paramString) {
/* 315 */     return this.rs.getDBType(paramString);
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean next() {
/* 320 */     return this.rs.next();
/*     */   }
/*     */   public String getString(int paramInt) {
/* 323 */     return this.rs.getString(paramInt);
/*     */   }
/*     */   public String getString(String paramString) {
/* 326 */     return this.rs.getString(paramString);
/*     */   }
/*     */   
/*     */   public int getInt(int paramInt) {
/* 330 */     return this.rs.getInt(paramInt);
/*     */   }
/*     */   
/*     */   public static String repaceWhiteSapce(String paramString) {
/* 334 */     StringBuilder stringBuilder = new StringBuilder();
/* 335 */     boolean bool = false;
/* 336 */     paramString = paramString.trim();
/*     */     
/* 338 */     for (byte b = 0; b < paramString.length(); b++) {
/* 339 */       char c = paramString.charAt(b);
/* 340 */       if (c == ' ' || c == '\t') {
/*     */         
/* 342 */         if (!bool) {
/* 343 */           stringBuilder.append(c);
/* 344 */           bool = true;
/*     */         } 
/*     */       } else {
/* 347 */         stringBuilder.append(c);
/* 348 */         bool = false;
/*     */       } 
/*     */     } 
/* 351 */     return stringBuilder.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String formartParams(Object... paramVarArgs) {
/* 360 */     String str = "||";
/* 361 */     if (paramVarArgs != null) {
/* 362 */       for (byte b = 0; b < paramVarArgs.length; b++) {
/* 363 */         Object object = paramVarArgs[b];
/* 364 */         if (object != null) {
/* 365 */           str = str + object.toString();
/*     */         }
/*     */       } 
/*     */     }
/*     */     
/* 370 */     if (paramVarArgs == null || paramVarArgs.length == 0) {
/* 371 */       str = "";
/*     */     }
/*     */     
/* 374 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/upgradetool/dbupgrade/upgrade/UpgradeRecordSet.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */