/*     */ package weaver.upgradetool.dbupgrade.upgrade;
/*     */ 
/*     */ import java.io.File;
/*     */ import weaver.general.GCONST;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ToolUtil
/*     */ {
/*     */   public static final String EXECUTE_STATUS_INIT = "0";
/*     */   public static final String EXECUTE_STATUS_RUN = "1";
/*     */   public static final String EXECUTE_STATUS_FIN = "2";
/*     */   public static final String EXECUTE_STATUS_ERR = "3";
/*     */   public static final String USED_OFF = "0";
/*     */   public static final String USED_ON = "1";
/*     */   public static final int PROCESS_INIT = 0;
/*     */   public static final int PROCESS_RUN = 1;
/*     */   public static final int PROCESS_FIN = 2;
/*     */   public static final String ACTION_SUCESS = "success";
/*     */   public static final String ACTION_FAIL = "failure";
/*     */   public static final String ACTION_INIT = "init";
/*     */   public static final int DB_MODIFYTYPE_TABLE = 1;
/*     */   public static final int DB_MODIFYTYPE_FIELD = 2;
/*     */   public static final int DB_MODIFYTYPE_DATA = 3;
/*     */   public static final int DB_MODIFYTYPE_PROCESS = 4;
/*     */   public static final int DB_MODIFYTYPE_TRIGGER = 5;
/*     */   public static final int DB_MODIFYTYPE_FUNCTION = 6;
/*     */   public static final int DB_MODIFYTYPE_VIEW = 7;
/*     */   public static final int DB_MODIFYTYPE_SEQUENCE = 8;
/*     */   public static final int DB_MODIFYTYPE_INDEX = 9;
/*     */   public static final int DB_MODIFYTYPE_CONSTRAINT = 10;
/*     */   public static final int DB_DetailMODIFYTYPE_TABLE_DELETE = 10;
/*     */   public static final int DB_DetailMODIFYTYPE_TABLE_ADD = 11;
/*     */   public static final int DB_DetailMODIFYTYPE_FIELD_DELETE = 20;
/*     */   public static final int DB_DetailMODIFYTYPE_FIELD_ADD = 21;
/*     */   public static final int DB_DetailMODIFYTYPE_FIELD_TYPE = 22;
/*     */   public static final int DB_DetailMODIFYTYPE_DATA_DELETE = 30;
/*     */   public static final int DB_DetailMODIFYTYPE_DATA_ADD = 31;
/*     */   public static final int DB_DetailMODIFYTYPE_DATA_CLEAR = 32;
/*     */   public static final int DB_DetailMODIFYTYPE_DATA_UPDATE = 33;
/*     */   public static final int DB_DetailMODIFYTYPE_PROCESS_DELETE = 40;
/*     */   public static final int DB_DetailMODIFYTYPE_PROCESS_ADD = 41;
/*     */   public static final int DB_DetailMODIFYTYPE_PROCESS_UPDATE = 42;
/*     */   public static final int DB_DetailMODIFYTYPE_TRIGGER_DELETE = 50;
/*     */   public static final int DB_DetailMODIFYTYPE_TRIGGER_ADD = 51;
/*     */   public static final int DB_DetailMODIFYTYPE_TRIGGER_UPDATE = 52;
/*     */   public static final int DB_DetailMODIFYTYPE_METHOD_DELETE = 60;
/*     */   public static final int DB_DetailMODIFYTYPE_METHOD_ADD = 61;
/*     */   public static final int DB_DetailMODIFYTYPE_METHOD_UPDATE = 62;
/*     */   public static final int DB_DetailMODIFYTYPE_VIEW_DELETE = 70;
/*     */   public static final int DB_DetailMODIFYTYPE_VIEW_ADD = 71;
/*     */   public static final int DB_DetailMODIFYTYPE_VIEW_UPDATE = 72;
/*     */   public static final int DB_DetailMODIFYTYPE_SEQUENCE_DELETE = 80;
/*     */   public static final int DB_DetailMODIFYTYPE_SEQUENCE_ADD = 81;
/*     */   public static final int DB_DetailMODIFYTYPE_SEQUENCE_UPDATE = 82;
/*     */   public static final int DB_DetailMODIFYTYPE_INDEX_DELETE = 90;
/*     */   public static final int DB_DetailMODIFYTYPE_INDEX_ADD = 91;
/*     */   public static final int DB_DetailMODIFYTYPE_INDEX_UPDATE = 92;
/*     */   public static final int DB_DetailMODIFYTYPE_CONSTRAINT_DELETE = 100;
/*     */   public static final int DB_DetailMODIFYTYPE_CONSTRAINT_ADD = 101;
/*     */   public static final int DB_DetailMODIFYTYPE_CONSTRAINT_UPDATE = 102;
/*     */   public static final int EXECUTE_SQL_STATUS_INIT = 0;
/*     */   public static final int EXECUTE_SQL_STATUS_ERROR = 1;
/*     */   public static final int EXECUTE_SQL_STATUS_RUN = 2;
/*     */   public static final String EXECUTE_SQL_ERR = "0";
/*     */   public static final String EXECUTE_SQL_SUCCESS = "1";
/* 123 */   public static final String LOGFILE = GCONST.getRootPath() + "sysupgradelog" + File.separatorChar + "dbupgrade" + File.separatorChar;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String convert(int paramInt) {
/* 133 */     String[] arrayOfString1 = { "零", "一", "二", "三", "四", "五", "六", "七", "八", "九" };
/*     */     
/* 135 */     String[] arrayOfString2 = { "", "十", "百", "千", "万", "十", "百", "千", "亿", "十", "百", "千", "万亿" };
/*     */     
/* 137 */     String str1 = String.valueOf(paramInt);
/*     */     
/* 139 */     char[] arrayOfChar = str1.toCharArray();
/*     */     
/* 141 */     String str2 = "";
/* 142 */     int i = arrayOfChar.length;
/* 143 */     for (byte b = 0; b < i; b++) {
/* 144 */       int j = arrayOfChar[b] - 48;
/* 145 */       if (j != 0) {
/* 146 */         str2 = str2 + arrayOfString1[j] + arrayOfString2[i - b - 1];
/*     */       }
/* 148 */       else if (b != i - 1) {
/*     */         
/* 150 */         if (arrayOfChar[b + 1] != '0') {
/* 151 */           str2 = str2 + arrayOfString1[j];
/*     */         }
/*     */       } 
/*     */     } 
/*     */     
/* 156 */     return str2;
/*     */   }
/*     */ 
/*     */   
/*     */   public static String getRootPath() {
/* 161 */     return GCONST.getRootPath();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/upgradetool/dbupgrade/upgrade/ToolUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */