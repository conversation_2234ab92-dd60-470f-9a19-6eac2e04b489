/*     */ package weaver.upgradetool.dbupgrade.upgrade;
/*     */ import java.io.File;
/*     */ import java.io.FileOutputStream;
/*     */ import java.util.Hashtable;
/*     */ import java.util.Map;
/*     */ import org.apache.poi.hssf.usermodel.HSSFCell;
/*     */ import org.apache.poi.hssf.usermodel.HSSFCellStyle;
/*     */ import org.apache.poi.hssf.usermodel.HSSFDataFormat;
/*     */ import org.apache.poi.hssf.usermodel.HSSFFont;
/*     */ import org.apache.poi.hssf.usermodel.HSSFRow;
/*     */ import org.apache.poi.hssf.usermodel.HSSFSheet;
/*     */ import org.apache.poi.hssf.usermodel.HSSFWorkbook;
/*     */ import org.apache.poi.ss.usermodel.CellType;
/*     */ import weaver.file.ExcelFile;
/*     */ import weaver.file.ExcelRow;
/*     */ import weaver.file.ExcelSheet;
/*     */ import weaver.file.ExcelStyle;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.Util;
/*     */ import weaver.upgradetool.dbupgrade.actions.upgrade.ReportAction;
/*     */ import weaver.upgradetool.dbupgrade.logger.DBUpgradeLogger;
/*     */ 
/*     */ public class ReportToExcel {
/*  24 */   private static Hashtable styleht = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getModifyAll(Map paramMap, String paramString1, String paramString2) {
/*  34 */     String str1 = Util.null2String(paramMap.get("myname"));
/*  35 */     if (!"weaver@wcology_".equals(str1)) {
/*  36 */       return null;
/*     */     }
/*  38 */     if (paramString1 == null) {
/*  39 */       paramString1 = "";
/*     */     }
/*     */     
/*  42 */     ExcelFile excelFile = new ExcelFile();
/*  43 */     ExcelSheet excelSheet = new ExcelSheet();
/*  44 */     ExcelRow excelRow = excelSheet.newExcelRow();
/*  45 */     excelFile.init();
/*  46 */     excelSheet.addColumnwidth(2000);
/*  47 */     excelSheet.addColumnwidth(4000);
/*  48 */     excelSheet.addColumnwidth(4000);
/*  49 */     excelSheet.addColumnwidth(4000);
/*  50 */     excelSheet.addColumnwidth(20000);
/*  51 */     excelSheet.addColumnwidth(2000);
/*  52 */     excelSheet.addColumnwidth(4000);
/*     */     
/*  54 */     excelSheet.addColumnwidth(5000);
/*  55 */     ExcelStyle excelStyle1 = excelFile.newExcelStyle("TopInfo");
/*  56 */     excelStyle1.setGroundcolor(ExcelStyle.WeaverLightGroundcolor);
/*  57 */     excelStyle1.setFontcolor(ExcelStyle.WeaverHeaderFontcolor);
/*  58 */     excelStyle1.setFontbold(ExcelStyle.WeaverHeaderFontbold);
/*  59 */     excelStyle1.setAlign(ExcelStyle.ALIGN_LEFT);
/*     */     
/*  61 */     ExcelStyle excelStyle2 = excelFile.newExcelStyle("Header");
/*  62 */     excelStyle2.setGroundcolor(ExcelStyle.WeaverHeaderGroundcolor1);
/*  63 */     excelStyle2.setFontcolor(ExcelStyle.WeaverHeaderFontcolor);
/*  64 */     excelStyle2.setFontbold(ExcelStyle.WeaverHeaderFontbold);
/*  65 */     excelStyle2.setAlign(ExcelStyle.ALIGN_LEFT);
/*     */     
/*  67 */     ExcelStyle excelStyle3 = excelFile.newExcelStyle("dark");
/*  68 */     excelStyle3.setGroundcolor(ExcelStyle.WeaverDarkGroundcolor);
/*  69 */     excelStyle3.setFontcolor(ExcelStyle.WeaverHeaderFontcolor);
/*  70 */     excelStyle3.setAlign(ExcelStyle.ALIGN_LEFT);
/*     */     
/*  72 */     ExcelStyle excelStyle4 = excelFile.newExcelStyle("light");
/*  73 */     excelStyle4.setGroundcolor(ExcelStyle.WeaverLightGroundcolor);
/*  74 */     excelStyle4.setFontcolor(ExcelStyle.WeaverHeaderFontcolor);
/*  75 */     excelStyle4.setAlign(ExcelStyle.ALIGN_LEFT);
/*     */     
/*  77 */     String str2 = Util.null2String(paramMap.get("fileName"));
/*  78 */     if ("".equals(str2)) {
/*  79 */       str2 = "MigrationReport";
/*     */     }
/*  81 */     excelFile.setFilename(str2);
/*  82 */     excelFile.addSheet(str2, excelSheet);
/*  83 */     excelRow.addStringValue("ID", "Header");
/*  84 */     excelRow.addStringValue("执行类型", "Header");
/*  85 */     excelRow.addStringValue("修改对象", "Header");
/*  86 */     excelRow.addStringValue("相关字段", "Header");
/*  87 */     excelRow.addStringValue("修改内容", "Header");
/*  88 */     excelRow.addStringValue("修改结果", "Header");
/*  89 */     excelRow.addStringValue("修改类型", "Header");
/*     */     
/*  91 */     excelRow.addStringValue("修改时间", "Header");
/*     */     
/*  93 */     excelSheet.addExcelRow(excelRow);
/*  94 */     StringBuffer stringBuffer = new StringBuffer("select  *  from DBUpgradeLog Where 1 =1 ");
/*  95 */     String str3 = Util.null2String(paramMap.get("tableName"));
/*  96 */     String str4 = Util.null2String(paramMap.get("fieldName"));
/*  97 */     String str5 = Util.null2String(paramMap.get("modifys"));
/*     */     
/*  99 */     if (!"".equals(str3)) {
/* 100 */       stringBuffer.append(" AND UPPER(MODIFYNAME)  like UPPER('%" + str3 + "%') ");
/*     */     }
/* 102 */     if (!"".equals(str4)) {
/* 103 */       stringBuffer.append(" AND UPPER(MODIFYFIELDNAME) like UPPER('%" + str4 + "%') ");
/*     */     }
/* 105 */     if (!"".equals(str5)) {
/* 106 */       String[] arrayOfString = str5.split("-");
/* 107 */       str5 = "(";
/* 108 */       for (String str : arrayOfString) {
/* 109 */         str5 = str5 + str + ",";
/*     */       }
/* 111 */       str5 = str5.substring(0, str5.length() - 1) + ")";
/* 112 */       stringBuffer.append(" AND MODIFYSTATUS  IN " + str5);
/*     */     } 
/*     */     
/* 115 */     DBUpgradeLogger.write2File("开始生成excel，sql为" + stringBuffer);
/* 116 */     UpgradeRecordSet upgradeRecordSet = new UpgradeRecordSet();
/* 117 */     upgradeRecordSet.executeSql(stringBuffer.toString());
/*     */     
/* 119 */     ReportAction reportAction = new ReportAction();
/* 120 */     while (upgradeRecordSet.next()) {
/* 121 */       ExcelRow excelRow1 = excelSheet.newExcelRow();
/* 122 */       excelRow1.addStringValue(Util.null2String(upgradeRecordSet.getString("ID")), "Border");
/* 123 */       excelRow1.addStringValue(reportAction.replaceMODIFYTYPE(Util.null2String(upgradeRecordSet.getString("MODIFYTYPE"))), "Border");
/* 124 */       excelRow1.addStringValue(Util.null2String(upgradeRecordSet.getString("MODIFYNAME")), "Border");
/* 125 */       excelRow1.addStringValue(Util.null2String(upgradeRecordSet.getString("MODIFYFIELDNAME")), "Border");
/* 126 */       String str = Util.null2String(upgradeRecordSet.getString("MODIFYCONTENT"));
/* 127 */       if (str.length() > 30000) {
/* 128 */         str = "数据内容太长，请直接在数据库中查询：select * from DBUpgradeLog where id=" + upgradeRecordSet.getString("ID");
/*     */       }
/* 130 */       excelRow1.addStringValue(str, "Border");
/* 131 */       excelRow1.addStringValue(reportAction.replaceMODIFYSTATUS(Util.null2String(upgradeRecordSet.getString("MODIFYSTATUS"))), "Border");
/* 132 */       excelRow1.addStringValue(reportAction.replaceTYPE(Util.null2String(upgradeRecordSet.getString("TYPE"))), "Border");
/*     */       
/* 134 */       excelRow1.addStringValue(Util.null2String(upgradeRecordSet.getString("MODIFYTIME")), "Border");
/* 135 */       excelSheet.addExcelRow(excelRow1);
/*     */     } 
/*     */     try {
/* 138 */       return toFile(excelFile, str2, paramString2);
/*     */     }
/* 140 */     catch (Exception exception) {
/*     */       
/* 142 */       exception.printStackTrace();
/*     */       
/* 144 */       return "";
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String toFile(ExcelFile paramExcelFile, String paramString1, String paramString2) throws Exception {
/* 156 */     if (paramString2 == null || paramString2.equals("")) {
/* 157 */       paramString2 = GCONST.getRootPath();
/*     */     }
/* 159 */     else if (!paramString2.endsWith(File.separator)) {
/* 160 */       paramString2 = paramString2 + File.separator;
/*     */     } 
/*     */     
/* 163 */     ExcelSheet excelSheet = null;
/* 164 */     ExcelRow excelRow = null;
/*     */     
/* 166 */     HSSFWorkbook hSSFWorkbook = null;
/* 167 */     HSSFSheet hSSFSheet = null;
/* 168 */     HSSFRow hSSFRow = null;
/* 169 */     HSSFCell hSSFCell = null;
/* 170 */     HSSFCellStyle hSSFCellStyle = null;
/*     */     
/* 172 */     if (paramExcelFile == null) {
/* 173 */       return "";
/*     */     }
/* 175 */     hSSFWorkbook = new HSSFWorkbook();
/*     */     
/* 177 */     initStyle(paramExcelFile, hSSFWorkbook);
/* 178 */     byte b = 0;
/*     */     
/* 180 */     while (paramExcelFile.next()) {
/* 181 */       String str = paramExcelFile.getSheetname();
/* 182 */       excelSheet = paramExcelFile.getSheet();
/*     */       
/* 184 */       if (excelSheet == null)
/*     */         continue; 
/* 186 */       hSSFSheet = hSSFWorkbook.createSheet();
/* 187 */       hSSFWorkbook.setSheetName(b, Util.fromScreen2(str, 7));
/* 188 */       b++;
/*     */       try {
/* 190 */         HSSFCellStyle hSSFCellStyle1 = hSSFWorkbook.createCellStyle();
/* 191 */         HSSFDataFormat hSSFDataFormat = hSSFWorkbook.createDataFormat();
/* 192 */         for (byte b2 = 0; b2 < excelSheet.size(); b2++) {
/* 193 */           excelRow = excelSheet.getExcelRow(b2);
/* 194 */           if (excelRow != null) {
/*     */             
/* 196 */             short s = excelRow.getHight();
/* 197 */             hSSFRow = hSSFSheet.createRow((short)b2);
/* 198 */             if (s != 255)
/* 199 */               hSSFRow.setHeightInPoints(s); 
/* 200 */             byte b3 = 0;
/* 201 */             boolean bool1 = false;
/* 202 */             boolean bool2 = false;
/* 203 */             if (excelRow.stylesize() == excelRow.size())
/* 204 */               bool1 = true; 
/* 205 */             if (excelRow.spansize() == excelRow.size())
/* 206 */               bool2 = true; 
/* 207 */             for (byte b4 = 0; b4 < excelRow.size(); b4++)
/* 208 */             { hSSFCell = hSSFRow.createCell((short)b3);
/* 209 */               String str3 = Util.null2String(excelRow.getValue(b4));
/* 210 */               String str4 = str3.substring(0, 2);
/* 211 */               String str5 = str3.substring(2);
/*     */ 
/*     */ 
/*     */               
/* 215 */               if (str4.indexOf("s_") == 0) {
/*     */ 
/*     */                 
/* 218 */                 hSSFCellStyle1.setDataFormat(hSSFDataFormat.getFormat("@"));
/* 219 */                 hSSFCell.setCellStyle(hSSFCellStyle1);
/* 220 */                 hSSFCell.setCellType(CellType.STRING);
/* 221 */                 hSSFCell.setCellValue(Util.fromScreen4(str5, 7));
/* 222 */               } else if (str4.indexOf("i_") == 0) {
/* 223 */                 int i = Util.getIntValue(str5);
/* 224 */                 if (i != 0)
/* 225 */                   hSSFCell.setCellValue(i); 
/* 226 */               } else if (str4.indexOf("f_") == 0) {
/* 227 */                 float f = Util.getFloatValue(str5);
/* 228 */                 if (f != 0.0D)
/* 229 */                   hSSFCell.setCellValue(f); 
/* 230 */               } else if (str4.indexOf("d_") == 0) {
/* 231 */                 double d = Util.getDoubleValue(str5);
/* 232 */                 if (d != 0.0D)
/* 233 */                   hSSFCell.setCellValue(d); 
/* 234 */               } else if (str4.indexOf("o_") == 0) {
/* 235 */                 hSSFCell.setCellFormula(str5);
/* 236 */               } else if (str4.indexOf("n_") == 0) {
/*     */ 
/*     */ 
/*     */                 
/* 240 */                 hSSFCell.setCellValue(Util.fromScreen4(str5, 7));
/*     */               } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */               
/* 249 */               if (bool1) {
/* 250 */                 String str6 = Util.null2String(excelRow.getStyle(b4));
/* 251 */                 if (!str6.equals("")) {
/* 252 */                   hSSFCellStyle = getStyle(str6);
/* 253 */                   if (hSSFCellStyle != null) {
/* 254 */                     hSSFCell.setCellStyle(hSSFCellStyle);
/*     */                   }
/*     */                 } 
/*     */               } 
/* 258 */               if (bool2) {
/* 259 */                 int i = excelRow.getSpan(b4);
/* 260 */                 if (i > 1) {
/* 261 */                   for (byte b5 = 0; b5 < i - 1; b5++) {
/* 262 */                     b3++;
/* 263 */                     hSSFCell = hSSFRow.createCell((short)b3);
/* 264 */                     hSSFCell.setCellValue("");
/* 265 */                     if (bool1 && hSSFCellStyle != null) {
/* 266 */                       hSSFCell.setCellStyle(hSSFCellStyle);
/*     */                     }
/*     */                   } 
/*     */                 }
/*     */               } 
/*     */               
/* 272 */               b3++; } 
/*     */           } 
/*     */         } 
/* 275 */       } catch (Exception exception) {
/* 276 */         DBUpgradeLogger.write2File("excelerror" + exception.getMessage());
/*     */       } 
/* 278 */       for (byte b1 = 0; b1 < excelSheet.columnsize(); b1++) {
/* 279 */         hSSFSheet.setColumnWidth((short)b1, excelSheet.getColumnwidth(b1));
/*     */       }
/*     */     } 
/*     */     
/* 283 */     String str1 = paramExcelFile.getFilename();
/*     */     
/* 285 */     String str2 = paramString2 + paramString1 + ".xls";
/* 286 */     File file = new File(str2);
/* 287 */     if (file.exists()) {
/* 288 */       file.delete();
/*     */     }
/* 290 */     FileOutputStream fileOutputStream = new FileOutputStream(file);
/* 291 */     hSSFWorkbook.write(fileOutputStream);
/* 292 */     fileOutputStream.close();
/* 293 */     DBUpgradeLogger.write2File("Excel已生成");
/*     */     
/* 295 */     return str2;
/*     */   }
/*     */   
/*     */   private static void initStyle(ExcelFile paramExcelFile, HSSFWorkbook paramHSSFWorkbook) {
/* 299 */     styleht = new Hashtable<>();
/* 300 */     HSSFCellStyle hSSFCellStyle = null;
/* 301 */     HSSFFont hSSFFont = null;
/* 302 */     hSSFCellStyle = paramHSSFWorkbook.createCellStyle();
/* 303 */     hSSFFont = paramHSSFWorkbook.createFont();
/* 304 */     while (paramExcelFile.nextStyle()) {
/* 305 */       String str = paramExcelFile.getStyleName();
/* 306 */       ExcelStyle excelStyle = paramExcelFile.getStyleValue();
/*     */       
/* 308 */       if (excelStyle != null) {
/*     */ 
/*     */         
/* 311 */         if (excelStyle.getGroundcolor() != 9) {
/*     */           
/* 313 */           hSSFCellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
/* 314 */           hSSFCellStyle.setFillForegroundColor(excelStyle.getGroundcolor());
/*     */         } 
/* 316 */         hSSFCellStyle.setRotation(excelStyle.getScale());
/* 317 */         if (excelStyle.getAlign() != 10)
/*     */         {
/* 319 */           hSSFCellStyle.setAlignment(HorizontalAlignment.forInt(excelStyle.getAlign()));
/*     */         }
/* 321 */         if (excelStyle.getDataformart() != 0) {
/* 322 */           hSSFCellStyle.setDataFormat(excelStyle.getDataformart());
/*     */         }
/* 324 */         hSSFCellStyle.setVerticalAlignment(VerticalAlignment.forInt(excelStyle.getValign()));
/*     */         
/* 326 */         hSSFFont.setColor(excelStyle.getFontcolor());
/*     */         
/* 328 */         hSSFFont.setBold((excelStyle.getFontbold() == 700));
/* 329 */         hSSFFont.setFontHeightInPoints(excelStyle.getFontheight());
/*     */         
/* 331 */         hSSFCellStyle.setFont(hSSFFont);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 340 */         styleht.put(str, hSSFCellStyle);
/*     */       } 
/*     */     } 
/*     */   }
/*     */   
/*     */   private static HSSFCellStyle getStyle(String paramString) {
/* 346 */     return (HSSFCellStyle)styleht.get(paramString);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/upgradetool/dbupgrade/upgrade/ReportToExcel.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */