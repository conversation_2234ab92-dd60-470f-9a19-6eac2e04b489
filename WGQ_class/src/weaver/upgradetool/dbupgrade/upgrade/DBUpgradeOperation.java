/*     */ package weaver.upgradetool.dbupgrade.upgrade;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import java.io.File;
/*     */ import java.sql.Connection;
/*     */ import java.sql.PreparedStatement;
/*     */ import java.sql.ResultSet;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.conn.RecordSetTrans;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.Util;
/*     */ import weaver.upgradetool.dbupgrade.actions.ActionProcess;
/*     */ import weaver.upgradetool.dbupgrade.invoke.DBUpgradeInvokeUtil;
/*     */ import weaver.upgradetool.dbupgrade.logger.DBUpgradeLogger;
/*     */ 
/*     */ public class DBUpgradeOperation
/*     */ {
/*  20 */   public static Long threadId = Long.valueOf(0L);
/*  21 */   DBUpgradeProcess dbUpgradeProcess = new DBUpgradeProcess();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONObject executeTask(String paramString1, String paramString2) {
/*  30 */     RecordSet recordSet = new RecordSet();
/*  31 */     JSONObject jSONObject = new JSONObject();
/*  32 */     boolean bool = true;
/*     */     
/*  34 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  35 */     hashMap.put("mainsequence", paramString1);
/*  36 */     hashMap.put("detailsequence", paramString2);
/*     */     
/*  38 */     recordSet.executeUpdate("update DBUpgradeDetail set status = ? where  mainsequence = ? and sequence = ?", new Object[] { "1", paramString1, paramString2 });
/*     */     
/*  40 */     recordSet.executeQuery("select * from DBUpgradeAction where  mainsequence = ? and detailsequence= ? and used = '1' and status!='1' and status!='2' order by sequence", new Object[] { paramString1, paramString2 });
/*  41 */     while (recordSet.next()) {
/*  42 */       String str1 = recordSet.getString("sequence");
/*  43 */       String str2 = recordSet.getString("action");
/*  44 */       hashMap.put("actionsequence", str1);
/*     */       
/*  46 */       JSONObject jSONObject1 = executeAction(str2, null, (HashMap)hashMap);
/*  47 */       String str3 = (String)jSONObject1.get("status");
/*     */ 
/*     */       
/*  50 */       if (!"success".equals(str3)) {
/*  51 */         bool = false;
/*     */ 
/*     */         
/*     */         break;
/*     */       } 
/*     */     } 
/*     */ 
/*     */     
/*  59 */     if (bool) {
/*  60 */       jSONObject.put("task_status", "success");
/*     */     } else {
/*  62 */       jSONObject.put("task_status", "failure");
/*     */     } 
/*     */     
/*  65 */     this.dbUpgradeProcess.updateDetailStatusByAction(paramString1, paramString2);
/*     */     
/*  67 */     this.dbUpgradeProcess.updateMainStatusByDetail(paramString1);
/*     */     
/*  69 */     return jSONObject;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONObject executeAction(String paramString1, String paramString2, HashMap<String, String> paramHashMap) {
/*  76 */     paramString1 = "weaver.upgradetool." + paramString1;
/*     */     
/*  78 */     ActionProcess.getInstance().initProcess();
/*  79 */     this.dbUpgradeProcess.updateActionStatus(paramHashMap.get("mainsequence"), paramHashMap.get("detailsequence"), paramHashMap.get("actionsequence"), "1");
/*  80 */     String str1 = (String)DBUpgradeInvokeUtil.invoke(paramString1, paramString2, paramHashMap);
/*  81 */     JSONObject jSONObject = JSONObject.parseObject(str1);
/*  82 */     String str2 = (String)jSONObject.get("status");
/*  83 */     if ("success".equals(str2)) {
/*     */       
/*  85 */       this.dbUpgradeProcess.updateActionStatus(paramHashMap.get("mainsequence"), paramHashMap.get("detailsequence"), paramHashMap.get("actionsequence"), "2");
/*     */     } else {
/*     */       
/*  88 */       this.dbUpgradeProcess.updateActionStatus(paramHashMap.get("mainsequence"), paramHashMap.get("detailsequence"), paramHashMap.get("actionsequence"), "3");
/*     */     } 
/*     */     
/*  91 */     ActionProcess.getInstance().initProcess();
/*     */     
/*  93 */     return jSONObject;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONObject executeTask(String paramString, boolean paramBoolean) {
/* 102 */     RecordSet recordSet1 = new RecordSet();
/* 103 */     RecordSet recordSet2 = new RecordSet();
/* 104 */     RecordSet recordSet3 = new RecordSet();
/* 105 */     JSONObject jSONObject = new JSONObject();
/* 106 */     boolean bool = true;
/* 107 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/* 109 */     DBUpgradeProcess.setStatus(1);
/*     */ 
/*     */     
/* 112 */     recordSet1.executeQuery("select * from  DBUpgradeDetail where mainsequence= ? and status != ? and used = ? order by sequence", new Object[] { paramString, "2", "1" });
/* 113 */     if (recordSet1.next()) {
/* 114 */       String str = recordSet1.getString("sequence");
/*     */       
/* 116 */       recordSet1.executeUpdate("update DBUpgradeDetail set status = ? where  mainsequence = ? and sequence = ? and used = ?", new Object[] { "1", paramString, str, "1" });
/*     */     } 
/*     */ 
/*     */     
/* 120 */     recordSet1.executeQuery("select * from DBUpgradeDetail where  mainsequence = ? and status != ? order by sequence", new Object[] { paramString, "2" });
/* 121 */     while (recordSet1.next()) {
/* 122 */       String str = recordSet1.getString("sequence");
/* 123 */       this.dbUpgradeProcess.updateDetailStatus(paramString, str, "1");
/*     */       
/* 125 */       recordSet3.executeQuery("select 1 from DBUpgradeAction  where  mainsequence = ? and detailsequence = ? and status!='1' and status!='2' and used = ?", new Object[] { paramString, str, "1" });
/* 126 */       if (recordSet3.next()) {
/* 127 */         recordSet2.executeQuery("select * from DBUpgradeAction where  mainsequence = ? and detailsequence = ? and used = ? and status!='1' and status!='2' order by detailsequence,sequence", new Object[] { paramString, str, "1" });
/* 128 */         while (recordSet2.next()) {
/* 129 */           hashMap.put("mainsequence", paramString);
/* 130 */           String str1 = recordSet2.getString("action");
/* 131 */           String str2 = recordSet2.getString("sequence");
/* 132 */           String str3 = recordSet2.getString("detailsequence");
/* 133 */           hashMap.put("detailsequence", str3);
/* 134 */           hashMap.put("actionsequence", str2);
/*     */           
/* 136 */           if (str1 != null || "".equals(str1)) {
/* 137 */             DBUpgradeLogger.write2File("执行action:" + str1);
/* 138 */             jSONObject = executeAction(str1, null, (HashMap)hashMap);
/* 139 */             String str4 = (String)jSONObject.get("status");
/* 140 */             this.dbUpgradeProcess.updateDetailStatusByAction(paramString, str3);
/*     */             
/* 142 */             if (!"success".equals(str4)) {
/* 143 */               bool = false;
/*     */ 
/*     */               
/*     */               break;
/*     */             } 
/*     */           } 
/*     */         } 
/*     */       } else {
/* 151 */         this.dbUpgradeProcess.updateDetailStatus(paramString, str, "2");
/*     */       } 
/*     */ 
/*     */       
/* 155 */       if (!bool) {
/*     */         break;
/*     */       }
/*     */     } 
/*     */ 
/*     */     
/* 161 */     if (bool) {
/* 162 */       jSONObject.put("task_status", "success");
/*     */     } else {
/* 164 */       jSONObject.put("task_status", "failure");
/*     */     } 
/*     */ 
/*     */     
/* 168 */     if (paramBoolean) {
/* 169 */       this.dbUpgradeProcess.updateMainStatusByDetail(paramString);
/*     */     }
/*     */     
/* 172 */     return jSONObject;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONObject executeTask(String paramString) {
/* 181 */     RecordSet recordSet1 = new RecordSet();
/* 182 */     RecordSet recordSet2 = new RecordSet();
/* 183 */     RecordSet recordSet3 = new RecordSet();
/* 184 */     JSONObject jSONObject = new JSONObject();
/* 185 */     boolean bool = true;
/* 186 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */     
/*     */     try {
/* 189 */       DBUpgradeProcess.setStatus(1);
/*     */ 
/*     */       
/* 192 */       recordSet1.executeQuery("select * from  DBUpgradeDetail where mainsequence= ? and status != ? and used = ? order by sequence", new Object[] { paramString, "2", "1" });
/* 193 */       if (recordSet1.next()) {
/* 194 */         String str = recordSet1.getString("sequence");
/*     */         
/* 196 */         recordSet1.executeUpdate("update DBUpgradeDetail set status = ? where  mainsequence = ? and sequence = ? and used = ?", new Object[] { "1", paramString, str, "1" });
/*     */       } 
/*     */ 
/*     */       
/* 200 */       recordSet1.executeQuery("select * from DBUpgradeDetail where  mainsequence = ? and status != ? order by sequence", new Object[] { paramString, "2" });
/* 201 */       while (recordSet1.next()) {
/* 202 */         String str = recordSet1.getString("sequence");
/* 203 */         this.dbUpgradeProcess.updateDetailStatus(paramString, str, "1");
/*     */         
/* 205 */         recordSet3.executeQuery("select 1 from DBUpgradeAction  where  mainsequence = ? and detailsequence = ? and status!='1' and status!='2' and used = ?", new Object[] { paramString, str, "1" });
/* 206 */         if (recordSet3.next()) {
/* 207 */           recordSet2.executeQuery("select * from DBUpgradeAction where  mainsequence = ? and detailsequence = ? and used = ? and status!='1' and status!='2' order by detailsequence,sequence", new Object[] { paramString, str, "1" });
/* 208 */           while (recordSet2.next()) {
/* 209 */             hashMap.put("mainsequence", paramString);
/* 210 */             String str1 = recordSet2.getString("action");
/* 211 */             String str2 = recordSet2.getString("sequence");
/* 212 */             String str3 = recordSet2.getString("detailsequence");
/* 213 */             hashMap.put("detailsequence", str3);
/* 214 */             hashMap.put("actionsequence", str2);
/*     */             
/* 216 */             if (str1 != null || "".equals(str1)) {
/* 217 */               DBUpgradeLogger.write2File("执行action:" + str1);
/* 218 */               jSONObject = executeAction(str1, null, (HashMap)hashMap);
/* 219 */               String str4 = (String)jSONObject.get("status");
/* 220 */               this.dbUpgradeProcess.updateDetailStatusByAction(paramString, str3);
/*     */               
/* 222 */               if (!"success".equals(str4)) {
/* 223 */                 bool = false;
/*     */ 
/*     */                 
/*     */                 break;
/*     */               } 
/*     */             } 
/*     */           } 
/*     */         } else {
/* 231 */           this.dbUpgradeProcess.updateDetailStatus(paramString, str, "2");
/*     */         } 
/*     */ 
/*     */         
/* 235 */         if (!bool) {
/*     */           break;
/*     */         }
/*     */       } 
/*     */ 
/*     */       
/* 241 */       if (bool) {
/* 242 */         jSONObject.put("task_status", "success");
/*     */       } else {
/* 244 */         jSONObject.put("task_status", "failure");
/*     */       } 
/*     */ 
/*     */       
/* 248 */       this.dbUpgradeProcess.updateMainStatusByDetail(paramString);
/* 249 */     } catch (Exception exception) {
/* 250 */       DBUpgradeLogger.write2File("DBUpgradeOperation.java executeTask方法执行报错,报错信息:" + exception.toString() + "====" + exception.getStackTrace());
/* 251 */       exception.printStackTrace();
/*     */     } 
/* 253 */     return jSONObject;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void nextTask(String paramString1, String paramString2) {
/* 262 */     this.dbUpgradeProcess.updateDetailStatusByAction(paramString1, paramString2);
/*     */     
/* 264 */     this.dbUpgradeProcess.updateMainStatusByDetail(paramString1);
/*     */     
/* 266 */     this.dbUpgradeProcess.updateNextMainStep(paramString1);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean getClosedTrigger_name() {
/* 274 */     RecordSet recordSet = new RecordSet();
/*     */     try {
/* 276 */       if (recordSet.getDBType().equalsIgnoreCase("ORACLE")) {
/* 277 */         String str1 = GCONST.getRootPath() + "system" + File.separatorChar + "upgradetoe9" + File.separatorChar + "resource" + File.separatorChar + "migration.properties";
/* 278 */         PropUtil propUtil = PropUtil.getInstance(new String[] { str1 });
/* 279 */         String str2 = Util.null2String(propUtil.getValues("e9_closedtriggername"));
/* 280 */         if ("".equalsIgnoreCase(str2)) {
/* 281 */           recordSet.executeQuery("select TRIGGER_NAME from USER_TRIGGERS where status='DISABLED'", new Object[0]);
/* 282 */           StringBuffer stringBuffer = new StringBuffer();
/* 283 */           while (recordSet.next()) {
/* 284 */             String str = Util.null2String(recordSet.getString("TRIGGER_NAME"));
/* 285 */             if ("".equalsIgnoreCase(str)) {
/* 286 */               if (stringBuffer.length() > 0) {
/* 287 */                 stringBuffer.append("," + str); continue;
/*     */               } 
/* 289 */               stringBuffer.append(str);
/*     */             } 
/*     */           } 
/*     */           
/* 293 */           propUtil.put("e9_closedtriggername", stringBuffer.toString());
/* 294 */           propUtil.store();
/*     */         } 
/*     */       } 
/* 297 */     } catch (Exception exception) {
/* 298 */       DBUpgradeLogger.write2File(exception.getMessage());
/* 299 */       return false;
/*     */     } 
/* 301 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean closeDBTrigger() {
/* 307 */     boolean bool = true;
/* 308 */     DBUpgradeLogger.write2File("开始关闭触发器");
/* 309 */     RecordSet recordSet = new RecordSet();
/* 310 */     RecordSetTrans recordSetTrans = new RecordSetTrans();
/* 311 */     String str = recordSet.getDBType();
/* 312 */     recordSetTrans.setAutoCommit(false);
/*     */     try {
/* 314 */       if ("oracle".equalsIgnoreCase(str) && !recordSet.getOrgindbtype().equals("jc")) {
/* 315 */         getClosedTrigger_name();
/* 316 */         recordSet.executeUpdate("BEGIN   FOR i in (SELECT 'ALTER TRIGGER ' || TRIGGER_NAME || ' DISABLE' strtri        FROM USER_TRIGGERS) LOOP     BEGIN     EXECUTE IMMEDIATE i.strtri;      EXCEPTION        WHEN OTHERS THEN         NULL;     END;   END LOOP; END; ", new Object[0]);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/*     */       }
/* 327 */       else if ("mysql".equalsIgnoreCase(str)) {
/*     */       
/*     */       } 
/*     */ 
/*     */       
/* 332 */       recordSetTrans.commit();
/* 333 */     } catch (Exception exception) {
/* 334 */       bool = false;
/* 335 */       DBUpgradeLogger.write2File(exception.getMessage());
/* 336 */       exception.printStackTrace();
/*     */     } finally {
/* 338 */       recordSetTrans.setAutoCommit(true);
/*     */     } 
/* 340 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void openDBTrigger() {
/* 347 */     DBUpgradeLogger.write2File("开始打开触发器189");
/* 348 */     RecordSet recordSet = new RecordSet();
/* 349 */     RecordSetTrans recordSetTrans = new RecordSetTrans();
/* 350 */     String str = recordSet.getDBType();
/* 351 */     recordSetTrans.setAutoCommit(false);
/*     */     try {
/* 353 */       if ("oracle".equalsIgnoreCase(str) && !recordSet.getOrgindbtype().equals("jc")) {
/* 354 */         DBUpgradeLogger.write2File("开始打开触发器236");
/* 355 */         recordSet.executeUpdate("BEGIN   FOR i in (SELECT 'ALTER TRIGGER ' || TRIGGER_NAME || ' ENABLE' strtri               FROM USER_TRIGGERS WHERE STATUS='DISABLED') LOOP     EXECUTE IMMEDIATE i.strtri;   END LOOP;  END; ", new Object[0]);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 361 */         closeTriggerBynames();
/* 362 */       } else if ("mysql".equalsIgnoreCase(str)) {
/*     */       
/*     */       } 
/*     */ 
/*     */       
/* 367 */       recordSetTrans.commit();
/* 368 */     } catch (Exception exception) {
/* 369 */       DBUpgradeLogger.write2File(exception.getMessage());
/* 370 */       exception.printStackTrace();
/*     */     } finally {
/* 372 */       recordSetTrans.setAutoCommit(true);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void closeTriggerBynames() {
/* 381 */     RecordSet recordSet = new RecordSet();
/* 382 */     if (recordSet.getDBType().equalsIgnoreCase("ORACLE") && !recordSet.getOrgindbtype().equalsIgnoreCase("jc")) {
/* 383 */       String str1 = GCONST.getRootPath() + "system" + File.separatorChar + "upgradetoe9" + File.separatorChar + "resource" + File.separatorChar + "migration.properties";
/* 384 */       PropUtil propUtil = PropUtil.getInstance(new String[] { str1 });
/* 385 */       String str2 = Util.null2String(propUtil.getValues("e9_closedtriggername"));
/* 386 */       String[] arrayOfString = str2.split(",");
/* 387 */       for (byte b = 0; b < arrayOfString.length; b++) {
/* 388 */         if (!"".equalsIgnoreCase(arrayOfString[b])) {
/* 389 */           recordSet.executeUpdate("alter trigger " + arrayOfString[b] + "disable", new Object[0]);
/*     */         }
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, String> getNewDbInfo() {
/* 400 */     RecordSet recordSet = new RecordSet();
/* 401 */     String str = recordSet.getDBType();
/* 402 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 403 */     if ("oracle".equalsIgnoreCase(str)) {
/* 404 */       String str1 = GCONST.getRootPath() + "system" + File.separatorChar + "upgradetoe9" + File.separatorChar + "resource" + File.separatorChar + "migration.properties";
/* 405 */       PropUtil propUtil = PropUtil.getInstance(new String[] { str1 });
/* 406 */       String str2 = Util.null2String(propUtil.getValues("synctables"));
/* 407 */       String[] arrayOfString = str2.split(",");
/* 408 */       for (byte b = 0; b < arrayOfString.length; b++) {
/*     */         try {
/* 410 */           recordSet.executeQuery("select count(*) as row_num from " + arrayOfString[b], new Object[0]);
/* 411 */           if (recordSet.next()) {
/* 412 */             hashMap.put(arrayOfString[b], Util.null2String(recordSet.getString("row_num"), "0"));
/*     */           } else {
/* 414 */             hashMap.put(arrayOfString[b], "0");
/*     */           } 
/* 416 */         } catch (Exception exception) {}
/*     */ 
/*     */ 
/*     */ 
/*     */       
/*     */       }
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     }
/* 427 */     else if (!"mysql".equalsIgnoreCase(str)) {
/*     */       
/* 429 */       if (!"dm".equalsIgnoreCase(str))
/*     */       {
/*     */         
/* 432 */         if (!"postgresql".equalsIgnoreCase(str))
/*     */         {
/*     */           
/* 435 */           if ("sqlserver".equalsIgnoreCase(str)) {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */             
/* 447 */             String str1 = GCONST.getRootPath() + "system" + File.separatorChar + "upgradetoe9" + File.separatorChar + "resource" + File.separatorChar + "migration.properties";
/* 448 */             PropUtil propUtil = PropUtil.getInstance(new String[] { str1 });
/* 449 */             String str2 = Util.null2String(propUtil.getValues("synctables"));
/* 450 */             String[] arrayOfString = str2.split(",");
/* 451 */             for (byte b = 0; b < arrayOfString.length; b++) {
/*     */               try {
/* 453 */                 recordSet.executeQuery("select count(*) as row_num from " + arrayOfString[b], new Object[0]);
/* 454 */                 if (recordSet.next()) {
/* 455 */                   hashMap.put(arrayOfString[b], Util.null2String(recordSet.getString("row_num"), "0"));
/*     */                 } else {
/* 457 */                   hashMap.put(arrayOfString[b], "0");
/*     */                 } 
/* 459 */               } catch (Exception exception) {}
/*     */             } 
/*     */           }  } 
/*     */       }
/*     */     } 
/* 464 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, String> getOldDBinfo() {
/* 473 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 474 */     Connection connection = null;
/* 475 */     PreparedStatement preparedStatement = null;
/* 476 */     String str = "";
/*     */     try {
/* 478 */       DBUtil dBUtil = new DBUtil();
/* 479 */       connection = dBUtil.getSourceConnection();
/* 480 */       String str1 = dBUtil.getDBtype();
/* 481 */       if ("oracle".equalsIgnoreCase(str1)) {
/* 482 */         String str2 = GCONST.getRootPath() + "system" + File.separatorChar + "upgradetoe9" + File.separatorChar + "resource" + File.separatorChar + "migration.properties";
/* 483 */         PropUtil propUtil = PropUtil.getInstance(new String[] { str2 });
/* 484 */         String str3 = Util.null2String(propUtil.getValues("synctables"));
/* 485 */         String[] arrayOfString = str3.split(",");
/* 486 */         for (byte b = 0; b < arrayOfString.length; b++) {
/*     */           try {
/* 488 */             preparedStatement = connection.prepareStatement("select count(*) as row_num from " + arrayOfString[b]);
/* 489 */             ResultSet resultSet = preparedStatement.executeQuery();
/* 490 */             if (resultSet.next()) {
/* 491 */               hashMap.put(arrayOfString[b], Util.null2String(resultSet.getString("row_num"), "0"));
/*     */             } else {
/* 493 */               hashMap.put(arrayOfString[b], "0");
/*     */             } 
/* 495 */           } catch (Exception exception) {}
/*     */         } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 507 */         preparedStatement.close();
/* 508 */       } else if (!"mysql".equalsIgnoreCase(str1)) {
/*     */         
/* 510 */         if (!"dm".equalsIgnoreCase(str1))
/*     */         {
/*     */           
/* 513 */           if (!"postgresql".equalsIgnoreCase(str1))
/*     */           {
/*     */             
/* 516 */             if ("sqlserver".equalsIgnoreCase(str1)) {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */               
/* 530 */               String str2 = GCONST.getRootPath() + "system" + File.separatorChar + "upgradetoe9" + File.separatorChar + "resource" + File.separatorChar + "migration.properties";
/* 531 */               PropUtil propUtil = PropUtil.getInstance(new String[] { str2 });
/* 532 */               String str3 = Util.null2String(propUtil.getValues("synctables"));
/* 533 */               String[] arrayOfString = str3.split(",");
/* 534 */               for (byte b = 0; b < arrayOfString.length; b++) {
/*     */                 try {
/* 536 */                   preparedStatement = connection.prepareStatement("select count(*) as row_num from " + arrayOfString[b]);
/* 537 */                   ResultSet resultSet = preparedStatement.executeQuery();
/* 538 */                   if (resultSet.next()) {
/* 539 */                     hashMap.put(arrayOfString[b], Util.null2String(resultSet.getString("row_num"), "0"));
/*     */                   } else {
/* 541 */                     hashMap.put(arrayOfString[b], "0");
/*     */                   } 
/* 543 */                 } catch (Exception exception) {}
/*     */               } 
/*     */ 
/*     */               
/* 547 */               preparedStatement.close();
/*     */             }  }  } 
/*     */       } 
/* 550 */     } catch (Exception exception) {
/* 551 */       DBUpgradeLogger.write2File("加载数据库驱动出现问题：" + exception);
/*     */     } finally {
/*     */       try {
/* 554 */         if (preparedStatement != null && !preparedStatement.isClosed()) {
/* 555 */           preparedStatement.close();
/*     */         }
/* 557 */         if (connection != null && !connection.isClosed()) {
/* 558 */           connection.close();
/*     */         }
/* 560 */       } catch (Exception exception) {
/* 561 */         DBUpgradeLogger.write2File(exception.getMessage());
/*     */       } 
/*     */     } 
/* 564 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONObject getLogInfo(String paramString) {
/* 571 */     JSONObject jSONObject = new JSONObject();
/* 572 */     String str = LogUtil.getInstance().getChangeLog(paramString).replaceAll("\t", " ");
/* 573 */     jSONObject.put("loginfo", str);
/* 574 */     return jSONObject;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void executeDataInit() {
/* 581 */     RecordSet recordSet = new RecordSet();
/* 582 */     String str = " update DBUpgradeDetail set STATUS = ? ";
/* 583 */     recordSet.executeUpdate(str, new Object[] { "0" });
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void shieldTool() {
/* 591 */     File file1 = new File(GCONST.getRootPath() + "system" + File.separatorChar + "upgradetoe9" + File.separatorChar + "DBUpgradeInit.jsp");
/* 592 */     File file2 = new File(GCONST.getRootPath() + "system" + File.separatorChar + "upgradetoe9" + File.separatorChar + "DBUpgradeInitCompleted.jsp");
/* 593 */     if (file1.exists() && file2.exists()) {
/* 594 */       file1.delete();
/* 595 */       file2.renameTo(file1);
/*     */     } 
/*     */     
/* 598 */     File file3 = new File(GCONST.getRootPath() + "system" + File.separatorChar + "upgradetoe9" + File.separatorChar + "resource" + File.separatorChar + "operationSwitch.properties");
/* 599 */     if (file3.exists()) {
/* 600 */       file3.delete();
/*     */     }
/*     */ 
/*     */ 
/*     */     
/* 605 */     String str = GCONST.getRootPath() + "system" + File.separatorChar + "upgradetoe9" + File.separatorChar + "resource" + File.separatorChar + "weaver_source.properties";
/* 606 */     PropUtil propUtil = PropUtil.getInstance(new String[] { str });
/* 607 */     PropUtil.editProp("", "", "", "", "", "", "", "");
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/upgradetool/dbupgrade/upgrade/DBUpgradeOperation.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */