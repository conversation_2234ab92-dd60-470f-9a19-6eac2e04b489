/*     */ package weaver.upgradetool.dbupgrade.upgrade;
/*     */ import java.io.BufferedWriter;
/*     */ import java.io.File;
/*     */ import java.sql.Connection;
/*     */ import java.sql.DriverManager;
/*     */ import java.sql.ResultSet;
/*     */ import java.sql.SQLException;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.upgradetool.dbupgrade.logger.DBUpgradeLogger;
/*     */ 
/*     */ public class DBUtil {
/*  14 */   private PropUtil prop = PropUtil.getInstance(new String[0]);
/*     */ 
/*     */ 
/*     */   
/*     */   private static String beforeLicense;
/*     */ 
/*     */ 
/*     */   
/*     */   public Connection getBeTransConnectionOld(HttpServletRequest paramHttpServletRequest) {
/*  23 */     Connection connection = null;
/*     */     try {
/*  25 */       String str1 = Util.null2String(paramHttpServletRequest.getParameter("dbserver"));
/*  26 */       String str2 = Util.null2String(paramHttpServletRequest.getParameter("dbport"));
/*  27 */       String str3 = Util.null2String(paramHttpServletRequest.getParameter("dbname"));
/*  28 */       String str4 = Util.null2String(paramHttpServletRequest.getParameter("username"));
/*  29 */       String str5 = Util.null2String(paramHttpServletRequest.getParameter("password"));
/*  30 */       String str6 = Util.null2String(paramHttpServletRequest.getParameter("dbtype"));
/*  31 */       if (str6.equalsIgnoreCase("sqlserver")) {
/*  32 */         Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
/*  33 */         connection = DriverManager.getConnection("jdbc:sqlserver://" + str1 + ":" + str2 + ";databaseName=" + str3, str4, str5);
/*  34 */       } else if (str6.equalsIgnoreCase("oracle")) {
/*  35 */         Class.forName("oracle.jdbc.OracleDriver");
/*  36 */         connection = DriverManager.getConnection("jdbc:oracle:thin:@" + str1 + ":" + str2 + ":" + str3, str4, str5);
/*     */       } 
/*  38 */     } catch (Exception exception) {
/*  39 */       BaseBean baseBean = new BaseBean();
/*  40 */       baseBean.writeLog(exception.getMessage());
/*  41 */       connection = null;
/*     */     } 
/*  43 */     return connection;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Connection getSourceConnection() {
/*  53 */     Object object = null;
/*  54 */     Connection connection = null;
/*     */     
/*  56 */     String str1 = Util.null2String(this.prop.getValues("dbserver"));
/*  57 */     String str2 = Util.null2String(this.prop.getValues("dbport"));
/*  58 */     String str3 = Util.null2String(this.prop.getValues("dbname"));
/*  59 */     String str4 = Util.null2String(this.prop.getValues("username"));
/*  60 */     String str5 = Util.null2String(this.prop.getValues("password"));
/*  61 */     String str6 = Util.null2String(this.prop.getValues("dbtype"));
/*  62 */     String str7 = "";
/*  63 */     String str8 = "";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  72 */     if (str6.equalsIgnoreCase("sqlserver2000")) {
/*  73 */       if (str2 == null)
/*  74 */         str2 = "1433"; 
/*  75 */       str7 = "jdbc:jtds:sqlserver://" + str1 + ":" + str2 + ";DatabaseName=" + str3;
/*  76 */       str8 = "net.sourceforge.jtds.jdbc.Driver";
/*  77 */     } else if (str6.equalsIgnoreCase("oracle9i") || str6.equalsIgnoreCase("oracle10g") || str6.equalsIgnoreCase("oracle11g")) {
/*  78 */       if (str2 == null)
/*  79 */         str2 = "1521"; 
/*  80 */       str7 = "jdbc:oracle:thin:@" + str1 + ":" + str2 + ":" + str3;
/*  81 */       str8 = "oracle.jdbc.OracleDriver";
/*  82 */     } else if (str6.equalsIgnoreCase("oracle12c")) {
/*  83 */       if (str2 == null)
/*  84 */         str2 = "1521"; 
/*  85 */       str7 = "jdbc:oracle:thin:@//" + str1 + ":" + str2 + "/" + str3;
/*  86 */       str8 = "oracle.jdbc.driver.OracleDriver";
/*  87 */     } else if (str6.equalsIgnoreCase("sqlserver2005") || str6.equalsIgnoreCase("sqlserver2008") || str6.equalsIgnoreCase("sqlserver2014")) {
/*  88 */       if (str2 == null)
/*  89 */         str2 = "1433"; 
/*  90 */       str7 = "jdbc:sqlserver://" + str1 + ":" + str2 + ";DatabaseName=" + str3;
/*  91 */       str8 = "com.microsoft.sqlserver.jdbc.SQLServerDriver";
/*     */     } 
/*     */     
/*     */     try {
/*  95 */       Driver driver = (Driver)Class.forName(str8).newInstance();
/*  96 */       DriverManager.registerDriver(driver);
/*  97 */       connection = DriverManager.getConnection(str7, str4, str5);
/*  98 */       DBUpgradeLogger.write2File("获取到连接，连接正常.");
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     }
/* 109 */     catch (Exception exception) {
/* 110 */       if (exception.toString().indexOf("java.lang.ClassNotFoundException") > -1) {
/* 111 */         DBUpgradeLogger.write2File("error====链接外部数据库失败!错误信息:ClassNotFoundException.");
/* 112 */         BaseBean baseBean = new BaseBean();
/* 113 */         baseBean.writeLog("error====链接外部数据库失败!错误信息:ClassNotFoundException.");
/* 114 */       } else if (exception.toString().indexOf("用户") > -1 || exception.toString().indexOf("user") > -1 || exception.toString().indexOf("password") > -1 || exception.toString().indexOf(str4) > -1) {
/* 115 */         DBUpgradeLogger.write2File("error====链接外部数据库失败!错误信息:用户名密码错误.");
/* 116 */         BaseBean baseBean = new BaseBean();
/* 117 */         baseBean.writeLog("error====链接外部数据库失败!错误信息:用户名密码错误.");
/* 118 */       } else if (exception.toString().indexOf("java.sql.SQLException: No suitable driver found for") > -1 && exception.toString().indexOf(str7) > -1) {
/* 119 */         DBUpgradeLogger.write2File("error====链接外部数据库失败!错误信息:缺少驱动.");
/* 120 */         BaseBean baseBean = new BaseBean();
/* 121 */         baseBean.writeLog("error====链接外部数据库失败!错误信息:缺少驱动");
/* 122 */       } else if (exception.toString().indexOf("The Network Adapter could not establish the connection") > -1 || exception.toString().indexOf("Communications link failure") > -1 || exception.toString().indexOf(str1) > -1 || exception.toString().indexOf(str2) > -1) {
/* 123 */         DBUpgradeLogger.write2File("error====链接外部数据库失败!错误信息:The Network Adapter could not establish the connection.");
/* 124 */         BaseBean baseBean = new BaseBean();
/* 125 */         baseBean.writeLog("error====链接外部数据库失败!错误信息:The Network Adapter could not establish the connection");
/* 126 */       } else if (exception.toString().indexOf("SID") > -1 || exception.toString().indexOf(str3) > -1) {
/* 127 */         DBUpgradeLogger.write2File("error====链接外部数据库失败!错误信息:SID/dbname错误.");
/* 128 */         BaseBean baseBean = new BaseBean();
/* 129 */         baseBean.writeLog("error====链接外部数据库失败!错误信息:SID/dbname错误");
/*     */       } else {
/* 131 */         DBUpgradeLogger.write2File("error====链接外部数据库失败!错误信息:未知错误.");
/* 132 */         BaseBean baseBean = new BaseBean();
/* 133 */         baseBean.writeLog("error====链接外部数据库失败!错误信息:未知错误错误");
/*     */       } 
/* 135 */       if (null != connection) {
/*     */         try {
/* 137 */           connection.close();
/* 138 */         } catch (Exception exception1) {}
/*     */       }
/*     */     } finally {}
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 157 */     return connection;
/*     */   }
/*     */   
/*     */   public Connection getBeTransConnection(HttpServletRequest paramHttpServletRequest) {
/* 161 */     String str1 = Util.null2String(paramHttpServletRequest.getParameter("dbserver"));
/* 162 */     String str2 = Util.null2String(paramHttpServletRequest.getParameter("dbport"));
/* 163 */     String str3 = Util.null2String(paramHttpServletRequest.getParameter("dbname"));
/* 164 */     String str4 = Util.null2String(paramHttpServletRequest.getParameter("username"));
/* 165 */     String str5 = Util.null2String(paramHttpServletRequest.getParameter("password"));
/* 166 */     String str6 = Util.null2String(paramHttpServletRequest.getParameter("dbtype"));
/* 167 */     String str7 = "";
/* 168 */     String str8 = "";
/*     */     
/* 170 */     if (str6.equalsIgnoreCase("sqlserver2000")) {
/* 171 */       if (str2 == null)
/* 172 */         str2 = "1433"; 
/* 173 */       str7 = "jdbc:jtds:sqlserver://" + str1 + ":" + str2 + ";DatabaseName=" + str3;
/* 174 */       str8 = "net.sourceforge.jtds.jdbc.Driver";
/* 175 */     } else if (str6.equalsIgnoreCase("oracle9i") || str6.equalsIgnoreCase("oracle10g") || str6.equalsIgnoreCase("oracle11g")) {
/* 176 */       if (str2 == null)
/* 177 */         str2 = "1521"; 
/* 178 */       str7 = "jdbc:oracle:thin:@" + str1 + ":" + str2 + ":" + str3;
/* 179 */       str8 = "oracle.jdbc.OracleDriver";
/* 180 */     } else if (str6.equalsIgnoreCase("oracle12c")) {
/* 181 */       if (str2 == null)
/* 182 */         str2 = "1521"; 
/* 183 */       str7 = "jdbc:oracle:thin:@" + str1 + ":" + str2 + "/" + str3;
/* 184 */       str8 = "oracle.jdbc.driver.OracleDriver";
/* 185 */     } else if (str6.equalsIgnoreCase("sqlserver2005") || str6.equalsIgnoreCase("sqlserver2008") || str6.equalsIgnoreCase("sqlserver2014")) {
/* 186 */       if (str2 == null)
/* 187 */         str2 = "1433"; 
/* 188 */       str7 = "jdbc:sqlserver://" + str1 + ":" + str2 + ";DatabaseName=" + str3;
/* 189 */       str8 = "com.microsoft.sqlserver.jdbc.SQLServerDriver";
/*     */     } 
/* 191 */     Connection connection = null;
/*     */     try {
/* 193 */       Driver driver = (Driver)Class.forName(str8).newInstance();
/* 194 */       DriverManager.registerDriver(driver);
/* 195 */       connection = DriverManager.getConnection(str7, str4, str5);
/* 196 */       DBUpgradeLogger.write2File("获取到连接，连接正常.");
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     }
/* 207 */     catch (Exception exception) {
/* 208 */       if (exception.toString().indexOf("java.lang.ClassNotFoundException") > -1) {
/* 209 */         DBUpgradeLogger.write2File("error====链接外部数据库失败!错误信息:ClassNotFoundException.");
/* 210 */         BaseBean baseBean = new BaseBean();
/* 211 */         baseBean.writeLog("error====链接外部数据库失败!错误信息:ClassNotFoundException.");
/* 212 */       } else if (exception.toString().indexOf("用户") > -1 || exception.toString().indexOf("user") > -1 || exception.toString().indexOf("password") > -1 || exception.toString().indexOf(str4) > -1) {
/* 213 */         DBUpgradeLogger.write2File("error====链接外部数据库失败!错误信息:用户名密码错误.");
/* 214 */         BaseBean baseBean = new BaseBean();
/* 215 */         baseBean.writeLog("error====链接外部数据库失败!错误信息:用户名密码错误.");
/* 216 */       } else if (exception.toString().indexOf("java.sql.SQLException: No suitable driver found for") > -1 && exception.toString().indexOf(str7) > -1) {
/* 217 */         DBUpgradeLogger.write2File("error====链接外部数据库失败!错误信息:缺少驱动.");
/* 218 */         BaseBean baseBean = new BaseBean();
/* 219 */         baseBean.writeLog("error====链接外部数据库失败!错误信息:缺少驱动");
/* 220 */       } else if (exception.toString().indexOf("The Network Adapter could not establish the connection") > -1 || exception.toString().indexOf("Communications link failure") > -1 || exception.toString().indexOf(str1) > -1 || exception.toString().indexOf(str2) > -1) {
/* 221 */         DBUpgradeLogger.write2File("error====链接外部数据库失败!错误信息:The Network Adapter could not establish the connection.");
/* 222 */         BaseBean baseBean = new BaseBean();
/* 223 */         baseBean.writeLog("error====链接外部数据库失败!错误信息:The Network Adapter could not establish the connection");
/* 224 */       } else if (exception.toString().indexOf("SID") > -1 || exception.toString().indexOf(str3) > -1) {
/* 225 */         DBUpgradeLogger.write2File("error====链接外部数据库失败!错误信息:SID/dbname错误.");
/* 226 */         BaseBean baseBean = new BaseBean();
/* 227 */         baseBean.writeLog("error====链接外部数据库失败!错误信息:SID/dbname错误");
/*     */       } else {
/* 229 */         DBUpgradeLogger.write2File("error====链接外部数据库失败!错误信息:未知错误.");
/* 230 */         BaseBean baseBean = new BaseBean();
/* 231 */         baseBean.writeLog("error====链接外部数据库失败!错误信息:未知错误错误");
/*     */       } 
/* 233 */       if (null != connection) {
/*     */         try {
/* 235 */           connection.close();
/* 236 */         } catch (Exception exception1) {}
/*     */       }
/*     */     } finally {}
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 249 */     return connection;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getDBtype() {
/* 254 */     String str1 = this.prop.getValues("dbtype");
/* 255 */     String str2 = "";
/* 256 */     if (str1.toLowerCase().indexOf("sqlserver") > -1) {
/* 257 */       str2 = "sqlserver";
/* 258 */     } else if (str1.toLowerCase().indexOf("oracle") > -1) {
/* 259 */       str2 = "oracle";
/*     */     } 
/* 261 */     return str2;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getBeforeDBCharacterSet(Connection paramConnection, String paramString) {
/* 271 */     String str1 = null;
/* 272 */     String str2 = "select cversion from license";
/*     */     
/* 274 */     String str3 = "";
/*     */     try {
/* 276 */       PreparedStatement preparedStatement = paramConnection.prepareStatement(str2);
/* 277 */       ResultSet resultSet = preparedStatement.executeQuery();
/* 278 */       if (resultSet.next()) {
/* 279 */         str3 = resultSet.getString("cversion");
/*     */       }
/* 281 */       setBeforeLicense(Util.null2String(str3));
/* 282 */     } catch (SQLException sQLException) {
/* 283 */       sQLException.printStackTrace();
/* 284 */       return str1;
/*     */     } 
/* 286 */     if (str3.startsWith("7.")) {
/* 287 */       if (paramString.equals("sqlserver")) {
/* 288 */         str1 = "936";
/* 289 */       } else if (paramString.equals("oracle")) {
/* 290 */         str1 = "ZHS16GBK";
/*     */       } 
/*     */     } else {
/* 293 */       if (paramString.equals("oracle")) {
/* 294 */         str2 = "SELECT value AS characterset from nls_database_parameters where parameter='NLS_CHARACTERSET'";
/* 295 */       } else if (paramString.equals("sqlserver")) {
/* 296 */         str2 = "SELECT CAST(COLLATIONPROPERTY('Chinese_PRC_Stroke_CI_AI_KS_WS', 'CodePage') AS varchar(500)) AS characterset";
/*     */       } 
/*     */       
/*     */       try {
/* 300 */         PreparedStatement preparedStatement = paramConnection.prepareStatement(str2);
/* 301 */         ResultSet resultSet = preparedStatement.executeQuery();
/* 302 */         if (resultSet.next()) {
/* 303 */           str1 = resultSet.getString("characterset");
/*     */         }
/* 305 */       } catch (SQLException sQLException) {
/* 306 */         sQLException.printStackTrace();
/* 307 */         return str1;
/*     */       } 
/*     */     } 
/* 310 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getNowDBCharacterSet() {
/* 319 */     String str1 = null;
/* 320 */     String str2 = null;
/* 321 */     UpgradeRecordSet upgradeRecordSet = new UpgradeRecordSet();
/* 322 */     RecordSet recordSet = new RecordSet();
/* 323 */     String str3 = upgradeRecordSet.getDBType();
/* 324 */     if (str3.equalsIgnoreCase("oracle")) {
/* 325 */       str2 = "SELECT value AS characterset from nls_database_parameters where parameter='NLS_CHARACTERSET'";
/* 326 */     } else if (str3.equalsIgnoreCase("sqlserver")) {
/* 327 */       str2 = "SELECT CAST(COLLATIONPROPERTY('Chinese_PRC_Stroke_CI_AI_KS_WS', 'CodePage') as varchar(500)) as characterset";
/*     */     } 
/* 329 */     upgradeRecordSet.executeQuery(str2, new Object[0]);
/* 330 */     if (upgradeRecordSet.next()) {
/* 331 */       str1 = upgradeRecordSet.getString("characterset");
/*     */     }
/* 333 */     return str1;
/*     */   }
/*     */   
/*     */   public void setBeforeLicense(String paramString) {
/* 337 */     this; beforeLicense = paramString;
/*     */   }
/*     */   
/*     */   public String getBeforeLicense() {
/* 341 */     DBUpgradeLogger.write2File("beforeLicense正在获取");
/* 342 */     this; if ("".equals(Util.null2String(beforeLicense))) {
/* 343 */       DBUpgradeLogger.write2File("beforeLicense为空，正在重新配置");
/* 344 */       String str1 = Util.null2String(this.prop.getValues("beforeLicense"));
/* 345 */       if (!"".equals(str1)) {
/* 346 */         this; beforeLicense = str1;
/* 347 */         this; return beforeLicense;
/*     */       } 
/* 349 */       DBUpgradeLogger.write2File("beforeLicense文件中为空，正在重新配置");
/* 350 */       String str2 = GCONST.getRootPath() + "system" + File.separatorChar + "upgradetoe9" + File.separatorChar + "resource" + File.separatorChar + "weaver_source_bak.properties";
/* 351 */       PropUtil propUtil = PropUtil.getInstance(new String[] { str2 });
/* 352 */       String str3 = "";
/* 353 */       str3 = propUtil.getValues("beforeLicense");
/* 354 */       DBUpgradeLogger.write2File("beforeLicense文件中为空，cversion=" + str3);
/* 355 */       beforeLicense = str3;
/*     */       
/* 357 */       if ("".equals(beforeLicense.trim())) {
/*     */         
/* 359 */         Statement statement = null;
/* 360 */         ResultSet resultSet = null;
/* 361 */         Connection connection = null;
/*     */         
/*     */         try {
/* 364 */           connection = getSourceConnection();
/* 365 */           statement = connection.createStatement();
/* 366 */           String str = "select cversion from license";
/* 367 */           resultSet = statement.executeQuery(str);
/*     */           
/* 369 */           if (resultSet.next()) {
/* 370 */             str3 = resultSet.getString("cversion");
/*     */           }
/* 372 */           beforeLicense = str3;
/* 373 */         } catch (SQLException sQLException) {
/* 374 */           DBUpgradeLogger.write2File("beforeLicense重新配置异常:" + sQLException.getMessage());
/*     */         } finally {
/*     */           try {
/* 377 */             if (resultSet != null) {
/* 378 */               resultSet.close();
/*     */             }
/* 380 */             if (statement != null) {
/* 381 */               statement.close();
/*     */             }
/* 383 */             if (connection != null) {
/* 384 */               connection.close();
/*     */             }
/* 386 */           } catch (Exception exception) {
/* 387 */             exception.printStackTrace();
/* 388 */             DBUpgradeLogger.write2File("关闭资源出现异常");
/*     */           } 
/*     */         } 
/*     */       } 
/*     */ 
/*     */       
/* 394 */       BufferedWriter bufferedWriter = null;
/* 395 */       String str4 = GCONST.getRootPath() + "system" + File.separatorChar + "upgradetoe9" + File.separatorChar + "resource" + File.separatorChar + "weaver_source.properties";
/* 396 */       File file = new File(str4);
/* 397 */       if (file.exists()) {
/*     */         try {
/* 399 */           bufferedWriter = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(file, true), "GBK"));
/* 400 */           String str = "";
/* 401 */           if (!"".equals(Util.null2String(str3))) {
/* 402 */             DBUpgradeLogger.write2File("beforeLicense重新配置");
/* 403 */             str = "beforeLicense = " + str3;
/* 404 */             bufferedWriter.write(str);
/* 405 */             bufferedWriter.newLine();
/*     */           } 
/* 407 */           bufferedWriter.flush();
/* 408 */         } catch (Exception exception) {
/* 409 */           DBUpgradeLogger.write2File("beforeLicense重新配置异常:" + exception.getMessage());
/*     */         } finally {
/*     */           try {
/* 412 */             bufferedWriter.close();
/* 413 */           } catch (IOException iOException) {
/* 414 */             iOException.printStackTrace();
/*     */           } 
/*     */         } 
/*     */       }
/*     */     } 
/* 419 */     DBUpgradeLogger.write2File("beforeLicense获取成功");
/* 420 */     this; return beforeLicense;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/upgradetool/dbupgrade/upgrade/DBUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */