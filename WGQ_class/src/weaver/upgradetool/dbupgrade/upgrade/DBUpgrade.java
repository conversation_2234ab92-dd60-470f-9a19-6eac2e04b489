/*     */ package weaver.upgradetool.dbupgrade.upgrade;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import java.io.File;
/*     */ import java.io.IOException;
/*     */ import java.io.RandomAccessFile;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.TimeUtil;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class DBUpgrade
/*     */ {
/*     */   public JSONObject getResourceFile() {
/*  22 */     JSONObject jSONObject = new JSONObject();
/*  23 */     RecordSet recordSet = new RecordSet();
/*  24 */     recordSet.execute("SELECT filesystem from systemset ");
/*  25 */     if (recordSet.next()) {
/*  26 */       String str1 = recordSet.getString("filesystem");
/*  27 */       if ("".equals(str1) || str1 == null) {
/*  28 */         jSONObject.put("filesystem", recordSet.getString("/ecology/filesystem"));
/*     */       } else {
/*  30 */         jSONObject.put("filesystem", recordSet.getString("filesystem"));
/*     */       } 
/*     */     } else {
/*     */       
/*  34 */       jSONObject.put("filesystem", "无法获取");
/*     */     } 
/*     */     
/*  37 */     String str = "ecology/filesystem\\necology/WEB-INF/service";
/*  38 */     jSONObject.put("fileinfo", str);
/*  39 */     jSONObject.put("status", "success");
/*     */     
/*  41 */     return jSONObject;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void nextTask(String paramString1, String paramString2) {
/*  48 */     DBUpgradeOperation dBUpgradeOperation = new DBUpgradeOperation();
/*  49 */     dBUpgradeOperation.nextTask(paramString1, paramString2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public HashMap<String, String> getExecuteSQLInfo() {
/*  56 */     RecordSet recordSet = new RecordSet();
/*  57 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  58 */     recordSet.executeQuery("select status from DBUpgradeSqlCounter", new Object[0]);
/*  59 */     if (recordSet.next()) {
/*  60 */       String str = recordSet.getString("status");
/*     */       
/*  62 */       if ("1".equals(str)) {
/*  63 */         String str1 = TimeUtil.getCurrentDateString();
/*  64 */         String str2 = GCONST.getRootPath() + "sysupgradelog" + File.separatorChar + str1 + ".log";
/*  65 */         String str3 = getSqlFileLine(str2);
/*  66 */         hashMap.put("sqlErrorContent", str3);
/*     */       } 
/*     */       
/*  69 */       hashMap.put("sqlstatus", str);
/*     */     } 
/*     */ 
/*     */     
/*  73 */     return (HashMap)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getSqlFileLine(String paramString) {
/*  86 */     File file = new File(paramString);
/*  87 */     if (!file.exists()) {
/*  88 */       return "";
/*     */     }
/*  90 */     RandomAccessFile randomAccessFile = null;
/*  91 */     StringBuffer stringBuffer = new StringBuffer();
/*  92 */     ArrayList<String> arrayList = new ArrayList();
/*     */     try {
/*  94 */       randomAccessFile = new RandomAccessFile(paramString, "r");
/*  95 */       long l1 = randomAccessFile.length();
/*  96 */       long l2 = randomAccessFile.getFilePointer();
/*  97 */       long l3 = l2 + l1 - 1L;
/*     */       
/*  99 */       randomAccessFile.seek(l3);
/* 100 */       int i = -1;
/* 101 */       while (l3 > l2) {
/* 102 */         i = randomAccessFile.read();
/* 103 */         if (i == 10 || i == 13) {
/* 104 */           String str1 = randomAccessFile.readLine();
/* 105 */           if (str1 != null && !"".equals(str1)) {
/* 106 */             str1 = new String(str1.getBytes("ISO-8859-1"), "gbk");
/* 107 */             arrayList.add(str1);
/*     */           }
/* 109 */           else if (arrayList.size() > 0) {
/*     */             break;
/*     */           } 
/*     */           
/* 113 */           l3--;
/*     */         } 
/* 115 */         l3--;
/* 116 */         randomAccessFile.seek(l3);
/* 117 */         if (l3 == 0L) {
/* 118 */           String str1 = randomAccessFile.readLine();
/* 119 */           if (str1 != null && !"".equals(str1)) {
/* 120 */             str1 = new String(str1.getBytes("ISO-8859-1"), "gbk");
/*     */           }
/* 122 */           arrayList.add(str1);
/*     */         } 
/*     */       } 
/* 125 */       for (int j = arrayList.size() - 1; j >= 0; j--) {
/* 126 */         stringBuffer.append(arrayList.get(j)).append("\r\n");
/*     */       }
/*     */       
/* 129 */       String str = stringBuffer.toString();
/*     */       
/* 131 */       return str;
/* 132 */     } catch (Exception exception) {
/* 133 */       exception.printStackTrace();
/* 134 */       return null;
/*     */     } finally {
/*     */       try {
/* 137 */         if (randomAccessFile != null)
/* 138 */           randomAccessFile.close(); 
/* 139 */       } catch (IOException iOException) {
/* 140 */         iOException.printStackTrace();
/*     */       } 
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/upgradetool/dbupgrade/upgrade/DBUpgrade.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */