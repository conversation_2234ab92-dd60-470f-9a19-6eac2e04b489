/*     */ package weaver.upgradetool.dbupgrade.upgrade;
/*     */ import java.io.BufferedInputStream;
/*     */ import java.io.BufferedOutputStream;
/*     */ import java.io.BufferedReader;
/*     */ import java.io.File;
/*     */ import java.io.FileInputStream;
/*     */ import java.io.FileNotFoundException;
/*     */ import java.io.FileOutputStream;
/*     */ import java.io.FileWriter;
/*     */ import java.io.IOException;
/*     */ import java.io.InputStreamReader;
/*     */ import java.sql.Connection;
/*     */ import java.sql.PreparedStatement;
/*     */ import java.sql.ResultSet;
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ import java.util.regex.Matcher;
/*     */ import java.util.regex.Pattern;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.Util;
/*     */ import weaver.upgradetool.dbupgrade.logger.DBUpgradeLogger;
/*     */ 
/*     */ public class FileOperation {
/*     */   public boolean doGenerateSecondImportFile(String paramString) {
/*  26 */     boolean bool = true;
/*  27 */     DBUpgradeLogger.write2File("开始生成二次导入文件");
/*  28 */     String str1 = GCONST.getRootPath() + "system" + File.separatorChar + "upgradetoe9" + File.separatorChar + "resource" + File.separatorChar + "weaver_source.properties";
/*  29 */     PropUtil propUtil1 = PropUtil.getInstance(new String[] { str1 });
/*  30 */     String str2 = propUtil1.getValues("username");
/*  31 */     String str3 = propUtil1.getValues("password");
/*  32 */     String str4 = propUtil1.getValues("sourcepath");
/*  33 */     String str5 = GCONST.getRootPath() + "WEB-INF" + File.separatorChar + "prop" + File.separatorChar + "weaver.properties";
/*  34 */     PropUtil propUtil2 = PropUtil.getInstance(new String[] { str5 });
/*  35 */     String str6 = propUtil2.getValues("ecology.user");
/*  36 */     String str7 = propUtil2.getValues("ecology.password");
/*  37 */     String str8 = GCONST.getRootPath() + "system" + File.separatorChar + "upgradetoe9" + File.separatorChar + "resource" + File.separatorChar + "parfilesecond_imp.txt";
/*     */     
/*  39 */     RecordSet recordSet = new RecordSet();
/*  40 */     String str9 = recordSet.getDBType();
/*  41 */     if ("oracle".equalsIgnoreCase(str9)) {
/*  42 */       DBUpgradeLogger.write2File("oracle数据库");
/*  43 */       Connection connection = null;
/*  44 */       PreparedStatement preparedStatement = null;
/*  45 */       String str = "";
/*     */       try {
/*  47 */         DBUtil dBUtil = new DBUtil();
/*  48 */         connection = dBUtil.getSourceConnection();
/*  49 */         preparedStatement = connection.prepareStatement("select default_tablespace from dba_users where username=?");
/*  50 */         preparedStatement.setString(1, str2.toUpperCase());
/*  51 */         ResultSet resultSet = preparedStatement.executeQuery();
/*  52 */         while (resultSet.next()) {
/*  53 */           str = resultSet.getString("default_tablespace");
/*     */         }
/*  55 */         DBUpgradeLogger.write2File("oracle数据库85：" + str);
/*  56 */         preparedStatement.close();
/*  57 */       } catch (Exception exception) {
/*  58 */         bool = false;
/*  59 */         DBUpgradeLogger.write2File("加载数据库驱动出现问题：" + exception);
/*     */       } finally {
/*     */         try {
/*  62 */           if (preparedStatement != null && !preparedStatement.isClosed()) {
/*  63 */             preparedStatement.close();
/*     */           }
/*  65 */         } catch (Exception exception) {
/*  66 */           DBUpgradeLogger.write2File(exception.getMessage());
/*     */         } 
/*     */         try {
/*  69 */           if (connection != null && !connection.isClosed()) {
/*  70 */             connection.close();
/*     */           }
/*  72 */         } catch (Exception exception) {
/*  73 */           DBUpgradeLogger.write2File(exception.getMessage());
/*     */         } 
/*     */       } 
/*  76 */       if (!bool) {
/*  77 */         return bool;
/*     */       }
/*  79 */       FileWriter fileWriter = null;
/*     */       try {
/*  81 */         File file = new File(str8);
/*  82 */         if (file.exists()) {
/*  83 */           file.delete();
/*     */         }
/*  85 */         file.createNewFile();
/*  86 */         fileWriter = new FileWriter(file);
/*  87 */         fileWriter.write("userid=" + str6 + "/" + str7 + "\r\n");
/*  88 */         fileWriter.write("directory=" + str4 + "\r\n");
/*  89 */         fileWriter.write("dumpfile=" + paramString + ".dmp\r\n");
/*  90 */         fileWriter.write("transform=oid:N\r\n");
/*  91 */         fileWriter.write("logfile=" + paramString + "_imp.log\r\n");
/*  92 */         fileWriter.write("remap_schema=" + str2 + ":" + str6 + "\r\n");
/*  93 */         recordSet.executeQuery("select username,default_tablespace from user_users", new Object[0]);
/*  94 */         String str10 = "";
/*  95 */         if (recordSet.next()) {
/*  96 */           str10 = recordSet.getString("default_tablespace");
/*     */         }
/*  98 */         if (!str.equalsIgnoreCase(str10)) {
/*  99 */           fileWriter.write("remap_tablespace=" + str + ":" + str10);
/*     */         }
/* 101 */       } catch (Exception exception) {
/* 102 */         bool = false;
/* 103 */         DBUpgradeLogger.write2File("生成二次导入文件失败" + exception);
/*     */       } finally {
/* 105 */         if (fileWriter != null) {
/*     */           try {
/* 107 */             fileWriter.flush();
/* 108 */             fileWriter.close();
/* 109 */           } catch (Exception exception) {
/* 110 */             DBUpgradeLogger.write2File("生成二次导入文件失败" + exception);
/*     */           } 
/*     */         }
/*     */       } 
/*     */       
/* 115 */       return bool;
/* 116 */     }  if (!str9.equalsIgnoreCase("sqlserver"))
/*     */     {
/* 118 */       if (!str9.equalsIgnoreCase("dm"))
/*     */       {
/* 120 */         if (!str9.equalsIgnoreCase("mysql"))
/*     */         {
/* 122 */           if (str9.equalsIgnoreCase("postgresql")); } 
/*     */       }
/*     */     }
/* 125 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean doGenerateSecondExportFile(String paramString1, String paramString2) {
/* 132 */     DBUpgradeLogger.write2File("开始生成二次导出文件");
/* 133 */     boolean bool = true;
/* 134 */     DBUpgradeLogger.write2File("根路径：" + GCONST.getRootPath());
/* 135 */     String str1 = GCONST.getRootPath() + "system" + File.separatorChar + "upgradetoe9" + File.separatorChar + "resource" + File.separatorChar + "weaver_source.properties";
/*     */     
/* 137 */     PropUtil propUtil = PropUtil.getInstance(new String[] { str1 });
/* 138 */     String str2 = propUtil.getValues("username");
/* 139 */     String str3 = propUtil.getValues("password");
/* 140 */     String str4 = propUtil.getValues("sourcepath");
/* 141 */     String str5 = propUtil.getValues("dbtype");
/* 142 */     RecordSet recordSet = new RecordSet();
/* 143 */     String str6 = recordSet.getDBType();
/* 144 */     if ("oracle".equalsIgnoreCase(str6)) {
/*     */ 
/*     */       
/* 147 */       String str = GCONST.getRootPath() + "system" + File.separatorChar + "upgradetoe9" + File.separatorChar + "resource" + File.separatorChar + "parfilesecond_exp.txt";
/* 148 */       FileWriter fileWriter = null;
/*     */       try {
/* 150 */         File file = new File(str);
/* 151 */         if (file.exists()) {
/* 152 */           file.delete();
/*     */         }
/* 154 */         file.createNewFile();
/* 155 */         fileWriter = new FileWriter(file);
/* 156 */         fileWriter.write("userid=" + str2 + "/" + str3 + "\r\n");
/* 157 */         fileWriter.write("directory=" + str4 + "\r\n");
/* 158 */         fileWriter.write("dumpfile=" + paramString1 + ".dmp\r\n");
/* 159 */         fileWriter.write("logfile=" + paramString1 + "_exp.log\r\n");
/* 160 */         fileWriter.write("tables=" + paramString2);
/* 161 */       } catch (Exception exception) {
/* 162 */         bool = false;
/* 163 */         DBUpgradeLogger.write2File("创建二次导出文件失败" + exception);
/*     */       } finally {
/*     */         try {
/* 166 */           if (fileWriter != null) {
/* 167 */             fileWriter.flush();
/* 168 */             fileWriter.close();
/*     */           } 
/* 170 */         } catch (Exception exception) {
/* 171 */           DBUpgradeLogger.write2File("创建二次导出文件失败" + exception);
/*     */         } 
/*     */       } 
/* 174 */     } else if (!paramString1.equalsIgnoreCase("sqlserver")) {
/*     */       
/* 176 */       if (!str5.equalsIgnoreCase("dm"))
/*     */       {
/* 178 */         if (!str5.equalsIgnoreCase("mysql"))
/*     */         {
/* 180 */           if (str5.equalsIgnoreCase("postgresql")); } 
/*     */       }
/*     */     } 
/* 183 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<String> AnalyzeImpLogFile(String paramString) {
/* 190 */     ArrayList<String> arrayList = new ArrayList();
/*     */     
/* 192 */     String str1 = GCONST.getRootPath() + "system" + File.separatorChar + "upgradetoe9" + File.separatorChar + "resource" + File.separatorChar + "logfile";
/* 193 */     File file1 = new File(str1);
/* 194 */     if (!file1.exists()) {
/* 195 */       return null;
/*     */     }
/* 197 */     String str2 = str1 + File.separatorChar + "DBimporttemlog.log";
/* 198 */     File file2 = new File(str2);
/* 199 */     if (file2 == null || !file2.exists() || !file2.isFile()) {
/* 200 */       DBUpgradeLogger.write2File("日志文件不存在+logfilepath");
/* 201 */       return null;
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 207 */     if ("ORA-06502".equalsIgnoreCase(paramString)) {
/* 208 */       FileInputStream fileInputStream = null;
/* 209 */       BufferedReader bufferedReader = null;
/*     */       try {
/* 211 */         Pattern pattern = Pattern.compile("\"(.*?)\"\\.\"(.*?)\"");
/* 212 */         String str3 = "^ORA-31693[\\s\\S]*$";
/* 213 */         String str4 = "^ORA-06502[\\s\\S]*$";
/* 214 */         String str5 = "^LPX-00210[\\s\\S]*$";
/* 215 */         fileInputStream = new FileInputStream(file2);
/* 216 */         bufferedReader = new BufferedReader(new InputStreamReader(fileInputStream));
/* 217 */         String str6 = null;
/* 218 */         while ((str6 = bufferedReader.readLine()) != null) {
/* 219 */           if (str6.matches(str3)) {
/* 220 */             String str = str6;
/* 221 */             if ((str6 = bufferedReader.readLine()) != null) {
/* 222 */               if (str6.matches(str4)) {
/* 223 */                 if ((str6 = bufferedReader.readLine()) != null) {
/* 224 */                   if (str6.matches(str5)) {
/* 225 */                     Matcher matcher = pattern.matcher(str);
/* 226 */                     if (matcher.find()) {
/*     */                       try {
/* 228 */                         String str7 = Util.null2String(matcher.group(2));
/* 229 */                         arrayList.add(str7);
/* 230 */                       } catch (Exception exception) {
/* 231 */                         DBUpgradeLogger.write2File("日志文件解析出错" + exception);
/*     */                       } 
/*     */                     }
/*     */                   } 
/*     */                   
/*     */                   continue;
/*     */                 } 
/*     */                 
/*     */                 break;
/*     */               } 
/*     */               
/*     */               continue;
/*     */             } 
/*     */             
/*     */             break;
/*     */           } 
/*     */         } 
/* 248 */       } catch (Exception exception) {
/* 249 */         DBUpgradeLogger.write2File("" + exception);
/*     */       } finally {
/*     */         try {
/* 252 */           if (bufferedReader != null) {
/* 253 */             bufferedReader.close();
/*     */           }
/* 255 */           if (fileInputStream != null) {
/* 256 */             fileInputStream.close();
/*     */           }
/* 258 */         } catch (Exception exception) {
/* 259 */           DBUpgradeLogger.write2File(exception + "");
/*     */         } 
/*     */       } 
/*     */     } 
/* 263 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean doGenerateExportFile(String paramString) {
/* 270 */     boolean bool = true;
/* 271 */     DBUpgradeLogger.write2File("根路径：" + GCONST.getRootPath());
/* 272 */     String str1 = GCONST.getRootPath() + "system" + File.separatorChar + "upgradetoe9" + File.separatorChar + "resource" + File.separatorChar + "weaver_source.properties";
/*     */     
/* 274 */     PropUtil propUtil = PropUtil.getInstance(new String[] { str1 });
/* 275 */     String str2 = propUtil.getValues("username");
/* 276 */     String str3 = propUtil.getValues("password");
/* 277 */     String str4 = propUtil.getValues("sourcepath");
/* 278 */     String str5 = propUtil.getValues("dbtype");
/* 279 */     RecordSet recordSet = new RecordSet();
/* 280 */     String str6 = recordSet.getDBType();
/* 281 */     if ("oracle".equalsIgnoreCase(str6)) {
/*     */ 
/*     */       
/* 284 */       String str = GCONST.getRootPath() + "system" + File.separatorChar + "upgradetoe9" + File.separatorChar + "resource" + File.separatorChar + "parfile.txt";
/* 285 */       FileWriter fileWriter = null;
/*     */       try {
/* 287 */         File file = new File(str);
/* 288 */         if (file.exists()) {
/* 289 */           file.delete();
/*     */         }
/* 291 */         file.createNewFile();
/* 292 */         fileWriter = new FileWriter(file);
/* 293 */         fileWriter.write("userid=" + str2 + "/" + str3 + "\r\n");
/* 294 */         fileWriter.write("directory=" + str4 + "\r\n");
/* 295 */         fileWriter.write("dumpfile=" + paramString + ".dmp\r\n");
/* 296 */         fileWriter.write("content=DATA_ONLY\r\n");
/* 297 */         fileWriter.write("logfile=" + paramString + "_exp.log\r\n");
/*     */       
/*     */       }
/* 300 */       catch (Exception exception) {
/* 301 */         bool = false;
/* 302 */         DBUpgradeLogger.write2File("创建导出文件失败" + exception);
/*     */       } finally {
/*     */         try {
/* 305 */           if (fileWriter != null) {
/* 306 */             fileWriter.flush();
/* 307 */             fileWriter.close();
/*     */           } 
/* 309 */         } catch (Exception exception) {}
/*     */       }
/*     */     
/*     */     }
/* 313 */     else if (!paramString.equalsIgnoreCase("sqlserver")) {
/*     */       
/* 315 */       if (!str5.equalsIgnoreCase("dm"))
/*     */       {
/* 317 */         if (!str5.equalsIgnoreCase("mysql"))
/*     */         {
/* 319 */           if (str5.equalsIgnoreCase("postgresql")); } 
/*     */       }
/*     */     } 
/* 322 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean doGenerateImportFile(String paramString) {
/* 330 */     boolean bool = true;
/* 331 */     DBUpgradeLogger.write2File("开始生成导入文件");
/* 332 */     String str1 = GCONST.getRootPath() + "system" + File.separatorChar + "upgradetoe9" + File.separatorChar + "resource" + File.separatorChar + "weaver_source.properties";
/* 333 */     PropUtil propUtil1 = PropUtil.getInstance(new String[] { str1 });
/* 334 */     String str2 = propUtil1.getValues("username");
/* 335 */     String str3 = propUtil1.getValues("password");
/* 336 */     String str4 = propUtil1.getValues("sourcepath");
/* 337 */     String str5 = GCONST.getRootPath() + "WEB-INF" + File.separatorChar + "prop" + File.separatorChar + "weaver.properties";
/* 338 */     PropUtil propUtil2 = PropUtil.getInstance(new String[] { str5 });
/* 339 */     String str6 = propUtil2.getValues("ecology.user");
/* 340 */     String str7 = propUtil2.getValues("ecology.password");
/* 341 */     String str8 = GCONST.getRootPath() + "system" + File.separatorChar + "upgradetoe9" + File.separatorChar + "resource" + File.separatorChar + "parfile_imp.txt";
/*     */     
/* 343 */     RecordSet recordSet = new RecordSet();
/* 344 */     String str9 = recordSet.getDBType();
/* 345 */     if ("oracle".equalsIgnoreCase(str9)) {
/* 346 */       DBUpgradeLogger.write2File("oracle数据库");
/* 347 */       Connection connection = null;
/* 348 */       PreparedStatement preparedStatement = null;
/* 349 */       String str = "";
/*     */       try {
/* 351 */         DBUtil dBUtil = new DBUtil();
/* 352 */         connection = dBUtil.getSourceConnection();
/* 353 */         preparedStatement = connection.prepareStatement("select default_tablespace from dba_users where username=?");
/* 354 */         preparedStatement.setString(1, str2.toUpperCase());
/* 355 */         ResultSet resultSet = preparedStatement.executeQuery();
/* 356 */         while (resultSet.next()) {
/* 357 */           str = resultSet.getString("default_tablespace");
/*     */         }
/* 359 */         DBUpgradeLogger.write2File("oracle数据库85：" + str);
/* 360 */         preparedStatement.close();
/* 361 */       } catch (Exception exception) {
/* 362 */         bool = false;
/* 363 */         DBUpgradeLogger.write2File("加载数据库驱动出现问题：" + exception);
/*     */       } finally {
/*     */         try {
/* 366 */           if (preparedStatement != null && !preparedStatement.isClosed()) {
/* 367 */             preparedStatement.close();
/*     */           }
/* 369 */         } catch (Exception exception) {
/* 370 */           DBUpgradeLogger.write2File(exception.getMessage());
/*     */         } 
/*     */         try {
/* 373 */           if (connection != null && !connection.isClosed()) {
/* 374 */             connection.close();
/*     */           }
/* 376 */         } catch (Exception exception) {
/* 377 */           DBUpgradeLogger.write2File(exception.getMessage());
/*     */         } 
/*     */       } 
/* 380 */       if (!bool) {
/* 381 */         return bool;
/*     */       }
/* 383 */       FileWriter fileWriter = null;
/*     */       try {
/* 385 */         File file = new File(str8);
/* 386 */         if (file.exists()) {
/* 387 */           file.delete();
/*     */         }
/* 389 */         file.createNewFile();
/* 390 */         fileWriter = new FileWriter(file);
/* 391 */         fileWriter.write("userid=" + str6 + "/" + str7 + "\r\n");
/* 392 */         fileWriter.write("directory=" + str4 + "\r\n");
/* 393 */         fileWriter.write("dumpfile=" + paramString + ".dmp\r\n");
/* 394 */         fileWriter.write("transform=oid:N\r\n");
/* 395 */         fileWriter.write("content=DATA_ONLY\r\n");
/* 396 */         fileWriter.write("logfile=" + paramString + "_imp.log\r\n");
/* 397 */         fileWriter.write("remap_schema=" + str2 + ":" + str6 + "\r\n");
/* 398 */         recordSet.executeQuery("select username,default_tablespace from user_users", new Object[0]);
/* 399 */         String str10 = "";
/* 400 */         if (recordSet.next()) {
/* 401 */           str10 = recordSet.getString("default_tablespace");
/*     */         }
/* 403 */         if (!str.equalsIgnoreCase(str10) && !"".equalsIgnoreCase(str) && !"".equalsIgnoreCase(str10)) {
/* 404 */           fileWriter.write("remap_tablespace=" + str + ":" + str10);
/*     */         }
/* 406 */         else if ("".equalsIgnoreCase(str)) {
/* 407 */           DBUpgradeLogger.write2File("源数据库表空间为空");
/* 408 */         } else if ("".equalsIgnoreCase(str10)) {
/* 409 */           DBUpgradeLogger.write2File("目标数据库表空间为空");
/*     */         }
/*     */       
/* 412 */       } catch (Exception exception) {
/* 413 */         bool = false;
/* 414 */         DBUpgradeLogger.write2File("生成导入文件失败" + exception);
/*     */       } finally {
/* 416 */         if (fileWriter != null) {
/*     */           try {
/* 418 */             fileWriter.flush();
/* 419 */             fileWriter.close();
/* 420 */           } catch (Exception exception) {
/* 421 */             DBUpgradeLogger.write2File("生成导入文件失败" + exception);
/*     */           } 
/*     */         }
/*     */       } 
/*     */       
/* 426 */       return bool;
/* 427 */     }  if (!str9.equalsIgnoreCase("sqlserver"))
/*     */     {
/* 429 */       if (!str9.equalsIgnoreCase("dm"))
/*     */       {
/* 431 */         if (!str9.equalsIgnoreCase("mysql"))
/*     */         {
/* 433 */           if (str9.equalsIgnoreCase("postgresql")); } 
/*     */       }
/*     */     }
/* 436 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void copyFile(String paramString1, String paramString2) {
/*     */     try {
/* 455 */       FileInputStream fileInputStream = new FileInputStream(paramString1);
/* 456 */       BufferedInputStream bufferedInputStream = new BufferedInputStream(fileInputStream);
/* 457 */       FileOutputStream fileOutputStream = new FileOutputStream(paramString2);
/* 458 */       BufferedOutputStream bufferedOutputStream = new BufferedOutputStream(fileOutputStream);
/* 459 */       int i = 0;
/* 460 */       while ((i = bufferedInputStream.read()) != -1) {
/* 461 */         bufferedOutputStream.write(i);
/*     */       }
/* 463 */       bufferedOutputStream.close();
/* 464 */       bufferedInputStream.close();
/* 465 */       fileOutputStream.close();
/* 466 */       fileInputStream.close();
/* 467 */     } catch (FileNotFoundException fileNotFoundException) {
/* 468 */       DBUpgradeLogger.write2File("文件未找到" + fileNotFoundException.getMessage());
/* 469 */     } catch (IOException iOException) {
/* 470 */       DBUpgradeLogger.write2File("文件复制失败" + iOException.getMessage());
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean delFile(String paramString) {
/* 480 */     File file = new File(paramString);
/* 481 */     if (!file.exists()) {
/* 482 */       return true;
/*     */     }
/* 484 */     if (file.isFile()) {
/* 485 */       return file.delete();
/*     */     }
/* 487 */     return true;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/upgradetool/dbupgrade/upgrade/FileOperation.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */