/*     */ package weaver.upgradetool.dbupgrade.upgrade;
/*     */ import java.io.BufferedInputStream;
/*     */ import java.io.BufferedWriter;
/*     */ import java.io.File;
/*     */ import java.io.FileInputStream;
/*     */ import java.io.FileOutputStream;
/*     */ import java.io.OutputStreamWriter;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ public class PropUtil extends BaseBean {
/*  13 */   public static String WEAVER_SOURCE = GCONST.getRootPath() + "system" + File.separatorChar + "upgradetoe9" + File.separatorChar + "resource" + File.separatorChar + "weaver_source.properties"; private Properties properties;
/*  14 */   public static String WEAVER = GCONST.getRootPath() + "WEB-INF" + File.separatorChar + "prop" + File.separatorChar + "weaver.properties";
/*  15 */   public static String MIGRATION = GCONST.getRootPath() + "system" + File.separatorChar + "upgradetoe9" + File.separatorChar + "resource" + File.separatorChar + "migration.properties"; private String path;
/*     */   
/*     */   public static PropUtil getInstance(String... paramVarArgs) {
/*  18 */     PropUtil propUtil = new PropUtil();
/*  19 */     if (paramVarArgs.length == 0) {
/*  20 */       propUtil.setPath(WEAVER_SOURCE);
/*  21 */       propUtil.load();
/*     */     } else {
/*  23 */       propUtil.setPath(paramVarArgs[0]);
/*  24 */       propUtil.load();
/*     */     } 
/*  26 */     return propUtil;
/*     */   }
/*     */ 
/*     */   
/*     */   private synchronized void load() {
/*  31 */     FileInputStream fileInputStream = null;
/*  32 */     BufferedInputStream bufferedInputStream = null;
/*     */     try {
/*  34 */       if (null == this.properties)
/*  35 */         this.properties = new Properties(); 
/*  36 */       this.properties.clear();
/*  37 */       fileInputStream = new FileInputStream(new File(this.path));
/*  38 */       bufferedInputStream = new BufferedInputStream(fileInputStream);
/*  39 */       this.properties.load(new InputStreamReader(bufferedInputStream, "GBK"));
/*  40 */     } catch (IOException iOException) {
/*  41 */       writeLog("读取配置文件出错 : " + this.path);
/*     */     } finally {
/*     */       
/*     */       try {
/*  45 */         if (null != bufferedInputStream) {
/*  46 */           bufferedInputStream.close();
/*     */         }
/*  48 */         if (null != fileInputStream) {
/*  49 */           fileInputStream.close();
/*     */         }
/*  51 */       } catch (Exception exception) {}
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public String getValues(String paramString) {
/*  58 */     if (null != this.properties) {
/*  59 */       return this.properties.getProperty(paramString);
/*     */     }
/*  61 */     return null;
/*     */   }
/*     */   
/*     */   public void put(String paramString1, String paramString2) {
/*  65 */     if (null != this.properties)
/*  66 */       this.properties.put(paramString1, paramString2); 
/*     */   }
/*     */   
/*     */   public void clear() {
/*  70 */     if (null != this.properties) {
/*  71 */       this.properties.clear();
/*  72 */       this.properties = null;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean editProp(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6, String paramString7, String paramString8) {
/*  80 */     boolean bool = true;
/*     */     try {
/*  82 */       File file = new File(WEAVER_SOURCE);
/*  83 */       BufferedWriter bufferedWriter = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(file), "GBK"));
/*  84 */       String str = "";
/*     */       
/*  86 */       str = "dbserver = " + paramString1;
/*  87 */       bufferedWriter.write(str);
/*  88 */       bufferedWriter.newLine();
/*     */       
/*  90 */       str = "dbport = " + paramString2;
/*  91 */       bufferedWriter.write(str);
/*  92 */       bufferedWriter.newLine();
/*     */       
/*  94 */       str = "dbname = " + paramString3;
/*  95 */       bufferedWriter.write(str);
/*  96 */       bufferedWriter.newLine();
/*     */       
/*  98 */       str = "username = " + paramString4;
/*  99 */       bufferedWriter.write(str);
/* 100 */       bufferedWriter.newLine();
/*     */       
/* 102 */       str = "password = " + paramString5;
/* 103 */       bufferedWriter.write(str);
/* 104 */       bufferedWriter.newLine();
/*     */       
/* 106 */       str = "dbtype = " + paramString6;
/* 107 */       bufferedWriter.write(str);
/* 108 */       bufferedWriter.newLine();
/*     */       
/* 110 */       str = "sourcepath = " + paramString7;
/* 111 */       bufferedWriter.write(str);
/* 112 */       bufferedWriter.newLine();
/* 113 */       if (!"".equals(Util.null2String(paramString8))) {
/* 114 */         str = "beforeLicense = " + paramString8;
/* 115 */         bufferedWriter.write(str);
/* 116 */         bufferedWriter.newLine();
/*     */       } 
/* 118 */       bufferedWriter.flush();
/* 119 */       bufferedWriter.close();
/* 120 */     } catch (Exception exception) {
/* 121 */       (new BaseBean()).writeLog(exception);
/* 122 */       bool = false;
/*     */     } 
/* 124 */     return bool;
/*     */   }
/*     */   
/*     */   public synchronized void store() {
/* 128 */     BufferedWriter bufferedWriter = null;
/*     */     try {
/* 130 */       if (null == this.properties) {
/*     */         return;
/*     */       }
/*     */       
/* 134 */       Set set = this.properties.keySet();
/* 135 */       File file = new File(this.path);
/* 136 */       bufferedWriter = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(file)));
/* 137 */       for (String str1 : set) {
/*     */         
/* 139 */         String str2 = Util.null2String(str1);
/* 140 */         String str3 = str2;
/* 141 */         if (!"".equals(str2) && !str2.startsWith("#"))
/* 142 */           str3 = str3 + "=" + Util.null2String((String)this.properties.get(str2)); 
/* 143 */         bufferedWriter.write(str3);
/* 144 */         bufferedWriter.newLine();
/*     */       } 
/* 146 */       bufferedWriter.flush();
/* 147 */     } catch (Exception exception) {
/* 148 */       exception.printStackTrace();
/*     */     } finally {
/* 150 */       if (bufferedWriter != null) {
/*     */         try {
/* 152 */           bufferedWriter.close();
/* 153 */         } catch (IOException iOException) {}
/*     */       }
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public String getPath() {
/* 161 */     return this.path;
/*     */   }
/*     */   
/*     */   public void setPath(String paramString) {
/* 165 */     this.path = paramString;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/upgradetool/dbupgrade/upgrade/PropUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */