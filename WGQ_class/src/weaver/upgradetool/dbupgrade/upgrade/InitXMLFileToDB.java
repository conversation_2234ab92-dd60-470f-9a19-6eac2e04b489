/*      */ package weaver.upgradetool.dbupgrade.upgrade;
/*      */ import com.alibaba.fastjson.JSONObject;
/*      */ import java.io.File;
/*      */ import java.sql.Statement;
/*      */ import java.util.ArrayList;
/*      */ import java.util.Hashtable;
/*      */ import java.util.List;
/*      */ import java.util.Map;
/*      */ import java.util.Set;
/*      */ import org.jdom.Document;
/*      */ import org.jdom.Element;
/*      */ import org.jdom.input.SAXBuilder;
/*      */ import weaver.conn.ConnectionPool;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.conn.WeaverConnection;
/*      */ import weaver.conn.constant.DBConstant;
/*      */ import weaver.general.BaseBean;
/*      */ import weaver.general.SecurityHelper;
/*      */ import weaver.general.TimeUtil;
/*      */ import weaver.general.Util;
/*      */ import weaver.servicefiles.ActionXML;
/*      */ import weaver.servicefiles.BrowserXML;
/*      */ import weaver.servicefiles.DataSourceXML;
/*      */ import weaver.servicefiles.GetXMLContent;
/*      */ import weaver.servicefiles.SMSXML;
/*      */ import weaver.servicefiles.ScheduleXML;
/*      */ import weaver.systeminfo.SysMaintenanceLog;
/*      */ import weaver.upgradetool.dbupgrade.logger.DBUpgradeLogger;
/*      */ 
/*      */ public class InitXMLFileToDB extends BaseBean implements FilenameFilter {
/*   31 */   private Logger log = LoggerFactory.getLogger(InitXMLFileToDB.class);
/*      */ 
/*      */ 
/*      */   
/*   35 */   public GetXMLContent objXML = GetXMLContent.getObjXML();
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*   41 */   private String isInitDatasource = "";
/*      */ 
/*      */ 
/*      */   
/*   45 */   private String isInitSchedule = "";
/*      */ 
/*      */ 
/*      */   
/*   49 */   private String isInitAction = "";
/*      */ 
/*      */ 
/*      */   
/*   53 */   private String isInitBrowser = "";
/*      */ 
/*      */ 
/*      */   
/*   57 */   private String isInitHrsyn = "";
/*      */ 
/*      */ 
/*      */   
/*   61 */   private String isInitSms = "";
/*      */ 
/*      */ 
/*      */   
/*   65 */   private String isInitE8Clear = "";
/*      */ 
/*      */ 
/*      */   
/*   69 */   private String isInitEncrypted = "";
/*      */ 
/*      */ 
/*      */   
/*   73 */   private String isInitCheckErrorData = "";
/*      */ 
/*      */ 
/*      */   
/*   77 */   private String isInitXmlInitToDb = "";
/*      */ 
/*      */ 
/*      */   
/*   81 */   private DataSourceXML dsXML = null;
/*      */ 
/*      */ 
/*      */   
/*   85 */   private ScheduleXML sdXML = null;
/*      */ 
/*      */ 
/*      */   
/*   89 */   private ActionXML acXML = null;
/*      */ 
/*      */ 
/*      */   
/*   93 */   private BrowserXML bwXML = null;
/*      */ 
/*      */ 
/*      */   
/*   97 */   private SMSXML smsXML = null;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private boolean initDBState = false;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private boolean isoracle = false;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private boolean isMysql = false;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void initStart() {
/*  128 */     DBUpgradeLogger.write2File("开始初始化文件");
/*  129 */     initDB();
/*  130 */     initServices();
/*      */     
/*  132 */     RecordSet recordSet = new RecordSet();
/*  133 */     if (this.initDBState) {
/*  134 */       recordSet.executeSql("select * from initservicexmlstate");
/*  135 */       if (recordSet.next()) {
/*  136 */         this.isInitDatasource = "0";
/*  137 */         this.isInitSchedule = "0";
/*  138 */         this.isInitAction = "0";
/*  139 */         this.isInitBrowser = "0";
/*  140 */         this.isInitHrsyn = "0";
/*  141 */         this.isInitSms = "0";
/*  142 */         this.isInitE8Clear = "0";
/*  143 */         this.isInitXmlInitToDb = "0";
/*  144 */         this.isInitCheckErrorData = "0";
/*      */       } 
/*      */     } else {
/*  147 */       this.log.info("初始化失败，请检查……");
/*      */       return;
/*      */     } 
/*  150 */     if (!this.isInitDatasource.equals("1")) {
/*  151 */       this.dsXML = new DataSourceXML();
/*      */       
/*  153 */       initXMLData("datasource", "pointid", "datasourcesetting", null, "datasource.xml", "datasource");
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*      */     }
/*  159 */     else if (this.isInitEncrypted.equals("0")) {
/*  160 */       doEncrypt();
/*      */     } 
/*      */     
/*  163 */     if (!this.isInitSchedule.equals("1")) {
/*  164 */       this.sdXML = new ScheduleXML();
/*      */       
/*  166 */       initXMLData("schedule", "pointid", "schedulesetting", null, "schedule.xml", "schedule");
/*      */     } 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  172 */     if (!this.isInitAction.equals("1")) {
/*  173 */       this.acXML = new ActionXML();
/*      */       
/*  175 */       initXMLData("action", "actionname", "actionsetting", null, "action.xml", "actioninterface");
/*      */     } 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  181 */     if (!this.isInitBrowser.equals("1")) {
/*  182 */       this.bwXML = new BrowserXML();
/*      */       
/*  184 */       initXMLData("browser", "showname", "datashowset", null, "browser.xml", "browser");
/*      */     } 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  190 */     if (!this.isInitHrsyn.equals("1")) {
/*  191 */       this.smsXML = new SMSXML();
/*      */       
/*  193 */       initXMLData("sms", null, "smspropertis", null, "sms.xml", "hrsyn");
/*      */     } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  200 */     if (!this.isInitSms.equals("1") && 
/*  201 */       this.isInitHrsyn.equals("1")) {
/*  202 */       recordSet.executeSql("update initservicexmlstate set sms='1'");
/*      */     }
/*      */ 
/*      */     
/*  206 */     if (!this.isInitXmlInitToDb.equals("1")) {
/*  207 */       writeInitXMLToDB();
/*      */     }
/*  209 */     if (!this.isInitCheckErrorData.equals("1")) {
/*  210 */       checkErrorData();
/*      */     }
/*      */     
/*  213 */     if (!this.isInitE8Clear.equals("1")) {
/*  214 */       UpgradeClear.E8Clear();
/*  215 */       recordSet.executeSql("update initservicexmlstate set e8clear='1'");
/*      */     } 
/*  217 */     DBUpgradeLogger.write2File("初始化完成");
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void checkErrorData() {
/*  226 */     RecordSet recordSet = new RecordSet();
/*  227 */     this.isoracle = recordSet.getDBType().equals("oracle");
/*      */     try {
/*  229 */       StringBuffer stringBuffer1 = new StringBuffer();
/*  230 */       StringBuffer stringBuffer2 = new StringBuffer();
/*  231 */       if (this.isoracle) {
/*  232 */         stringBuffer1.append("select  count(1) from datashowset where INSTR(showpageurl,'formmode',1,1)>0 and browserfrom=0");
/*  233 */         stringBuffer2.append("update datashowset set browserfrom='1' where INSTR(showpageurl,'formmode',1,1)>0 and browserfrom=0");
/*      */       } else {
/*  235 */         stringBuffer1.append("select count(1) from datashowset where LEFT(showpageurl,9) ='/formmode' and browserfrom =0");
/*  236 */         stringBuffer2.append("update datashowset set browserfrom='1' where LEFT(showpageurl,9) ='/formmode' and browserfrom =0");
/*      */       } 
/*  238 */       recordSet.executeSql(stringBuffer1.toString());
/*  239 */       recordSet.next();
/*  240 */       int i = recordSet.getInt(1);
/*  241 */       if (i > 0) {
/*  242 */         this.log.info("发现浏览框数据异常数：" + i);
/*  243 */         recordSet.executeSql(stringBuffer2.toString());
/*      */       } 
/*  245 */     } catch (Exception exception) {
/*  246 */       this.log.error(exception);
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void doEncrypt() {
/*  254 */     RecordSet recordSet1 = new RecordSet();
/*  255 */     RecordSet recordSet2 = new RecordSet();
/*      */     
/*  257 */     recordSet1.executeSql("select id, pointid, url, host, port, dbname from datasourcesetting order by id ");
/*  258 */     while (recordSet1.next()) {
/*  259 */       String str1 = Util.null2String(recordSet1.getString("id"));
/*  260 */       String str2 = Util.null2String(recordSet1.getString("pointid"));
/*  261 */       String str3 = Util.null2String(recordSet1.getString("url"));
/*  262 */       String str4 = Util.null2String(recordSet1.getString("host"));
/*  263 */       String str5 = Util.null2String(recordSet1.getString("port"));
/*  264 */       String str6 = Util.null2String(recordSet1.getString("dbname"));
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  272 */       String str7 = "update datasourcesetting set url='" + str3 + "',host='" + str4 + "',port='" + str5 + "',dbname='" + str6 + "' where pointid='" + str2 + "'";
/*  273 */       recordSet2.executeSql(str7);
/*      */ 
/*      */       
/*  276 */       SysMaintenanceLog sysMaintenanceLog = new SysMaintenanceLog();
/*  277 */       sysMaintenanceLog.resetParameter();
/*  278 */       sysMaintenanceLog.setRelatedId(Util.getIntValue(str1));
/*  279 */       sysMaintenanceLog.setRelatedName(str2 + "(INIT)");
/*  280 */       sysMaintenanceLog.setOperateType("2");
/*  281 */       sysMaintenanceLog.setOperateDesc("DataSourceSetting_Update");
/*  282 */       sysMaintenanceLog.setOperateItem("380");
/*  283 */       sysMaintenanceLog.setClientAddress("127.0.0.1");
/*      */       try {
/*  285 */         sysMaintenanceLog.setSysLogInfo();
/*  286 */       } catch (Exception exception) {
/*  287 */         this.log.error("更新数据源修改日志失败：", exception);
/*      */       } 
/*      */     } 
/*      */     
/*  291 */     recordSet2.executeSql("update initservicexmlstate set datasource_encrypted = '1' ");
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   private void initServices() {
/*  297 */     String str1 = GetXMLContent.rootpath + "/init";
/*  298 */     String str2 = GetXMLContent.rootpath + "/init_success";
/*  299 */     String str3 = GetXMLContent.rootpath + "/init_failure";
/*  300 */     RecordSet recordSet = new RecordSet();
/*  301 */     String str4 = "INSERT INTO Int_Service_Persistence_Log\n        ( org_filename ,\n          to_filename ,\n          opttime ,\n          result ,\n          summary\n        )\nVALUES  ( ?,           ?,          ?,          ?,          ?        )";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  314 */     File file = new File(str1);
/*  315 */     File[] arrayOfFile = file.listFiles();
/*  316 */     for (File file1 : arrayOfFile) {
/*  317 */       if (!file1.isDirectory()) {
/*  318 */         SAXBuilder sAXBuilder = new SAXBuilder();
/*  319 */         Document document = null;
/*  320 */         Element element = null;
/*  321 */         String str = "";
/*      */         try {
/*  323 */           str = file1.getName();
/*  324 */           document = sAXBuilder.build(file1);
/*  325 */           element = document.getRootElement();
/*  326 */           String str5 = element.getAttribute("id").getValue();
/*  327 */           String str6 = "";
/*  328 */           String str7 = "";
/*  329 */           String str8 = "";
/*  330 */           switch (str5) {
/*      */             case "action":
/*  332 */               str6 = "actionname";
/*  333 */               str7 = "actionsetting";
/*  334 */               str8 = "actioninterface";
/*  335 */               this.acXML = new ActionXML();
/*      */               break;
/*      */             case "datasource":
/*  338 */               str6 = "pointid";
/*  339 */               str7 = "datasourcesetting";
/*  340 */               str8 = "datasource";
/*  341 */               this.dsXML = new DataSourceXML();
/*      */               break;
/*      */             case "schedule":
/*  344 */               str6 = "pointid";
/*  345 */               str7 = "schedulesetting";
/*  346 */               str8 = "schedule";
/*  347 */               this.sdXML = new ScheduleXML();
/*      */               break;
/*      */             case "sms":
/*  350 */               str6 = null;
/*  351 */               str7 = "smspropertis";
/*  352 */               str8 = "hrsyn";
/*  353 */               this.smsXML = new SMSXML();
/*      */               break;
/*      */             case "browser":
/*  356 */               str6 = null;
/*  357 */               str7 = "datashowset";
/*  358 */               str8 = "browser";
/*  359 */               this.bwXML = new BrowserXML();
/*      */               break;
/*      */           } 
/*      */ 
/*      */ 
/*      */           
/*  365 */           boolean bool = initXMLData(str5, str6, str7, str1, str, str8);
/*  366 */           if (bool) {
/*      */             
/*  368 */             file1.renameTo(new File(str2 + File.separator + str));
/*  369 */             recordSet.executeUpdate(str4, new Object[] { str, "", TimeUtil.getCurrentTimeString(), "1", "success!" });
/*      */           } else {
/*      */             
/*  372 */             file1.renameTo(new File(str3 + File.separator + str));
/*  373 */             recordSet.executeUpdate(str4, new Object[] { str, "", TimeUtil.getCurrentTimeString(), "0", "failure!unsupport xml file!" });
/*      */           } 
/*  375 */         } catch (Exception exception) {
/*  376 */           this.log.error("==============initServices error:" + exception.getMessage());
/*  377 */           file1.renameTo(new File(str3 + File.separator + str));
/*  378 */           recordSet.executeUpdate(str4, new Object[] { str, "", TimeUtil.getCurrentTimeString(), "0", exception.getMessage() });
/*  379 */           exception.printStackTrace();
/*      */         } 
/*      */       } 
/*      */     } 
/*      */ 
/*      */ 
/*      */     
/*  386 */     arrayOfFile = file.listFiles();
/*  387 */     if (arrayOfFile.length > 0) {
/*  388 */       for (File file1 : arrayOfFile) {
/*  389 */         file1.delete();
/*      */       }
/*      */     }
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean writeInitXMLToDB() {
/*  401 */     boolean bool = true;
/*  402 */     String str1 = GetXMLContent.rootpath;
/*  403 */     String str2 = str1 + File.separatorChar + "init";
/*  404 */     File file = new File(str2);
/*      */     
/*      */     try {
/*  407 */       if (file.exists() && file.isDirectory()) {
/*      */         
/*  409 */         File[] arrayOfFile = file.listFiles(this);
/*  410 */         if (arrayOfFile.length > 0)
/*  411 */           this.log.error("Init service XML Start ..."); 
/*  412 */         for (File file1 : arrayOfFile) {
/*  413 */           String str = str2 + File.separatorChar + file1.getName();
/*  414 */           Element element = this.objXML.getFileContentByPath(str);
/*  415 */           if (null != element) {
/*  416 */             String str3 = "";
/*  417 */             String str4 = "";
/*  418 */             String str5 = "";
/*  419 */             String str6 = "";
/*  420 */             String str7 = "";
/*  421 */             String str8 = "";
/*  422 */             str3 = element.getAttributeValue("id");
/*  423 */             if (str3.equals("datasource")) {
/*  424 */               this.dsXML = new DataSourceXML();
/*  425 */               str4 = "pointid";
/*  426 */               str5 = "datasourcesetting";
/*  427 */               str8 = "datasource";
/*  428 */             } else if (str3.equals("schedule")) {
/*  429 */               this.sdXML = new ScheduleXML();
/*  430 */               str4 = "pointid";
/*  431 */               str5 = "schedulesetting";
/*  432 */               str8 = "schedule";
/*  433 */             } else if (str3.equals("action")) {
/*  434 */               this.acXML = new ActionXML();
/*  435 */               str4 = "actionname";
/*  436 */               str5 = "actionsetting";
/*  437 */               str8 = "actioninterface";
/*  438 */             } else if (str3.equals("browser")) {
/*  439 */               this.bwXML = new BrowserXML();
/*  440 */               str4 = "showname";
/*  441 */               str5 = "datashowset";
/*  442 */               str8 = "browser";
/*      */             } 
/*  444 */             if (!str3.equals("")) {
/*  445 */               bool = initXMLData(str3, str4, str5, str2, file1.getName(), str8);
/*      */             }
/*  447 */             if (bool) {
/*  448 */               file1.delete();
/*      */             } else {
/*  450 */               return bool;
/*      */             } 
/*      */           } 
/*      */         } 
/*  454 */         this.log.error("Init service XML End ...");
/*      */       } else {
/*  456 */         this.log.error("不存在目录,不处理！");
/*      */       } 
/*      */ 
/*      */       
/*  460 */       if (bool) {
/*  461 */         RecordSet recordSet = new RecordSet();
/*  462 */         bool = recordSet.executeSql("update initservicexmlstate set xmlinittodb='1'");
/*      */       } 
/*  464 */     } catch (Exception exception) {
/*  465 */       this.log.error(exception);
/*  466 */       exception.printStackTrace();
/*  467 */       bool = false;
/*      */     } 
/*      */     
/*  470 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean initXMLData(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6) {
/*  487 */     boolean bool = true;
/*  488 */     RecordSet recordSet = new RecordSet();
/*      */ 
/*      */ 
/*      */     
/*  492 */     ArrayList<String> arrayList = new ArrayList();
/*  493 */     if (!"".equals(paramString2)) {
/*  494 */       recordSet.executeSql("select " + paramString2 + " from " + paramString3);
/*  495 */       while (recordSet.next()) {
/*  496 */         arrayList.add(Util.null2String(recordSet.getString(1)));
/*      */       }
/*      */     } 
/*      */     
/*  500 */     Element element = null;
/*  501 */     if (paramString4 == null) {
/*  502 */       element = this.objXML.getFileContent(paramString5);
/*      */     } else {
/*  504 */       element = this.objXML.getFileContentByPath(paramString4 + File.separatorChar + paramString5);
/*      */     } 
/*  506 */     if (element == null) {
/*  507 */       return bool;
/*      */     }
/*  509 */     List list = element.getChildren("service-point");
/*      */     
/*  511 */     for (Element element1 : list) {
/*      */       
/*  513 */       String str1 = element1.getAttributeValue("id");
/*      */ 
/*      */ 
/*      */       
/*  517 */       Hashtable<Object, Object> hashtable1 = new Hashtable<>();
/*  518 */       Hashtable<Object, Object> hashtable2 = new Hashtable<>();
/*  519 */       Element element2 = element1.getChild("invoke-factory").getChild("construct");
/*  520 */       String str2 = element2.getAttributeValue("class");
/*      */       try {
/*  522 */         if (Class.forName(str2) == null) {
/*  523 */           throw new ClassNotFoundException(str2);
/*      */         }
/*  525 */       } catch (Exception exception) {
/*  526 */         DBUpgradeLogger.write2File("Class文件" + str2 + "不存在，请确定class文件是否迁移到系统文件夹对应的目录下");
/*  527 */       } catch (Throwable throwable) {
/*  528 */         DBUpgradeLogger.write2File("Class文件" + str2 + "不存在，请确定class文件是否迁移到系统文件夹对应的目录下");
/*      */       } 
/*      */       
/*  531 */       hashtable1.put("construct", str2);
/*  532 */       List list1 = element2.getChildren("set");
/*      */       
/*  534 */       for (Element element3 : list1) {
/*      */         
/*  536 */         String str3 = Util.null2String(element3.getAttributeValue("property"));
/*  537 */         String str4 = Util.null2String(element3.getAttributeValue("value"));
/*  538 */         if (!"".equals(str3)) {
/*  539 */           hashtable1.put(str3, str4);
/*      */         }
/*      */       } 
/*  542 */       if (paramString1.equals("action") || paramString1.equals("browser")) {
/*  543 */         List list2 = element1.getChild("invoke-factory").getChild("construct").getChildren("set-service");
/*  544 */         for (Element element3 : list2) {
/*      */           
/*  546 */           String str3 = element3.getAttributeValue("property");
/*  547 */           String str4 = element3.getAttributeValue("service-id");
/*  548 */           if (paramString1.equals("action")) {
/*  549 */             hashtable2.put(str3, str4); continue;
/*  550 */           }  if (paramString1.equals("browser"))
/*  551 */             hashtable1.put(str3, str4); 
/*      */         } 
/*      */       } 
/*  554 */       if (paramString1.equals("datasource")) {
/*  555 */         String str3 = Util.null2String(hashtable1.get("iscode"));
/*  556 */         if (str3.equals("")) {
/*  557 */           String str8 = Util.null2String(hashtable1.get("user"));
/*  558 */           hashtable1.put("user", SecurityHelper.encrypt("ecology", str8));
/*  559 */           String str9 = Util.null2String(hashtable1.get("password"));
/*  560 */           hashtable1.put("password", SecurityHelper.encrypt("ecology", str9));
/*      */           
/*  562 */           hashtable1.put("iscode", "1");
/*      */         } 
/*      */         
/*  565 */         String str4 = Util.null2String(hashtable1.get("url"));
/*  566 */         String str5 = Util.null2String(hashtable1.get("host"));
/*  567 */         String str6 = Util.null2String(hashtable1.get("port"));
/*  568 */         String str7 = Util.null2String(hashtable1.get("dbname"));
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  578 */         hashtable1.put("usepool", "1");
/*      */         
/*  580 */         recordSet.executeSql("update initservicexmlstate set datasource_encrypted = '1' ");
/*      */       } 
/*  582 */       bool = writeDateToDB(paramString1, str1, hashtable1, hashtable2);
/*  583 */       if (bool) {
/*  584 */         bool = recordSet.executeSql("update initservicexmlstate set " + paramString6 + "='1'");
/*  585 */         if (bool && paramString6.equals("hrsyn"))
/*  586 */           this.isInitHrsyn = "1"; 
/*      */         continue;
/*      */       } 
/*  589 */       bool = recordSet.executeSql("update initservicexmlstate set " + paramString6 + "='0'");
/*      */     } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  597 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private boolean writeDateToDB(String paramString1, String paramString2, Hashtable paramHashtable1, Hashtable paramHashtable2) {
/*  610 */     boolean bool = false;
/*      */     try {
/*  612 */       if (paramString1.equals("datasource")) {
/*  613 */         if (this.dsXML == null) {
/*  614 */           this.dsXML = new DataSourceXML();
/*      */         }
/*  616 */         bool = writeToDataSourceXMLAddNew(paramString2, paramHashtable1);
/*      */ 
/*      */         
/*  619 */         RecordSet recordSet = new RecordSet();
/*  620 */         SysMaintenanceLog sysMaintenanceLog = new SysMaintenanceLog();
/*  621 */         String str1 = "";
/*  622 */         String str2 = recordSet.getDBType();
/*  623 */         String str3 = "select id from datasourcesetting where lower(pointid) = ? ";
/*  624 */         recordSet.executeQuery(str3, new Object[] { paramString2.toLowerCase() });
/*  625 */         if (recordSet.next()) {
/*  626 */           str1 = Util.null2String(recordSet.getString("id"));
/*      */         }
/*  628 */         sysMaintenanceLog.resetParameter();
/*  629 */         sysMaintenanceLog.setRelatedId(Util.getIntValue(str1));
/*  630 */         sysMaintenanceLog.setRelatedName(paramString2 + "(INIT)");
/*  631 */         sysMaintenanceLog.setOperateType("1");
/*  632 */         sysMaintenanceLog.setOperateDesc("DataSourceSetting_Insert");
/*  633 */         sysMaintenanceLog.setOperateItem("380");
/*  634 */         sysMaintenanceLog.setClientAddress("127.0.0.1");
/*      */         try {
/*  636 */           sysMaintenanceLog.setSysLogInfo();
/*  637 */         } catch (Exception exception) {
/*  638 */           this.log.error("更新数据源新建日志失败：", exception);
/*      */         } 
/*  640 */       } else if (paramString1.equals("schedule")) {
/*  641 */         if (this.sdXML == null) {
/*  642 */           this.sdXML = new ScheduleXML();
/*      */         }
/*  644 */         String str = Util.null2String(this.sdXML.writeToScheduleXMLAddNew(paramString2, paramHashtable1));
/*  645 */         this.log.info("initManager writeToScheduleXMLAddNew result :" + str);
/*  646 */         if (str.equals("true")) {
/*  647 */           bool = true;
/*  648 */         } else if (str.equals("true")) {
/*  649 */           bool = false;
/*      */         } else {
/*      */           try {
/*  652 */             JSONObject jSONObject = (JSONObject)JSONObject.parse(str);
/*  653 */             if (jSONObject.getBoolean("status").booleanValue()) {
/*  654 */               bool = true;
/*      */             } else {
/*  656 */               bool = false;
/*      */             } 
/*  658 */           } catch (Exception exception) {
/*  659 */             (new BaseBean()).writeLog("执行过程中解析报错,不影响执行" + str.toString());
/*      */           }
/*      */         
/*      */         } 
/*  663 */       } else if (paramString1.equals("action")) {
/*  664 */         String[] arrayOfString1 = null;
/*  665 */         String[] arrayOfString2 = null;
/*  666 */         String[] arrayOfString3 = null;
/*  667 */         String str1 = Util.null2String(paramHashtable1.get("construct"));
/*  668 */         paramHashtable1.remove("construct");
/*  669 */         Set set1 = paramHashtable1.keySet();
/*  670 */         String str2 = "";
/*  671 */         String str3 = "";
/*  672 */         String str4 = "";
/*  673 */         for (String str : set1) {
/*  674 */           str2 = str2 + "," + str;
/*  675 */           str3 = str3 + "^*^" + ("".equals(paramHashtable1.get(str)) ? " " : (String)paramHashtable1.get(str));
/*  676 */           str4 = str4 + ",0";
/*      */         } 
/*  678 */         Set set2 = paramHashtable2.keySet();
/*  679 */         for (String str : set2) {
/*  680 */           str2 = str2 + "," + str;
/*  681 */           str3 = str3 + "," + ("".equals(paramHashtable2.get(str)) ? " " : (String)paramHashtable2.get(str));
/*  682 */           str4 = str4 + ",1";
/*      */         } 
/*  684 */         if (!str2.equals("")) {
/*  685 */           arrayOfString1 = Util.TokenizerString2(str2.substring(1), ",");
/*  686 */           arrayOfString2 = Util.TokenizerString2(str3.substring(1), "^*^");
/*  687 */           arrayOfString3 = Util.TokenizerString2(str4.substring(1), ",");
/*      */         } 
/*  689 */         if (this.acXML == null) {
/*  690 */           this.acXML = new ActionXML();
/*      */         }
/*  692 */         bool = this.acXML.writeToActionXMLAddNew(paramString2, paramString2, str1, arrayOfString1, arrayOfString2, arrayOfString3);
/*  693 */       } else if (paramString1.equals("browser")) {
/*  694 */         if (this.bwXML == null) {
/*  695 */           this.bwXML = new BrowserXML();
/*      */         }
/*  697 */         bool = this.bwXML.writeToBrowserXMLAddNew(paramString2, paramHashtable1);
/*  698 */       } else if (paramString1.equals("sms")) {
/*  699 */         if (this.smsXML == null) {
/*  700 */           this.smsXML = new SMSXML();
/*      */         }
/*  702 */         bool = this.smsXML.writeDBForSmsXMLNew(paramHashtable1);
/*      */       } 
/*  704 */     } catch (Exception exception) {
/*  705 */       this.log.error(exception);
/*  706 */       bool = false;
/*      */     } 
/*      */     
/*  709 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void initDB() {
/*  720 */     RecordSet recordSet = new RecordSet();
/*  721 */     this.isoracle = recordSet.getDBType().equals("oracle");
/*  722 */     this.isMysql = "mysql".equals(recordSet.getDBType());
/*      */     
/*      */     try {
/*  725 */       StringBuffer stringBuffer = new StringBuffer();
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  730 */       this.initDBState = true;
/*      */       
/*  732 */       String str1 = (String)((Map)DBConstant.COLUMN_TYPES.get(recordSet.getDBType())).get("varchar");
/*  733 */       String str2 = (String)((Map)DBConstant.COLUMN_TYPES.get(recordSet.getDBType())).get("int");
/*  734 */       String str3 = (String)((Map)DBConstant.COLUMN_TYPES.get(recordSet.getDBType())).get("id") + ",";
/*      */ 
/*      */       
/*  737 */       if (notExistTable("INITSERVICEXMLSTATE")) {
/*  738 */         stringBuffer = new StringBuffer();
/*  739 */         stringBuffer.append("create table initservicexmlstate(");
/*  740 */         stringBuffer.append("    datasource char(1),");
/*  741 */         stringBuffer.append("    datasource_encrypted char(1),");
/*  742 */         stringBuffer.append("    schedule char(1),");
/*  743 */         stringBuffer.append("    actioninterface char(1),");
/*  744 */         stringBuffer.append("    browser char(1),");
/*  745 */         stringBuffer.append("    hrsyn char(1),");
/*  746 */         stringBuffer.append("    e8clear char(1))");
/*      */         
/*  748 */         this.log.info("创建表：INITSERVICEXMLSTATE ……");
/*  749 */         boolean bool = createTable("INITSERVICEXMLSTATE", stringBuffer.toString());
/*  750 */         this.initDBState = this.initDBState ? bool : this.initDBState;
/*      */         
/*  752 */         if (bool) {
/*  753 */           boolean bool1 = recordSet.executeQuery("select count(1) from initservicexmlstate", new Object[0]);
/*  754 */           if (bool1 && recordSet.next() && recordSet.getInt(1) <= 0) {
/*  755 */             bool = recordSet.execute("insert into initservicexmlstate(datasource,schedule,actioninterface,browser,hrsyn,e8clear) values('0','0','0','0','0','0')");
/*  756 */             this.initDBState = this.initDBState ? bool : this.initDBState;
/*      */           } 
/*      */         } 
/*      */       } 
/*      */       
/*  761 */       if (!notExistTable("INITSERVICEXMLSTATE") && 
/*  762 */         notExistField("INITSERVICEXMLSTATE", "E8CLEAR")) {
/*  763 */         boolean bool = recordSet.execute("alter table INITSERVICEXMLSTATE add E8CLEAR char(1)");
/*  764 */         this.initDBState = this.initDBState ? bool : this.initDBState;
/*      */       } 
/*      */       
/*  767 */       if (!notExistTable("INITSERVICEXMLSTATE") && 
/*  768 */         notExistField("INITSERVICEXMLSTATE", "SMS")) {
/*  769 */         boolean bool = recordSet.execute("alter table INITSERVICEXMLSTATE add SMS char(1)");
/*  770 */         this.initDBState = this.initDBState ? bool : this.initDBState;
/*      */       } 
/*      */       
/*  773 */       if (!notExistTable("INITSERVICEXMLSTATE") && 
/*  774 */         notExistField("INITSERVICEXMLSTATE", "CHECKERRORDATA")) {
/*  775 */         boolean bool = recordSet.execute("alter table INITSERVICEXMLSTATE add CHECKERRORDATA char(1)");
/*  776 */         this.initDBState = this.initDBState ? bool : this.initDBState;
/*      */       } 
/*      */       
/*  779 */       if (!notExistTable("INITSERVICEXMLSTATE") && 
/*  780 */         notExistField("INITSERVICEXMLSTATE", "XMLINITTODB")) {
/*  781 */         boolean bool = recordSet.execute("alter table INITSERVICEXMLSTATE add XMLINITTODB char(1)");
/*  782 */         this.initDBState = this.initDBState ? bool : this.initDBState;
/*      */       } 
/*      */ 
/*      */ 
/*      */       
/*  787 */       if (notExistTable("DATASOURCESETTING")) {
/*  788 */         stringBuffer = new StringBuffer();
/*  789 */         stringBuffer.append("create table datasourcesetting(");
/*  790 */         stringBuffer.append(str3);
/*  791 */         stringBuffer.append("    pointid ").append(str1).append("(254),");
/*  792 */         stringBuffer.append("    classpath ").append(str1).append("(254),");
/*  793 */         stringBuffer.append("    type  ").append(str1).append("(50),");
/*  794 */         stringBuffer.append("    datasourcename ").append(str1).append("(254),");
/*  795 */         stringBuffer.append("    iscluster char(1),");
/*  796 */         stringBuffer.append("    typename ").append(str1).append("(254),");
/*  797 */         stringBuffer.append("    url ").append(str1).append("(2000),");
/*  798 */         stringBuffer.append("    host ").append(str1).append("(500),");
/*  799 */         stringBuffer.append("    port ").append(str1).append("(500),");
/*  800 */         stringBuffer.append("    dbname ").append(str1).append("(500),");
/*  801 */         stringBuffer.append("    username ").append(str1).append("(500),");
/*  802 */         stringBuffer.append("    password ").append(str1).append("(500),");
/*  803 */         stringBuffer.append("    minconn ").append(str2).append(",");
/*  804 */         stringBuffer.append("    maxconn ").append(str2).append(",");
/*  805 */         stringBuffer.append("    iscode char(1)");
/*  806 */         stringBuffer.append(")");
/*      */         
/*  808 */         this.log.info("创建表：DATASOURCESETTING ……");
/*  809 */         boolean bool = createTable("DATASOURCESETTING", stringBuffer.toString());
/*  810 */         if (this.isoracle) {
/*  811 */           createSequenceAndTrigger("DATASOURCESETTING");
/*      */         }
/*      */         
/*  814 */         this.initDBState = this.initDBState ? bool : this.initDBState;
/*      */       } 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  820 */       if (notExistTable("schedulesetting")) {
/*  821 */         stringBuffer = new StringBuffer();
/*  822 */         stringBuffer.append("create table schedulesetting(");
/*  823 */         stringBuffer.append(str3);
/*  824 */         stringBuffer.append("    pointid ").append(str1).append("(254),");
/*  825 */         stringBuffer.append("    classpath ").append(str1).append("(254),");
/*  826 */         stringBuffer.append("    cronexpr ").append(str1).append("(254),");
/*  827 */         stringBuffer.append("    createdate ").append(str1).append("(10),");
/*  828 */         stringBuffer.append("    createtime ").append(str1).append("(8),");
/*  829 */         stringBuffer.append("    modifydate ").append(str1).append("(10),");
/*  830 */         stringBuffer.append("    modifytime ").append(str1).append("(8))");
/*      */         
/*  832 */         this.log.info("创建表：SCHEDULESETTING ……");
/*  833 */         boolean bool = createTable("SCHEDULESETTING", stringBuffer.toString());
/*  834 */         if (this.isoracle) {
/*  835 */           createSequenceAndTrigger("SCHEDULESETTING");
/*      */         }
/*  837 */         this.initDBState = this.initDBState ? bool : this.initDBState;
/*      */       } 
/*      */ 
/*      */ 
/*      */       
/*  842 */       if (notExistTable("smspropertis")) {
/*  843 */         stringBuffer = new StringBuffer();
/*  844 */         stringBuffer.append("create table smspropertis(");
/*  845 */         stringBuffer.append(str3);
/*  846 */         stringBuffer.append("    prop ").append(str1).append("(254),");
/*  847 */         stringBuffer.append("    val ").append(str1).append("(1000))");
/*      */         
/*  849 */         this.log.info("创建表：SMSPROPERTIS ……");
/*  850 */         boolean bool = createTable("SMSPROPERTIS", stringBuffer.toString());
/*  851 */         if (this.isoracle) {
/*  852 */           createSequenceAndTrigger("SMSPROPERTIS");
/*      */         }
/*  854 */         this.initDBState = this.initDBState ? bool : this.initDBState;
/*      */       } 
/*      */ 
/*      */       
/*  858 */       if (!notExistTable("actionsettingdetail") && 
/*  859 */         notExistField("actionsettingdetail", "isdatasource")) {
/*      */         
/*  861 */         boolean bool = recordSet.execute("alter table actionsettingdetail add isdatasource char(1)");
/*      */         
/*  863 */         this.initDBState = this.initDBState ? bool : this.initDBState;
/*      */       } 
/*      */ 
/*      */       
/*  867 */       if (!notExistTable("datashowset")) {
/*  868 */         if (notExistField("datashowset", "searchByName")) {
/*      */           
/*  870 */           stringBuffer = new StringBuffer("");
/*  871 */           if (this.isoracle) {
/*  872 */             stringBuffer.append("alter table datashowset add searchByName varchar2(4000)");
/*      */           } else {
/*  874 */             stringBuffer.append("alter table datashowset add searchByName text");
/*      */           } 
/*  876 */           boolean bool = recordSet.execute(stringBuffer.toString());
/*      */           
/*  878 */           this.initDBState = this.initDBState ? bool : this.initDBState;
/*      */         } 
/*      */         
/*  881 */         if (notExistField("datashowset", "customid")) {
/*  882 */           boolean bool = recordSet.execute("alter table datashowset add customid " + str2);
/*      */           
/*  884 */           this.initDBState = this.initDBState ? bool : this.initDBState;
/*      */         } 
/*      */       } 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  891 */       if (!notExistTable("SCHEDULESETTING") && 
/*  892 */         notExistField("SCHEDULESETTING", "createdate")) {
/*  893 */         boolean bool = recordSet.executeSql("alter table SCHEDULESETTING add createdate " + str1 + "(10)");
/*      */         
/*  895 */         this.initDBState = this.initDBState ? bool : this.initDBState;
/*      */       } 
/*      */       
/*  898 */       if (!notExistTable("SCHEDULESETTING") && 
/*  899 */         notExistField("SCHEDULESETTING", "createtime")) {
/*  900 */         boolean bool = recordSet.executeSql("alter table SCHEDULESETTING add createtime " + str1 + "(8)");
/*      */         
/*  902 */         this.initDBState = this.initDBState ? bool : this.initDBState;
/*      */       } 
/*      */       
/*  905 */       if (!notExistTable("SCHEDULESETTING") && 
/*  906 */         notExistField("SCHEDULESETTING", "modifydate")) {
/*  907 */         boolean bool = recordSet.executeSql("alter table SCHEDULESETTING add modifydate " + str1 + "(10)");
/*      */         
/*  909 */         this.initDBState = this.initDBState ? bool : this.initDBState;
/*      */       } 
/*      */       
/*  912 */       if (!notExistTable("SCHEDULESETTING") && 
/*  913 */         notExistField("SCHEDULESETTING", "modifytime")) {
/*  914 */         boolean bool = recordSet.executeSql("alter table SCHEDULESETTING add modifytime " + str1 + "(8)");
/*      */         
/*  916 */         this.initDBState = this.initDBState ? bool : this.initDBState;
/*      */       } 
/*      */ 
/*      */       
/*  920 */       if (!notExistTable("SCHEDULESETTING") && 
/*  921 */         notExistField("SCHEDULESETTING", "runstatus")) {
/*  922 */         boolean bool = recordSet.executeSql("ALTER TABLE SCHEDULESETTING add runstatus " + str2 + " default 0  not null");
/*      */         
/*  924 */         this.initDBState = this.initDBState ? bool : this.initDBState;
/*      */       } 
/*      */       
/*  927 */       if (!notExistTable("datasourcesetting")) {
/*  928 */         if (notExistField("datasourcesetting", "usepool")) {
/*      */           
/*  930 */           stringBuffer = new StringBuffer();
/*  931 */           stringBuffer.append("alter table datasourcesetting add usepool ").append(str2).append(" default 1 ");
/*  932 */           boolean bool = recordSet.executeSql(stringBuffer.toString());
/*      */           
/*  934 */           this.initDBState = this.initDBState ? bool : this.initDBState;
/*      */           
/*  936 */           if (bool) {
/*  937 */             bool = recordSet.executeSql("update datasourcesetting set usepool = 1 ");
/*      */             
/*  939 */             this.initDBState = this.initDBState ? bool : this.initDBState;
/*      */           } 
/*      */         } 
/*      */ 
/*      */         
/*  944 */         if (notExistField("datasourcesetting", "sortid")) {
/*  945 */           stringBuffer = new StringBuffer();
/*      */           
/*  947 */           stringBuffer.append("alter table datasourcesetting add sortid ").append(str2).append(" default 1 ");
/*  948 */           boolean bool = recordSet.executeSql(stringBuffer.toString());
/*      */           
/*  950 */           this.initDBState = this.initDBState ? bool : this.initDBState;
/*      */           
/*  952 */           if (bool) {
/*  953 */             bool = recordSet.executeSql("update datasourcesetting set sortid = 1 ");
/*      */             
/*  955 */             this.initDBState = this.initDBState ? bool : this.initDBState;
/*      */           } 
/*      */           
/*  958 */           if ("oracle".equals(recordSet.getDBType())) {
/*  959 */             bool = recordSet.executeSql("alter table datasourcesetting modify url varchar2(2000) ");
/*  960 */             this.initDBState = this.initDBState ? bool : this.initDBState;
/*      */             
/*  962 */             bool = recordSet.executeSql("alter table datasourcesetting modify host varchar2(500) ");
/*  963 */             this.initDBState = this.initDBState ? bool : this.initDBState;
/*      */ 
/*      */             
/*  966 */             bool = recordSet.executeSql("alter table datasourcesetting rename column port to port_temp ");
/*  967 */             this.initDBState = this.initDBState ? bool : this.initDBState;
/*      */             
/*  969 */             bool = recordSet.executeSql("alter table datasourcesetting add port varchar2(500) ");
/*  970 */             this.initDBState = this.initDBState ? bool : this.initDBState;
/*      */             
/*  972 */             bool = recordSet.executeSql("update datasourcesetting set port = to_char(port_temp) ");
/*  973 */             this.initDBState = this.initDBState ? bool : this.initDBState;
/*      */             
/*  975 */             bool = recordSet.executeSql("alter table datasourcesetting drop column port_temp ");
/*  976 */             this.initDBState = this.initDBState ? bool : this.initDBState;
/*      */ 
/*      */             
/*  979 */             bool = recordSet.executeSql("alter table datasourcesetting modify dbname varchar2(500) ");
/*  980 */             this.initDBState = this.initDBState ? bool : this.initDBState;
/*      */             
/*  982 */             bool = recordSet.executeSql("alter table datasourcesetting modify username varchar2(500) ");
/*  983 */             this.initDBState = this.initDBState ? bool : this.initDBState;
/*      */             
/*  985 */             bool = recordSet.executeSql("alter table datasourcesetting modify password varchar2(500) ");
/*  986 */             this.initDBState = this.initDBState ? bool : this.initDBState;
/*      */           }
/*  988 */           else if ("sqlserver".equals(recordSet.getDBType())) {
/*  989 */             bool = recordSet.executeSql("alter table datasourcesetting alter column url varchar(2000) ");
/*  990 */             this.initDBState = this.initDBState ? bool : this.initDBState;
/*      */             
/*  992 */             bool = recordSet.executeSql("alter table datasourcesetting alter column host varchar(500) ");
/*  993 */             this.initDBState = this.initDBState ? bool : this.initDBState;
/*      */             
/*  995 */             bool = recordSet.executeSql("alter table datasourcesetting alter column port varchar(500) ");
/*  996 */             this.initDBState = this.initDBState ? bool : this.initDBState;
/*      */             
/*  998 */             bool = recordSet.executeSql("alter table datasourcesetting alter column dbname varchar(500) ");
/*  999 */             this.initDBState = this.initDBState ? bool : this.initDBState;
/*      */             
/* 1001 */             bool = recordSet.executeSql("alter table datasourcesetting alter column username varchar(500) ");
/* 1002 */             this.initDBState = this.initDBState ? bool : this.initDBState;
/*      */             
/* 1004 */             bool = recordSet.executeSql("alter table datasourcesetting alter column password varchar(500) ");
/* 1005 */             this.initDBState = this.initDBState ? bool : this.initDBState;
/*      */           }
/*      */           else {
/*      */             
/* 1009 */             bool = recordSet.executeSql("alter table datasourcesetting modify column url varchar(2000) ");
/* 1010 */             this.initDBState = this.initDBState ? bool : this.initDBState;
/*      */             
/* 1012 */             bool = recordSet.executeSql("alter table datasourcesetting modify column host varchar(500) ");
/* 1013 */             this.initDBState = this.initDBState ? bool : this.initDBState;
/*      */             
/* 1015 */             bool = recordSet.executeSql("alter table datasourcesetting modify column port varchar(500) ");
/* 1016 */             this.initDBState = this.initDBState ? bool : this.initDBState;
/*      */             
/* 1018 */             bool = recordSet.executeSql("alter table datasourcesetting modify column dbname varchar(500) ");
/* 1019 */             this.initDBState = this.initDBState ? bool : this.initDBState;
/*      */             
/* 1021 */             bool = recordSet.executeSql("alter table datasourcesetting modify column username varchar(500) ");
/* 1022 */             this.initDBState = this.initDBState ? bool : this.initDBState;
/*      */             
/* 1024 */             bool = recordSet.executeSql("alter table datasourcesetting modify column password varchar(500) ");
/* 1025 */             this.initDBState = this.initDBState ? bool : this.initDBState;
/*      */           } 
/* 1027 */           bool = recordSet.executeSql("update datasourcesetting set port = '' where iscluster = '2' ");
/* 1028 */           this.initDBState = this.initDBState ? bool : this.initDBState;
/*      */         
/*      */         }
/*      */ 
/*      */       
/*      */       }
/*      */     
/*      */     }
/* 1036 */     catch (Exception exception) {
/* 1037 */       this.log.error(exception);
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean notExistTable(String paramString) {
/* 1046 */     RecordSet recordSet = new RecordSet();
/*      */     
/* 1048 */     if (paramString == null) {
/* 1049 */       return Boolean.FALSE.booleanValue();
/*      */     }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/* 1064 */     String str1 = "";
/* 1065 */     String str2 = recordSet.getDBType().toLowerCase();
/* 1066 */     if ("oracle".equals(str2)) {
/* 1067 */       str1 = "select count(1) from user_tables where TABLE_NAME = upper(?)";
/* 1068 */     } else if ("sqlserver".equals(str2)) {
/* 1069 */       str1 = "select count(1) from sysobjects where LOWER(name)=LOWER(?) ";
/* 1070 */     } else if ("mysql".equals(str2)) {
/* 1071 */       str1 = "select count(1) from information_schema.Tables where LOWER(Table_Name)=LOWER(?) ";
/* 1072 */     } else if ("postgresql".equals(str2.toLowerCase())) {
/* 1073 */       str1 = "select count(1) from information_schema.Tables where LOWER(Table_Name)=LOWER(?) ";
/*      */     } 
/* 1075 */     boolean bool = recordSet.executeQuery(str1, new Object[] { paramString.trim().toUpperCase() });
/* 1076 */     if (bool && recordSet != null && recordSet.next()) {
/* 1077 */       int i = Util.getIntValue(recordSet.getString(1));
/* 1078 */       if (i == 0) {
/* 1079 */         this.log.info("table(" + paramString + ") not exist");
/* 1080 */         return Boolean.TRUE.booleanValue();
/*      */       } 
/*      */     } 
/* 1083 */     this.log.info("table(" + paramString + ") exist");
/* 1084 */     return Boolean.FALSE.booleanValue();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void createSequenceAndTrigger(String paramString) {
/* 1093 */     paramString = paramString.toUpperCase();
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*      */     try {
/* 1114 */       String str1 = paramString + "_id";
/*      */       
/* 1116 */       String str2 = paramString + "_Trigger";
/*      */       
/* 1118 */       this.log.info("创建sequence：" + str1.toUpperCase());
/* 1119 */       RecordSet recordSet = new RecordSet();
/* 1120 */       String str3 = " select * from user_sequences where sequence_name = '" + str1 + "'";
/* 1121 */       recordSet.execute(str3.toUpperCase());
/* 1122 */       if (!recordSet.next()) {
/* 1123 */         StringBuffer stringBuffer = new StringBuffer();
/* 1124 */         stringBuffer.append("create sequence ").append(paramString).append("_id");
/* 1125 */         stringBuffer.append(" start with 1");
/* 1126 */         stringBuffer.append(" increment by 1");
/* 1127 */         stringBuffer.append(" MAXVALUE 9223372036854775807 nocycle ");
/*      */         
/* 1129 */         recordSet.executeUpdate(stringBuffer.toString().toUpperCase(), new Object[0]);
/*      */       } else {
/* 1131 */         this.log.info("sequence[" + str1.toUpperCase() + "] exist");
/*      */       } 
/*      */       
/* 1134 */       this.log.info("trigger：" + str2.toUpperCase());
/* 1135 */       str3 = "select * from user_triggers where trigger_name = '" + str2 + "'";
/* 1136 */       recordSet.execute(str3.toUpperCase());
/* 1137 */       if (!recordSet.next()) {
/* 1138 */         StringBuffer stringBuffer = new StringBuffer("");
/* 1139 */         stringBuffer.append("create or replace trigger ").append(paramString).append("_Trigger");
/* 1140 */         stringBuffer.append(" before insert on ").append(paramString);
/* 1141 */         stringBuffer.append(" for each row ");
/* 1142 */         stringBuffer.append(" begin ");
/* 1143 */         stringBuffer.append(" select ").append(paramString).append("_id.nextval into :new.id from dual; ");
/* 1144 */         stringBuffer.append("end; ");
/*      */         
/* 1146 */         ConnectionPool connectionPool = null;
/* 1147 */         WeaverConnection weaverConnection = null;
/*      */         try {
/* 1149 */           connectionPool = ConnectionPool.getInstance();
/* 1150 */           weaverConnection = connectionPool.getConnection();
/* 1151 */           Statement statement = weaverConnection.createStatement();
/* 1152 */           statement.execute(stringBuffer.toString().toUpperCase());
/* 1153 */           statement.close();
/* 1154 */           connectionPool.returnConnection(weaverConnection);
/* 1155 */         } catch (Exception exception) {
/* 1156 */           this.log.error(exception);
/*      */         } finally {
/* 1158 */           if (connectionPool != null && weaverConnection != null) {
/* 1159 */             connectionPool.returnConnection(weaverConnection);
/*      */           
/*      */           }
/*      */         }
/*      */       
/*      */       }
/*      */       else {
/*      */         
/* 1167 */         this.log.info("trigger[" + str2.toUpperCase() + "] exist");
/*      */       } 
/* 1169 */     } catch (Exception exception) {
/* 1170 */       this.log.error(exception);
/* 1171 */       exception.printStackTrace();
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean accept(File paramFile, String paramString) {
/* 1182 */     return paramString.toLowerCase().endsWith(".xml");
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean createTable(String paramString1, String paramString2) {
/* 1192 */     paramString1 = paramString1.toUpperCase();
/* 1193 */     if (notExistTable(paramString1)) {
/* 1194 */       RecordSet recordSet = new RecordSet();
/* 1195 */       boolean bool = recordSet.executeUpdate(paramString2.toUpperCase(), new Object[0]);
/*      */       
/* 1197 */       this.log.info("create table (" + paramString1 + ") " + (bool ? "succeed" : "fail"));
/* 1198 */       return bool;
/*      */     } 
/* 1200 */     return Boolean.TRUE.booleanValue();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean notExistField(String paramString1, String paramString2) {
/* 1210 */     RecordSet recordSet = new RecordSet();
/*      */     
/* 1212 */     if (paramString1 == null || paramString2 == null) {
/* 1213 */       return Boolean.FALSE.booleanValue();
/*      */     }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/* 1225 */     String str1 = recordSet.getDBType().toLowerCase();
/* 1226 */     String str2 = "";
/* 1227 */     if ("oracle".equals(str1)) {
/* 1228 */       str2 = "SELECT COUNT(1) FROM USER_TAB_COLUMNS WHERE TABLE_NAME=upper(?) AND COLUMN_NAME=upper(?)";
/* 1229 */     } else if ("sqlserver".equals(str1)) {
/* 1230 */       str2 = "select count(1) from syscolumns where id=object_id(lower(?)) and name=lower(?)";
/* 1231 */     } else if ("postgresql".equals(str1)) {
/* 1232 */       str2 = "select count(1) from pg_attribute where attrelid = lower(?)::regclass::oid and attname = lower(?)";
/* 1233 */     } else if ("mysql".equals(str1)) {
/* 1234 */       str2 = "select COUNT(1) from information_schema.columns where lower(table_name)=lower(?) and lower(column_name)=lower(?) and table_schema = database() ";
/*      */     } 
/*      */ 
/*      */     
/* 1238 */     boolean bool = recordSet.executeQuery(str2, new Object[] { paramString1.trim().toUpperCase(), paramString2.trim().toUpperCase() });
/* 1239 */     if (bool && recordSet.next() && Util.getIntValue(recordSet.getString(1)) == 0) {
/* 1240 */       this.log.info("filed (" + paramString1 + "." + paramString2 + ") not exist");
/* 1241 */       return Boolean.TRUE.booleanValue();
/*      */     } 
/* 1243 */     this.log.info("filed (" + paramString1 + "." + paramString2 + ") exist");
/* 1244 */     return Boolean.FALSE.booleanValue();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean writeToDataSourceXMLAddNew(String paramString, Hashtable paramHashtable) {
/* 1255 */     boolean bool = true;
/* 1256 */     String str1 = Util.null2String((String)paramHashtable.get("construct"));
/* 1257 */     String str2 = Util.null2String((String)paramHashtable.get("type"));
/* 1258 */     String str3 = Util.null2String((String)paramHashtable.get("datasourcename"));
/* 1259 */     String str4 = Util.null2s((String)paramHashtable.get("iscluster"), "1");
/* 1260 */     String str5 = Util.null2String((String)paramHashtable.get("typename"));
/* 1261 */     String str6 = Util.null2String((String)paramHashtable.get("url"));
/* 1262 */     String str7 = Util.null2String((String)paramHashtable.get("host"));
/* 1263 */     String str8 = Util.null2String((String)paramHashtable.get("port"));
/* 1264 */     String str9 = Util.null2String((String)paramHashtable.get("dbname"));
/*      */     
/* 1266 */     String str10 = Util.null2String((String)paramHashtable.get("usepool"), "1");
/* 1267 */     String str11 = Util.null2String((String)paramHashtable.get("sortid"), "1");
/* 1268 */     String str12 = Util.null2String((String)paramHashtable.get("minconn"));
/* 1269 */     String str13 = Util.null2String((String)paramHashtable.get("maxconn"));
/*      */     
/* 1271 */     String str14 = Util.null2String((String)paramHashtable.get("iscode"));
/* 1272 */     String str15 = Util.null2String((String)paramHashtable.get("user"));
/* 1273 */     String str16 = Util.null2String((String)paramHashtable.get("password"));
/*      */     try {
/* 1275 */       if (!paramString.trim().equals("")) {
/* 1276 */         if (str3.equals("")) {
/* 1277 */           str3 = paramString;
/*      */         }
/* 1279 */         if (str1.equals("")) {
/* 1280 */           str1 = "weaver.interfaces.datasource.BaseDataSource";
/*      */         }
/* 1282 */         StringBuffer stringBuffer = new StringBuffer();
/* 1283 */         RecordSet recordSet = new RecordSet();
/* 1284 */         String str17 = recordSet.getDBType();
/* 1285 */         String str18 = "select 1 from datasourcesetting where lower(pointid)=?";
/* 1286 */         recordSet.executeQuery(str18, new Object[] { paramString.toLowerCase() });
/* 1287 */         if (recordSet.next()) {
/* 1288 */           stringBuffer.append("update datasourcesetting set ");
/* 1289 */           stringBuffer.append("type='").append(str2).append("',");
/* 1290 */           stringBuffer.append("datasourcename='").append(str3).append("',");
/* 1291 */           stringBuffer.append("iscluster='").append(str4).append("',");
/* 1292 */           stringBuffer.append("typename='").append(str5).append("',");
/* 1293 */           stringBuffer.append("url='").append(str6).append("',");
/* 1294 */           stringBuffer.append("host='").append(str7).append("',");
/* 1295 */           stringBuffer.append("port='").append(str8).append("',");
/* 1296 */           stringBuffer.append("dbname='").append(str9).append("',");
/* 1297 */           stringBuffer.append("username='").append(str15).append("',");
/* 1298 */           stringBuffer.append("password='").append(str16).append("',");
/* 1299 */           stringBuffer.append("minconn='").append(str12).append("',");
/* 1300 */           stringBuffer.append("maxconn='").append(str13).append("',");
/* 1301 */           stringBuffer.append("iscode='").append(str14).append("',");
/* 1302 */           stringBuffer.append("usepool='").append(str10).append("',");
/* 1303 */           stringBuffer.append("sortid='").append(str11).append("'");
/* 1304 */           stringBuffer.append("where ");
/* 1305 */           stringBuffer.append("lower(pointid)='").append(paramString.toLowerCase()).append("' ");
/*      */         } else {
/* 1307 */           stringBuffer.append("insert into datasourcesetting(");
/* 1308 */           stringBuffer.append(" pointid,classpath,type,datasourcename,iscluster,typename,url,host,port,dbname,username,password,minconn,maxconn,iscode,usepool,sortid ");
/* 1309 */           stringBuffer.append(" )values( ");
/* 1310 */           stringBuffer.append("'").append(paramString).append("',");
/* 1311 */           stringBuffer.append("'").append(str1).append("',");
/* 1312 */           stringBuffer.append("'").append(str2).append("',");
/* 1313 */           stringBuffer.append("'").append(str3).append("',");
/* 1314 */           stringBuffer.append("'").append(str4).append("',");
/* 1315 */           stringBuffer.append("'").append(str5).append("',");
/* 1316 */           stringBuffer.append("'").append(str6).append("',");
/* 1317 */           stringBuffer.append("'").append(str7).append("',");
/* 1318 */           stringBuffer.append("'").append(str8).append("',");
/* 1319 */           stringBuffer.append("'").append(str9).append("',");
/* 1320 */           stringBuffer.append("'").append(str15).append("',");
/* 1321 */           stringBuffer.append("'").append(str16).append("',");
/* 1322 */           stringBuffer.append("'").append(str12).append("',");
/* 1323 */           stringBuffer.append("'").append(str13).append("',");
/* 1324 */           stringBuffer.append("'").append(str14).append("', ");
/* 1325 */           stringBuffer.append("'").append(str10).append("',");
/* 1326 */           stringBuffer.append("'").append(str11).append("' ");
/* 1327 */           stringBuffer.append(")");
/*      */         } 
/* 1329 */         recordSet.executeUpdate(stringBuffer.toString(), new Object[0]);
/*      */       } 
/* 1331 */     } catch (Exception exception) {
/* 1332 */       exception.printStackTrace();
/* 1333 */       exception.printStackTrace();
/* 1334 */       bool = false;
/*      */     } 
/*      */     
/* 1337 */     return bool;
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/upgradetool/dbupgrade/upgrade/InitXMLFileToDB.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */