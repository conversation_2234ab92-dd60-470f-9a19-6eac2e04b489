/*     */ package weaver.upgradetool.dbupgrade.upgrade;
/*     */ 
/*     */ import com.alibaba.fastjson.JSON;
/*     */ import com.alibaba.fastjson.JSONArray;
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import java.util.HashMap;
/*     */ import java.util.LinkedHashMap;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.MathUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.upgradetool.dbupgrade.actions.ActionProcess;
/*     */ import weaver.upgradetool.dbupgrade.logger.DBUpgradeLogger;
/*     */ 
/*     */ 
/*     */ 
/*     */ public class DBUpgradeProcess
/*     */ {
/*     */   private static int status;
/*     */   
/*     */   public static int getStatus() {
/*  21 */     return status;
/*     */   }
/*     */   
/*     */   public static void setStatus(int paramInt) {
/*  25 */     status = paramInt;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void initProcess() {
/*  32 */     RecordSet recordSet = new RecordSet();
/*  33 */     status = 0;
/*  34 */     recordSet.executeUpdate("update DBUpgradeAction set status=? where used = ?", new Object[] { "0", "1" });
/*  35 */     recordSet.executeUpdate("update DBUpgradeDetail set status=? where used = ?", new Object[] { "0", "1" });
/*  36 */     recordSet.executeUpdate("update DBUpgradeMain set status=? where used = ?", new Object[] { "0", "1" });
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONObject getProcessMain() {
/*  44 */     RecordSet recordSet = new RecordSet();
/*  45 */     recordSet.executeQuery("select * from DBUpgradeMain where status != ? and used = ? order by sequence", new Object[] { "2", "1" });
/*  46 */     JSONObject jSONObject = new JSONObject();
/*  47 */     if (recordSet.next()) {
/*  48 */       jSONObject.put(recordSet.getString("id"), recordSet.getString("name"));
/*     */     }
/*  50 */     return jSONObject;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONObject getProcessDetail() {
/*  58 */     RecordSet recordSet = new RecordSet();
/*  59 */     recordSet.executeQuery("select detail.id as detailid,detail.name as detailname,main.id as mainid,main.name as mainname from DBUpgradeDetail detail LEFT JOIN DBUpgradeMain main on main.sequence = detail. mainsequence where detail.status != ? and detail.used = ? order by detail.mainsequence,detail.sequence", new Object[] { "2", "1" });
/*  60 */     JSONObject jSONObject = new JSONObject();
/*  61 */     if (recordSet.next()) {
/*  62 */       String str1 = recordSet.getString("detailid");
/*  63 */       String str2 = recordSet.getString("detailname");
/*  64 */       String str3 = recordSet.getString("mainid");
/*  65 */       String str4 = recordSet.getString("mainname");
/*     */       
/*  67 */       jSONObject.put("detailid", str1);
/*  68 */       jSONObject.put("detailname", str2);
/*  69 */       jSONObject.put("mainid", str3);
/*  70 */       jSONObject.put("mainname", str4);
/*     */     } 
/*  72 */     return jSONObject;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONObject getProcessDetailByMain(String paramString) {
/*  82 */     RecordSet recordSet1 = new RecordSet();
/*  83 */     RecordSet recordSet2 = new RecordSet();
/*  84 */     JSONObject jSONObject = new JSONObject();
/*  85 */     String str1 = "";
/*  86 */     String str2 = "";
/*     */     
/*     */     try {
/*  89 */       if (status == 0) {
/*  90 */         recordSet1.executeQuery("select * from DBUpgradeDetail where mainsequence = ? and status = ? and used = ? order by sequence", new Object[] { paramString, "1", "1" });
/*  91 */         while (recordSet1.next()) {
/*  92 */           String str = recordSet1.getString("sequence");
/*  93 */           recordSet2.executeUpdate("update DBUpgradeDetail set status=? where status=? and mainsequence = ? and used = ?", new Object[] { "0", "1", paramString, "1" });
/*  94 */           recordSet2.executeUpdate("update DBUpgradeAction set status=? where status=? and mainsequence = ? and detailsequence= ? and used = ?", new Object[] { "0", "1", paramString, str, "1" });
/*     */         } 
/*     */       } 
/*     */       
/*  98 */       recordSet1.executeQuery("select * from DBUpgradeDetail where mainsequence = ? and used = ? order by sequence", new Object[] { paramString, "1" });
/*  99 */       JSONArray jSONArray = new JSONArray();
/* 100 */       byte b1 = 0;
/* 101 */       byte b2 = 0;
/* 102 */       String str3 = "";
/* 103 */       String str4 = "0";
/* 104 */       String str5 = "success";
/* 105 */       while (recordSet1.next()) {
/* 106 */         b1++;
/* 107 */         JSONObject jSONObject1 = new JSONObject();
/* 108 */         String str6 = recordSet1.getString("id");
/* 109 */         String str7 = recordSet1.getString("name");
/* 110 */         String str8 = recordSet1.getString("sequence");
/* 111 */         String str9 = recordSet1.getString("status");
/* 112 */         if ("2".equals(str9)) {
/* 113 */           b2++;
/*     */         }
/*     */         
/* 116 */         if ("1".equals(str9) || "3".equals(str9)) {
/* 117 */           str3 = str7;
/* 118 */           str4 = str8;
/*     */         } 
/*     */         
/* 121 */         jSONObject1.put("id", str6);
/* 122 */         jSONObject1.put("name", str7);
/* 123 */         jSONObject1.put("sequence", str8);
/* 124 */         jSONObject1.put("status", str9);
/*     */         
/* 126 */         jSONArray.add(jSONObject1);
/*     */       } 
/*     */ 
/*     */       
/* 130 */       if (b2 == b1) {
/* 131 */         str3 = "任务执行完成，请点击“下一步”";
/*     */       } else {
/*     */         
/* 134 */         recordSet1.executeQuery("select 1 from DBUpgradeDetail where mainsequence = ? and status= ? and used = ?  order by sequence", new Object[] { paramString, "3", "1" });
/* 135 */         if (recordSet1.next()) {
/* 136 */           str5 = "failure";
/*     */         } else {
/*     */           
/* 139 */           recordSet2.executeQuery("select 1 from DBUpgradeDetail where mainsequence = ? and status!= ? and used = ?  order by sequence", new Object[] { paramString, "0", "1" });
/* 140 */           if (!recordSet2.next()) {
/* 141 */             str5 = "init";
/*     */           }
/*     */         } 
/*     */       } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 150 */       if (status == 0) {
/* 151 */         str5 = "init";
/*     */       }
/*     */ 
/*     */       
/* 155 */       if (paramString != null && Util.getIntValue(paramString) == 50 && Util.getIntValue(str4) == 30) {
/* 156 */         DBUpgrade dBUpgrade = new DBUpgrade();
/* 157 */         HashMap<String, String> hashMap = dBUpgrade.getExecuteSQLInfo();
/* 158 */         jSONObject = JSONObject.parseObject(JSON.toJSONString(hashMap));
/* 159 */         jSONObject.put("executesql", "1");
/*     */       } else {
/* 161 */         jSONObject.put("executesql", "");
/*     */       } 
/* 163 */       jSONObject.put("detailStatus", jSONArray);
/* 164 */       jSONObject.put("total", "" + b1);
/* 165 */       jSONObject.put("finished", "" + b2);
/* 166 */       jSONObject.put("currentTask", "" + str3);
/* 167 */       jSONObject.put("taskStatus", str5);
/*     */       
/* 169 */       if (b1 != 0) {
/* 170 */         JSONObject jSONObject1 = getActionProcess(paramString, str4);
/* 171 */         String str = jSONObject1.getString("actionPercent");
/* 172 */         str1 = jSONObject1.getString("actionProcessName");
/* 173 */         str2 = jSONObject1.getString("name");
/*     */         
/* 175 */         double d1 = MathUtil.div(Double.parseDouble(str), 100.0D, 2);
/* 176 */         double d2 = b2 + d1;
/*     */         
/* 178 */         double d3 = MathUtil.div(d2 * 100.0D, b1, 2);
/*     */         
/* 180 */         jSONObject.put("percent", "" + d3);
/* 181 */         jSONObject.put("actionProcessName", "" + str1);
/* 182 */         jSONObject.put("actionName", "" + str2);
/*     */       } else {
/* 184 */         jSONObject.put("percent", "100");
/* 185 */         jSONObject.put("actionProcessName", "" + str1);
/* 186 */         jSONObject.put("actionName", "" + str2);
/*     */       } 
/* 188 */     } catch (Exception exception) {
/* 189 */       exception.printStackTrace();
/* 190 */       DBUpgradeLogger.write2File("执行" + exception);
/*     */     } 
/*     */     
/* 193 */     return jSONObject;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public JSONObject getActionProcess(String paramString1, String paramString2) {
/* 202 */     RecordSet recordSet = new RecordSet();
/* 203 */     int i = 0;
/* 204 */     int j = 0;
/* 205 */     JSONObject jSONObject = new JSONObject();
/*     */     
/* 207 */     recordSet.executeQuery("select * from DBUpgradeAction  where mainsequence = ? and detailsequence= ? and status = ? and used = ? order by mainsequence,detailsequence,sequence,id", new Object[] { paramString1, paramString2, "1", "1" });
/*     */     
/* 209 */     if (recordSet.next()) {
/* 210 */       String str1 = recordSet.getString("id");
/* 211 */       String str2 = recordSet.getString("name");
/* 212 */       String str3 = recordSet.getString("sequence");
/*     */       
/* 214 */       jSONObject.put("id", str1);
/* 215 */       jSONObject.put("name", str2);
/* 216 */       jSONObject.put("sequence", str3);
/*     */     } 
/*     */ 
/*     */     
/* 220 */     recordSet.executeQuery("select count(*) as total from DBUpgradeAction  where mainsequence = ? and detailsequence= ? and used = ?", new Object[] { paramString1, paramString2, "1" });
/* 221 */     if (recordSet.next()) {
/* 222 */       i = recordSet.getInt("total");
/*     */     }
/*     */     
/* 225 */     recordSet.executeQuery("select count(*) as finished from DBUpgradeAction  where status = ? and mainsequence = ? and detailsequence= ? and used = ?", new Object[] { "2", paramString1, paramString2, "1" });
/* 226 */     if (recordSet.next()) {
/* 227 */       j = recordSet.getInt("finished");
/*     */     }
/*     */ 
/*     */     
/* 231 */     if (i == 0) {
/* 232 */       jSONObject.put("actionPercent", "0");
/* 233 */       jSONObject.put("actionProcessName", "");
/*     */     } else {
/* 235 */       ActionProcess actionProcess = ActionProcess.getInstance();
/* 236 */       String str1 = actionProcess.getActionProcess();
/* 237 */       String str2 = actionProcess.getActionProcessName();
/* 238 */       double d1 = 0.0D;
/* 239 */       double d2 = 0.0D;
/*     */       try {
/* 241 */         if (str1 != null && !"".equals(str1)) {
/* 242 */           d1 = Double.parseDouble(str1);
/*     */         }
/*     */       }
/* 245 */       catch (Exception exception) {
/* 246 */         d1 = 0.0D;
/*     */       } 
/*     */       
/* 249 */       double d3 = j + MathUtil.div(d1, 100.0D, 2);
/*     */       
/* 251 */       d2 = MathUtil.div(100.0D * d3, i, 2);
/*     */       
/* 253 */       jSONObject.put("actionPercent", Double.valueOf(d2));
/* 254 */       jSONObject.put("actionProcessName", str2);
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 259 */     return jSONObject;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void updateActionStatus(String paramString1, String paramString2, String paramString3, String paramString4) {
/* 267 */     RecordSet recordSet = new RecordSet();
/* 268 */     recordSet.executeUpdate("update DBUpgradeAction set status=? where  mainsequence = ? and detailsequence= ? and used = ?", new Object[] { paramString4, paramString1, paramString2, "1" });
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void updateActionStatus(String paramString1, String paramString2, String paramString3, boolean paramBoolean) {
/* 275 */     RecordSet recordSet = new RecordSet();
/* 276 */     if (paramBoolean) {
/* 277 */       recordSet.executeUpdate("update DBUpgradeAction set status=? where  mainsequence = ? and detailsequence= ? and used = ?", new Object[] { "2", paramString1, paramString2, "1" });
/*     */     } else {
/*     */       
/* 280 */       recordSet.executeUpdate("update DBUpgradeAction set status=? where  mainsequence = ? and detailsequence= ? and used = ?", new Object[] { "3", paramString1, paramString2, "1" });
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void updateDetailStatusByAction(String paramString1, String paramString2) {
/* 288 */     RecordSet recordSet1 = new RecordSet();
/* 289 */     RecordSet recordSet2 = new RecordSet();
/*     */     
/* 291 */     recordSet1.executeQuery("select 1 from  DBUpgradeAction where  mainsequence = ? and detailsequence= ? and status !=? and used = ?", new Object[] { paramString1, paramString2, "2", "1" });
/* 292 */     if (recordSet1.next()) {
/*     */       
/* 294 */       recordSet2.executeQuery("select 1 from  DBUpgradeAction where  mainsequence = ? and detailsequence= ? and status =? and used = ?", new Object[] { paramString1, paramString2, "3", "1" });
/* 295 */       if (recordSet2.next())
/*     */       {
/* 297 */         recordSet2.executeUpdate("update DBUpgradeDetail set status = ? where  mainsequence = ? and sequence = ? and used = ?", new Object[] { "3", paramString1, paramString2, "1" });
/*     */       }
/*     */     } else {
/*     */       
/* 301 */       recordSet1.executeUpdate("update DBUpgradeDetail set status = ? where  mainsequence = ? and sequence = ? and used = ?", new Object[] { "2", paramString1, paramString2, "1" });
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void updateDetailStatus(String paramString1, String paramString2, String paramString3) {
/* 312 */     RecordSet recordSet = new RecordSet();
/* 313 */     recordSet.executeUpdate("update DBUpgradeDetail set status = ? where  mainsequence = ? and sequence = ? and used = ?", new Object[] { paramString3, paramString1, paramString2, "1" });
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void updateMainStatusByDetail(String paramString) {
/* 320 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 322 */     recordSet.executeQuery("select 1 from  DBUpgradeDetail where mainsequence= ? and status != ? and used = ?", new Object[] { paramString, "2", "1" });
/* 323 */     if (!recordSet.next())
/*     */     {
/*     */ 
/*     */       
/* 327 */       recordSet.executeUpdate("update DBUpgradeMain set status=? where sequence = ? and used = ?", new Object[] { "2", paramString, "1" });
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void updateMainStatus(String paramString1, String paramString2) {
/* 337 */     RecordSet recordSet = new RecordSet();
/* 338 */     recordSet.executeQuery("select * from  DBUpgradeMain where sequence = ? and used = ?", new Object[] { paramString1, "1" });
/*     */     
/* 340 */     if (recordSet.next()) {
/* 341 */       String str = recordSet.getString("status");
/* 342 */       if (!str.equals("2")) {
/* 343 */         recordSet.executeUpdate("update DBUpgradeMain set status=? where sequence = ? and used = ?", new Object[] { paramString2, paramString1, "1" });
/*     */       }
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void updateNextMainStatus(String paramString1, String paramString2) {
/* 355 */     RecordSet recordSet = new RecordSet();
/* 356 */     recordSet.executeQuery("select * from DBUpgradeMain where status=? or status=? and sequence > ? and used = ? order by sequence", new Object[] { "1", "0", paramString1, "1" });
/* 357 */     if (recordSet.next()) {
/* 358 */       String str = recordSet.getString("sequence");
/* 359 */       updateMainStatus(str, paramString2);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void updateMainStatus_Run(String paramString1, String paramString2) {
/* 369 */     RecordSet recordSet = new RecordSet();
/* 370 */     recordSet.executeUpdate("update DBUpgradeMain set status=? where sequence = ? ", new Object[] { paramString2, paramString1 });
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void updateNextMainStep(String paramString) {
/* 378 */     RecordSet recordSet = new RecordSet();
/* 379 */     String str = "";
/* 380 */     recordSet.executeQuery("select id from  DBUpgradeMain set status=? and used = ? order by sequence", new Object[] { "0", paramString, "1" });
/* 381 */     if (recordSet.next()) {
/* 382 */       str = recordSet.getString("id");
/*     */     }
/*     */     
/* 385 */     recordSet.executeUpdate("update DBUpgradeMain set status=? where id= ?", new Object[] { "1", str });
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static LinkedHashMap<String, JSONObject> getMainStep() {
/* 394 */     RecordSet recordSet = new RecordSet();
/* 395 */     String str = "select id,name,url,sequence,status,used from DBUpgradeMain order by sequence asc";
/* 396 */     recordSet.execute(str);
/* 397 */     LinkedHashMap<Object, Object> linkedHashMap = new LinkedHashMap<>();
/* 398 */     byte b = 0;
/* 399 */     while (recordSet.next()) {
/* 400 */       b++;
/* 401 */       String str1 = Util.null2String(recordSet.getString("id"));
/* 402 */       String str2 = Util.null2String(recordSet.getString("name"));
/* 403 */       String str3 = Util.null2String(recordSet.getString("url"));
/* 404 */       String str4 = Util.null2String(recordSet.getString("status"));
/* 405 */       String str5 = Util.null2String(recordSet.getString("used"));
/* 406 */       String str6 = Util.null2String(recordSet.getString("sequence"));
/* 407 */       JSONObject jSONObject = new JSONObject();
/* 408 */       jSONObject.put("id", str1);
/* 409 */       jSONObject.put("name", str2);
/* 410 */       jSONObject.put("status", "" + str4);
/* 411 */       jSONObject.put("used", "" + str5);
/* 412 */       jSONObject.put("url", str3);
/* 413 */       jSONObject.put("step", "" + b);
/* 414 */       jSONObject.put("stepname", "步骤" + ToolUtil.convert(b) + "：" + str2);
/* 415 */       jSONObject.put("sequence", str6);
/* 416 */       linkedHashMap.put("" + b, jSONObject);
/*     */     } 
/* 418 */     return (LinkedHashMap)linkedHashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static JSONObject getNextMainStep() {
/* 427 */     RecordSet recordSet = new RecordSet();
/* 428 */     String str = "select id,name,url,sequence from DBUpgradeMain where status = ? or status = ? order by sequence";
/* 429 */     recordSet.executeQuery(str, new Object[] { "1", "3" });
/* 430 */     JSONObject jSONObject = new JSONObject();
/* 431 */     if (recordSet.next()) {
/* 432 */       String str1 = Util.null2String(recordSet.getString("id"));
/* 433 */       String str2 = Util.null2String(recordSet.getString("name"));
/* 434 */       String str3 = Util.null2String(recordSet.getString("url"));
/* 435 */       String str4 = Util.null2String(recordSet.getString("status"));
/* 436 */       String str5 = Util.null2String(recordSet.getString("used"));
/* 437 */       String str6 = Util.null2String(recordSet.getString("sequence"));
/* 438 */       jSONObject.put("id", str1);
/* 439 */       jSONObject.put("name", str2);
/* 440 */       jSONObject.put("status", "" + str4);
/* 441 */       jSONObject.put("used", "" + str5);
/* 442 */       jSONObject.put("url", str3);
/* 443 */       jSONObject.put("sequence", str6);
/*     */     } else {
/*     */       
/* 446 */       str = "select id,name,url,sequence from DBUpgradeMain where status = ? order by sequence desc";
/* 447 */       recordSet.executeQuery(str, new Object[] { "2" });
/* 448 */       if (recordSet.next()) {
/* 449 */         String str1 = Util.null2String(recordSet.getString("id"));
/* 450 */         String str2 = Util.null2String(recordSet.getString("name"));
/* 451 */         String str3 = Util.null2String(recordSet.getString("url"));
/* 452 */         String str4 = Util.null2String(recordSet.getString("status"));
/* 453 */         String str5 = Util.null2String(recordSet.getString("used"));
/* 454 */         String str6 = Util.null2String(recordSet.getString("sequence"));
/* 455 */         jSONObject.put("id", str1);
/* 456 */         jSONObject.put("name", str2);
/* 457 */         jSONObject.put("status", "" + str4);
/* 458 */         jSONObject.put("used", "" + str5);
/* 459 */         jSONObject.put("url", str3);
/* 460 */         jSONObject.put("sequence", str6);
/*     */       } else {
/* 462 */         str = "select id,name,url,sequence from DBUpgradeMain where status != ? order by sequence";
/* 463 */         recordSet.executeQuery(str, new Object[] { "2" });
/* 464 */         if (recordSet.next()) {
/* 465 */           String str1 = Util.null2String(recordSet.getString("id"));
/* 466 */           String str2 = Util.null2String(recordSet.getString("name"));
/* 467 */           String str3 = Util.null2String(recordSet.getString("url"));
/* 468 */           String str4 = Util.null2String(recordSet.getString("status"));
/* 469 */           String str5 = Util.null2String(recordSet.getString("used"));
/* 470 */           String str6 = Util.null2String(recordSet.getString("sequence"));
/* 471 */           jSONObject.put("id", str1);
/* 472 */           jSONObject.put("name", str2);
/* 473 */           jSONObject.put("status", "" + str4);
/* 474 */           jSONObject.put("used", "" + str5);
/* 475 */           jSONObject.put("url", str3);
/* 476 */           jSONObject.put("sequence", str6);
/*     */         } 
/*     */       } 
/*     */     } 
/*     */     
/* 481 */     return jSONObject;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/upgradetool/dbupgrade/upgrade/DBUpgradeProcess.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */