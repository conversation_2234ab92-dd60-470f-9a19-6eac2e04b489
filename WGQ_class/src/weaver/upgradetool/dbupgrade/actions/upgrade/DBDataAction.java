/*     */ package weaver.upgradetool.dbupgrade.actions.upgrade;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import java.io.File;
/*     */ import java.util.HashMap;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.docs.category.SecCategoryCustomSearchComInfo;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.Util;
/*     */ import weaver.upgradetool.dbupgrade.actions.ActionInterface;
/*     */ import weaver.upgradetool.dbupgrade.logger.DBUpgradeLogger;
/*     */ import weaver.upgradetool.dbupgrade.upgrade.DBUtil;
/*     */ import weaver.upgradetool.dbupgrade.upgrade.PropUtil;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class DBDataAction
/*     */   implements ActionInterface
/*     */ {
/*     */   public String execute(HashMap<String, String> paramHashMap) {
/*  27 */     startAction(null);
/*  28 */     DBUpgradeLogger.write2File("开始执行代码数据调整的操作");
/*  29 */     JSONObject jSONObject = new JSONObject();
/*     */     
/*  31 */     boolean bool = true;
/*  32 */     RecordSet recordSet = new RecordSet();
/*     */ 
/*     */     
/*  35 */     DBUtil dBUtil = new DBUtil();
/*  36 */     String str1 = Util.null2String(dBUtil.getBeforeLicense());
/*  37 */     if ("".equals(str1)) {
/*  38 */       DBUpgradeLogger.write2File("获取版本号失败");
/*  39 */       jSONObject.put("status", "failure");
/*  40 */       endAction(null);
/*  41 */       return jSONObject.toJSONString();
/*     */     } 
/*  43 */     String str2 = "8";
/*  44 */     String str3 = "0";
/*  45 */     if (str1.indexOf("KB") > -1) {
/*  46 */       str3 = Util.null2String(str1.substring(str1.indexOf("KB") + 2), "0");
/*  47 */     } else if (str1.indexOf(".") > 0) {
/*  48 */       str3 = Util.null2String(str1.substring(0, str1.indexOf(".")), "0");
/*     */     } 
/*  50 */     DBUpgradeLogger.write2File("之前版本：" + str3);
/*     */ 
/*     */     
/*  53 */     if (str2.compareTo(str3) > 0) {
/*  54 */       DBUpgradeLogger.write2File("解决流程强制归档的开关");
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/*  65 */       recordSet.executeUpdate("update workflow_base set isoverrb=1 where id in(select workflowid from workflow_function_manage  where retract=1 or retract=2   group by workflowid)", new Object[0]);
/*     */     } 
/*  67 */     DBUpgradeLogger.write2File("版本" + str3);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  84 */     DBUpgradeLogger.write2File("初始化文档目录");
/*  85 */     recordSet.executeQuery("select id from DocSecCategory group by id", new Object[0]);
/*  86 */     SecCategoryCustomSearchComInfo secCategoryCustomSearchComInfo = new SecCategoryCustomSearchComInfo();
/*  87 */     while (recordSet.next()) {
/*  88 */       int i = Util.getIntValue(recordSet.getString("id"), -1);
/*  89 */       if (i == -1) {
/*     */         continue;
/*     */       }
/*     */       try {
/*  93 */         secCategoryCustomSearchComInfo.checkDefaultCustomSearch(i);
/*  94 */       } catch (Exception exception) {
/*  95 */         DBUpgradeLogger.write2File("文档目录初始化失败ID：" + i);
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     try {
/* 103 */       recordSet.executeQuery("select * from HtmlLabelInfo,cus_formfield where fieldlable=indexid AND languageid =7  and scope='HrmCustomFieldByInfoType'", new Object[0]);
/* 104 */     } catch (Exception exception) {
/* 105 */       recordSet.executeQuery("update cus_formfield set fieldlable=''  where scope='HrmCustomFieldByInfoType'", new Object[0]);
/*     */     } 
/*     */ 
/*     */     
/*     */     try {
/* 110 */       String str = GCONST.getRootPath() + "WEB-INF" + File.separatorChar + "prop" + File.separatorChar + "weaver_initmodule.properties";
/* 111 */       PropUtil propUtil = PropUtil.getInstance(new String[] { str });
/* 112 */       recordSet.executeQuery("select 1 from CptShareDetail", new Object[0]);
/* 113 */       if (recordSet.next()) {
/* 114 */         propUtil.put("cpt", "1");
/*     */       }
/* 116 */       propUtil.store();
/* 117 */     } catch (Exception exception) {
/* 118 */       DBUpgradeLogger.writeErrorLog2File("修改字段逻辑状态失败：" + exception.toString());
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/*     */     try {
/* 124 */       String str = GCONST.getRootPath() + "WEB-INF" + File.separatorChar + "prop" + File.separatorChar + "formmode.properties";
/* 125 */       PropUtil propUtil = PropUtil.getInstance(new String[] { str });
/* 126 */       propUtil.put("INITSTATUS", "y");
/* 127 */       propUtil.store();
/*     */     }
/* 129 */     catch (Exception exception) {}
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 143 */     if (bool) {
/* 144 */       jSONObject.put("status", "success");
/*     */     } else {
/* 146 */       jSONObject.put("status", "failure");
/*     */     } 
/* 148 */     endAction(null);
/* 149 */     return jSONObject.toJSONString();
/*     */   }
/*     */   
/*     */   public void startAction(HashMap<String, String> paramHashMap) {}
/*     */   
/*     */   public void endAction(HashMap<String, String> paramHashMap) {}
/*     */   
/*     */   public void setActionProcess(String paramString) {}
/*     */   
/*     */   public void setActionProcessName(String paramString) {}
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/upgradetool/dbupgrade/actions/upgrade/DBDataAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */