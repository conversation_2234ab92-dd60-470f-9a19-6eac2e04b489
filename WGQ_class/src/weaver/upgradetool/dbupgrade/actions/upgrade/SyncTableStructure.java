/*      */ package weaver.upgradetool.dbupgrade.actions.upgrade;
/*      */ import com.alibaba.fastjson.JSONObject;
/*      */ import java.io.File;
/*      */ import java.sql.Connection;
/*      */ import java.sql.ResultSet;
/*      */ import java.sql.Statement;
/*      */ import java.util.ArrayList;
/*      */ import java.util.Arrays;
/*      */ import java.util.HashMap;
/*      */ import java.util.Iterator;
/*      */ import java.util.LinkedList;
/*      */ import java.util.List;
/*      */ import java.util.Map;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.general.Util;
/*      */ import weaver.upgradetool.dbupgrade.logger.DBUpgradeLogger;
/*      */ import weaver.upgradetool.dbupgrade.upgrade.PropUtil;
/*      */ import weaver.upgradetool.dbupgrade.upgrade.UpgradeRecordSet;
/*      */ 
/*      */ public class SyncTableStructure implements ActionInterface {
/*   21 */   private DBUtil dbutil = new DBUtil();
/*   22 */   PropUtil propUtil = PropUtil.getInstance(new String[] { PropUtil.MIGRATION });
/*      */ 
/*      */   
/*      */   public String execute(HashMap<String, String> paramHashMap) {
/*   26 */     JSONObject jSONObject = new JSONObject();
/*   27 */     startAction(null);
/*   28 */     boolean bool = true;
/*      */     
/*   30 */     DBUpgradeLogger.write2File("====SyncTableStructure 开始对系统表单进行特殊处理");
/*   31 */     bool = specialHandleForBill();
/*   32 */     if (!bool) {
/*   33 */       jSONObject.put("status", "failure");
/*   34 */       DBUpgradeLogger.write2File("error====SyncTableStructure 对系统表单进行特殊处理出现异常");
/*   35 */       return jSONObject.toJSONString();
/*      */     } 
/*   37 */     DBUpgradeLogger.write2File("====SyncTableStructure 系统表单进行特殊处理结束");
/*      */ 
/*      */     
/*   40 */     DBUpgradeLogger.write2File("====SyncTableStructure 开始同步表结构");
/*   41 */     bool = syncTableStructure();
/*   42 */     if (!bool) {
/*   43 */       jSONObject.put("status", "failure");
/*   44 */       DBUpgradeLogger.write2File("error====SyncTableStructure 同步表结构出现异常");
/*   45 */       return jSONObject.toJSONString();
/*      */     } 
/*   47 */     DBUpgradeLogger.write2File("====SyncTableStructure 同步表结构结束");
/*      */ 
/*      */     
/*   50 */     DBUpgradeLogger.write2File("====SyncTableStructure 开始删除E7中无需同步的表");
/*   51 */     bool = dropTable();
/*   52 */     if (!bool) {
/*   53 */       jSONObject.put("status", "failure");
/*   54 */       DBUpgradeLogger.write2File("error====SyncTableStructure 开始删除E7中无需同步的表出现异常");
/*   55 */       return jSONObject.toJSONString();
/*      */     } 
/*   57 */     DBUpgradeLogger.write2File("====SyncTableStructure 删除E7中无需同步的表结束");
/*      */ 
/*      */ 
/*      */     
/*   61 */     DBUpgradeLogger.write2File("====SyncTableStructure 开始删除源库中的所有视图");
/*   62 */     bool = dropView();
/*   63 */     if (!bool) {
/*   64 */       jSONObject.put("status", "failure");
/*   65 */       DBUpgradeLogger.write2File("error====SyncTableStructure 删除源库中的所有视图出现异常");
/*   66 */       return jSONObject.toJSONString();
/*      */     } 
/*   68 */     DBUpgradeLogger.write2File("====SyncTableStructure 删除源库中的所有视图结束");
/*      */ 
/*      */     
/*   71 */     DBUpgradeLogger.write2File("====SyncTableStructure 开始修改表名大小");
/*   72 */     bool = SyncTableName();
/*   73 */     if (!bool) {
/*   74 */       jSONObject.put("status", "failure");
/*   75 */       DBUpgradeLogger.write2File("error====SyncTableStructure 修改表名大小出现异常");
/*   76 */       return jSONObject.toJSONString();
/*      */     } 
/*   78 */     DBUpgradeLogger.write2File("====SyncTableStructure 修改表名大小结束");
/*      */ 
/*      */     
/*   81 */     DBUpgradeLogger.write2File("====SyncTableStructure 开始检查所有需要同步的表索引在源库中是否有冲突数据");
/*   82 */     bool = checkIndex();
/*   83 */     if (!bool) {
/*   84 */       jSONObject.put("status", "failure");
/*   85 */       DBUpgradeLogger.write2File("error====SyncTableStructure 检查所有需要同步的表索引在源库中是否有冲突数据出现异常");
/*   86 */       return jSONObject.toJSONString();
/*      */     } 
/*   88 */     DBUpgradeLogger.write2File("====SyncTableStructure 检查所有需要同步的表索引在源库中是否有冲突数据结束");
/*      */     
/*   90 */     setActionProcess("100");
/*   91 */     jSONObject.put("status", "success");
/*   92 */     endAction(null);
/*   93 */     return jSONObject.toJSONString();
/*      */   }
/*      */ 
/*      */   
/*      */   public void startAction(HashMap<String, String> paramHashMap) {
/*   98 */     ActionProcess.getInstance().setActionProcess("0");
/*   99 */     ActionProcess.getInstance().setActionProcess("startAction");
/*  100 */     DBUpgradeLogger.write2File("=====================startAction:SyncTableStructure=====================");
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void endAction(HashMap<String, String> paramHashMap) {
/*  107 */     ActionProcess.getInstance().setActionProcess("100");
/*  108 */     ActionProcess.getInstance().setActionProcess("endAction");
/*  109 */     DBUpgradeLogger.write2File("=====================endAction:SyncTableStructure=====================");
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public void setActionProcess(String paramString) {
/*  115 */     ActionProcess.getInstance().setActionProcess(paramString);
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public void setActionProcessName(String paramString) {
/*  121 */     ActionProcess.getInstance().setActionProcessName(paramString);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean dropTable() {
/*  128 */     boolean bool = true;
/*  129 */     String str = this.dbutil.getDBtype();
/*  130 */     List<String> list = Arrays.asList(this.propUtil.getValues("e7_droptables").split(","));
/*  131 */     Connection connection = this.dbutil.getSourceConnection();
/*  132 */     ResultSet resultSet1 = null;
/*      */     
/*  134 */     Statement statement1 = null;
/*  135 */     Statement statement2 = null;
/*  136 */     ResultSet resultSet2 = null;
/*  137 */     byte b = 1;
/*      */     try {
/*  139 */       statement1 = connection.createStatement();
/*  140 */       statement2 = connection.createStatement();
/*  141 */       for (String str1 : list) {
/*  142 */         if ("oracle".equalsIgnoreCase(str)) {
/*  143 */           resultSet1 = statement1.executeQuery("SELECT TABLE_NAME FROM USER_TABLES WHERE TABLE_NAME ='" + str1 + "'");
/*  144 */         } else if ("sqlserver".equalsIgnoreCase(str)) {
/*  145 */           resultSet1 = statement1.executeQuery("SELECT NAME AS TABLE_NAME FROM SYSOBJECTS WHERE XTYPE='U' AND NAME ='" + str1 + "'");
/*      */         } 
/*  147 */         if (resultSet1.next()) {
/*  148 */           DBUpgradeLogger.write2File("====开始删除E7中无需同步的表:" + str1);
/*  149 */           if ("oracle".equalsIgnoreCase(str)) {
/*  150 */             statement1.executeUpdate("DROP TABLE " + str1 + " CASCADE CONSTRAINTS");
/*  151 */           } else if ("sqlserver".equalsIgnoreCase(str)) {
/*      */             try {
/*  153 */               statement1.executeUpdate("DROP TABLE " + str1);
/*  154 */             } catch (Exception exception) {
/*      */               try {
/*  156 */                 if ("SqlServer".equalsIgnoreCase(str)) {
/*      */                   
/*  158 */                   resultSet2 = statement2.executeQuery("SELECT NAME FROM SYSOBJECTS WHERE TYPE='D' AND PARENT_OBJ IN(SELECT ID FROM SYSOBJECTS WHERE NAME='" + str1 + "') ");
/*  159 */                   while (resultSet2.next()) {
/*  160 */                     String str2 = resultSet2.getString("NAME");
/*  161 */                     DBUpgradeLogger.write2File("无法删除表,开始删除表相关依赖:" + str2);
/*  162 */                     statement1.executeUpdate("ALTER TABLE " + str1 + " DROP CONSTRAINT " + str2);
/*      */                   } 
/*      */                   
/*  165 */                   resultSet2 = statement2.executeQuery("  SELECT NAME FROM SYS.INDEXES  WHERE IS_UNIQUE !=1 AND IS_PRIMARY_KEY!=1  AND OBJECT_ID = OBJECT_ID('" + str1 + "')");
/*  166 */                   while (resultSet2.next()) {
/*  167 */                     String str2 = resultSet2.getString("NAME");
/*  168 */                     DBUpgradeLogger.write2File("无法删除表,开始删除表相关依赖:" + str2);
/*  169 */                     statement1.executeUpdate("drop Index " + str1 + "." + str2);
/*      */                   } 
/*  171 */                   statement1.executeUpdate("DROP TABLE " + str1);
/*      */                 } 
/*  173 */               } catch (Exception exception1) {
/*  174 */                 DBUpgradeLogger.write2File("error===经过两次尝试仍然无法删除表" + str1 + ",请联系泛微工作人员进行排查问题,错误信息:" + exception1.toString() + "||" + exception1.getStackTrace());
/*  175 */                 bool = false;
/*  176 */                 return bool;
/*      */               } finally {
/*  178 */                 if (resultSet2 != null) {
/*  179 */                   resultSet2.close();
/*      */                 }
/*  181 */                 if (statement2 != null) {
/*  182 */                   statement2.close();
/*      */                 }
/*      */               } 
/*      */             } 
/*      */           } 
/*      */         } 
/*  188 */         double d = MathUtil.div((b * 15), list.size(), 1) + 70.0D;
/*  189 */         setActionProcess(d + "");
/*  190 */         b++;
/*      */       } 
/*  192 */       if ("oracle".equalsIgnoreCase(str)) {
/*  193 */         resultSet1 = statement1.executeQuery(" SELECT TABLE_NAME FROM USER_TABLES");
/*  194 */       } else if ("sqlserver".equalsIgnoreCase(str)) {
/*  195 */         resultSet1 = statement1.executeQuery("SELECT NAME AS TABLE_NAME FROM SYSOBJECTS WHERE XTYPE='U'");
/*      */       } 
/*  197 */       ArrayList<String> arrayList = new ArrayList();
/*  198 */       while (resultSet1.next()) {
/*  199 */         arrayList.add(resultSet1.getString("TABLE_NAME").trim().toUpperCase());
/*      */       }
/*  201 */       List<String> list1 = Arrays.asList(this.propUtil.getValues("synctables").split(","));
/*      */       
/*  203 */       arrayList.removeAll(list1);
/*  204 */       if (arrayList.size() > 0) {
/*  205 */         for (String str1 : arrayList) {
/*  206 */           DBUpgradeLogger.write2File("error====发现源库中表未被删除:" + str1 + " 请检查是否需要手动删除");
/*      */         }
/*  208 */       } else if (arrayList.size() == 0) {
/*  209 */         DBUpgradeLogger.write2File("==== SyncTableStructure 删除E7中无需同步的表已完成,经过校验,删除过程无异常情况");
/*      */       }
/*      */     
/*  212 */     } catch (Exception exception) {
/*  213 */       DBUpgradeLogger.write2File("error====SyncTableStructure 删除E7中无需同步的表出现异常,异常信息为:" + exception.toString());
/*  214 */       bool = false;
/*      */     } finally {
/*      */       try {
/*  217 */         if (resultSet1 != null) {
/*  218 */           resultSet1.close();
/*      */         }
/*  220 */         if (statement1 != null) {
/*  221 */           statement1.close();
/*      */         }
/*  223 */         if (connection != null) {
/*  224 */           connection.close();
/*      */         }
/*  226 */       } catch (Exception exception) {
/*  227 */         DBUpgradeLogger.write2File("error===SyncTableStructure 关闭资源异常:异常信息:" + exception.toString());
/*  228 */         exception.printStackTrace();
/*      */       } 
/*      */     } 
/*  231 */     return bool;
/*      */   }
/*      */ 
/*      */   
/*      */   public boolean specialHandleForBill() {
/*  236 */     List<String> list = Arrays.asList(this.propUtil.getValues("synctables").split(","));
/*  237 */     boolean bool = true;
/*  238 */     long l = System.currentTimeMillis();
/*  239 */     String str = this.dbutil.getDBtype();
/*      */     
/*  241 */     UpgradeRecordSet upgradeRecordSet = new UpgradeRecordSet();
/*  242 */     Connection connection = this.dbutil.getSourceConnection();
/*  243 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  244 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*      */ 
/*      */     
/*  247 */     Statement statement = null;
/*      */     
/*  249 */     ResultSet resultSet = null;
/*      */     
/*  251 */     ArrayList<String> arrayList = new ArrayList();
/*      */     
/*      */     try {
/*  254 */       byte b = 1;
/*  255 */       ArrayList<String> arrayList1 = new ArrayList();
/*  256 */       ArrayList<String> arrayList2 = new ArrayList();
/*      */       
/*  258 */       String str1 = "";
/*  259 */       if ("Oracle".equalsIgnoreCase(str)) {
/*  260 */         str1 = "SELECT COLUMN_NAME,DATA_TYPE,DATA_LENGTH,DATA_PRECISION,DATA_SCALE,NULLABLE,DATA_DEFAULT FROM USER_TAB_COLUMNS WHERE TABLE_NAME=";
/*      */       }
/*  262 */       else if ("postgresql".equalsIgnoreCase(str)) {
/*      */         
/*  264 */         str1 = "select COLUMN_NAME,DATA_TYPE,CHARACTER_OCTET_LENGTH as DATA_LENGTH,numeric_precision as  DATA_PRECISION,numeric_scale as DATA_SCALE ,is_nullable as NULLABLE,column_default as DATA_DEFAULT from Information_schema.columns WHERE table_name = ";
/*      */       }
/*  266 */       else if ("SqlServer".equalsIgnoreCase(str)) {
/*  267 */         str1 = "SELECT SC.NAME AS COLUMN_NAME,ST.NAME AS DATA_TYPE,SC.LENGTH AS DATA_LENGTH,SC.XPREC AS DATA_PRECISION,SC.XSCALE AS DATA_SCALE,SC.ISNULLABLE AS NULLABLE,CM.TEXT AS DATA_DEFAULT FROM SYSCOLUMNS SC LEFT JOIN SYSTYPES ST ON SC.XUSERTYPE=ST.XUSERTYPE LEFT JOIN SYSCOMMENTS CM ON SC.CDEFAULT=CM.ID WHERE SC.ID=Object_Id(?)";
/*      */       } 
/*      */       
/*  270 */       statement = connection.createStatement();
/*  271 */       for (String str2 : list) {
/*  272 */         str2 = str2.toUpperCase();
/*  273 */         if (!str2.startsWith("BILL_")) {
/*  274 */           b++;
/*      */           continue;
/*      */         } 
/*  277 */         arrayList.clear();
/*  278 */         arrayList1.clear();
/*  279 */         arrayList2.clear();
/*  280 */         hashMap1.clear();
/*  281 */         hashMap2.clear();
/*      */         
/*  283 */         if ("Oracle".equalsIgnoreCase(str)) {
/*  284 */           upgradeRecordSet.executeQuery(str1 + "'" + str2 + "'", new Object[0]);
/*  285 */         } else if ("SqlServer".equalsIgnoreCase(str)) {
/*  286 */           upgradeRecordSet.executeQuery(str1.replace("?", "'" + str2 + "'"), new Object[0]);
/*      */         }
/*  288 */         else if ("postgresql".equalsIgnoreCase(str)) {
/*  289 */           upgradeRecordSet.executeQuery(str1 + "lower('" + str2 + "')", new Object[0]);
/*  290 */         }  DBUpgradeLogger.write2File("操作表名:" + str2);
/*      */         
/*  292 */         while (upgradeRecordSet.next()) {
/*  293 */           String str5 = upgradeRecordSet.getString("COLUMN_NAME");
/*  294 */           arrayList1.add(str5.toUpperCase().trim());
/*      */         } 
/*      */         
/*  297 */         if ("Oracle".equalsIgnoreCase(str)) {
/*  298 */           resultSet = statement.executeQuery(str1 + "'" + str2 + "'");
/*  299 */         } else if ("SqlServer".equalsIgnoreCase(str)) {
/*  300 */           resultSet = statement.executeQuery(str1.replace("?", "'" + str2 + "'"));
/*      */         } 
/*      */         
/*  303 */         while (resultSet.next()) {
/*  304 */           String str5 = resultSet.getString("COLUMN_NAME");
/*  305 */           arrayList2.add(str5.toUpperCase().trim());
/*      */         } 
/*      */         
/*  308 */         arrayList.addAll(arrayList1);
/*  309 */         arrayList.retainAll(arrayList2);
/*      */         
/*  311 */         arrayList2.removeAll(arrayList);
/*      */ 
/*      */         
/*  314 */         String str3 = "ALTER TABLE " + str2 + " ADD ";
/*  315 */         String str4 = "";
/*  316 */         for (String str5 : arrayList2) {
/*  317 */           if ("Oracle".equalsIgnoreCase(str)) {
/*  318 */             str4 = "SELECT DATA_TYPE,DATA_LENGTH,DATA_PRECISION,DATA_SCALE FROM USER_TAB_COLUMNS WHERE COLUMN_NAME='" + str5.toUpperCase() + "' AND TABLE_NAME ='" + str2 + "'";
/*  319 */           } else if ("SqlServer".equalsIgnoreCase(str)) {
/*  320 */             str4 = "SELECT ST.NAME AS DATA_TYPE,SC.LENGTH AS DATA_LENGTH,SC.XPREC AS DATA_PRECISION,SC.XSCALE AS DATA_SCALE FROM SYSCOLUMNS SC,SYSTYPES ST WHERE SC.XUSERTYPE=ST.XUSERTYPE AND SC.NAME='" + str5.toUpperCase() + "' AND SC.ID IN(SELECT ID FROM SYSOBJECTS WHERE XTYPE='U' and name='" + str2 + "')";
/*      */           } 
/*  322 */           resultSet = statement.executeQuery(str4);
/*      */           
/*  324 */           resultSet.next();
/*  325 */           String str6 = resultSet.getString(1);
/*  326 */           if ("Oracle".equalsIgnoreCase(str)) {
/*  327 */             if (str6.equalsIgnoreCase("VARCHAR2") || str6.equalsIgnoreCase("NVARCHAR2") || str6.equalsIgnoreCase("CHAR") || str6.equalsIgnoreCase("RAW")) {
/*  328 */               str6 = str6 + "(" + resultSet.getString("DATA_LENGTH") + ")";
/*  329 */             } else if (str6.equalsIgnoreCase("NUMBER")) {
/*  330 */               String str7 = Util.null2String(resultSet.getString("DATA_PRECISION"));
/*  331 */               if (str7 != "") {
/*  332 */                 str6 = str6 + "(" + str7 + "," + resultSet.getString("DATA_SCALE") + ")";
/*      */               }
/*  334 */             } else if (str6.equalsIgnoreCase("FLOAT")) {
/*  335 */               str6 = str6 + "(" + resultSet.getString("DATA_PRECISION") + ")";
/*      */             } 
/*  337 */           } else if ("SqlServer".equalsIgnoreCase(str)) {
/*  338 */             if (str6.equalsIgnoreCase("CHAR") || str6.equalsIgnoreCase("VARBINARY") || str6.equalsIgnoreCase("VARCHAR")) {
/*  339 */               str6 = str6 + "(" + resultSet.getString("DATA_LENGTH") + ")";
/*  340 */             } else if (str6.equalsIgnoreCase("NCHAR") || str6.equalsIgnoreCase("NVARCHAR")) {
/*  341 */               String str7 = resultSet.getString("DATA_LENGTH");
/*  342 */               if (str7.equals("-1")) {
/*  343 */                 str6 = str6 + "(MAX)";
/*      */               } else {
/*  345 */                 str6 = str6 + "(" + (Integer.parseInt(str7) / 2) + ")";
/*      */               } 
/*  347 */             } else if (str6.equalsIgnoreCase("DECIMAL") || str6.equalsIgnoreCase("NUMERIC")) {
/*  348 */               String str7 = Util.null2String(resultSet.getString("DATA_SCALE"));
/*  349 */               if (str7.equals("0") || str7.equals("")) {
/*  350 */                 str6 = str6 + "(" + resultSet.getString("DATA_PRECISION") + ")";
/*      */               } else {
/*  352 */                 str6 = str6 + "(" + resultSet.getString("DATA_PRECISION") + "," + resultSet.getString("DATA_SCALE") + ")";
/*      */               } 
/*      */             } 
/*      */           } 
/*  356 */           DBUpgradeLogger.write2File("开始在E9库中添加:" + str3 + str5 + " " + str6);
/*  357 */           upgradeRecordSet.executeUpdate(str3 + str5 + " " + str6, new Object[0]);
/*      */         } 
/*  359 */         double d = MathUtil.div((b * 10), list.size(), 1);
/*  360 */         setActionProcess(d + "");
/*  361 */         b++;
/*      */       } 
/*  363 */       long l1 = System.currentTimeMillis();
/*  364 */       DBUpgradeLogger.write2File("程序运行时间：" + (l1 - l) + "ms");
/*      */     }
/*  366 */     catch (Exception exception) {
/*  367 */       bool = false;
/*  368 */       exception.printStackTrace();
/*  369 */       DBUpgradeLogger.write2File("error===SyncTableStructure specialHandleForBill 同步表结构异常:异常信息:" + exception.toString() + "///" + exception.getStackTrace());
/*      */     } finally {
/*      */       try {
/*  372 */         if (resultSet != null) {
/*  373 */           resultSet.close();
/*      */         }
/*  375 */         if (statement != null) {
/*  376 */           statement.close();
/*      */         }
/*  378 */         if (connection != null) {
/*  379 */           connection.close();
/*      */         }
/*  381 */       } catch (Exception exception) {
/*  382 */         bool = false;
/*  383 */         DBUpgradeLogger.write2File("error===SyncTableStructure specialHandleForBill 关闭资源异常:异常信息:" + exception.toString() + "///" + exception.getStackTrace());
/*      */       } 
/*      */     } 
/*  386 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean syncTableStructure() {
/*  392 */     List<String> list = Arrays.asList(this.propUtil.getValues("synctables").split(","));
/*  393 */     boolean bool = true;
/*  394 */     long l = System.currentTimeMillis();
/*  395 */     String str = this.dbutil.getDBtype();
/*      */     
/*  397 */     UpgradeRecordSet upgradeRecordSet = new UpgradeRecordSet();
/*  398 */     Connection connection = this.dbutil.getSourceConnection();
/*  399 */     RecordSet recordSet = new RecordSet();
/*  400 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  401 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*      */ 
/*      */ 
/*      */     
/*  405 */     Statement statement1 = null;
/*  406 */     Statement statement2 = null;
/*      */     
/*  408 */     ResultSet resultSet1 = null;
/*  409 */     ResultSet resultSet2 = null;
/*      */     
/*  411 */     ArrayList<String> arrayList = new ArrayList();
/*      */     
/*      */     try {
/*  414 */       byte b = 1;
/*  415 */       ArrayList<String> arrayList1 = new ArrayList();
/*  416 */       ArrayList<String> arrayList2 = new ArrayList();
/*      */       
/*  418 */       String str1 = "";
/*  419 */       if ("Oracle".equalsIgnoreCase(str)) {
/*  420 */         str1 = "SELECT COLUMN_NAME,DATA_TYPE,DATA_LENGTH,DATA_PRECISION,DATA_SCALE,NULLABLE,DATA_DEFAULT FROM USER_TAB_COLUMNS WHERE TABLE_NAME=";
/*  421 */       } else if ("SqlServer".equalsIgnoreCase(str)) {
/*  422 */         str1 = "SELECT SC.NAME AS COLUMN_NAME,ST.NAME AS DATA_TYPE,SC.LENGTH AS DATA_LENGTH,SC.XPREC AS DATA_PRECISION,SC.XSCALE AS DATA_SCALE,SC.ISNULLABLE AS NULLABLE,CM.TEXT AS DATA_DEFAULT FROM SYSCOLUMNS SC LEFT JOIN SYSTYPES ST ON SC.XUSERTYPE=ST.XUSERTYPE LEFT JOIN SYSCOMMENTS CM ON SC.CDEFAULT=CM.ID WHERE SC.ID=Object_Id(?)";
/*      */       }
/*  424 */       else if ("postgresql".equalsIgnoreCase(str)) {
/*      */         
/*  426 */         str1 = " select COLUMN_NAME,DATA_TYPE,CHARACTER_OCTET_LENGTH as DATA_LENGTH,numeric_precision as  DATA_PRECISION,numeric_scale as DATA_SCALE ,is_nullable as NULLABLE,column_default as DATA_DEFAULT from Information_schema.columns WHERE table_name = ";
/*      */       } 
/*      */       
/*  429 */       statement1 = connection.createStatement();
/*  430 */       for (String str2 : list) {
/*  431 */         str2 = str2.toUpperCase();
/*  432 */         arrayList.clear();
/*  433 */         arrayList1.clear();
/*  434 */         arrayList2.clear();
/*  435 */         hashMap1.clear();
/*  436 */         hashMap2.clear();
/*      */         
/*  438 */         if ("SYSDETACHDETAIL".equalsIgnoreCase(str2) && "Oracle".equalsIgnoreCase(str)) {
/*  439 */           resultSet1 = statement1.executeQuery(" SELECT COLUMN_NAME FROM USER_TAB_COLUMNS WHERE TABLE_NAME ='SYSDETACHDETAIL' AND UPPER(COLUMN_NAME)='TYPE'");
/*  440 */           if (resultSet1.next()) {
/*  441 */             String str6 = "alter table SYSDETACHDETAIL drop column \"type\"";
/*  442 */             statement1.execute(str6);
/*  443 */             DBUpgradeLogger.write2File("表名:" + str2 + "需要进行特殊处理:" + str6);
/*      */           } 
/*      */         } 
/*      */         
/*  447 */         if ("Oracle".equalsIgnoreCase(str)) {
/*  448 */           upgradeRecordSet.executeQuery(str1 + "'" + str2 + "'", new Object[0]);
/*  449 */         } else if ("SqlServer".equalsIgnoreCase(str)) {
/*  450 */           upgradeRecordSet.executeQuery(str1.replace("?", "'" + str2 + "'"), new Object[0]);
/*      */         }
/*  452 */         else if ("postgresql".equalsIgnoreCase(str)) {
/*      */           
/*  454 */           upgradeRecordSet.executeQuery(str1 + "lower('" + str2 + "')", new Object[0]);
/*      */         } 
/*  456 */         DBUpgradeLogger.write2File("操作表名:" + str2);
/*      */         
/*  458 */         while (upgradeRecordSet.next()) {
/*  459 */           String str6 = upgradeRecordSet.getString("COLUMN_NAME");
/*  460 */           arrayList1.add(str6.toUpperCase().trim());
/*      */         } 
/*      */         
/*  463 */         if ("Oracle".equalsIgnoreCase(str)) {
/*  464 */           resultSet1 = statement1.executeQuery(str1 + "'" + str2 + "'");
/*  465 */         } else if ("SqlServer".equalsIgnoreCase(str)) {
/*  466 */           resultSet1 = statement1.executeQuery(str1.replace("?", "'" + str2 + "'"));
/*      */         }
/*  468 */         else if ("postgresql".equalsIgnoreCase(str)) {
/*      */           
/*  470 */           upgradeRecordSet.executeQuery(str1 + "lower('" + str2 + "')", new Object[0]);
/*      */         } 
/*  472 */         while (resultSet1.next()) {
/*  473 */           String str6 = resultSet1.getString("COLUMN_NAME");
/*  474 */           arrayList2.add(str6.toUpperCase().trim());
/*      */         } 
/*  476 */         if (arrayList2.size() == 0) {
/*  477 */           DBUpgradeLogger.write2File("====检测到表:" + str2 + " 在E7中不存在,不进行同步操作");
/*  478 */           b++;
/*      */           
/*      */           continue;
/*      */         } 
/*  482 */         arrayList.addAll(arrayList1);
/*  483 */         arrayList.retainAll(arrayList2);
/*      */         
/*  485 */         arrayList1.removeAll(arrayList);
/*  486 */         arrayList2.removeAll(arrayList);
/*      */ 
/*      */         
/*  489 */         String str3 = "ALTER TABLE " + str2 + " ADD ";
/*  490 */         String str4 = "";
/*      */         
/*  492 */         connection.setAutoCommit(false);
/*  493 */         for (String str6 : arrayList1) {
/*  494 */           if ("Oracle".equalsIgnoreCase(str)) {
/*  495 */             str4 = "SELECT DATA_TYPE,DATA_LENGTH,DATA_PRECISION,DATA_SCALE FROM USER_TAB_COLUMNS WHERE COLUMN_NAME='" + str6.toUpperCase() + "' AND TABLE_NAME ='" + str2 + "'";
/*  496 */           } else if ("SqlServer".equalsIgnoreCase(str)) {
/*  497 */             str4 = "SELECT ST.NAME AS DATA_TYPE,SC.LENGTH AS DATA_LENGTH,SC.XPREC AS DATA_PRECISION,SC.XSCALE AS DATA_SCALE FROM SYSCOLUMNS SC,SYSTYPES ST WHERE SC.XUSERTYPE=ST.XUSERTYPE AND SC.NAME='" + str6.toUpperCase() + "' AND SC.ID IN(SELECT ID FROM SYSOBJECTS WHERE XTYPE='U' and name='" + str2 + "')";
/*      */           }
/*  499 */           else if ("postgresql".equalsIgnoreCase(str)) {
/*      */             
/*  501 */             str4 = "   select COLUMN_NAME,DATA_TYPE,CHARACTER_OCTET_LENGTH as DATA_LENGTH,numeric_precision as  DATA_PRECISION,numeric_scale as DATA_SCALE  from Information_schema.columns WHERE table_name = lower('" + str2 + "')and column_name= lower('" + str6 + "')";
/*      */           } 
/*  503 */           recordSet.executeQuery(str4, new Object[0]);
/*      */           
/*  505 */           recordSet.next();
/*  506 */           String str7 = recordSet.getString(1);
/*  507 */           if ("Oracle".equalsIgnoreCase(str)) {
/*  508 */             if (str7.equalsIgnoreCase("VARCHAR2") || str7.equalsIgnoreCase("NVARCHAR2") || str7.equalsIgnoreCase("CHAR") || str7.equalsIgnoreCase("RAW")) {
/*  509 */               str7 = str7 + "(" + recordSet.getString("DATA_LENGTH") + ")";
/*  510 */             } else if (str7.equalsIgnoreCase("NUMBER")) {
/*  511 */               String str8 = Util.null2String(recordSet.getString("DATA_PRECISION"));
/*  512 */               if (str8 != "") {
/*  513 */                 str7 = str7 + "(" + str8 + "," + recordSet.getString("DATA_SCALE") + ")";
/*      */               }
/*  515 */             } else if (str7.equalsIgnoreCase("FLOAT")) {
/*  516 */               str7 = str7 + "(" + recordSet.getString("DATA_PRECISION") + ")";
/*      */             } 
/*  518 */           } else if ("SqlServer".equalsIgnoreCase(str)) {
/*  519 */             if (str7.equalsIgnoreCase("CHAR") || str7.equalsIgnoreCase("VARBINARY") || str7.equalsIgnoreCase("VARCHAR")) {
/*  520 */               str7 = str7 + "(" + recordSet.getString("DATA_LENGTH") + ")";
/*  521 */             } else if (str7.equalsIgnoreCase("NCHAR") || str7.equalsIgnoreCase("NVARCHAR")) {
/*  522 */               String str8 = recordSet.getString("DATA_LENGTH");
/*  523 */               if (str8.equals("-1")) {
/*  524 */                 str7 = str7 + "(MAX)";
/*      */               } else {
/*  526 */                 str7 = str7 + "(" + (Integer.parseInt(str8) / 2) + ")";
/*      */               } 
/*  528 */             } else if (str7.equalsIgnoreCase("DECIMAL") || str7.equalsIgnoreCase("NUMERIC")) {
/*  529 */               String str8 = Util.null2String(recordSet.getString("DATA_SCALE"));
/*  530 */               if (str8.equals("0") || str8.equals("")) {
/*  531 */                 str7 = str7 + "(" + recordSet.getString("DATA_PRECISION") + ")";
/*      */               } else {
/*  533 */                 str7 = str7 + "(" + recordSet.getString("DATA_PRECISION") + "," + recordSet.getString("DATA_SCALE") + ")";
/*      */               } 
/*      */             } 
/*      */           } 
/*  537 */           DBUpgradeLogger.write2File("开始添加:" + str3 + str6 + " " + str7);
/*  538 */           statement1.executeUpdate(str3 + str6 + " " + str7);
/*      */         } 
/*  540 */         connection.commit();
/*      */ 
/*      */         
/*  543 */         String str5 = "ALTER TABLE " + str2 + " DROP COLUMN ";
/*  544 */         connection.setAutoCommit(false);
/*  545 */         statement2 = connection.createStatement();
/*  546 */         for (String str6 : arrayList2) {
/*      */           
/*  548 */           DBUpgradeLogger.write2File("开始删除:" + str5 + str6);
/*      */           try {
/*  550 */             statement1.executeUpdate(str5 + str6);
/*  551 */           } catch (Exception exception) {
/*      */             try {
/*  553 */               if ("SqlServer".equalsIgnoreCase(str)) {
/*  554 */                 resultSet2 = statement2.executeQuery("SELECT NAME FROM SYSOBJECTS WHERE TYPE='D' AND PARENT_OBJ IN(SELECT ID FROM SYSOBJECTS WHERE NAME='" + str2 + "') ");
/*  555 */                 while (resultSet2.next()) {
/*  556 */                   String str7 = resultSet2.getString("NAME");
/*  557 */                   DBUpgradeLogger.write2File("无法删除字段,开始删除相关依赖:" + str7);
/*  558 */                   statement1.executeUpdate("ALTER TABLE " + str2 + " DROP CONSTRAINT " + str7);
/*      */                 } 
/*  560 */                 resultSet2 = statement2.executeQuery("  SELECT NAME FROM SYS.INDEXES  WHERE IS_UNIQUE !=1 AND IS_PRIMARY_KEY!=1  AND OBJECT_ID = OBJECT_ID('" + str2 + "')");
/*  561 */                 while (resultSet2.next()) {
/*  562 */                   String str7 = Util.null2String(resultSet2.getString("NAME"));
/*  563 */                   if ("null".equalsIgnoreCase(str7) || "".equals(str7)) {
/*  564 */                     DBUpgradeLogger.write2File("无法删除字段,删除主键时，主键名为NULL"); continue;
/*      */                   } 
/*  566 */                   DBUpgradeLogger.write2File("无法删除字段,开始删除相关依赖:" + str7);
/*  567 */                   statement1.executeUpdate("drop Index " + str2 + "." + str7);
/*      */                 } 
/*      */ 
/*      */                 
/*  571 */                 statement1.executeUpdate(str5 + str6);
/*      */               } 
/*  573 */             } catch (Exception exception1) {
/*  574 */               DBUpgradeLogger.write2File("error===经过两次尝试仍然无法删除字段" + str6 + ",请联系泛微工作人员进行排查问题,错误信息:" + exception1.toString() + "||" + exception1.getStackTrace());
/*  575 */               bool = false;
/*  576 */               return bool;
/*      */             } finally {
/*  578 */               if (resultSet2 != null) {
/*  579 */                 resultSet2.close();
/*      */               }
/*  581 */               if (statement2 != null) {
/*  582 */                 statement2.close();
/*      */               }
/*      */             } 
/*      */           } 
/*      */         } 
/*  587 */         connection.commit();
/*      */ 
/*      */         
/*  590 */         if ("Oracle".equalsIgnoreCase(str)) {
/*  591 */           upgradeRecordSet.executeQuery(str1 + "'" + str2 + "'", new Object[0]);
/*  592 */         } else if ("SqlServer".equalsIgnoreCase(str)) {
/*  593 */           upgradeRecordSet.executeQuery(str1.replace("?", "'" + str2 + "'"), new Object[0]);
/*      */         } 
/*  595 */         while (upgradeRecordSet.next()) {
/*  596 */           String str6 = Util.null2String(upgradeRecordSet.getString("COLUMN_NAME")).toUpperCase().trim();
/*  597 */           Map<String, String> map = getColumnMap(upgradeRecordSet.getString("DATA_TYPE"), upgradeRecordSet.getString("DATA_LENGTH"), upgradeRecordSet.getString("DATA_PRECISION"), upgradeRecordSet.getString("DATA_SCALE"), upgradeRecordSet.getString("DATA_DEFAULT"), upgradeRecordSet.getString("NULLABLE"));
/*  598 */           hashMap2.put(str6, map);
/*      */         } 
/*  600 */         if ("Oracle".equalsIgnoreCase(str)) {
/*  601 */           resultSet1 = statement1.executeQuery(str1 + "'" + str2 + "'");
/*  602 */         } else if ("SqlServer".equalsIgnoreCase(str)) {
/*  603 */           resultSet1 = statement1.executeQuery(str1.replace("?", "'" + str2 + "'"));
/*      */         } 
/*      */         
/*  606 */         while (resultSet1.next()) {
/*  607 */           String str6 = Util.null2String(resultSet1.getString("COLUMN_NAME")).toUpperCase().trim();
/*  608 */           Map<String, String> map = getColumnMap(resultSet1.getString("DATA_TYPE"), resultSet1.getString("DATA_LENGTH"), resultSet1.getString("DATA_PRECISION"), resultSet1.getString("DATA_SCALE"), resultSet1.getString("DATA_DEFAULT"), resultSet1.getString("NULLABLE"));
/*  609 */           hashMap1.put(str6, map);
/*      */         } 
/*  611 */         connection.setAutoCommit(true);
/*  612 */         bool = checkColumns(str2, (Map)hashMap1, (Map)hashMap2, connection);
/*  613 */         if (!bool) {
/*  614 */           DBUpgradeLogger.write2File("checkColumns 方法在检查时出现异常");
/*  615 */           return bool;
/*      */         } 
/*  617 */         double d = MathUtil.div((b * 60), list.size(), 1) + 10.0D;
/*  618 */         setActionProcess(d + "");
/*  619 */         b++;
/*      */       } 
/*  621 */       long l1 = System.currentTimeMillis();
/*  622 */       DBUpgradeLogger.write2File("程序运行时间：" + (l1 - l) + "ms");
/*      */     }
/*  624 */     catch (Exception exception) {
/*  625 */       bool = false;
/*  626 */       exception.printStackTrace();
/*  627 */       DBUpgradeLogger.write2File("error===SyncTableStructure 同步表结构异常:异常信息:" + exception.toString() + "///" + exception.getStackTrace());
/*      */     } finally {
/*      */       try {
/*  630 */         if (resultSet1 != null) {
/*  631 */           resultSet1.close();
/*      */         }
/*  633 */         if (statement1 != null) {
/*  634 */           statement1.close();
/*      */         }
/*  636 */         if (connection != null) {
/*  637 */           connection.close();
/*      */         }
/*  639 */       } catch (Exception exception) {
/*  640 */         bool = false;
/*  641 */         DBUpgradeLogger.write2File("error===SyncTableStructure 关闭资源异常:异常信息:" + exception.toString() + "///" + exception.getStackTrace());
/*      */       } 
/*      */     } 
/*  644 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean checkColumns(String paramString, Map<String, Map<String, String>> paramMap1, Map<String, Map<String, String>> paramMap2, Connection paramConnection) {
/*  655 */     boolean bool = true;
/*      */     
/*  657 */     UpgradeRecordSet upgradeRecordSet = new UpgradeRecordSet();
/*      */     
/*  659 */     Statement statement = null;
/*  660 */     DBUpgradeLogger.write2File("####对表:" + paramString + "做字段长度,ISNULL检查");
/*      */     
/*  662 */     if (paramConnection == null) {
/*  663 */       DBUpgradeLogger.write2File("####数据库连接为空，再次请求连接");
/*  664 */       paramConnection = this.dbutil.getSourceConnection();
/*      */     } 
/*  666 */     String str = this.dbutil.getDBtype();
/*  667 */     ResultSet resultSet = null;
/*      */     try {
/*  669 */       statement = paramConnection.createStatement();
/*      */       
/*  671 */       if (paramString.equalsIgnoreCase("WORKPLANSHAREDETAIL")) {
/*  672 */         statement.executeUpdate("UPDATE WORKPLANSHAREDETAIL SET SHARETYPE='1' WHERE SHARETYPE IS NULL");
/*  673 */         DBUpgradeLogger.write2File("对WORKPLANSHAREDETAIL进行特殊处理:UPDATE WORKPLANSHAREDETAIL SET SHARETYPE='1' WHERE SHARETYPE IS NULL");
/*  674 */         DBUpgradeLogger.writeErrorLog2File("对WORKPLANSHAREDETAIL进行特殊处理:UPDATE WORKPLANSHAREDETAIL SET SHARETYPE='1' WHERE SHARETYPE IS NULL");
/*      */       } 
/*  676 */       DBUpgradeLogger.write2File("=======开始对表" + paramString + "所有字段进行校验");
/*  677 */       for (String str1 : paramMap2.keySet()) {
/*  678 */         DBUpgradeLogger.write2File("开始校验字段 " + str1);
/*  679 */         int i = Integer.parseInt((String)((Map)paramMap2.get(str1)).get("DATA_LENGTH"));
/*  680 */         int j = Integer.parseInt((String)((Map)paramMap1.get(str1)).get("DATA_LENGTH"));
/*  681 */         String str2 = ((String)((Map)paramMap2.get(str1)).get("DATA_TYPE")).toUpperCase();
/*  682 */         String str3 = ((String)((Map)paramMap1.get(str1)).get("DATA_TYPE")).toUpperCase();
/*  683 */         String str4 = ((String)((Map)paramMap2.get(str1)).get("NULLABLE")).toUpperCase();
/*  684 */         String str5 = ((String)((Map)paramMap1.get(str1)).get("NULLABLE")).toUpperCase();
/*  685 */         String str6 = Util.null2String((String)((Map)paramMap2.get(str1)).get("DATA_DEFAULT"));
/*  686 */         if (str2.contains("CHAR") && str2.contains("N")) {
/*  687 */           i /= 2;
/*      */         }
/*      */         
/*  690 */         if (str3.contains("CHAR") && str3.contains("N")) {
/*  691 */           j /= 2;
/*      */         }
/*      */ 
/*      */         
/*  695 */         if (str2.contains("CHAR") && str3.contains("CHAR") && i < j && str3.equalsIgnoreCase(str2)) {
/*  696 */           DBUpgradeLogger.write2File("####发现表:" + paramString + "字段长度不一致,字段:" + str1 + "在源环境中长度为:" + j + ",E9环境中长度为:" + i);
/*  697 */           if ("oracle".equalsIgnoreCase(str)) {
/*  698 */             upgradeRecordSet.executeSql("ALTER TABLE " + paramString + " MODIFY " + str1 + " " + str3 + "(" + j + ")");
/*  699 */             DBUpgradeLogger.writeErrorLog2File("####发现表:" + paramString + "字段长度不一致,字段:" + str1 + "在源环境中长度为:" + j + ",E9环境中长度为:" + i + " 执行语句:" + "ALTER TABLE " + paramString + " MODIFY " + str1 + " " + str3 + "(" + j + ")");
/*      */           }
/*  701 */           else if ("sqlserver".equalsIgnoreCase(str)) {
/*      */ 
/*      */ 
/*      */             
/*  705 */             upgradeRecordSet.executeSql("ALTER TABLE " + paramString + " ALTER COLUMN " + str1 + " " + str3 + "(" + j + ")");
/*      */             
/*  707 */             DBUpgradeLogger.writeErrorLog2File("####发现表:" + paramString + "字段长度不一致,字段:" + str1 + "在源环境中长度为:" + j + ",E9环境中长度为:" + i + " 执行语句:" + "ALTER TABLE " + paramString + " ALTER COLUMN " + str1 + " " + str3 + "(" + j + ")");
/*      */           }
/*      */         
/*  710 */         } else if (!str2.equalsIgnoreCase(str3)) {
/*  711 */           DBUpgradeLogger.write2File("####检测到表:" + paramString + "字段:" + str1 + "和目标环境中字段类型不一致,E9中为:" + str2 + ",E7中为:" + str3);
/*  712 */           DBUpgradeLogger.writeErrorLog2File("####检测到表:" + paramString + "字段:" + str1 + "和目标环境中字段类型不一致,E9中为:" + str2 + ",E7中为:" + str3);
/*  713 */           DBUpgradeLogger.writeErrorLog2File("####发现表:" + str + "修改字段类型" + str + " " + str + "开始执行");
/*  714 */           if ("ORACLE".equalsIgnoreCase(str.trim())) {
/*  715 */             DBUpgradeLogger.writeErrorLog2File("####发现表:" + paramString + "修改字段类型" + str1 + " " + str2 + "开始执行");
/*  716 */             statement.executeUpdate("alter table " + paramString + " disable all triggers");
/*  717 */             if (str2.equalsIgnoreCase("CLOB")) {
/*      */ 
/*      */               
/*  720 */               DBUpgradeLogger.writeErrorLog2File("####发现表:" + paramString + "修改字段类型" + str1 + " " + str2 + "开始执行");
/*      */               try {
/*  722 */                 statement.execute("alter table " + paramString + " drop column nametemp1");
/*  723 */               } catch (Exception exception) {}
/*      */ 
/*      */               
/*      */               try {
/*  727 */                 statement.execute("alter table " + paramString + " add nametemp1 clob");
/*  728 */                 statement.execute(" update " + paramString + " set nametemp1=to_char(" + str1 + ")");
/*  729 */                 statement.execute("alter table " + paramString + " drop column " + str1);
/*  730 */                 statement.execute("alter table " + paramString + " rename column nametemp1 to " + str1);
/*  731 */               } catch (Exception exception) {
/*  732 */                 DBUpgradeLogger.writeErrorLog2File("####发现表:" + paramString + "修改字段类型" + str1 + " " + str3 + "转" + str2 + "执行出现错误" + exception);
/*  733 */                 DBUpgradeLogger.write2File("####发现表:" + paramString + "修改字段类型" + str1 + " " + str3 + "转" + str2 + "执行出现错误" + exception);
/*  734 */                 exception.printStackTrace();
/*  735 */                 writeErrortableToFile(paramString);
/*  736 */                 return true;
/*      */               }
/*      */             
/*  739 */             } else if ((str2.equalsIgnoreCase("VARCHAR2") || str2.equalsIgnoreCase("VARCHAR") || str2.equalsIgnoreCase("CHAR")) && (str3.equalsIgnoreCase("NUMBER") || str3
/*  740 */               .equalsIgnoreCase("INT") || str3.equalsIgnoreCase("DATE") || str3.equalsIgnoreCase("FLOAT") || str3
/*  741 */               .equalsIgnoreCase("INTEGER") || str3.equalsIgnoreCase("CHAR") || str3.equalsIgnoreCase("DECIMAL"))) {
/*  742 */               DBUpgradeLogger.writeErrorLog2File("####发现表:" + paramString + "修改字段类型" + str1 + " " + str2 + "开始执行");
/*  743 */               int k = 500;
/*  744 */               if (i > 2) {
/*  745 */                 k = i;
/*      */               }
/*  747 */               if (j > i && j > 1) {
/*  748 */                 k = j;
/*      */               }
/*      */               try {
/*  751 */                 statement.execute("alter table " + paramString + " drop column nametemp1");
/*  752 */               } catch (Exception exception) {}
/*      */ 
/*      */               
/*      */               try {
/*  756 */                 statement.execute("alter table " + paramString + " add nametemp1 " + str2 + "(" + k + ")");
/*  757 */                 statement.execute("update " + paramString + " set nametemp1=to_char(" + str1 + ")");
/*  758 */                 statement.execute("alter table " + paramString + " drop column " + str1);
/*  759 */                 statement.execute("alter table " + paramString + " rename column nametemp1 to " + str1);
/*  760 */                 upgradeRecordSet.executeSql("ALTER TABLE " + paramString + " MODIFY " + str1 + " " + str2 + "(" + k + ")");
/*  761 */               } catch (Exception exception) {
/*  762 */                 DBUpgradeLogger.writeErrorLog2File("####发现表:" + paramString + "修改字段类型" + str1 + " " + str3 + "转" + str2 + "执行出现错误" + exception);
/*  763 */                 DBUpgradeLogger.write2File("####发现表:" + paramString + "修改字段类型" + str1 + " " + str3 + "转" + str2 + "执行出现错误" + exception);
/*  764 */                 exception.printStackTrace();
/*  765 */                 writeErrortableToFile(paramString);
/*  766 */                 return true;
/*      */               } 
/*  768 */               DBUpgradeLogger.writeErrorLog2File("####发现表:" + paramString + "修改字段类型" + str1 + " " + str2 + "执行成功");
/*      */             }
/*  770 */             else if ((str2.equalsIgnoreCase("NUMBER") || str2.equalsIgnoreCase("INT") || str2
/*  771 */               .equalsIgnoreCase("INTEGER")) && (str3
/*  772 */               .equalsIgnoreCase("CHAR") || str3.equalsIgnoreCase("VARCHAR") || str3.equalsIgnoreCase("VARCHAR2"))) {
/*  773 */               DBUpgradeLogger.writeErrorLog2File("####发现表:" + paramString + "修改字段类型" + str1 + " " + str2 + "开始执行");
/*      */               try {
/*  775 */                 statement.execute("alter table " + paramString + " drop column nametemp1");
/*  776 */               } catch (Exception exception) {}
/*      */ 
/*      */               
/*      */               try {
/*  780 */                 statement.execute("alter table " + paramString + " add nametemp1 " + str2 + " ");
/*  781 */                 statement.execute("update " + paramString + " set nametemp1=" + str1 + "");
/*  782 */                 statement.execute("alter table " + paramString + " drop column " + str1);
/*  783 */                 statement.execute("alter table " + paramString + " rename column nametemp1 to " + str1);
/*  784 */               } catch (Exception exception) {
/*  785 */                 DBUpgradeLogger.writeErrorLog2File("####发现表:" + paramString + "修改字段类型" + str1 + " " + str3 + "转" + str2 + "执行出现错误" + exception);
/*  786 */                 DBUpgradeLogger.write2File("####发现表:" + paramString + "修改字段类型" + str1 + " " + str3 + "转" + str2 + "执行出现错误" + exception);
/*  787 */                 exception.printStackTrace();
/*  788 */                 writeErrortableToFile(paramString);
/*  789 */                 return true;
/*      */               } 
/*  791 */               DBUpgradeLogger.writeErrorLog2File("####发现表:" + paramString + "修改字段类型" + str1 + " " + str2 + "执行成功");
/*  792 */             } else if ((str2.equalsIgnoreCase("VARCHAR2") || str2.equalsIgnoreCase("VARCHAR")) && str3
/*  793 */               .equalsIgnoreCase("CLOB")) {
/*  794 */               DBUpgradeLogger.writeErrorLog2File("####发现表:" + paramString + "修改字段类型" + str1 + " " + str2 + "开始执行");
/*  795 */               if (checkClobTableByTablename(paramString)) {
/*      */                 try {
/*  797 */                   statement.execute("alter table " + paramString + " drop column nametemp1");
/*  798 */                 } catch (Exception exception) {}
/*      */ 
/*      */                 
/*      */                 try {
/*  802 */                   statement.execute("alter table " + paramString + " add nametemp1 " + str2 + "(4000)");
/*  803 */                   statement.execute(" update " + paramString + " set nametemp1=" + str1 + "");
/*  804 */                   statement.execute("alter table " + paramString + " drop column " + str1);
/*  805 */                   statement.execute("alter table " + paramString + " rename column nametemp1 to " + str1);
/*  806 */                 } catch (Exception exception) {
/*  807 */                   DBUpgradeLogger.writeErrorLog2File("####发现表:" + paramString + "修改字段类型" + str1 + " " + str3 + "转" + str2 + "执行出现错误" + exception);
/*  808 */                   DBUpgradeLogger.write2File("####发现表:" + paramString + "修改字段类型" + str1 + " " + str3 + "转" + str2 + "执行出现错误" + exception);
/*  809 */                   exception.printStackTrace();
/*  810 */                   writeErrortableToFile(paramString);
/*  811 */                   return true;
/*      */                 } 
/*  813 */                 DBUpgradeLogger.writeErrorLog2File("####发现表:" + paramString + "修改字段类型" + str1 + " " + str2 + "执行成功");
/*      */               } 
/*  815 */             }  if ((str2.equalsIgnoreCase("float") || str2.equalsIgnoreCase("INT") || str2.equalsIgnoreCase("INTEGER") || str2
/*  816 */               .equalsIgnoreCase("DOUBLE") || str2.equalsIgnoreCase("NUMBER") || str2.equalsIgnoreCase("Decimal") || str2
/*  817 */               .equalsIgnoreCase("BINARY_FLOAT") || str2.equalsIgnoreCase("BINARY_DOUBLE")) && (str3
/*  818 */               .equalsIgnoreCase("float") || str3.equalsIgnoreCase("INT") || str3.equalsIgnoreCase("INTEGER") || str3
/*  819 */               .equalsIgnoreCase("DOUBLE") || str3.equalsIgnoreCase("NUMBER") || str3.equalsIgnoreCase("Decimal") || str3
/*  820 */               .equalsIgnoreCase("BINARY_FLOAT") || str3.equalsIgnoreCase("BINARY_DOUBLE"))) {
/*      */               
/*  822 */               DBUpgradeLogger.writeErrorLog2File("####发现表:" + paramString + "修改字段类型" + str1 + " " + str2 + "开始执行");
/*      */               try {
/*  824 */                 statement.execute("alter table " + paramString + " modify(" + str1 + "  " + str2 + ")");
/*  825 */               } catch (Exception exception) {
/*  826 */                 DBUpgradeLogger.writeErrorLog2File("####发现表:" + paramString + "修改字段类型" + str1 + " " + str3 + "转" + str2 + "执行出现错误" + exception);
/*  827 */                 DBUpgradeLogger.write2File("####发现表:" + paramString + "修改字段类型" + str1 + " " + str3 + "转" + str2 + "执行出现错误" + exception);
/*  828 */                 exception.printStackTrace();
/*  829 */                 writeErrortableToFile(paramString);
/*  830 */                 return true;
/*      */               } 
/*  832 */               DBUpgradeLogger.writeErrorLog2File("####发现表:" + paramString + "修改字段类型" + str1 + " " + str2 + "执行成功");
/*      */             } 
/*  834 */           } else if ("sqlserver".equalsIgnoreCase(str)) {
/*  835 */             if (str2.equalsIgnoreCase("TEXT") || str2.equalsIgnoreCase("NTEXT")) {
/*  836 */               DBUpgradeLogger.writeErrorLog2File("####发现表:" + paramString + "修改字段类型" + str1 + " " + str2 + "E7:开始执行");
/*      */               try {
/*  838 */                 if (str3.equalsIgnoreCase("NTEXT")) {
/*  839 */                   statement.execute("alter table " + paramString + " alter column " + str1 + " VARCHAR(MAX)");
/*  840 */                   statement.execute("alter table " + paramString + " alter column " + str1 + " text");
/*  841 */                 } else if (str3.equalsIgnoreCase("TEXT")) {
/*  842 */                   statement.execute("alter table " + paramString + " alter column " + str1 + " NVARCHAR(MAX)");
/*  843 */                   statement.execute("alter table " + paramString + " alter column " + str1 + " NTEXT");
/*      */                 } else {
/*  845 */                   statement.execute("alter table " + paramString + " alter column " + str1 + " varchar(MAX)");
/*  846 */                   statement.execute("alter table " + paramString + " alter column " + str1 + " " + str2);
/*      */                 } 
/*  848 */               } catch (Exception exception) {
/*  849 */                 DBUpgradeLogger.writeErrorLog2File("####发现表:" + paramString + "修改字段类型" + str1 + " " + str3 + "转" + str2 + "执行出现错误" + exception);
/*  850 */                 DBUpgradeLogger.write2File("####发现表:" + paramString + "修改字段类型" + str1 + " " + str3 + "转" + str2 + "执行出现错误" + exception);
/*  851 */                 exception.printStackTrace();
/*  852 */                 writeErrortableToFile(paramString);
/*  853 */                 return true;
/*      */               }
/*      */             
/*  856 */             } else if (str2.toUpperCase().contains("CHAR")) {
/*  857 */               int k = 200;
/*  858 */               if (i > 2) {
/*  859 */                 k = i;
/*      */               }
/*  861 */               if (j > i && j > 2) {
/*  862 */                 k = j;
/*      */               }
/*      */               try {
/*  865 */                 if (str3.equalsIgnoreCase("TEXT") || str3.equalsIgnoreCase("NTEXT")) {
/*  866 */                   statement.execute("alter table " + paramString + " alter column " + str1 + " varchar(8000)");
/*  867 */                   upgradeRecordSet.execute("alter table " + paramString + " alter column " + str1 + "  " + str2 + "(8000)");
/*  868 */                   i = 8000;
/*      */                 } else {
/*  870 */                   statement.execute("alter table " + paramString + " alter column " + str1 + " " + str2 + "(" + k + ") ");
/*      */ 
/*      */                 
/*      */                 }
/*      */ 
/*      */               
/*      */               }
/*  877 */               catch (Exception exception) {
/*      */                 try {
/*  879 */                   DBUpgradeLogger.write2File("====修改E7E8中原有的字段:alter table " + paramString + " alter column " + str1 + "" + str2 + "(" + k + ")出错，开始进行特殊处理");
/*  880 */                   Statement statement1 = paramConnection.createStatement();
/*  881 */                   Statement statement2 = paramConnection.createStatement();
/*  882 */                   ResultSet resultSet1 = statement1.executeQuery("SELECT NAME FROM SYSOBJECTS WHERE TYPE='D' AND PARENT_OBJ IN(SELECT ID FROM SYSOBJECTS WHERE NAME='" + paramString + "') ");
/*  883 */                   while (resultSet1.next()) {
/*  884 */                     String str7 = resultSet1.getString("NAME");
/*  885 */                     DBUpgradeLogger.write2File("无法修改字段类型,开始删除相关依赖:" + str7);
/*  886 */                     statement2.executeUpdate("ALTER TABLE " + paramString + " DROP CONSTRAINT " + str7);
/*      */                   } 
/*      */                   
/*  889 */                   ResultSet resultSet2 = statement1.executeQuery("  SELECT NAME FROM SYS.INDEXES  WHERE IS_UNIQUE !=1 AND IS_PRIMARY_KEY!=1  AND OBJECT_ID = OBJECT_ID('" + paramString + "')");
/*  890 */                   while (resultSet2.next()) {
/*  891 */                     String str7 = Util.null2String(resultSet2.getString("NAME"));
/*  892 */                     if ("null".equalsIgnoreCase(str7) || "".equals(str7)) {
/*  893 */                       DBUpgradeLogger.write2File("无法修改字段,修改主键时，主键名为NULL"); continue;
/*      */                     } 
/*  895 */                     DBUpgradeLogger.write2File("无法修改字段,开始删除相关依赖:" + str7);
/*  896 */                     statement2.executeUpdate("drop Index " + paramString + "." + str7);
/*      */                   } 
/*      */ 
/*      */ 
/*      */ 
/*      */                   
/*  902 */                   statement.execute("alter table " + paramString + " alter column " + str1 + " " + str2 + "(" + k + ") ");
/*  903 */                   str3 = str2;
/*  904 */                 } catch (Exception exception1) {
/*  905 */                   DBUpgradeLogger.writeErrorLog2File("####发现表:" + paramString + "修改字段类型" + str1 + " " + str3 + "转" + str2 + "执行出现错误" + exception1);
/*  906 */                   DBUpgradeLogger.write2File("####发现表:" + paramString + "修改字段类型" + str1 + " " + str3 + "转" + str2 + "执行出现错误" + exception1);
/*  907 */                   exception.printStackTrace();
/*  908 */                   writeErrortableToFile(paramString);
/*  909 */                   return true;
/*      */                 } 
/*      */               } 
/*      */             } else {
/*      */               
/*      */               try {
/*  915 */                 if (str3.equalsIgnoreCase("TEXT") || str3.equalsIgnoreCase("NTEXT")) {
/*  916 */                   statement.execute("alter table " + paramString + " alter column " + str1 + " varchar(8000)");
/*  917 */                   upgradeRecordSet.execute("alter table " + paramString + " alter column " + str1 + "  " + str2 + "(8000)");
/*  918 */                   i = 8000;
/*      */                 } 
/*  920 */                 if (str2.equalsIgnoreCase("float")) {
/*  921 */                   statement.execute("alter table " + paramString + " alter column " + str1 + " float");
/*      */                 } else {
/*  923 */                   int k = 200;
/*  924 */                   if (i > 2) {
/*  925 */                     k = i;
/*      */                   }
/*  927 */                   if (j > i && j > 2) {
/*  928 */                     k = j;
/*      */                   }
/*  930 */                   statement.execute("alter table " + paramString + " alter column " + str1 + "  " + str2 + "(" + k + ")");
/*      */                 } 
/*  932 */               } catch (Exception exception) {
/*      */ 
/*      */                 
/*      */                 try {
/*      */ 
/*      */ 
/*      */                   
/*  939 */                   DBUpgradeLogger.write2File("====修改E7E8中原有的字段:alter table " + paramString + " alter column " + str1 + " e9_datatype出错，开始进行特殊处理");
/*  940 */                   Statement statement1 = paramConnection.createStatement();
/*  941 */                   Statement statement2 = paramConnection.createStatement();
/*  942 */                   ResultSet resultSet1 = statement1.executeQuery("SELECT NAME FROM SYSOBJECTS WHERE TYPE='D' AND PARENT_OBJ IN(SELECT ID FROM SYSOBJECTS WHERE NAME='" + paramString + "') ");
/*  943 */                   while (resultSet1.next()) {
/*  944 */                     String str7 = resultSet1.getString("NAME");
/*  945 */                     DBUpgradeLogger.write2File("无法修改字段类型,开始删除相关依赖:" + str7);
/*  946 */                     statement2.executeUpdate("ALTER TABLE " + paramString + " DROP CONSTRAINT " + str7);
/*      */                   } 
/*      */                   
/*  949 */                   ResultSet resultSet2 = statement1.executeQuery("  SELECT NAME FROM SYS.INDEXES  WHERE IS_UNIQUE !=1 AND IS_PRIMARY_KEY!=1  AND OBJECT_ID = OBJECT_ID('" + paramString + "')");
/*  950 */                   while (resultSet2.next()) {
/*  951 */                     String str7 = Util.null2String(resultSet2.getString("NAME"));
/*  952 */                     if ("null".equalsIgnoreCase(str7) || "".equals(str7)) {
/*  953 */                       DBUpgradeLogger.write2File("无法修改字段,修改主键时，主键名为NULL"); continue;
/*      */                     } 
/*  955 */                     DBUpgradeLogger.write2File("无法修改字段,开始删除相关依赖:" + str7);
/*  956 */                     statement2.executeUpdate("drop Index " + paramString + "." + str7);
/*      */                   } 
/*      */                   
/*  959 */                   statement.execute("alter table " + paramString + " alter column " + str1 + "  " + str2);
/*  960 */                   str3 = str2;
/*  961 */                 } catch (Exception exception1) {
/*  962 */                   DBUpgradeLogger.writeErrorLog2File("####发现表:" + paramString + "修改字段类型" + str1 + " " + str3 + "转" + str2 + "执行出现错误" + exception1);
/*  963 */                   DBUpgradeLogger.write2File("####发现表:" + paramString + "修改字段类型" + str1 + " " + str3 + "转" + str2 + "执行出现错误" + exception1);
/*  964 */                   exception.printStackTrace();
/*  965 */                   writeErrortableToFile(paramString);
/*  966 */                   return true;
/*      */                 } 
/*      */               } 
/*      */             } 
/*      */           } 
/*      */         } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  979 */         if (!str4.equals(str5) && ("N".equals(str4) || "0".equals(str4))) {
/*  980 */           if ("ID".equalsIgnoreCase(str1)) {
/*  981 */             DBUpgradeLogger.write2File("####检测到表:" + paramString + "字段ISNULL不一致,字段名为ID,需要强制更新ID字段");
/*  982 */             String str8 = "";
/*  983 */             if ("oracle".equalsIgnoreCase(str)) {
/*  984 */               str8 = "UPDATE " + paramString + " SET ID=ROWNUM WHERE ID IS NULL OR ID=0";
/*  985 */             } else if ("sqlserver".equalsIgnoreCase(str)) {
/*  986 */               str8 = "WITH TEMPTABLE AS ( SELECT ID, ROW_NUMBER() OVER(ORDER BY ID) ROWNUM FROM " + paramString + " )  UPDATE TEMPTABLE SET ID = ROWNUM";
/*      */             } 
/*  988 */             statement.executeUpdate(str8);
/*  989 */             DBUpgradeLogger.write2File("####检测到表:" + paramString + "字段ISNULL不一致,字段名为ID,需要强制更新ID字段,执行语句为:" + str8);
/*  990 */             DBUpgradeLogger.writeErrorLog2File("####检测到表:" + paramString + "字段ISNULL不一致,字段名为ID,需要强制更新ID字段,执行语句为:" + str8);
/*      */           } 
/*  992 */           DBUpgradeLogger.write2File("####检测到表:" + paramString + "字段ISNULL不一致,字段:" + str1 + "在源环境中ISNULL为:" + str5 + ",E9环境中为:" + str4);
/*  993 */           DBUpgradeLogger.writeErrorLog2File("####检测到表:" + paramString + "字段ISNULL不一致,字段:" + str1 + "在源环境中ISNULL为:" + str5 + ",E9环境中为:" + str4);
/*  994 */           if (!"".equals(str6)) {
/*  995 */             String str8 = "UPDATE " + paramString + " SET " + str1 + " =" + str6 + " WHERE " + str1 + " IS NULL";
/*  996 */             DBUpgradeLogger.write2File("####检测到表:" + paramString + "字段:" + str1 + "在目标环境中有默认值:" + str6 + ",开始执行更新语句:" + str8);
/*  997 */             DBUpgradeLogger.writeErrorLog2File("####检测到表:" + paramString + "字段:" + str1 + "在目标环境中有默认值:" + str6 + ",开始执行更新语句:" + str8);
/*      */             try {
/*  999 */               resultSet = statement.executeQuery("SELECT COUNT(1) AS COUNTS FROM " + paramString);
/* 1000 */               if (resultSet.next()) {
/* 1001 */                 int k = resultSet.getInt("COUNTS");
/* 1002 */                 if (k >= 100000) {
/* 1003 */                   DBUpgradeLogger.write2File("表" + paramString + "共有" + k + "条数据,数据量较大,需要较长时间,请耐心等待");
/*      */                 }
/*      */               } 
/* 1006 */             } catch (Exception exception) {
/* 1007 */               exception.printStackTrace();
/*      */             } 
/* 1009 */             statement.executeUpdate(str8);
/*      */           } 
/* 1011 */           String str7 = "";
/* 1012 */           if (str3.contains("CHAR") || str3.contains("TEXT")) {
/* 1013 */             str7 = "UPDATE " + paramString + " SET " + str1 + " =' ' WHERE " + str1 + " IS NULL";
/* 1014 */           } else if (str3.equals("NUMBER") || str3.equals("INT") || str3.equals("INTEGER")) {
/* 1015 */             str7 = "UPDATE " + paramString + " SET " + str1 + "='0' WHERE " + str1 + " IS NULL";
/* 1016 */           } else if (str3.equals("FLOAT")) {
/* 1017 */             str7 = "UPDATE " + paramString + " SET " + str1 + "='0.0' WHERE " + str1 + " IS NULL";
/*      */           } else {
/* 1019 */             DBUpgradeLogger.write2File("####表:" + paramString + "字段:" + str1 + "类型不在指定类型中,暂不做修改");
/*      */             continue;
/*      */           } 
/*      */           try {
/* 1023 */             if ("sqlserver".equalsIgnoreCase(str)) {
/* 1024 */               statement.executeUpdate("ALTER TABLE " + paramString + " DISABLE TRIGGER all");
/* 1025 */             } else if ("oracle".equalsIgnoreCase(str)) {
/* 1026 */               statement.executeUpdate("alter table " + paramString + " disable all triggers");
/*      */             } 
/* 1028 */           } catch (Exception exception) {
/* 1029 */             exception.printStackTrace();
/*      */           } 
/* 1031 */           DBUpgradeLogger.write2File("####更新表:" + paramString + "字段:" + str1 + ",语句为:" + str7);
/* 1032 */           statement.executeUpdate(str7);
/* 1033 */           DBUpgradeLogger.write2File("####修改表:" + paramString + "字段:" + str1 + ",语句为:" + str7);
/*      */         } 
/*      */       } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/* 1044 */       DBUpgradeLogger.write2File("####对表:" + paramString + "做字段长度,ISNULL检查完成");
/* 1045 */     } catch (Exception exception) {
/* 1046 */       DBUpgradeLogger.write2File("####对表:" + paramString + "做字段长度,ISNULL检查出现异常,异常信息:" + exception.toString());
/* 1047 */       bool = false;
/* 1048 */       exception.printStackTrace();
/*      */     } finally {
/*      */       try {
/* 1051 */         if (resultSet != null) {
/* 1052 */           resultSet.close();
/*      */         }
/* 1054 */         if (statement != null) {
/* 1055 */           statement.close();
/*      */         
/*      */         }
/*      */       
/*      */       }
/* 1060 */       catch (Exception exception) {
/* 1061 */         DBUpgradeLogger.write2File("error===SyncTableStructure 关闭资源异常:异常信息:" + exception.toString());
/* 1062 */         exception.printStackTrace();
/*      */       } 
/*      */     } 
/* 1065 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Map<String, String> getColumnMap(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6) {
/* 1079 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 1080 */     hashMap.put("DATA_TYPE", paramString1);
/* 1081 */     hashMap.put("DATA_LENGTH", paramString2);
/* 1082 */     hashMap.put("DATA_PRECISION", paramString3);
/* 1083 */     hashMap.put("DATA_SCALE", paramString4);
/* 1084 */     hashMap.put("DATA_DEFAULT", paramString5);
/* 1085 */     hashMap.put("NULLABLE", paramString6);
/* 1086 */     return (Map)hashMap;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void writeErrortableToFile(String paramString) {
/* 1094 */     String str1 = GCONST.getRootPath() + "system" + File.separatorChar + "upgradetoe9" + File.separatorChar + "report";
/* 1095 */     File file1 = new File(str1);
/* 1096 */     if (!file1.exists() || !file1.isDirectory()) {
/* 1097 */       file1.mkdir();
/*      */     }
/* 1099 */     String str2 = GCONST.getRootPath() + "system" + File.separatorChar + "upgradetoe9" + File.separatorChar + "report" + File.separatorChar + "tableconvererror.properties";
/* 1100 */     File file2 = new File(str2);
/* 1101 */     if (!file2.exists() || !file2.isFile()) {
/*      */       try {
/* 1103 */         file2.createNewFile();
/* 1104 */       } catch (Exception exception) {
/* 1105 */         DBUpgradeLogger.writeErrorLog2File("error===SyncTableStructure 创建错误日志出错:异常信息:" + str2 + exception.toString());
/*      */       } 
/*      */     }
/* 1108 */     PropUtil propUtil = PropUtil.getInstance(new String[] { str2 });
/* 1109 */     String str3 = Util.null2String(propUtil.getValues("e7_errorlog"));
/* 1110 */     if (!str3.equalsIgnoreCase("")) {
/* 1111 */       str3 = str3 + ",";
/*      */     }
/* 1113 */     propUtil.put("e7_errorlog", str3 + paramString);
/* 1114 */     propUtil.store();
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean checkClobTableByTablename(String paramString) {
/* 1122 */     String str = Util.null2String(this.propUtil.getValues("e7_clobdealtables"));
/* 1123 */     String[] arrayOfString = str.split(",");
/* 1124 */     for (String str1 : arrayOfString) {
/* 1125 */       if (str1.equalsIgnoreCase(paramString)) {
/* 1126 */         return true;
/*      */       }
/*      */     } 
/* 1129 */     return false;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean addFieldToE9(String paramString1, String paramString2) {
/* 1138 */     boolean bool = true;
/* 1139 */     UpgradeRecordSet upgradeRecordSet = new UpgradeRecordSet();
/* 1140 */     String str = upgradeRecordSet.getDBType();
/* 1141 */     Statement statement = null;
/* 1142 */     ResultSet resultSet = null;
/* 1143 */     Connection connection = null;
/*      */     try {
/* 1145 */       connection = this.dbutil.getSourceConnection();
/* 1146 */       statement = connection.createStatement();
/* 1147 */       String str1 = "";
/* 1148 */       if ("Oracle".equalsIgnoreCase(str)) {
/* 1149 */         str1 = "SELECT DATA_TYPE,DATA_LENGTH,DATA_PRECISION,DATA_SCALE FROM USER_TAB_COLUMNS WHERE COLUMN_NAME='" + paramString2.toUpperCase() + "' AND TABLE_NAME ='" + paramString1 + "'";
/* 1150 */       } else if ("SqlServer".equalsIgnoreCase(str)) {
/* 1151 */         str1 = "SELECT ST.NAME AS DATA_TYPE,SC.LENGTH AS DATA_LENGTH,SC.XPREC AS DATA_PRECISION,SC.XSCALE AS DATA_SCALE FROM SYSCOLUMNS SC,SYSTYPES ST WHERE SC.XUSERTYPE=ST.XUSERTYPE AND SC.NAME='" + paramString2.toUpperCase() + "' AND SC.ID IN(SELECT ID FROM SYSOBJECTS WHERE XTYPE='U' and name='" + paramString1 + "')";
/*      */       }
/* 1153 */       else if ("SqlServer".equalsIgnoreCase(str)) {
/*      */         
/* 1155 */         str1 = "   select DATA_TYPE,CHARACTER_OCTET_LENGTH as DATA_LENGTH,numeric_precision as  DATA_PRECISION,numeric_scale as DATA_SCALE  from Information_schema.columns WHERE table_name = lower('" + paramString1 + "')and column_name= lower('" + paramString2 + "')";
/*      */       } 
/* 1157 */       resultSet = statement.executeQuery(str1);
/*      */       
/* 1159 */       resultSet.next();
/* 1160 */       String str2 = resultSet.getString("DATA_TYPE");
/* 1161 */       if ("Oracle".equalsIgnoreCase(str)) {
/* 1162 */         if (str2.equalsIgnoreCase("VARCHAR2") || str2.equalsIgnoreCase("NVARCHAR2") || str2.equalsIgnoreCase("CHAR") || str2.equalsIgnoreCase("RAW")) {
/* 1163 */           String str3 = resultSet.getString("DATA_LENGTH");
/* 1164 */           str2 = str2 + "(" + (str3.equals("-1") ? "3500" : str3) + ")";
/* 1165 */         } else if (str2.equalsIgnoreCase("NUMBER")) {
/* 1166 */           String str3 = Util.null2String(resultSet.getString("DATA_PRECISION"));
/* 1167 */           if (str3 != "") {
/* 1168 */             str2 = str2 + "(" + str3 + "," + resultSet.getString("DATA_SCALE") + ")";
/*      */           }
/* 1170 */         } else if (str2.equalsIgnoreCase("FLOAT")) {
/* 1171 */           str2 = str2 + "(" + resultSet.getString("DATA_PRECISION") + ")";
/*      */         } 
/* 1173 */       } else if ("SqlServer".equalsIgnoreCase(str)) {
/* 1174 */         if (str2.equalsIgnoreCase("CHAR") || str2.equalsIgnoreCase("VARBINARY") || str2.equalsIgnoreCase("VARCHAR")) {
/* 1175 */           String str3 = resultSet.getString("DATA_LENGTH");
/* 1176 */           str2 = str2 + "(" + (str3.equals("-1") ? "MAX" : str3) + ")";
/* 1177 */         } else if (str2.equalsIgnoreCase("NCHAR") || str2.equalsIgnoreCase("NVARCHAR")) {
/* 1178 */           str2 = str2 + "(" + (Integer.parseInt(resultSet.getString("DATA_LENGTH")) / 2) + ")";
/* 1179 */         } else if (str2.equalsIgnoreCase("DECIMAL") || str2.equalsIgnoreCase("NUMERIC")) {
/* 1180 */           String str3 = Util.null2String(resultSet.getString("DATA_SCALE"));
/* 1181 */           if (str3.equals("0") || str3.equals("")) {
/* 1182 */             str2 = str2 + "(" + resultSet.getString("DATA_PRECISION") + ")";
/*      */           } else {
/* 1184 */             str2 = str2 + "(" + resultSet.getString("DATA_PRECISION") + "," + resultSet.getString("DATA_SCALE") + ")";
/*      */           } 
/*      */         } 
/*      */       } 
/* 1188 */       DBUpgradeLogger.write2File("开始删除E9中原有的字段:ALTER TABLE " + paramString1 + " DROP COLUMN " + paramString2);
/* 1189 */       boolean bool1 = upgradeRecordSet.executeUpdate("ALTER TABLE " + paramString1 + " DROP COLUMN " + paramString2, new Object[0]);
/* 1190 */       if (!bool1 && 
/* 1191 */         "SqlServer".equalsIgnoreCase(str)) {
/* 1192 */         DBUpgradeLogger.write2File("====删除E9中原有的字段:ALTER TABLE " + paramString1 + "  DROP COLUMN " + paramString2 + "出错，开始进行特殊处理");
/* 1193 */         UpgradeRecordSet upgradeRecordSet1 = new UpgradeRecordSet();
/* 1194 */         UpgradeRecordSet upgradeRecordSet2 = new UpgradeRecordSet();
/* 1195 */         upgradeRecordSet1.executeQuery("SELECT NAME FROM SYSOBJECTS WHERE TYPE='D' AND PARENT_OBJ IN(SELECT ID FROM SYSOBJECTS WHERE NAME='" + paramString1 + "') ", new Object[0]);
/* 1196 */         while (upgradeRecordSet1.next()) {
/* 1197 */           String str3 = upgradeRecordSet1.getString("NAME");
/* 1198 */           DBUpgradeLogger.write2File("无法删除字段,开始删除相关依赖:" + str3);
/* 1199 */           upgradeRecordSet2.executeUpdate("ALTER TABLE " + paramString1 + " DROP CONSTRAINT " + str3, new Object[0]);
/*      */         } 
/*      */         
/* 1202 */         upgradeRecordSet1.executeQuery("  SELECT NAME FROM SYS.INDEXES  WHERE IS_UNIQUE !=1 AND IS_PRIMARY_KEY!=1  AND OBJECT_ID = OBJECT_ID('" + paramString1 + "')", new Object[0]);
/* 1203 */         while (upgradeRecordSet1.next()) {
/* 1204 */           String str3 = Util.null2String(upgradeRecordSet1.getString("NAME"));
/* 1205 */           if ("null".equalsIgnoreCase(str3) || "".equals(str3)) {
/* 1206 */             DBUpgradeLogger.write2File("无法删除字段,删除主键时，主键名为NULL"); continue;
/*      */           } 
/* 1208 */           DBUpgradeLogger.write2File("无法删除字段,开始删除相关依赖:" + str3);
/* 1209 */           upgradeRecordSet2.executeUpdate("drop Index " + paramString1 + "." + str3, new Object[0]);
/*      */         } 
/*      */         
/* 1212 */         DBUpgradeLogger.write2File("====删除E9中原有的字段:ALTER TABLE " + paramString1 + " DROP COLUMN " + paramString2 + "出错，特殊处理完成");
/* 1213 */         boolean bool2 = upgradeRecordSet1.executeUpdate("ALTER TABLE " + paramString1 + " DROP COLUMN " + paramString2, new Object[0]);
/* 1214 */         DBUpgradeLogger.write2File("再次尝试删除E9中原有的字段:ALTER TABLE " + paramString1 + " DROP COLUMN " + paramString2 + ",执行结果：" + bool2);
/*      */       } 
/*      */       
/* 1217 */       DBUpgradeLogger.write2File("开始在E9中重新添加:ALTER TABLE " + paramString1 + " ADD " + paramString2 + " " + str2);
/* 1218 */       upgradeRecordSet.executeUpdate("ALTER TABLE " + paramString1 + " ADD " + paramString2 + " " + str2, new Object[0]);
/* 1219 */     } catch (Exception exception) {
/* 1220 */       bool = false;
/* 1221 */       DBUpgradeLogger.write2File("同步表" + paramString1 + "字段:" + paramString2 + "出现异常,异常信息:" + exception.toString() + "==" + exception.getStackTrace());
/* 1222 */       exception.printStackTrace();
/*      */     } finally {
/*      */       try {
/* 1225 */         if (resultSet != null) {
/* 1226 */           resultSet.close();
/*      */         }
/* 1228 */         if (statement != null) {
/* 1229 */           statement.close();
/*      */         }
/* 1231 */         if (connection != null) {
/* 1232 */           connection.close();
/*      */         }
/* 1234 */       } catch (Exception exception) {
/* 1235 */         exception.printStackTrace();
/* 1236 */         DBUpgradeLogger.write2File("关闭资源出现异常");
/*      */       } 
/*      */     } 
/* 1239 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean checkIndex() {
/* 1248 */     boolean bool = true;
/* 1249 */     UpgradeRecordSet upgradeRecordSet = new UpgradeRecordSet();
/* 1250 */     String str1 = upgradeRecordSet.getDBType();
/* 1251 */     List<String> list = Arrays.asList(this.propUtil.getValues("e7_indexcheck").split(","));
/* 1252 */     String str2 = "";
/* 1253 */     String str3 = "";
/* 1254 */     Connection connection = null;
/* 1255 */     Statement statement1 = null;
/* 1256 */     Statement statement2 = null;
/* 1257 */     Statement statement3 = null;
/* 1258 */     ResultSet resultSet1 = null;
/* 1259 */     ResultSet resultSet2 = null;
/* 1260 */     ResultSet resultSet3 = null;
/*      */     try {
/* 1262 */       connection = this.dbutil.getSourceConnection();
/* 1263 */       statement1 = connection.createStatement();
/* 1264 */       statement2 = connection.createStatement();
/* 1265 */       statement3 = connection.createStatement();
/* 1266 */       byte b = 1;
/* 1267 */       for (String str : list) {
/* 1268 */         if ("oracle".equalsIgnoreCase(str1)) {
/* 1269 */           str2 = "SELECT T.TABLE_NAME AS TABLENAME,T.INDEX_NAME AS INDEXNAME,WM_CONCAT(T.COLUMN_NAME) AS INDEXCOLUMNS FROM USER_IND_COLUMNS T,USER_INDEXES I WHERE T.INDEX_NAME = I.INDEX_NAME AND T.TABLE_NAME = I.TABLE_NAME AND I.UNIQUENESS='UNIQUE' AND  T.TABLE_NAME ='" + str.toUpperCase() + "' GROUP BY  T.TABLE_NAME,T.INDEX_NAME ";
/* 1270 */           str3 = "SELECT DATA_TYPE,DATA_LENGTH,DATA_PRECISION,DATA_SCALE FROM USER_TAB_COLUMNS WHERE COLUMN_NAME='UPGRADEE9TEMPID' AND TABLE_NAME ='" + str.toUpperCase() + "' ";
/*      */         }
/* 1272 */         else if ("sqlserver".equalsIgnoreCase(str1)) {
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */           
/* 1292 */           str2 = "select indexs.Tab_Name as TABLENAME,indexs.Index_Name as INDEXNAME ,indexs.[Co_Names] as INDEXCOLUMNS,Ind_Attribute.is_primary_key as ISPRIMARYKEY,Ind_Attribute.is_unique AS ISUNIQUE,Ind_Attribute.is_disabled AS ISDISABLED from (select Tab_Name,Index_Name, [Co_Names]=stuff((select ','+[Co_Name] from ( select tab.Name as Tab_Name,ind.Name as Index_Name,Col.Name as Co_Name from sys.indexes ind inner join sys.tables tab on ind.Object_id = tab.object_id and ind.type in (1,2) inner join sys.index_columns index_columns on tab.object_id = index_columns.object_id and ind.index_id = index_columns.index_id inner join sys.columns Col on tab.object_id = Col.object_id and index_columns.column_id = Col.column_id ) t where Tab_Name=tb.Tab_Name and Index_Name=tb.Index_Name for xml path('')), 1, 1, '') from ( select tab.Name as Tab_Name,ind.Name as Index_Name,Col.Name as Co_Name from sys.indexes ind inner join sys.tables tab on ind.Object_id = tab.object_id and ind.type in (1,2) inner join sys.index_columns index_columns on tab.object_id = index_columns.object_id and ind.index_id = index_columns.index_id inner join sys.columns Col on tab.object_id = Col.object_id and index_columns.column_id = Col.column_id )tb where Tab_Name not like 'sys%' group by Tab_Name,Index_Name ) indexs inner join sys.indexes Ind_Attribute on indexs.Index_Name = Ind_Attribute.name where  indexs.Tab_Name ='" + str.toUpperCase() + "' and Ind_Attribute.is_unique=1 order by indexs.Tab_Name ";
/*      */ 
/*      */ 
/*      */           
/* 1296 */           str3 = "SELECT ST.NAME AS DATA_TYPE,SC.LENGTH AS DATA_LENGTH,SC.XPREC AS DATA_PRECISION,SC.XSCALE AS DATA_SCALE  FROM SYSCOLUMNS SC,SYSTYPES ST WHERE SC.XUSERTYPE=ST.XUSERTYPE AND SC.NAME='UPGRADEE9TEMPID'  AND SC.ID IN(SELECT ID FROM SYSOBJECTS WHERE XTYPE='U' and name='" + str.toUpperCase() + "') ";
/*      */         } 
/*      */         
/* 1299 */         DBUpgradeLogger.write2File("开始检查表" + str + "主键约束在E7中是否存在重复数据");
/* 1300 */         upgradeRecordSet.executeQuery(str2, new Object[0]);
/* 1301 */         while (upgradeRecordSet.next()) {
/* 1302 */           String str4 = Util.null2String(upgradeRecordSet.getString("INDEXCOLUMNS")).replaceAll(" ", "");
/* 1303 */           DBUpgradeLogger.write2File("检查表" + str + "，找到索引，相关字段为：" + str4);
/* 1304 */           if (!"".equals(str4)) {
/* 1305 */             DBUpgradeLogger.write2File("检查表" + str + "，开始查询索引约束重复数据：select distinct " + str4 + " from " + str + " group by " + str4 + " having count(*)>1");
/* 1306 */             resultSet1 = statement1.executeQuery("select distinct " + str4 + " from " + str + " group by " + str4 + " having count(*)>1");
/*      */             
/* 1308 */             List<String> list1 = Arrays.asList(str4.split(","));
/*      */             
/* 1310 */             String str5 = "(";
/* 1311 */             byte b1 = 0;
/* 1312 */             while (resultSet1.next()) {
/* 1313 */               b1++;
/* 1314 */               str5 = str5 + "(";
/* 1315 */               for (String str6 : list1) {
/* 1316 */                 str5 = str5 + str6 + "=" + resultSet1.getString(str6) + " AND ";
/*      */               }
/* 1318 */               str5 = str5 + " 1=1 ) or ";
/*      */             } 
/* 1320 */             str5 = str5 + "1=3 )";
/*      */             
/* 1322 */             if (b1 > 0) {
/* 1323 */               DBUpgradeLogger.write2File("检查表" + str + "，发现索引约束重复数据");
/* 1324 */               resultSet2 = statement2.executeQuery(str3);
/* 1325 */               if (!resultSet2.next()) {
/* 1326 */                 statement2.executeUpdate("ALTER TABLE " + str + "  ADD UPGRADEE9TEMPID INT");
/*      */               }
/* 1328 */               if ("oracle".equalsIgnoreCase(str1)) {
/* 1329 */                 statement2.executeUpdate("UPDATE " + str + " SET UPGRADEE9TEMPID=ROWNUM ");
/* 1330 */               } else if ("sqlserver".equalsIgnoreCase(str1)) {
/* 1331 */                 statement2.executeUpdate("WITH TEMPTABLE AS ( SELECT UPGRADEE9TEMPID , ROW_NUMBER() OVER(ORDER BY UPGRADEE9TEMPID) ROWNUM FROM " + str + " )  UPDATE TEMPTABLE SET UPGRADEE9TEMPID = ROWNUM");
/*      */               } 
/* 1333 */               String str6 = "DELETE FROM " + str + " WHERE " + str5;
/* 1334 */               str6 = str6 + " and upgradee9tempid not in(SELECT MAX(upgradee9tempid) FROM " + str + " GROUP BY " + str4 + "  HAVING COUNT(*) > 1)";
/* 1335 */               DBUpgradeLogger.write2File("检查表" + str + "，发现索引约束重复数据，开始执行删除语句：" + str6);
/* 1336 */               statement2.executeUpdate(str6);
/*      */               
/* 1338 */               statement2.executeUpdate("ALTER TABLE " + str + "  DROP COLUMN UPGRADEE9TEMPID "); continue;
/*      */             } 
/* 1340 */             DBUpgradeLogger.write2File("检查表" + str + "，未发现索引约束重复数据");
/*      */           } 
/*      */         } 
/*      */         
/* 1344 */         double d = MathUtil.div((b * 5), list.size(), 1) + 95.0D;
/* 1345 */         setActionProcess(d + "");
/* 1346 */         b++;
/*      */       } 
/* 1348 */     } catch (Exception exception) {
/* 1349 */       DBUpgradeLogger.write2File("error====检查所有需要同步的表索引在源库中是否有冲突数据出现异常,异常信息：" + exception.toString() + "||" + exception.getStackTrace());
/* 1350 */       bool = false;
/* 1351 */       exception.printStackTrace();
/*      */     } finally {
/*      */       try {
/* 1354 */         if (resultSet1 != null) {
/* 1355 */           resultSet1.close();
/*      */         }
/* 1357 */         if (resultSet2 != null) {
/* 1358 */           resultSet2.close();
/*      */         }
/* 1360 */         if (resultSet3 != null) {
/* 1361 */           resultSet3.close();
/*      */         }
/* 1363 */         if (statement1 != null) {
/* 1364 */           statement1.close();
/*      */         }
/* 1366 */         if (statement2 != null) {
/* 1367 */           statement2.close();
/*      */         }
/* 1369 */         if (statement3 != null) {
/* 1370 */           statement3.close();
/*      */         }
/* 1372 */         if (connection != null) {
/* 1373 */           connection.close();
/*      */         }
/* 1375 */       } catch (Exception exception) {
/* 1376 */         exception.printStackTrace();
/* 1377 */         DBUpgradeLogger.write2File("检查所有需要同步的表索引在源库中是否有冲突数据异常：关闭资源出现异常");
/*      */       } 
/*      */     } 
/* 1380 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean dropView() {
/* 1388 */     boolean bool = true;
/* 1389 */     String str1 = this.dbutil.getDBtype();
/* 1390 */     Connection connection = null;
/* 1391 */     Statement statement = null;
/* 1392 */     ResultSet resultSet = null;
/* 1393 */     String str2 = "";
/* 1394 */     if ("sqlserver".equalsIgnoreCase(str1)) {
/* 1395 */       str2 = "SELECT NAME AS VIEW_NAME FROM sys.objects WHERE type = 'V' ";
/* 1396 */     } else if ("oracle".equalsIgnoreCase(str1)) {
/* 1397 */       str2 = "SELECT VIEW_NAME FROM USER_VIEWS ";
/*      */     } 
/*      */     try {
/* 1400 */       connection = this.dbutil.getSourceConnection();
/* 1401 */       statement = connection.createStatement();
/* 1402 */       resultSet = statement.executeQuery(str2);
/* 1403 */       byte b = 1;
/* 1404 */       ArrayList<String> arrayList = new ArrayList();
/* 1405 */       while (resultSet.next()) {
/* 1406 */         arrayList.add(resultSet.getString("VIEW_NAME"));
/*      */       }
/* 1408 */       for (String str : arrayList) {
/* 1409 */         DBUpgradeLogger.write2File("删除源数据库视图：DROP VIEW " + str);
/* 1410 */         statement.executeUpdate("DROP VIEW " + str);
/* 1411 */         double d = MathUtil.div((b * 10), arrayList.size(), 1) + 85.0D;
/* 1412 */         setActionProcess(d + "");
/* 1413 */         b++;
/*      */       } 
/* 1415 */     } catch (Exception exception) {
/* 1416 */       bool = false;
/* 1417 */       DBUpgradeLogger.write2File("error=========删除源数据库视图出现异常：" + exception.toString() + "===" + exception.getStackTrace());
/* 1418 */       exception.printStackTrace();
/*      */     } finally {
/*      */       try {
/* 1421 */         if (resultSet != null) {
/* 1422 */           resultSet.close();
/*      */         }
/* 1424 */         if (statement != null) {
/* 1425 */           statement.close();
/*      */         }
/* 1427 */         if (connection != null) {
/* 1428 */           connection.close();
/*      */         }
/* 1430 */       } catch (Exception exception) {
/* 1431 */         exception.printStackTrace();
/* 1432 */         DBUpgradeLogger.write2File("删除源数据库视图 发生异常：关闭资源出现异常");
/*      */       } 
/*      */     } 
/* 1435 */     return bool;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean SyncTableName() {
/* 1444 */     boolean bool = true;
/* 1445 */     String str1 = this.dbutil.getDBtype();
/* 1446 */     Connection connection = null;
/* 1447 */     Statement statement = null;
/* 1448 */     ResultSet resultSet = null;
/* 1449 */     String str2 = "select name from sys.tables order by name";
/* 1450 */     LinkedList<String> linkedList1 = new LinkedList();
/* 1451 */     LinkedList<String> linkedList2 = new LinkedList();
/* 1452 */     if ("sqlserver".equalsIgnoreCase(str1)) {
/*      */       
/*      */       try {
/* 1455 */         connection = this.dbutil.getSourceConnection();
/* 1456 */         statement = connection.createStatement();
/* 1457 */         resultSet = statement.executeQuery(str2);
/* 1458 */         while (resultSet.next()) {
/* 1459 */           linkedList1.add(resultSet.getString("name"));
/*      */         }
/*      */       }
/* 1462 */       catch (Exception exception) {
/* 1463 */         bool = false;
/* 1464 */         DBUpgradeLogger.write2File("error=========修改表名大小写出现异常：" + exception.toString() + "===" + exception.getStackTrace());
/* 1465 */         exception.printStackTrace();
/*      */       } finally {
/*      */         try {
/* 1468 */           if (resultSet != null) {
/* 1469 */             resultSet.close();
/*      */           }
/* 1471 */           if (statement != null) {
/* 1472 */             statement.close();
/*      */           }
/* 1474 */           if (connection != null) {
/* 1475 */             connection.close();
/*      */           }
/* 1477 */         } catch (Exception exception) {
/* 1478 */           exception.printStackTrace();
/* 1479 */           DBUpgradeLogger.write2File("修改表名大小写 发生异常：关闭资源出现异常");
/*      */         } 
/*      */       } 
/*      */       
/* 1483 */       UpgradeRecordSet upgradeRecordSet = new UpgradeRecordSet();
/* 1484 */       upgradeRecordSet.executeSql(str2);
/* 1485 */       while (upgradeRecordSet.next()) {
/* 1486 */         linkedList2.add(upgradeRecordSet.getString("name"));
/*      */       }
/*      */       
/* 1489 */       LinkedList<String> linkedList3 = removeAllSame(linkedList1, linkedList2);
/* 1490 */       LinkedList<String> linkedList4 = removeAllSame(linkedList2, linkedList1);
/* 1491 */       Iterator<String> iterator = linkedList3.iterator();
/* 1492 */       connection = this.dbutil.getSourceConnection();
/*      */       try {
/* 1494 */         statement = connection.createStatement();
/* 1495 */         while (iterator.hasNext()) {
/* 1496 */           String str = iterator.next();
/* 1497 */           Iterator<String> iterator1 = linkedList4.iterator();
/* 1498 */           while (iterator1.hasNext()) {
/* 1499 */             String str3 = iterator1.next();
/* 1500 */             if (str.toLowerCase().equals(str3.toLowerCase())) {
/* 1501 */               str2 = "EXEC sp_rename '" + str + "','" + str3 + "'";
/* 1502 */               DBUpgradeLogger.write2File("修改E7/E8表名称：sql=" + str2);
/* 1503 */               statement.executeUpdate(str2);
/*      */             }
/*      */           
/*      */           } 
/*      */         } 
/* 1508 */       } catch (Exception exception) {
/* 1509 */         bool = false;
/* 1510 */         DBUpgradeLogger.write2File("error=========修改表名大小写出现异常：" + exception.toString() + "===" + exception.getStackTrace());
/* 1511 */         exception.printStackTrace();
/*      */       } finally {
/*      */         try {
/* 1514 */           if (statement != null) {
/* 1515 */             statement.close();
/*      */           }
/* 1517 */           if (connection != null) {
/* 1518 */             connection.close();
/*      */           }
/* 1520 */         } catch (Exception exception) {
/* 1521 */           exception.printStackTrace();
/* 1522 */           DBUpgradeLogger.write2File("修改表名大小写 发生异常：关闭资源出现异常");
/*      */         } 
/*      */       } 
/*      */     } 
/*      */ 
/*      */     
/* 1528 */     return bool;
/*      */   }
/*      */   
/*      */   private LinkedList<String> removeAllSame(List<String> paramList1, List<String> paramList2) {
/* 1532 */     LinkedList<String> linkedList = new LinkedList<>(paramList1);
/* 1533 */     HashSet<String> hashSet = new HashSet<>(paramList2);
/* 1534 */     Iterator<String> iterator = linkedList.iterator();
/* 1535 */     while (iterator.hasNext()) {
/* 1536 */       if (hashSet.contains(iterator.next())) {
/* 1537 */         iterator.remove();
/*      */       }
/*      */     } 
/* 1540 */     return linkedList;
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/upgradetool/dbupgrade/actions/upgrade/SyncTableStructure.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */