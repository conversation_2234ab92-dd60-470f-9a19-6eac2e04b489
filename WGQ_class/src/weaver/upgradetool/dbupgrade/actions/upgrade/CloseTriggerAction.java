/*    */ package weaver.upgradetool.dbupgrade.actions.upgrade;
/*    */ 
/*    */ import com.alibaba.fastjson.JSONObject;
/*    */ import java.util.HashMap;
/*    */ import weaver.upgradetool.dbupgrade.actions.ActionInterface;
/*    */ import weaver.upgradetool.dbupgrade.logger.DBUpgradeLogger;
/*    */ import weaver.upgradetool.dbupgrade.upgrade.DBUpgradeOperation;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class CloseTriggerAction
/*    */   implements ActionInterface
/*    */ {
/*    */   public String execute(HashMap<String, String> paramHashMap) {
/* 17 */     DBUpgradeLogger.write2File("开始执行关闭触发器的action");
/* 18 */     DBUpgradeOperation dBUpgradeOperation = new DBUpgradeOperation();
/* 19 */     boolean bool = dBUpgradeOperation.closeDBTrigger();
/* 20 */     JSONObject jSONObject = new JSONObject();
/* 21 */     if (bool) {
/* 22 */       jSONObject.put("status", "success");
/*    */     } else {
/* 24 */       jSONObject.put("status", "failure");
/*    */     } 
/* 26 */     return jSONObject.toJSONString();
/*    */   }
/*    */   
/*    */   public void startAction(HashMap<String, String> paramHashMap) {}
/*    */   
/*    */   public void endAction(HashMap<String, String> paramHashMap) {}
/*    */   
/*    */   public void setActionProcess(String paramString) {}
/*    */   
/*    */   public void setActionProcessName(String paramString) {}
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/upgradetool/dbupgrade/actions/upgrade/CloseTriggerAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */