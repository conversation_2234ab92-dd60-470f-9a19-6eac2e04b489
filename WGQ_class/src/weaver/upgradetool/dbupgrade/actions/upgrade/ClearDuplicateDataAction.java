/*     */ package weaver.upgradetool.dbupgrade.actions.upgrade;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import java.sql.Connection;
/*     */ import java.sql.ResultSet;
/*     */ import java.sql.Statement;
/*     */ import java.util.Arrays;
/*     */ import java.util.HashMap;
/*     */ import java.util.HashSet;
/*     */ import java.util.List;
/*     */ import weaver.general.MathUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.upgradetool.dbupgrade.actions.ActionInterface;
/*     */ import weaver.upgradetool.dbupgrade.actions.ActionProcess;
/*     */ import weaver.upgradetool.dbupgrade.logger.DBUpgradeLogger;
/*     */ import weaver.upgradetool.dbupgrade.upgrade.DBUtil;
/*     */ import weaver.upgradetool.dbupgrade.upgrade.PropUtil;
/*     */ import weaver.upgradetool.dbupgrade.upgrade.UpgradeRecordSet;
/*     */ 
/*     */ public class ClearDuplicateDataAction implements ActionInterface {
/*  21 */   JSONObject object = new JSONObject();
/*  22 */   private DBUtil dbutil = new DBUtil();
/*  23 */   PropUtil propUtil = PropUtil.getInstance(new String[] { PropUtil.MIGRATION });
/*     */ 
/*     */   
/*     */   public String execute(HashMap<String, String> paramHashMap) {
/*  27 */     startAction(null);
/*  28 */     List<String> list1 = Arrays.asList(this.propUtil.getValues("clearduplicatedata_tables").split(","));
/*  29 */     List<String> list2 = Arrays.asList(this.propUtil.getValues("e7copydatatoe9table").split(","));
/*     */     
/*  31 */     DBUpgradeLogger.write2File("====ClearDuplicateDataAction 开始清空E7或E8表重复数据");
/*  32 */     boolean bool = clearE7OrE8DuplicateData(list1);
/*     */     
/*  34 */     if (!bool) {
/*  35 */       this.object.put("status", "failure");
/*  36 */       DBUpgradeLogger.write2File("error====ClearDuplicateDataAction 清空E7或E8表重复数据失败");
/*  37 */       return this.object.toJSONString();
/*     */     } 
/*  39 */     bool = copyE7OrE8DataToE9(list2);
/*  40 */     if (!bool) {
/*  41 */       this.object.put("status", "failure");
/*  42 */       DBUpgradeLogger.write2File("error====ClearDuplicateDataAction 复制E7或E8表数据失败");
/*  43 */       return this.object.toJSONString();
/*     */     } 
/*  45 */     DBUpgradeLogger.write2File("====ClearDuplicateDataAction 清空E7或E8表重复数据完成");
/*     */     
/*  47 */     this.object.put("status", "success");
/*  48 */     endAction(null);
/*  49 */     return this.object.toJSONString();
/*     */   }
/*     */ 
/*     */   
/*     */   public void startAction(HashMap<String, String> paramHashMap) {
/*  54 */     ActionProcess.getInstance().setActionProcess("0");
/*  55 */     ActionProcess.getInstance().setActionProcess("startAction");
/*  56 */     DBUpgradeLogger.write2File("=====================startAction:ClearDuplicateDataAction=====================");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void endAction(HashMap<String, String> paramHashMap) {
/*  63 */     ActionProcess.getInstance().setActionProcess("100");
/*  64 */     ActionProcess.getInstance().setActionProcess("endAction");
/*  65 */     DBUpgradeLogger.write2File("=====================endAction:ClearDuplicateDataAction=====================");
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void setActionProcess(String paramString) {
/*  71 */     ActionProcess.getInstance().setActionProcess(paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void setActionProcessName(String paramString) {
/*  77 */     ActionProcess.getInstance().setActionProcessName(paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean copyE7OrE8DataToE9(List<String> paramList) {
/*  86 */     boolean bool = true;
/*  87 */     UpgradeRecordSet upgradeRecordSet = new UpgradeRecordSet();
/*  88 */     Connection connection = null;
/*  89 */     Statement statement = null;
/*  90 */     ResultSet resultSet = null;
/*  91 */     connection = this.dbutil.getSourceConnection();
/*  92 */     for (String str : paramList) {
/*     */       
/*  94 */       try { statement = connection.createStatement();
/*  95 */         String[] arrayOfString1 = str.split("%");
/*  96 */         if (arrayOfString1.length < 2)
/*     */         
/*     */         { 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */           
/* 165 */           if (statement != null)
/*     */             
/* 167 */             try { statement.close(); }
/* 168 */             catch (Exception exception)
/* 169 */             { DBUpgradeLogger.write2File("error====ClearDuplicateDataAction 关闭资源异常:" + exception.toString());
/* 170 */               exception.printStackTrace(); }   continue; }  String str1 = arrayOfString1[0]; String str2 = arrayOfString1[1]; String str3 = ""; boolean bool1 = false; if (arrayOfString1.length >= 3) { str3 = arrayOfString1[2]; bool1 = true; }  DBUpgradeLogger.write2File("=========复制数据字段名：" + str2); DBUpgradeLogger.write2File("=========复制数据表名：" + str1); if ("".equals(str1)) { if (statement != null) try { statement.close(); } catch (Exception exception) { DBUpgradeLogger.write2File("error====ClearDuplicateDataAction 关闭资源异常:" + exception.toString()); exception.printStackTrace(); }   continue; }  String[] arrayOfString2 = str2.split("\\."); DBUpgradeLogger.write2File("开始复制数据字段数组长度：" + arrayOfString2.length); if (arrayOfString2.length < 1) { if (statement != null) try { statement.close(); } catch (Exception exception) { DBUpgradeLogger.write2File("error====ClearDuplicateDataAction 关闭资源异常:" + exception.toString()); exception.printStackTrace(); }   continue; }  resultSet = statement.executeQuery(" select * from " + str1); while (resultSet.next()) { String str4 = ""; String str5 = ""; for (byte b = 0; b < arrayOfString2.length; b++) { if (!"".equals(arrayOfString2[b])) if (str5.length() <= 0) { str5 = str5 + " " + arrayOfString2[b]; str4 = str4 + "'" + Util.null2String(resultSet.getString(arrayOfString2[b])) + "'"; } else { str5 = str5 + "," + arrayOfString2[b]; str4 = str4 + ",'" + Util.null2String(resultSet.getString(arrayOfString2[b])) + "'"; }   }  if (str5.length() <= 0) continue;  if (bool1) { str5 = str3 + "," + str5; str4 = "(select MAX(" + str3 + ")+1 from " + str1 + " )," + str4; }  String str6 = " insert into " + str1 + "(" + str5 + ") values(" + str4 + ")"; DBUpgradeLogger.write2File("========执行的SQL：" + str6); upgradeRecordSet.executeUpdate(str6, new Object[0]); }  statement.executeUpdate(" delete from " + str1); DBUpgradeLogger.write2File("结束复制数据表名：" + str1); } catch (Exception exception) { DBUpgradeLogger.write2File("error====ClearDuplicateDataAction 复制数据错误:表数据：" + str + "错误信息" + exception.toString()); } finally { if (statement != null) try { statement.close(); } catch (Exception exception) { DBUpgradeLogger.write2File("error====ClearDuplicateDataAction 关闭资源异常:" + exception.toString()); exception.printStackTrace(); }
/*     */         
/*     */          }
/*     */     
/*     */     } 
/* 175 */     if (connection != null) {
/*     */       try {
/* 177 */         connection.close();
/* 178 */       } catch (Exception exception) {
/* 179 */         DBUpgradeLogger.write2File("关闭数据库连接错误116");
/* 180 */         DBUpgradeLogger.write2File("error====ClearDuplicateDataAction 关闭资源异常:" + exception.toString());
/* 181 */         exception.printStackTrace();
/*     */       } 
/*     */     }
/* 184 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean clearE7OrE8DuplicateData(List<String> paramList) {
/* 192 */     boolean bool = true;
/* 193 */     UpgradeRecordSet upgradeRecordSet = new UpgradeRecordSet();
/* 194 */     Connection connection = null;
/* 195 */     Statement statement = null;
/* 196 */     ResultSet resultSet = null;
/* 197 */     HashSet<String> hashSet1 = new HashSet();
/* 198 */     HashSet<String> hashSet2 = new HashSet();
/* 199 */     byte b = 1;
/*     */     try {
/* 201 */       for (String str : paramList) {
/* 202 */         hashSet1.clear();
/* 203 */         hashSet2.clear();
/* 204 */         String[] arrayOfString = str.split("\\.");
/* 205 */         DBUpgradeLogger.write2File("表的长度：" + arrayOfString.length);
/* 206 */         if (arrayOfString.length > 1) {
/* 207 */           DBUpgradeLogger.write2File("开始执行清空E7中的数据：" + str);
/* 208 */           str = arrayOfString[0];
/* 209 */           StringBuffer stringBuffer = new StringBuffer();
/* 210 */           for (byte b1 = 1; b1 < arrayOfString.length; b1++) {
/* 211 */             String str2 = arrayOfString[b1];
/* 212 */             if (str2 != null && !"".equals(str2))
/*     */             {
/*     */               
/* 215 */               if (stringBuffer.length() > 0) {
/* 216 */                 stringBuffer.append("," + str2);
/*     */               } else {
/* 218 */                 stringBuffer.append(str2);
/*     */               }  } 
/*     */           } 
/* 221 */           String str1 = "select " + stringBuffer + " from " + str;
/* 222 */           DBUpgradeLogger.write2File("查询数据sql：select " + stringBuffer + " from " + str);
/* 223 */           upgradeRecordSet.executeQuery(str1, new Object[0]);
/* 224 */           connection = this.dbutil.getSourceConnection();
/* 225 */           statement = connection.createStatement();
/* 226 */           while (upgradeRecordSet.next()) {
/* 227 */             StringBuffer stringBuffer1 = new StringBuffer();
/* 228 */             for (byte b2 = 1; b2 < arrayOfString.length; b2++) {
/* 229 */               String str2 = arrayOfString[b2];
/* 230 */               if (str2 != null || !"".equals(str2))
/*     */               {
/*     */                 
/* 233 */                 if (stringBuffer1.length() > 0) {
/* 234 */                   stringBuffer1.append(" and " + str2 + "='" + Util.null2String(upgradeRecordSet.getString(str2) + "' "));
/*     */                 } else {
/* 236 */                   stringBuffer1.append(" " + str2 + "='" + Util.null2String(upgradeRecordSet.getString(str2) + "' "));
/*     */                 }  } 
/*     */             } 
/*     */             try {
/* 240 */               if (stringBuffer1.length() > 0) {
/* 241 */                 statement.execute("delete from " + str + " where " + stringBuffer1);
/*     */               }
/* 243 */             } catch (Exception exception) {
/* 244 */               DBUpgradeLogger.write2File("删除数据异常sql：delete from " + str + " where " + stringBuffer1); break;
/*     */             } 
/*     */           } 
/*     */           continue;
/*     */         } 
/* 249 */         connection = this.dbutil.getSourceConnection();
/* 250 */         statement = connection.createStatement();
/*     */         
/* 252 */         if (str.equalsIgnoreCase("HTMLLABELINFO")) {
/* 253 */           String str1 = "select indexid from " + str;
/* 254 */           upgradeRecordSet.executeQuery(str1, new Object[0]);
/* 255 */           while (upgradeRecordSet.next()) {
/* 256 */             hashSet1.add(upgradeRecordSet.getString("indexid"));
/*     */           }
/* 258 */           resultSet = statement.executeQuery(str1);
/* 259 */           while (resultSet.next()) {
/* 260 */             hashSet2.add(resultSet.getString("indexid"));
/*     */           }
/* 262 */         } else if (str.equalsIgnoreCase("SQLFILELOGINFO")) {
/* 263 */           String str1 = "select sqlfilename from " + str;
/* 264 */           upgradeRecordSet.executeQuery(str1, new Object[0]);
/* 265 */           while (upgradeRecordSet.next()) {
/* 266 */             hashSet1.add(upgradeRecordSet.getString("sqlfilename"));
/*     */           }
/* 268 */           resultSet = statement.executeQuery(str1);
/* 269 */           while (resultSet.next()) {
/* 270 */             hashSet2.add(resultSet.getString("sqlfilename"));
/*     */           }
/*     */         } else {
/* 273 */           DBUpgradeLogger.write2File("==== E7/E8环境中" + str + "表");
/* 274 */           String str1 = "select id from " + str;
/* 275 */           upgradeRecordSet.executeQuery(str1, new Object[0]);
/* 276 */           while (upgradeRecordSet.next()) {
/* 277 */             hashSet1.add(upgradeRecordSet.getString("id"));
/*     */           }
/*     */           try {
/* 280 */             resultSet = statement.executeQuery(str1);
/* 281 */             while (resultSet.next()) {
/* 282 */               hashSet2.add(resultSet.getString("id"));
/*     */             }
/* 284 */           } catch (Exception exception) {
/* 285 */             DBUpgradeLogger.write2File("==== E7/E8环境中" + str + "表不存在");
/*     */           } 
/*     */         } 
/*     */         
/* 289 */         DBUpgradeLogger.write2File("==== E7/E8环境中" + str + "表ID:" + Arrays.toString(hashSet2.toArray()) + "长度为：" + (hashSet2.toArray()).length);
/* 290 */         if (hashSet2.size() > 0) {
/* 291 */           if (hashSet2.size() > 0 && hashSet1.size() > 0) {
/* 292 */             DBUpgradeLogger.write2File("==== E9环境中" + str + "表ID:" + Arrays.toString(hashSet1.toArray()) + "长度为：" + (hashSet1.toArray()).length);
/* 293 */             hashSet1.retainAll(hashSet2);
/* 294 */             DBUpgradeLogger.write2File("==== E7/E8与E9环境中" + str + "表相同的ID:" + Arrays.toString(hashSet1.toArray()) + "长度为：" + (hashSet1.toArray()).length);
/*     */           } 
/* 296 */           DBUpgradeLogger.write2File("==== 开始删除E7/E8环境中" + str + "表ID重复数据");
/*     */           
/* 298 */           for (String str1 : hashSet1) {
/*     */             try {
/* 300 */               if (str.equalsIgnoreCase("HTMLLABELINFO")) {
/* 301 */                 statement.execute("delete from " + str + " where  indexid='" + str1 + "'"); continue;
/* 302 */               }  if (str.equalsIgnoreCase("SQLFILELOGINFO")) {
/* 303 */                 statement.execute("delete from " + str + " where  sqlfilename='" + str1 + "'"); continue;
/*     */               } 
/* 305 */               statement.execute("delete from " + str + " where  id=" + str1);
/*     */             }
/* 307 */             catch (Exception exception) {}
/*     */           } 
/*     */         } 
/*     */ 
/*     */ 
/*     */         
/* 313 */         DBUpgradeLogger.write2File("====删除" + str + "表数据结束");
/* 314 */         double d = MathUtil.div((b * 100), paramList.size(), 1);
/* 315 */         setActionProcess(d + "");
/* 316 */         b++;
/*     */       }
/*     */     
/* 319 */     } catch (Exception exception) {
/* 320 */       bool = false;
/* 321 */       DBUpgradeLogger.write2File("error====ClearDuplicateDataAction 清空E7或E8表重复数据出现异常:" + exception.toString());
/* 322 */       exception.printStackTrace();
/*     */     } finally {
/*     */       try {
/* 325 */         if (resultSet != null) {
/* 326 */           resultSet.close();
/*     */         }
/* 328 */         if (statement != null) {
/* 329 */           statement.close();
/*     */         }
/* 331 */         if (connection != null) {
/* 332 */           connection.close();
/*     */         }
/* 334 */       } catch (Exception exception) {
/* 335 */         DBUpgradeLogger.write2File("error====ClearDuplicateDataAction 关闭资源异常:" + exception.toString());
/* 336 */         exception.printStackTrace();
/*     */       } 
/*     */     } 
/* 339 */     setActionProcess("100");
/* 340 */     return bool;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/upgradetool/dbupgrade/actions/upgrade/ClearDuplicateDataAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */