/*     */ package weaver.upgradetool.dbupgrade.actions.upgrade;
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import java.sql.Connection;
/*     */ import java.sql.ResultSet;
/*     */ import java.sql.SQLException;
/*     */ import java.sql.Statement;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Arrays;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.Set;
/*     */ import java.util.regex.Matcher;
/*     */ import java.util.regex.Pattern;
/*     */ import org.apache.commons.lang.StringUtils;
/*     */ import weaver.general.MathUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.upgradetool.dbupgrade.actions.ActionInterface;
/*     */ import weaver.upgradetool.dbupgrade.actions.ActionProcess;
/*     */ import weaver.upgradetool.dbupgrade.logger.DBUpgradeLogger;
/*     */ import weaver.upgradetool.dbupgrade.upgrade.DBUtil;
/*     */ import weaver.upgradetool.dbupgrade.upgrade.PropUtil;
/*     */ import weaver.upgradetool.dbupgrade.upgrade.UpgradeRecordSet;
/*     */ 
/*     */ public class DropAndCreateTableAction implements ActionInterface {
/*  26 */   private DBUtil dbutil = new DBUtil();
/*  27 */   PropUtil propUtil = PropUtil.getInstance(new String[] { PropUtil.MIGRATION });
/*     */ 
/*     */ 
/*     */   
/*     */   public String execute(HashMap<String, String> paramHashMap) {
/*  32 */     JSONObject jSONObject = new JSONObject();
/*  33 */     startAction(null);
/*     */ 
/*     */     
/*  36 */     DBUpgradeLogger.write2File("====DropAndCreateTableAction 开始获取需要在E9中drop的表");
/*  37 */     List<String> list1 = getDropTableList();
/*  38 */     String str1 = StringUtils.join(list1.toArray(), ",");
/*  39 */     this.propUtil.put("e9_droptables", str1);
/*  40 */     this.propUtil.store();
/*  41 */     DBUpgradeLogger.write2File("====DropAndCreateTableAction 获取需要在E9中drop的表结束");
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  53 */     DBUpgradeLogger.write2File("====DropAndCreateTableAction 开始drop E9中的表");
/*  54 */     UpgradeRecordSet upgradeRecordSet = new UpgradeRecordSet();
/*     */     
/*  56 */     byte b = 1;
/*     */     try {
/*  58 */       Connection connection = this.dbutil.getSourceConnection();
/*  59 */       Statement statement = connection.createStatement();
/*  60 */       for (String str : list1) {
/*     */         try {
/*  62 */           if (!statement.execute("select  * from " + str)) {
/*     */             continue;
/*     */           }
/*  65 */         } catch (Exception exception) {
/*     */           continue;
/*     */         } 
/*     */         
/*  69 */         boolean bool1 = upgradeRecordSet.executeSql("drop table " + str);
/*  70 */         if (bool1) {
/*  71 */           DBUpgradeLogger.write2File("====Drop表:" + str + " 成功");
/*     */         } else {
/*  73 */           DBUpgradeLogger.write2File("====Drop表:" + str + " 失败");
/*     */         } 
/*  75 */         double d = MathUtil.div((b * 20), list1.size(), 1);
/*  76 */         setActionProcess(d + "");
/*  77 */         b++;
/*     */       } 
/*  79 */     } catch (Exception exception) {
/*  80 */       DBUpgradeLogger.write2File("error====DropAndCreateTableAction Drop E9中的表发生异常,异常信息:" + exception.toString());
/*  81 */       jSONObject.put("status", "failure");
/*  82 */       exception.printStackTrace();
/*  83 */       return jSONObject.toJSONString();
/*     */     } 
/*  85 */     DBUpgradeLogger.write2File("====DropAndCreateTableAction Drop E9中的表结束");
/*     */ 
/*     */     
/*  88 */     DBUpgradeLogger.write2File("====DropAndCreateTableAction 在E9中创建表:开始");
/*  89 */     List<String> list2 = getCreateTableList();
/*  90 */     String str2 = StringUtils.join(list2.toArray(), ",");
/*  91 */     this.propUtil.put("e9_createtables", str2);
/*  92 */     this.propUtil.store();
/*  93 */     boolean bool = createTableToE9(list2);
/*  94 */     if (!bool) {
/*  95 */       jSONObject.put("status", "failure");
/*  96 */       return jSONObject.toJSONString();
/*     */     } 
/*  98 */     DBUpgradeLogger.write2File("====DropAndCreateTableAction 在E9中创建表:结束");
/*     */ 
/*     */ 
/*     */     
/* 102 */     jSONObject.put("status", "success");
/* 103 */     endAction(null);
/* 104 */     return jSONObject.toJSONString();
/*     */   }
/*     */ 
/*     */   
/*     */   public void startAction(HashMap<String, String> paramHashMap) {
/* 109 */     ActionProcess.getInstance().setActionProcess("0");
/* 110 */     ActionProcess.getInstance().setActionProcess("startAction");
/* 111 */     DBUpgradeLogger.write2File("=====================startAction:DropAndCreateTableAction=====================");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void endAction(HashMap<String, String> paramHashMap) {
/* 118 */     ActionProcess.getInstance().setActionProcess("100");
/* 119 */     ActionProcess.getInstance().setActionProcess("endAction");
/* 120 */     DBUpgradeLogger.write2File("=====================endAction:DropAndCreateTableAction=====================");
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void setActionProcess(String paramString) {
/* 126 */     ActionProcess.getInstance().setActionProcess(paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void setActionProcessName(String paramString) {
/* 132 */     ActionProcess.getInstance().setActionProcessName(paramString);
/*     */   }
/*     */ 
/*     */   
/*     */   public List<String> getDropTableList() {
/* 137 */     ArrayList<String> arrayList = new ArrayList();
/* 138 */     UpgradeRecordSet upgradeRecordSet = new UpgradeRecordSet();
/* 139 */     String str1 = upgradeRecordSet.getDBType().toLowerCase();
/* 140 */     String str2 = getDropAndCreateSql(str1);
/* 141 */     upgradeRecordSet.execute(str2);
/* 142 */     while (upgradeRecordSet.next()) {
/* 143 */       arrayList.add(upgradeRecordSet.getString("TABLE_NAME").trim());
/*     */     }
/* 145 */     return arrayList;
/*     */   }
/*     */ 
/*     */   
/*     */   public List<String> getCreateTableList() {
/* 150 */     ArrayList<String> arrayList = new ArrayList();
/* 151 */     Statement statement = null;
/* 152 */     Connection connection = null;
/* 153 */     ResultSet resultSet = null;
/* 154 */     String str1 = this.dbutil.getDBtype();
/* 155 */     String str2 = getDropAndCreateSql(str1);
/*     */     try {
/* 157 */       connection = this.dbutil.getSourceConnection();
/* 158 */       statement = connection.createStatement();
/* 159 */       resultSet = statement.executeQuery(str2);
/* 160 */       while (resultSet.next()) {
/* 161 */         arrayList.add(resultSet.getString("TABLE_NAME").trim());
/*     */       }
/* 163 */     } catch (SQLException sQLException) {
/* 164 */       DBUpgradeLogger.write2File("error====DropAndCreateTableAction查询需要在E9中创建的表时发生异常,异常信息:" + sQLException.toString());
/* 165 */       sQLException.printStackTrace();
/*     */     } finally {
/*     */       try {
/* 168 */         if (resultSet != null) {
/* 169 */           resultSet.close();
/*     */         }
/* 171 */         if (statement != null) {
/* 172 */           statement.close();
/*     */         }
/* 174 */         if (connection != null) {
/* 175 */           connection.close();
/*     */         }
/* 177 */       } catch (Exception exception) {
/* 178 */         DBUpgradeLogger.write2File("error===DropAndCreateTableAction 关闭资源异常:异常信息:" + exception.toString());
/* 179 */         exception.printStackTrace();
/*     */       } 
/*     */     } 
/*     */     
/* 183 */     return arrayList;
/*     */   }
/*     */   
/*     */   public boolean createTableToE9(List<String> paramList) {
/* 187 */     boolean bool = true;
/*     */     
/* 189 */     String str = this.dbutil.getDBtype();
/*     */     
/* 191 */     Connection connection = this.dbutil.getSourceConnection();
/* 192 */     UpgradeRecordSet upgradeRecordSet1 = new UpgradeRecordSet();
/* 193 */     UpgradeRecordSet upgradeRecordSet2 = new UpgradeRecordSet();
/* 194 */     UpgradeRecordSet upgradeRecordSet3 = new UpgradeRecordSet();
/* 195 */     Statement statement1 = null;
/* 196 */     ResultSet resultSet1 = null;
/* 197 */     ResultSet resultSet2 = null;
/* 198 */     Statement statement2 = null;
/* 199 */     ResultSet resultSet3 = null;
/* 200 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 201 */     byte b = 1;
/* 202 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/*     */     try {
/* 204 */       statement1 = connection.createStatement();
/* 205 */       statement2 = connection.createStatement();
/* 206 */       if ("oracle".equalsIgnoreCase(str)) {
/*     */         
/* 208 */         resultSet1 = statement1.executeQuery("SELECT T.TABLE_NAME, \n       T.INDEX_NAME, \n       I.UNIQUENESS, \n       I.INDEX_TYPE, \n       C.CONSTRAINT_TYPE, \n       LISTAGG(T.COLUMN_NAME,',') WITHIN GROUP(ORDER BY T.INDEX_NAME) COLS,\n       (CASE\n         WHEN C.CONSTRAINT_TYPE = 'P' OR C.CONSTRAINT_TYPE = 'R' THEN \n          'ALTER TABLE ' || T.TABLE_NAME || ' ADD CONSTRAINT ' ||\n          T.INDEX_NAME || (CASE\n            WHEN C.CONSTRAINT_TYPE = 'P' THEN\n             ' PRIMARY KEY ('\n            ELSE\n             ' FOREIGN KEY ('\n          END) || LISTAGG(T.COLUMN_NAME,',') WITHIN GROUP(ORDER BY T.INDEX_NAME) || ');'\n         ELSE \n          'CREATE ' || (CASE\n            WHEN I.UNIQUENESS = 'UNIQUE' THEN\n             I.UNIQUENESS || ' '\n            ELSE\n             CASE\n               WHEN I.INDEX_TYPE = 'NORMAL' THEN\n                ''\n               ELSE\n                I.INDEX_TYPE || ' '\n             END\n          END) || 'INDEX ' || T.INDEX_NAME || ' ON ' || T.TABLE_NAME || '(' ||\n          LISTAGG(T.COLUMN_NAME,',') WITHIN GROUP(ORDER BY T.INDEX_NAME) || ');'\n       END) SQL_CMD \n  FROM USER_IND_COLUMNS T, USER_INDEXES I, USER_CONSTRAINTS C\n WHERE T.INDEX_NAME = I.INDEX_NAME\n   AND T.INDEX_NAME = C.CONSTRAINT_NAME(+)\n   AND I.INDEX_TYPE != 'FUNCTION-BASED NORMAL'\n GROUP BY T.TABLE_NAME,\n          T.INDEX_NAME,\n          I.UNIQUENESS,\n          I.INDEX_TYPE,\n          C.CONSTRAINT_TYPE");
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 248 */         while (resultSet1.next()) {
/* 249 */           String str1 = Util.null2String(resultSet1.getString("SQL_CMD").trim());
/* 250 */           String str2 = Util.null2String(resultSet1.getString("INDEX_NAME").trim());
/* 251 */           String str3 = Util.null2String(resultSet1.getString("TABLE_NAME").trim());
/* 252 */           if (!"".equals(str2)) {
/* 253 */             upgradeRecordSet2.executeQuery("SELECT 1 FROM USER_INDEXES u where index_name='" + str2 + "'", new Object[0]);
/* 254 */             if (!upgradeRecordSet2.next()) {
/*     */               try {
/* 256 */                 str1 = str1.replace(";", "");
/* 257 */                 DBUpgradeLogger.write2File("====索引SQL:" + str1);
/* 258 */                 if ("".equals(str1)) {
/*     */                   continue;
/*     */                 }
/* 261 */                 HashMap<Object, Object> hashMap = null;
/* 262 */                 if (hashMap2.containsKey(str3.toUpperCase())) {
/* 263 */                   hashMap = (HashMap)hashMap2.get(str3.toUpperCase());
/*     */                 } else {
/* 265 */                   hashMap = new HashMap<>();
/*     */                 } 
/* 267 */                 if (hashMap.containsKey(str2.toUpperCase())) {
/*     */                   continue;
/*     */                 }
/* 270 */                 hashMap.put(str2.toUpperCase(), str1);
/*     */                 
/* 272 */                 hashMap2.put(str3.toUpperCase(), hashMap);
/* 273 */               } catch (Exception exception) {}
/*     */             }
/*     */           } 
/*     */         } 
/*     */       } 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 282 */       for (String str1 : paramList) {
/* 283 */         DBUpgradeLogger.write2File("====开始处理" + str1);
/*     */         
/* 285 */         if ("Oracle".equalsIgnoreCase(str)) {
/* 286 */           upgradeRecordSet1.executeQuery("select table_name From user_tables where  table_name='" + str1.toUpperCase().trim() + "'", new Object[0]);
/* 287 */         } else if ("SqlServer".equalsIgnoreCase(str)) {
/* 288 */           upgradeRecordSet1.executeQuery("select name from sys.tables where name='" + str1.toUpperCase().trim() + "'", new Object[0]);
/*     */         }
/* 290 */         else if ("postgresql".equalsIgnoreCase(str)) {
/* 291 */           upgradeRecordSet1.executeQuery("select relname from pg_class where oid =lower('" + str1 + "')::regclass::oid", new Object[0]);
/*     */         } 
/* 293 */         if (upgradeRecordSet1.next()) {
/* 294 */           DBUpgradeLogger.write2File("====创建表" + str1 + "在e9环境中已存在,无需创建");
/*     */           
/*     */           continue;
/*     */         } 
/* 298 */         StringBuffer stringBuffer = new StringBuffer();
/* 299 */         stringBuffer.append("create table " + str1 + "( ");
/*     */         
/* 301 */         String str2 = "";
/* 302 */         if ("Oracle".equalsIgnoreCase(str)) {
/* 303 */           str2 = "select COLUMN_NAME,DATA_TYPE,DATA_LENGTH,DATA_PRECISION,DATA_SCALE from User_Tab_Columns where table_name ='" + str1.toUpperCase().trim() + "' order by COLUMN_ID";
/*     */         }
/* 305 */         else if ("postgresql".equalsIgnoreCase(str)) {
/*     */           
/* 307 */           str2 = "select COLUMN_NAME,DATA_TYPE,CHARACTER_OCTET_LENGTH as DATA_LENGTH,numeric_precision as  DATA_PRECISION,numeric_scale as DATA_SCALE from Information_schema.columns WHERE table_name = lower('" + str1 + "') order by ordinal_position";
/*     */         }
/* 309 */         else if ("SqlServer".equalsIgnoreCase(str)) {
/* 310 */           str2 = "SELECT SC.NAME AS COLUMN_NAME,ST.NAME AS DATA_TYPE,SC.LENGTH AS DATA_LENGTH,SC.XPREC AS DATA_PRECISION,SC.XSCALE AS DATA_SCALE FROM SYSCOLUMNS SC,SYSTYPES ST WHERE SC.XUSERTYPE=ST.XUSERTYPE AND SC.ID IN(SELECT ID FROM SYSOBJECTS WHERE XTYPE='U' AND NAME='" + str1.toUpperCase().trim() + "')";
/* 311 */           hashMap1.clear();
/* 312 */           resultSet2 = statement1.executeQuery(" SELECT COLUMN_NAME,IS_NULLABLE FROM INFORMATION_SCHEMA.columns WHERE TABLE_NAME='" + str1.toUpperCase().trim() + "' AND  COLUMNPROPERTY(OBJECT_ID('" + str1.toUpperCase().trim() + "'),COLUMN_NAME,'IsIdentity')=1");
/* 313 */           while (resultSet2.next()) {
/* 314 */             hashMap1.put(resultSet2.getString("COLUMN_NAME").trim(), resultSet2.getString("IS_NULLABLE"));
/*     */           }
/*     */         } 
/* 317 */         resultSet1 = statement1.executeQuery(str2);
/* 318 */         boolean bool1 = false;
/* 319 */         while (resultSet1.next()) {
/* 320 */           bool1 = true;
/*     */           
/* 322 */           String str4 = resultSet1.getString("DATA_TYPE");
/* 323 */           String str5 = resultSet1.getString("COLUMN_NAME").trim();
/* 324 */           if ("Oracle".equalsIgnoreCase(str)) {
/* 325 */             if (str4.equalsIgnoreCase("VARCHAR2") || str4.equalsIgnoreCase("NVARCHAR2") || str4.equalsIgnoreCase("CHAR") || str4.equalsIgnoreCase("RAW")) {
/* 326 */               String str6 = Util.null2String(resultSet1.getString("DATA_LENGTH"));
/* 327 */               if ("".equalsIgnoreCase(str6) || "-1".equalsIgnoreCase(str6)) {
/* 328 */                 str6 = "MAX";
/*     */               }
/* 330 */               str4 = str4 + "(" + str6 + ")";
/* 331 */             } else if (str4.equalsIgnoreCase("NUMBER")) {
/* 332 */               String str6 = Util.null2String(resultSet1.getString("DATA_PRECISION"));
/* 333 */               if (str6 != "") {
/* 334 */                 str4 = str4 + "(" + str6 + "," + resultSet1.getString("DATA_SCALE") + ")";
/*     */               }
/* 336 */             } else if (str4.equalsIgnoreCase("FLOAT")) {
/* 337 */               str4 = str4 + "(" + resultSet1.getString("DATA_PRECISION") + ")";
/*     */             } 
/* 339 */           } else if ("SqlServer".equalsIgnoreCase(str)) {
/* 340 */             if (str4.equalsIgnoreCase("CHAR") || str4.equalsIgnoreCase("VARBINARY") || str4.equalsIgnoreCase("VARCHAR")) {
/* 341 */               String str6 = Util.null2String(resultSet1.getString("DATA_LENGTH"));
/* 342 */               if ("".equalsIgnoreCase(str6) || "-1".equalsIgnoreCase(str6)) {
/* 343 */                 str6 = "MAX";
/*     */               }
/* 345 */               str4 = str4 + "(" + str6 + ")";
/* 346 */             } else if (str4.equalsIgnoreCase("NCHAR") || str4.equalsIgnoreCase("NVARCHAR")) {
/* 347 */               str4 = str4 + "(" + (Integer.parseInt(resultSet1.getString("DATA_LENGTH")) / 2) + ")";
/* 348 */             } else if (str4.equalsIgnoreCase("DECIMAL") || str4.equalsIgnoreCase("NUMERIC")) {
/* 349 */               String str6 = Util.null2String(resultSet1.getString("DATA_SCALE"));
/* 350 */               if (str6.equals("0") || str6.equals("")) {
/* 351 */                 str4 = str4 + "(" + resultSet1.getString("DATA_PRECISION") + ")";
/*     */               } else {
/* 353 */                 str4 = str4 + "(" + resultSet1.getString("DATA_PRECISION") + "," + resultSet1.getString("DATA_SCALE") + ")";
/*     */               } 
/*     */             } 
/*     */           } 
/*     */           
/* 358 */           if ("sqlserver".equalsIgnoreCase(str) && hashMap1.containsKey(str5)) {
/* 359 */             String str6 = ((String)hashMap1.get(str5)).equalsIgnoreCase("NO") ? " NOT NULL " : " ";
/* 360 */             stringBuffer.append(str5 + " " + str4 + str6 + " IDENTITY(1,1),"); continue;
/*     */           } 
/* 362 */           stringBuffer.append(str5 + " " + str4 + ",");
/*     */         } 
/*     */         
/* 365 */         if (!bool1) {
/* 366 */           DBUpgradeLogger.write2File("====未找到" + str1 + "在e7环境中不存在,无需创建");
/* 367 */           b++;
/*     */           continue;
/*     */         } 
/* 370 */         String str3 = stringBuffer.substring(0, stringBuffer.lastIndexOf(",")) + ")";
/* 371 */         DBUpgradeLogger.write2File("====创建表开始创建表:" + str1);
/* 372 */         upgradeRecordSet1.executeUpdate(str3, new Object[0]);
/* 373 */         DBUpgradeLogger.write2File("====创建表创建表:" + str1 + "成功");
/*     */         
/* 375 */         if ("Oracle".equalsIgnoreCase(str)) {
/* 376 */           String str4 = "SELECT s.text,s.name FROM USER_SOURCE s left join user_triggers t on upper(t.trigger_name)=upper(s.name) WHERE TYPE='TRIGGER' and t.table_name='" + str1 + "'  ORDER BY LINE ASC";
/*     */           
/* 378 */           String str5 = "";
/* 379 */           resultSet1 = statement1.executeQuery(str4);
/* 380 */           DBUpgradeLogger.write2File("====开始为表:" + str1 + "创建触发器");
/*     */ 
/*     */           
/* 383 */           HashMap<String, String> hashMap = getTiggerSqlsForOracle(resultSet1);
/* 384 */           for (Map.Entry<String, String> entry : hashMap.entrySet()) {
/* 385 */             if ("".equals(Util.null2String((String)entry.getValue()))) {
/*     */               continue;
/*     */             }
/*     */             
/* 389 */             String str6 = "create or replace ";
/* 390 */             str6 = str6 + (String)entry.getValue();
/* 391 */             upgradeRecordSet2.setChecksql(false);
/*     */             
/* 393 */             upgradeRecordSet2.executeSql(str6);
/*     */             
/* 395 */             int i = str6.toUpperCase().indexOf(" select ".toUpperCase()) + " select ".length();
/* 396 */             int j = str6.toUpperCase().indexOf(".nextval".toUpperCase());
/* 397 */             if (i == 7 || j == -1 || i == -1 || i > j) {
/*     */               continue;
/*     */             }
/* 400 */             str5 = str6.substring(i, j).trim().toUpperCase();
/*     */             
/* 402 */             resultSet3 = statement2.executeQuery("select  'create sequence ' ||sequence_name|| ' minvalue ' ||min_value|| ' maxvalue ' ||max_value|| ' start with 1' || ' increment by ' ||increment_by|| ( case  when cache_size= 0  then  ' nocache'   else   ' cache ' ||cache_size end) || ( case  when cycle_flag='N' then  ' nocycle' when cycle_flag='Y' then ' cycle' else   ' cycle ' ||cycle_flag end) || ( case  when order_flag='N' then  ' noorder' when order_flag='Y' then ' order' else   ' order ' ||order_flag end) ,min_value from user_sequences where sequence_name='" + str5 + "'");
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */             
/* 411 */             if (resultSet3.next()) {
/* 412 */               upgradeRecordSet2.executeQuery("select * from user_sequences where sequence_name='" + str5 + "' ", new Object[0]);
/* 413 */               if (upgradeRecordSet2.next()) {
/* 414 */                 upgradeRecordSet3.executeSql("drop sequence " + str5);
/*     */               }
/* 416 */               upgradeRecordSet2.executeUpdate(resultSet3.getString(1), new Object[0]);
/*     */             } 
/*     */           } 
/*     */         } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/*     */         try {
/* 449 */           DBUpgradeLogger.write2File("====开始创建索引表名:" + str1);
/* 450 */           if ("Oracle".equalsIgnoreCase(str)) {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */             
/* 491 */             if (!hashMap2.containsKey(str1.toUpperCase())) {
/*     */               continue;
/*     */             }
/* 494 */             HashMap hashMap = (HashMap)hashMap2.get(str1.toUpperCase());
/* 495 */             if (hashMap == null || hashMap.size() == 0) {
/*     */               continue;
/*     */             }
/* 498 */             DBUpgradeLogger.write2File("====索引SQL的数量:" + hashMap.size());
/* 499 */             Set set = hashMap.keySet();
/* 500 */             for (String str4 : set) {
/* 501 */               String str5 = Util.null2String((String)hashMap.get(str4));
/* 502 */               if ("".equalsIgnoreCase(str5)) {
/*     */                 continue;
/*     */               }
/*     */               
/*     */               try {
/* 507 */                 DBUpgradeLogger.write2File("====索引SQL:" + str5);
/*     */ 
/*     */ 
/*     */                 
/* 511 */                 upgradeRecordSet3.executeUpdate(str5, new Object[0]);
/* 512 */               } catch (Exception exception) {}
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */             
/*     */             }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */           
/*     */           }
/* 537 */           else if ("sqlserver".equalsIgnoreCase(str)) {
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */             
/* 558 */             String str4 = "select indexs.Tab_Name as TABLENAME,indexs.Index_Name as INDEXNAME ,indexs.[Co_Names] as INDEXCOLUMNS,Ind_Attribute.is_primary_key as ISPRIMARYKEY,Ind_Attribute.is_unique AS ISUNIQUE,Ind_Attribute.is_disabled AS ISDISABLED from (select Tab_Name,Index_Name, [Co_Names]=stuff((select ','+[Co_Name] from ( select tab.Name as Tab_Name,ind.Name as Index_Name,Col.Name as Co_Name from sys.indexes ind inner join sys.tables tab on ind.Object_id = tab.object_id and ind.type in (1,2) inner join sys.index_columns index_columns on tab.object_id = index_columns.object_id and ind.index_id = index_columns.index_id inner join sys.columns Col on tab.object_id = Col.object_id and index_columns.column_id = Col.column_id ) t where Tab_Name=tb.Tab_Name and Index_Name=tb.Index_Name for xml path('')), 1, 1, '') from ( select tab.Name as Tab_Name,ind.Name as Index_Name,Col.Name as Co_Name from sys.indexes ind inner join sys.tables tab on ind.Object_id = tab.object_id and ind.type in (1,2) inner join sys.index_columns index_columns on tab.object_id = index_columns.object_id and ind.index_id = index_columns.index_id inner join sys.columns Col on tab.object_id = Col.object_id and index_columns.column_id = Col.column_id )tb where Tab_Name not like 'sys%' group by Tab_Name,Index_Name ) indexs inner join sys.indexes Ind_Attribute on indexs.Index_Name = Ind_Attribute.name where  indexs.Tab_Name ='" + str1.toUpperCase() + "'order by indexs.Tab_Name ";
/*     */             
/* 560 */             resultSet1 = statement1.executeQuery(str4);
/* 561 */             while (resultSet1.next()) {
/* 562 */               String str5 = Util.null2String(resultSet1.getString("INDEXCOLUMNS").trim());
/* 563 */               String str6 = Util.null2String(resultSet1.getString("INDEXNAME").trim());
/* 564 */               String str7 = Util.null2String(resultSet1.getString("ISPRIMARYKEY"), "0");
/* 565 */               String str8 = Util.null2String(resultSet1.getString("ISUNIQUE"), "0");
/*     */               try {
/* 567 */                 upgradeRecordSet2.executeQuery("SELECT * FROM SYSINDEXES WHERE NAME='" + str6.trim() + "'", new Object[0]);
/* 568 */                 if (upgradeRecordSet2.next()) {
/*     */                   continue;
/*     */                 }
/* 571 */                 String str9 = "";
/* 572 */                 if ("1".equals(str7)) {
/* 573 */                   str9 = "alter table  " + str1 + " ADD PRIMARY KEY(" + str5 + ")";
/*     */                 } else {
/* 575 */                   String str10 = "";
/* 576 */                   if ("1".equals(str8)) {
/* 577 */                     str10 = "unique";
/*     */                   }
/* 579 */                   str9 = "create " + str10 + " index " + str6 + " on " + str1 + " (" + str5 + ")";
/*     */                 } 
/* 581 */                 DBUpgradeLogger.write2File("====索引SQL:" + str9);
/* 582 */                 upgradeRecordSet3.executeUpdate(str9, new Object[0]);
/* 583 */               } catch (Exception exception) {
/* 584 */                 DBUpgradeLogger.write2File("====创建索引" + str6 + "出现错误:" + exception.toString());
/*     */               }
/*     */             
/*     */             } 
/*     */           } 
/* 589 */         } catch (Exception exception) {
/* 590 */           exception.printStackTrace();
/* 591 */           DBUpgradeLogger.write2File("====创建索引出现错误:" + exception.toString());
/*     */         } 
/* 593 */         DBUpgradeLogger.write2File("====创建索引结束表名:" + str1);
/*     */ 
/*     */         
/* 596 */         double d = MathUtil.div((b * 80), paramList.size(), 1) + 20.0D;
/* 597 */         setActionProcess(d + "");
/* 598 */         b++;
/*     */       } 
/* 600 */       setActionProcess("100");
/* 601 */     } catch (Exception exception) {
/* 602 */       exception.printStackTrace();
/* 603 */       DBUpgradeLogger.write2File("error====DropAndCreateTableAction创建表发生错误!错误信息:" + exception.getMessage() + "///" + exception.getStackTrace());
/* 604 */       bool = false;
/*     */     } finally {
/*     */       try {
/* 607 */         if (resultSet1 != null) {
/* 608 */           resultSet1.close();
/*     */         }
/* 610 */         if (resultSet3 != null) {
/* 611 */           resultSet3.close();
/*     */         }
/* 613 */         if (statement1 != null) {
/* 614 */           statement1.close();
/*     */         }
/* 616 */         if (statement2 != null) {
/* 617 */           statement2.close();
/*     */         }
/* 619 */         if (connection != null) {
/* 620 */           connection.close();
/*     */         }
/* 622 */       } catch (Exception exception) {
/* 623 */         DBUpgradeLogger.write2File("error===DropAndCreateTableAction 关闭资源异常:异常信息:" + exception.toString());
/* 624 */         exception.printStackTrace();
/*     */       } 
/*     */     } 
/* 627 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getCreateIndexSql(String paramString1, String paramString2, String paramString3) throws Exception {
/* 635 */     String str = "";
/* 636 */     PropUtil propUtil1 = PropUtil.getInstance(new String[] { PropUtil.WEAVER_SOURCE });
/* 637 */     PropUtil propUtil2 = PropUtil.getInstance(new String[] { PropUtil.WEAVER });
/* 638 */     Pattern pattern = Pattern.compile(propUtil1.getValues("username").trim(), 2);
/* 639 */     Matcher matcher = pattern.matcher(paramString1);
/* 640 */     if (matcher.find()) {
/* 641 */       str = matcher.replaceAll(propUtil2.getValues("ecology.user").trim());
/*     */     }
/* 643 */     str = str.replace("\"" + paramString3 + "\"".toUpperCase(), "\"" + paramString2 + "\"".toUpperCase());
/* 644 */     return str;
/*     */   }
/*     */   
/*     */   public HashMap<String, String> getTiggerSqlsForOracle(ResultSet paramResultSet) throws Exception {
/* 648 */     PropUtil propUtil1 = PropUtil.getInstance(new String[] { PropUtil.WEAVER_SOURCE });
/* 649 */     PropUtil propUtil2 = PropUtil.getInstance(new String[] { PropUtil.WEAVER });
/* 650 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 651 */     while (paramResultSet.next()) {
/* 652 */       String str1 = Util.null2String(paramResultSet.getString("text").trim());
/* 653 */       String str2 = Util.null2String(paramResultSet.getString("name").trim());
/* 654 */       if ("".equals(str1)) {
/*     */         continue;
/*     */       }
/*     */       
/* 658 */       hashMap1.put(str2, Util.null2String((String)hashMap1.get(str2)) + " " + str1);
/*     */     } 
/*     */ 
/*     */     
/* 662 */     Pattern pattern = Pattern.compile("([\\S]*?" + propUtil1.getValues("username").trim() + "[\\S]*)", 2);
/* 663 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 664 */     for (String str1 : hashMap1.keySet()) {
/* 665 */       String str2 = (String)hashMap1.get(str1);
/* 666 */       Matcher matcher = pattern.matcher(str2);
/* 667 */       while (matcher.find()) {
/* 668 */         String str = matcher.group();
/* 669 */         str2 = matcher.replaceFirst(str.substring(str.indexOf(".") + 1).replace("\"", "").replace("'", ""));
/* 670 */         matcher = pattern.matcher(str2);
/*     */       } 
/* 672 */       hashMap2.put(str1, str2);
/*     */     } 
/* 674 */     return (HashMap)hashMap2;
/*     */   }
/*     */ 
/*     */   
/*     */   public HashMap<String, String> getTiggerSqlsForSqlserver(ResultSet paramResultSet, String paramString) throws Exception {
/* 679 */     PropUtil propUtil1 = PropUtil.getInstance(new String[] { PropUtil.WEAVER_SOURCE });
/* 680 */     PropUtil propUtil2 = PropUtil.getInstance(new String[] { PropUtil.WEAVER });
/* 681 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 682 */     while (paramResultSet.next()) {
/* 683 */       String str1 = Util.null2String(paramResultSet.getString("text").trim().toUpperCase());
/* 684 */       if (str1.contains(propUtil1.getValues("username").toUpperCase())) {
/* 685 */         str1 = str1.replace(propUtil1.getValues("username").toUpperCase(), propUtil2.getValues("ecology.user").toUpperCase());
/*     */       }
/* 687 */       String str2 = paramString;
/* 688 */       if ("".equals(str1)) {
/*     */         continue;
/*     */       }
/*     */       
/* 692 */       hashMap.put(str2, (Util.null2String((String)hashMap.get(str2)) + " " + str1).replaceAll("\t", " "));
/*     */     } 
/* 694 */     return (HashMap)hashMap;
/*     */   }
/*     */   
/*     */   public String getDropAndCreateSql(String paramString) {
/* 698 */     DBUpgradeLogger.write2File("====getDropAndCreateSql 开始");
/* 699 */     String str1 = "";
/* 700 */     List<String> list1 = Arrays.asList(this.propUtil.getValues("e9_dropandcreatetables").split(","));
/* 701 */     List<String> list2 = Arrays.asList(this.propUtil.getValues("e9_dropandcreatetables_exclude").split(","));
/*     */     
/* 703 */     String str2 = "";
/* 704 */     String str3 = "";
/* 705 */     for (String str : list1) {
/* 706 */       if ("".equals(str))
/* 707 */         continue;  if (paramString.equalsIgnoreCase("oracle")) {
/* 708 */         if (str.contains("%")) {
/* 709 */           str = str.contains("_") ? str.replace("_", "\\_") : str;
/* 710 */           str2 = str2 + "OR TABLE_NAME LIKE '" + str + "' ESCAPE '\\' "; continue;
/*     */         } 
/* 712 */         str3 = str3 + "'" + str + "',"; continue;
/*     */       } 
/* 714 */       if (paramString.equalsIgnoreCase("sqlserver")) {
/* 715 */         if (str.contains("%")) {
/* 716 */           str = str.contains("_") ? str.replace("_", "\\_") : str;
/* 717 */           str2 = str2 + "OR NAME LIKE '" + str + "' ESCAPE '\\' "; continue;
/*     */         } 
/* 719 */         str3 = str3 + "'" + str + "',";
/*     */       } 
/*     */     } 
/*     */     
/* 723 */     if (str2.startsWith("OR")) {
/* 724 */       if (str3.endsWith(",")) {
/* 725 */         str3 = str3.substring(0, str3.length() - 1);
/* 726 */         if (paramString.equalsIgnoreCase("oracle")) {
/* 727 */           str2 = "AND ((" + str2.substring(2) + ") OR TABLE_NAME IN(" + str3 + ")) ";
/* 728 */         } else if (paramString.equalsIgnoreCase("sqlserver")) {
/* 729 */           str2 = "AND ((" + str2.substring(2) + ") OR NAME IN(" + str3 + ")) ";
/*     */         } 
/*     */       } else {
/* 732 */         str2 = "AND (" + str2.substring(2) + ") ";
/*     */       } 
/*     */     }
/*     */     
/* 736 */     String str4 = "";
/* 737 */     for (String str : list2) {
/* 738 */       if ("".equals(str))
/* 739 */         continue;  if (paramString.equalsIgnoreCase("oracle")) {
/* 740 */         str4 = str4 + "AND TABLE_NAME !='" + str + "' "; continue;
/* 741 */       }  if (paramString.equalsIgnoreCase("sqlserver")) {
/* 742 */         str4 = str4 + "AND NAME!='" + str + "' ";
/*     */       }
/*     */     } 
/*     */ 
/*     */     
/* 747 */     if (paramString.equalsIgnoreCase("oracle")) {
/* 748 */       str1 = "SELECT TABLE_NAME FROM USER_TABLES WHERE 1=1 " + str2 + str4;
/* 749 */     } else if (paramString.equalsIgnoreCase("sqlserver")) {
/* 750 */       str1 = "SELECT NAME AS TABLE_NAME FROM SYSOBJECTS WHERE XTYPE='U' " + str2 + str4;
/*     */     } 
/* 752 */     DBUpgradeLogger.write2File("====getDropAndCreateSql 结束，语句为：" + str1);
/* 753 */     return str1;
/*     */   }
/*     */   
/*     */   public boolean test(List<String> paramList) {
/* 757 */     boolean bool = false;
/* 758 */     DBUpgradeLogger.write2File("====DropAndCreateTableAction 开始drop E9中的表_测试!");
/* 759 */     for (String str : paramList) {
/* 760 */       DBUpgradeLogger.write2File("====DropAndCreateTableAction drop E9中的表:" + str + "成功_测试!");
/* 761 */       bool = true;
/*     */     } 
/* 763 */     DBUpgradeLogger.write2File("====DropAndCreateTableAction drop E9中的表完成_测试!");
/*     */     
/* 765 */     DBUpgradeLogger.write2File("====DropAndCreateTableAction 开始在E9中创建表_测试!");
/* 766 */     for (String str : paramList) {
/* 767 */       DBUpgradeLogger.write2File("====DropAndCreateTableAction 在E9中创建表:" + str + "成功_测试!");
/* 768 */       bool = true;
/*     */     } 
/* 770 */     DBUpgradeLogger.write2File("====DropAndCreateTableAction drop 在E9中创建表完成_测试!");
/*     */     
/* 772 */     return bool;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/upgradetool/dbupgrade/actions/upgrade/DropAndCreateTableAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */