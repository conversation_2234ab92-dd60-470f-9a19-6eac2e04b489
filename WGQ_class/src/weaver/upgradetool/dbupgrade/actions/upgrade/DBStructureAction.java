/*    */ package weaver.upgradetool.dbupgrade.actions.upgrade;
/*    */ 
/*    */ import com.alibaba.fastjson.JSONObject;
/*    */ import java.util.HashMap;
/*    */ import weaver.upgradetool.dbupgrade.actions.ActionInterface;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class DBStructureAction
/*    */   implements ActionInterface
/*    */ {
/*    */   public String execute(HashMap<String, String> paramHashMap) {
/* 14 */     startAction(null);
/* 15 */     JSONObject jSONObject = new JSONObject();
/*    */     
/* 17 */     boolean bool = true;
/* 18 */     if (bool) {
/* 19 */       jSONObject.put("status", "success");
/*    */     } else {
/* 21 */       jSONObject.put("status", "failure");
/*    */     } 
/* 23 */     endAction(null);
/* 24 */     return jSONObject.toJSONString();
/*    */   }
/*    */   
/*    */   public void startAction(HashMap<String, String> paramHashMap) {}
/*    */   
/*    */   public void endAction(HashMap<String, String> paramHashMap) {}
/*    */   
/*    */   public void setActionProcess(String paramString) {}
/*    */   
/*    */   public void setActionProcessName(String paramString) {}
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/upgradetool/dbupgrade/actions/upgrade/DBStructureAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */