/*    */ package weaver.upgradetool.dbupgrade.actions.upgrade;
/*    */ 
/*    */ import com.alibaba.fastjson.JSONObject;
/*    */ import java.text.SimpleDateFormat;
/*    */ import java.util.Date;
/*    */ import java.util.HashMap;
/*    */ import weaver.upgradetool.dbupgrade.actions.ActionInterface;
/*    */ import weaver.upgradetool.dbupgrade.logger.DBUpgradeLogger;
/*    */ import weaver.upgradetool.dbupgrade.upgrade.FileOperation;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class DoGenerateSqlFileAction
/*    */   implements ActionInterface
/*    */ {
/*    */   public String execute(HashMap<String, String> paramHashMap) {
/* 26 */     DBUpgradeLogger.write2File("开始执行生成导入导出文件的action");
/* 27 */     Date date = new Date();
/* 28 */     SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
/* 29 */     String str1 = simpleDateFormat.format(date);
/* 30 */     FileOperation fileOperation = new FileOperation();
/* 31 */     String str2 = "ecology_" + str1;
/* 32 */     boolean bool1 = fileOperation.doGenerateExportFile(str2);
/* 33 */     boolean bool2 = fileOperation.doGenerateImportFile(str2);
/* 34 */     JSONObject jSONObject = new JSONObject();
/* 35 */     if (bool1 && bool2) {
/* 36 */       jSONObject.put("status", "success");
/*    */     } else {
/* 38 */       jSONObject.put("status", "failure");
/*    */     } 
/* 40 */     return jSONObject.toJSONString();
/*    */   }
/*    */   
/*    */   public void startAction(HashMap<String, String> paramHashMap) {}
/*    */   
/*    */   public void endAction(HashMap<String, String> paramHashMap) {}
/*    */   
/*    */   public void setActionProcess(String paramString) {}
/*    */   
/*    */   public void setActionProcessName(String paramString) {}
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/upgradetool/dbupgrade/actions/upgrade/DoGenerateSqlFileAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */