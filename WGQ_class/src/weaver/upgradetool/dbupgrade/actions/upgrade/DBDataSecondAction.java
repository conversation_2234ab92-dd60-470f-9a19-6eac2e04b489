/*     */ package weaver.upgradetool.dbupgrade.actions.upgrade;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.fna.maintenance.BudgetfeeTypeComInfo;
/*     */ import weaver.general.Util;
/*     */ import weaver.upgradetool.dbupgrade.actions.ActionInterface;
/*     */ import weaver.upgradetool.dbupgrade.logger.DBUpgradeLogger;
/*     */ import weaver.upgradetool.dbupgrade.upgrade.DBUtil;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class DBDataSecondAction
/*     */   implements ActionInterface
/*     */ {
/*     */   private static final int HTML = 1;
/*     */   private static final int MODE = 2;
/*     */   
/*     */   public String execute(HashMap<String, String> paramHashMap) {
/*  35 */     startAction(null);
/*  36 */     DBUpgradeLogger.write2File("开始执行代码数据调整的操作2");
/*  37 */     JSONObject jSONObject = new JSONObject();
/*     */     
/*  39 */     boolean bool = true;
/*  40 */     RecordSet recordSet = new RecordSet();
/*     */ 
/*     */     
/*  43 */     DBUtil dBUtil = new DBUtil();
/*  44 */     String str1 = Util.null2String(dBUtil.getBeforeLicense());
/*  45 */     if ("".equals(str1)) {
/*  46 */       DBUpgradeLogger.write2File("获取版本号失败");
/*  47 */       jSONObject.put("status", "failure");
/*  48 */       endAction(null);
/*  49 */       return jSONObject.toJSONString();
/*     */     } 
/*  51 */     String str2 = "8";
/*  52 */     String str3 = "0";
/*  53 */     if (str1.indexOf("KB") > -1) {
/*  54 */       str3 = Util.null2String(str1.substring(str1.indexOf("KB") + 2), "0");
/*  55 */     } else if (str1.indexOf(".") > 0) {
/*  56 */       str3 = Util.null2String(str1.substring(0, str1.indexOf(".")), "0");
/*     */     } 
/*  58 */     DBUpgradeLogger.write2File("之前版本：" + str3);
/*     */ 
/*     */ 
/*     */     
/*  62 */     if ("**********".compareTo(str3) > 0) {
/*  63 */       DBUpgradeLogger.write2File("财务科目数据初始化操作");
/*     */       
/*  65 */       recordSet.executeUpdate("delete from fnaInitDataTb", new Object[0]);
/*  66 */       BudgetfeeTypeComInfo budgetfeeTypeComInfo = new BudgetfeeTypeComInfo();
/*  67 */       budgetfeeTypeComInfo.initDataFnaBudgetfeeType();
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  75 */     if (initPrintForm()) {
/*  76 */       DBUpgradeLogger.writeErrorLog2File("初始化流程打印模板成功");
/*     */     } else {
/*  78 */       DBUpgradeLogger.writeErrorLog2File("初始化流程打印模板失败，请访问/api/workflow/nodeformedit/initdata?code=666进行初始化");
/*     */     } 
/*     */     
/*  81 */     if (bool) {
/*  82 */       jSONObject.put("status", "success");
/*     */     } else {
/*  84 */       jSONObject.put("status", "failure");
/*     */     } 
/*  86 */     endAction(null);
/*  87 */     return jSONObject.toJSONString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean initPrintForm() {
/*  99 */     RecordSet recordSet1 = new RecordSet();
/* 100 */     RecordSet recordSet2 = new RecordSet();
/* 101 */     recordSet1.executeUpdate("delete from workflow_printset", new Object[0]);
/* 102 */     byte b = 0;
/*     */     
/* 104 */     ArrayList<Integer> arrayList = new ArrayList();
/*     */     
/* 106 */     recordSet1.executeQuery("select * from workflow_nodemode where isprint=1", new Object[0]);
/* 107 */     while (recordSet1.next()) {
/* 108 */       int k = recordSet1.getInt("workflowid");
/* 109 */       int m = recordSet1.getInt("nodeid");
/* 110 */       int n = recordSet1.getInt("id");
/* 111 */       if (b == 'Ϩ') {
/* 112 */         recordSet2 = new RecordSet();
/* 113 */         b = 0;
/*     */       } 
/* 115 */       boolean bool = true;
/*     */       
/* 117 */       if (arrayList.indexOf(Integer.valueOf(m)) > -1) {
/* 118 */         bool = false;
/*     */       }
/*     */       
/* 121 */       boolean bool1 = createPrintSet(recordSet2, k, m, 2, n, bool);
/* 122 */       if (!bool1) {
/* 123 */         return false;
/*     */       }
/*     */       
/* 126 */       arrayList.add(Integer.valueOf(m));
/*     */     } 
/*     */ 
/*     */     
/* 130 */     b = 0;
/* 131 */     recordSet1 = new RecordSet();
/* 132 */     recordSet1.executeQuery("select max(id) as maxid from workflow_nodehtmllayout", new Object[0]);
/* 133 */     int i = 0;
/* 134 */     if (recordSet1.next()) {
/* 135 */       i = Util.getIntValue(recordSet1.getString("maxid"));
/*     */     }
/*     */     int j;
/* 138 */     for (j = 0; j <= i; j += 5000) {
/* 139 */       int k = j + 5000;
/* 140 */       recordSet1.executeQuery("select workflowid,nodeid,id from workflow_nodehtmllayout where type=1 and isactive=1 and id>=" + j + " and id<" + k + " ", new Object[0]);
/* 141 */       while (recordSet1.next()) {
/*     */         
/* 143 */         if (b == 'Ϩ') {
/* 144 */           recordSet2 = new RecordSet();
/* 145 */           b = 0;
/*     */         } 
/* 147 */         int m = recordSet1.getInt("workflowid");
/* 148 */         int n = recordSet1.getInt("nodeid");
/* 149 */         int i1 = recordSet1.getInt("id");
/* 150 */         boolean bool = true;
/*     */         
/* 152 */         if (arrayList.indexOf(Integer.valueOf(n)) > -1) {
/* 153 */           bool = false;
/*     */         }
/* 155 */         boolean bool1 = createPrintSet(recordSet2, m, n, 1, i1, bool);
/* 156 */         if (!bool1) {
/* 157 */           return false;
/*     */         }
/*     */         
/* 160 */         arrayList.add(Integer.valueOf(n));
/*     */         
/* 162 */         b++;
/*     */       } 
/*     */     } 
/* 165 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean createPrintSet(RecordSet paramRecordSet, int paramInt1, int paramInt2, int paramInt3, int paramInt4, int paramInt5) {
/* 179 */     String str = "insert into workflow_printset(workflowid,nodeid,type,modeid,isactive,printenable) values(?,?,?,?,1,?)";
/* 180 */     return paramRecordSet.executeUpdate(str, new Object[] { Integer.valueOf(paramInt1), Integer.valueOf(paramInt2), Integer.valueOf(paramInt3), Integer.valueOf(paramInt4), Integer.valueOf(paramInt5) });
/*     */   }
/*     */   
/*     */   public void startAction(HashMap<String, String> paramHashMap) {}
/*     */   
/*     */   public void endAction(HashMap<String, String> paramHashMap) {}
/*     */   
/*     */   public void setActionProcess(String paramString) {}
/*     */   
/*     */   public void setActionProcessName(String paramString) {}
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/upgradetool/dbupgrade/actions/upgrade/DBDataSecondAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */