/*     */ package weaver.upgradetool.dbupgrade.actions.upgrade;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import java.io.File;
/*     */ import java.io.FileWriter;
/*     */ import java.io.IOException;
/*     */ import java.io.PrintWriter;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Calendar;
/*     */ import java.util.HashMap;
/*     */ import java.util.Iterator;
/*     */ import java.util.LinkedHashMap;
/*     */ import java.util.Map;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.file.FileManage;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.upgradetool.dbupgrade.actions.ActionInterface;
/*     */ import weaver.upgradetool.dbupgrade.actions.ActionProcess;
/*     */ import weaver.upgradetool.dbupgrade.logger.DBUpgradeLogger;
/*     */ import weaver.upgradetool.dbupgrade.logger.UpgradeLog;
/*     */ import weaver.upgradetool.dbupgrade.upgrade.DBUtil;
/*     */ import weaver.upgradetool.dbupgrade.upgrade.UpgradeRecordSet;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ExecuteSqlAction
/*     */   implements ActionInterface
/*     */ {
/*  45 */   private static StringBuffer errorbuffer = new StringBuffer();
/*  46 */   private UpgradeRecordSet upgradeRecordSet = new UpgradeRecordSet();
/*  47 */   private UpgradeLog upgradeLog = new UpgradeLog();
/*  48 */   String currentDate = TimeUtil.getCurrentDateString();
/*  49 */   String currentTime = TimeUtil.getCurrentTimeString().substring(11);
/*  50 */   DBUtil dbUtil = new DBUtil();
/*  51 */   private String beforeLicense = this.dbUtil.getBeforeLicense();
/*     */ 
/*     */   
/*     */   public String execute(HashMap<String, String> paramHashMap) {
/*  55 */     startAction(null);
/*  56 */     JSONObject jSONObject = new JSONObject();
/*  57 */     boolean bool = executeSql();
/*  58 */     if (bool) {
/*  59 */       jSONObject.put("status", "success");
/*     */     } else {
/*  61 */       jSONObject.put("status", "failure");
/*     */     } 
/*  63 */     endAction(null);
/*  64 */     return jSONObject.toJSONString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean executeSql() {
/*     */     // Byte code:
/*     */     //   0: iconst_1
/*     */     //   1: istore_1
/*     */     //   2: ldc ''
/*     */     //   4: astore #7
/*     */     //   6: iconst_0
/*     */     //   7: istore #8
/*     */     //   9: iconst_0
/*     */     //   10: istore #9
/*     */     //   12: iconst_0
/*     */     //   13: istore #10
/*     */     //   15: new java/lang/StringBuffer
/*     */     //   18: dup
/*     */     //   19: invokespecial <init> : ()V
/*     */     //   22: astore #11
/*     */     //   24: new java/util/ArrayList
/*     */     //   27: dup
/*     */     //   28: invokespecial <init> : ()V
/*     */     //   31: astore #12
/*     */     //   33: ldc ''
/*     */     //   35: astore #13
/*     */     //   37: new java/lang/StringBuffer
/*     */     //   40: dup
/*     */     //   41: invokespecial <init> : ()V
/*     */     //   44: astore #14
/*     */     //   46: new weaver/general/BaseBean
/*     */     //   49: dup
/*     */     //   50: invokespecial <init> : ()V
/*     */     //   53: astore #15
/*     */     //   55: aload #15
/*     */     //   57: ldc 'upgradesetting'
/*     */     //   59: ldc 'checkdata'
/*     */     //   61: invokevirtual getPropValue : (Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;
/*     */     //   64: ldc '1'
/*     */     //   66: invokestatic null2String : (Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;
/*     */     //   69: astore #16
/*     */     //   71: invokestatic getRootPath : ()Ljava/lang/String;
/*     */     //   74: astore #4
/*     */     //   76: new java/lang/StringBuilder
/*     */     //   79: dup
/*     */     //   80: invokespecial <init> : ()V
/*     */     //   83: aload #4
/*     */     //   85: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   88: ldc 'migrationsql'
/*     */     //   90: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   93: getstatic java/io/File.separatorChar : C
/*     */     //   96: invokevirtual append : (C)Ljava/lang/StringBuilder;
/*     */     //   99: invokevirtual toString : ()Ljava/lang/String;
/*     */     //   102: astore #5
/*     */     //   104: new java/lang/StringBuilder
/*     */     //   107: dup
/*     */     //   108: invokespecial <init> : ()V
/*     */     //   111: aload #4
/*     */     //   113: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   116: ldc 'data'
/*     */     //   118: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   121: getstatic java/io/File.separatorChar : C
/*     */     //   124: invokevirtual append : (C)Ljava/lang/StringBuilder;
/*     */     //   127: invokevirtual toString : ()Ljava/lang/String;
/*     */     //   130: astore #6
/*     */     //   132: invokestatic getInstance : ()Lweaver/conn/ConnectionPool;
/*     */     //   135: astore #17
/*     */     //   137: aload #17
/*     */     //   139: invokevirtual getConnection : ()Lweaver/conn/WeaverConnection;
/*     */     //   142: astore #18
/*     */     //   144: new weaver/conn/RecordSet
/*     */     //   147: dup
/*     */     //   148: invokespecial <init> : ()V
/*     */     //   151: astore #19
/*     */     //   153: aload #19
/*     */     //   155: invokevirtual getDBType : ()Ljava/lang/String;
/*     */     //   158: ldc 'mysql'
/*     */     //   160: invokevirtual equals : (Ljava/lang/Object;)Z
/*     */     //   163: istore #20
/*     */     //   165: new java/util/LinkedHashMap
/*     */     //   168: dup
/*     */     //   169: invokespecial <init> : ()V
/*     */     //   172: astore #21
/*     */     //   174: aload #21
/*     */     //   176: ldc 'oracle'
/*     */     //   178: ldc 'Oracle'
/*     */     //   180: invokevirtual put : (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
/*     */     //   183: pop
/*     */     //   184: aload #21
/*     */     //   186: ldc 'sqlserver'
/*     */     //   188: ldc 'SQLServer'
/*     */     //   190: invokevirtual put : (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
/*     */     //   193: pop
/*     */     //   194: aload #21
/*     */     //   196: ldc 'db2'
/*     */     //   198: ldc 'DB2'
/*     */     //   200: invokevirtual put : (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
/*     */     //   203: pop
/*     */     //   204: aload #21
/*     */     //   206: ldc 'mysql'
/*     */     //   208: ldc 'Mysql'
/*     */     //   210: invokevirtual put : (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
/*     */     //   213: pop
/*     */     //   214: aload #21
/*     */     //   216: ldc 'dm'
/*     */     //   218: ldc 'DM'
/*     */     //   220: invokevirtual put : (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
/*     */     //   223: pop
/*     */     //   224: aload #21
/*     */     //   226: ldc 'jc'
/*     */     //   228: ldc 'JC'
/*     */     //   230: invokevirtual put : (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
/*     */     //   233: pop
/*     */     //   234: aload #21
/*     */     //   236: ldc 'pg'
/*     */     //   238: ldc 'PG'
/*     */     //   240: invokevirtual put : (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
/*     */     //   243: pop
/*     */     //   244: aload #21
/*     */     //   246: ldc 'hg'
/*     */     //   248: ldc 'PG'
/*     */     //   250: invokevirtual put : (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
/*     */     //   253: pop
/*     */     //   254: aload #21
/*     */     //   256: ldc 'og'
/*     */     //   258: ldc 'PG'
/*     */     //   260: invokevirtual put : (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
/*     */     //   263: pop
/*     */     //   264: ldc ''
/*     */     //   266: astore #22
/*     */     //   268: ldc ''
/*     */     //   270: astore #23
/*     */     //   272: aload_0
/*     */     //   273: invokevirtual getScriptbreak : ()Ljava/lang/String;
/*     */     //   276: astore #24
/*     */     //   278: aload_0
/*     */     //   279: aload #21
/*     */     //   281: invokevirtual getDBFileName : (Ljava/util/LinkedHashMap;)Ljava/lang/String;
/*     */     //   284: astore #25
/*     */     //   286: new java/lang/StringBuilder
/*     */     //   289: dup
/*     */     //   290: invokespecial <init> : ()V
/*     */     //   293: aload #5
/*     */     //   295: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   298: aload #25
/*     */     //   300: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   303: getstatic java/io/File.separatorChar : C
/*     */     //   306: invokevirtual append : (C)Ljava/lang/StringBuilder;
/*     */     //   309: invokevirtual toString : ()Ljava/lang/String;
/*     */     //   312: astore #22
/*     */     //   314: new java/lang/StringBuilder
/*     */     //   317: dup
/*     */     //   318: invokespecial <init> : ()V
/*     */     //   321: aload #6
/*     */     //   323: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   326: aload #25
/*     */     //   328: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   331: getstatic java/io/File.separatorChar : C
/*     */     //   334: invokevirtual append : (C)Ljava/lang/StringBuilder;
/*     */     //   337: invokevirtual toString : ()Ljava/lang/String;
/*     */     //   340: astore #23
/*     */     //   342: aload_0
/*     */     //   343: aload #21
/*     */     //   345: invokevirtual getLeaveSqlFilePathList : (Ljava/util/LinkedHashMap;)Ljava/util/ArrayList;
/*     */     //   348: astore #26
/*     */     //   350: aconst_null
/*     */     //   351: astore #27
/*     */     //   353: aload #15
/*     */     //   355: invokestatic getConfigFile : ()Ljava/lang/String;
/*     */     //   358: ldc 'DriverClasses'
/*     */     //   360: invokevirtual getPropValue : (Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;
/*     */     //   363: astore #28
/*     */     //   365: invokestatic getServerName : ()Ljava/lang/String;
/*     */     //   368: astore #29
/*     */     //   370: aload #15
/*     */     //   372: invokestatic getConfigFile : ()Ljava/lang/String;
/*     */     //   375: new java/lang/StringBuilder
/*     */     //   378: dup
/*     */     //   379: invokespecial <init> : ()V
/*     */     //   382: aload #29
/*     */     //   384: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   387: ldc '.url'
/*     */     //   389: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   392: invokevirtual toString : ()Ljava/lang/String;
/*     */     //   395: invokevirtual getPropValue : (Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;
/*     */     //   398: astore #30
/*     */     //   400: aload #15
/*     */     //   402: invokestatic getConfigFile : ()Ljava/lang/String;
/*     */     //   405: new java/lang/StringBuilder
/*     */     //   408: dup
/*     */     //   409: invokespecial <init> : ()V
/*     */     //   412: aload #29
/*     */     //   414: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   417: ldc '.user'
/*     */     //   419: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   422: invokevirtual toString : ()Ljava/lang/String;
/*     */     //   425: invokevirtual getPropValue : (Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;
/*     */     //   428: astore #31
/*     */     //   430: aload #15
/*     */     //   432: invokestatic getConfigFile : ()Ljava/lang/String;
/*     */     //   435: new java/lang/StringBuilder
/*     */     //   438: dup
/*     */     //   439: invokespecial <init> : ()V
/*     */     //   442: aload #29
/*     */     //   444: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   447: ldc '.password'
/*     */     //   449: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   452: invokevirtual toString : ()Ljava/lang/String;
/*     */     //   455: invokevirtual getPropValue : (Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;
/*     */     //   458: astore #32
/*     */     //   460: aload #28
/*     */     //   462: invokestatic forName : (Ljava/lang/String;)Ljava/lang/Class;
/*     */     //   465: pop
/*     */     //   466: new java/util/Properties
/*     */     //   469: dup
/*     */     //   470: invokespecial <init> : ()V
/*     */     //   473: astore #33
/*     */     //   475: aload #33
/*     */     //   477: ldc 'user'
/*     */     //   479: aload #31
/*     */     //   481: invokevirtual put : (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
/*     */     //   484: pop
/*     */     //   485: aload #33
/*     */     //   487: ldc 'password'
/*     */     //   489: aload #32
/*     */     //   491: invokevirtual put : (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
/*     */     //   494: pop
/*     */     //   495: aload #33
/*     */     //   497: ldc 'CHARSET'
/*     */     //   499: ldc 'ISO'
/*     */     //   501: invokevirtual put : (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
/*     */     //   504: pop
/*     */     //   505: aload #30
/*     */     //   507: aload #33
/*     */     //   509: invokestatic getConnection : (Ljava/lang/String;Ljava/util/Properties;)Ljava/sql/Connection;
/*     */     //   512: astore #27
/*     */     //   514: aload #27
/*     */     //   516: invokeinterface createStatement : ()Ljava/sql/Statement;
/*     */     //   521: astore #34
/*     */     //   523: new java/io/File
/*     */     //   526: dup
/*     */     //   527: aload #22
/*     */     //   529: invokespecial <init> : (Ljava/lang/String;)V
/*     */     //   532: astore #35
/*     */     //   534: aload #35
/*     */     //   536: invokevirtual list : ()[Ljava/lang/String;
/*     */     //   539: astore #36
/*     */     //   541: iconst_0
/*     */     //   542: istore #37
/*     */     //   544: iload #37
/*     */     //   546: aload #26
/*     */     //   548: invokevirtual size : ()I
/*     */     //   551: if_icmpge -> 752
/*     */     //   554: aload #26
/*     */     //   556: iload #37
/*     */     //   558: invokevirtual get : (I)Ljava/lang/Object;
/*     */     //   561: checkcast java/lang/String
/*     */     //   564: astore #38
/*     */     //   566: new java/io/File
/*     */     //   569: dup
/*     */     //   570: new java/lang/StringBuilder
/*     */     //   573: dup
/*     */     //   574: invokespecial <init> : ()V
/*     */     //   577: aload #5
/*     */     //   579: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   582: aload #38
/*     */     //   584: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   587: invokevirtual toString : ()Ljava/lang/String;
/*     */     //   590: invokespecial <init> : (Ljava/lang/String;)V
/*     */     //   593: astore #39
/*     */     //   595: aload #39
/*     */     //   597: invokevirtual list : ()[Ljava/lang/String;
/*     */     //   600: astore #40
/*     */     //   602: aload #40
/*     */     //   604: ifnull -> 746
/*     */     //   607: aload #40
/*     */     //   609: arraylength
/*     */     //   610: ifle -> 746
/*     */     //   613: iconst_0
/*     */     //   614: istore #41
/*     */     //   616: iload #41
/*     */     //   618: aload #40
/*     */     //   620: arraylength
/*     */     //   621: if_icmpge -> 746
/*     */     //   624: aload #40
/*     */     //   626: iload #41
/*     */     //   628: aaload
/*     */     //   629: invokestatic null2String : (Ljava/lang/String;)Ljava/lang/String;
/*     */     //   632: astore #42
/*     */     //   634: new java/lang/StringBuilder
/*     */     //   637: dup
/*     */     //   638: invokespecial <init> : ()V
/*     */     //   641: aload #5
/*     */     //   643: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   646: aload #38
/*     */     //   648: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   651: getstatic java/io/File.separatorChar : C
/*     */     //   654: invokevirtual append : (C)Ljava/lang/StringBuilder;
/*     */     //   657: aload #42
/*     */     //   659: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   662: invokevirtual toString : ()Ljava/lang/String;
/*     */     //   665: astore #43
/*     */     //   667: new java/lang/StringBuilder
/*     */     //   670: dup
/*     */     //   671: invokespecial <init> : ()V
/*     */     //   674: aload #6
/*     */     //   676: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   679: aload #38
/*     */     //   681: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   684: getstatic java/io/File.separatorChar : C
/*     */     //   687: invokevirtual append : (C)Ljava/lang/StringBuilder;
/*     */     //   690: aload #42
/*     */     //   692: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   695: invokevirtual toString : ()Ljava/lang/String;
/*     */     //   698: astore #44
/*     */     //   700: aload #42
/*     */     //   702: ldc 'sql'
/*     */     //   704: invokevirtual indexOf : (Ljava/lang/String;)I
/*     */     //   707: iflt -> 725
/*     */     //   710: new java/io/File
/*     */     //   713: dup
/*     */     //   714: aload #44
/*     */     //   716: invokespecial <init> : (Ljava/lang/String;)V
/*     */     //   719: invokevirtual exists : ()Z
/*     */     //   722: ifeq -> 733
/*     */     //   725: aload #43
/*     */     //   727: invokestatic DeleteFile : (Ljava/lang/String;)V
/*     */     //   730: goto -> 740
/*     */     //   733: aload #43
/*     */     //   735: aload #44
/*     */     //   737: invokestatic moveFileTo : (Ljava/lang/String;Ljava/lang/String;)V
/*     */     //   740: iinc #41, 1
/*     */     //   743: goto -> 616
/*     */     //   746: iinc #37, 1
/*     */     //   749: goto -> 544
/*     */     //   752: aload #36
/*     */     //   754: arraylength
/*     */     //   755: istore #9
/*     */     //   757: goto -> 767
/*     */     //   760: astore #37
/*     */     //   762: aload #37
/*     */     //   764: invokevirtual printStackTrace : ()V
/*     */     //   767: aload #19
/*     */     //   769: ldc 'select * from DBUpgradeSqlCounter'
/*     */     //   771: iconst_0
/*     */     //   772: anewarray java/lang/Object
/*     */     //   775: invokevirtual executeQuery : (Ljava/lang/String;[Ljava/lang/Object;)Z
/*     */     //   778: pop
/*     */     //   779: aload #19
/*     */     //   781: invokevirtual next : ()Z
/*     */     //   784: ifeq -> 805
/*     */     //   787: aload #19
/*     */     //   789: ldc 'runfilename'
/*     */     //   791: invokevirtual getString : (Ljava/lang/String;)Ljava/lang/String;
/*     */     //   794: astore #7
/*     */     //   796: aload #19
/*     */     //   798: ldc 'errorline'
/*     */     //   800: invokevirtual getInt : (Ljava/lang/String;)I
/*     */     //   803: istore #8
/*     */     //   805: ldc ''
/*     */     //   807: astore #37
/*     */     //   809: aload #19
/*     */     //   811: ldc 'select checkdburl from UpgradeCheckInfo'
/*     */     //   813: invokevirtual execute : (Ljava/lang/String;)Z
/*     */     //   816: pop
/*     */     //   817: aload #19
/*     */     //   819: invokevirtual next : ()Z
/*     */     //   822: ifeq -> 834
/*     */     //   825: aload #19
/*     */     //   827: ldc 'checkdburl'
/*     */     //   829: invokevirtual getString : (Ljava/lang/String;)Ljava/lang/String;
/*     */     //   832: astore #37
/*     */     //   834: aload #36
/*     */     //   836: ifnull -> 3587
/*     */     //   839: aload #36
/*     */     //   841: arraylength
/*     */     //   842: ifle -> 3587
/*     */     //   845: iconst_1
/*     */     //   846: istore #10
/*     */     //   848: aload_0
/*     */     //   849: iconst_2
/*     */     //   850: iload #10
/*     */     //   852: iload #9
/*     */     //   854: ldc ''
/*     */     //   856: iload #8
/*     */     //   858: invokevirtual changeStatus : (IIILjava/lang/String;I)V
/*     */     //   861: aload #19
/*     */     //   863: ldc 'select sqlfilename from SqlFileLogInfo'
/*     */     //   865: invokevirtual executeSql : (Ljava/lang/String;)Z
/*     */     //   868: pop
/*     */     //   869: aload #19
/*     */     //   871: invokevirtual next : ()Z
/*     */     //   874: ifeq -> 895
/*     */     //   877: aload #12
/*     */     //   879: aload #19
/*     */     //   881: ldc 'sqlfilename'
/*     */     //   883: invokevirtual getString : (Ljava/lang/String;)Ljava/lang/String;
/*     */     //   886: invokeinterface add : (Ljava/lang/Object;)Z
/*     */     //   891: pop
/*     */     //   892: goto -> 869
/*     */     //   895: iconst_0
/*     */     //   896: istore #38
/*     */     //   898: iload #38
/*     */     //   900: aload #36
/*     */     //   902: arraylength
/*     */     //   903: iconst_1
/*     */     //   904: isub
/*     */     //   905: if_icmpge -> 974
/*     */     //   908: iload #38
/*     */     //   910: iconst_1
/*     */     //   911: iadd
/*     */     //   912: istore #39
/*     */     //   914: iload #39
/*     */     //   916: aload #36
/*     */     //   918: arraylength
/*     */     //   919: if_icmpge -> 968
/*     */     //   922: aload #36
/*     */     //   924: iload #38
/*     */     //   926: aaload
/*     */     //   927: aload #36
/*     */     //   929: iload #39
/*     */     //   931: aaload
/*     */     //   932: invokevirtual compareTo : (Ljava/lang/String;)I
/*     */     //   935: ifle -> 962
/*     */     //   938: aload #36
/*     */     //   940: iload #38
/*     */     //   942: aaload
/*     */     //   943: astore #40
/*     */     //   945: aload #36
/*     */     //   947: iload #38
/*     */     //   949: aload #36
/*     */     //   951: iload #39
/*     */     //   953: aaload
/*     */     //   954: aastore
/*     */     //   955: aload #36
/*     */     //   957: iload #39
/*     */     //   959: aload #40
/*     */     //   961: aastore
/*     */     //   962: iinc #39, 1
/*     */     //   965: goto -> 914
/*     */     //   968: iinc #38, 1
/*     */     //   971: goto -> 898
/*     */     //   974: iconst_0
/*     */     //   975: istore #38
/*     */     //   977: iload #38
/*     */     //   979: aload #36
/*     */     //   981: arraylength
/*     */     //   982: if_icmpge -> 1637
/*     */     //   985: aload #36
/*     */     //   987: iload #38
/*     */     //   989: aaload
/*     */     //   990: invokestatic null2String : (Ljava/lang/String;)Ljava/lang/String;
/*     */     //   993: astore #39
/*     */     //   995: new java/lang/StringBuilder
/*     */     //   998: dup
/*     */     //   999: invokespecial <init> : ()V
/*     */     //   1002: aload #22
/*     */     //   1004: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   1007: aload #39
/*     */     //   1009: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   1012: invokevirtual toString : ()Ljava/lang/String;
/*     */     //   1015: astore #40
/*     */     //   1017: new java/lang/StringBuilder
/*     */     //   1020: dup
/*     */     //   1021: invokespecial <init> : ()V
/*     */     //   1024: aload #23
/*     */     //   1026: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   1029: aload #39
/*     */     //   1031: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   1034: invokevirtual toString : ()Ljava/lang/String;
/*     */     //   1037: astore #41
/*     */     //   1039: aload #39
/*     */     //   1041: astore #13
/*     */     //   1043: aload #39
/*     */     //   1045: ldc 'sql'
/*     */     //   1047: invokevirtual indexOf : (Ljava/lang/String;)I
/*     */     //   1050: ifge -> 1226
/*     */     //   1053: new java/io/File
/*     */     //   1056: dup
/*     */     //   1057: aload #40
/*     */     //   1059: invokespecial <init> : (Ljava/lang/String;)V
/*     */     //   1062: astore #42
/*     */     //   1064: aload #42
/*     */     //   1066: invokevirtual isDirectory : ()Z
/*     */     //   1069: ifeq -> 1156
/*     */     //   1072: aload #42
/*     */     //   1074: invokestatic deleteDirectory : (Ljava/io/File;)V
/*     */     //   1077: getstatic java/lang/System.out : Ljava/io/PrintStream;
/*     */     //   1080: new java/lang/StringBuilder
/*     */     //   1083: dup
/*     */     //   1084: invokespecial <init> : ()V
/*     */     //   1087: ldc '删除目录'
/*     */     //   1089: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   1092: aload #42
/*     */     //   1094: invokevirtual getName : ()Ljava/lang/String;
/*     */     //   1097: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   1100: invokevirtual toString : ()Ljava/lang/String;
/*     */     //   1103: invokevirtual println : (Ljava/lang/String;)V
/*     */     //   1106: aload_0
/*     */     //   1107: new java/lang/StringBuilder
/*     */     //   1110: dup
/*     */     //   1111: invokespecial <init> : ()V
/*     */     //   1114: ldc '删除目录'
/*     */     //   1116: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   1119: aload #42
/*     */     //   1121: invokevirtual getName : ()Ljava/lang/String;
/*     */     //   1124: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   1127: invokevirtual toString : ()Ljava/lang/String;
/*     */     //   1130: invokevirtual writeLog : (Ljava/lang/String;)V
/*     */     //   1133: iload #10
/*     */     //   1135: iconst_1
/*     */     //   1136: iadd
/*     */     //   1137: istore #10
/*     */     //   1139: goto -> 1631
/*     */     //   1142: astore #43
/*     */     //   1144: aload_0
/*     */     //   1145: aload #43
/*     */     //   1147: invokevirtual toString : ()Ljava/lang/String;
/*     */     //   1150: invokevirtual writeLog : (Ljava/lang/String;)V
/*     */     //   1153: goto -> 1226
/*     */     //   1156: aload #40
/*     */     //   1158: invokestatic DeleteFile : (Ljava/lang/String;)V
/*     */     //   1161: getstatic java/lang/System.out : Ljava/io/PrintStream;
/*     */     //   1164: new java/lang/StringBuilder
/*     */     //   1167: dup
/*     */     //   1168: invokespecial <init> : ()V
/*     */     //   1171: ldc '删除文件'
/*     */     //   1173: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   1176: aload #42
/*     */     //   1178: invokevirtual getName : ()Ljava/lang/String;
/*     */     //   1181: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   1184: invokevirtual toString : ()Ljava/lang/String;
/*     */     //   1187: invokevirtual println : (Ljava/lang/String;)V
/*     */     //   1190: aload_0
/*     */     //   1191: new java/lang/StringBuilder
/*     */     //   1194: dup
/*     */     //   1195: invokespecial <init> : ()V
/*     */     //   1198: ldc '删除文件'
/*     */     //   1200: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   1203: aload #42
/*     */     //   1205: invokevirtual getName : ()Ljava/lang/String;
/*     */     //   1208: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   1211: invokevirtual toString : ()Ljava/lang/String;
/*     */     //   1214: invokevirtual writeLog : (Ljava/lang/String;)V
/*     */     //   1217: iload #10
/*     */     //   1219: iconst_1
/*     */     //   1220: iadd
/*     */     //   1221: istore #10
/*     */     //   1223: goto -> 1631
/*     */     //   1226: ldc '0'
/*     */     //   1228: aload #16
/*     */     //   1230: invokevirtual equals : (Ljava/lang/Object;)Z
/*     */     //   1233: ifeq -> 1364
/*     */     //   1236: aload #12
/*     */     //   1238: aload #39
/*     */     //   1240: invokeinterface contains : (Ljava/lang/Object;)Z
/*     */     //   1245: ifeq -> 1631
/*     */     //   1248: aload #40
/*     */     //   1250: aload #41
/*     */     //   1252: invokestatic moveFileTo : (Ljava/lang/String;Ljava/lang/String;)V
/*     */     //   1255: getstatic java/lang/System.out : Ljava/io/PrintStream;
/*     */     //   1258: new java/lang/StringBuilder
/*     */     //   1261: dup
/*     */     //   1262: invokespecial <init> : ()V
/*     */     //   1265: ldc '正在执行'
/*     */     //   1267: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   1270: aload #13
/*     */     //   1272: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   1275: ldc ',第('
/*     */     //   1277: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   1280: iload #10
/*     */     //   1282: invokevirtual append : (I)Ljava/lang/StringBuilder;
/*     */     //   1285: ldc ')个脚本，共('
/*     */     //   1287: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   1290: iload #9
/*     */     //   1292: invokevirtual append : (I)Ljava/lang/StringBuilder;
/*     */     //   1295: ldc ')个。数据库已执行过该脚本。'
/*     */     //   1297: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   1300: invokevirtual toString : ()Ljava/lang/String;
/*     */     //   1303: invokevirtual println : (Ljava/lang/String;)V
/*     */     //   1306: aload_0
/*     */     //   1307: new java/lang/StringBuilder
/*     */     //   1310: dup
/*     */     //   1311: invokespecial <init> : ()V
/*     */     //   1314: ldc '正在执行'
/*     */     //   1316: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   1319: aload #13
/*     */     //   1321: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   1324: ldc ',第('
/*     */     //   1326: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   1329: iload #10
/*     */     //   1331: invokevirtual append : (I)Ljava/lang/StringBuilder;
/*     */     //   1334: ldc ')个脚本，共('
/*     */     //   1336: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   1339: iload #9
/*     */     //   1341: invokevirtual append : (I)Ljava/lang/StringBuilder;
/*     */     //   1344: ldc ')个。数据库已执行过该脚本。'
/*     */     //   1346: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   1349: invokevirtual toString : ()Ljava/lang/String;
/*     */     //   1352: invokevirtual writeLog : (Ljava/lang/String;)V
/*     */     //   1355: iload #10
/*     */     //   1357: iconst_1
/*     */     //   1358: iadd
/*     */     //   1359: istore #10
/*     */     //   1361: goto -> 1631
/*     */     //   1364: aload #39
/*     */     //   1366: ldc 'sql'
/*     */     //   1368: invokevirtual indexOf : (Ljava/lang/String;)I
/*     */     //   1371: iflt -> 1389
/*     */     //   1374: new java/io/File
/*     */     //   1377: dup
/*     */     //   1378: aload #41
/*     */     //   1380: invokespecial <init> : (Ljava/lang/String;)V
/*     */     //   1383: invokevirtual exists : ()Z
/*     */     //   1386: ifeq -> 1503
/*     */     //   1389: aload #40
/*     */     //   1391: invokestatic DeleteFile : (Ljava/lang/String;)V
/*     */     //   1394: getstatic java/lang/System.out : Ljava/io/PrintStream;
/*     */     //   1397: new java/lang/StringBuilder
/*     */     //   1400: dup
/*     */     //   1401: invokespecial <init> : ()V
/*     */     //   1404: ldc '正在执行'
/*     */     //   1406: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   1409: aload #13
/*     */     //   1411: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   1414: ldc ',第('
/*     */     //   1416: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   1419: iload #10
/*     */     //   1421: invokevirtual append : (I)Ljava/lang/StringBuilder;
/*     */     //   1424: ldc ')个脚本，共('
/*     */     //   1426: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   1429: iload #9
/*     */     //   1431: invokevirtual append : (I)Ljava/lang/StringBuilder;
/*     */     //   1434: ldc ')个。data目录已存在该脚本。'
/*     */     //   1436: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   1439: invokevirtual toString : ()Ljava/lang/String;
/*     */     //   1442: invokevirtual println : (Ljava/lang/String;)V
/*     */     //   1445: aload_0
/*     */     //   1446: new java/lang/StringBuilder
/*     */     //   1449: dup
/*     */     //   1450: invokespecial <init> : ()V
/*     */     //   1453: ldc '正在执行'
/*     */     //   1455: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   1458: aload #13
/*     */     //   1460: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   1463: ldc ',第('
/*     */     //   1465: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   1468: iload #10
/*     */     //   1470: invokevirtual append : (I)Ljava/lang/StringBuilder;
/*     */     //   1473: ldc ')个脚本，共('
/*     */     //   1475: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   1478: iload #9
/*     */     //   1480: invokevirtual append : (I)Ljava/lang/StringBuilder;
/*     */     //   1483: ldc ')个。data目录已存在该脚本。'
/*     */     //   1485: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   1488: invokevirtual toString : ()Ljava/lang/String;
/*     */     //   1491: invokevirtual writeLog : (Ljava/lang/String;)V
/*     */     //   1494: iload #10
/*     */     //   1496: iconst_1
/*     */     //   1497: iadd
/*     */     //   1498: istore #10
/*     */     //   1500: goto -> 1631
/*     */     //   1503: aload #12
/*     */     //   1505: aload #39
/*     */     //   1507: invokeinterface contains : (Ljava/lang/Object;)Z
/*     */     //   1512: ifeq -> 1631
/*     */     //   1515: aload #40
/*     */     //   1517: aload #41
/*     */     //   1519: invokestatic moveFileTo : (Ljava/lang/String;Ljava/lang/String;)V
/*     */     //   1522: getstatic java/lang/System.out : Ljava/io/PrintStream;
/*     */     //   1525: new java/lang/StringBuilder
/*     */     //   1528: dup
/*     */     //   1529: invokespecial <init> : ()V
/*     */     //   1532: ldc '正在执行'
/*     */     //   1534: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   1537: aload #13
/*     */     //   1539: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   1542: ldc ',第('
/*     */     //   1544: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   1547: iload #10
/*     */     //   1549: invokevirtual append : (I)Ljava/lang/StringBuilder;
/*     */     //   1552: ldc ')个脚本，共('
/*     */     //   1554: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   1557: iload #9
/*     */     //   1559: invokevirtual append : (I)Ljava/lang/StringBuilder;
/*     */     //   1562: ldc ')个。数据库已执行过该脚本。'
/*     */     //   1564: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   1567: invokevirtual toString : ()Ljava/lang/String;
/*     */     //   1570: invokevirtual println : (Ljava/lang/String;)V
/*     */     //   1573: aload_0
/*     */     //   1574: new java/lang/StringBuilder
/*     */     //   1577: dup
/*     */     //   1578: invokespecial <init> : ()V
/*     */     //   1581: ldc '正在执行'
/*     */     //   1583: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   1586: aload #13
/*     */     //   1588: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   1591: ldc ',第('
/*     */     //   1593: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   1596: iload #10
/*     */     //   1598: invokevirtual append : (I)Ljava/lang/StringBuilder;
/*     */     //   1601: ldc ')个脚本，共('
/*     */     //   1603: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   1606: iload #9
/*     */     //   1608: invokevirtual append : (I)Ljava/lang/StringBuilder;
/*     */     //   1611: ldc ')个。数据库已执行过该脚本。'
/*     */     //   1613: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   1616: invokevirtual toString : ()Ljava/lang/String;
/*     */     //   1619: invokevirtual writeLog : (Ljava/lang/String;)V
/*     */     //   1622: iload #10
/*     */     //   1624: iconst_1
/*     */     //   1625: iadd
/*     */     //   1626: istore #10
/*     */     //   1628: goto -> 1631
/*     */     //   1631: iinc #38, 1
/*     */     //   1634: goto -> 977
/*     */     //   1637: aload #35
/*     */     //   1639: invokevirtual list : ()[Ljava/lang/String;
/*     */     //   1642: astore #36
/*     */     //   1644: iconst_0
/*     */     //   1645: istore #38
/*     */     //   1647: iload #38
/*     */     //   1649: aload #36
/*     */     //   1651: arraylength
/*     */     //   1652: iconst_1
/*     */     //   1653: isub
/*     */     //   1654: if_icmpge -> 1723
/*     */     //   1657: iload #38
/*     */     //   1659: iconst_1
/*     */     //   1660: iadd
/*     */     //   1661: istore #39
/*     */     //   1663: iload #39
/*     */     //   1665: aload #36
/*     */     //   1667: arraylength
/*     */     //   1668: if_icmpge -> 1717
/*     */     //   1671: aload #36
/*     */     //   1673: iload #38
/*     */     //   1675: aaload
/*     */     //   1676: aload #36
/*     */     //   1678: iload #39
/*     */     //   1680: aaload
/*     */     //   1681: invokevirtual compareTo : (Ljava/lang/String;)I
/*     */     //   1684: ifle -> 1711
/*     */     //   1687: aload #36
/*     */     //   1689: iload #38
/*     */     //   1691: aaload
/*     */     //   1692: astore #40
/*     */     //   1694: aload #36
/*     */     //   1696: iload #38
/*     */     //   1698: aload #36
/*     */     //   1700: iload #39
/*     */     //   1702: aaload
/*     */     //   1703: aastore
/*     */     //   1704: aload #36
/*     */     //   1706: iload #39
/*     */     //   1708: aload #40
/*     */     //   1710: aastore
/*     */     //   1711: iinc #39, 1
/*     */     //   1714: goto -> 1663
/*     */     //   1717: iinc #38, 1
/*     */     //   1720: goto -> 1647
/*     */     //   1723: iload #10
/*     */     //   1725: iconst_1
/*     */     //   1726: if_icmplt -> 1735
/*     */     //   1729: iload #10
/*     */     //   1731: iconst_1
/*     */     //   1732: isub
/*     */     //   1733: istore #10
/*     */     //   1735: invokestatic currentTimeMillis : ()J
/*     */     //   1738: lstore #38
/*     */     //   1740: iconst_0
/*     */     //   1741: istore #40
/*     */     //   1743: iload #40
/*     */     //   1745: aload #36
/*     */     //   1747: arraylength
/*     */     //   1748: if_icmpge -> 3577
/*     */     //   1751: iload #10
/*     */     //   1753: iconst_1
/*     */     //   1754: iadd
/*     */     //   1755: istore #10
/*     */     //   1757: aload #36
/*     */     //   1759: iload #40
/*     */     //   1761: aaload
/*     */     //   1762: invokestatic null2String : (Ljava/lang/String;)Ljava/lang/String;
/*     */     //   1765: astore #41
/*     */     //   1767: new java/lang/StringBuilder
/*     */     //   1770: dup
/*     */     //   1771: invokespecial <init> : ()V
/*     */     //   1774: aload #22
/*     */     //   1776: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   1779: aload #41
/*     */     //   1781: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   1784: invokevirtual toString : ()Ljava/lang/String;
/*     */     //   1787: astore #42
/*     */     //   1789: new java/lang/StringBuilder
/*     */     //   1792: dup
/*     */     //   1793: invokespecial <init> : ()V
/*     */     //   1796: aload #23
/*     */     //   1798: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   1801: aload #41
/*     */     //   1803: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   1806: invokevirtual toString : ()Ljava/lang/String;
/*     */     //   1809: astore #43
/*     */     //   1811: aload #41
/*     */     //   1813: ldc 'sql'
/*     */     //   1815: invokevirtual indexOf : (Ljava/lang/String;)I
/*     */     //   1818: ifge -> 1991
/*     */     //   1821: new java/io/File
/*     */     //   1824: dup
/*     */     //   1825: aload #42
/*     */     //   1827: invokespecial <init> : (Ljava/lang/String;)V
/*     */     //   1830: astore #44
/*     */     //   1832: aload #44
/*     */     //   1834: invokevirtual isDirectory : ()Z
/*     */     //   1837: ifeq -> 1927
/*     */     //   1840: aload #44
/*     */     //   1842: invokestatic deleteDirectory : (Ljava/io/File;)V
/*     */     //   1845: getstatic java/lang/System.out : Ljava/io/PrintStream;
/*     */     //   1848: new java/lang/StringBuilder
/*     */     //   1851: dup
/*     */     //   1852: invokespecial <init> : ()V
/*     */     //   1855: ldc '删除目录'
/*     */     //   1857: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   1860: aload #44
/*     */     //   1862: invokevirtual getName : ()Ljava/lang/String;
/*     */     //   1865: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   1868: invokevirtual toString : ()Ljava/lang/String;
/*     */     //   1871: invokevirtual println : (Ljava/lang/String;)V
/*     */     //   1874: aload_0
/*     */     //   1875: new java/lang/StringBuilder
/*     */     //   1878: dup
/*     */     //   1879: invokespecial <init> : ()V
/*     */     //   1882: ldc '删除目录'
/*     */     //   1884: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   1887: aload #44
/*     */     //   1889: invokevirtual getName : ()Ljava/lang/String;
/*     */     //   1892: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   1895: invokevirtual toString : ()Ljava/lang/String;
/*     */     //   1898: invokevirtual writeLog : (Ljava/lang/String;)V
/*     */     //   1901: goto -> 3571
/*     */     //   1904: astore #45
/*     */     //   1906: aload_0
/*     */     //   1907: aload #45
/*     */     //   1909: invokevirtual toString : ()Ljava/lang/String;
/*     */     //   1912: invokevirtual writeLog : (Ljava/lang/String;)V
/*     */     //   1915: aload_0
/*     */     //   1916: aload #45
/*     */     //   1918: invokevirtual toString : ()Ljava/lang/String;
/*     */     //   1921: invokevirtual writeLog : (Ljava/lang/String;)V
/*     */     //   1924: goto -> 1991
/*     */     //   1927: aload #42
/*     */     //   1929: invokestatic DeleteFile : (Ljava/lang/String;)V
/*     */     //   1932: getstatic java/lang/System.out : Ljava/io/PrintStream;
/*     */     //   1935: new java/lang/StringBuilder
/*     */     //   1938: dup
/*     */     //   1939: invokespecial <init> : ()V
/*     */     //   1942: ldc '删除文件'
/*     */     //   1944: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   1947: aload #44
/*     */     //   1949: invokevirtual getName : ()Ljava/lang/String;
/*     */     //   1952: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   1955: invokevirtual toString : ()Ljava/lang/String;
/*     */     //   1958: invokevirtual println : (Ljava/lang/String;)V
/*     */     //   1961: aload_0
/*     */     //   1962: new java/lang/StringBuilder
/*     */     //   1965: dup
/*     */     //   1966: invokespecial <init> : ()V
/*     */     //   1969: ldc '删除文件'
/*     */     //   1971: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   1974: aload #44
/*     */     //   1976: invokevirtual getName : ()Ljava/lang/String;
/*     */     //   1979: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   1982: invokevirtual toString : ()Ljava/lang/String;
/*     */     //   1985: invokevirtual writeLog : (Ljava/lang/String;)V
/*     */     //   1988: goto -> 3571
/*     */     //   1991: aload #41
/*     */     //   1993: ldc 'sql'
/*     */     //   1995: invokevirtual indexOf : (Ljava/lang/String;)I
/*     */     //   1998: ifge -> 2004
/*     */     //   2001: goto -> 3571
/*     */     //   2004: aload #41
/*     */     //   2006: ldc '_KB'
/*     */     //   2008: invokevirtual indexOf : (Ljava/lang/String;)I
/*     */     //   2011: iconst_m1
/*     */     //   2012: if_icmple -> 2371
/*     */     //   2015: aload #41
/*     */     //   2017: aload #41
/*     */     //   2019: ldc 'KB'
/*     */     //   2021: invokevirtual indexOf : (Ljava/lang/String;)I
/*     */     //   2024: iconst_2
/*     */     //   2025: iadd
/*     */     //   2026: aload #41
/*     */     //   2028: ldc '.sql'
/*     */     //   2030: invokevirtual lastIndexOf : (Ljava/lang/String;)I
/*     */     //   2033: invokevirtual substring : (II)Ljava/lang/String;
/*     */     //   2036: astore #44
/*     */     //   2038: aload_0
/*     */     //   2039: new java/lang/StringBuilder
/*     */     //   2042: dup
/*     */     //   2043: invokespecial <init> : ()V
/*     */     //   2046: ldc '当licenseKb号('
/*     */     //   2048: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   2051: aload_0
/*     */     //   2052: getfield beforeLicense : Ljava/lang/String;
/*     */     //   2055: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   2058: ldc ')小于'
/*     */     //   2060: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   2063: aload #44
/*     */     //   2065: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   2068: ldc '时执行！！注：两者相等不执行'
/*     */     //   2070: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   2073: invokevirtual toString : ()Ljava/lang/String;
/*     */     //   2076: invokevirtual writeLog : (Ljava/lang/String;)V
/*     */     //   2079: aload #44
/*     */     //   2081: ldc '0'
/*     */     //   2083: invokestatic null2String : (Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;
/*     */     //   2086: astore #45
/*     */     //   2088: ldc '0'
/*     */     //   2090: astore #46
/*     */     //   2092: aload_0
/*     */     //   2093: getfield beforeLicense : Ljava/lang/String;
/*     */     //   2096: ldc 'KB'
/*     */     //   2098: invokevirtual indexOf : (Ljava/lang/String;)I
/*     */     //   2101: iconst_m1
/*     */     //   2102: if_icmple -> 2133
/*     */     //   2105: aload_0
/*     */     //   2106: getfield beforeLicense : Ljava/lang/String;
/*     */     //   2109: aload_0
/*     */     //   2110: getfield beforeLicense : Ljava/lang/String;
/*     */     //   2113: ldc 'KB'
/*     */     //   2115: invokevirtual indexOf : (Ljava/lang/String;)I
/*     */     //   2118: iconst_2
/*     */     //   2119: iadd
/*     */     //   2120: invokevirtual substring : (I)Ljava/lang/String;
/*     */     //   2123: ldc '0'
/*     */     //   2125: invokestatic null2String : (Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;
/*     */     //   2128: astore #46
/*     */     //   2130: goto -> 2239
/*     */     //   2133: aload_0
/*     */     //   2134: getfield beforeLicense : Ljava/lang/String;
/*     */     //   2137: ldc '.'
/*     */     //   2139: invokevirtual indexOf : (Ljava/lang/String;)I
/*     */     //   2142: iconst_m1
/*     */     //   2143: if_icmple -> 2171
/*     */     //   2146: aload_0
/*     */     //   2147: getfield beforeLicense : Ljava/lang/String;
/*     */     //   2150: iconst_0
/*     */     //   2151: aload_0
/*     */     //   2152: getfield beforeLicense : Ljava/lang/String;
/*     */     //   2155: ldc '.'
/*     */     //   2157: invokevirtual indexOf : (Ljava/lang/String;)I
/*     */     //   2160: invokevirtual substring : (II)Ljava/lang/String;
/*     */     //   2163: invokestatic null2String : (Ljava/lang/String;)Ljava/lang/String;
/*     */     //   2166: astore #46
/*     */     //   2168: goto -> 2239
/*     */     //   2171: aload_0
/*     */     //   2172: getfield beforeLicense : Ljava/lang/String;
/*     */     //   2175: invokestatic null2String : (Ljava/lang/String;)Ljava/lang/String;
/*     */     //   2178: astore #46
/*     */     //   2180: aload_0
/*     */     //   2181: new java/lang/StringBuilder
/*     */     //   2184: dup
/*     */     //   2185: invokespecial <init> : ()V
/*     */     //   2188: ldc 'error beforeKB=beforeLicense:'
/*     */     //   2190: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   2193: aload #46
/*     */     //   2195: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   2198: ldc ';获取不到beforeLicense'
/*     */     //   2200: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   2203: invokevirtual toString : ()Ljava/lang/String;
/*     */     //   2206: invokevirtual writeLog : (Ljava/lang/String;)V
/*     */     //   2209: iconst_0
/*     */     //   2210: istore_1
/*     */     //   2211: aload #17
/*     */     //   2213: aload #18
/*     */     //   2215: invokevirtual returnConnection : (Lweaver/conn/WeaverConnection;)V
/*     */     //   2218: aload #27
/*     */     //   2220: invokeinterface close : ()V
/*     */     //   2225: goto -> 3681
/*     */     //   2228: astore #47
/*     */     //   2230: aload_0
/*     */     //   2231: aload #47
/*     */     //   2233: invokevirtual writeLog : (Ljava/lang/Exception;)V
/*     */     //   2236: goto -> 3681
/*     */     //   2239: aload #45
/*     */     //   2241: aload #46
/*     */     //   2243: invokevirtual compareTo : (Ljava/lang/String;)I
/*     */     //   2246: ifgt -> 2371
/*     */     //   2249: aload_0
/*     */     //   2250: new java/lang/StringBuilder
/*     */     //   2253: dup
/*     */     //   2254: invokespecial <init> : ()V
/*     */     //   2257: ldc '该脚本'
/*     */     //   2259: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   2262: aload #41
/*     */     //   2264: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   2267: ldc '不需要执行，beforeLicense='
/*     */     //   2269: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   2272: aload_0
/*     */     //   2273: getfield beforeLicense : Ljava/lang/String;
/*     */     //   2276: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   2279: ldc '直接放到data目录下'
/*     */     //   2281: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   2284: invokevirtual toString : ()Ljava/lang/String;
/*     */     //   2287: invokevirtual writeLog : (Ljava/lang/String;)V
/*     */     //   2290: new java/io/File
/*     */     //   2293: dup
/*     */     //   2294: aload #43
/*     */     //   2296: invokespecial <init> : (Ljava/lang/String;)V
/*     */     //   2299: invokevirtual exists : ()Z
/*     */     //   2302: ifne -> 2315
/*     */     //   2305: aload #42
/*     */     //   2307: aload #43
/*     */     //   2309: invokestatic moveFileTo : (Ljava/lang/String;Ljava/lang/String;)V
/*     */     //   2312: goto -> 2320
/*     */     //   2315: aload #42
/*     */     //   2317: invokestatic DeleteFile : (Ljava/lang/String;)V
/*     */     //   2320: goto -> 3571
/*     */     //   2323: astore #47
/*     */     //   2325: aload #47
/*     */     //   2327: invokevirtual printStackTrace : ()V
/*     */     //   2330: aload #19
/*     */     //   2332: new java/lang/StringBuilder
/*     */     //   2335: dup
/*     */     //   2336: invokespecial <init> : ()V
/*     */     //   2339: ldc '拷贝文件失败：'
/*     */     //   2341: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   2344: aload #42
/*     */     //   2346: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   2349: ldc '    '
/*     */     //   2351: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   2354: aload #47
/*     */     //   2356: invokevirtual toString : ()Ljava/lang/String;
/*     */     //   2359: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   2362: invokevirtual toString : ()Ljava/lang/String;
/*     */     //   2365: invokevirtual writeLog : (Ljava/lang/Object;)V
/*     */     //   2368: goto -> 3571
/*     */     //   2371: aload #41
/*     */     //   2373: astore #13
/*     */     //   2375: aload_0
/*     */     //   2376: iconst_2
/*     */     //   2377: iload #10
/*     */     //   2379: iload #9
/*     */     //   2381: aload #13
/*     */     //   2383: iload #8
/*     */     //   2385: invokevirtual changeStatus : (IIILjava/lang/String;I)V
/*     */     //   2388: getstatic java/lang/System.out : Ljava/io/PrintStream;
/*     */     //   2391: new java/lang/StringBuilder
/*     */     //   2394: dup
/*     */     //   2395: invokespecial <init> : ()V
/*     */     //   2398: ldc '正在执行'
/*     */     //   2400: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   2403: aload #13
/*     */     //   2405: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   2408: ldc ',第('
/*     */     //   2410: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   2413: iload #10
/*     */     //   2415: invokevirtual append : (I)Ljava/lang/StringBuilder;
/*     */     //   2418: ldc ')个脚本，共('
/*     */     //   2420: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   2423: iload #9
/*     */     //   2425: invokevirtual append : (I)Ljava/lang/StringBuilder;
/*     */     //   2428: ldc ')个。'
/*     */     //   2430: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   2433: invokevirtual toString : ()Ljava/lang/String;
/*     */     //   2436: invokevirtual println : (Ljava/lang/String;)V
/*     */     //   2439: aload_0
/*     */     //   2440: new java/lang/StringBuilder
/*     */     //   2443: dup
/*     */     //   2444: invokespecial <init> : ()V
/*     */     //   2447: ldc '正在执行'
/*     */     //   2449: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   2452: aload #13
/*     */     //   2454: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   2457: ldc ',第('
/*     */     //   2459: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   2462: iload #10
/*     */     //   2464: invokevirtual append : (I)Ljava/lang/StringBuilder;
/*     */     //   2467: ldc ')个脚本，共('
/*     */     //   2469: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   2472: iload #9
/*     */     //   2474: invokevirtual append : (I)Ljava/lang/StringBuilder;
/*     */     //   2477: ldc ')个。'
/*     */     //   2479: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   2482: invokevirtual toString : ()Ljava/lang/String;
/*     */     //   2485: invokevirtual writeLog : (Ljava/lang/String;)V
/*     */     //   2488: invokestatic getInstance : ()Lweaver/upgradetool/dbupgrade/actions/ActionProcess;
/*     */     //   2491: new java/lang/StringBuilder
/*     */     //   2494: dup
/*     */     //   2495: invokespecial <init> : ()V
/*     */     //   2498: ldc ''
/*     */     //   2500: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   2503: iload #10
/*     */     //   2505: iconst_1
/*     */     //   2506: isub
/*     */     //   2507: iload #9
/*     */     //   2509: idiv
/*     */     //   2510: invokevirtual append : (I)Ljava/lang/StringBuilder;
/*     */     //   2513: invokevirtual toString : ()Ljava/lang/String;
/*     */     //   2516: invokevirtual setActionProcess : (Ljava/lang/String;)V
/*     */     //   2519: invokestatic getInstance : ()Lweaver/upgradetool/dbupgrade/actions/ActionProcess;
/*     */     //   2522: new java/lang/StringBuilder
/*     */     //   2525: dup
/*     */     //   2526: invokespecial <init> : ()V
/*     */     //   2529: ldc '正在执行'
/*     */     //   2531: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   2534: aload #13
/*     */     //   2536: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   2539: ldc ',第('
/*     */     //   2541: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   2544: iload #10
/*     */     //   2546: invokevirtual append : (I)Ljava/lang/StringBuilder;
/*     */     //   2549: ldc ')个脚本，共('
/*     */     //   2551: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   2554: iload #9
/*     */     //   2556: invokevirtual append : (I)Ljava/lang/StringBuilder;
/*     */     //   2559: ldc ')个。'
/*     */     //   2561: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   2564: invokevirtual toString : ()Ljava/lang/String;
/*     */     //   2567: invokevirtual setActionProcessName : (Ljava/lang/String;)V
/*     */     //   2570: new java/io/File
/*     */     //   2573: dup
/*     */     //   2574: aload #42
/*     */     //   2576: invokespecial <init> : (Ljava/lang/String;)V
/*     */     //   2579: astore #44
/*     */     //   2581: ldc ''
/*     */     //   2583: astore #45
/*     */     //   2585: ldc ''
/*     */     //   2587: astore #46
/*     */     //   2589: aconst_null
/*     */     //   2590: astore #47
/*     */     //   2592: new java/io/BufferedReader
/*     */     //   2595: dup
/*     */     //   2596: new java/io/InputStreamReader
/*     */     //   2599: dup
/*     */     //   2600: new java/io/FileInputStream
/*     */     //   2603: dup
/*     */     //   2604: aload #44
/*     */     //   2606: invokespecial <init> : (Ljava/io/File;)V
/*     */     //   2609: ldc 'gbk'
/*     */     //   2611: invokespecial <init> : (Ljava/io/InputStream;Ljava/lang/String;)V
/*     */     //   2614: invokespecial <init> : (Ljava/io/Reader;)V
/*     */     //   2617: astore #48
/*     */     //   2619: invokestatic getInstance : ()Ljava/util/Calendar;
/*     */     //   2622: astore #49
/*     */     //   2624: aload #49
/*     */     //   2626: invokevirtual getTimeInMillis : ()J
/*     */     //   2629: lstore #50
/*     */     //   2631: iconst_0
/*     */     //   2632: istore #52
/*     */     //   2634: iload #20
/*     */     //   2636: ifeq -> 3034
/*     */     //   2639: aload #18
/*     */     //   2641: invokevirtual isClosed : ()Z
/*     */     //   2644: ifeq -> 2654
/*     */     //   2647: aload #17
/*     */     //   2649: invokevirtual getConnection : ()Lweaver/conn/WeaverConnection;
/*     */     //   2652: astore #18
/*     */     //   2654: new java/util/HashMap
/*     */     //   2657: dup
/*     */     //   2658: invokespecial <init> : ()V
/*     */     //   2661: astore #53
/*     */     //   2663: new weaver/conn/mybatis/MysqlScriptRunner
/*     */     //   2666: dup
/*     */     //   2667: aload #18
/*     */     //   2669: iconst_1
/*     */     //   2670: iconst_1
/*     */     //   2671: invokespecial <init> : (Ljava/sql/Connection;ZZ)V
/*     */     //   2674: astore #54
/*     */     //   2676: aload #41
/*     */     //   2678: aload #7
/*     */     //   2680: invokevirtual equals : (Ljava/lang/Object;)Z
/*     */     //   2683: ifeq -> 2704
/*     */     //   2686: aload #54
/*     */     //   2688: aload #48
/*     */     //   2690: iload #8
/*     */     //   2692: aload #14
/*     */     //   2694: aload #41
/*     */     //   2696: aload #53
/*     */     //   2698: invokevirtual runScript4Upgrade : (Ljava/io/Reader;ILjava/lang/StringBuffer;Ljava/lang/String;Ljava/util/HashMap;)V
/*     */     //   2701: goto -> 2718
/*     */     //   2704: aload #54
/*     */     //   2706: aload #48
/*     */     //   2708: iconst_0
/*     */     //   2709: aload #14
/*     */     //   2711: aload #41
/*     */     //   2713: aload #53
/*     */     //   2715: invokevirtual runScript4Upgrade : (Ljava/io/Reader;ILjava/lang/StringBuffer;Ljava/lang/String;Ljava/util/HashMap;)V
/*     */     //   2718: iload #40
/*     */     //   2720: bipush #10
/*     */     //   2722: irem
/*     */     //   2723: bipush #9
/*     */     //   2725: if_icmpne -> 2761
/*     */     //   2728: invokestatic currentTimeMillis : ()J
/*     */     //   2731: lstore #55
/*     */     //   2733: lload #55
/*     */     //   2735: lload #38
/*     */     //   2737: lsub
/*     */     //   2738: ldc2_w 180000
/*     */     //   2741: lcmp
/*     */     //   2742: ifle -> 2761
/*     */     //   2745: aload #18
/*     */     //   2747: invokevirtual close : ()V
/*     */     //   2750: aload #17
/*     */     //   2752: invokevirtual getConnection : ()Lweaver/conn/WeaverConnection;
/*     */     //   2755: astore #18
/*     */     //   2757: lload #55
/*     */     //   2759: lstore #38
/*     */     //   2761: goto -> 3023
/*     */     //   2764: astore #53
/*     */     //   2766: aload_0
/*     */     //   2767: aload #53
/*     */     //   2769: invokevirtual getSql : ()Ljava/lang/String;
/*     */     //   2772: ldc 'false'
/*     */     //   2774: invokevirtual writeLog : (Ljava/lang/String;Ljava/lang/String;)V
/*     */     //   2777: aload_0
/*     */     //   2778: aload #41
/*     */     //   2780: aload #53
/*     */     //   2782: invokevirtual getSql : ()Ljava/lang/String;
/*     */     //   2785: aload #53
/*     */     //   2787: invokevirtual getRowNum : ()I
/*     */     //   2790: aload #53
/*     */     //   2792: invokevirtual markErrorLog : (Ljava/lang/String;Ljava/lang/String;ILjava/lang/Exception;)V
/*     */     //   2795: aload #53
/*     */     //   2797: invokevirtual toString : ()Ljava/lang/String;
/*     */     //   2800: astore #54
/*     */     //   2802: aload #54
/*     */     //   2804: ldc '已存在'
/*     */     //   2806: invokevirtual indexOf : (Ljava/lang/String;)I
/*     */     //   2809: ifge -> 3004
/*     */     //   2812: aload #54
/*     */     //   2814: ldc '多次指定了列名'
/*     */     //   2816: invokevirtual indexOf : (Ljava/lang/String;)I
/*     */     //   2819: ifge -> 3004
/*     */     //   2822: aload #54
/*     */     //   2824: ldc '已由现有对象使用'
/*     */     //   2826: invokevirtual indexOf : (Ljava/lang/String;)I
/*     */     //   2829: ifge -> 3004
/*     */     //   2832: aload #54
/*     */     //   2834: ldc '插入重复键'
/*     */     //   2836: invokevirtual indexOf : (Ljava/lang/String;)I
/*     */     //   2839: ifge -> 3004
/*     */     //   2842: aload #54
/*     */     //   2844: ldc 'Duplicate column'
/*     */     //   2846: invokevirtual indexOf : (Ljava/lang/String;)I
/*     */     //   2849: ifge -> 3004
/*     */     //   2852: aload #54
/*     */     //   2854: ldc 'already exists'
/*     */     //   2856: invokevirtual indexOf : (Ljava/lang/String;)I
/*     */     //   2859: ifge -> 3004
/*     */     //   2862: aload #54
/*     */     //   2864: ldc '重复键值为'
/*     */     //   2866: invokevirtual indexOf : (Ljava/lang/String;)I
/*     */     //   2869: ifge -> 3004
/*     */     //   2872: aload #54
/*     */     //   2874: ldc '违反唯一性约束条件'
/*     */     //   2876: invokevirtual indexOf : (Ljava/lang/String;)I
/*     */     //   2879: iflt -> 2885
/*     */     //   2882: goto -> 3004
/*     */     //   2885: aload #48
/*     */     //   2887: invokevirtual close : ()V
/*     */     //   2890: aload_0
/*     */     //   2891: iconst_1
/*     */     //   2892: iload #10
/*     */     //   2894: iload #9
/*     */     //   2896: aload #13
/*     */     //   2898: aload #53
/*     */     //   2900: invokevirtual getRowNum : ()I
/*     */     //   2903: invokevirtual changeStatus : (IIILjava/lang/String;I)V
/*     */     //   2906: getstatic java/lang/System.out : Ljava/io/PrintStream;
/*     */     //   2909: new java/lang/StringBuilder
/*     */     //   2912: dup
/*     */     //   2913: invokespecial <init> : ()V
/*     */     //   2916: ldc '#####'
/*     */     //   2918: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   2921: ldc 'line.separator'
/*     */     //   2923: invokestatic getProperty : (Ljava/lang/String;)Ljava/lang/String;
/*     */     //   2926: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   2929: aload #11
/*     */     //   2931: invokevirtual toString : ()Ljava/lang/String;
/*     */     //   2934: ldc '<br>'
/*     */     //   2936: ldc 'line.separator'
/*     */     //   2938: invokestatic getProperty : (Ljava/lang/String;)Ljava/lang/String;
/*     */     //   2941: invokevirtual replace : (Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/lang/String;
/*     */     //   2944: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   2947: ldc 'line.separator'
/*     */     //   2949: invokestatic getProperty : (Ljava/lang/String;)Ljava/lang/String;
/*     */     //   2952: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   2955: ldc '#####'
/*     */     //   2957: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   2960: invokevirtual toString : ()Ljava/lang/String;
/*     */     //   2963: invokevirtual println : (Ljava/lang/String;)V
/*     */     //   2966: getstatic java/lang/System.out : Ljava/io/PrintStream;
/*     */     //   2969: ldc '###### SQL Script error ######'
/*     */     //   2971: invokevirtual println : (Ljava/lang/String;)V
/*     */     //   2974: iconst_0
/*     */     //   2975: istore_1
/*     */     //   2976: aload #17
/*     */     //   2978: aload #18
/*     */     //   2980: invokevirtual returnConnection : (Lweaver/conn/WeaverConnection;)V
/*     */     //   2983: aload #27
/*     */     //   2985: invokeinterface close : ()V
/*     */     //   2990: goto -> 3681
/*     */     //   2993: astore #55
/*     */     //   2995: aload_0
/*     */     //   2996: aload #55
/*     */     //   2998: invokevirtual writeLog : (Ljava/lang/Exception;)V
/*     */     //   3001: goto -> 3681
/*     */     //   3004: goto -> 3023
/*     */     //   3007: astore #53
/*     */     //   3009: aload #48
/*     */     //   3011: invokevirtual close : ()V
/*     */     //   3014: aload_0
/*     */     //   3015: aload #53
/*     */     //   3017: invokevirtual writeLog : (Ljava/lang/Exception;)V
/*     */     //   3020: aload #53
/*     */     //   3022: athrow
/*     */     //   3023: aload_0
/*     */     //   3024: aload #45
/*     */     //   3026: ldc 'true'
/*     */     //   3028: invokevirtual writeLog : (Ljava/lang/String;Ljava/lang/String;)V
/*     */     //   3031: goto -> 3480
/*     */     //   3034: aload #48
/*     */     //   3036: invokevirtual readLine : ()Ljava/lang/String;
/*     */     //   3039: dup
/*     */     //   3040: astore #47
/*     */     //   3042: ifnull -> 3480
/*     */     //   3045: aload #47
/*     */     //   3047: invokevirtual trim : ()Ljava/lang/String;
/*     */     //   3050: astore #47
/*     */     //   3052: iinc #52, 1
/*     */     //   3055: aload #41
/*     */     //   3057: aload #7
/*     */     //   3059: invokevirtual equals : (Ljava/lang/Object;)Z
/*     */     //   3062: ifeq -> 3075
/*     */     //   3065: iload #8
/*     */     //   3067: iload #52
/*     */     //   3069: if_icmplt -> 3075
/*     */     //   3072: goto -> 3034
/*     */     //   3075: new java/lang/StringBuilder
/*     */     //   3078: dup
/*     */     //   3079: invokespecial <init> : ()V
/*     */     //   3082: aload #46
/*     */     //   3084: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   3087: aload #47
/*     */     //   3089: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   3092: ldc '\\n'
/*     */     //   3094: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   3097: invokevirtual toString : ()Ljava/lang/String;
/*     */     //   3100: astore #46
/*     */     //   3102: aload #47
/*     */     //   3104: aload #24
/*     */     //   3106: invokevirtual equalsIgnoreCase : (Ljava/lang/String;)Z
/*     */     //   3109: ifne -> 3142
/*     */     //   3112: new java/lang/StringBuilder
/*     */     //   3115: dup
/*     */     //   3116: invokespecial <init> : ()V
/*     */     //   3119: aload #45
/*     */     //   3121: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   3124: ldc ' '
/*     */     //   3126: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   3129: aload #47
/*     */     //   3131: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   3134: invokevirtual toString : ()Ljava/lang/String;
/*     */     //   3137: astore #45
/*     */     //   3139: goto -> 3034
/*     */     //   3142: new java/lang/StringBuffer
/*     */     //   3145: dup
/*     */     //   3146: aload #46
/*     */     //   3148: invokespecial <init> : (Ljava/lang/String;)V
/*     */     //   3151: astore #14
/*     */     //   3153: ldc ''
/*     */     //   3155: astore #46
/*     */     //   3157: aload #34
/*     */     //   3159: aload #45
/*     */     //   3161: invokeinterface execute : (Ljava/lang/String;)Z
/*     */     //   3166: pop
/*     */     //   3167: goto -> 3461
/*     */     //   3170: astore #53
/*     */     //   3172: aload_0
/*     */     //   3173: aload #41
/*     */     //   3175: aload #45
/*     */     //   3177: iload #52
/*     */     //   3179: aload #53
/*     */     //   3181: invokevirtual markErrorLog : (Ljava/lang/String;Ljava/lang/String;ILjava/lang/Exception;)V
/*     */     //   3184: aload #53
/*     */     //   3186: invokevirtual toString : ()Ljava/lang/String;
/*     */     //   3189: astore #54
/*     */     //   3191: aload #54
/*     */     //   3193: ldc '已存在'
/*     */     //   3195: invokevirtual indexOf : (Ljava/lang/String;)I
/*     */     //   3198: ifge -> 3461
/*     */     //   3201: aload #54
/*     */     //   3203: ldc '多次指定了列名'
/*     */     //   3205: invokevirtual indexOf : (Ljava/lang/String;)I
/*     */     //   3208: ifge -> 3461
/*     */     //   3211: aload #54
/*     */     //   3213: ldc '已由现有对象使用'
/*     */     //   3215: invokevirtual indexOf : (Ljava/lang/String;)I
/*     */     //   3218: ifge -> 3461
/*     */     //   3221: aload #54
/*     */     //   3223: ldc '插入重复键'
/*     */     //   3225: invokevirtual indexOf : (Ljava/lang/String;)I
/*     */     //   3228: ifge -> 3461
/*     */     //   3231: aload #54
/*     */     //   3233: ldc '违反唯一约束条件'
/*     */     //   3235: invokevirtual indexOf : (Ljava/lang/String;)I
/*     */     //   3238: ifge -> 3461
/*     */     //   3241: aload #54
/*     */     //   3243: ldc 'Duplicate column'
/*     */     //   3245: invokevirtual indexOf : (Ljava/lang/String;)I
/*     */     //   3248: ifge -> 3461
/*     */     //   3251: aload #54
/*     */     //   3253: ldc 'already exists'
/*     */     //   3255: invokevirtual indexOf : (Ljava/lang/String;)I
/*     */     //   3258: ifge -> 3461
/*     */     //   3261: aload #54
/*     */     //   3263: invokevirtual toLowerCase : ()Ljava/lang/String;
/*     */     //   3266: ldc 'ORA-00001'
/*     */     //   3268: invokevirtual indexOf : (Ljava/lang/String;)I
/*     */     //   3271: ifge -> 3461
/*     */     //   3274: aload #54
/*     */     //   3276: ldc '重复键值为'
/*     */     //   3278: invokevirtual indexOf : (Ljava/lang/String;)I
/*     */     //   3281: ifge -> 3461
/*     */     //   3284: aload #54
/*     */     //   3286: ldc '违反唯一性约束条件'
/*     */     //   3288: invokevirtual indexOf : (Ljava/lang/String;)I
/*     */     //   3291: ifge -> 3461
/*     */     //   3294: aload #54
/*     */     //   3296: ldc '不能在具有唯一索引'
/*     */     //   3298: invokevirtual indexOf : (Ljava/lang/String;)I
/*     */     //   3301: ifge -> 3461
/*     */     //   3304: aload #54
/*     */     //   3306: ldc '插入重复键的行'
/*     */     //   3308: invokevirtual indexOf : (Ljava/lang/String;)I
/*     */     //   3311: ifge -> 3461
/*     */     //   3314: aload #54
/*     */     //   3316: ldc '重复键的值'
/*     */     //   3318: invokevirtual indexOf : (Ljava/lang/String;)I
/*     */     //   3321: ifge -> 3461
/*     */     //   3324: aload #54
/*     */     //   3326: ldc 'duplicate key value violates unique constraint'
/*     */     //   3328: invokevirtual indexOf : (Ljava/lang/String;)I
/*     */     //   3331: iflt -> 3337
/*     */     //   3334: goto -> 3461
/*     */     //   3337: aload_0
/*     */     //   3338: aload #45
/*     */     //   3340: ldc 'false'
/*     */     //   3342: invokevirtual writeLog : (Ljava/lang/String;Ljava/lang/String;)V
/*     */     //   3345: aload #48
/*     */     //   3347: invokevirtual close : ()V
/*     */     //   3350: aload_0
/*     */     //   3351: iconst_1
/*     */     //   3352: iload #10
/*     */     //   3354: iload #9
/*     */     //   3356: aload #13
/*     */     //   3358: iload #52
/*     */     //   3360: invokevirtual changeStatus : (IIILjava/lang/String;I)V
/*     */     //   3363: getstatic java/lang/System.out : Ljava/io/PrintStream;
/*     */     //   3366: new java/lang/StringBuilder
/*     */     //   3369: dup
/*     */     //   3370: invokespecial <init> : ()V
/*     */     //   3373: ldc '#####'
/*     */     //   3375: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   3378: ldc 'line.separator'
/*     */     //   3380: invokestatic getProperty : (Ljava/lang/String;)Ljava/lang/String;
/*     */     //   3383: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   3386: aload #11
/*     */     //   3388: invokevirtual toString : ()Ljava/lang/String;
/*     */     //   3391: ldc '<br>'
/*     */     //   3393: ldc 'line.separator'
/*     */     //   3395: invokestatic getProperty : (Ljava/lang/String;)Ljava/lang/String;
/*     */     //   3398: invokevirtual replace : (Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/lang/String;
/*     */     //   3401: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   3404: ldc 'line.separator'
/*     */     //   3406: invokestatic getProperty : (Ljava/lang/String;)Ljava/lang/String;
/*     */     //   3409: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   3412: ldc '#####'
/*     */     //   3414: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   3417: invokevirtual toString : ()Ljava/lang/String;
/*     */     //   3420: invokevirtual println : (Ljava/lang/String;)V
/*     */     //   3423: getstatic java/lang/System.out : Ljava/io/PrintStream;
/*     */     //   3426: ldc '###### SQL Script error ######'
/*     */     //   3428: invokevirtual println : (Ljava/lang/String;)V
/*     */     //   3431: iconst_0
/*     */     //   3432: istore_1
/*     */     //   3433: aload #17
/*     */     //   3435: aload #18
/*     */     //   3437: invokevirtual returnConnection : (Lweaver/conn/WeaverConnection;)V
/*     */     //   3440: aload #27
/*     */     //   3442: invokeinterface close : ()V
/*     */     //   3447: goto -> 3681
/*     */     //   3450: astore #55
/*     */     //   3452: aload_0
/*     */     //   3453: aload #55
/*     */     //   3455: invokevirtual writeLog : (Ljava/lang/Exception;)V
/*     */     //   3458: goto -> 3681
/*     */     //   3461: aload_0
/*     */     //   3462: aload #45
/*     */     //   3464: ldc 'true'
/*     */     //   3466: invokevirtual writeLog : (Ljava/lang/String;Ljava/lang/String;)V
/*     */     //   3469: ldc ''
/*     */     //   3471: astore #45
/*     */     //   3473: ldc ''
/*     */     //   3475: astore #46
/*     */     //   3477: goto -> 3034
/*     */     //   3480: aload_0
/*     */     //   3481: lload #50
/*     */     //   3483: aload #41
/*     */     //   3485: invokevirtual insertExecuteTime : (JLjava/lang/String;)V
/*     */     //   3488: aload #48
/*     */     //   3490: invokevirtual close : ()V
/*     */     //   3493: new java/io/File
/*     */     //   3496: dup
/*     */     //   3497: aload #43
/*     */     //   3499: invokespecial <init> : (Ljava/lang/String;)V
/*     */     //   3502: invokevirtual exists : ()Z
/*     */     //   3505: ifne -> 3518
/*     */     //   3508: aload #42
/*     */     //   3510: aload #43
/*     */     //   3512: invokestatic moveFileTo : (Ljava/lang/String;Ljava/lang/String;)V
/*     */     //   3515: goto -> 3523
/*     */     //   3518: aload #42
/*     */     //   3520: invokestatic DeleteFile : (Ljava/lang/String;)V
/*     */     //   3523: goto -> 3571
/*     */     //   3526: astore #53
/*     */     //   3528: aload #53
/*     */     //   3530: invokevirtual printStackTrace : ()V
/*     */     //   3533: aload #19
/*     */     //   3535: new java/lang/StringBuilder
/*     */     //   3538: dup
/*     */     //   3539: invokespecial <init> : ()V
/*     */     //   3542: ldc '拷贝文件失败：'
/*     */     //   3544: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   3547: aload #42
/*     */     //   3549: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   3552: ldc '    '
/*     */     //   3554: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   3557: aload #53
/*     */     //   3559: invokevirtual toString : ()Ljava/lang/String;
/*     */     //   3562: invokevirtual append : (Ljava/lang/String;)Ljava/lang/StringBuilder;
/*     */     //   3565: invokevirtual toString : ()Ljava/lang/String;
/*     */     //   3568: invokevirtual writeLog : (Ljava/lang/Object;)V
/*     */     //   3571: iinc #40, 1
/*     */     //   3574: goto -> 1743
/*     */     //   3577: aload_0
/*     */     //   3578: iconst_0
/*     */     //   3579: iconst_0
/*     */     //   3580: iconst_0
/*     */     //   3581: ldc ''
/*     */     //   3583: iconst_0
/*     */     //   3584: invokevirtual changeStatus : (IIILjava/lang/String;I)V
/*     */     //   3587: aload #17
/*     */     //   3589: aload #18
/*     */     //   3591: invokevirtual returnConnection : (Lweaver/conn/WeaverConnection;)V
/*     */     //   3594: aload #27
/*     */     //   3596: invokeinterface close : ()V
/*     */     //   3601: goto -> 3681
/*     */     //   3604: astore #28
/*     */     //   3606: aload_0
/*     */     //   3607: aload #28
/*     */     //   3609: invokevirtual writeLog : (Ljava/lang/Exception;)V
/*     */     //   3612: goto -> 3681
/*     */     //   3615: astore #28
/*     */     //   3617: aload_0
/*     */     //   3618: aload #28
/*     */     //   3620: invokevirtual writeLog : (Ljava/lang/Exception;)V
/*     */     //   3623: aload #17
/*     */     //   3625: aload #18
/*     */     //   3627: invokevirtual returnConnection : (Lweaver/conn/WeaverConnection;)V
/*     */     //   3630: aload #27
/*     */     //   3632: invokeinterface close : ()V
/*     */     //   3637: goto -> 3681
/*     */     //   3640: astore #28
/*     */     //   3642: aload_0
/*     */     //   3643: aload #28
/*     */     //   3645: invokevirtual writeLog : (Ljava/lang/Exception;)V
/*     */     //   3648: goto -> 3681
/*     */     //   3651: astore #57
/*     */     //   3653: aload #17
/*     */     //   3655: aload #18
/*     */     //   3657: invokevirtual returnConnection : (Lweaver/conn/WeaverConnection;)V
/*     */     //   3660: aload #27
/*     */     //   3662: invokeinterface close : ()V
/*     */     //   3667: goto -> 3678
/*     */     //   3670: astore #58
/*     */     //   3672: aload_0
/*     */     //   3673: aload #58
/*     */     //   3675: invokevirtual writeLog : (Ljava/lang/Exception;)V
/*     */     //   3678: aload #57
/*     */     //   3680: athrow
/*     */     //   3681: iload_1
/*     */     //   3682: ireturn
/*     */     // Line number table:
/*     */     //   Java source line number -> byte code offset
/*     */     //   #71	-> 0
/*     */     //   #77	-> 2
/*     */     //   #78	-> 6
/*     */     //   #79	-> 9
/*     */     //   #80	-> 12
/*     */     //   #81	-> 15
/*     */     //   #82	-> 24
/*     */     //   #83	-> 33
/*     */     //   #84	-> 37
/*     */     //   #87	-> 46
/*     */     //   #88	-> 55
/*     */     //   #91	-> 71
/*     */     //   #92	-> 76
/*     */     //   #93	-> 104
/*     */     //   #95	-> 132
/*     */     //   #96	-> 137
/*     */     //   #97	-> 144
/*     */     //   #98	-> 153
/*     */     //   #99	-> 165
/*     */     //   #100	-> 174
/*     */     //   #101	-> 184
/*     */     //   #102	-> 194
/*     */     //   #103	-> 204
/*     */     //   #104	-> 214
/*     */     //   #105	-> 224
/*     */     //   #106	-> 234
/*     */     //   #107	-> 244
/*     */     //   #108	-> 254
/*     */     //   #109	-> 264
/*     */     //   #110	-> 268
/*     */     //   #111	-> 272
/*     */     //   #112	-> 278
/*     */     //   #113	-> 286
/*     */     //   #114	-> 314
/*     */     //   #117	-> 342
/*     */     //   #118	-> 350
/*     */     //   #125	-> 353
/*     */     //   #126	-> 365
/*     */     //   #127	-> 370
/*     */     //   #128	-> 400
/*     */     //   #129	-> 430
/*     */     //   #130	-> 460
/*     */     //   #131	-> 466
/*     */     //   #132	-> 475
/*     */     //   #133	-> 485
/*     */     //   #134	-> 495
/*     */     //   #135	-> 505
/*     */     //   #137	-> 514
/*     */     //   #140	-> 523
/*     */     //   #141	-> 534
/*     */     //   #144	-> 541
/*     */     //   #145	-> 554
/*     */     //   #146	-> 566
/*     */     //   #147	-> 595
/*     */     //   #149	-> 602
/*     */     //   #150	-> 613
/*     */     //   #151	-> 624
/*     */     //   #152	-> 634
/*     */     //   #153	-> 667
/*     */     //   #156	-> 700
/*     */     //   #157	-> 725
/*     */     //   #158	-> 730
/*     */     //   #160	-> 733
/*     */     //   #150	-> 740
/*     */     //   #144	-> 746
/*     */     //   #167	-> 752
/*     */     //   #170	-> 757
/*     */     //   #168	-> 760
/*     */     //   #169	-> 762
/*     */     //   #172	-> 767
/*     */     //   #173	-> 779
/*     */     //   #174	-> 787
/*     */     //   #175	-> 796
/*     */     //   #179	-> 805
/*     */     //   #180	-> 809
/*     */     //   #181	-> 817
/*     */     //   #182	-> 825
/*     */     //   #185	-> 834
/*     */     //   #186	-> 845
/*     */     //   #188	-> 848
/*     */     //   #190	-> 861
/*     */     //   #191	-> 869
/*     */     //   #192	-> 877
/*     */     //   #195	-> 895
/*     */     //   #196	-> 908
/*     */     //   #197	-> 922
/*     */     //   #198	-> 938
/*     */     //   #199	-> 945
/*     */     //   #200	-> 955
/*     */     //   #196	-> 962
/*     */     //   #195	-> 968
/*     */     //   #208	-> 974
/*     */     //   #211	-> 985
/*     */     //   #212	-> 995
/*     */     //   #213	-> 1017
/*     */     //   #214	-> 1039
/*     */     //   #217	-> 1043
/*     */     //   #219	-> 1053
/*     */     //   #220	-> 1064
/*     */     //   #222	-> 1072
/*     */     //   #223	-> 1077
/*     */     //   #224	-> 1106
/*     */     //   #225	-> 1133
/*     */     //   #226	-> 1139
/*     */     //   #227	-> 1142
/*     */     //   #228	-> 1144
/*     */     //   #229	-> 1153
/*     */     //   #232	-> 1156
/*     */     //   #233	-> 1161
/*     */     //   #234	-> 1190
/*     */     //   #235	-> 1217
/*     */     //   #236	-> 1223
/*     */     //   #240	-> 1226
/*     */     //   #241	-> 1236
/*     */     //   #242	-> 1248
/*     */     //   #243	-> 1255
/*     */     //   #244	-> 1306
/*     */     //   #245	-> 1355
/*     */     //   #246	-> 1361
/*     */     //   #250	-> 1364
/*     */     //   #251	-> 1389
/*     */     //   #252	-> 1394
/*     */     //   #253	-> 1445
/*     */     //   #254	-> 1494
/*     */     //   #255	-> 1500
/*     */     //   #256	-> 1503
/*     */     //   #257	-> 1515
/*     */     //   #258	-> 1522
/*     */     //   #259	-> 1573
/*     */     //   #260	-> 1622
/*     */     //   #261	-> 1628
/*     */     //   #208	-> 1631
/*     */     //   #266	-> 1637
/*     */     //   #268	-> 1644
/*     */     //   #269	-> 1657
/*     */     //   #270	-> 1671
/*     */     //   #271	-> 1687
/*     */     //   #272	-> 1694
/*     */     //   #273	-> 1704
/*     */     //   #269	-> 1711
/*     */     //   #268	-> 1717
/*     */     //   #277	-> 1723
/*     */     //   #278	-> 1729
/*     */     //   #281	-> 1735
/*     */     //   #284	-> 1740
/*     */     //   #286	-> 1751
/*     */     //   #288	-> 1757
/*     */     //   #289	-> 1767
/*     */     //   #290	-> 1789
/*     */     //   #293	-> 1811
/*     */     //   #295	-> 1821
/*     */     //   #296	-> 1832
/*     */     //   #298	-> 1840
/*     */     //   #299	-> 1845
/*     */     //   #300	-> 1874
/*     */     //   #301	-> 1901
/*     */     //   #302	-> 1904
/*     */     //   #303	-> 1906
/*     */     //   #304	-> 1915
/*     */     //   #305	-> 1924
/*     */     //   #307	-> 1927
/*     */     //   #308	-> 1932
/*     */     //   #309	-> 1961
/*     */     //   #310	-> 1988
/*     */     //   #315	-> 1991
/*     */     //   #316	-> 2001
/*     */     //   #318	-> 2004
/*     */     //   #319	-> 2015
/*     */     //   #320	-> 2038
/*     */     //   #321	-> 2079
/*     */     //   #322	-> 2088
/*     */     //   #323	-> 2092
/*     */     //   #324	-> 2105
/*     */     //   #325	-> 2133
/*     */     //   #326	-> 2146
/*     */     //   #328	-> 2171
/*     */     //   #329	-> 2180
/*     */     //   #330	-> 2209
/*     */     //   #493	-> 2211
/*     */     //   #496	-> 2218
/*     */     //   #499	-> 2225
/*     */     //   #497	-> 2228
/*     */     //   #498	-> 2230
/*     */     //   #499	-> 2236
/*     */     //   #333	-> 2239
/*     */     //   #334	-> 2249
/*     */     //   #337	-> 2290
/*     */     //   #338	-> 2305
/*     */     //   #340	-> 2315
/*     */     //   #345	-> 2320
/*     */     //   #342	-> 2323
/*     */     //   #343	-> 2325
/*     */     //   #344	-> 2330
/*     */     //   #346	-> 2368
/*     */     //   #349	-> 2371
/*     */     //   #350	-> 2375
/*     */     //   #352	-> 2388
/*     */     //   #353	-> 2439
/*     */     //   #355	-> 2488
/*     */     //   #356	-> 2519
/*     */     //   #358	-> 2570
/*     */     //   #359	-> 2581
/*     */     //   #360	-> 2585
/*     */     //   #361	-> 2589
/*     */     //   #366	-> 2592
/*     */     //   #368	-> 2619
/*     */     //   #369	-> 2624
/*     */     //   #371	-> 2631
/*     */     //   #372	-> 2634
/*     */     //   #375	-> 2639
/*     */     //   #376	-> 2647
/*     */     //   #378	-> 2654
/*     */     //   #380	-> 2663
/*     */     //   #382	-> 2676
/*     */     //   #383	-> 2686
/*     */     //   #385	-> 2704
/*     */     //   #387	-> 2718
/*     */     //   #388	-> 2728
/*     */     //   #390	-> 2733
/*     */     //   #391	-> 2745
/*     */     //   #392	-> 2750
/*     */     //   #393	-> 2757
/*     */     //   #420	-> 2761
/*     */     //   #396	-> 2764
/*     */     //   #398	-> 2766
/*     */     //   #400	-> 2777
/*     */     //   #402	-> 2795
/*     */     //   #403	-> 2802
/*     */     //   #404	-> 2866
/*     */     //   #407	-> 2885
/*     */     //   #409	-> 2890
/*     */     //   #411	-> 2906
/*     */     //   #412	-> 2966
/*     */     //   #413	-> 2974
/*     */     //   #493	-> 2976
/*     */     //   #496	-> 2983
/*     */     //   #499	-> 2990
/*     */     //   #497	-> 2993
/*     */     //   #498	-> 2995
/*     */     //   #499	-> 3001
/*     */     //   #420	-> 3004
/*     */     //   #416	-> 3007
/*     */     //   #417	-> 3009
/*     */     //   #418	-> 3014
/*     */     //   #419	-> 3020
/*     */     //   #423	-> 3023
/*     */     //   #425	-> 3034
/*     */     //   #426	-> 3045
/*     */     //   #427	-> 3052
/*     */     //   #428	-> 3055
/*     */     //   #429	-> 3072
/*     */     //   #431	-> 3075
/*     */     //   #432	-> 3102
/*     */     //   #435	-> 3142
/*     */     //   #436	-> 3153
/*     */     //   #437	-> 3157
/*     */     //   #461	-> 3167
/*     */     //   #438	-> 3170
/*     */     //   #440	-> 3172
/*     */     //   #442	-> 3184
/*     */     //   #443	-> 3191
/*     */     //   #444	-> 3235
/*     */     //   #445	-> 3263
/*     */     //   #446	-> 3298
/*     */     //   #447	-> 3328
/*     */     //   #451	-> 3337
/*     */     //   #452	-> 3345
/*     */     //   #454	-> 3350
/*     */     //   #456	-> 3363
/*     */     //   #457	-> 3423
/*     */     //   #458	-> 3431
/*     */     //   #493	-> 3433
/*     */     //   #496	-> 3440
/*     */     //   #499	-> 3447
/*     */     //   #497	-> 3450
/*     */     //   #498	-> 3452
/*     */     //   #499	-> 3458
/*     */     //   #463	-> 3461
/*     */     //   #464	-> 3469
/*     */     //   #465	-> 3473
/*     */     //   #470	-> 3480
/*     */     //   #472	-> 3488
/*     */     //   #475	-> 3493
/*     */     //   #476	-> 3508
/*     */     //   #478	-> 3518
/*     */     //   #484	-> 3523
/*     */     //   #481	-> 3526
/*     */     //   #482	-> 3528
/*     */     //   #483	-> 3533
/*     */     //   #284	-> 3571
/*     */     //   #487	-> 3577
/*     */     //   #493	-> 3587
/*     */     //   #496	-> 3594
/*     */     //   #499	-> 3601
/*     */     //   #497	-> 3604
/*     */     //   #498	-> 3606
/*     */     //   #501	-> 3612
/*     */     //   #490	-> 3615
/*     */     //   #491	-> 3617
/*     */     //   #493	-> 3623
/*     */     //   #496	-> 3630
/*     */     //   #499	-> 3637
/*     */     //   #497	-> 3640
/*     */     //   #498	-> 3642
/*     */     //   #501	-> 3648
/*     */     //   #493	-> 3651
/*     */     //   #496	-> 3660
/*     */     //   #499	-> 3667
/*     */     //   #497	-> 3670
/*     */     //   #498	-> 3672
/*     */     //   #501	-> 3678
/*     */     //   #502	-> 3681
/*     */     // Exception table:
/*     */     //   from	to	target	type
/*     */     //   353	2211	3615	java/lang/Exception
/*     */     //   353	2211	3651	finally
/*     */     //   752	757	760	java/lang/Exception
/*     */     //   1072	1139	1142	java/lang/Exception
/*     */     //   1840	1901	1904	java/lang/Exception
/*     */     //   2218	2225	2228	java/lang/Exception
/*     */     //   2239	2976	3615	java/lang/Exception
/*     */     //   2239	2976	3651	finally
/*     */     //   2290	2320	2323	java/lang/Exception
/*     */     //   2639	2761	2764	weaver/conn/mybatis/MysqlScriptRunnerException
/*     */     //   2639	2761	3007	java/lang/Exception
/*     */     //   2983	2990	2993	java/lang/Exception
/*     */     //   3004	3433	3615	java/lang/Exception
/*     */     //   3004	3433	3651	finally
/*     */     //   3142	3167	3170	java/lang/Exception
/*     */     //   3440	3447	3450	java/lang/Exception
/*     */     //   3461	3587	3615	java/lang/Exception
/*     */     //   3461	3587	3651	finally
/*     */     //   3493	3523	3526	java/lang/Exception
/*     */     //   3594	3601	3604	java/lang/Exception
/*     */     //   3615	3623	3651	finally
/*     */     //   3630	3637	3640	java/lang/Exception
/*     */     //   3651	3653	3651	finally
/*     */     //   3660	3667	3670	java/lang/Exception
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void insertExecuteTime(long paramLong, String paramString) {
/* 511 */     RecordSet recordSet = new RecordSet();
/* 512 */     Calendar calendar = Calendar.getInstance();
/* 513 */     long l = calendar.getTimeInMillis();
/* 514 */     double d = (l - paramLong);
/* 515 */     if (d >= 0.0D) {
/* 516 */       d /= 1000.0D;
/*     */     } else {
/* 518 */       d = 0.0D;
/*     */     } 
/*     */     
/* 521 */     this.currentDate = TimeUtil.getCurrentDateString();
/* 522 */     this.currentTime = TimeUtil.getCurrentTimeString().substring(11);
/* 523 */     recordSet.executeUpdate("insert into SqlFileLogInfo(sqlfilename,rundate,runtime,packageno,exectime)values('" + paramString + "','" + this.currentDate + "','" + this.currentTime + "','','" + d + "')", new Object[0]);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void markErrorLog(String paramString1, String paramString2, int paramInt, Exception paramException) {
/*     */     try {
/* 532 */       errorbuffer = new StringBuffer();
/* 533 */       errorbuffer.append("错误位置").append("<br>");
/* 534 */       errorbuffer.append("  " + paramString1 + "第" + paramInt + "行").append("<br>");
/* 535 */       errorbuffer.append("错误语句").append("<br>");
/* 536 */       errorbuffer.append(paramString2).append("<br>");
/* 537 */       errorbuffer.append("错误原因").append("<br>");
/* 538 */       String str = paramException.toString();
/* 539 */       str = str.replaceAll("'", "  ");
/*     */       
/* 541 */       errorbuffer.append("  " + str).append("<br>");
/* 542 */       markErrorLog2Sysupgradelog(paramString1, paramString2, paramInt, paramException);
/*     */       
/* 544 */       DBUpgradeLogger.write2File(errorbuffer.toString().replace("<br>", "\r\n"));
/* 545 */     } catch (Exception exception) {
/* 546 */       exception.printStackTrace();
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String markErrorLog2Sysupgradelog(String paramString1, String paramString2, int paramInt, Exception paramException) {
/* 556 */     String str1 = GCONST.getRootPath() + "sysupgradelog" + File.separatorChar;
/* 557 */     FileManage.createDir(str1);
/* 558 */     String str2 = str1 + this.currentDate + ".log";
/*     */     
/*     */     try {
/* 561 */       PrintWriter printWriter = new PrintWriter(new FileWriter(str2, true));
/* 562 */       printWriter.println("错误时间");
/* 563 */       printWriter.println("  " + this.currentTime);
/* 564 */       printWriter.println("错误位置");
/* 565 */       printWriter.println("  " + paramString1 + "第" + paramInt + "行");
/* 566 */       printWriter.println("错误语句");
/* 567 */       printWriter.println(paramString2);
/* 568 */       printWriter.println("错误原因");
/* 569 */       String str = paramException.toString();
/* 570 */       printWriter.println("  " + str);
/*     */       
/* 572 */       str = str.replaceAll("'", "  ");
/*     */       
/* 574 */       printWriter.println("");
/* 575 */       printWriter.println("");
/* 576 */       printWriter.flush();
/* 577 */       printWriter.close();
/* 578 */     } catch (IOException iOException) {
/* 579 */       iOException.printStackTrace();
/*     */     } 
/* 581 */     return str2;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void changeStatus(int paramInt1, int paramInt2, int paramInt3, String paramString, int paramInt4) {
/* 594 */     RecordSet recordSet = new RecordSet();
/* 595 */     recordSet.executeQuery("select 1 from DBUpgradeSqlCounter", new Object[0]);
/* 596 */     if (!recordSet.next()) {
/* 597 */       recordSet.executeUpdate("insert into DBUpgradeSqlCounter(status,runned,total,runfilename,errorline) values (?,?,?,?,?)", new Object[] { Integer.valueOf(paramInt1), Integer.valueOf(paramInt2), Integer.valueOf(paramInt3), paramString, Integer.valueOf(paramInt4) });
/*     */     } else {
/* 599 */       recordSet.executeUpdate("update DBUpgradeSqlCounter set status=?,runned=?,total=?,runfilename=?,errorline=?", new Object[] { Integer.valueOf(paramInt1), Integer.valueOf(paramInt2), Integer.valueOf(paramInt3), paramString, Integer.valueOf(paramInt4) });
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getScriptbreak() {
/* 609 */     String str1 = "";
/* 610 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 612 */     String str2 = recordSet.getDBType().toLowerCase();
/* 613 */     boolean bool1 = str2.equals("oracle");
/* 614 */     boolean bool2 = str2.equals("db2");
/* 615 */     boolean bool3 = str2.equals("mysql");
/* 616 */     boolean bool4 = str2.equals("pg");
/* 617 */     boolean bool5 = str2.equals("dm");
/* 618 */     boolean bool6 = str2.equals("jc");
/* 619 */     boolean bool7 = str2.equals("hg");
/* 620 */     boolean bool8 = str2.equals("og");
/* 621 */     if (bool1 || bool6) {
/* 622 */       str1 = "/";
/* 623 */     } else if (bool2) {
/* 624 */       str1 = ";";
/* 625 */     } else if (bool3) {
/* 626 */       str1 = ";";
/* 627 */     } else if (bool5) {
/* 628 */       str1 = "/";
/*     */     }
/* 630 */     else if (bool4) {
/* 631 */       str1 = "/";
/*     */     }
/* 633 */     else if (bool7) {
/* 634 */       str1 = "/";
/*     */     }
/* 636 */     else if (bool8) {
/* 637 */       str1 = "/";
/*     */     } else {
/*     */       
/* 640 */       str1 = "GO";
/*     */     } 
/*     */     
/* 643 */     return str1;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getDBFileName(LinkedHashMap<String, String> paramLinkedHashMap) {
/* 648 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 650 */     String str = recordSet.getDBType().toLowerCase();
/* 651 */     return paramLinkedHashMap.get(str);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void writeLog(String paramString) {
/* 658 */     DBUpgradeLogger.write2File(paramString);
/*     */   }
/*     */   
/*     */   public ArrayList<String> getLeaveSqlFilePathList(LinkedHashMap<String, String> paramLinkedHashMap) {
/* 662 */     ArrayList<String> arrayList = new ArrayList();
/* 663 */     Iterator<Map.Entry> iterator = paramLinkedHashMap.entrySet().iterator();
/* 664 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 666 */     String str = recordSet.getDBType().toLowerCase();
/* 667 */     while (iterator.hasNext()) {
/* 668 */       Map.Entry entry = iterator.next();
/* 669 */       String str1 = (String)entry.getKey();
/* 670 */       if (!str.equalsIgnoreCase(str1)) {
/* 671 */         arrayList.add(entry.getValue());
/*     */       }
/*     */     } 
/*     */     
/* 675 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void writeLog(String paramString1, String paramString2) {
/* 681 */     this.upgradeLog = this.upgradeRecordSet.getUpgradeLogObj(paramString1);
/* 682 */     DBUpgradeLogger.write2File(paramString1 + "$result:" + paramString2);
/* 683 */     if ("true".equals(paramString2)) {
/* 684 */       this.upgradeLog.setModifyStatus("1");
/*     */     } else {
/* 686 */       this.upgradeLog.setModifyStatus("0");
/*     */     } 
/*     */     
/* 689 */     DBUpgradeLogger.write2DB(this.upgradeLog);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void writeLog(Exception paramException) {
/* 696 */     DBUpgradeLogger.write2File(paramException.getMessage());
/*     */   }
/*     */ 
/*     */   
/*     */   public void startAction(HashMap<String, String> paramHashMap) {
/* 701 */     DBUpgradeLogger.write2File("开始--执行脚本...");
/* 702 */     ActionProcess.getInstance().setActionProcess("0");
/* 703 */     ActionProcess.getInstance().setActionProcessName("开始执行脚本");
/*     */   }
/*     */ 
/*     */   
/*     */   public void endAction(HashMap<String, String> paramHashMap) {
/* 708 */     DBUpgradeLogger.write2File("结束--执行脚本..");
/* 709 */     ActionProcess.getInstance().setActionProcess("100");
/* 710 */     ActionProcess.getInstance().setActionProcessName("执行脚本完成");
/*     */   }
/*     */   
/*     */   public void setActionProcess(String paramString) {}
/*     */   
/*     */   public void setActionProcessName(String paramString) {}
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/upgradetool/dbupgrade/actions/upgrade/ExecuteSqlAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */