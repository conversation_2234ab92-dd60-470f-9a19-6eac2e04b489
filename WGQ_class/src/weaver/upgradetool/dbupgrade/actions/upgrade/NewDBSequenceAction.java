/*     */ package weaver.upgradetool.dbupgrade.actions.upgrade;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import java.io.File;
/*     */ import java.util.HashMap;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.MathUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.upgradetool.dbupgrade.actions.ActionInterface;
/*     */ import weaver.upgradetool.dbupgrade.actions.ActionProcess;
/*     */ import weaver.upgradetool.dbupgrade.logger.DBUpgradeLogger;
/*     */ import weaver.upgradetool.dbupgrade.logger.UpgradeLog;
/*     */ import weaver.upgradetool.dbupgrade.upgrade.PropUtil;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class NewDBSequenceAction
/*     */   implements ActionInterface
/*     */ {
/*     */   public String execute(HashMap<String, String> paramHashMap) {
/*  28 */     startAction(null);
/*  29 */     JSONObject jSONObject = new JSONObject();
/*  30 */     boolean bool = synchroSequence();
/*     */     
/*  32 */     if (bool) {
/*  33 */       jSONObject.put("status", "success");
/*     */     } else {
/*  35 */       jSONObject.put("status", "failure");
/*     */     } 
/*  37 */     endAction(null);
/*  38 */     return jSONObject.toJSONString();
/*     */   }
/*     */ 
/*     */   
/*     */   public void startAction(HashMap<String, String> paramHashMap) {
/*  43 */     DBUpgradeLogger.write2File("startAction  NewDBSequenceAction......");
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void endAction(HashMap<String, String> paramHashMap) {
/*  49 */     DBUpgradeLogger.write2File("endAction  NewDBSequenceAction......");
/*     */   }
/*     */ 
/*     */   
/*     */   public void setActionProcess(String paramString) {
/*  54 */     ActionProcess.getInstance().setActionProcess(paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void setActionProcessName(String paramString) {
/*  60 */     ActionProcess.getInstance().setActionProcessName(paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean synchroSequence() {
/*  69 */     boolean bool = true;
/*     */     
/*  71 */     String str1 = GCONST.getRootPath() + "system" + File.separatorChar + "upgradetoe9" + File.separatorChar + "resource" + File.separatorChar + "migration.properties";
/*  72 */     PropUtil propUtil = PropUtil.getInstance(new String[] { str1 });
/*     */     
/*  74 */     String str2 = propUtil.getValues("synctables");
/*  75 */     String[] arrayOfString = str2.split(",");
/*     */ 
/*     */ 
/*     */     
/*  79 */     String str3 = "";
/*     */     
/*  81 */     RecordSet recordSet = new RecordSet();
/*  82 */     DBUpgradeLogger dBUpgradeLogger = new DBUpgradeLogger();
/*  83 */     String str4 = recordSet.getDBType();
/*  84 */     if (str4.equalsIgnoreCase("oracle")) {
/*  85 */       RecordSet recordSet1 = new RecordSet();
/*     */       
/*  87 */       String str5 = "SELECT s.text,t.table_name FROM USER_SOURCE s left join user_triggers t on upper(t.trigger_name)=upper(s.name)  WHERE TYPE='TRIGGER' and upper(text) like '%BEFORE INSERT ON ";
/*     */ 
/*     */       
/*  90 */       String str6 = "drop sequence ";
/*  91 */       String str7 = "";
/*  92 */       String str8 = "";
/*  93 */       int i = 0;
/*  94 */       String str9 = "";
/*  95 */       UpgradeLog upgradeLog = new UpgradeLog();
/*     */       
/*     */       try {
/*  98 */         for (byte b = 0; b < arrayOfString.length; b++) {
/*  99 */           if (arrayOfString[b] != null) {
/* 100 */             str3 = arrayOfString[b];
/* 101 */             str3 = str3.toUpperCase().trim();
/*     */           } 
/*     */           
/* 104 */           double d = MathUtil.div((b * 100), arrayOfString.length, 1);
/* 105 */           setActionProcess(d + "");
/*     */           
/* 107 */           recordSet.executeQuery("select table_name From user_tables where table_name=? ", new Object[] { str3 });
/* 108 */           if (!recordSet.next()) {
/* 109 */             DBUpgradeLogger.write2File("表名:" + str3 + "在e9环境中不存在,无需同步.");
/*     */           } else {
/*     */             
/* 112 */             DBUpgradeLogger.write2File("表名:" + str3 + "");
/* 113 */             recordSet.executeQuery(str5 + str3 + "%' and t.table_name=?", new Object[] { str3 });
/* 114 */             while (recordSet.next()) {
/* 115 */               String str10 = recordSet.getString(1).toUpperCase();
/*     */               
/* 117 */               int j = str10.indexOf(" select ".toUpperCase()) + " select ".length();
/* 118 */               int k = str10.indexOf(".nextval".toUpperCase());
/* 119 */               int m = str10.indexOf(":new.".toUpperCase()) + ":new.".length();
/* 120 */               int n = str10.indexOf(" from ".toUpperCase());
/*     */ 
/*     */               
/* 123 */               if (m > n) {
/* 124 */                 n = str10.indexOf(":=new".toUpperCase());
/*     */               }
/* 126 */               if (j == 7 || k == -1 || m == 4 || n == -1 || m > n) {
/* 127 */                 DBUpgradeLogger.write2File(str3 + "的sequence获取错误");
/*     */                 
/*     */                 continue;
/*     */               } 
/* 131 */               DBUpgradeLogger.write2File("startS:" + j + ",endS:" + k + ",startI:" + m + ",endI:" + n + "<br>");
/* 132 */               str9 = str10.substring(j, k).trim();
/* 133 */               String str11 = str10.substring(m, n).trim();
/* 134 */               DBUpgradeLogger.write2File("开始同步" + str9 + "：");
/*     */ 
/*     */               
/* 137 */               if (str3.equalsIgnoreCase("imagelibrary")) {
/*     */                 continue;
/*     */               }
/* 140 */               String str12 = "select max(" + str11 + ") from " + str3;
/* 141 */               recordSet1.executeQuery(str12, new Object[0]);
/* 142 */               if (recordSet1.next()) {
/* 143 */                 str7 = Util.null2String(recordSet1.getString(1));
/* 144 */                 if (str7 == "") {
/* 145 */                   DBUpgradeLogger.write2File(str3 + "的max(" + str11 + ")为空");
/*     */                   
/*     */                   continue;
/*     */                 } 
/* 149 */                 recordSet1.executeQuery("select max_value,min_value from user_sequences where sequence_name='" + str9 + "'", new Object[0]);
/* 150 */                 if (recordSet1.next()) {
/*     */                   
/* 152 */                   String str = recordSet1.getString("max_value");
/* 153 */                   if (str != null && str.trim().startsWith("-")) {
/* 154 */                     str7 = (Long.parseLong(str7) - 4L) + "";
/*     */                   } else {
/*     */                     
/* 157 */                     str7 = (Long.parseLong(str7) + 4L) + "";
/*     */                   } 
/*     */                 } else {
/*     */                   
/* 161 */                   str7 = (Long.parseLong(str7) + 4L) + "";
/*     */                 } 
/* 163 */                 DBUpgradeLogger.write2File("sequenceName:" + str9 + ",maxId:" + str7 + ">");
/*     */               } 
/* 165 */               DBUpgradeLogger.write2File(str6 + str9 + "");
/*     */               
/* 167 */               recordSet1.executeQuery("select  'create sequence ' ||sequence_name|| ' minvalue ' ||min_value|| ' maxvalue ' ||max_value|| ' start with " + str7 + "' || ' increment by ' ||increment_by|| ( case  when cache_size= 0  then  ' nocache'   else   ' cache ' ||cache_size end) || ( case  when cycle_flag='N' then  ' nocycle' when cycle_flag='Y' then ' cycle' else   ' cycle ' ||cycle_flag end) || ( case  when order_flag='N' then  ' noorder' when order_flag='Y' then ' order' else   ' order ' ||order_flag end) ,min_value from user_sequences where sequence_name=?", new Object[] { str9 });
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */               
/* 177 */               if (recordSet1.next()) {
/* 178 */                 str8 = recordSet1.getString(1);
/* 179 */                 String str = recordSet1.getString(2);
/* 180 */                 if (str == null || "".equals(str)) {
/* 181 */                   i = 1;
/*     */                 } else {
/* 183 */                   i = recordSet1.getInt(2);
/*     */                 } 
/* 185 */                 if (str7 == null || "".equals(str7)) {
/* 186 */                   DBUpgradeLogger.write2File("maxId为空,暂不处理.<br>");
/*     */                   continue;
/*     */                 } 
/* 189 */                 if (Integer.parseInt(str7) < i) {
/* 190 */                   DBUpgradeLogger.write2File("maxId小于sequence的最小值,暂不处理.<br>");
/*     */                   
/*     */                   continue;
/*     */                 } 
/*     */               } 
/* 195 */               if (!"".equals(str8)) {
/*     */                 
/* 197 */                 recordSet1.executeUpdate(str6 + str9, new Object[0]);
/* 198 */                 DBUpgradeLogger.write2File(str6 + str9);
/* 199 */                 upgradeLog = dBUpgradeLogger.getUpgradeLogObj(str6 + str9);
/* 200 */                 upgradeLog.setModifyStatus("1");
/* 201 */                 DBUpgradeLogger.write2DB(upgradeLog);
/*     */ 
/*     */                 
/* 204 */                 recordSet1.executeUpdate(str8, new Object[0]);
/* 205 */                 DBUpgradeLogger.write2File(str8);
/* 206 */                 upgradeLog = dBUpgradeLogger.getUpgradeLogObj(str8);
/* 207 */                 upgradeLog.setModifyStatus("1");
/* 208 */                 DBUpgradeLogger.write2DB(upgradeLog);
/*     */               } 
/*     */             } 
/*     */           } 
/* 212 */         }  DBUpgradeLogger.write2File("同步sequence结束");
/* 213 */       } catch (Exception exception) {
/* 214 */         DBUpgradeLogger.write2File("同步sequence" + str9 + "异常:" + exception.getMessage());
/* 215 */         DBUpgradeLogger.write2File("同步sequence" + str9 + "异常:" + exception.getStackTrace());
/* 216 */         bool = false;
/* 217 */         exception.printStackTrace();
/*     */       }
/*     */       finally {}
/* 220 */     } else if (str4.equalsIgnoreCase("sqlserver")) {
/*     */     
/*     */     } 
/*     */     
/* 224 */     return bool;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/upgradetool/dbupgrade/actions/upgrade/NewDBSequenceAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */