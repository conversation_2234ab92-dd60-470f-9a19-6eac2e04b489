/*     */ package weaver.upgradetool.dbupgrade.actions.upgrade;
/*     */ 
/*     */ import java.util.HashMap;
/*     */ import weaver.general.Util;
/*     */ import weaver.upgradetool.dbupgrade.actions.ActionInterface;
/*     */ import weaver.upgradetool.dbupgrade.logger.DBUpgradeLogger;
/*     */ import weaver.upgradetool.dbupgrade.upgrade.UpgradeRecordSet;
/*     */ 
/*     */ public class ReportAction
/*     */   implements ActionInterface {
/*  11 */   private String resStr = new String();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String execute(HashMap<String, String> paramHashMap) {
/*  21 */     startAction(paramHashMap);
/*  22 */     return this.resStr;
/*     */   }
/*     */ 
/*     */   
/*     */   public void startAction(HashMap<String, String> paramHashMap) {
/*  27 */     UpgradeRecordSet upgradeRecordSet = new UpgradeRecordSet();
/*  28 */     String str1 = upgradeRecordSet.getDBType();
/*  29 */     StringBuffer stringBuffer = new StringBuffer();
/*  30 */     String str2 = Util.null2String(paramHashMap.get("myname"));
/*  31 */     if (!"weaver@_ecolo".equals(str2)) {
/*  32 */       DBUpgradeLogger.write2File("myname为" + str2 + "，不正确，直接返回空数据");
/*  33 */       this.resStr = "[]";
/*     */     } else {
/*  35 */       String str3 = Util.null2String(paramHashMap.get("tableName"));
/*  36 */       String str4 = Util.null2String(paramHashMap.get("fieldName"));
/*  37 */       String str5 = Util.null2String(paramHashMap.get("pageIndex"));
/*  38 */       String str6 = Util.null2String(paramHashMap.get("pageSize"));
/*  39 */       String str7 = Util.null2String(paramHashMap.get("modifys"));
/*  40 */       DBUpgradeLogger.write2File("tableName-" + str3);
/*  41 */       DBUpgradeLogger.write2File("fieldName-" + str4);
/*  42 */       DBUpgradeLogger.write2File("pageIndex-" + str5);
/*  43 */       DBUpgradeLogger.write2File("pageSize-" + str6);
/*  44 */       DBUpgradeLogger.write2File("modifyRes-" + str7);
/*  45 */       String[] arrayOfString = str7.split("-");
/*  46 */       if (str7.length() > 0) {
/*  47 */         str7 = "(";
/*  48 */         for (byte b1 = 0; b1 < arrayOfString.length; b1++) {
/*  49 */           if (!arrayOfString[b1].trim().equals("")) {
/*  50 */             str7 = str7 + arrayOfString[b1] + ",";
/*     */           }
/*     */         } 
/*  53 */         str7 = str7.substring(0, str7.length() - 1) + ")";
/*     */       } 
/*     */       
/*  56 */       int i = Integer.parseInt(str5);
/*  57 */       int j = Integer.parseInt(str6);
/*  58 */       int k = (i - 1) * j + 1;
/*  59 */       int m = k + j - 1;
/*  60 */       StringBuffer stringBuffer1 = new StringBuffer("select count(id) as count from DBUpgradeLog where 1 = 1 ");
/*  61 */       if (str1.equalsIgnoreCase("oracle")) {
/*  62 */         stringBuffer.append("select rownum myrownum,t.*  from DBUpgradeLog t  where 1 = 1");
/*  63 */       } else if (str1.equalsIgnoreCase("mysql")) {
/*  64 */         stringBuffer.append("select * from DBUpgradeLog where 1 = 1 ");
/*     */       }
/*  66 */       else if (str1.equalsIgnoreCase("postgresql")) {
/*  67 */         stringBuffer.append("select * from DBUpgradeLog where 1 = 1 ");
/*     */       }
/*  69 */       else if (str1.equalsIgnoreCase("sqlserver")) {
/*  70 */         stringBuffer.append("select top " + str6 + " * from DBUpgradeLog where 1 = 1 ");
/*  71 */       } else if (str1.equalsIgnoreCase("DM")) {
/*  72 */         stringBuffer.append("select rownum myrownum,t.*  from DBUpgradeLog t  where 1 = 1 ");
/*     */       } 
/*  74 */       StringBuffer stringBuffer2 = new StringBuffer();
/*  75 */       if (!"".equals(str3)) {
/*  76 */         stringBuffer2.append(" AND UPPER(MODIFYNAME)  like UPPER('%" + str3 + "%') ");
/*     */       }
/*  78 */       if (!"".equals(str4)) {
/*  79 */         stringBuffer2.append(" AND UPPER(MODIFYFIELDNAME) like UPPER('%" + str4 + "%') ");
/*     */       }
/*  81 */       if (!"".equals(str7)) {
/*  82 */         stringBuffer2.append(" AND MODIFYSTATUS  IN " + str7);
/*     */       }
/*  84 */       stringBuffer.append(stringBuffer2);
/*  85 */       stringBuffer1.append(stringBuffer2);
/*  86 */       String str8 = "";
/*     */       
/*  88 */       if (str1.equalsIgnoreCase("oracle")) {
/*  89 */         str8 = "select * FROM (" + stringBuffer.toString() + ") where myrownum between " + k + " and " + m;
/*  90 */       } else if (str1.equalsIgnoreCase("mysql")) {
/*  91 */         str8 = stringBuffer.toString() + " limit " + (k - 1) + "," + str6;
/*     */       }
/*  93 */       else if (str1.equalsIgnoreCase("postgresql")) {
/*  94 */         str8 = stringBuffer.toString() + " limit " + str6 + " offset " + (k - 1);
/*     */       }
/*  96 */       else if (str1.equalsIgnoreCase("sqlserver")) {
/*  97 */         UpgradeRecordSet upgradeRecordSet2 = new UpgradeRecordSet();
/*  98 */         String str = "0";
/*  99 */         StringBuffer stringBuffer4 = new StringBuffer("select max(b.id) as minid from (select  top ");
/* 100 */         stringBuffer4.append(k - 1);
/* 101 */         stringBuffer4.append(" id from DBUpgradeLog WHERE 1=1 ");
/* 102 */         stringBuffer4.append(stringBuffer2);
/* 103 */         stringBuffer4.append(" )t  left join DBUpgradeLog b on t.id=b.id");
/* 104 */         upgradeRecordSet2.executeQuery(stringBuffer4.toString(), new Object[0]);
/* 105 */         if (upgradeRecordSet2.next()) {
/* 106 */           str = Util.null2String(upgradeRecordSet2.getString("minid"));
/*     */         }
/* 108 */         if ("".equals(str)) {
/* 109 */           str = "0";
/*     */         }
/* 111 */         stringBuffer.append("and id > ");
/* 112 */         stringBuffer.append(str);
/* 113 */         stringBuffer.append(stringBuffer2);
/* 114 */         str8 = stringBuffer.toString();
/* 115 */       } else if (str1.equalsIgnoreCase("DM")) {
/* 116 */         str8 = "select * FROM (" + stringBuffer.toString() + ") where myrownum between " + k + " and " + m;
/*     */       } 
/* 118 */       DBUpgradeLogger.write2File("查询本页sql：" + str8);
/* 119 */       upgradeRecordSet.executeQuery(str8, new Object[0]);
/* 120 */       StringBuffer stringBuffer3 = new StringBuffer();
/* 121 */       stringBuffer3.append("[");
/* 122 */       String str9 = stringBuffer1.toString();
/* 123 */       UpgradeRecordSet upgradeRecordSet1 = new UpgradeRecordSet();
/* 124 */       DBUpgradeLogger.write2File("查总条数sql：" + str9);
/* 125 */       upgradeRecordSet1.executeQuery(str9, new Object[0]);
/* 126 */       int n = 0;
/* 127 */       if (upgradeRecordSet1.next()) {
/* 128 */         n = upgradeRecordSet1.getInt(1);
/*     */       }
/* 130 */       byte b = 0;
/* 131 */       if (n > 0) {
/* 132 */         while (b < j * (i - 1)) {
/* 133 */           stringBuffer3.append("{},");
/* 134 */           b++;
/*     */         } 
/*     */       }
/* 137 */       while (upgradeRecordSet.next()) {
/* 138 */         stringBuffer3.append("{\"MODIFYNAME\":\"").append(replaceAll(replaceBlank(upgradeRecordSet.getString("MODIFYNAME")))).append("\",");
/* 139 */         stringBuffer3.append("\"MODIFYFIELDNAME\":\"").append(replaceAll(replaceBlank(upgradeRecordSet.getString("MODIFYFIELDNAME")))).append("\",");
/* 140 */         stringBuffer3.append("\"MODIFYTYPE\":\"").append(replaceMODIFYTYPE(replaceAll(replaceBlank(upgradeRecordSet.getString("MODIFYTYPE"))))).append("\",");
/* 141 */         stringBuffer3.append("\"MODIFYCONTENT\":\"").append(replaceAll(replaceBlank(upgradeRecordSet.getString("MODIFYCONTENT")))).append("\",");
/* 142 */         stringBuffer3.append("\"MODIFYSTATUS\":\"").append(replaceMODIFYSTATUS(replaceAll(replaceBlank(upgradeRecordSet.getString("MODIFYSTATUS"))))).append("\",");
/* 143 */         stringBuffer3.append("\"TYPE\":\"").append(replaceTYPE(replaceAll(replaceBlank(upgradeRecordSet.getString("TYPE"))))).append("\",");
/* 144 */         stringBuffer3.append("\"ERRORLOG\":\"").append(replaceAll(replaceBlank(upgradeRecordSet.getString("ERRORLOG")))).append("\",");
/* 145 */         stringBuffer3.append("\"MODIFYTIME\":\"").append(replaceAll(replaceBlank(upgradeRecordSet.getString("MODIFYTIME")))).append("\"},");
/* 146 */         b++;
/*     */       } 
/* 148 */       while (b < n) {
/* 149 */         stringBuffer3.append("{},");
/* 150 */         b++;
/*     */       } 
/* 152 */       if (n > 0) {
/* 153 */         this.resStr = stringBuffer3.substring(0, stringBuffer3.length() - 1);
/*     */       } else {
/* 155 */         this.resStr = stringBuffer3.toString();
/*     */       } 
/* 157 */       this.resStr += "]";
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public String replaceMODIFYSTATUS(String paramString) {
/* 164 */     String str = "其他";
/* 165 */     switch (paramString) {
/*     */       case "0":
/* 167 */         str = "失败";
/*     */         break;
/*     */       case "1":
/* 170 */         str = "成功";
/*     */         break;
/*     */       case "2":
/* 173 */         str = "不修改";
/*     */         break;
/*     */     } 
/* 176 */     return str;
/*     */   }
/*     */   
/*     */   public String replaceMODIFYTYPE(String paramString) {
/* 180 */     String str = "其他";
/* 181 */     switch (paramString) {
/*     */       case "1":
/* 183 */         str = "表";
/*     */         break;
/*     */       case "2":
/* 186 */         str = "字段";
/*     */         break;
/*     */       case "3":
/* 189 */         str = "数据";
/*     */         break;
/*     */       case "4":
/* 192 */         str = "存储过程";
/*     */         break;
/*     */       case "5":
/* 195 */         str = "触发器";
/*     */         break;
/*     */       case "6":
/* 198 */         str = "方法";
/*     */         break;
/*     */       case "7":
/* 201 */         str = "视图";
/*     */         break;
/*     */       case "8":
/* 204 */         str = "序列";
/*     */         break;
/*     */     } 
/* 207 */     return str;
/*     */   }
/*     */   
/*     */   public String replaceTYPE(String paramString) {
/* 211 */     String str = "其他";
/* 212 */     switch (paramString) {
/*     */       case "10":
/* 214 */         str = "删除表";
/*     */         break;
/*     */       case "11":
/* 217 */         str = "新增表";
/*     */         break;
/*     */       case "20":
/* 220 */         str = "删除字段";
/*     */         break;
/*     */       case "21":
/* 223 */         str = "新增字段";
/*     */         break;
/*     */       case "22":
/* 226 */         str = "更新字段";
/*     */         break;
/*     */       case "30":
/* 229 */         str = "删除数据";
/*     */         break;
/*     */       case "31":
/* 232 */         str = "插入数据";
/*     */         break;
/*     */       case "32":
/* 235 */         str = "清空数据";
/*     */         break;
/*     */       case "33":
/* 238 */         str = "更新数据";
/*     */         break;
/*     */       case "40":
/* 241 */         str = "删除存储过程";
/*     */         break;
/*     */       case "41":
/* 244 */         str = "新增存储过程";
/*     */         break;
/*     */       case "42":
/* 247 */         str = "更新存储过程";
/*     */         break;
/*     */       case "50":
/* 250 */         str = "删除触发器";
/*     */         break;
/*     */       case "51":
/* 253 */         str = "新增触发器";
/*     */         break;
/*     */       case "52":
/* 256 */         str = "更新触发器";
/*     */         break;
/*     */       case "60":
/* 259 */         str = "删除方法";
/*     */         break;
/*     */       case "61":
/* 262 */         str = "新增方法";
/*     */         break;
/*     */       case "62":
/* 265 */         str = "更新方法";
/*     */         break;
/*     */       case "70":
/* 268 */         str = "删除视图";
/*     */         break;
/*     */       case "71":
/* 271 */         str = "新增视图";
/*     */         break;
/*     */       case "72":
/* 274 */         str = "更新视图";
/*     */         break;
/*     */       case "80":
/* 277 */         str = "删除序列";
/*     */         break;
/*     */       case "81":
/* 280 */         str = "新增序列";
/*     */         break;
/*     */       case "82":
/* 283 */         str = "更新序列";
/*     */         break;
/*     */       case "90":
/* 286 */         str = "删除索引";
/*     */         break;
/*     */       case "91":
/* 289 */         str = "新增索引";
/*     */         break;
/*     */       case "92":
/* 292 */         str = "更新索引";
/*     */         break;
/*     */       case "100":
/* 295 */         str = "删除约束";
/*     */         break;
/*     */       case "101":
/* 298 */         str = "新增约束";
/*     */         break;
/*     */       case "102":
/* 301 */         str = "更新约束";
/*     */         break;
/*     */     } 
/* 304 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void endAction(HashMap<String, String> paramHashMap) {}
/*     */ 
/*     */ 
/*     */   
/*     */   public void setActionProcess(String paramString) {}
/*     */ 
/*     */ 
/*     */   
/*     */   public void setActionProcessName(String paramString) {}
/*     */ 
/*     */ 
/*     */   
/*     */   private String replaceAll(String paramString) {
/* 323 */     return paramString.replaceAll(">", "&gt;").replaceAll("<", "&lt;").replaceAll(" ", "&nbsp;").replaceAll("'", "&apos;").replaceAll("\"", "&quot;").replaceAll("\n", "\\\\n");
/*     */   }
/*     */   private String replaceBlank(String paramString) {
/* 326 */     return Util.null2String(paramString).equals("") ? "&nbsp" : paramString;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/upgradetool/dbupgrade/actions/upgrade/ReportAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */