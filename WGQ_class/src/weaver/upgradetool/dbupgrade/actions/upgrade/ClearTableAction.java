/*     */ package weaver.upgradetool.dbupgrade.actions.upgrade;
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import java.sql.Connection;
/*     */ import java.sql.ResultSet;
/*     */ import java.sql.SQLException;
/*     */ import java.sql.Statement;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Arrays;
/*     */ import java.util.HashMap;
/*     */ import java.util.HashSet;
/*     */ import java.util.Iterator;
/*     */ import java.util.LinkedList;
/*     */ import java.util.List;
/*     */ import org.apache.commons.lang.StringUtils;
/*     */ import weaver.upgradetool.dbupgrade.actions.ActionProcess;
/*     */ import weaver.upgradetool.dbupgrade.logger.DBUpgradeLogger;
/*     */ import weaver.upgradetool.dbupgrade.upgrade.PropUtil;
/*     */ import weaver.upgradetool.dbupgrade.upgrade.UpgradeRecordSet;
/*     */ 
/*     */ public class ClearTableAction implements ActionInterface {
/*  21 */   private DBUtil dbutil = new DBUtil();
/*  22 */   PropUtil propUtil = PropUtil.getInstance(new String[] { PropUtil.MIGRATION });
/*     */ 
/*     */   
/*     */   public String execute(HashMap<String, String> paramHashMap) {
/*  26 */     JSONObject jSONObject = new JSONObject();
/*     */     
/*  28 */     startAction(null);
/*  29 */     DBUpgradeLogger.write2File("====ClearTableAction 开始获取E9中所有表");
/*  30 */     List<String> list1 = getE9Tables();
/*  31 */     DBUpgradeLogger.write2File("====ClearTableAction E9中所有表为：" + Arrays.toString(list1.toArray()));
/*  32 */     DBUpgradeLogger.write2File("====ClearTableAction 获取E9中所有表结束");
/*     */ 
/*     */     
/*  35 */     DBUpgradeLogger.write2File("====ClearTableAction 开始获取E7中所有表");
/*  36 */     List<String> list2 = getE7OrE8Tables();
/*  37 */     DBUpgradeLogger.write2File("====ClearTableAction E7中所有表为：" + Arrays.toString(list2.toArray()));
/*  38 */     DBUpgradeLogger.write2File("====ClearTableAction 获取E7中所有表结束");
/*     */ 
/*     */ 
/*     */     
/*  42 */     List<String> list3 = receiveCollectionList(list1, list2);
/*  43 */     DBUpgradeLogger.write2File("====ClearTableAction E7和E9中相同表为：" + Arrays.toString(list3.toArray()));
/*  44 */     List<String> list4 = Arrays.asList(this.propUtil.getValues("e9_excludetables").split(","));
/*     */ 
/*     */     
/*  47 */     list3.removeAll(list4);
/*     */ 
/*     */     
/*  50 */     String str1 = StringUtils.join(list3.toArray(), ",");
/*  51 */     this.propUtil.put("cleartables", str1);
/*     */     
/*  53 */     HashSet<String> hashSet = new HashSet<>(list3);
/*     */     
/*  55 */     List<String> list5 = Arrays.asList(this.propUtil.getValues("clearduplicatedata_tables").split(","));
/*  56 */     for (String str : list5) {
/*  57 */       if (str.indexOf(".") > 0) {
/*  58 */         hashSet.add(str.split("\\.")[0]); continue;
/*     */       } 
/*  60 */       hashSet.add(str);
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/*  65 */     String str2 = StringUtils.join(hashSet.toArray(), ",");
/*  66 */     this.propUtil.put("synctables", str2);
/*     */     
/*  68 */     list2.removeAll(hashSet);
/*  69 */     String str3 = StringUtils.join(list2.toArray(), ",");
/*  70 */     this.propUtil.put("e7_droptables", str3);
/*     */     
/*  72 */     this.propUtil.store();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  80 */     DBUpgradeLogger.write2File("====ClearTableAction 开始清空E9表数据");
/*  81 */     boolean bool = cleartables();
/*  82 */     if (!bool) {
/*  83 */       jSONObject.put("status", "failure");
/*  84 */       DBUpgradeLogger.write2File("error====ClearTableAction 清空E9表数据失败");
/*  85 */       return jSONObject.toJSONString();
/*     */     } 
/*  87 */     DBUpgradeLogger.write2File("====ClearTableAction 清空E9表数据完成");
/*     */     
/*  89 */     jSONObject.put("status", "success");
/*  90 */     endAction(null);
/*  91 */     return jSONObject.toJSONString();
/*     */   }
/*     */ 
/*     */   
/*     */   public void startAction(HashMap<String, String> paramHashMap) {
/*  96 */     ActionProcess.getInstance().setActionProcess("0");
/*  97 */     ActionProcess.getInstance().setActionProcess("startAction");
/*  98 */     DBUpgradeLogger.write2File("=====================startAction:ClearTableAction=====================");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void endAction(HashMap<String, String> paramHashMap) {
/* 105 */     ActionProcess.getInstance().setActionProcess("100");
/* 106 */     ActionProcess.getInstance().setActionProcess("endAction");
/* 107 */     DBUpgradeLogger.write2File("=====================endAction:ClearTableAction=====================");
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void setActionProcess(String paramString) {
/* 113 */     ActionProcess.getInstance().setActionProcess(paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void setActionProcessName(String paramString) {
/* 119 */     ActionProcess.getInstance().setActionProcessName(paramString);
/*     */   }
/*     */   
/*     */   public List<String> getE9Tables() {
/* 123 */     ArrayList<String> arrayList = new ArrayList();
/* 124 */     UpgradeRecordSet upgradeRecordSet = new UpgradeRecordSet();
/* 125 */     String str = "";
/* 126 */     if ("oracle".equalsIgnoreCase(upgradeRecordSet.getDBType())) {
/* 127 */       str = "SELECT TABLE_NAME FROM USER_TABLES";
/* 128 */     } else if ("sqlserver".equalsIgnoreCase(upgradeRecordSet.getDBType())) {
/* 129 */       str = "SELECT NAME AS TABLE_NAME FROM SYSOBJECTS WHERE XTYPE='U' ";
/*     */     } 
/* 131 */     upgradeRecordSet.executeQuery(str, new Object[0]);
/* 132 */     while (upgradeRecordSet.next()) {
/* 133 */       arrayList.add(upgradeRecordSet.getString("TABLE_NAME").toUpperCase());
/*     */     }
/* 135 */     return arrayList;
/*     */   }
/*     */ 
/*     */   
/*     */   public List<String> getE7OrE8Tables() {
/* 140 */     ArrayList<String> arrayList = new ArrayList();
/* 141 */     Statement statement = null;
/* 142 */     Connection connection = null;
/* 143 */     ResultSet resultSet = null;
/* 144 */     String str1 = this.dbutil.getDBtype();
/* 145 */     String str2 = "";
/* 146 */     if (str1.equalsIgnoreCase("oracle")) {
/* 147 */       str2 = "SELECT TABLE_NAME FROM USER_TABLES";
/* 148 */     } else if (str1.equalsIgnoreCase("sqlserver")) {
/* 149 */       str2 = "SELECT NAME AS TABLE_NAME FROM SYSOBJECTS WHERE XTYPE='U' ";
/*     */     } 
/*     */     try {
/* 152 */       connection = this.dbutil.getSourceConnection();
/* 153 */       statement = connection.createStatement();
/* 154 */       resultSet = statement.executeQuery(str2);
/* 155 */       while (resultSet.next()) {
/* 156 */         arrayList.add(resultSet.getString("TABLE_NAME").toUpperCase());
/*     */       }
/* 158 */     } catch (SQLException sQLException) {
/* 159 */       sQLException.printStackTrace();
/* 160 */       DBUpgradeLogger.write2File("error===ClearTableAction 清空数据时出现异常:异常信息:" + sQLException.toString());
/*     */     } finally {
/*     */       try {
/* 163 */         if (resultSet != null) {
/* 164 */           resultSet.close();
/*     */         }
/* 166 */         if (statement != null) {
/* 167 */           statement.close();
/*     */         }
/* 169 */         if (connection != null) {
/* 170 */           connection.close();
/*     */         }
/* 172 */       } catch (Exception exception) {
/* 173 */         DBUpgradeLogger.write2File("error===ClearTableAction 关闭资源异常:异常信息:" + exception.toString());
/* 174 */         exception.printStackTrace();
/*     */       } 
/*     */     } 
/* 177 */     return arrayList;
/*     */   }
/*     */ 
/*     */   
/*     */   public static List<String> receiveCollectionList(List<String> paramList1, List<String> paramList2) {
/* 182 */     ArrayList<String> arrayList = new ArrayList();
/* 183 */     LinkedList<String> linkedList = new LinkedList<>(paramList1);
/* 184 */     HashSet<String> hashSet = new HashSet<>(paramList2);
/* 185 */     Iterator<String> iterator = linkedList.iterator();
/* 186 */     while (iterator.hasNext()) {
/* 187 */       if (!hashSet.contains(iterator.next())) {
/* 188 */         iterator.remove();
/*     */       }
/*     */     } 
/* 191 */     arrayList = new ArrayList<>(linkedList);
/* 192 */     return arrayList;
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean cleartables() {
/* 197 */     List<String> list = Arrays.asList(this.propUtil.getValues("cleartables").split(","));
/* 198 */     boolean bool = true;
/* 199 */     byte b = 1;
/*     */     
/*     */     try {
/* 202 */       UpgradeRecordSet upgradeRecordSet = new UpgradeRecordSet();
/* 203 */       String str = "delete from ";
/* 204 */       for (String str1 : list) {
/* 205 */         str1 = str1.toUpperCase().trim();
/*     */         
/* 207 */         if ("Oracle".equalsIgnoreCase(upgradeRecordSet.getDBType())) {
/* 208 */           upgradeRecordSet.executeQuery("select table_name From user_tables where table_name='" + str1 + "'", new Object[0]);
/* 209 */         } else if ("SqlServer".equalsIgnoreCase(upgradeRecordSet.getDBType())) {
/* 210 */           upgradeRecordSet.executeQuery("select name from sys.tables where name='" + str1 + "'", new Object[0]);
/*     */         } 
/* 212 */         if (!upgradeRecordSet.next()) {
/* 213 */           DBUpgradeLogger.write2File("表" + str1 + "在e9环境中不存在,无需清空数据.");
/*     */           
/*     */           continue;
/*     */         } 
/* 217 */         upgradeRecordSet.executeUpdate(str + str1, new Object[0]);
/* 218 */         DBUpgradeLogger.write2File("表" + str1 + "清除数据成功");
/* 219 */         double d = MathUtil.div((b * 100), list.size(), 1);
/* 220 */         setActionProcess(d + "");
/* 221 */         b++;
/*     */       } 
/* 223 */       DBUpgradeLogger.write2File("======================================清空E9表数据完成=============================================");
/* 224 */     } catch (Exception exception) {
/* 225 */       bool = false;
/* 226 */       exception.printStackTrace();
/* 227 */       DBUpgradeLogger.write2File("error===ClearTableAction 清空数据时出现异常:异常信息:" + exception.toString());
/*     */     } 
/* 229 */     setActionProcess("100");
/* 230 */     return bool;
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean test(List<String> paramList) {
/* 235 */     boolean bool = false;
/* 236 */     DBUpgradeLogger.write2File("====ClearTableAction 开始清空E9表数据");
/* 237 */     for (String str : paramList) {
/* 238 */       DBUpgradeLogger.write2File("====ClearTableAction 清空表:" + str + "数据成功_测试!");
/* 239 */       bool = true;
/*     */     } 
/* 241 */     DBUpgradeLogger.write2File("====ClearTableAction 清空E9表数据完成");
/* 242 */     return bool;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/upgradetool/dbupgrade/actions/upgrade/ClearTableAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */