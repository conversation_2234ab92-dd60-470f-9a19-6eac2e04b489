/*    */ package weaver.upgradetool.dbupgrade.actions;
/*    */ 
/*    */ public class ActionProcess
/*    */ {
/*  5 */   private static ActionProcess actionProcessObj = null;
/*    */ 
/*    */   
/*    */   private String actionProcess;
/*    */   
/*    */   private String actionProcessName;
/*    */ 
/*    */   
/*    */   public static ActionProcess getInstance() {
/* 14 */     if (actionProcessObj == null) {
/* 15 */       actionProcessObj = new ActionProcess();
/*    */     }
/* 17 */     return actionProcessObj;
/*    */   }
/*    */   
/*    */   public void initProcess() {
/* 21 */     setActionProcess("0");
/* 22 */     setActionProcessName("");
/*    */   }
/*    */   
/*    */   public String getActionProcess() {
/* 26 */     return this.actionProcess;
/*    */   }
/*    */   public void setActionProcess(String paramString) {
/* 29 */     this.actionProcess = paramString;
/*    */   }
/*    */   public String getActionProcessName() {
/* 32 */     return this.actionProcessName;
/*    */   }
/*    */   public void setActionProcessName(String paramString) {
/* 35 */     this.actionProcessName = paramString;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/upgradetool/dbupgrade/actions/ActionProcess.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */