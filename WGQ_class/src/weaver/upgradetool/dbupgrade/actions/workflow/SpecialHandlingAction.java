/*     */ package weaver.upgradetool.dbupgrade.actions.workflow;
/*     */ 
/*     */ import com.alibaba.fastjson.JSONObject;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import weaver.general.Util;
/*     */ import weaver.upgradetool.dbupgrade.actions.ActionInterface;
/*     */ import weaver.upgradetool.dbupgrade.actions.ActionProcess;
/*     */ import weaver.upgradetool.dbupgrade.logger.DBUpgradeLogger;
/*     */ import weaver.upgradetool.dbupgrade.upgrade.UpgradeRecordSet;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SpecialHandlingAction
/*     */   implements ActionInterface
/*     */ {
/*     */   public String execute(HashMap<String, String> paramHashMap) {
/*  22 */     startAction(null);
/*  23 */     JSONObject jSONObject = new JSONObject();
/*     */     
/*  25 */     boolean bool = handingTable1();
/*  26 */     if (bool) {
/*  27 */       jSONObject.put("status", "success");
/*     */     } else {
/*  29 */       jSONObject.put("status", "failure");
/*     */     } 
/*  31 */     endAction(null);
/*  32 */     return jSONObject.toJSONString();
/*     */   }
/*     */ 
/*     */   
/*     */   public void startAction(HashMap<String, String> paramHashMap) {
/*  37 */     ActionProcess.getInstance().setActionProcess("0");
/*  38 */     ActionProcess.getInstance().setActionProcess("startAction");
/*  39 */     DBUpgradeLogger.write2File("=====================startAction:SpecialHandlingAction=====================");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void endAction(HashMap<String, String> paramHashMap) {
/*  46 */     ActionProcess.getInstance().setActionProcess("100");
/*  47 */     ActionProcess.getInstance().setActionProcess("endAction");
/*  48 */     DBUpgradeLogger.write2File("=====================endAction:SpecialHandlingAction=====================");
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void setActionProcess(String paramString) {
/*  54 */     ActionProcess.getInstance().setActionProcess(paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void setActionProcessName(String paramString) {
/*  60 */     ActionProcess.getInstance().setActionProcessName(paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean handingTable1() {
/*  66 */     DBUpgradeLogger.write2File("开始单独处理表workflow_datainput_entry数据");
/*  67 */     boolean bool = true;
/*  68 */     UpgradeRecordSet upgradeRecordSet1 = new UpgradeRecordSet();
/*  69 */     UpgradeRecordSet upgradeRecordSet2 = new UpgradeRecordSet();
/*  70 */     UpgradeRecordSet upgradeRecordSet3 = new UpgradeRecordSet();
/*  71 */     String str = "";
/*  72 */     if (upgradeRecordSet1.getDBType().equalsIgnoreCase("oracle")) {
/*  73 */       str = "SELECT COLUMN_NAME FROM USER_TAB_COLUMNS WHERE TABLE_NAME='WORKFLOW_DATAINPUT_ENTRY'";
/*  74 */     } else if (upgradeRecordSet1.getDBType().equalsIgnoreCase("sqlserver")) {
/*  75 */       str = "SELECT SC.NAME AS COLUMN_NAME FROM SYSCOLUMNS SC,SYSTYPES ST WHERE SC.XTYPE=ST.XTYPE AND SC.ID IN(SELECT ID FROM SYSOBJECTS WHERE XTYPE='U' AND NAME='WORKFLOW_DATAINPUT_ENTRY')";
/*     */     } 
/*     */     try {
/*  78 */       upgradeRecordSet1.executeQuery(str, new Object[0]);
/*  79 */       ArrayList<String> arrayList = new ArrayList();
/*  80 */       while (upgradeRecordSet1.next()) {
/*  81 */         arrayList.add(upgradeRecordSet1.getString("COLUMN_NAME").toUpperCase());
/*     */       }
/*  83 */       if (arrayList.contains("DETAILINDEX")) {
/*  84 */         upgradeRecordSet3.executeSql("UPDATE WORKFLOW_DATAINPUT_ENTRY SET DETAILINDEX='0' WHERE  DETAILINDEX IS NULL");
/*     */       }
/*  86 */       if (arrayList.contains("ENABLE")) {
/*  87 */         upgradeRecordSet3.executeSql("UPDATE WORKFLOW_DATAINPUT_ENTRY SET ENABLE='0' WHERE  ENABLE IS NULL ");
/*     */       }
/*  89 */       if (arrayList.contains("ISDEL")) {
/*  90 */         upgradeRecordSet3.executeSql("UPDATE WORKFLOW_DATAINPUT_ENTRY SET ISDEL='0' WHERE  ISDEL IS NULL");
/*     */       }
/*  92 */       upgradeRecordSet1.executeQuery("SELECT ID,TRIGGERFIELDNAME FROM WORKFLOW_DATAINPUT_ENTRY", new Object[0]);
/*  93 */       while (upgradeRecordSet1.next()) {
/*  94 */         String str1 = upgradeRecordSet1.getString("ID");
/*  95 */         if (arrayList.contains("TRIGGERNAME")) {
/*  96 */           String str2 = Util.null2String(upgradeRecordSet1.getString("TRIGGERFIELDNAME")).toUpperCase().trim();
/*  97 */           if (str2.contains("FIELD")) {
/*  98 */             str2 = str2.substring("FIELD".length());
/*     */           }
/* 100 */           upgradeRecordSet2.executeQuery(" SELECT A.INDEXDESC FROM HTMLLABELINDEX A,WORKFLOW_BILLFIELD B WHERE A.ID=B.FIELDLABEL AND B.ID=" + str2 + " AND A.INDEXDESC IS NOT  NULL", new Object[0]);
/* 101 */           if (upgradeRecordSet2.next()) {
/* 102 */             upgradeRecordSet3.executeSql("UPDATE WORKFLOW_DATAINPUT_ENTRY SET TRIGGERNAME='触发字段" + upgradeRecordSet2.getString("INDEXDESC") + "' WHERE TRIGGERNAME IS NULL AND ID=" + str1);
/*     */           }
/*     */         } 
/*     */       } 
/* 106 */     } catch (Exception exception) {
/* 107 */       bool = false;
/* 108 */       DBUpgradeLogger.write2File("单独处理表workflow_datainput_entry出现异常:" + exception.toString());
/* 109 */       exception.printStackTrace();
/*     */     } 
/*     */     
/* 112 */     return bool;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/upgradetool/dbupgrade/actions/workflow/SpecialHandlingAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */