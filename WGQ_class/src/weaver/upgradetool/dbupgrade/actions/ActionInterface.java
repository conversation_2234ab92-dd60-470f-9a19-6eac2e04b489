package weaver.upgradetool.dbupgrade.actions;

import java.util.HashMap;

public interface ActionInterface {
  String execute(HashMap<String, String> paramHashMap);
  
  void startAction(HashMap<String, String> paramHashMap);
  
  void endAction(HashMap<String, String> paramHashMap);
  
  void setActionProcess(String paramString);
  
  void setActionProcessName(String paramString);
}


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/upgradetool/dbupgrade/actions/ActionInterface.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */