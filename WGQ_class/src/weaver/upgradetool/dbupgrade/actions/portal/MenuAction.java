/*    */ package weaver.upgradetool.dbupgrade.actions.portal;
/*    */ 
/*    */ import com.alibaba.fastjson.JSONObject;
/*    */ import java.util.HashMap;
/*    */ import weaver.conn.RecordSet;
/*    */ import weaver.general.MathUtil;
/*    */ import weaver.upgradetool.dbupgrade.actions.ActionInterface;
/*    */ import weaver.upgradetool.dbupgrade.actions.ActionProcess;
/*    */ import weaver.upgradetool.dbupgrade.logger.DBUpgradeLogger;
/*    */ import weaver.upgradetool.dbupgrade.upgrade.UpgradeRecordSet;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class MenuAction
/*    */   implements ActionInterface
/*    */ {
/*    */   public String execute(HashMap<String, String> paramHashMap) {
/* 19 */     startAction(null);
/* 20 */     JSONObject jSONObject = new JSONObject();
/* 21 */     initMenu();
/* 22 */     jSONObject.put("status", "success");
/* 23 */     endAction(null);
/* 24 */     return jSONObject.toJSONString();
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   public void initMenu() {
/* 31 */     RecordSet recordSet = new RecordSet();
/* 32 */     UpgradeRecordSet upgradeRecordSet = new UpgradeRecordSet();
/* 33 */     int i = 0;
/* 34 */     recordSet.execute("select count(distinct id) as count from hrmsubcompany  where id!=1");
/* 35 */     if (recordSet.next()) {
/* 36 */       i = recordSet.getInt("count");
/*    */     }
/*    */     
/* 39 */     recordSet.execute("select distinct id from hrmsubcompany  where id!=1");
/* 40 */     byte b = 0;
/* 41 */     while (recordSet.next()) {
/* 42 */       setActionProcess("" + MathUtil.div(b, i, 2));
/* 43 */       int j = recordSet.getInt("id");
/* 44 */       DBUpgradeLogger.write2File("$$$开始：同步分部:" + j + "门户菜单......");
/* 45 */       upgradeRecordSet.executeUpdate("delete from mainmenuconfig where resourceid=?", new Object[] { Integer.valueOf(j) });
/* 46 */       upgradeRecordSet.executeUpdate("delete from leftmenuconfig where resourceid=?", new Object[] { Integer.valueOf(j) });
/*    */       
/* 48 */       String str = "insert into mainmenuconfig (userid,infoid,visible,viewindex,resourceid,resourcetype,locked,lockedbyid,usecustomname,customname,customname_e)select  distinct  userid,infoid,visible,viewindex,?,2,locked,lockedbyid,usecustomname,customname,customname_e from mainmenuconfig where resourcetype=1  and resourceid=1";
/* 49 */       upgradeRecordSet.executeUpdate(str, new Object[] { Integer.valueOf(j) });
/* 50 */       str = "insert into leftmenuconfig (userid,infoid,visible,viewindex,resourceid,resourcetype,locked,lockedbyid,usecustomname,customname,customname_e)select  distinct  userid,infoid,visible,viewindex,?,2,locked,lockedbyid,usecustomname,customname,customname_e from leftmenuconfig where resourcetype=1  and resourceid=1";
/* 51 */       upgradeRecordSet.executeUpdate(str, new Object[] { Integer.valueOf(j) });
/*    */       
/* 53 */       DBUpgradeLogger.write2File("$$$结束：同步分部:" + j + "门户菜单......");
/* 54 */       b++;
/*    */     } 
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public void startAction(HashMap<String, String> paramHashMap) {
/* 61 */     DBUpgradeLogger.write2File("startAction  MenuAction......");
/* 62 */     DBUpgradeLogger.write2File("同步分部菜单开始......");
/* 63 */     setActionProcessName("同步分部菜单");
/*    */   }
/*    */ 
/*    */   
/*    */   public void endAction(HashMap<String, String> paramHashMap) {
/* 68 */     DBUpgradeLogger.write2File("endAction  MenuAction......");
/* 69 */     DBUpgradeLogger.write2File("同步分部菜单结束......");
/*    */   }
/*    */ 
/*    */   
/*    */   public void setActionProcess(String paramString) {
/* 74 */     ActionProcess.getInstance().setActionProcess(paramString);
/*    */   }
/*    */ 
/*    */   
/*    */   public void setActionProcessName(String paramString) {
/* 79 */     ActionProcess.getInstance().setActionProcessName(paramString);
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/upgradetool/dbupgrade/actions/portal/MenuAction.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */