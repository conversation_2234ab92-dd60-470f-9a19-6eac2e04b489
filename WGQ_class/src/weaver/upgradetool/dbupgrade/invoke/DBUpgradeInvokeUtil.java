/*    */ package weaver.upgradetool.dbupgrade.invoke;
/*    */ 
/*    */ import java.lang.reflect.Method;
/*    */ import java.util.HashMap;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ public class DBUpgradeInvokeUtil
/*    */ {
/*    */   public static Object invoke(String paramString1, String paramString2, HashMap<String, String> paramHashMap) {
/* 12 */     return invokeMenthod(paramString1, paramString2, paramHashMap);
/*    */   }
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */   
/*    */   private static Object invokeMenthod(String paramString1, String paramString2, HashMap<String, String> paramHashMap) {
/* 21 */     Object object = null;
/* 22 */     Class<?> clazz = null;
/*    */     try {
/* 24 */       if (paramString2 == null || "".equals(paramString2)) {
/* 25 */         paramString2 = "execute";
/*    */       }
/* 27 */       clazz = Class.forName(paramString1);
/*    */ 
/*    */       
/* 30 */       if (paramHashMap == null) {
/* 31 */         Method method = clazz.getMethod(paramString2, new Class[0]);
/* 32 */         object = method.invoke(clazz.newInstance(), new Object[0]);
/*    */       } else {
/* 34 */         Method method = clazz.getMethod(paramString2, new Class[] { HashMap.class });
/* 35 */         object = method.invoke(clazz.newInstance(), new Object[] { paramHashMap });
/*    */       } 
/* 37 */     } catch (IllegalArgumentException illegalArgumentException) {
/* 38 */       illegalArgumentException.printStackTrace();
/* 39 */       object = Boolean.valueOf(false);
/* 40 */     } catch (Exception exception) {
/* 41 */       exception.printStackTrace();
/* 42 */       object = Boolean.valueOf(false);
/*    */     } 
/* 44 */     return object;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/upgradetool/dbupgrade/invoke/DBUpgradeInvokeUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */