/*     */ package weaver.upgradetool.installupgrade;
/*     */ import java.io.BufferedReader;
/*     */ import java.io.File;
/*     */ import java.io.FileReader;
/*     */ import java.sql.Connection;
/*     */ import java.sql.DriverManager;
/*     */ import java.sql.ResultSet;
/*     */ import java.sql.SQLException;
/*     */ import java.sql.Statement;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.HashSet;
/*     */ import java.util.Iterator;
/*     */ import java.util.LinkedList;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.Properties;
/*     */ import java.util.regex.Matcher;
/*     */ import java.util.regex.Pattern;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import net.sf.json.JSONArray;
/*     */ import net.sf.json.JSONObject;
/*     */ import weaver.conn.ConnectionPool;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.MathUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.system.EditProperties;
/*     */ import weaver.upgradetool.dbupgrade.logger.DBUpgradeLogger;
/*     */ import weaver.upgradetool.dbupgrade.logger.UpgradeLog;
/*     */ import weaver.upgradetool.dbupgrade.upgrade.PropUtil;
/*     */ import weaver.upgradetool.dbupgrade.upgrade.UpgradeRecordSet;
/*     */ 
/*     */ public class UpgradeDBCmd {
/*  35 */   public static volatile int process = 0;
/*  36 */   public final String dbDir = GCONST.getRootPath() + "WEB-INF" + File.separatorChar + "sysupgrade" + File.separatorChar;
/*     */   public String execute(HttpServletRequest paramHttpServletRequest, Map<String, Object> paramMap) {
/*  38 */     JSONObject jSONObject = new JSONObject();
/*  39 */     if (process == 0) {
/*  40 */       DBUpgradeLogger.write2File("===开始还原数据库===");
/*  41 */       process = 1;
/*     */       
/*  43 */       String str1 = Util.null2String((String)paramMap.get("useStandardDB"));
/*  44 */       if ("1".equals(str1)) {
/*  45 */         String str = checkDBFile();
/*  46 */         if ("".equals(str)) {
/*  47 */           jSONObject.put("exec_status", "fail");
/*  48 */           jSONObject.put("msg", "缺少标准数据库快照信息，请将数据库快照文件拷贝到/WEB-INF/sysupgrade目录下!");
/*  49 */           process = 0;
/*  50 */           return jSONObject.toString();
/*     */         } 
/*     */       } 
/*     */ 
/*     */       
/*  55 */       String str2 = editWeaverProp(paramHttpServletRequest, paramMap);
/*  56 */       if (!"success".equals(str2)) {
/*  57 */         jSONObject.put("exec_status", "fail");
/*  58 */         jSONObject.put("msg", str2);
/*  59 */         process = 0;
/*  60 */         return jSONObject.toString();
/*     */       } 
/*     */       
/*  63 */       process = 2;
/*  64 */       DBUpgradeLogger.write2File("====SyncTableStructure 开始还原表结构");
/*     */       
/*  66 */       boolean bool = syncTableStructure();
/*  67 */       if (!bool) {
/*  68 */         jSONObject.put("exec_status", "fail");
/*  69 */         jSONObject.put("msg", "还原表结构失败");
/*  70 */         process = 0;
/*  71 */         return jSONObject.toString();
/*     */       } 
/*  73 */       DBUpgradeLogger.write2File("====SyncTableStructure 还原表结构结束");
/*  74 */       process = 3;
/*  75 */       DBUpgradeLogger.write2File("====SyncTableStructure 开始还原存储过程、触发器等");
/*     */       
/*  77 */       bool = syncOther();
/*  78 */       if (!bool) {
/*  79 */         jSONObject.put("exec_status", "fail");
/*  80 */         jSONObject.put("msg", "还原存储过程、触发器失败");
/*  81 */         process = 0;
/*  82 */         return jSONObject.toString();
/*     */       } 
/*  84 */       DBUpgradeLogger.write2File("====SyncTableStructure 还原存储过程、触发器结束");
/*  85 */       process = 4;
/*     */       
/*  87 */       DBUpgradeLogger.write2File("====SyncTableStructure 还原存储过程、触发器结束");
/*  88 */       process = 5;
/*  89 */       File file = new File(GCONST.getRootPath() + "WEB-INF" + File.separatorChar + "sysupgrade" + File.separatorChar + "installUpgrade.properties");
/*  90 */       if (!file.exists()) {
/*     */         try {
/*  92 */           file.createNewFile();
/*  93 */         } catch (IOException iOException) {
/*  94 */           iOException.printStackTrace();
/*     */         } 
/*     */       }
/*     */       
/*  98 */       DBUpgradeLogger.write2File("===还原数据库结束===");
/*     */     } 
/*     */     
/* 101 */     jSONObject.put("exec_status", "success");
/* 102 */     return jSONObject.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String checkDBFile() {
/* 110 */     File file = new File(this.dbDir);
/* 111 */     String str = "";
/* 112 */     if (file.exists()) {
/* 113 */       File[] arrayOfFile = file.listFiles();
/* 114 */       for (int i = arrayOfFile.length - 1; arrayOfFile != null && i >= 0; i--) {
/* 115 */         if (arrayOfFile[i].getName().endsWith(".dbrecover")) {
/* 116 */           str = arrayOfFile[i].getName();
/*     */           break;
/*     */         } 
/*     */       } 
/*     */     } 
/* 121 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public String editWeaverProp(HttpServletRequest paramHttpServletRequest, Map<String, Object> paramMap) {
/* 127 */     String str1 = "success";
/* 128 */     DBUpgradeLogger.write2File("开始修改weaver.properties");
/* 129 */     EditProperties editProperties = new EditProperties();
/* 130 */     int i = Util.getIntValue((String)paramMap.get("languageId"));
/* 131 */     String str2 = Util.null2String((String)paramMap.get("validCode"));
/* 132 */     String str3 = Util.null2String((String)paramMap.get("dbType"));
/* 133 */     String str4 = Util.null2String((String)paramMap.get("dbIp"));
/* 134 */     String str5 = Util.null2String((String)paramMap.get("dbPort"));
/* 135 */     String str6 = Util.null2String((String)paramMap.get("dbName"));
/* 136 */     String str7 = Util.null2String((String)paramMap.get("dbInstance"));
/* 137 */     String str8 = Util.null2String((String)paramMap.get("username"));
/* 138 */     String str9 = Util.null2String((String)paramMap.get("password"));
/* 139 */     if (str3.equals("")) {
/* 140 */       str3 = "sqlserver";
/*     */     }
/* 142 */     String str10 = "";
/* 143 */     String str11 = "";
/* 144 */     Connection connection = null;
/* 145 */     String str12 = "";
/* 146 */     String str13 = "";
/* 147 */     String str14 = "";
/* 148 */     String str15 = "";
/* 149 */     String str16 = "";
/* 150 */     boolean bool = false;
/* 151 */     String str17 = "";
/* 152 */     String str18 = "";
/* 153 */     String str19 = "";
/* 154 */     if (str3.equalsIgnoreCase("sqlserver") && str7.equals("MSSQLSERVER")) {
/* 155 */       str10 = "jdbc:sqlserver://" + str4 + ":" + str5 + ";DatabaseName=" + str6 + ";SelectMethod=cursor";
/* 156 */       str11 = "com.microsoft.sqlserver.jdbc.SQLServerDriver";
/* 157 */       str13 = paramHttpServletRequest.getRealPath("/data/init/SQLServer");
/* 158 */       str14 = paramHttpServletRequest.getRealPath("/data/SQLServer");
/* 159 */       str17 = paramHttpServletRequest.getRealPath("/data/init/SQLServer/config");
/* 160 */       str15 = "go";
/* 161 */       str18 = "sqlserver";
/* 162 */       str19 = "1";
/* 163 */     } else if (str3.equalsIgnoreCase("sqlserver") && !str7.equals("MSSQLSERVER")) {
/* 164 */       str10 = "jdbc:sqlserver://" + str4 + ":" + str5 + ";instanceName=" + str7 + ";DatabaseName=" + str6 + ";SelectMethod=cursor";
/* 165 */       str11 = "com.microsoft.sqlserver.jdbc.SQLServerDriver";
/* 166 */       str13 = paramHttpServletRequest.getRealPath("/data/init/SQLServer");
/* 167 */       str14 = paramHttpServletRequest.getRealPath("/data/SQLServer");
/* 168 */       str17 = paramHttpServletRequest.getRealPath("/data/init/SQLServer/config");
/* 169 */       str15 = "go";
/* 170 */       str18 = "sqlserver";
/* 171 */       str19 = "1";
/* 172 */     } else if (str3.equalsIgnoreCase("oracle")) {
/* 173 */       str10 = "jdbc:oracle:thin:@" + str4 + ":" + str5 + ":" + str6;
/* 174 */       str11 = "oracle.jdbc.OracleDriver";
/* 175 */       str13 = paramHttpServletRequest.getRealPath("/data/init/Oracle");
/* 176 */       str14 = paramHttpServletRequest.getRealPath("/data/Oracle");
/* 177 */       str17 = paramHttpServletRequest.getRealPath("/data/init/Oracle/config");
/* 178 */       str15 = "/";
/* 179 */       str18 = "oracle";
/* 180 */       str19 = "2";
/* 181 */     } else if (str3.equalsIgnoreCase("db2")) {
/* 182 */       str10 = "jdbc:db2:" + str4 + ":" + str6;
/* 183 */       str11 = "COM.ibm.db2.jdbc.net.DB2Driver";
/* 184 */       str13 = paramHttpServletRequest.getRealPath("/data/init/DB2");
/* 185 */       str14 = paramHttpServletRequest.getRealPath("/data/DB2");
/* 186 */       str17 = paramHttpServletRequest.getRealPath("/data/init/DB2/config");
/* 187 */       str15 = ";";
/* 188 */       str18 = "db2";
/* 189 */       str19 = "3";
/* 190 */     } else if (str3.equalsIgnoreCase("mysql")) {
/* 191 */       str10 = "jdbc:mysql://" + str4 + ":" + str5 + "/" + str6 + "?characterEncoding=utf8&useSSL=false&autoReconnect=true&failOverReadOnly=false&serverTimezone=Asia/Shanghai";
/* 192 */       str11 = "com.mysql.cj.jdbc.Driver";
/* 193 */       str13 = paramHttpServletRequest.getRealPath("/data/init/Mysql");
/* 194 */       str14 = paramHttpServletRequest.getRealPath("/data/Mysql");
/* 195 */       str17 = paramHttpServletRequest.getRealPath("/data/init/Mysql/config");
/* 196 */       str18 = "mysql";
/* 197 */       str19 = "4";
/* 198 */     } else if (str3.equalsIgnoreCase("nt")) {
/* 199 */       str10 = "jdbc:gbase://" + str4 + ":" + str5 + "/" + str6 + "?characterEncoding=utf8&useSSL=false&autoReconnect=true&failOverReadOnly=false&serverTimezone=Asia/Shanghai";
/* 200 */       str11 = "com.gbase.jdbc.Driver";
/* 201 */       str13 = paramHttpServletRequest.getRealPath("/data/init/Mysql");
/* 202 */       str14 = paramHttpServletRequest.getRealPath("/data/Mysql");
/* 203 */       str17 = paramHttpServletRequest.getRealPath("/data/init/Mysql/config");
/* 204 */       str18 = "nt";
/* 205 */       str19 = "7";
/* 206 */     } else if (str3.equalsIgnoreCase("dm")) {
/* 207 */       str6 = "";
/* 208 */       str10 = "jdbc:dm://" + str4 + ":" + str5;
/* 209 */       str11 = "dm.jdbc.driver.DmDriver";
/* 210 */       str13 = paramHttpServletRequest.getRealPath("/data/init/DM");
/* 211 */       str14 = paramHttpServletRequest.getRealPath("/data/DM");
/* 212 */       str17 = paramHttpServletRequest.getRealPath("/data/init/DM/config");
/* 213 */       str15 = "/";
/* 214 */       str18 = "dm";
/* 215 */       str19 = "5";
/* 216 */     } else if (str3.equalsIgnoreCase("st")) {
/* 217 */       str10 = "jdbc:oscar://" + str4 + ":" + str5 + "/" + str6;
/* 218 */       str11 = "com.oscar.Driver";
/* 219 */       str13 = paramHttpServletRequest.getRealPath("/data/init/ST");
/* 220 */       str14 = paramHttpServletRequest.getRealPath("/data/ST");
/* 221 */       str17 = paramHttpServletRequest.getRealPath("/data/init/ST/config");
/* 222 */       str15 = "/";
/* 223 */       str18 = "st";
/* 224 */       str19 = "6";
/* 225 */     } else if (str3.equalsIgnoreCase("jc")) {
/* 226 */       str10 = "jdbc:kingbase8://" + str4 + ":" + str5 + "/" + str6 + "?prepareThreshold=0";
/* 227 */       str11 = "com.kingbase8.Driver";
/* 228 */       str13 = paramHttpServletRequest.getRealPath("/data/init/JC");
/* 229 */       str14 = paramHttpServletRequest.getRealPath("/data/JC");
/* 230 */       str17 = paramHttpServletRequest.getRealPath("/data/init/JC/config");
/* 231 */       str15 = "/";
/* 232 */       str18 = "jc";
/* 233 */       str19 = "8";
/* 234 */     } else if (str3.equalsIgnoreCase("gs")) {
/* 235 */       str10 = "jdbc:zenith:@" + str4 + ":" + str5;
/* 236 */       str11 = "com.huawei.gauss.jdbc.ZenithDriver";
/* 237 */       str13 = paramHttpServletRequest.getRealPath("/data/init/GS");
/* 238 */       str14 = paramHttpServletRequest.getRealPath("/data/GS");
/* 239 */       str17 = paramHttpServletRequest.getRealPath("/data/init/GS/config");
/* 240 */       str15 = "/";
/* 241 */       str18 = "gs";
/* 242 */       str19 = "9";
/*     */       
/* 244 */       Pattern pattern = Pattern.compile("([\\s;]+)$", 2);
/* 245 */     } else if (str3.equalsIgnoreCase("ob")) {
/* 246 */       str10 = "jdbc:oceanbase://" + str4 + ":" + str5 + "/" + str6;
/* 247 */       str11 = "com.alipay.oceanbase.jdbc.Driver";
/* 248 */       str13 = paramHttpServletRequest.getRealPath("/data/init/OB");
/* 249 */       str14 = paramHttpServletRequest.getRealPath("/data/OB");
/* 250 */       str17 = paramHttpServletRequest.getRealPath("/data/init/OB/config");
/* 251 */       str18 = "mysql";
/* 252 */       str19 = "10";
/*     */     } 
/*     */     
/* 255 */     String str20 = str3;
/* 256 */     boolean bool1 = Stream.<String>of(new String[] { "mysql", "nt", "ob" }).anyMatch(paramString2 -> paramString2.equalsIgnoreCase(paramString1));
/*     */     
/*     */     try {
/* 259 */       Class.forName(str11);
/* 260 */     } catch (ClassNotFoundException classNotFoundException) {
/* 261 */       classNotFoundException.printStackTrace();
/*     */     } 
/* 263 */     Properties properties = new Properties();
/* 264 */     properties.put("user", str8);
/* 265 */     properties.put("password", str9);
/* 266 */     if (!bool1) {
/* 267 */       properties.put("CHARSET", "ISO");
/*     */     }
/* 269 */     String str21 = null;
/*     */     try {
/* 271 */       connection = DriverManager.getConnection(str10, properties);
/* 272 */       str21 = connection.getMetaData().getDatabaseProductName();
/* 273 */     } catch (Exception exception) {
/* 274 */       String str = "";
/*     */       
/* 276 */       if (str3.equals("oracle")) {
/*     */         try {
/* 278 */           str = "jdbc:oracle:thin:@" + str4 + ":" + str5 + "/" + str6;
/* 279 */           connection = DriverManager.getConnection(str, properties);
/* 280 */           bool = true;
/* 281 */         } catch (Exception exception1) {
/* 282 */           exception.printStackTrace();
/* 283 */           System.out.println("oracle DB Connection Error! Reason is :" + exception.getMessage());
/* 284 */           str1 = "DB Connection Error!";
/* 285 */           return str1;
/*     */         } 
/*     */       } else {
/* 288 */         exception.printStackTrace();
/* 289 */         System.out.println("DB Connection Error! Reason is :" + exception.getMessage());
/* 290 */         str1 = "DB Connection Error!";
/* 291 */         return str1;
/*     */       } 
/*     */     } 
/*     */     
/* 295 */     ConnectionPool connectionPool = ConnectionPool.getInstance();
/*     */     
/* 297 */     editProperties.editProp(str4, str5, str6, str8, str9, str19, "utf8", Boolean.valueOf(bool), "weaver.properties");
/* 298 */     Prop.loadTemplateProp2("weaver");
/* 299 */     connectionPool.init();
/*     */     
/* 301 */     DBUpgradeLogger.write2File("完成修改weaver.properties");
/* 302 */     return str1;
/*     */   }
/*     */ 
/*     */   
/*     */   public void setActionProcess(String paramString) {}
/*     */ 
/*     */   
/*     */   public void setActionProcessName(String paramString) {}
/*     */ 
/*     */   
/*     */   public HashMap<String, String> getTiggerSqls(ResultSet paramResultSet) throws Exception {
/* 313 */     PropUtil propUtil1 = PropUtil.getInstance(new String[] { PropUtil.WEAVER_SOURCE });
/* 314 */     PropUtil propUtil2 = PropUtil.getInstance(new String[] { PropUtil.WEAVER });
/* 315 */     HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 316 */     while (paramResultSet.next()) {
/* 317 */       String str1 = Util.null2String(paramResultSet.getString("text").trim());
/* 318 */       String str2 = Util.null2String(paramResultSet.getString("name").trim());
/* 319 */       if ("".equals(str1) || str1.toUpperCase().contains("FORMTABLE_MAIN_")) {
/*     */         continue;
/*     */       }
/* 322 */       hashMap1.put(str2, (Util.null2String((String)hashMap1.get(str2)) + " " + str1).replaceAll("\t", " ").replaceAll("\"", ""));
/*     */     } 
/*     */ 
/*     */     
/* 326 */     Pattern pattern = Pattern.compile("([\\S]*?" + propUtil1.getValues("username").trim() + "[\\S]*)", 2);
/* 327 */     HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 328 */     for (String str1 : hashMap1.keySet()) {
/* 329 */       String str2 = (String)hashMap1.get(str1);
/* 330 */       Matcher matcher = pattern.matcher(str2);
/* 331 */       while (matcher.find()) {
/* 332 */         String str = matcher.group();
/* 333 */         str2 = matcher.replaceFirst(str.substring(str.indexOf(".") + 1).replace("\"", "").replace("'", ""));
/* 334 */         matcher = pattern.matcher(str2);
/*     */       } 
/* 336 */       hashMap2.put(str1, str2);
/*     */     } 
/* 338 */     return (HashMap)hashMap2;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, List<JSONObject>> getDBRecoverFileWhitType() {
/* 346 */     ArrayList arrayList = new ArrayList();
/* 347 */     RecordSet recordSet = new RecordSet();
/* 348 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 349 */     File file1 = new File(this.dbDir);
/* 350 */     File file2 = null;
/* 351 */     if (file1.exists()) {
/* 352 */       String str = "";
/* 353 */       recordSet.execute("select cversion from license");
/* 354 */       if (recordSet.next()) {
/* 355 */         str = Util.null2String(recordSet.getString("cversion")).toLowerCase().trim();
/*     */       }
/* 357 */       File[] arrayOfFile = file1.listFiles();
/* 358 */       for (int i = arrayOfFile.length - 1; arrayOfFile != null && i >= 0; i--) {
/* 359 */         if (arrayOfFile[i].getName().endsWith(".dbrecover") && arrayOfFile[i].getName().contains(str)) {
/* 360 */           file2 = arrayOfFile[i];
/*     */           break;
/*     */         } 
/*     */       } 
/*     */     } 
/*     */     try {
/* 366 */       FileReader fileReader = new FileReader(file2);
/* 367 */       BufferedReader bufferedReader = new BufferedReader(fileReader);
/* 368 */       String str = null;
/* 369 */       while ((str = bufferedReader.readLine()) != null) {
/*     */         
/* 371 */         str = SecurityHelper.decrypt("weaververify", str).trim();
/* 372 */         if (!"".equals(str)) {
/*     */           try {
/* 374 */             String[] arrayOfString = str.split("===>");
/* 375 */             if ("table".equals(Util.null2String(arrayOfString[0]))) {
/* 376 */               hashMap.put(arrayOfString[1], JSONArray.toList(JSONArray.fromObject(arrayOfString[2]), new JSONObject(), new JsonConfig()));
/*     */             }
/* 378 */           } catch (Exception exception) {
/* 379 */             DBUpgradeLogger.write2File("error====解析数据库标准快照文件语句:" + str + "失败!失败原因:" + exception.toString());
/* 380 */             exception.printStackTrace();
/*     */           } 
/*     */         }
/*     */       } 
/* 384 */     } catch (Exception exception) {
/* 385 */       exception.printStackTrace();
/* 386 */       return null;
/*     */     } 
/*     */     
/* 389 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List<String> getCurAllTables() {
/* 397 */     ArrayList<String> arrayList = new ArrayList();
/* 398 */     UpgradeRecordSet upgradeRecordSet = new UpgradeRecordSet();
/* 399 */     String str1 = upgradeRecordSet.getDBType().toLowerCase();
/* 400 */     String str2 = "";
/* 401 */     if (str1.equalsIgnoreCase("oracle")) {
/* 402 */       str2 = "SELECT TABLE_NAME FROM USER_TAB_COMMENTS WHERE  TABLE_TYPE ='TABLE' ";
/* 403 */     } else if (str1.equalsIgnoreCase("sqlserver")) {
/* 404 */       str2 = "SELECT NAME AS TABLE_NAME FROM SYSOBJECTS WHERE XTYPE='U' ";
/*     */     } 
/* 406 */     upgradeRecordSet.execute(str2);
/* 407 */     while (upgradeRecordSet.next()) {
/* 408 */       arrayList.add(upgradeRecordSet.getString("TABLE_NAME").toUpperCase());
/*     */     }
/* 410 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static List<String> receiveCollectionList(List<String> paramList1, List<String> paramList2) {
/* 416 */     LinkedList<String> linkedList = new LinkedList<>(paramList1);
/* 417 */     HashSet<String> hashSet = new HashSet<>(paramList2);
/* 418 */     Iterator<String> iterator = linkedList.iterator();
/* 419 */     while (iterator.hasNext()) {
/* 420 */       if (!hashSet.contains(iterator.next())) {
/* 421 */         iterator.remove();
/*     */       }
/*     */     } 
/* 424 */     return new ArrayList<>(linkedList);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean syncTableStructure() {
/* 432 */     boolean bool = true;
/* 433 */     UpgradeRecordSet upgradeRecordSet = new UpgradeRecordSet();
/* 434 */     long l = System.currentTimeMillis();
/* 435 */     String str = upgradeRecordSet.getDBType();
/* 436 */     HashSet hashSet1 = new HashSet();
/* 437 */     Map<String, List<JSONObject>> map = getDBRecoverFileWhitType();
/* 438 */     HashSet hashSet2 = new HashSet();
/* 439 */     HashSet<?> hashSet = new HashSet();
/*     */     try {
/* 441 */       byte b = 1;
/*     */       
/* 443 */       if (map != null)
/*     */       {
/* 445 */         for (String str1 : map.keySet()) {
/* 446 */           DBUpgradeLogger.write2File("操作表名:" + str1);
/* 447 */           HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 448 */           HashMap<Object, Object> hashMap2 = new HashMap<>();
/* 449 */           hashSet2.clear();
/* 450 */           hashSet.clear();
/* 451 */           List list = map.get(str1);
/* 452 */           str1 = str1.toUpperCase();
/*     */           
/* 454 */           String str2 = "";
/* 455 */           if ("Oracle".equalsIgnoreCase(str)) {
/* 456 */             str2 = "SELECT COLUMN_NAME,DATA_TYPE,DATA_LENGTH,DATA_PRECISION,DATA_SCALE,NULLABLE,DATA_DEFAULT FROM USER_TAB_COLUMNS WHERE TABLE_NAME='" + str1 + "'";
/*     */           }
/* 458 */           else if ("SqlServer".equalsIgnoreCase(str)) {
/* 459 */             str2 = "SELECT SC.NAME AS COLUMN_NAME,ST.NAME AS DATA_TYPE,SC.LENGTH AS DATA_LENGTH,SC.XPREC AS DATA_PRECISION,SC.XSCALE AS DATA_SCALE,SC.ISNULLABLE AS NULLABLE,CM.TEXT AS DATA_DEFAULT FROM SYSCOLUMNS SC LEFT JOIN SYSTYPES ST ON SC.XUSERTYPE=ST.XUSERTYPE LEFT JOIN SYSCOMMENTS CM ON SC.CDEFAULT=CM.ID WHERE SC.ID=Object_Id('" + str1 + "')";
/*     */           } 
/*     */ 
/*     */           
/* 463 */           upgradeRecordSet.execute(str2);
/*     */           
/* 465 */           boolean bool1 = false;
/* 466 */           while (upgradeRecordSet.next()) {
/* 467 */             bool1 = true;
/* 468 */             String str5 = upgradeRecordSet.getString("COLUMN_NAME").toUpperCase().trim();
/* 469 */             Map<String, String> map1 = getColumnMap(upgradeRecordSet.getString("DATA_TYPE"), upgradeRecordSet.getString("DATA_LENGTH"), upgradeRecordSet.getString("DATA_PRECISION"), upgradeRecordSet.getString("DATA_SCALE"), upgradeRecordSet.getString("DATA_DEFAULT"), upgradeRecordSet.getString("NULLABLE"));
/* 470 */             hashMap2.put(str5.toUpperCase().trim(), map1);
/*     */           } 
/* 472 */           if (!bool1) {
/* 473 */             b++;
/* 474 */             DBUpgradeLogger.write2File("====该表" + str1 + "在标准库中不存在,需要手动创建");
/*     */             
/*     */             continue;
/*     */           } 
/* 478 */           for (JSONObject jSONObject : list) {
/* 479 */             String str5 = jSONObject.getString("columnname").toUpperCase().trim();
/* 480 */             Map<String, String> map1 = getColumnMap(jSONObject.containsKey("datatype") ? jSONObject.getString("datatype") : null, 
/* 481 */                 jSONObject.containsKey("length") ? jSONObject.getString("length") : null, 
/* 482 */                 jSONObject.containsKey("precision") ? jSONObject.getString("precision") : null, 
/* 483 */                 jSONObject.containsKey("scale") ? jSONObject.getString("scale") : null, 
/* 484 */                 jSONObject.containsKey("datadefault") ? jSONObject.getString("datadefault") : null, 
/* 485 */                 jSONObject.containsKey("nullable") ? jSONObject.getString("nullable") : null);
/* 486 */             hashMap1.put(str5.toUpperCase().trim(), map1);
/*     */           } 
/* 488 */           hashSet2 = new HashSet(hashMap2.keySet());
/* 489 */           hashSet = new HashSet(hashMap1.keySet());
/*     */ 
/*     */           
/* 492 */           ArrayList<?> arrayList = new ArrayList();
/*     */ 
/*     */           
/* 495 */           arrayList.addAll(hashSet2);
/* 496 */           arrayList.retainAll(hashSet);
/*     */           
/* 498 */           hashSet2.removeAll(arrayList);
/* 499 */           hashSet.removeAll(arrayList);
/*     */ 
/*     */           
/* 502 */           String str3 = "ALTER TABLE " + str1 + " ADD ";
/* 503 */           String str4 = "";
/*     */ 
/*     */           
/* 506 */           for (String str5 : hashSet) {
/* 507 */             Map map1 = (Map)hashMap1.get(str5.toUpperCase());
/*     */             
/* 509 */             if (map1 != null) {
/* 510 */               String str6 = (String)map1.get("DATA_TYPE");
/* 511 */               if ("Oracle".equalsIgnoreCase(str)) {
/* 512 */                 if (str6.equalsIgnoreCase("VARCHAR2") || str6.equalsIgnoreCase("NVARCHAR2") || str6.equalsIgnoreCase("CHAR") || str6.equalsIgnoreCase("RAW")) {
/* 513 */                   String str7 = (String)map1.get("DATA_LENGTH");
/* 514 */                   str6 = str6 + "(" + (str7.equals("-1") ? "3500" : str7) + ")";
/* 515 */                 } else if (str6.equalsIgnoreCase("NUMBER")) {
/* 516 */                   String str7 = Util.null2String((String)map1.get("DATA_PRECISION"));
/* 517 */                   String str8 = Util.null2String((String)map1.get("DATA_PRECISION"));
/* 518 */                   String str9 = Util.null2String((String)map1.get("DATA_LENGTH"));
/* 519 */                   if (str7.equals("") && str9.equals("22")) {
/* 520 */                     str6 = str8.equals("0") ? "int" : "number";
/* 521 */                   } else if (!str7.equals("")) {
/* 522 */                     str6 = str6 + "(" + str7 + "," + (String)map1.get("DATA_SCALE") + ")";
/*     */                   } else {
/* 524 */                     DBUpgradeLogger.write2File("====添加字段失败,无法识别的类型:" + str6);
/*     */                   } 
/* 526 */                 } else if (str6.equalsIgnoreCase("FLOAT")) {
/* 527 */                   str6 = str6 + "(" + (String)map1.get("DATA_PRECISION") + ")";
/*     */                 } 
/* 529 */               } else if ("SqlServer".equalsIgnoreCase(str)) {
/* 530 */                 if (str6.equalsIgnoreCase("CHAR") || str6.equalsIgnoreCase("VARBINARY") || str6.equalsIgnoreCase("VARCHAR")) {
/* 531 */                   String str7 = (String)map1.get("DATA_LENGTH");
/* 532 */                   str6 = str6 + "(" + (str7.equals("-1") ? "MAX" : str7) + ")";
/* 533 */                 } else if (str6.equalsIgnoreCase("NCHAR") || str6.equalsIgnoreCase("NVARCHAR")) {
/* 534 */                   String str7 = (String)map1.get("DATA_LENGTH");
/* 535 */                   if (str7.equals("-1")) {
/* 536 */                     str6 = str6 + "(MAX)";
/*     */                   } else {
/* 538 */                     str6 = str6 + "(" + (Integer.parseInt(str7) / 2) + ")";
/*     */                   } 
/* 540 */                 } else if (str6.equalsIgnoreCase("DECIMAL") || str6.equalsIgnoreCase("NUMERIC")) {
/* 541 */                   String str7 = Util.null2String((String)map1.get("DATA_SCALE"));
/* 542 */                   if (str7.equals("0") || str7.equals("")) {
/* 543 */                     str6 = str6 + "(" + (String)map1.get("DATA_PRECISION") + ")";
/*     */                   } else {
/* 545 */                     str6 = str6 + "(" + (String)map1.get("DATA_PRECISION") + "," + (String)map1.get("DATA_SCALE") + ")";
/*     */                   } 
/*     */                 } 
/*     */               } 
/* 549 */               DBUpgradeLogger.write2File("开始添加:" + str3 + str5 + " " + str6);
/* 550 */               upgradeRecordSet.execute(str3 + str5 + " " + str6);
/* 551 */               hashMap1.remove(str5.toUpperCase());
/*     */             } 
/*     */           } 
/* 554 */           bool = checkColumns(str1, (Map)hashMap1, (Map)hashMap2);
/* 555 */           if (!bool) {
/* 556 */             DBUpgradeLogger.write2File("checkColumns 方法在检查时出现异常");
/* 557 */             return bool;
/*     */           } 
/* 559 */           double d = MathUtil.div((b * 100), map.size(), 1);
/* 560 */           setActionProcess(d + "");
/* 561 */           b++;
/*     */         }
/*     */       
/*     */       }
/* 565 */     } catch (Exception exception) {
/* 566 */       bool = false;
/* 567 */       exception.printStackTrace();
/* 568 */       DBUpgradeLogger.write2File(exception.getStackTrace() + "///" + exception.getMessage());
/*     */     } 
/* 570 */     setActionProcess("100");
/* 571 */     return bool;
/*     */   }
/*     */ 
/*     */   
/*     */   public Map<String, String> getColumnMap(String paramString1, String paramString2, String paramString3, String paramString4, String paramString5, String paramString6) {
/* 576 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 577 */     hashMap.put("DATA_TYPE", paramString1);
/* 578 */     hashMap.put("DATA_LENGTH", paramString2);
/* 579 */     hashMap.put("DATA_PRECISION", paramString3);
/* 580 */     hashMap.put("DATA_SCALE", paramString4);
/* 581 */     hashMap.put("DATA_DEFAULT", paramString5);
/* 582 */     hashMap.put("NULLABLE", paramString6);
/* 583 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean checkColumns(String paramString, Map<String, Map<String, String>> paramMap1, Map<String, Map<String, String>> paramMap2) {
/* 596 */     boolean bool = true;
/* 597 */     UpgradeRecordSet upgradeRecordSet = new UpgradeRecordSet();
/* 598 */     Object object = null;
/* 599 */     String str = upgradeRecordSet.getDBType();
/* 600 */     DBUpgradeLogger.write2File("####对表:" + paramString + "做字段类型,长度等检查");
/*     */     try {
/* 602 */       for (String str1 : paramMap1.keySet()) {
/* 603 */         DBUpgradeLogger.write2File("对表:" + paramString + "===字段:" + str1 + "进行校验");
/* 604 */         int i = Integer.parseInt((String)((Map)paramMap2.get(str1)).get("DATA_LENGTH"));
/* 605 */         int j = Integer.parseInt((String)((Map)paramMap1.get(str1)).get("DATA_LENGTH"));
/* 606 */         String str2 = ((String)((Map)paramMap2.get(str1)).get("DATA_TYPE")).toUpperCase();
/* 607 */         String str3 = ((String)((Map)paramMap1.get(str1)).get("DATA_TYPE")).toUpperCase();
/* 608 */         String str4 = ((String)((Map)paramMap2.get(str1)).get("NULLABLE")).toUpperCase();
/* 609 */         String str5 = ((String)((Map)paramMap1.get(str1)).get("NULLABLE")).toUpperCase();
/* 610 */         String str6 = Util.null2String((String)((Map)paramMap1.get(str1)).get("DATA_DEFAULT"));
/*     */ 
/*     */         
/* 613 */         if (str2.contains("CHAR") && str3.contains("CHAR") && i != j) {
/* 614 */           DBUpgradeLogger.write2File("####发现表:" + paramString + "字段长度不一致,字段:" + str1 + "在当前环境中长度为:" + i + ",标准环境中长度为:" + j);
/* 615 */           if (i > j) {
/* 616 */             DBUpgradeLogger.write2File("当前表字段长度大于快照中的字段长度，不做处理");
/*     */             continue;
/*     */           } 
/* 619 */           if ("oracle".equalsIgnoreCase(str)) {
/* 620 */             upgradeRecordSet.executeSql("ALTER TABLE " + paramString + " MODIFY " + str1 + " " + str3 + "(" + j + ")");
/* 621 */           } else if ("sqlserver".equalsIgnoreCase(str)) {
/* 622 */             upgradeRecordSet.executeSql("ALTER TABLE " + paramString + " ALTER COLUMN " + str1 + " " + str3 + "(" + j + ")");
/*     */           } 
/*     */         } 
/*     */         
/* 626 */         if (!str2.equalsIgnoreCase(str3)) {
/* 627 */           DBUpgradeLogger.write2File("error##########################发现表:" + paramString + "######字段:" + str1 + "类型不一致,E7标准中类型为:" + str3 + ",在当前环境中类型为:" + str2 + "###########");
/* 628 */           DBUpgradeLogger.writeErrorLog2File("error##########################发现表:" + paramString + "######字段:" + str1 + "类型不一致,E7标准中类型为:" + str3 + ",在当前环境中类型为:" + str2 + "###########");
/*     */         } 
/*     */       } 
/*     */ 
/*     */ 
/*     */       
/* 634 */       DBUpgradeLogger.write2File("####对表:" + paramString + "做字段类型,长度等检查完成");
/* 635 */     } catch (Exception exception) {
/* 636 */       DBUpgradeLogger.write2File("####对表:" + paramString + "做字段类型,长度等检查出现异常,异常信息:" + exception.toString());
/* 637 */       bool = false;
/* 638 */       exception.printStackTrace();
/*     */     } 
/* 640 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean syncOther() {
/* 648 */     boolean bool = true;
/* 649 */     UpgradeRecordSet upgradeRecordSet = new UpgradeRecordSet();
/* 650 */     Map<String, Map<String, String>> map = getDBRecoverFileFunc();
/* 651 */     byte b = 0;
/* 652 */     Statement statement = null;
/* 653 */     Connection connection = null;
/*     */     try {
/* 655 */       for (String str : map.keySet()) {
/* 656 */         b++;
/* 657 */         DBUpgradeLogger.write2File("=====================================SyncOthers 开始同步" + str + "!");
/* 658 */         Map map1 = map.get(str);
/* 659 */         byte b1 = 0;
/* 660 */         byte b2 = 0;
/* 661 */         byte b3 = 0;
/* 662 */         connection = getLocalConnection();
/* 663 */         if (connection == null) {
/* 664 */           DBUpgradeLogger.write2File("数据库连接失败，请检查参数信息是否正确！");
/* 665 */           bool = false;
/* 666 */           return bool;
/*     */         } 
/* 668 */         for (String str1 : map1.keySet()) {
/*     */           try {
/* 670 */             if (b3 && b3 % 50 == 0) {
/*     */               try {
/* 672 */                 if (statement != null) statement.close(); 
/* 673 */                 if (connection != null) connection.close(); 
/* 674 */               } catch (Exception exception) {
/* 675 */                 exception.printStackTrace();
/*     */               } 
/* 677 */               connection = getLocalConnection();
/* 678 */               if (connection == null) {
/* 679 */                 DBUpgradeLogger.write2File("数据库连接失败，请检查参数信息是否正确！");
/* 680 */                 bool = false;
/* 681 */                 return bool;
/*     */               } 
/*     */             } 
/* 684 */             statement = connection.createStatement();
/* 685 */             DBUpgradeLogger.write2File("==== 开始同步" + str1 + ".");
/* 686 */             String str2 = (String)map1.get(str1);
/* 687 */             if (upgradeRecordSet.getDBType().toLowerCase().equals("sqlserver")) {
/*     */               try {
/* 689 */                 DBUpgradeLogger.write2File("drop " + str + " " + str1);
/* 690 */                 statement.executeUpdate("drop " + str + " " + str1);
/* 691 */               } catch (Exception exception) {
/* 692 */                 exception.printStackTrace();
/*     */               } 
/*     */             }
/* 695 */             DBUpgradeLogger.write2File(str2);
/* 696 */             if (str2.contains("$")) {
/* 697 */               DBUpgradeLogger.write2File("=====数据库快照文件中的标准语句中存在特殊符号,已忽略");
/* 698 */             } else if (str.equalsIgnoreCase("type") && str2.toLowerCase().contains("object")) {
/* 699 */               UpgradeLog upgradeLog = upgradeRecordSet.getUpgradeLogObj(str2);
/*     */               try {
/* 701 */                 statement.executeUpdate(str2);
/* 702 */                 if (upgradeRecordSet.checkWriteLog(str2)) {
/* 703 */                   upgradeLog.setModifyStatus("1");
/* 704 */                   DBUpgradeLogger.write2DB(upgradeLog);
/*     */                 } 
/* 706 */               } catch (Exception exception) {
/* 707 */                 if (upgradeRecordSet.checkWriteLog(str2)) {
/* 708 */                   upgradeLog.setModifyStatus("0");
/* 709 */                   DBUpgradeLogger.write2DB(upgradeLog);
/*     */                 } 
/* 711 */                 DBUpgradeLogger.write2File("=====数据库快照文件中的标准type函数无需更新,已忽略");
/*     */               } 
/*     */             } else {
/* 714 */               statement.executeUpdate(str2);
/*     */             } 
/* 716 */             b1++;
/* 717 */             double d = MathUtil.div((b3 * 100 / map.size()), map1.size(), 1) + ((b - 1) * 100 / map.size());
/* 718 */             setActionProcess(d + "");
/* 719 */             b3++;
/* 720 */           } catch (Exception exception) {
/* 721 */             b3++;
/* 722 */             DBUpgradeLogger.write2File("==== 开始同步" + str1 + "失败!");
/* 723 */             b2++;
/*     */           } 
/*     */         } 
/*     */         try {
/* 727 */           if (statement != null) statement.close(); 
/* 728 */           if (connection != null) connection.close(); 
/* 729 */         } catch (Exception exception) {
/* 730 */           exception.printStackTrace();
/*     */         } 
/* 732 */         DBUpgradeLogger.write2File("=====================================SyncOthers 同步" + str + "结束,成功了" + b1 + "条记录,失败了" + b2 + "条记录!");
/*     */       } 
/* 734 */     } catch (Exception exception) {
/* 735 */       exception.printStackTrace();
/*     */     } finally {
/*     */       try {
/* 738 */         if (statement != null) statement.close(); 
/* 739 */         if (connection != null) connection.close(); 
/* 740 */       } catch (Exception exception) {
/* 741 */         exception.printStackTrace();
/*     */       } 
/*     */     } 
/* 744 */     setActionProcess("100");
/* 745 */     bool = true;
/* 746 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, Map<String, String>> getDBRecoverFileFunc() {
/* 755 */     ArrayList arrayList = new ArrayList();
/* 756 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 757 */     File file1 = new File(this.dbDir);
/* 758 */     File file2 = null;
/* 759 */     if (file1.exists()) {
/* 760 */       File[] arrayOfFile = file1.listFiles();
/* 761 */       for (int i = arrayOfFile.length - 1; arrayOfFile != null && i >= 0; i--) {
/* 762 */         if (arrayOfFile[i].getName().endsWith(".dbrecover")) {
/* 763 */           file2 = arrayOfFile[i];
/*     */           break;
/*     */         } 
/*     */       } 
/*     */     } 
/*     */     try {
/* 769 */       FileReader fileReader = new FileReader(file2);
/* 770 */       BufferedReader bufferedReader = new BufferedReader(fileReader);
/* 771 */       String str = null;
/* 772 */       while ((str = bufferedReader.readLine()) != null) {
/*     */         
/* 774 */         str = SecurityHelper.decrypt("weaververify", str).trim();
/* 775 */         if (!"".equals(str)) {
/*     */           try {
/* 777 */             String[] arrayOfString = str.split("===>");
/* 778 */             String str1 = Util.null2String(arrayOfString[0]).toLowerCase();
/* 779 */             if (!"table".equals(str1) && !str1.equals("")) {
/* 780 */               HashMap<Object, Object> hashMap1 = new HashMap<>();
/* 781 */               hashMap1.put(arrayOfString[1], arrayOfString[2]);
/* 782 */               if (hashMap.get(str1) == null) {
/* 783 */                 hashMap.put(str1, hashMap1); continue;
/*     */               } 
/* 785 */               ((Map<Object, Object>)hashMap.get(str1)).putAll(hashMap1);
/*     */             }
/*     */           
/* 788 */           } catch (Exception exception) {
/* 789 */             DBUpgradeLogger.write2File("error====解析数据库标准快照文件语句:" + str + "失败!失败原因:" + exception.toString());
/* 790 */             exception.printStackTrace();
/*     */           } 
/*     */         }
/*     */       } 
/* 794 */     } catch (Exception exception) {
/* 795 */       exception.printStackTrace();
/* 796 */       return null;
/*     */     } 
/*     */     
/* 799 */     return (Map)hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Connection getLocalConnection() {
/* 808 */     Connection connection = null;
/*     */     try {
/* 810 */       RecordSet recordSet = new RecordSet();
/*     */       try {
/* 812 */         String str1 = recordSet.getPropValue("weaver", "DriverClasses");
/* 813 */         String str2 = recordSet.getPropValue("weaver", "ecology.url");
/* 814 */         String str3 = recordSet.getPropValue("weaver", "ecology.user");
/* 815 */         String str4 = recordSet.getPropValue("weaver", "ecology.password");
/* 816 */         Class.forName(str1);
/* 817 */         connection = DriverManager.getConnection(str2, str3, str4);
/* 818 */         return connection;
/* 819 */       } catch (Exception exception) {
/* 820 */         exception.printStackTrace();
/* 821 */         return null;
/*     */       } 
/* 823 */     } catch (Exception exception) {
/* 824 */       (new BaseBean()).writeLog(exception.getMessage());
/* 825 */       if (connection != null) {
/*     */         try {
/* 827 */           connection.close();
/* 828 */         } catch (SQLException sQLException) {
/* 829 */           connection = null;
/* 830 */           sQLException.printStackTrace();
/*     */         } 
/*     */       } else {
/* 833 */         connection = null;
/*     */       } 
/*     */       
/* 836 */       return connection;
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/upgradetool/installupgrade/UpgradeDBCmd.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */