/*     */ package weaver.upgradetool;
/*     */ import java.io.BufferedReader;
/*     */ import java.io.ByteArrayOutputStream;
/*     */ import java.io.File;
/*     */ import java.io.FileInputStream;
/*     */ import java.io.FileNotFoundException;
/*     */ import java.io.FileOutputStream;
/*     */ import java.io.IOException;
/*     */ import java.io.InputStreamReader;
/*     */ import java.util.Properties;
/*     */ 
/*     */ public class UpgradeFileUtil {
/*  13 */   private static String[] allowPaths = null;
/*     */   
/*     */   public UpgradeFileUtil() {
/*  16 */     if (allowPaths == null) {
/*  17 */       allowPaths = getAllowPaths();
/*     */     }
/*     */   }
/*     */ 
/*     */   
/*     */   public String getPath(String paramString) {
/*  23 */     if (paramString == null) {
/*  24 */       return "";
/*     */     }
/*  26 */     String str = System.getProperty("os.name").toLowerCase();
/*  27 */     if (!str.startsWith("windows")) {
/*  28 */       return paramString.replaceAll("\\\\+", File.separator);
/*     */     }
/*  30 */     return paramString;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public StringBuffer readFile(File paramFile) {
/*  41 */     String str = "GBK";
/*     */     
/*  43 */     InputStreamReader inputStreamReader = null;
/*  44 */     BufferedReader bufferedReader = null;
/*  45 */     StringBuffer stringBuffer = new StringBuffer();
/*     */     try {
/*  47 */       boolean bool = FileCharsetDetector.check(paramFile);
/*  48 */       if (bool) {
/*  49 */         str = "UTF-8";
/*     */       } else {
/*  51 */         str = "GBK";
/*     */       } 
/*     */       
/*  54 */       inputStreamReader = new InputStreamReader(new FileInputStream(paramFile), str);
/*  55 */       bufferedReader = new BufferedReader(inputStreamReader);
/*  56 */       String str1 = null;
/*  57 */       while ((str1 = bufferedReader.readLine()) != null) {
/*  58 */         stringBuffer.append(str1).append("\r\n");
/*     */       }
/*  60 */     } catch (IOException iOException) {
/*  61 */       iOException.printStackTrace();
/*     */     } finally {
/*  63 */       if (bufferedReader != null) {
/*     */         try {
/*  65 */           bufferedReader.close();
/*  66 */         } catch (IOException iOException) {
/*  67 */           iOException.printStackTrace();
/*     */         } 
/*     */       }
/*  70 */       if (inputStreamReader != null) {
/*     */         try {
/*  72 */           inputStreamReader.close();
/*  73 */         } catch (IOException iOException) {
/*  74 */           iOException.printStackTrace();
/*     */         } 
/*     */       }
/*     */     } 
/*     */ 
/*     */     
/*  80 */     return stringBuffer;
/*     */   }
/*     */   
/*     */   public static String[] getAllowPaths() {
/*  84 */     FileInputStream fileInputStream = null;
/*  85 */     String[] arrayOfString = null;
/*     */     try {
/*  87 */       Properties properties = new Properties();
/*  88 */       String str = GCONST.getRootPath() + "templetecheck" + File.separatorChar + "exclude" + File.separatorChar + "allow.properties";
/*  89 */       if ((new File(str)).exists()) {
/*  90 */         fileInputStream = new FileInputStream(str);
/*  91 */         properties.load(fileInputStream);
/*  92 */         String str1 = properties.getProperty("allow");
/*  93 */         arrayOfString = str1.split(",");
/*  94 */         fileInputStream.close();
/*     */       } 
/*  96 */     } catch (Exception exception) {
/*  97 */       exception.printStackTrace();
/*     */     } finally {
/*     */       try {
/* 100 */         if (fileInputStream != null) {
/* 101 */           fileInputStream.close();
/*     */         }
/* 103 */       } catch (IOException iOException) {}
/*     */     } 
/*     */     
/* 106 */     return arrayOfString;
/*     */   }
/*     */   
/*     */   public boolean checkPath(String paramString) {
/* 110 */     boolean bool = false;
/* 111 */     if ("".equals(paramString) || paramString == null) {
/* 112 */       return true;
/*     */     }
/* 114 */     RecordSet recordSet = new RecordSet();
/* 115 */     paramString = paramString.replace("\\", "/");
/*     */ 
/*     */     
/* 118 */     String str = paramString.toLowerCase().replace(GCONST.getRootPath().replace("\\", "/").toLowerCase(), "");
/* 119 */     int i = paramString.toLowerCase().indexOf(str);
/*     */     
/* 121 */     paramString = paramString.substring(i);
/* 122 */     if (!paramString.startsWith("/")) {
/* 123 */       paramString = "/" + paramString;
/*     */     }
/*     */     
/* 126 */     recordSet.executeQuery("select * from configFileManager where filepath =?", new Object[] { paramString });
/* 127 */     if (recordSet.next()) {
/* 128 */       bool = true;
/*     */     }
/* 130 */     else if (allowPaths != null) {
/* 131 */       for (byte b = 0; b < allowPaths.length; b++) {
/* 132 */         String str1 = allowPaths[b];
/* 133 */         if (paramString.indexOf(str1) > -1) {
/* 134 */           bool = true;
/*     */           
/*     */           break;
/*     */         } 
/*     */       } 
/*     */     } 
/* 140 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static void downLoadHtmlFileToSyspath(File paramFile, String paramString) {
/* 146 */     FileInputStream fileInputStream = null;
/*     */     
/*     */     try {
/* 149 */       fileInputStream = new FileInputStream(paramFile);
/* 150 */     } catch (FileNotFoundException fileNotFoundException) {
/* 151 */       fileNotFoundException.printStackTrace();
/*     */     } 
/* 153 */     if (null == fileInputStream) {
/*     */       return;
/*     */     }
/* 156 */     byte[] arrayOfByte = new byte[0];
/*     */     try {
/* 158 */       arrayOfByte = readInputStream(fileInputStream);
/* 159 */     } catch (IOException iOException) {
/* 160 */       iOException.printStackTrace();
/*     */     } 
/*     */     
/* 163 */     File file = new File(paramString + File.separator + paramFile.getName());
/*     */ 
/*     */     
/* 166 */     FileOutputStream fileOutputStream = null;
/*     */     try {
/* 168 */       fileOutputStream = new FileOutputStream(file);
/*     */       
/* 170 */       fileOutputStream.write(arrayOfByte);
/* 171 */     } catch (FileNotFoundException fileNotFoundException) {
/* 172 */       fileNotFoundException.printStackTrace();
/* 173 */     } catch (IOException iOException) {
/*     */       
/* 175 */       iOException.printStackTrace();
/*     */     } finally {
/* 177 */       if (null != fileOutputStream) {
/*     */         try {
/* 179 */           fileOutputStream.flush();
/* 180 */           fileOutputStream.close();
/* 181 */         } catch (IOException iOException) {
/* 182 */           iOException.printStackTrace();
/*     */         } 
/*     */       }
/*     */       try {
/* 186 */         fileInputStream.close();
/* 187 */       } catch (IOException iOException) {
/* 188 */         iOException.printStackTrace();
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public static final byte[] readInputStream(InputStream paramInputStream) throws IOException {
/* 196 */     byte[] arrayOfByte = new byte[1024];
/* 197 */     int i = 0;
/* 198 */     ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
/* 199 */     while ((i = paramInputStream.read(arrayOfByte)) != -1) {
/* 200 */       byteArrayOutputStream.write(arrayOfByte, 0, i);
/*     */     }
/*     */     try {
/* 203 */       byteArrayOutputStream.close();
/* 204 */     } catch (IOException iOException) {
/*     */       
/* 206 */       iOException.printStackTrace();
/*     */     } 
/* 208 */     return byteArrayOutputStream.toByteArray();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getToolVersion() {
/* 218 */     BaseBean baseBean = new BaseBean();
/* 219 */     String str = baseBean.getPropValue("upgradetoolutil", "version");
/* 220 */     if (str == null || "".equals(str)) {
/* 221 */       str = "未知的升级工具版本";
/*     */     } else {
/* 223 */       str = "V" + str;
/*     */     } 
/* 225 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/weaver/upgradetool/UpgradeFileUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */