/*     */ package microsoft.exchange.webservices.data.core.request;
/*     */ 
/*     */ import java.io.BufferedInputStream;
/*     */ import java.io.ByteArrayOutputStream;
/*     */ import java.io.IOException;
/*     */ import java.io.InputStream;
/*     */ import java.io.OutputStream;
/*     */ import java.security.NoSuchAlgorithmException;
/*     */ import java.security.cert.CertificateException;
/*     */ import java.security.cert.X509Certificate;
/*     */ import java.util.Arrays;
/*     */ import java.util.HashMap;
/*     */ import java.util.Iterator;
/*     */ import java.util.Map;
/*     */ import javax.net.ssl.HostnameVerifier;
/*     */ import javax.net.ssl.SSLContext;
/*     */ import microsoft.exchange.webservices.data.core.WebProxy;
/*     */ import microsoft.exchange.webservices.data.core.exception.http.EWSHttpException;
/*     */ import org.apache.http.Header;
/*     */ import org.apache.http.HttpEntity;
/*     */ import org.apache.http.HttpHost;
/*     */ import org.apache.http.auth.AuthScope;
/*     */ import org.apache.http.auth.Credentials;
/*     */ import org.apache.http.auth.NTCredentials;
/*     */ import org.apache.http.client.CredentialsProvider;
/*     */ import org.apache.http.client.config.RequestConfig;
/*     */ import org.apache.http.client.methods.CloseableHttpResponse;
/*     */ import org.apache.http.client.methods.HttpPost;
/*     */ import org.apache.http.client.methods.HttpUriRequest;
/*     */ import org.apache.http.client.protocol.HttpClientContext;
/*     */ import org.apache.http.conn.socket.LayeredConnectionSocketFactory;
/*     */ import org.apache.http.conn.ssl.NoopHostnameVerifier;
/*     */ import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
/*     */ import org.apache.http.impl.client.BasicCredentialsProvider;
/*     */ import org.apache.http.impl.client.CloseableHttpClient;
/*     */ import org.apache.http.impl.client.HttpClients;
/*     */ import org.apache.http.protocol.HttpContext;
/*     */ import org.apache.http.ssl.SSLContextBuilder;
/*     */ import org.apache.http.ssl.TrustStrategy;
/*     */ import org.apache.http.util.EntityUtils;
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HttpClientWebRequest
/*     */   extends HttpWebRequest
/*     */ {
/*  47 */   private HttpPost httpPost = null;
/*  48 */   private CloseableHttpResponse response = null;
/*     */   private final CloseableHttpClient httpClient;
/*     */   private final HttpClientContext httpContext;
/*     */   
/*     */   public HttpClientWebRequest(CloseableHttpClient paramCloseableHttpClient, HttpClientContext paramHttpClientContext) {
/*  53 */     this.httpClient = createSSLClientDefault();
/*  54 */     this.httpContext = paramHttpClientContext;
/*     */   }
/*     */ 
/*     */   
/*     */   public CloseableHttpClient createSSLClientDefault() {
/*  59 */     CloseableHttpClient closeableHttpClient = null;
/*     */     try {
/*  61 */       SSLContext sSLContext = null;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/*  67 */       sSLContext = (new SSLContextBuilder()).loadTrustMaterial(null, new TrustStrategy() { public boolean isTrusted(X509Certificate[] param1ArrayOfX509Certificate, String param1String) throws CertificateException { return true; } }).build();
/*  68 */       SSLConnectionSocketFactory sSLConnectionSocketFactory = new SSLConnectionSocketFactory(sSLContext, (HostnameVerifier)NoopHostnameVerifier.INSTANCE);
/*  69 */       closeableHttpClient = HttpClients.custom().setSSLSocketFactory((LayeredConnectionSocketFactory)sSLConnectionSocketFactory).build();
/*  70 */     } catch (NoSuchAlgorithmException|java.security.KeyStoreException|java.security.KeyManagementException noSuchAlgorithmException) {
/*  71 */       noSuchAlgorithmException.printStackTrace();
/*     */     } 
/*  73 */     return closeableHttpClient;
/*     */   }
/*     */   
/*     */   public void close() throws IOException {
/*  77 */     if (this.response != null && this.response.getEntity() != null) {
/*  78 */       EntityUtils.consume(this.response.getEntity());
/*  79 */     } else if (this.httpPost != null) {
/*  80 */       this.httpPost.releaseConnection();
/*     */     } 
/*     */     
/*  83 */     this.httpPost = null;
/*     */   }
/*     */   
/*     */   public void prepareConnection() {
/*  87 */     this.httpPost = new HttpPost(getUrl().toString());
/*  88 */     this.httpPost.addHeader("Content-type", getContentType());
/*  89 */     this.httpPost.addHeader("User-Agent", getUserAgent());
/*  90 */     this.httpPost.addHeader("Accept", getAccept());
/*  91 */     this.httpPost.addHeader("Keep-Alive", "300");
/*  92 */     this.httpPost.addHeader("Connection", "Keep-Alive");
/*  93 */     if (isAcceptGzipEncoding()) {
/*  94 */       this.httpPost.addHeader("Accept-Encoding", "gzip,deflate");
/*     */     }
/*     */     
/*  97 */     if (getHeaders() != null) {
/*  98 */       Iterator<Map.Entry> iterator = getHeaders().entrySet().iterator();
/*     */       
/* 100 */       while (iterator.hasNext()) {
/* 101 */         Map.Entry entry = iterator.next();
/* 102 */         this.httpPost.addHeader((String)entry.getKey(), (String)entry.getValue());
/*     */       } 
/*     */     } 
/*     */     
/* 106 */     RequestConfig.Builder builder = RequestConfig.custom().setAuthenticationEnabled(true).setConnectionRequestTimeout(getTimeout()).setConnectTimeout(getTimeout()).setRedirectsEnabled(isAllowAutoRedirect()).setSocketTimeout(getTimeout()).setTargetPreferredAuthSchemes(Arrays.asList(new String[] { "NTLM", "Basic" })).setProxyPreferredAuthSchemes(Arrays.asList(new String[] { "NTLM", "Basic" }));
/* 107 */     BasicCredentialsProvider basicCredentialsProvider = new BasicCredentialsProvider();
/* 108 */     WebProxy webProxy = getProxy();
/* 109 */     if (webProxy != null) {
/* 110 */       HttpHost httpHost = new HttpHost(webProxy.getHost(), webProxy.getPort());
/* 111 */       builder.setProxy(httpHost);
/* 112 */       if (webProxy.hasCredentials()) {
/* 113 */         NTCredentials nTCredentials = new NTCredentials(webProxy.getCredentials().getUsername(), webProxy.getCredentials().getPassword(), "", webProxy.getCredentials().getDomain());
/* 114 */         basicCredentialsProvider.setCredentials(new AuthScope(httpHost), (Credentials)nTCredentials);
/*     */       } 
/*     */     } 
/*     */     
/* 118 */     if (isAllowAuthentication() && getUsername() != null) {
/* 119 */       NTCredentials nTCredentials = new NTCredentials(getUsername(), getPassword(), "", getDomain());
/* 120 */       basicCredentialsProvider.setCredentials(new AuthScope(AuthScope.ANY), (Credentials)nTCredentials);
/*     */     } 
/*     */     
/* 123 */     this.httpContext.setCredentialsProvider((CredentialsProvider)basicCredentialsProvider);
/* 124 */     this.httpPost.setConfig(builder.build());
/*     */   }
/*     */   
/*     */   public InputStream getInputStream() throws EWSHttpException, IOException {
/* 128 */     throwIfResponseIsNull();
/* 129 */     BufferedInputStream bufferedInputStream = null;
/*     */     
/*     */     try {
/* 132 */       bufferedInputStream = new BufferedInputStream(this.response.getEntity().getContent());
/* 133 */       return bufferedInputStream;
/* 134 */     } catch (IOException iOException) {
/* 135 */       throw new EWSHttpException("Connection Error " + iOException);
/*     */     } 
/*     */   }
/*     */   
/*     */   public InputStream getErrorStream() throws EWSHttpException {
/* 140 */     throwIfResponseIsNull();
/* 141 */     BufferedInputStream bufferedInputStream = null;
/*     */     
/*     */     try {
/* 144 */       bufferedInputStream = new BufferedInputStream(this.response.getEntity().getContent());
/* 145 */       return bufferedInputStream;
/* 146 */     } catch (Exception exception) {
/* 147 */       throw new EWSHttpException("Connection Error " + exception);
/*     */     } 
/*     */   }
/*     */   
/*     */   public OutputStream getOutputStream() throws EWSHttpException {
/* 152 */     ByteArrayOutputStream byteArrayOutputStream = null;
/* 153 */     throwIfRequestIsNull();
/* 154 */     byteArrayOutputStream = new ByteArrayOutputStream();
/* 155 */     this.httpPost.setEntity((HttpEntity)new ByteArrayOSRequestEntity(byteArrayOutputStream));
/* 156 */     return byteArrayOutputStream;
/*     */   }
/*     */   
/*     */   public Map<String, String> getResponseHeaders() throws EWSHttpException {
/* 160 */     throwIfResponseIsNull();
/* 161 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 162 */     Header[] arrayOfHeader1 = this.response.getAllHeaders();
/* 163 */     Header[] arrayOfHeader2 = arrayOfHeader1;
/* 164 */     int i = arrayOfHeader1.length;
/*     */     
/* 166 */     for (byte b = 0; b < i; b++) {
/* 167 */       Header header = arrayOfHeader2[b];
/* 168 */       if (header.getName().equals("Set-Cookie")) {
/* 169 */         String str = "";
/* 170 */         if (hashMap.containsKey("Set-Cookie")) {
/* 171 */           str = str + (String)hashMap.get("Set-Cookie");
/* 172 */           str = str + ",";
/*     */         } 
/*     */         
/* 175 */         str = str + header.getValue();
/* 176 */         hashMap.put("Set-Cookie", str);
/*     */       } else {
/* 178 */         hashMap.put(header.getName(), header.getValue());
/*     */       } 
/*     */     } 
/*     */     
/* 182 */     return (Map)hashMap;
/*     */   }
/*     */   
/*     */   public String getResponseHeaderField(String paramString) throws EWSHttpException {
/* 186 */     throwIfResponseIsNull();
/* 187 */     Header header = this.response.getFirstHeader(paramString);
/* 188 */     return (header != null) ? header.getValue() : null;
/*     */   }
/*     */   
/*     */   public String getContentEncoding() throws EWSHttpException {
/* 192 */     throwIfResponseIsNull();
/* 193 */     return (this.response.getFirstHeader("content-encoding") != null) ? this.response.getFirstHeader("content-encoding").getValue() : null;
/*     */   }
/*     */   
/*     */   public String getResponseContentType() throws EWSHttpException {
/* 197 */     throwIfResponseIsNull();
/* 198 */     return (this.response.getFirstHeader("Content-type") != null) ? this.response.getFirstHeader("Content-type").getValue() : null;
/*     */   }
/*     */   
/*     */   public int executeRequest() throws EWSHttpException, IOException {
/* 202 */     throwIfRequestIsNull();
/* 203 */     this.response = this.httpClient.execute((HttpUriRequest)this.httpPost, (HttpContext)this.httpContext);
/* 204 */     return this.response.getStatusLine().getStatusCode();
/*     */   }
/*     */   
/*     */   public int getResponseCode() throws EWSHttpException {
/* 208 */     throwIfResponseIsNull();
/* 209 */     return this.response.getStatusLine().getStatusCode();
/*     */   }
/*     */   
/*     */   public String getResponseText() throws EWSHttpException {
/* 213 */     throwIfResponseIsNull();
/* 214 */     return this.response.getStatusLine().getReasonPhrase();
/*     */   }
/*     */   
/*     */   private void throwIfRequestIsNull() throws EWSHttpException {
/* 218 */     if (null == this.httpPost) {
/* 219 */       throw new EWSHttpException("Connection not established");
/*     */     }
/*     */   }
/*     */   
/*     */   private void throwIfResponseIsNull() throws EWSHttpException {
/* 224 */     if (null == this.response) {
/* 225 */       throw new EWSHttpException("Connection not established");
/*     */     }
/*     */   }
/*     */   
/*     */   public Map<String, String> getRequestProperty() throws EWSHttpException {
/* 230 */     throwIfRequestIsNull();
/* 231 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 232 */     Header[] arrayOfHeader1 = this.httpPost.getAllHeaders();
/* 233 */     Header[] arrayOfHeader2 = arrayOfHeader1;
/* 234 */     int i = arrayOfHeader1.length;
/*     */     
/* 236 */     for (byte b = 0; b < i; b++) {
/* 237 */       Header header = arrayOfHeader2[b];
/* 238 */       hashMap.put(header.getName(), header.getValue());
/*     */     } 
/*     */     
/* 241 */     return (Map)hashMap;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/microsoft/exchange/webservices/data/core/request/HttpClientWebRequest.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */