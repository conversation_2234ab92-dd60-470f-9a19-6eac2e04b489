/*     */ package wscheck;
/*     */ 
/*     */ import java.io.File;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class KeyUpgrade
/*     */ {
/*  22 */   private String rootpath = GCONST.getRootPath();
/*  23 */   private String keyupgradefolder = "keyupgrade";
/*  24 */   private String datafolder = "data";
/*  25 */   private String keyfolder = "key";
/*  26 */   private String keyext = "." + this.keyfolder;
/*  27 */   private String keyupgradepath = this.rootpath + this.keyupgradefolder;
/*  28 */   private String onlykeypath = this.rootpath + this.datafolder + File.separatorChar + this.keyfolder;
/*  29 */   private String onlykeyname = "ecology" + this.keyext;
/*     */ 
/*     */   
/*     */   public void execute() {
/*  33 */     update();
/*     */   }
/*     */   public void update() {
/*  36 */     File file = new File(this.onlykeypath + File.separatorChar + this.onlykeyname);
/*  37 */     boolean bool = CheckKey.verifykey();
/*     */     
/*  39 */     if (!bool) {
/*     */       return;
/*     */     }
/*     */     
/*  43 */     if (file.exists()) {
/*     */       
/*  45 */       ArrayList<String> arrayList = CommonUtil.getLineList(file);
/*  46 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*  47 */       for (byte b = 0; arrayList != null && b < arrayList.size(); b++) {
/*  48 */         String str = arrayList.get(b);
/*  49 */         if (!"".equals(str)) {
/*     */ 
/*     */ 
/*     */           
/*  53 */           str = SecurityHelper.decrypt("weaververify", str);
/*  54 */           arrayList.set(b, str);
/*  55 */           String[] arrayOfString = str.split("=");
/*  56 */           if (arrayOfString != null && 
/*  57 */             arrayOfString.length == 2) {
/*  58 */             hashMap.put(arrayOfString[0], b + "");
/*     */           }
/*     */         } 
/*     */       } 
/*     */       
/*  63 */       File file1 = new File(this.keyupgradepath);
/*  64 */       if (file1.exists()) {
/*  65 */         ArrayList<String> arrayList1 = new ArrayList();
/*  66 */         File[] arrayOfFile = file1.listFiles(); byte b1;
/*  67 */         for (b1 = 0; arrayOfFile != null && b1 < arrayOfFile.length; b1++) {
/*  68 */           if (arrayOfFile[b1].getPath().lastIndexOf(this.keyext) > -1) {
/*  69 */             arrayList1.add(arrayOfFile[b1].getAbsolutePath());
/*  70 */             ArrayList<String> arrayList2 = CommonUtil.getLineList(arrayOfFile[b1]);
/*  71 */             for (byte b3 = 0; arrayList2 != null && b3 < arrayList2.size(); b3++) {
/*  72 */               String str = arrayList2.get(b3);
/*  73 */               if (!"".equals(str)) {
/*     */ 
/*     */ 
/*     */                 
/*  77 */                 str = SecurityHelper.decrypt("weaververify", str);
/*     */                 
/*  79 */                 String[] arrayOfString = str.split("=");
/*  80 */                 if (arrayOfString != null && 
/*  81 */                   arrayOfString.length == 2) {
/*     */ 
/*     */                   
/*  84 */                   String str1 = CommonUtil.getString((String)hashMap.get(arrayOfString[0]));
/*     */ 
/*     */                   
/*  87 */                   if (str1.equals("")) {
/*  88 */                     arrayList.add(str);
/*  89 */                     hashMap.put(arrayOfString[0], "" + (arrayList.size() - 1));
/*  90 */                     if (PageErrorMap.containsKey(Util.null2String(arrayOfString[0])))
/*     */                     {
/*  92 */                       PageErrorMap.remove(arrayOfString[0]);
/*     */                     }
/*     */                   } else {
/*  95 */                     arrayList.set(Util.getIntValue(str1, 0), str);
/*     */                     
/*  97 */                     if (PageErrorMap.containsKey(Util.null2String(arrayOfString[0])))
/*     */                     {
/*  99 */                       PageErrorMap.remove(arrayOfString[0]);
/*     */                     }
/*     */                   } 
/*     */                 } 
/*     */               } 
/*     */             } 
/*     */           } 
/*     */         } 
/*     */ 
/*     */         
/* 109 */         for (b1 = 0; b1 < arrayList1.size(); b1++) {
/* 110 */           String str = arrayList1.get(b1);
/* 111 */           ZipUtils.deleteFile(str);
/*     */         } 
/*     */ 
/*     */         
/* 115 */         StringBuffer stringBuffer = new StringBuffer();
/* 116 */         for (byte b2 = 0; arrayList != null && b2 < arrayList.size(); b2++) {
/* 117 */           String str = SecurityHelper.encrypt("weaververify", arrayList.get(b2));
/*     */ 
/*     */           
/* 120 */           stringBuffer.append(str);
/* 121 */           if (b2 < arrayList.size() - 1) {
/* 122 */             stringBuffer.append("\n");
/*     */           }
/*     */         } 
/* 125 */         if (!"".equals(stringBuffer.toString())) {
/*     */           
/* 127 */           ZipUtils.deleteFile(this.onlykeypath + File.separatorChar + this.onlykeyname);
/* 128 */           CommonUtil.rewritefile(this.onlykeypath + File.separatorChar + this.onlykeyname, stringBuffer.toString());
/*     */         } 
/* 130 */         if (arrayOfFile.length > 0) {
/*     */           
/* 132 */           PageErrorMap.getList().clear();
/* 133 */           PageErrorMap.clear();
/* 134 */           PageErrorMap.getAdminlist().clear();
/* 135 */           PageMD5Map.initMD5Map();
/* 136 */           CheckScanFile checkScanFile = new CheckScanFile();
/* 137 */           checkScanFile.execute();
/*     */         } 
/*     */       } 
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/wscheck/KeyUpgrade.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */