/*     */ package wscheck;
/*     */ 
/*     */ import java.io.BufferedInputStream;
/*     */ import java.io.BufferedOutputStream;
/*     */ import java.io.File;
/*     */ import java.io.FileInputStream;
/*     */ import java.io.FileNotFoundException;
/*     */ import java.io.FileOutputStream;
/*     */ import java.io.IOException;
/*     */ import java.io.InputStream;
/*     */ import java.util.Enumeration;
/*     */ import java.util.List;
/*     */ import java.util.zip.GZIPOutputStream;
/*     */ import org.apache.commons.io.FileUtils;
/*     */ import org.apache.tools.zip.ZipEntry;
/*     */ import org.apache.tools.zip.ZipFile;
/*     */ import org.apache.tools.zip.ZipOutputStream;
/*     */ import weaver.general.BaseBean;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ZipUtils
/*     */ {
/*  26 */   private static int BUFFER = 4096;
/*  27 */   private static byte[] B_ARRAY = new byte[BUFFER];
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void zip(String paramString1, String paramString2) throws Exception {
/*  35 */     FileOutputStream fileOutputStream = null;
/*  36 */     BufferedOutputStream bufferedOutputStream = null;
/*  37 */     ZipOutputStream zipOutputStream = null;
/*     */     
/*     */     try {
/*  40 */       File file1 = new File(paramString2);
/*  41 */       if (!file1.getParentFile().exists())
/*  42 */         file1.getParentFile().mkdirs(); 
/*  43 */       fileOutputStream = new FileOutputStream(paramString2);
/*  44 */       bufferedOutputStream = new BufferedOutputStream(fileOutputStream);
/*  45 */       zipOutputStream = new ZipOutputStream(bufferedOutputStream);
/*  46 */       File file2 = new File(paramString1);
/*  47 */       String str = null;
/*  48 */       if (file2.isDirectory()) {
/*     */         
/*  50 */         str = file2.getPath();
/*     */       }
/*     */       else {
/*     */         
/*  54 */         str = file2.getParent();
/*     */       } 
/*  56 */       str = str + "" + File.separatorChar;
/*  57 */       zipFile(file2, str, zipOutputStream);
/*  58 */       zipOutputStream.closeEntry();
/*     */     }
/*  60 */     catch (Exception exception) {
/*     */       
/*  62 */       (new BaseBean()).writeLog("压缩文件出错:" + exception);
/*  63 */       throw exception;
/*     */     } finally {
/*     */ 
/*     */       
/*     */       try {
/*     */         
/*  69 */         if (null != zipOutputStream)
/*     */         {
/*  71 */           zipOutputStream.close();
/*     */         }
/*     */       }
/*  74 */       catch (Exception exception) {}
/*     */ 
/*     */ 
/*     */ 
/*     */       
/*     */       try {
/*  80 */         if (null != bufferedOutputStream)
/*     */         {
/*  82 */           bufferedOutputStream.close();
/*     */         }
/*     */       }
/*  85 */       catch (Exception exception) {}
/*     */ 
/*     */ 
/*     */ 
/*     */       
/*     */       try {
/*  91 */         if (null != fileOutputStream)
/*     */         {
/*  93 */           fileOutputStream.close();
/*     */         }
/*     */       }
/*  96 */       catch (Exception exception) {}
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static void zipFile(File paramFile, String paramString, ZipOutputStream paramZipOutputStream) throws Exception {
/* 111 */     File[] arrayOfFile = new File[0];
/* 112 */     if (paramFile.isDirectory()) {
/*     */       
/* 114 */       arrayOfFile = paramFile.listFiles();
/*     */     }
/*     */     else {
/*     */       
/* 118 */       arrayOfFile = new File[1];
/* 119 */       arrayOfFile[0] = paramFile;
/*     */     } 
/*     */     
/* 122 */     byte[] arrayOfByte = new byte[1024];
/* 123 */     int i = 0;
/*     */     
/*     */     try {
/* 126 */       for (File file : arrayOfFile) {
/*     */         
/* 128 */         if (file.isDirectory())
/*     */         {
/* 130 */           String str = file.getPath().substring(paramString.length()) + "" + File.separatorChar;
/*     */ 
/*     */           
/* 133 */           zipFile(file, paramString, paramZipOutputStream);
/*     */         }
/*     */         else
/*     */         {
/* 137 */           String str = file.getPath().substring(paramString.length());
/* 138 */           ZipEntry zipEntry = new ZipEntry(str);
/*     */           
/* 140 */           zipEntry.setSize(file.length());
/* 141 */           zipEntry.setTime(file.lastModified());
/*     */           
/* 143 */           FileInputStream fileInputStream = new FileInputStream(file);
/* 144 */           BufferedInputStream bufferedInputStream = new BufferedInputStream(fileInputStream);
/* 145 */           paramZipOutputStream.putNextEntry(zipEntry);
/* 146 */           while ((i = bufferedInputStream.read(arrayOfByte)) > 0)
/*     */           {
/* 148 */             paramZipOutputStream.write(arrayOfByte, 0, i);
/*     */           }
/* 150 */           bufferedInputStream.close();
/* 151 */           fileInputStream.close();
/*     */         }
/*     */       
/*     */       } 
/* 155 */     } catch (Exception exception) {
/*     */       
/* 157 */       exception.printStackTrace();
/* 158 */       (new BaseBean()).writeLog("压缩文件出错 zipFile : " + exception);
/* 159 */       throw exception;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void unZip(String paramString1, String paramString2) throws Exception {
/* 173 */     paramString2 = paramString2.endsWith("" + File.separatorChar) ? paramString2 : (paramString2 + File.separatorChar);
/* 174 */     byte[] arrayOfByte = new byte[1024];
/*     */     
/* 176 */     ZipFile zipFile = null;
/*     */     
/*     */     try {
/* 179 */       zipFile = new ZipFile(new File(paramString1));
/* 180 */       Enumeration<ZipEntry> enumeration = zipFile.getEntries();
/* 181 */       ZipEntry zipEntry = null;
/* 182 */       while (enumeration.hasMoreElements())
/*     */       {
/* 184 */         zipEntry = enumeration.nextElement();
/* 185 */         File file = new File(paramString2 + zipEntry.getName());
/* 186 */         if (zipEntry.isDirectory()) {
/*     */ 
/*     */           
/* 189 */           file.mkdirs();
/*     */         }
/*     */         else {
/*     */           
/* 193 */           if (!file.getParentFile().exists())
/* 194 */             file.getParentFile().mkdirs(); 
/* 195 */           FileOutputStream fileOutputStream = new FileOutputStream(file);
/* 196 */           InputStream inputStream = zipFile.getInputStream(zipEntry); int i;
/* 197 */           while ((i = inputStream.read(arrayOfByte)) > 0) {
/* 198 */             fileOutputStream.write(arrayOfByte, 0, i);
/*     */           }
/* 200 */           fileOutputStream.close();
/* 201 */           inputStream.close();
/*     */         } 
/* 203 */         file.setLastModified(zipEntry.getTime());
/*     */       }
/*     */     
/*     */     }
/* 207 */     catch (IOException iOException) {
/*     */       
/* 209 */       iOException.printStackTrace();
/* 210 */       (new BaseBean()).writeLog("文件解压成功 unZip : " + iOException);
/* 211 */       throw new Exception(iOException);
/*     */     } finally {
/*     */ 
/*     */       
/*     */       try {
/*     */         
/* 217 */         if (null != zipFile) {
/* 218 */           zipFile.close();
/*     */         }
/* 220 */       } catch (Exception exception) {
/*     */         
/* 222 */         exception.printStackTrace();
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void execute(String paramString1, String paramString2) {
/* 232 */     File file = new File(paramString1);
/* 233 */     String str = paramString1.substring(paramString1.lastIndexOf("" + File.separatorChar) + 1);
/* 234 */     ZipOutputStream zipOutputStream = getZipOutputStream(paramString2);
/* 235 */     zipPack(zipOutputStream, file, str);
/*     */     try {
/* 237 */       if (null != zipOutputStream) {
/* 238 */         zipOutputStream.close();
/*     */       }
/* 240 */     } catch (IOException iOException) {
/* 241 */       iOException.printStackTrace();
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void execute(List<String> paramList, String paramString1, String paramString2, String paramString3) {
/* 255 */     ZipOutputStream zipOutputStream = getZipOutputStream(paramString1);
/*     */     
/* 257 */     for (String str : paramList) {
/* 258 */       File file = new File(str);
/*     */       
/* 260 */       if (file.exists()) {
/*     */         
/* 262 */         String str1 = paramString3 + File.separatorChar + str.substring(paramString2.length());
/* 263 */         zipPack(zipOutputStream, file, str1);
/*     */       } 
/*     */     } 
/*     */     
/*     */     try {
/* 268 */       if (null != zipOutputStream) {
/* 269 */         zipOutputStream.close();
/*     */       }
/* 271 */     } catch (IOException iOException) {
/* 272 */       iOException.printStackTrace();
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static void zipPack(ZipOutputStream paramZipOutputStream, File paramFile, String paramString) {
/* 282 */     if (paramFile.isDirectory()) {
/*     */       
/* 284 */       packFolder(paramZipOutputStream, paramFile, paramString);
/*     */     } else {
/*     */       
/* 287 */       packFile(paramZipOutputStream, paramFile, paramString);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static void packFolder(ZipOutputStream paramZipOutputStream, File paramFile, String paramString) {
/* 296 */     File[] arrayOfFile = paramFile.listFiles();
/*     */     
/*     */     try {
/* 299 */       paramZipOutputStream.putNextEntry(new ZipEntry(paramString + "" + File.separatorChar));
/* 300 */     } catch (IOException iOException) {
/* 301 */       iOException.printStackTrace();
/*     */     } 
/* 303 */     paramString = (paramString.length() == 0) ? "" : (paramString + "" + File.separatorChar);
/* 304 */     for (File file : arrayOfFile) {
/* 305 */       zipPack(paramZipOutputStream, file, paramString + file.getName());
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static void packFile(ZipOutputStream paramZipOutputStream, File paramFile, String paramString) {
/* 314 */     ZipEntry zipEntry = new ZipEntry(paramString);
/*     */ 
/*     */     
/* 317 */     zipEntry.setSize(paramFile.length());
/* 318 */     zipEntry.setTime(paramFile.lastModified());
/*     */     try {
/* 320 */       paramZipOutputStream.putNextEntry(zipEntry);
/* 321 */     } catch (IOException iOException) {
/* 322 */       iOException.printStackTrace();
/*     */     } 
/* 324 */     FileInputStream fileInputStream = null;
/*     */     try {
/* 326 */       fileInputStream = new FileInputStream(paramFile);
/* 327 */     } catch (FileNotFoundException fileNotFoundException) {
/* 328 */       fileNotFoundException.printStackTrace();
/*     */     } 
/* 330 */     int i = 0;
/*     */     
/*     */     try {
/* 333 */       while ((i = fileInputStream.read(B_ARRAY, 0, BUFFER)) != -1) {
/* 334 */         paramZipOutputStream.write(B_ARRAY, 0, i);
/*     */       }
/* 336 */     } catch (IOException iOException) {
/* 337 */       iOException.printStackTrace();
/* 338 */     } catch (NullPointerException nullPointerException) {
/* 339 */       System.err
/* 340 */         .println("NullPointerException info ======= [FileInputStream is null]");
/*     */     } finally {
/*     */       try {
/* 343 */         if (null != fileInputStream) {
/* 344 */           fileInputStream.close();
/*     */         }
/* 346 */         if (null != paramZipOutputStream) {
/* 347 */           paramZipOutputStream.closeEntry();
/*     */         }
/* 349 */       } catch (IOException iOException) {}
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static void compress(File paramFile) {
/* 359 */     File file = new File(paramFile.getAbsolutePath());
/* 360 */     FileInputStream fileInputStream = null;
/* 361 */     GZIPOutputStream gZIPOutputStream = null;
/*     */     try {
/* 363 */       fileInputStream = new FileInputStream(paramFile);
/* 364 */       gZIPOutputStream = new GZIPOutputStream(new FileOutputStream(file));
/* 365 */       int i = 0;
/* 366 */       while ((i = fileInputStream.read(B_ARRAY, 0, BUFFER)) != -1) {
/* 367 */         gZIPOutputStream.write(B_ARRAY, 0, i);
/*     */       }
/* 369 */     } catch (FileNotFoundException fileNotFoundException) {
/* 370 */       fileNotFoundException.printStackTrace();
/* 371 */     } catch (IOException iOException) {
/* 372 */       iOException.printStackTrace();
/*     */     } finally {
/*     */       try {
/* 375 */         if (fileInputStream != null) {
/* 376 */           fileInputStream.close();
/*     */         }
/*     */         
/* 379 */         if (gZIPOutputStream != null) {
/* 380 */           gZIPOutputStream.close();
/*     */         }
/*     */         
/* 383 */         deleteFile(paramFile.getAbsolutePath());
/* 384 */       } catch (IOException iOException) {
/* 385 */         iOException.printStackTrace();
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static ZipOutputStream getZipOutputStream(String paramString) {
/* 396 */     paramString = paramString.endsWith(".zip") ? paramString : (paramString + ".zip");
/*     */     
/* 398 */     FileOutputStream fileOutputStream = null;
/*     */     try {
/* 400 */       File file = new File(paramString);
/* 401 */       if (!file.getParentFile().exists())
/* 402 */         file.getParentFile().mkdirs(); 
/* 403 */       fileOutputStream = new FileOutputStream(paramString);
/* 404 */     } catch (FileNotFoundException fileNotFoundException) {
/* 405 */       fileNotFoundException.printStackTrace();
/*     */     } 
/* 407 */     BufferedOutputStream bufferedOutputStream = new BufferedOutputStream(fileOutputStream);
/*     */     
/* 409 */     return new ZipOutputStream(bufferedOutputStream);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void deleteFile(String paramString) {
/*     */     try {
/* 424 */       File file = new File(paramString);
/* 425 */       if (file.exists() && file.isDirectory())
/*     */       {
/* 427 */         FileUtils.deleteDirectory(file);
/*     */       }
/* 429 */       else if (file.exists() && file.isFile())
/*     */       {
/* 431 */         file.delete();
/*     */       }
/*     */     
/* 434 */     } catch (Exception exception) {
/*     */       
/* 436 */       exception.printStackTrace();
/* 437 */       (new BaseBean()).writeLog("删除文件失败 : " + exception);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void deleteFile(String paramString, boolean paramBoolean) {
/*     */     try {
/* 449 */       if (paramBoolean)
/*     */       {
/* 451 */         deleteFile(paramString);
/*     */       }
/*     */       else
/*     */       {
/* 455 */         File file = new File(paramString);
/* 456 */         if (file.exists() && file.isDirectory())
/*     */         {
/* 458 */           String[] arrayOfString = file.list();
/* 459 */           if (null != arrayOfString && arrayOfString.length > 0)
/*     */           {
/* 461 */             for (byte b = 0; b < arrayOfString.length; b++)
/*     */             {
/* 463 */               deleteFile(file.getAbsolutePath() + File.separatorChar + arrayOfString[b]);
/*     */             }
/*     */           }
/*     */         }
/*     */       
/*     */       }
/*     */     
/* 470 */     } catch (Exception exception) {
/*     */       
/* 472 */       exception.printStackTrace();
/* 473 */       (new BaseBean()).writeLog("删除文件失败 : " + exception);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean checkZip(String paramString) {
/* 483 */     boolean bool = false;
/* 484 */     File file = new File(paramString);
/* 485 */     if (file.exists())
/*     */     {
/* 487 */       if (file.isFile() && paramString.lastIndexOf(".zip") > -1)
/*     */       {
/* 489 */         bool = true;
/*     */       }
/*     */     }
/* 492 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void main(String[] paramArrayOfString) {
/*     */     try {
/* 501 */       unZip("D://FileUpload.zip", "D://stu");
/*     */     
/*     */     }
/* 504 */     catch (Exception exception) {}
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/wscheck/ZipUtils.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */