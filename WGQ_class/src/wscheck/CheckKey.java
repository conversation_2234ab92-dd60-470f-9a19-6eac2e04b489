/*     */ package wscheck;
/*     */ 
/*     */ import java.io.File;
/*     */ import java.lang.reflect.Method;
/*     */ import java.util.ArrayList;
/*     */ import weaver.general.GCONST;
/*     */ 
/*     */ public class CheckKey
/*     */ {
/*  10 */   private static String ROOTFILEPATH = GCONST.getRootPath();
/*  11 */   private static String SYSUPGRADELOG = ROOTFILEPATH + "sysupgradelog" + File.separatorChar;
/*     */ 
/*     */   
/*     */   public void execute() {
/*  15 */     boolean bool = verifykey();
/*  16 */     if (!bool) {
/*     */       return;
/*     */     }
/*     */     try {
/*  20 */       String str1 = GCONST.getRootPath();
/*  21 */       String str2 = "data" + File.separatorChar + "key" + File.separatorChar + "ecology.key";
/*  22 */       String str3 = str1 + str2;
/*  23 */       File file = new File(str3);
/*  24 */       if (file.exists()) {
/*  25 */         Class<?> clazz = Class.forName("wscheck.CheckScanFile");
/*  26 */         Object object = clazz.newInstance();
/*  27 */         Method method = clazz.getMethod("execute", new Class[0]);
/*  28 */         method.invoke(object, null);
/*     */       } 
/*  30 */     } catch (Exception exception) {
/*  31 */       exception.printStackTrace();
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean verifykey() {
/*  42 */     boolean bool = false;
/*  43 */     String str1 = GCONST.getRootPath();
/*  44 */     String str2 = "data" + File.separatorChar + "key" + File.separatorChar + "ecology.key";
/*  45 */     File file = new File(str1 + str2);
/*  46 */     byte b = -1;
/*  47 */     if (!file.exists()) {
/*  48 */       b = 0;
/*  49 */       bool = false;
/*     */     } else {
/*     */       
/*  52 */       int i = 0;
/*  53 */       int j = 0;
/*  54 */       ArrayList<String> arrayList = CommonUtil.getLineList(file);
/*  55 */       if (arrayList.size() > 1) {
/*  56 */         for (byte b1 = 0; b1 < arrayList.size(); b1++) {
/*  57 */           String str = arrayList.get(b1);
/*  58 */           if (!"".equals(str)) {
/*     */ 
/*     */ 
/*     */             
/*  62 */             str = SecurityHelper.decrypt("weaververify", str);
/*     */             
/*  64 */             if (str != null && !str.equals("")) {
/*  65 */               if (str.indexOf(MD5Coder.stringMD5("createdate")) == 0) {
/*  66 */                 i = b1 + 1;
/*     */               }
/*  68 */               if (str.indexOf(MD5Coder.stringMD5("createversion")) == 0) {
/*  69 */                 j = b1 + 1;
/*     */               }
/*  71 */               if (i > 0 && j > 0) {
/*  72 */                 bool = true; break;
/*     */               } 
/*     */             } 
/*     */           } 
/*     */         } 
/*  77 */         if (!bool) {
/*  78 */           b = 1;
/*     */         }
/*     */       } else {
/*  81 */         b = 1;
/*  82 */         bool = false;
/*     */       } 
/*     */     } 
/*     */     
/*  86 */     if (!bool) {
/*  87 */       keyerror(b);
/*     */     }
/*  89 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void keyerror(int paramInt) {
/*     */     try {
/*  99 */       String str1 = GCONST.getRootPath();
/* 100 */       String str2 = "data" + File.separatorChar + "key" + File.separatorChar + "ecology.key";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     }
/* 127 */     catch (Exception exception) {}
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void checkKeyFile() {
/* 136 */     boolean bool = false;
/* 137 */     boolean bool1 = verifykey();
/* 138 */     if (!bool1) {
/*     */       return;
/*     */     }
/* 141 */     String str1 = GCONST.getRootPath();
/* 142 */     String str2 = "data" + File.separatorChar + "key" + File.separatorChar + "ecology.key";
/* 143 */     String str3 = str1 + str2;
/* 144 */     File file = new File(str3);
/* 145 */     if (file.exists()) {
/* 146 */       ArrayList arrayList = CommonUtil.getLineList(file);
/* 147 */       bool = (arrayList.size() == 5) ? true : false;
/*     */     } 
/*     */     
/* 150 */     if (bool) {
/* 151 */       String str4 = GCONST.getRootPath();
/* 152 */       String str5 = "keyupgrade";
/* 153 */       String str6 = str4 + str5;
/*     */       
/* 155 */       File file1 = new File(str6);
/* 156 */       if (file1.exists()) {
/* 157 */         File[] arrayOfFile = file1.listFiles();
/* 158 */         for (byte b = 0; arrayOfFile != null && b < arrayOfFile.length; b++) {
/* 159 */           if (arrayOfFile[b].getPath().lastIndexOf(".key") > -1)
/*     */             try {
/* 161 */               ZipUtils.deleteFile(arrayOfFile[b].getAbsolutePath());
/* 162 */             } catch (Exception exception) {} 
/*     */         } 
/*     */       } 
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/wscheck/CheckKey.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */