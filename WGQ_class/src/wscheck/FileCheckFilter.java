/*     */ package wscheck;
/*     */ 
/*     */ import com.weaver.function.ConfigInfo;
/*     */ import com.weaver.update.ClusterUpgradeInfo;
/*     */ import java.io.File;
/*     */ import java.io.IOException;
/*     */ import java.io.PrintWriter;
/*     */ import java.util.Date;
/*     */ import java.util.HashSet;
/*     */ import java.util.Iterator;
/*     */ import java.util.List;
/*     */ import java.util.Properties;
/*     */ import javax.servlet.Filter;
/*     */ import javax.servlet.FilterChain;
/*     */ import javax.servlet.FilterConfig;
/*     */ import javax.servlet.ServletContext;
/*     */ import javax.servlet.ServletException;
/*     */ import javax.servlet.ServletRequest;
/*     */ import javax.servlet.ServletResponse;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import org.json.JSONException;
/*     */ import org.json.JSONObject;
/*     */ import weaver.common.util.string.StringUtil;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.system.GetPackageWork;
/*     */ import weaver.system.SysUpgradeCominfo;
/*     */ import weaver.system.SystemUpgradeUtil;
/*     */ 
/*     */ public class FileCheckFilter
/*     */   extends BaseBean implements Filter {
/*  35 */   private static String ischeck = "";
/*  36 */   private String uncheckSessionUrl = "";
/*     */   private boolean doUpgradeConfirm = false;
/*  38 */   private static Properties prop = new Properties();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void doFilter(ServletRequest paramServletRequest, ServletResponse paramServletResponse, FilterChain paramFilterChain) throws IOException, ServletException {
/*  47 */     if (paramServletRequest instanceof HttpServletRequest) {
/*     */       
/*  49 */       JSONObject jSONObject = new JSONObject();
/*     */       
/*  51 */       HttpServletRequest httpServletRequest = (HttpServletRequest)paramServletRequest;
/*  52 */       HttpServletResponse httpServletResponse = (HttpServletResponse)paramServletResponse;
/*  53 */       String str1 = httpServletRequest.getRequestURI();
/*     */       
/*  55 */       int i = SysUpgradeCominfo.upgradetype;
/*  56 */       int j = SysUpgradeCominfo.status;
/*  57 */       int k = SysUpgradeCominfo.pStatus;
/*  58 */       String str2 = SysUpgradeCominfo.checkVersionResult;
/*     */ 
/*     */       
/*  61 */       boolean bool = SysUpgradeCominfo.hasUpdateClusterNode;
/*  62 */       if (!bool) {
/*     */         
/*  64 */         boolean bool1 = checkUrl(str1);
/*  65 */         if (!bool1) {
/*     */           
/*     */           try {
/*  68 */             if (str1.indexOf(".jsp") > -1 || str1.indexOf(".html") > -1) {
/*  69 */               setNoCacheHeader(httpServletResponse);
/*  70 */               httpServletResponse.sendRedirect(Util.null2String(SystemUpgradeUtil.secondarydirname) + "/login/UpgradeMessage.jsp?error=nodeNotUpdate&errortag=1");
/*     */             } else {
/*  72 */               jSONObject.put("status", "false");
/*  73 */               jSONObject.put("showType", "url");
/*  74 */               jSONObject.put("url", Util.null2String(SystemUpgradeUtil.secondarydirname) + "/login/UpgradeMessage.jsp?error=nodeNotUpdate&errortag=1");
/*     */               
/*  76 */               httpServletResponse.getWriter().print(jSONObject.toString());
/*  77 */               httpServletResponse.getWriter().flush();
/*     */             }
/*     */           
/*  80 */           } catch (JSONException jSONException) {
/*  81 */             jSONException.printStackTrace();
/*     */           } 
/*     */           
/*     */           return;
/*     */         } 
/*     */       } 
/*     */       
/*  88 */       if (i == 2) {
/*     */         
/*  90 */         boolean bool1 = checkUrl(str1);
/*  91 */         if (!bool1) {
/*     */           
/*     */           try {
/*  94 */             if (str1.indexOf(".jsp") > -1 || str1.indexOf(".html") > -1) {
/*  95 */               setNoCacheHeader(httpServletResponse);
/*  96 */               httpServletResponse.sendRedirect(Util.null2String(SystemUpgradeUtil.secondarydirname) + "/login/UpgradeMessage.jsp?error=monitordoUpgrade");
/*     */             } else {
/*  98 */               jSONObject.put("status", "false");
/*  99 */               jSONObject.put("showType", "url");
/* 100 */               jSONObject.put("url", Util.null2String(SystemUpgradeUtil.secondarydirname) + "/login/UpgradeMessage.jsp?error=monitordoUpgrade");
/*     */               
/* 102 */               httpServletResponse.getWriter().print(jSONObject.toString());
/* 103 */               httpServletResponse.getWriter().flush();
/*     */             }
/*     */           
/* 106 */           } catch (JSONException jSONException) {
/* 107 */             jSONException.printStackTrace();
/*     */           } 
/*     */           
/*     */           return;
/*     */         } 
/*     */       } 
/*     */       
/* 114 */       if (i == 0 && !SysUpgradeCominfo.checkClusterMain) {
/*     */         
/*     */         try {
/* 117 */           ClusterUpgradeInfo clusterUpgradeInfo = new ClusterUpgradeInfo();
/* 118 */           String str = clusterUpgradeInfo.getClusterUpgradeStatus();
/* 119 */           if ("2".equals(str)) {
/*     */             
/* 121 */             boolean bool1 = checkUrl(str1);
/* 122 */             if (!bool1) {
/*     */               
/*     */               try {
/* 125 */                 if (str1.indexOf(".jsp") > -1 || str1.indexOf(".html") > -1) {
/* 126 */                   setNoCacheHeader(httpServletResponse);
/* 127 */                   httpServletResponse.sendRedirect(Util.null2String(SystemUpgradeUtil.secondarydirname) + "/login/UpgradeMessage.jsp?error=nodeNotUpdate&errortag=2");
/*     */                 } else {
/* 129 */                   jSONObject.put("status", "false");
/* 130 */                   jSONObject.put("showType", "url");
/* 131 */                   jSONObject.put("url", Util.null2String(SystemUpgradeUtil.secondarydirname) + "/login/UpgradeMessage.jsp?error=nodeNotUpdate&errortag=2");
/*     */                   
/* 133 */                   httpServletResponse.getWriter().print(jSONObject.toString());
/* 134 */                   httpServletResponse.getWriter().flush();
/*     */                 }
/*     */               
/* 137 */               } catch (JSONException jSONException) {
/* 138 */                 jSONException.printStackTrace();
/*     */               } 
/*     */               return;
/*     */             } 
/*     */           } 
/* 143 */         } catch (Exception exception) {
/* 144 */           exception.printStackTrace();
/*     */         } 
/*     */       }
/*     */ 
/*     */ 
/*     */       
/* 150 */       if (str2 != null && !"".equals(str2)) {
/*     */         try {
/* 152 */           boolean bool1 = checkUrl(str1);
/* 153 */           if (!bool1) {
/* 154 */             if (str1.indexOf(".jsp") > -1 || str1.indexOf(".html") > -1) {
/* 155 */               if ("RestoreError".equalsIgnoreCase(str2)) {
/* 156 */                 setNoCacheHeader(httpServletResponse);
/* 157 */                 httpServletResponse.sendRedirect(Util.null2String(SystemUpgradeUtil.secondarydirname) + "/login/UpgradeMessage.jsp?error=restoreError");
/*     */               } else {
/* 159 */                 setNoCacheHeader(httpServletResponse);
/* 160 */                 httpServletResponse.sendRedirect(Util.null2String(SystemUpgradeUtil.secondarydirname) + "/login/UpgradeMessage.jsp?error=versionError&errorMsg=" + str2);
/*     */               } 
/*     */             } else {
/*     */               
/* 164 */               jSONObject.put("status", "false");
/* 165 */               jSONObject.put("showType", "url");
/* 166 */               if ("RestoreError".equalsIgnoreCase(str2)) {
/* 167 */                 jSONObject.put("url", Util.null2String(SystemUpgradeUtil.secondarydirname) + "/login/UpgradeMessage.jsp?error=restoreError");
/*     */               } else {
/* 169 */                 jSONObject.put("url", Util.null2String(SystemUpgradeUtil.secondarydirname) + "/login/UpgradeMessage.jsp?error=versionError&errorMsg=" + str2);
/*     */               } 
/*     */               
/* 172 */               httpServletResponse.getWriter().print(jSONObject.toString());
/* 173 */               httpServletResponse.getWriter().flush();
/*     */             } 
/*     */             
/*     */             return;
/*     */           } 
/* 178 */         } catch (JSONException jSONException) {
/* 179 */           jSONException.printStackTrace();
/*     */         } 
/*     */       }
/*     */ 
/*     */       
/* 184 */       if (j != 0 || k == 3 || k == 1 || k == 2) {
/*     */ 
/*     */         
/* 187 */         if (isLoginUrl(str1)) {
/*     */           
/* 189 */           if (i == 0) {
/* 190 */             setNoCacheHeader(httpServletResponse);
/* 191 */             httpServletResponse.sendRedirect(Util.null2String(SystemUpgradeUtil.secondarydirname) + "/login/Upgrade.jsp?date=" + (new Date()).getTime());
/*     */ 
/*     */ 
/*     */             
/*     */             return;
/*     */           } 
/*     */         } else {
/* 198 */           User user = (User)httpServletRequest.getSession(true).getAttribute("weaver_user@bean");
/* 199 */           if (user != null) {
/* 200 */             int m = user.getUID();
/* 201 */             if (!ConfigInfo.checkIsUpdateUser(user.getUID())) {
/*     */               
/* 203 */               Object object = httpServletRequest.getSession(true).getAttribute("weaver_uadminLogin");
/* 204 */               if (object != null) {
/* 205 */                 if (i == 0) {
/* 206 */                   boolean bool1 = checkUrl(str1);
/* 207 */                   if (!bool1) {
/*     */                     
/*     */                     try {
/* 210 */                       httpServletRequest.getSession(true).removeAttribute("weaver_user@bean");
/* 211 */                       if (str1.indexOf(".jsp") > -1 || str1.indexOf(".html") > -1) {
/* 212 */                         setNoCacheHeader(httpServletResponse);
/* 213 */                         httpServletResponse.sendRedirect(Util.null2String(SystemUpgradeUtil.secondarydirname) + "/login/Upgrade.jsp?date=" + (new Date()).getTime());
/*     */                       } else {
/* 215 */                         jSONObject.put("status", "false");
/* 216 */                         jSONObject.put("showType", "url");
/* 217 */                         jSONObject.put("url", Util.null2String(SystemUpgradeUtil.secondarydirname) + "/login/Upgrade.jsp?date=" + (new Date()).getTime());
/*     */                         
/* 219 */                         httpServletResponse.getWriter().print(jSONObject.toString());
/* 220 */                         httpServletResponse.getWriter().flush();
/*     */                       }
/*     */                     
/* 223 */                     } catch (JSONException jSONException) {
/* 224 */                       jSONException.printStackTrace();
/*     */                     } 
/*     */                     
/*     */                     return;
/*     */                   } 
/*     */                 } 
/*     */               } else {
/* 231 */                 boolean bool1 = checkUrl(str1);
/* 232 */                 if (!bool1) {
/* 233 */                   if (i == 0) {
/*     */                     try {
/* 235 */                       if (str1.indexOf(".jsp") > -1 || str1.indexOf(".html") > -1) {
/* 236 */                         setNoCacheHeader(httpServletResponse);
/* 237 */                         httpServletResponse.sendRedirect(Util.null2String(SystemUpgradeUtil.secondarydirname) + "/login/UpgradeMessage.jsp?error=upgradeMessage");
/*     */                       } else {
/* 239 */                         httpServletRequest.getSession(true).removeAttribute("weaver_user@bean");
/* 240 */                         jSONObject.put("status", "false");
/* 241 */                         jSONObject.put("showType", "url");
/* 242 */                         jSONObject.put("url", Util.null2String(SystemUpgradeUtil.secondarydirname) + "/login/UpgradeMessage.jsp?error=upgradeMessage");
/*     */                         
/* 244 */                         httpServletResponse.getWriter().print(jSONObject.toString());
/* 245 */                         httpServletResponse.getWriter().flush();
/*     */                       }
/*     */                     
/* 248 */                     } catch (JSONException jSONException) {
/* 249 */                       jSONException.printStackTrace();
/*     */                     } 
/*     */                   } else {
/*     */ 
/*     */                     
/*     */                     try {
/*     */                       
/* 256 */                       httpServletRequest.getSession(true).removeAttribute("weaver_user@bean");
/* 257 */                       if (str1.indexOf(".jsp") > -1 || str1.indexOf(".html") > -1) {
/* 258 */                         setNoCacheHeader(httpServletResponse);
/* 259 */                         httpServletResponse.sendRedirect(Util.null2String(SystemUpgradeUtil.secondarydirname) + "/login/UpgradeMessage.jsp?error=doUpgrade");
/*     */                       } else {
/* 261 */                         jSONObject.put("status", "false");
/* 262 */                         jSONObject.put("showType", "url");
/* 263 */                         jSONObject.put("url", Util.null2String(SystemUpgradeUtil.secondarydirname) + "/login/UpgradeMessage.jsp?error=doUpgrade");
/*     */                         
/* 265 */                         httpServletResponse.getWriter().print(jSONObject.toString());
/* 266 */                         httpServletResponse.getWriter().flush();
/*     */                       }
/*     */                     
/* 269 */                     } catch (JSONException jSONException) {
/* 270 */                       jSONException.printStackTrace();
/*     */                     } 
/*     */                   } 
/*     */ 
/*     */                   
/*     */                   return;
/*     */                 } 
/*     */               } 
/* 278 */             } else if (i == 0) {
/* 279 */               boolean bool1 = checkUrl(str1);
/* 280 */               if (!bool1) {
/*     */                 
/*     */                 try {
/* 283 */                   httpServletRequest.getSession(true).removeAttribute("weaver_user@bean");
/* 284 */                   if (str1.indexOf(".jsp") > -1 || str1.indexOf(".html") > -1) {
/* 285 */                     setNoCacheHeader(httpServletResponse);
/* 286 */                     httpServletResponse.sendRedirect(Util.null2String(SystemUpgradeUtil.secondarydirname) + "/login/Upgrade.jsp?date=" + (new Date()).getTime());
/*     */                   } else {
/* 288 */                     jSONObject.put("status", "false");
/* 289 */                     jSONObject.put("showType", "url");
/* 290 */                     jSONObject.put("url", Util.null2String(SystemUpgradeUtil.secondarydirname) + "/login/Upgrade.jsp?date=" + (new Date()).getTime());
/*     */                     
/* 292 */                     httpServletResponse.getWriter().print(jSONObject.toString());
/* 293 */                     httpServletResponse.getWriter().flush();
/*     */                   }
/*     */                 
/* 296 */                 } catch (JSONException jSONException) {
/* 297 */                   jSONException.printStackTrace();
/*     */                 } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */                 
/*     */                 return;
/*     */               } 
/*     */             } 
/*     */           } 
/*     */         } 
/*     */       } else {
/* 310 */         Object object = httpServletRequest.getSession(true).getAttribute("weaver_uadminLogin");
/* 311 */         if (object != null && 
/* 312 */           SysUpgradeCominfo.getUpgradeFinished()) {
/* 313 */           httpServletRequest.getSession(true).removeAttribute("weaver_uadminLogin");
/*     */         }
/*     */         
/* 316 */         User user = (User)httpServletRequest.getSession(true).getAttribute("weaver_user@bean");
/*     */         
/* 318 */         if (!"1".equals(SysUpgradeCominfo.hasUpgradeConfirm)) {
/*     */           
/* 320 */           str1 = httpServletRequest.getRequestURI();
/* 321 */           if (user != null) {
/* 322 */             boolean bool1 = false;
/* 323 */             str1 = httpServletRequest.getRequestURI();
/* 324 */             if (SysUpgradeCominfo.doUpgradeConfirmAllUser) {
/* 325 */               bool1 = true;
/*     */             } else {
/* 327 */               List list = SysUpgradeCominfo.isUpdateUserList;
/* 328 */               if (user.isAdmin() || list.contains(user.getUID() + "")) {
/* 329 */                 bool1 = true;
/*     */               }
/*     */             } 
/* 332 */             if (bool1) {
/* 333 */               User user1 = (User)httpServletRequest.getSession(true).getAttribute("weaver_upgradeConfirm");
/*     */               
/*     */               try {
/* 336 */                 if (!isLoginUrl(str1)) {
/*     */ 
/*     */                   
/* 339 */                   ClusterUpgradeInfo clusterUpgradeInfo = new ClusterUpgradeInfo();
/* 340 */                   String str = clusterUpgradeInfo.getClusterUpgradeConfirm();
/* 341 */                   if ("1".equals(str))
/*     */                   {
/* 343 */                     SysUpgradeCominfo.hasUpgradeConfirm = "1";
/*     */                   }
/* 345 */                   boolean bool2 = checkUrl(str1);
/*     */ 
/*     */                   
/* 348 */                   if (!bool2 && !"1".equals(str)) {
/* 349 */                     jSONObject = new JSONObject();
/* 350 */                     if (user1 == null) {
/*     */ 
/*     */                       
/* 353 */                       if (str1.indexOf(".jsp") > -1 || str1.indexOf(".html") > -1) {
/* 354 */                         setNoCacheHeader(httpServletResponse);
/* 355 */                         httpServletResponse.sendRedirect(Util.null2String(SystemUpgradeUtil.secondarydirname) + "/login/upgradeCheck.jsp?operationtype=upgradeendecomfirm&date=" + (new Date()).getTime());
/*     */                         
/*     */                         return;
/*     */                       } 
/* 359 */                       PrintWriter printWriter = httpServletResponse.getWriter();
/* 360 */                       jSONObject.put("status", "false");
/* 361 */                       jSONObject.put("showType", "url");
/* 362 */                       jSONObject.put("url", Util.null2String(SystemUpgradeUtil.secondarydirname) + "/login/upgradeCheck.jsp?operationtype=upgradeendecomfirm&date=" + (new Date()).getTime());
/* 363 */                       printWriter.print(jSONObject.toString());
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */                       
/*     */                       return;
/*     */                     } 
/*     */                   } 
/* 374 */                 } else if (user1 != null && 
/* 375 */                   str1.indexOf("upgradeCheck.jsp") <= -1) {
/*     */ 
/*     */                   
/* 378 */                   httpServletRequest.getSession(true).removeAttribute("weaver_upgradeConfirm");
/* 379 */                   httpServletRequest.getSession(true).removeAttribute("weaver_bakConfirm");
/*     */                 
/*     */                 }
/*     */               
/*     */               }
/* 384 */               catch (Exception exception) {
/* 385 */                 exception.printStackTrace();
/*     */               } 
/*     */             } 
/*     */           } 
/*     */         } else {
/*     */ 
/*     */           
/*     */           try {
/*     */             
/* 394 */             if (!SystemUpgradeUtil.getFileBakIsSet()) {
/*     */               
/* 396 */               str1 = httpServletRequest.getRequestURI();
/* 397 */               if (user != null) {
/* 398 */                 boolean bool1 = false;
/* 399 */                 str1 = httpServletRequest.getRequestURI();
/* 400 */                 if (SysUpgradeCominfo.doUpgradeConfirmAllUser) {
/* 401 */                   bool1 = true;
/*     */                 } else {
/* 403 */                   List list = SysUpgradeCominfo.isUpdateUserList;
/* 404 */                   if (list.contains(user.getUID() + "")) {
/* 405 */                     bool1 = true;
/*     */                   }
/*     */                 } 
/* 408 */                 if (bool1 && 
/* 409 */                   !isLoginUrl(str1)) {
/* 410 */                   boolean bool2 = checkUrl(str1);
/*     */ 
/*     */                   
/* 413 */                   if (!bool2) {
/*     */                     
/* 415 */                     String str = Util.null2String(httpServletRequest.getSession(true).getAttribute("weaver_bakConfirm")).trim();
/* 416 */                     if (!str.equals("iscomfirm")) {
/* 417 */                       if (str1.indexOf(".jsp") > -1 || str1.indexOf(".html") > -1) {
/* 418 */                         setNoCacheHeader(httpServletResponse);
/* 419 */                         httpServletResponse.sendRedirect(Util.null2String(SystemUpgradeUtil.secondarydirname) + "/login/upgradeCheck.jsp?operationtype=upgradeendecomfirm&filebakcheck=1&date=" + (new Date()).getTime());
/*     */                         return;
/*     */                       } 
/* 422 */                       jSONObject = new JSONObject();
/* 423 */                       PrintWriter printWriter = httpServletResponse.getWriter();
/* 424 */                       jSONObject.put("status", "false");
/* 425 */                       jSONObject.put("showType", "url");
/* 426 */                       jSONObject.put("url", Util.null2String(SystemUpgradeUtil.secondarydirname) + "/login/upgradeCheck.jsp?operationtype=upgradeendecomfirm&filebakcheck=1&date=" + (new Date()).getTime());
/* 427 */                       printWriter.print(jSONObject.toString());
/*     */ 
/*     */ 
/*     */                       
/*     */                       return;
/*     */                     } 
/*     */                   } 
/*     */                 } 
/*     */               } 
/*     */             } 
/* 437 */           } catch (Exception exception) {
/* 438 */             exception.printStackTrace();
/*     */           } 
/*     */         } 
/*     */       } 
/*     */       
/* 443 */       if ("".equals(ischeck)) {
/*     */         
/* 445 */         ischeck = Util.null2String((new BaseBean()).getPropValue("FileCheck", "ischeck")).trim();
/* 446 */         ischeck = ischeck.equals("") ? "0" : ischeck;
/*     */       } 
/*     */       
/* 449 */       if (!"0".equals(ischeck))
/* 450 */         CheckPage.check(httpServletRequest, httpServletResponse); 
/* 451 */       paramFilterChain.doFilter(paramServletRequest, paramServletResponse);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void setNoCacheHeader(HttpServletResponse paramHttpServletResponse) {
/* 460 */     paramHttpServletResponse.setHeader("Cache-Control", "no-cache");
/* 461 */     paramHttpServletResponse.setHeader("Pragma", "no-cache");
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void init(FilterConfig paramFilterConfig) {
/* 467 */     boolean bool = InitCheck.isIsinit();
/* 468 */     if (!bool) {
/*     */       
/* 470 */       if ("".equals(Util.null2String(GCONST.getRootPath()))) {
/*     */         
/* 472 */         ServletContext servletContext = paramFilterConfig.getServletContext();
/* 473 */         String str = servletContext.getRealPath("/");
/* 474 */         if (!str.endsWith("" + File.separatorChar))
/* 475 */           str = str + File.separatorChar; 
/* 476 */         GCONST.setRootPath(str);
/*     */       } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 484 */       GetPackageWork getPackageWork = new GetPackageWork();
/* 485 */       getPackageWork.runTimer();
/*     */       
/* 487 */       BaseBean baseBean = new BaseBean();
/* 488 */       String str1 = baseBean.getPropValue("weaver_session_filter", "unchecksessionurl");
/* 489 */       String str2 = baseBean.getPropValue("weaver_session_filter_dev", "unchecksessionurl");
/* 490 */       this.uncheckSessionUrl += str1 + str2;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void destroy() {}
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean checkUrl(String paramString) {
/* 504 */     HashSet<String> hashSet = SysUpgradeCominfo.getExcludeset();
/* 505 */     hashSet.add("/upgrade");
/*     */     
/* 507 */     boolean bool = false;
/* 508 */     Iterator<String> iterator = hashSet.iterator();
/* 509 */     while (iterator.hasNext()) {
/* 510 */       String str = iterator.next();
/* 511 */       if (paramString.toLowerCase().indexOf(str.toLowerCase()) > -1) {
/* 512 */         bool = true;
/*     */         break;
/*     */       } 
/*     */     } 
/* 516 */     if (!bool) {
/* 517 */       String[] arrayOfString = this.uncheckSessionUrl.split(";");
/* 518 */       return checkUrl(paramString, arrayOfString);
/*     */     } 
/* 520 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isLoginUrl(String paramString) {
/* 530 */     if (paramString.indexOf("/Login.jsp") > 0 || paramString.indexOf("/login.jsp") > 0 || paramString.indexOf("/wui/index.html") >= 0 || paramString.indexOf("/wui/index.jsp") >= 0) {
/* 531 */       return true;
/*     */     }
/* 533 */     return false;
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean checkUrl(String paramString, String[] paramArrayOfString) {
/* 538 */     if (paramArrayOfString == null) {
/* 539 */       return true;
/*     */     }
/* 541 */     for (String str : paramArrayOfString) {
/* 542 */       if (StringUtil.isNotNullAndEmpty(str) && paramString.startsWith(str)) {
/* 543 */         return true;
/*     */       }
/*     */     } 
/* 546 */     return false;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/wscheck/FileCheckFilter.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */