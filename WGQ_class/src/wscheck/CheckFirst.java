/*    */ package wscheck;
/*    */ 
/*    */ import java.lang.reflect.Method;
/*    */ 
/*    */ public class CheckFirst {
/*    */   public static void execute() {
/*    */     try {
/*  8 */       Class<?> clazz1 = Class.forName("wscheck.PageMD5Map");
/*  9 */       Object object1 = clazz1.newInstance();
/* 10 */       Method method1 = clazz1.getMethod("initMD5Map", new Class[0]);
/* 11 */       method1.invoke(object1, null);
/*    */       
/* 13 */       Class<?> clazz2 = Class.forName("wscheck.CheckKey");
/* 14 */       Object object2 = clazz2.newInstance();
/* 15 */       Method method2 = clazz2.getMethod("execute", new Class[0]);
/* 16 */       method2.invoke(object2, null);
/*    */ 
/*    */       
/* 19 */       Class<?> clazz3 = Class.forName("wscheck.KeyUpgrade");
/* 20 */       Object object3 = clazz3.newInstance();
/* 21 */       Method method3 = clazz3.getMethod("execute", new Class[0]);
/* 22 */       method3.invoke(object3, null);
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */     
/*    */     }
/* 32 */     catch (Exception exception) {
/* 33 */       exception.printStackTrace();
/*    */     } 
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/wscheck/CheckFirst.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */