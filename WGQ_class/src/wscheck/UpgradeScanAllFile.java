/*     */ package wscheck;
/*     */ 
/*     */ import java.io.File;
/*     */ import java.util.HashMap;
/*     */ import java.util.Map;
/*     */ import weaver.general.GCONST;
/*     */ 
/*     */ 
/*     */ public class UpgradeScanAllFile
/*     */ {
/*  11 */   Map md5map = new HashMap<>();
/*     */ 
/*     */ 
/*     */   
/*  15 */   public String scanfiletypesE9 = ".jsp|.java|.class|.js|.css|.jar|.less|.json|.md|.gitignore";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void getFileMap(File paramFile, String paramString) {
/*  24 */     File[] arrayOfFile = paramFile.listFiles();
/*  25 */     boolean bool = false;
/*     */     try {
/*  27 */       for (byte b = 0; arrayOfFile != null && b < arrayOfFile.length; b++) {
/*  28 */         String str1 = arrayOfFile[b].getPath();
/*  29 */         String str2 = str1.substring(paramString.length());
/*     */         
/*  31 */         if (str2.indexOf("" + File.separatorChar) == -1 || str2.indexOf("" + File.separatorChar) > 0) {
/*  32 */           str2 = "" + File.separatorChar + str2;
/*     */         }
/*     */         
/*  35 */         str2 = str2.replaceAll("\\\\", "/");
/*  36 */         if (arrayOfFile[b].isFile()) {
/*  37 */           bool = false;
/*     */ 
/*     */ 
/*     */           
/*  41 */           String str3 = arrayOfFile[b].getAbsolutePath();
/*  42 */           String str4 = GCONST.getRootPath();
/*  43 */           if (str4.endsWith(File.separator)) {
/*  44 */             str4 = str4 + "WEB-INF" + File.separator;
/*     */           } else {
/*  46 */             str4 = str4 + File.separator + "WEB-INF" + File.separator;
/*     */           } 
/*  48 */           if (str3.contains(str4)) {
/*  49 */             String str = str3.substring(str4.length());
/*     */             
/*  51 */             if (!str.contains(File.separator)) {
/*     */               continue;
/*     */             }
/*     */           } 
/*     */ 
/*     */           
/*  57 */           boolean bool1 = rightFileType(str1, str2).booleanValue();
/*  58 */           if (bool1) {
/*  59 */             if (str2.endsWith("map")) {
/*     */               continue;
/*     */             }
/*  62 */             String str5 = str2.substring(0, str2.lastIndexOf(".")).toLowerCase();
/*  63 */             if (str5.endsWith("bak") || str5.contains("副本") || str5.contains("复制") || str5.contains("复件") || str5.contains("备份") || str5.indexOf(")") > 0 || str5.indexOf("(") > 0) {
/*     */               continue;
/*     */             }
/*  66 */             String str6 = getMD5(arrayOfFile[b]);
/*  67 */             this.md5map.put(str2, str6);
/*     */           } 
/*     */         } 
/*     */         
/*  71 */         if (arrayOfFile[b].isDirectory()) {
/*  72 */           String str = str2 + "/";
/*  73 */           getFileMap(arrayOfFile[b], paramString);
/*     */         }  continue;
/*     */       } 
/*  76 */     } catch (Exception exception) {
/*  77 */       exception.printStackTrace();
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getMD5(File paramFile) {
/*  89 */     String str = "";
/*     */     try {
/*  91 */       str = MD5Coder.fileMD5(paramFile);
/*  92 */     } catch (Exception exception) {
/*  93 */       exception.printStackTrace();
/*  94 */       str = "";
/*     */     } 
/*     */     
/*  97 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Boolean rightFileType(String paramString1, String paramString2) {
/* 108 */     String[] arrayOfString = this.scanfiletypesE9.split("\\|");
/* 109 */     for (String str : arrayOfString) {
/* 110 */       if (paramString1.endsWith(str.trim())) {
/* 111 */         return Boolean.valueOf(true);
/*     */       }
/*     */     } 
/* 114 */     return Boolean.valueOf(false);
/*     */   }
/*     */   
/*     */   public Map getMd5map() {
/* 118 */     return this.md5map;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/wscheck/UpgradeScanAllFile.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */