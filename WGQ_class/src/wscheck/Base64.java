/*     */ package wscheck;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public final class Base64
/*     */ {
/*     */   private static final int BASELENGTH = 255;
/*     */   private static final int LOOKUPLENGTH = 64;
/*     */   private static final int TWENTYFOURBITGROUP = 24;
/*     */   private static final int EIGHTBIT = 8;
/*     */   private static final int SIXTEENBIT = 16;
/*     */   private static final int FOURBYTE = 4;
/*     */   private static final int SIGN = -128;
/*     */   private static final byte PAD = 61;
/*  21 */   private static byte[] base64Alphabet = new byte[255];
/*     */   
/*  23 */   private static byte[] lookUpBase64Alphabet = new byte[64];
/*     */   static {
/*     */     byte b1;
/*  26 */     for (b1 = 0; b1 < 'ÿ'; b1++) {
/*  27 */       base64Alphabet[b1] = -1;
/*     */     }
/*  29 */     for (b1 = 90; b1 >= 65; b1--) {
/*  30 */       base64Alphabet[b1] = (byte)(b1 - 65);
/*     */     }
/*  32 */     for (b1 = 122; b1 >= 97; b1--) {
/*  33 */       base64Alphabet[b1] = (byte)(b1 - 97 + 26);
/*     */     }
/*  35 */     for (b1 = 57; b1 >= 48; b1--) {
/*  36 */       base64Alphabet[b1] = (byte)(b1 - 48 + 52);
/*     */     }
/*     */     
/*  39 */     base64Alphabet[43] = 62;
/*  40 */     base64Alphabet[47] = 63;
/*     */     
/*  42 */     for (b1 = 0; b1 <= 25; b1++)
/*  43 */       lookUpBase64Alphabet[b1] = (byte)(65 + b1); 
/*     */     byte b2;
/*  45 */     for (b1 = 26, b2 = 0; b1 <= 51; b1++, b2++) {
/*  46 */       lookUpBase64Alphabet[b1] = (byte)(97 + b2);
/*     */     }
/*  48 */     for (b1 = 52, b2 = 0; b1 <= 61; b1++, b2++) {
/*  49 */       lookUpBase64Alphabet[b1] = (byte)(48 + b2);
/*     */     }
/*  51 */     lookUpBase64Alphabet[62] = 43;
/*  52 */     lookUpBase64Alphabet[63] = 47;
/*     */   }
/*     */   
/*     */   public static boolean isBase64(String paramString) {
/*  56 */     return isArrayByteBase64(paramString.getBytes());
/*     */   }
/*     */ 
/*     */   
/*     */   public static boolean isBase64(byte paramByte) {
/*  61 */     return (paramByte == 61 || base64Alphabet[paramByte] != -1);
/*     */   }
/*     */   
/*     */   public static boolean isArrayByteBase64(byte[] paramArrayOfbyte) {
/*  65 */     int i = paramArrayOfbyte.length;
/*  66 */     if (i == 0)
/*     */     {
/*     */       
/*  69 */       return true;
/*     */     }
/*  71 */     for (byte b = 0; b < i; b++) {
/*  72 */       if (!isBase64(paramArrayOfbyte[b]))
/*  73 */         return false; 
/*     */     } 
/*  75 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static byte[] encode(byte[] paramArrayOfbyte) {
/*  86 */     int i = paramArrayOfbyte.length * 8;
/*  87 */     int j = i % 24;
/*  88 */     int k = i / 24;
/*  89 */     byte[] arrayOfByte = null;
/*     */     
/*  91 */     if (j != 0) {
/*     */       
/*  93 */       arrayOfByte = new byte[(k + 1) * 4];
/*     */     } else {
/*     */       
/*  96 */       arrayOfByte = new byte[k * 4];
/*     */     } 
/*     */     
/*  99 */     byte b1 = 0, b2 = 0, b3 = 0, b4 = 0, b5 = 0;
/*     */     
/* 101 */     int m = 0;
/* 102 */     int n = 0;
/* 103 */     byte b = 0;
/*     */     
/* 105 */     for (b = 0; b < k; b++) {
/* 106 */       n = b * 3;
/* 107 */       b3 = paramArrayOfbyte[n];
/* 108 */       b4 = paramArrayOfbyte[n + 1];
/* 109 */       b5 = paramArrayOfbyte[n + 2];
/*     */ 
/*     */ 
/*     */       
/* 113 */       b2 = (byte)(b4 & 0xF);
/* 114 */       b1 = (byte)(b3 & 0x3);
/*     */       
/* 116 */       m = b * 4;
/* 117 */       byte b6 = ((b3 & 0xFFFFFF80) == 0) ? (byte)(b3 >> 2) : (byte)(b3 >> 2 ^ 0xC0);
/* 118 */       byte b7 = ((b4 & 0xFFFFFF80) == 0) ? (byte)(b4 >> 4) : (byte)(b4 >> 4 ^ 0xF0);
/* 119 */       byte b8 = ((b5 & 0xFFFFFF80) == 0) ? (byte)(b5 >> 6) : (byte)(b5 >> 6 ^ 0xFC);
/*     */       
/* 121 */       arrayOfByte[m] = lookUpBase64Alphabet[b6];
/*     */ 
/*     */ 
/*     */       
/* 125 */       arrayOfByte[m + 1] = lookUpBase64Alphabet[b7 | b1 << 4];
/* 126 */       arrayOfByte[m + 2] = lookUpBase64Alphabet[b2 << 2 | b8];
/* 127 */       arrayOfByte[m + 3] = lookUpBase64Alphabet[b5 & 0x3F];
/*     */     } 
/*     */ 
/*     */     
/* 131 */     n = b * 3;
/* 132 */     m = b * 4;
/* 133 */     if (j == 8) {
/* 134 */       b3 = paramArrayOfbyte[n];
/* 135 */       b1 = (byte)(b3 & 0x3);
/*     */ 
/*     */       
/* 138 */       byte b6 = ((b3 & 0xFFFFFF80) == 0) ? (byte)(b3 >> 2) : (byte)(b3 >> 2 ^ 0xC0);
/* 139 */       arrayOfByte[m] = lookUpBase64Alphabet[b6];
/* 140 */       arrayOfByte[m + 1] = lookUpBase64Alphabet[b1 << 4];
/* 141 */       arrayOfByte[m + 2] = 61;
/* 142 */       arrayOfByte[m + 3] = 61;
/* 143 */     } else if (j == 16) {
/*     */       
/* 145 */       b3 = paramArrayOfbyte[n];
/* 146 */       b4 = paramArrayOfbyte[n + 1];
/* 147 */       b2 = (byte)(b4 & 0xF);
/* 148 */       b1 = (byte)(b3 & 0x3);
/*     */       
/* 150 */       byte b6 = ((b3 & 0xFFFFFF80) == 0) ? (byte)(b3 >> 2) : (byte)(b3 >> 2 ^ 0xC0);
/* 151 */       byte b7 = ((b4 & 0xFFFFFF80) == 0) ? (byte)(b4 >> 4) : (byte)(b4 >> 4 ^ 0xF0);
/*     */       
/* 153 */       arrayOfByte[m] = lookUpBase64Alphabet[b6];
/* 154 */       arrayOfByte[m + 1] = lookUpBase64Alphabet[b7 | b1 << 4];
/* 155 */       arrayOfByte[m + 2] = lookUpBase64Alphabet[b2 << 2];
/* 156 */       arrayOfByte[m + 3] = 61;
/*     */     } 
/*     */     
/* 159 */     return arrayOfByte;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static byte[] decode(byte[] paramArrayOfbyte) {
/* 171 */     if (paramArrayOfbyte.length == 0) {
/* 172 */       return new byte[0];
/*     */     }
/*     */     
/* 175 */     int i = paramArrayOfbyte.length / 4;
/* 176 */     byte[] arrayOfByte = null;
/* 177 */     byte b1 = 0, b2 = 0, b3 = 0, b4 = 0, b5 = 0, b6 = 0;
/*     */ 
/*     */ 
/*     */     
/* 181 */     byte b = 0;
/* 182 */     int j = 0;
/*     */ 
/*     */     
/* 185 */     int k = paramArrayOfbyte.length;
/*     */     
/* 187 */     while (paramArrayOfbyte[k - 1] == 61) {
/* 188 */       if (--k == 0) {
/* 189 */         return new byte[0];
/*     */       }
/*     */     } 
/* 192 */     arrayOfByte = new byte[k - i];
/*     */ 
/*     */     
/* 195 */     for (k = 0; k < i; k++) {
/* 196 */       j = k * 4;
/* 197 */       b5 = paramArrayOfbyte[j + 2];
/* 198 */       b6 = paramArrayOfbyte[j + 3];
/*     */       
/* 200 */       b1 = base64Alphabet[paramArrayOfbyte[j]];
/* 201 */       b2 = base64Alphabet[paramArrayOfbyte[j + 1]];
/*     */       
/* 203 */       if (b5 != 61 && b6 != 61) {
/*     */         
/* 205 */         b3 = base64Alphabet[b5];
/* 206 */         b4 = base64Alphabet[b6];
/*     */         
/* 208 */         arrayOfByte[b] = (byte)(b1 << 2 | b2 >> 4);
/* 209 */         arrayOfByte[b + 1] = (byte)((b2 & 0xF) << 4 | b3 >> 2 & 0xF);
/* 210 */         arrayOfByte[b + 2] = (byte)(b3 << 6 | b4);
/* 211 */       } else if (b5 == 61) {
/*     */         
/* 213 */         arrayOfByte[b] = (byte)(b1 << 2 | b2 >> 4);
/* 214 */       } else if (b6 == 61) {
/*     */         
/* 216 */         b3 = base64Alphabet[b5];
/*     */         
/* 218 */         arrayOfByte[b] = (byte)(b1 << 2 | b2 >> 4);
/* 219 */         arrayOfByte[b + 1] = (byte)((b2 & 0xF) << 4 | b3 >> 2 & 0xF);
/*     */       } 
/* 221 */       b += 3;
/*     */     } 
/* 223 */     return arrayOfByte;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/wscheck/Base64.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */