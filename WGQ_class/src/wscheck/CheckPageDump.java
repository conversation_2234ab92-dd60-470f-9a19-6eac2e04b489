/*      */ package wscheck;
/*      */ 
/*      */ import java.io.File;
/*      */ import java.io.FileOutputStream;
/*      */ import java.util.ArrayList;
/*      */ import org.objectweb.asm.ClassWriter;
/*      */ import org.objectweb.asm.FieldVisitor;
/*      */ import org.objectweb.asm.Label;
/*      */ import org.objectweb.asm.MethodVisitor;
/*      */ import org.objectweb.asm.Opcodes;
/*      */ import weaver.general.GCONST;
/*      */ import weaver.general.Util;
/*      */ 
/*      */ 
/*      */ 
/*      */ public class CheckPageDump
/*      */   implements Opcodes
/*      */ {
/*      */   public static byte[] dump() throws Exception {
/*   20 */     ClassWriter classWriter = new ClassWriter(0);
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*   25 */     classWriter.visit(49, 33, "wscheck/CheckPage", null, "java/lang/Object", null);
/*      */ 
/*      */     
/*   28 */     classWriter.visitSource("CheckPage.java", null);
/*      */ 
/*      */     
/*   31 */     MethodVisitor methodVisitor = classWriter.visitMethod(1, "<init>", "()V", null, null);
/*   32 */     methodVisitor.visitCode();
/*   33 */     Label label1 = new Label();
/*   34 */     methodVisitor.visitLabel(label1);
/*   35 */     methodVisitor.visitLineNumber(10, label1);
/*   36 */     methodVisitor.visitVarInsn(25, 0);
/*   37 */     methodVisitor.visitMethodInsn(183, "java/lang/Object", "<init>", "()V");
/*      */     
/*   39 */     methodVisitor.visitInsn(177);
/*   40 */     Label label2 = new Label();
/*   41 */     methodVisitor.visitLabel(label2);
/*   42 */     methodVisitor.visitLocalVariable("this", "Lwscheck/CheckPage;", null, label1, label2, 0);
/*      */     
/*   44 */     methodVisitor.visitMaxs(1, 1);
/*   45 */     methodVisitor.visitEnd();
/*      */ 
/*      */ 
/*      */     
/*   49 */     methodVisitor = classWriter.visitMethod(9, "check", "(Ljavax/servlet/http/HttpServletRequest;Ljavax/servlet/http/HttpServletResponse;)V", null, null);
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*   54 */     methodVisitor.visitCode();
/*   55 */     label1 = new Label();
/*   56 */     label2 = new Label();
/*   57 */     Label label3 = new Label();
/*   58 */     methodVisitor.visitTryCatchBlock(label1, label2, label3, "java/lang/Exception");
/*   59 */     methodVisitor.visitLabel(label1);
/*   60 */     methodVisitor.visitLineNumber(16, label1);
/*   61 */     methodVisitor.visitVarInsn(25, 0);
/*   62 */     methodVisitor.visitMethodInsn(185, "javax/servlet/http/HttpServletRequest", "getRequestURI", "()Ljava/lang/String;");
/*      */ 
/*      */     
/*   65 */     methodVisitor.visitVarInsn(58, 2);
/*   66 */     Label label4 = new Label();
/*   67 */     methodVisitor.visitLabel(label4);
/*   68 */     methodVisitor.visitLineNumber(17, label4);
/*   69 */     methodVisitor.visitVarInsn(25, 0);
/*   70 */     methodVisitor.visitInsn(4);
/*   71 */     methodVisitor.visitMethodInsn(185, "javax/servlet/http/HttpServletRequest", "getSession", "(Z)Ljavax/servlet/http/HttpSession;");
/*      */ 
/*      */     
/*   74 */     methodVisitor.visitLdcInsn("weaver_user@bean");
/*   75 */     methodVisitor.visitMethodInsn(185, "javax/servlet/http/HttpSession", "getAttribute", "(Ljava/lang/String;)Ljava/lang/Object;");
/*      */ 
/*      */     
/*   78 */     methodVisitor.visitTypeInsn(192, "weaver/hrm/User");
/*   79 */     methodVisitor.visitVarInsn(58, 3);
/*   80 */     Label label5 = new Label();
/*   81 */     methodVisitor.visitLabel(label5);
/*   82 */     methodVisitor.visitLineNumber(18, label5);
/*   83 */     methodVisitor.visitVarInsn(25, 2);
/*   84 */     methodVisitor.visitLdcInsn("Homepage.jsp");
/*   85 */     methodVisitor.visitMethodInsn(182, "java/lang/String", "indexOf", "(Ljava/lang/String;)I");
/*      */     
/*   87 */     Label label6 = new Label();
/*   88 */     methodVisitor.visitJumpInsn(158, label6);
/*   89 */     Label label7 = new Label();
/*   90 */     methodVisitor.visitLabel(label7);
/*   91 */     methodVisitor.visitLineNumber(19, label7);
/*   92 */     methodVisitor.visitVarInsn(25, 1);
/*   93 */     methodVisitor.visitLdcInsn("text/html;charset=GBK");
/*   94 */     methodVisitor.visitMethodInsn(185, "javax/servlet/http/HttpServletResponse", "setContentType", "(Ljava/lang/String;)V");
/*      */ 
/*      */     
/*   97 */     Label label8 = new Label();
/*   98 */     methodVisitor.visitLabel(label8);
/*   99 */     methodVisitor.visitLineNumber(20, label8);
/*  100 */     methodVisitor.visitVarInsn(25, 1);
/*  101 */     methodVisitor.visitMethodInsn(185, "javax/servlet/http/HttpServletResponse", "getOutputStream", "()Ljavax/servlet/ServletOutputStream;");
/*      */ 
/*      */     
/*  104 */     methodVisitor.visitVarInsn(58, 4);
/*  105 */     Label label9 = new Label();
/*  106 */     methodVisitor.visitLabel(label9);
/*  107 */     methodVisitor.visitLineNumber(21, label9);
/*  108 */     methodVisitor.visitTypeInsn(187, "java/lang/StringBuffer");
/*  109 */     methodVisitor.visitInsn(89);
/*  110 */     methodVisitor.visitMethodInsn(183, "java/lang/StringBuffer", "<init>", "()V");
/*      */     
/*  112 */     methodVisitor.visitVarInsn(58, 5);
/*  113 */     Label label10 = new Label();
/*  114 */     methodVisitor.visitLabel(label10);
/*  115 */     methodVisitor.visitLineNumber(22, label10);
/*  116 */     methodVisitor.visitLdcInsn("");
/*  117 */     methodVisitor.visitVarInsn(58, 6);
/*  118 */     Label label11 = new Label();
/*  119 */     methodVisitor.visitLabel(label11);
/*  120 */     methodVisitor.visitLineNumber(23, label11);
/*  121 */     methodVisitor.visitLdcInsn("");
/*  122 */     methodVisitor.visitVarInsn(58, 7);
/*  123 */     Label label12 = new Label();
/*  124 */     methodVisitor.visitLabel(label12);
/*  125 */     methodVisitor.visitLineNumber(24, label12);
/*  126 */     methodVisitor.visitLdcInsn("-1");
/*  127 */     methodVisitor.visitMethodInsn(184, "wscheck/PageErrorMap", "get", "(Ljava/lang/String;)Ljava/lang/String;");
/*      */     
/*  129 */     methodVisitor.visitMethodInsn(184, "weaver/general/Util", "null2String", "(Ljava/lang/String;)Ljava/lang/String;");
/*      */     
/*  131 */     methodVisitor.visitLdcInsn("");
/*  132 */     methodVisitor.visitMethodInsn(182, "java/lang/String", "equals", "(Ljava/lang/Object;)Z");
/*      */     
/*  134 */     Label label13 = new Label();
/*  135 */     methodVisitor.visitJumpInsn(154, label13);
/*  136 */     Label label14 = new Label();
/*  137 */     methodVisitor.visitLabel(label14);
/*  138 */     methodVisitor.visitLineNumber(26, label14);
/*  139 */     methodVisitor.visitLdcInsn("-1");
/*  140 */     methodVisitor.visitMethodInsn(184, "wscheck/PageErrorMap", "get", "(Ljava/lang/String;)Ljava/lang/String;");
/*      */     
/*  142 */     methodVisitor.visitMethodInsn(184, "weaver/general/Util", "null2String", "(Ljava/lang/String;)Ljava/lang/String;");
/*      */     
/*  144 */     methodVisitor.visitVarInsn(58, 6);
/*  145 */     methodVisitor.visitLabel(label13);
/*  146 */     methodVisitor.visitLineNumber(28, label13);
/*  147 */     methodVisitor.visitLdcInsn("-2");
/*  148 */     methodVisitor.visitMethodInsn(184, "wscheck/PageErrorMap", "get", "(Ljava/lang/String;)Ljava/lang/String;");
/*      */     
/*  150 */     methodVisitor.visitMethodInsn(184, "weaver/general/Util", "null2String", "(Ljava/lang/String;)Ljava/lang/String;");
/*      */     
/*  152 */     methodVisitor.visitLdcInsn("");
/*  153 */     methodVisitor.visitMethodInsn(182, "java/lang/String", "equals", "(Ljava/lang/Object;)Z");
/*      */     
/*  155 */     Label label15 = new Label();
/*  156 */     methodVisitor.visitJumpInsn(154, label15);
/*  157 */     Label label16 = new Label();
/*  158 */     methodVisitor.visitLabel(label16);
/*  159 */     methodVisitor.visitLineNumber(30, label16);
/*  160 */     methodVisitor.visitLdcInsn("-2");
/*  161 */     methodVisitor.visitMethodInsn(184, "wscheck/PageErrorMap", "get", "(Ljava/lang/String;)Ljava/lang/String;");
/*      */     
/*  163 */     methodVisitor.visitMethodInsn(184, "weaver/general/Util", "null2String", "(Ljava/lang/String;)Ljava/lang/String;");
/*      */     
/*  165 */     methodVisitor.visitVarInsn(58, 6);
/*  166 */     methodVisitor.visitLabel(label15);
/*  167 */     methodVisitor.visitLineNumber(32, label15);
/*  168 */     methodVisitor.visitLdcInsn("-3");
/*  169 */     methodVisitor.visitMethodInsn(184, "wscheck/PageErrorMap", "get", "(Ljava/lang/String;)Ljava/lang/String;");
/*      */     
/*  171 */     methodVisitor.visitMethodInsn(184, "weaver/general/Util", "null2String", "(Ljava/lang/String;)Ljava/lang/String;");
/*      */     
/*  173 */     methodVisitor.visitLdcInsn("");
/*  174 */     methodVisitor.visitMethodInsn(182, "java/lang/String", "equals", "(Ljava/lang/Object;)Z");
/*      */     
/*  176 */     Label label17 = new Label();
/*  177 */     methodVisitor.visitJumpInsn(154, label17);
/*  178 */     Label label18 = new Label();
/*  179 */     methodVisitor.visitLabel(label18);
/*  180 */     methodVisitor.visitLineNumber(34, label18);
/*  181 */     methodVisitor.visitLdcInsn("-3");
/*  182 */     methodVisitor.visitMethodInsn(184, "wscheck/PageErrorMap", "get", "(Ljava/lang/String;)Ljava/lang/String;");
/*      */     
/*  184 */     methodVisitor.visitMethodInsn(184, "weaver/general/Util", "null2String", "(Ljava/lang/String;)Ljava/lang/String;");
/*      */     
/*  186 */     methodVisitor.visitVarInsn(58, 6);
/*  187 */     methodVisitor.visitLabel(label17);
/*  188 */     methodVisitor.visitLineNumber(36, label17);
/*  189 */     methodVisitor.visitMethodInsn(184, "wscheck/PageErrorMap", "size", "()I");
/*      */     
/*  191 */     Label label19 = new Label();
/*  192 */     methodVisitor.visitJumpInsn(158, label19);
/*  193 */     methodVisitor.visitLdcInsn("");
/*  194 */     methodVisitor.visitVarInsn(25, 6);
/*  195 */     methodVisitor.visitMethodInsn(182, "java/lang/String", "equals", "(Ljava/lang/Object;)Z");
/*      */     
/*  197 */     methodVisitor.visitJumpInsn(153, label19);
/*  198 */     Label label20 = new Label();
/*  199 */     methodVisitor.visitLabel(label20);
/*  200 */     methodVisitor.visitLineNumber(38, label20);
/*  201 */     methodVisitor.visitMethodInsn(184, "wscheck/CheckScanFile", "hasOldModifyFile", "()Z");
/*      */     
/*  203 */     Label label21 = new Label();
/*  204 */     methodVisitor.visitJumpInsn(153, label21);
/*  205 */     Label label22 = new Label();
/*  206 */     methodVisitor.visitLabel(label22);
/*  207 */     methodVisitor.visitLineNumber(40, label22);
/*  208 */     methodVisitor
/*  209 */       .visitLdcInsn("&nbsp;&nbsp;当前系统存在文件未通过校验,请与管理员联系或者与泛微人员联系！&nbsp;&nbsp;");
/*  210 */     methodVisitor.visitVarInsn(58, 7);
/*  211 */     Label label23 = new Label();
/*  212 */     methodVisitor.visitLabel(label23);
/*  213 */     methodVisitor.visitLineNumber(41, label23);
/*  214 */     methodVisitor.visitVarInsn(25, 3);
/*  215 */     methodVisitor.visitJumpInsn(198, label19);
/*  216 */     methodVisitor.visitVarInsn(25, 3);
/*  217 */     methodVisitor.visitMethodInsn(182, "weaver/hrm/User", "getUID", "()I");
/*      */     
/*  219 */     methodVisitor.visitInsn(4);
/*  220 */     methodVisitor.visitJumpInsn(160, label19);
/*  221 */     Label label24 = new Label();
/*  222 */     methodVisitor.visitLabel(label24);
/*  223 */     methodVisitor.visitLineNumber(43, label24);
/*  224 */     methodVisitor
/*  225 */       .visitLdcInsn("&nbsp;&nbsp;当前系统存在文件未通过校验,请与管理员联系或者与泛微人员联系！&nbsp;<a href='/keygenerator/getNoCheckFiles.jsp' target='_blank'>点击获取文件</a>&nbsp;&nbsp;");
/*  226 */     methodVisitor.visitVarInsn(58, 7);
/*  227 */     methodVisitor.visitJumpInsn(167, label19);
/*  228 */     methodVisitor.visitLabel(label21);
/*  229 */     methodVisitor.visitLineNumber(48, label21);
/*  230 */     methodVisitor.visitVarInsn(25, 3);
/*  231 */     methodVisitor.visitJumpInsn(198, label19);
/*  232 */     methodVisitor.visitMethodInsn(184, "wscheck/PageErrorMap", "getAdminlist", "()Ljava/util/List;");
/*      */     
/*  234 */     methodVisitor.visitTypeInsn(187, "java/lang/StringBuilder");
/*  235 */     methodVisitor.visitInsn(89);
/*  236 */     methodVisitor.visitMethodInsn(183, "java/lang/StringBuilder", "<init>", "()V");
/*      */     
/*  238 */     methodVisitor.visitVarInsn(25, 3);
/*  239 */     methodVisitor.visitMethodInsn(182, "weaver/hrm/User", "getUID", "()I");
/*      */     
/*  241 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuilder", "append", "(I)Ljava/lang/StringBuilder;");
/*      */     
/*  243 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuilder", "toString", "()Ljava/lang/String;");
/*      */     
/*  245 */     methodVisitor.visitMethodInsn(185, "java/util/List", "indexOf", "(Ljava/lang/Object;)I");
/*      */     
/*  247 */     methodVisitor.visitInsn(2);
/*  248 */     methodVisitor.visitJumpInsn(164, label19);
/*  249 */     Label label25 = new Label();
/*  250 */     methodVisitor.visitLabel(label25);
/*  251 */     methodVisitor.visitLineNumber(50, label25);
/*  252 */     methodVisitor
/*  253 */       .visitLdcInsn("&nbsp;&nbsp;当前系统存在文件未通过校验,请与管理员联系或者与泛微人员联系！&nbsp;&nbsp;");
/*  254 */     methodVisitor.visitVarInsn(58, 7);
/*  255 */     Label label26 = new Label();
/*  256 */     methodVisitor.visitLabel(label26);
/*  257 */     methodVisitor.visitLineNumber(51, label26);
/*  258 */     methodVisitor.visitVarInsn(25, 3);
/*  259 */     methodVisitor.visitJumpInsn(198, label19);
/*  260 */     methodVisitor.visitVarInsn(25, 3);
/*  261 */     methodVisitor.visitMethodInsn(182, "weaver/hrm/User", "getUID", "()I");
/*      */     
/*  263 */     methodVisitor.visitInsn(4);
/*  264 */     methodVisitor.visitJumpInsn(160, label19);
/*  265 */     Label label27 = new Label();
/*  266 */     methodVisitor.visitLabel(label27);
/*  267 */     methodVisitor.visitLineNumber(53, label27);
/*  268 */     methodVisitor
/*  269 */       .visitLdcInsn("&nbsp;&nbsp;当前系统存在文件未通过校验,请与管理员联系或者与泛微人员联系！&nbsp;<a href='/keygenerator/getNoCheckFiles.jsp' target='_blank'>点击获取文件</a>&nbsp;&nbsp;");
/*  270 */     methodVisitor.visitVarInsn(58, 7);
/*  271 */     methodVisitor.visitLabel(label19);
/*  272 */     methodVisitor.visitLineNumber(58, label19);
/*  273 */     methodVisitor.visitLdcInsn("");
/*  274 */     methodVisitor.visitVarInsn(25, 6);
/*  275 */     methodVisitor.visitMethodInsn(182, "java/lang/String", "equals", "(Ljava/lang/Object;)Z");
/*      */     
/*  277 */     Label label28 = new Label();
/*  278 */     methodVisitor.visitJumpInsn(154, label28);
/*  279 */     Label label29 = new Label();
/*  280 */     methodVisitor.visitLabel(label29);
/*  281 */     methodVisitor.visitLineNumber(59, label29);
/*  282 */     methodVisitor.visitVarInsn(25, 6);
/*  283 */     methodVisitor.visitVarInsn(58, 7);
/*  284 */     methodVisitor.visitLabel(label28);
/*  285 */     methodVisitor.visitLineNumber(61, label28);
/*  286 */     methodVisitor.visitVarInsn(25, 5);
/*  287 */     methodVisitor.visitLdcInsn("<SCRIPT language=\"javascript\">\n");
/*  288 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  290 */     methodVisitor.visitInsn(87);
/*  291 */     Label label30 = new Label();
/*  292 */     methodVisitor.visitLabel(label30);
/*  293 */     methodVisitor.visitLineNumber(62, label30);
/*  294 */     methodVisitor.visitVarInsn(25, 5);
/*  295 */     methodVisitor.visitLdcInsn("var backMessage = \"\";\n");
/*  296 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  298 */     methodVisitor.visitInsn(87);
/*  299 */     Label label31 = new Label();
/*  300 */     methodVisitor.visitLabel(label31);
/*  301 */     methodVisitor.visitLineNumber(63, label31);
/*  302 */     methodVisitor.visitVarInsn(25, 5);
/*  303 */     methodVisitor.visitLdcInsn("var backMessagex = \"\";\n");
/*  304 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  306 */     methodVisitor.visitInsn(87);
/*  307 */     Label label32 = new Label();
/*  308 */     methodVisitor.visitLabel(label32);
/*  309 */     methodVisitor.visitLineNumber(64, label32);
/*  310 */     methodVisitor.visitVarInsn(25, 5);
/*  311 */     methodVisitor.visitLdcInsn("var backMessagey = \"\";\n");
/*  312 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  314 */     methodVisitor.visitInsn(87);
/*  315 */     Label label33 = new Label();
/*  316 */     methodVisitor.visitLabel(label33);
/*  317 */     methodVisitor.visitLineNumber(65, label33);
/*  318 */     methodVisitor.visitVarInsn(25, 5);
/*  319 */     methodVisitor.visitLdcInsn("var backMessagedelay = 0;\n");
/*  320 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  322 */     methodVisitor.visitInsn(87);
/*  323 */     Label label34 = new Label();
/*  324 */     methodVisitor.visitLabel(label34);
/*  325 */     methodVisitor.visitLineNumber(66, label34);
/*  326 */     methodVisitor.visitVarInsn(25, 5);
/*  327 */     methodVisitor.visitLdcInsn("function feedBackMessage(){\n");
/*  328 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  330 */     methodVisitor.visitInsn(87);
/*  331 */     Label label35 = new Label();
/*  332 */     methodVisitor.visitLabel(label35);
/*  333 */     methodVisitor.visitLineNumber(67, label35);
/*  334 */     methodVisitor.visitVarInsn(25, 5);
/*  335 */     methodVisitor.visitLdcInsn("var message = backMessage;\n");
/*  336 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  338 */     methodVisitor.visitInsn(87);
/*  339 */     Label label36 = new Label();
/*  340 */     methodVisitor.visitLabel(label36);
/*  341 */     methodVisitor.visitLineNumber(68, label36);
/*  342 */     methodVisitor.visitVarInsn(25, 5);
/*  343 */     methodVisitor.visitLdcInsn("var x = backMessagex;\n");
/*  344 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  346 */     methodVisitor.visitInsn(87);
/*  347 */     Label label37 = new Label();
/*  348 */     methodVisitor.visitLabel(label37);
/*  349 */     methodVisitor.visitLineNumber(69, label37);
/*  350 */     methodVisitor.visitVarInsn(25, 5);
/*  351 */     methodVisitor.visitLdcInsn("var y = backMessagey;\n");
/*  352 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  354 */     methodVisitor.visitInsn(87);
/*  355 */     Label label38 = new Label();
/*  356 */     methodVisitor.visitLabel(label38);
/*  357 */     methodVisitor.visitLineNumber(70, label38);
/*  358 */     methodVisitor.visitVarInsn(25, 5);
/*  359 */     methodVisitor.visitLdcInsn("var delay = backMessagedelay;\n");
/*  360 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  362 */     methodVisitor.visitInsn(87);
/*  363 */     Label label39 = new Label();
/*  364 */     methodVisitor.visitLabel(label39);
/*  365 */     methodVisitor.visitLineNumber(71, label39);
/*  366 */     methodVisitor.visitVarInsn(25, 5);
/*  367 */     methodVisitor.visitLdcInsn(" if(!message) return;\n");
/*  368 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  370 */     methodVisitor.visitInsn(87);
/*  371 */     Label label40 = new Label();
/*  372 */     methodVisitor.visitLabel(label40);
/*  373 */     methodVisitor.visitLineNumber(72, label40);
/*  374 */     methodVisitor.visitVarInsn(25, 5);
/*  375 */     methodVisitor
/*  376 */       .visitLdcInsn(" x=/\\d{1,2}%|100%|left|right/.test(x)?x:(parseInt(x)||0)+'px';\n");
/*  377 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  379 */     methodVisitor.visitInsn(87);
/*  380 */     Label label41 = new Label();
/*  381 */     methodVisitor.visitLabel(label41);
/*  382 */     methodVisitor.visitLineNumber(73, label41);
/*  383 */     methodVisitor.visitVarInsn(25, 5);
/*  384 */     methodVisitor
/*  385 */       .visitLdcInsn(" y=/\\d{1,2}%|100%|top|bottom/.test(y)?y:(parseInt(y)||0)+'px';\n");
/*  386 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  388 */     methodVisitor.visitInsn(87);
/*  389 */     Label label42 = new Label();
/*  390 */     methodVisitor.visitLabel(label42);
/*  391 */     methodVisitor.visitLineNumber(74, label42);
/*  392 */     methodVisitor.visitVarInsn(25, 5);
/*  393 */     methodVisitor.visitLdcInsn(" delay=parseInt(delay)||-1;\n");
/*  394 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  396 */     methodVisitor.visitInsn(87);
/*  397 */     Label label43 = new Label();
/*  398 */     methodVisitor.visitLabel(label43);
/*  399 */     methodVisitor.visitLineNumber(75, label43);
/*  400 */     methodVisitor.visitVarInsn(25, 5);
/*  401 */     methodVisitor
/*  402 */       .visitLdcInsn(" var fdDiv=document.getElementById('show_feedBack_message');\n");
/*  403 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  405 */     methodVisitor.visitInsn(87);
/*  406 */     Label label44 = new Label();
/*  407 */     methodVisitor.visitLabel(label44);
/*  408 */     methodVisitor.visitLineNumber(76, label44);
/*  409 */     methodVisitor.visitVarInsn(25, 5);
/*  410 */     methodVisitor.visitLdcInsn(" if(!fdDiv){\n");
/*  411 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  413 */     methodVisitor.visitInsn(87);
/*  414 */     Label label45 = new Label();
/*  415 */     methodVisitor.visitLabel(label45);
/*  416 */     methodVisitor.visitLineNumber(77, label45);
/*  417 */     methodVisitor.visitVarInsn(25, 5);
/*  418 */     methodVisitor
/*  419 */       .visitLdcInsn("  var showMessage=document.createElement(\"div\");\n");
/*  420 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  422 */     methodVisitor.visitInsn(87);
/*  423 */     Label label46 = new Label();
/*  424 */     methodVisitor.visitLabel(label46);
/*  425 */     methodVisitor.visitLineNumber(78, label46);
/*  426 */     methodVisitor.visitVarInsn(25, 5);
/*  427 */     methodVisitor
/*  428 */       .visitLdcInsn("  showMessage.setAttribute(\"id\",\"show_feedBack_message\");\n");
/*  429 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  431 */     methodVisitor.visitInsn(87);
/*  432 */     Label label47 = new Label();
/*  433 */     methodVisitor.visitLabel(label47);
/*  434 */     methodVisitor.visitLineNumber(79, label47);
/*  435 */     methodVisitor.visitVarInsn(25, 5);
/*  436 */     methodVisitor
/*  437 */       .visitLdcInsn("  showMessage.setAttribute(\"style\",\"z-index:10000;filter:alpha(opacity=100);position:absolute;white-space:nowrapborder:1px solid #f00;background:#fc0;line-height:18px;padding:3px;font-size:12px;\");\n");
/*  438 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  440 */     methodVisitor.visitInsn(87);
/*  441 */     Label label48 = new Label();
/*  442 */     methodVisitor.visitLabel(label48);
/*  443 */     methodVisitor.visitLineNumber(80, label48);
/*  444 */     methodVisitor.visitVarInsn(25, 5);
/*  445 */     methodVisitor
/*  446 */       .visitLdcInsn("  showMessage.style.cssText = \"z-index:10000;filter:alpha(opacity=100);position:absolute;white-space:nowrapborder:1px solid #f00;background:#fc0;line-height:18px;padding:3px;font-size:12px;\";\n");
/*  447 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  449 */     methodVisitor.visitInsn(87);
/*  450 */     Label label49 = new Label();
/*  451 */     methodVisitor.visitLabel(label49);
/*  452 */     methodVisitor.visitLineNumber(81, label49);
/*  453 */     methodVisitor.visitVarInsn(25, 5);
/*  454 */     methodVisitor.visitLdcInsn("  document.body.appendChild(showMessage);\n");
/*  455 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  457 */     methodVisitor.visitInsn(87);
/*  458 */     Label label50 = new Label();
/*  459 */     methodVisitor.visitLabel(label50);
/*  460 */     methodVisitor.visitLineNumber(82, label50);
/*  461 */     methodVisitor.visitVarInsn(25, 5);
/*  462 */     methodVisitor
/*  463 */       .visitLdcInsn("  fdDiv=document.getElementById('show_feedBack_message');\n");
/*  464 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  466 */     methodVisitor.visitInsn(87);
/*  467 */     Label label51 = new Label();
/*  468 */     methodVisitor.visitLabel(label51);
/*  469 */     methodVisitor.visitLineNumber(83, label51);
/*  470 */     methodVisitor.visitVarInsn(25, 5);
/*  471 */     methodVisitor.visitLdcInsn(" }\n");
/*  472 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  474 */     methodVisitor.visitInsn(87);
/*  475 */     Label label52 = new Label();
/*  476 */     methodVisitor.visitLabel(label52);
/*  477 */     methodVisitor.visitLineNumber(84, label52);
/*  478 */     methodVisitor.visitVarInsn(25, 5);
/*  479 */     methodVisitor
/*  480 */       .visitLdcInsn(" if(feedBackMessage.timer){clearInterval(feedBackMessage.timer)}\n");
/*  481 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  483 */     methodVisitor.visitInsn(87);
/*  484 */     Label label53 = new Label();
/*  485 */     methodVisitor.visitLabel(label53);
/*  486 */     methodVisitor.visitLineNumber(85, label53);
/*  487 */     methodVisitor.visitVarInsn(25, 5);
/*  488 */     methodVisitor.visitLdcInsn(" fdDiv.innerHTML=message;\n");
/*  489 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  491 */     methodVisitor.visitInsn(87);
/*  492 */     Label label54 = new Label();
/*  493 */     methodVisitor.visitLabel(label54);
/*  494 */     methodVisitor.visitLineNumber(86, label54);
/*  495 */     methodVisitor.visitVarInsn(25, 5);
/*  496 */     methodVisitor.visitLdcInsn(" fdDiv.style.display=\"\";\n");
/*  497 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  499 */     methodVisitor.visitInsn(87);
/*  500 */     Label label55 = new Label();
/*  501 */     methodVisitor.visitLabel(label55);
/*  502 */     methodVisitor.visitLineNumber(87, label55);
/*  503 */     methodVisitor.visitVarInsn(25, 5);
/*  504 */     methodVisitor
/*  505 */       .visitLdcInsn(" var docWidth=document.documentElement.scrollWidth>document.documentElement.clientWidth?document.documentElement.scrollWidth:document.documentElement.clientWidth;\n");
/*  506 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  508 */     methodVisitor.visitInsn(87);
/*  509 */     Label label56 = new Label();
/*  510 */     methodVisitor.visitLabel(label56);
/*  511 */     methodVisitor.visitLineNumber(88, label56);
/*  512 */     methodVisitor.visitVarInsn(25, 5);
/*  513 */     methodVisitor
/*  514 */       .visitLdcInsn(" var docHeight=document.documentElement.scrollHeight>document.documentElement.clientHeight?document.documentElement.scrollHeight:document.documentElement.clientHeight;\n");
/*  515 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  517 */     methodVisitor.visitInsn(87);
/*  518 */     Label label57 = new Label();
/*  519 */     methodVisitor.visitLabel(label57);
/*  520 */     methodVisitor.visitLineNumber(89, label57);
/*  521 */     methodVisitor.visitVarInsn(25, 5);
/*  522 */     methodVisitor.visitLdcInsn(" if(/left|right/.test(x)){\n");
/*  523 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  525 */     methodVisitor.visitInsn(87);
/*  526 */     Label label58 = new Label();
/*  527 */     methodVisitor.visitLabel(label58);
/*  528 */     methodVisitor.visitLineNumber(90, label58);
/*  529 */     methodVisitor.visitVarInsn(25, 5);
/*  530 */     methodVisitor
/*  531 */       .visitLdcInsn("  x=(x==\"left\")?\"0px\":(docWidth-fdDiv.offsetWidth)+\"px\";\n");
/*  532 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  534 */     methodVisitor.visitInsn(87);
/*  535 */     Label label59 = new Label();
/*  536 */     methodVisitor.visitLabel(label59);
/*  537 */     methodVisitor.visitLineNumber(91, label59);
/*  538 */     methodVisitor.visitVarInsn(25, 5);
/*  539 */     methodVisitor.visitLdcInsn(" }\n");
/*  540 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  542 */     methodVisitor.visitInsn(87);
/*  543 */     Label label60 = new Label();
/*  544 */     methodVisitor.visitLabel(label60);
/*  545 */     methodVisitor.visitLineNumber(92, label60);
/*  546 */     methodVisitor.visitVarInsn(25, 5);
/*  547 */     methodVisitor.visitLdcInsn(" if(/top|bottom/.test(y)){");
/*  548 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  550 */     methodVisitor.visitInsn(87);
/*  551 */     Label label61 = new Label();
/*  552 */     methodVisitor.visitLabel(label61);
/*  553 */     methodVisitor.visitLineNumber(93, label61);
/*  554 */     methodVisitor.visitVarInsn(25, 5);
/*  555 */     methodVisitor
/*  556 */       .visitLdcInsn("  y=(y==\"top\")?\"0px\":(docHeight-fdDiv.offsetHeight)+\"px\";\n");
/*  557 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  559 */     methodVisitor.visitInsn(87);
/*  560 */     Label label62 = new Label();
/*  561 */     methodVisitor.visitLabel(label62);
/*  562 */     methodVisitor.visitLineNumber(94, label62);
/*  563 */     methodVisitor.visitVarInsn(25, 5);
/*  564 */     methodVisitor.visitLdcInsn(" }\n");
/*  565 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  567 */     methodVisitor.visitInsn(87);
/*  568 */     Label label63 = new Label();
/*  569 */     methodVisitor.visitLabel(label63);
/*  570 */     methodVisitor.visitLineNumber(95, label63);
/*  571 */     methodVisitor.visitVarInsn(25, 5);
/*  572 */     methodVisitor.visitLdcInsn(" fdDiv.style.left=x;\n");
/*  573 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  575 */     methodVisitor.visitInsn(87);
/*  576 */     Label label64 = new Label();
/*  577 */     methodVisitor.visitLabel(label64);
/*  578 */     methodVisitor.visitLineNumber(96, label64);
/*  579 */     methodVisitor.visitVarInsn(25, 5);
/*  580 */     methodVisitor.visitLdcInsn(" fdDiv.style.top=y;\n");
/*  581 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  583 */     methodVisitor.visitInsn(87);
/*  584 */     Label label65 = new Label();
/*  585 */     methodVisitor.visitLabel(label65);
/*  586 */     methodVisitor.visitLineNumber(97, label65);
/*  587 */     methodVisitor.visitVarInsn(25, 5);
/*  588 */     methodVisitor.visitLdcInsn(" try{\n");
/*  589 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  591 */     methodVisitor.visitInsn(87);
/*  592 */     Label label66 = new Label();
/*  593 */     methodVisitor.visitLabel(label66);
/*  594 */     methodVisitor.visitLineNumber(98, label66);
/*  595 */     methodVisitor.visitVarInsn(25, 5);
/*  596 */     methodVisitor.visitLdcInsn(" fdDiv.filters.Alpha.Opacity=100;\n");
/*  597 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  599 */     methodVisitor.visitInsn(87);
/*  600 */     Label label67 = new Label();
/*  601 */     methodVisitor.visitLabel(label67);
/*  602 */     methodVisitor.visitLineNumber(99, label67);
/*  603 */     methodVisitor.visitVarInsn(25, 5);
/*  604 */     methodVisitor.visitLdcInsn(" var step=parseInt(delay/100);\n");
/*  605 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  607 */     methodVisitor.visitInsn(87);
/*  608 */     Label label68 = new Label();
/*  609 */     methodVisitor.visitLabel(label68);
/*  610 */     methodVisitor.visitLineNumber(100, label68);
/*  611 */     methodVisitor.visitVarInsn(25, 5);
/*  612 */     methodVisitor.visitLdcInsn(" var alpha=fdDiv.filters.Alpha.Opacity;\n");
/*  613 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  615 */     methodVisitor.visitInsn(87);
/*  616 */     Label label69 = new Label();
/*  617 */     methodVisitor.visitLabel(label69);
/*  618 */     methodVisitor.visitLineNumber(101, label69);
/*  619 */     methodVisitor.visitVarInsn(25, 5);
/*  620 */     methodVisitor.visitLdcInsn(" if(delay!=-1){\n");
/*  621 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  623 */     methodVisitor.visitInsn(87);
/*  624 */     Label label70 = new Label();
/*  625 */     methodVisitor.visitLabel(label70);
/*  626 */     methodVisitor.visitLineNumber(102, label70);
/*  627 */     methodVisitor.visitVarInsn(25, 5);
/*  628 */     methodVisitor
/*  629 */       .visitLdcInsn("  feedBackMessage.timer=setInterval(function(){\n");
/*  630 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  632 */     methodVisitor.visitInsn(87);
/*  633 */     Label label71 = new Label();
/*  634 */     methodVisitor.visitLabel(label71);
/*  635 */     methodVisitor.visitLineNumber(103, label71);
/*  636 */     methodVisitor.visitVarInsn(25, 5);
/*  637 */     methodVisitor.visitLdcInsn("   if(fdDiv.filters.Alpha.Opacity>0){\n");
/*  638 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  640 */     methodVisitor.visitInsn(87);
/*  641 */     Label label72 = new Label();
/*  642 */     methodVisitor.visitLabel(label72);
/*  643 */     methodVisitor.visitLineNumber(104, label72);
/*  644 */     methodVisitor.visitVarInsn(25, 5);
/*  645 */     methodVisitor.visitLdcInsn("    fdDiv.filters.Alpha.Opacity--;\n");
/*  646 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  648 */     methodVisitor.visitInsn(87);
/*  649 */     Label label73 = new Label();
/*  650 */     methodVisitor.visitLabel(label73);
/*  651 */     methodVisitor.visitLineNumber(105, label73);
/*  652 */     methodVisitor.visitVarInsn(25, 5);
/*  653 */     methodVisitor.visitLdcInsn("   }else{\n");
/*  654 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  656 */     methodVisitor.visitInsn(87);
/*  657 */     Label label74 = new Label();
/*  658 */     methodVisitor.visitLabel(label74);
/*  659 */     methodVisitor.visitLineNumber(106, label74);
/*  660 */     methodVisitor.visitVarInsn(25, 5);
/*  661 */     methodVisitor.visitLdcInsn("    clearInterval(feedBackMessage.timer);\n");
/*  662 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  664 */     methodVisitor.visitInsn(87);
/*  665 */     Label label75 = new Label();
/*  666 */     methodVisitor.visitLabel(label75);
/*  667 */     methodVisitor.visitLineNumber(107, label75);
/*  668 */     methodVisitor.visitVarInsn(25, 5);
/*  669 */     methodVisitor.visitLdcInsn("    fdDiv.style.display=\"none\";\n");
/*  670 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  672 */     methodVisitor.visitInsn(87);
/*  673 */     Label label76 = new Label();
/*  674 */     methodVisitor.visitLabel(label76);
/*  675 */     methodVisitor.visitLineNumber(108, label76);
/*  676 */     methodVisitor.visitVarInsn(25, 5);
/*  677 */     methodVisitor.visitLdcInsn("   }\n");
/*  678 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  680 */     methodVisitor.visitInsn(87);
/*  681 */     Label label77 = new Label();
/*  682 */     methodVisitor.visitLabel(label77);
/*  683 */     methodVisitor.visitLineNumber(109, label77);
/*  684 */     methodVisitor.visitVarInsn(25, 5);
/*  685 */     methodVisitor.visitLdcInsn("  },step);\n");
/*  686 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  688 */     methodVisitor.visitInsn(87);
/*  689 */     Label label78 = new Label();
/*  690 */     methodVisitor.visitLabel(label78);
/*  691 */     methodVisitor.visitLineNumber(110, label78);
/*  692 */     methodVisitor.visitVarInsn(25, 5);
/*  693 */     methodVisitor.visitLdcInsn(" }\n");
/*  694 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  696 */     methodVisitor.visitInsn(87);
/*  697 */     Label label79 = new Label();
/*  698 */     methodVisitor.visitLabel(label79);
/*  699 */     methodVisitor.visitLineNumber(111, label79);
/*  700 */     methodVisitor.visitVarInsn(25, 5);
/*  701 */     methodVisitor.visitLdcInsn(" }catch(e){\n");
/*  702 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  704 */     methodVisitor.visitInsn(87);
/*  705 */     Label label80 = new Label();
/*  706 */     methodVisitor.visitLabel(label80);
/*  707 */     methodVisitor.visitLineNumber(112, label80);
/*  708 */     methodVisitor.visitVarInsn(25, 5);
/*  709 */     methodVisitor
/*  710 */       .visitLdcInsn(" setTimeout(function(){fdDiv.setAttribute(\"style\",\"display:none;\");},3000); \n");
/*  711 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  713 */     methodVisitor.visitInsn(87);
/*  714 */     Label label81 = new Label();
/*  715 */     methodVisitor.visitLabel(label81);
/*  716 */     methodVisitor.visitLineNumber(113, label81);
/*  717 */     methodVisitor.visitVarInsn(25, 5);
/*  718 */     methodVisitor.visitLdcInsn(" }\n");
/*  719 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  721 */     methodVisitor.visitInsn(87);
/*  722 */     Label label82 = new Label();
/*  723 */     methodVisitor.visitLabel(label82);
/*  724 */     methodVisitor.visitLineNumber(114, label82);
/*  725 */     methodVisitor.visitVarInsn(25, 5);
/*  726 */     methodVisitor.visitLdcInsn("\t}\n");
/*  727 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  729 */     methodVisitor.visitInsn(87);
/*  730 */     Label label83 = new Label();
/*  731 */     methodVisitor.visitLabel(label83);
/*  732 */     methodVisitor.visitLineNumber(116, label83);
/*  733 */     methodVisitor.visitVarInsn(25, 5);
/*  734 */     methodVisitor.visitTypeInsn(187, "java/lang/StringBuilder");
/*  735 */     methodVisitor.visitInsn(89);
/*  736 */     methodVisitor.visitLdcInsn("backMessage = \"");
/*  737 */     methodVisitor.visitMethodInsn(183, "java/lang/StringBuilder", "<init>", "(Ljava/lang/String;)V");
/*      */     
/*  739 */     methodVisitor.visitVarInsn(25, 7);
/*  740 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuilder", "append", "(Ljava/lang/String;)Ljava/lang/StringBuilder;");
/*      */     
/*  742 */     methodVisitor.visitLdcInsn("\";\n");
/*  743 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuilder", "append", "(Ljava/lang/String;)Ljava/lang/StringBuilder;");
/*      */     
/*  745 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuilder", "toString", "()Ljava/lang/String;");
/*      */     
/*  747 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  749 */     methodVisitor.visitInsn(87);
/*  750 */     Label label84 = new Label();
/*  751 */     methodVisitor.visitLabel(label84);
/*  752 */     methodVisitor.visitLineNumber(117, label84);
/*  753 */     methodVisitor.visitVarInsn(25, 5);
/*  754 */     methodVisitor.visitLdcInsn("backMessagex = 'left';\n");
/*  755 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  757 */     methodVisitor.visitInsn(87);
/*  758 */     Label label85 = new Label();
/*  759 */     methodVisitor.visitLabel(label85);
/*  760 */     methodVisitor.visitLineNumber(118, label85);
/*  761 */     methodVisitor.visitVarInsn(25, 5);
/*  762 */     methodVisitor.visitLdcInsn("backMessagey = 'top';\n");
/*  763 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  765 */     methodVisitor.visitInsn(87);
/*  766 */     Label label86 = new Label();
/*  767 */     methodVisitor.visitLabel(label86);
/*  768 */     methodVisitor.visitLineNumber(119, label86);
/*  769 */     methodVisitor.visitVarInsn(25, 5);
/*  770 */     methodVisitor.visitLdcInsn("backMessagedelay = 3000;\n");
/*  771 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  773 */     methodVisitor.visitInsn(87);
/*  774 */     Label label87 = new Label();
/*  775 */     methodVisitor.visitLabel(label87);
/*  776 */     methodVisitor.visitLineNumber(120, label87);
/*  777 */     methodVisitor.visitVarInsn(25, 5);
/*  778 */     methodVisitor.visitLdcInsn("if (window.addEventListener){\n");
/*  779 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  781 */     methodVisitor.visitInsn(87);
/*  782 */     Label label88 = new Label();
/*  783 */     methodVisitor.visitLabel(label88);
/*  784 */     methodVisitor.visitLineNumber(121, label88);
/*  785 */     methodVisitor.visitVarInsn(25, 5);
/*  786 */     methodVisitor
/*  787 */       .visitLdcInsn("    window.addEventListener(\"load\", feedBackMessage, false);\n");
/*  788 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  790 */     methodVisitor.visitInsn(87);
/*  791 */     Label label89 = new Label();
/*  792 */     methodVisitor.visitLabel(label89);
/*  793 */     methodVisitor.visitLineNumber(122, label89);
/*  794 */     methodVisitor.visitVarInsn(25, 5);
/*  795 */     methodVisitor.visitLdcInsn("}else if (window.attachEvent){\n");
/*  796 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  798 */     methodVisitor.visitInsn(87);
/*  799 */     Label label90 = new Label();
/*  800 */     methodVisitor.visitLabel(label90);
/*  801 */     methodVisitor.visitLineNumber(123, label90);
/*  802 */     methodVisitor.visitVarInsn(25, 5);
/*  803 */     methodVisitor
/*  804 */       .visitLdcInsn("    window.attachEvent(\"onload\", feedBackMessage);\n");
/*  805 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  807 */     methodVisitor.visitInsn(87);
/*  808 */     Label label91 = new Label();
/*  809 */     methodVisitor.visitLabel(label91);
/*  810 */     methodVisitor.visitLineNumber(124, label91);
/*  811 */     methodVisitor.visitVarInsn(25, 5);
/*  812 */     methodVisitor.visitLdcInsn("}else{\n");
/*  813 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  815 */     methodVisitor.visitInsn(87);
/*  816 */     Label label92 = new Label();
/*  817 */     methodVisitor.visitLabel(label92);
/*  818 */     methodVisitor.visitLineNumber(125, label92);
/*  819 */     methodVisitor.visitVarInsn(25, 5);
/*  820 */     methodVisitor.visitLdcInsn("    window.onload=feedBackMessage;\n");
/*  821 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  823 */     methodVisitor.visitInsn(87);
/*  824 */     Label label93 = new Label();
/*  825 */     methodVisitor.visitLabel(label93);
/*  826 */     methodVisitor.visitLineNumber(126, label93);
/*  827 */     methodVisitor.visitVarInsn(25, 5);
/*  828 */     methodVisitor.visitLdcInsn("}\n");
/*  829 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  831 */     methodVisitor.visitInsn(87);
/*  832 */     Label label94 = new Label();
/*  833 */     methodVisitor.visitLabel(label94);
/*  834 */     methodVisitor.visitLineNumber(127, label94);
/*  835 */     methodVisitor.visitVarInsn(25, 5);
/*  836 */     methodVisitor.visitLdcInsn("</script>");
/*  837 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "append", "(Ljava/lang/String;)Ljava/lang/StringBuffer;");
/*      */     
/*  839 */     methodVisitor.visitInsn(87);
/*  840 */     Label label95 = new Label();
/*  841 */     methodVisitor.visitLabel(label95);
/*  842 */     methodVisitor.visitLineNumber(129, label95);
/*  843 */     methodVisitor.visitVarInsn(25, 4);
/*  844 */     methodVisitor.visitVarInsn(25, 5);
/*  845 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuffer", "toString", "()Ljava/lang/String;");
/*      */     
/*  847 */     methodVisitor.visitMethodInsn(182, "javax/servlet/ServletOutputStream", "print", "(Ljava/lang/String;)V");
/*      */ 
/*      */     
/*  850 */     Label label96 = new Label();
/*  851 */     methodVisitor.visitLabel(label96);
/*  852 */     methodVisitor.visitLineNumber(130, label96);
/*  853 */     methodVisitor.visitVarInsn(25, 4);
/*  854 */     methodVisitor.visitMethodInsn(182, "javax/servlet/ServletOutputStream", "flush", "()V");
/*      */     
/*  856 */     methodVisitor.visitLabel(label2);
/*  857 */     methodVisitor.visitJumpInsn(167, label6);
/*  858 */     methodVisitor.visitLabel(label3);
/*  859 */     methodVisitor.visitLineNumber(132, label3);
/*  860 */     methodVisitor.visitVarInsn(58, 2);
/*  861 */     methodVisitor.visitLabel(label6);
/*  862 */     methodVisitor.visitLineNumber(136, label6);
/*  863 */     methodVisitor.visitInsn(177);
/*  864 */     Label label97 = new Label();
/*  865 */     methodVisitor.visitLabel(label97);
/*  866 */     methodVisitor
/*  867 */       .visitLocalVariable("request", "Ljavax/servlet/http/HttpServletRequest;", null, label1, label97, 0);
/*      */ 
/*      */     
/*  870 */     methodVisitor.visitLocalVariable("response", "Ljavax/servlet/http/HttpServletResponse;", null, label1, label97, 1);
/*      */ 
/*      */     
/*  873 */     methodVisitor.visitLocalVariable("filepath", "Ljava/lang/String;", null, label4, label3, 2);
/*      */     
/*  875 */     methodVisitor.visitLocalVariable("user", "Lweaver/hrm/User;", null, label5, label3, 3);
/*  876 */     methodVisitor.visitLocalVariable("out", "Ljavax/servlet/ServletOutputStream;", null, label9, label2, 4);
/*      */     
/*  878 */     methodVisitor.visitLocalVariable("sb", "Ljava/lang/StringBuffer;", null, label10, label2, 5);
/*      */     
/*  880 */     methodVisitor.visitLocalVariable("message1", "Ljava/lang/String;", null, label11, label2, 6);
/*      */     
/*  882 */     methodVisitor.visitLocalVariable("message", "Ljava/lang/String;", null, label12, label2, 7);
/*      */     
/*  884 */     methodVisitor.visitMaxs(4, 8);
/*  885 */     methodVisitor.visitEnd();
/*      */     
/*  887 */     classWriter.visitEnd();
/*      */     
/*  889 */     return classWriter.toByteArray();
/*      */   }
/*      */   
/*      */   public static byte[] dumpFilter() throws Exception {
/*  893 */     ClassWriter classWriter = new ClassWriter(0);
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  898 */     classWriter.visit(49, 33, "wscheck/FileCheckFilter", null, "weaver/general/BaseBean", new String[] { "javax/servlet/Filter" });
/*      */     
/*  900 */     classWriter.visitSource("FileCheckFilter.java", null);
/*      */ 
/*      */     
/*  903 */     FieldVisitor fieldVisitor = classWriter.visitField(10, "ischeck", "Ljava/lang/String;", null, null);
/*  904 */     fieldVisitor.visitEnd();
/*      */ 
/*      */     
/*  907 */     MethodVisitor methodVisitor = classWriter.visitMethod(8, "<clinit>", "()V", null, null);
/*  908 */     methodVisitor.visitCode();
/*  909 */     Label label1 = new Label();
/*  910 */     methodVisitor.visitLabel(label1);
/*  911 */     methodVisitor.visitLineNumber(25, label1);
/*  912 */     methodVisitor.visitLdcInsn("");
/*  913 */     methodVisitor.visitFieldInsn(179, "wscheck/FileCheckFilter", "ischeck", "Ljava/lang/String;");
/*  914 */     Label label2 = new Label();
/*  915 */     methodVisitor.visitLabel(label2);
/*  916 */     methodVisitor.visitLineNumber(21, label2);
/*  917 */     methodVisitor.visitInsn(177);
/*  918 */     methodVisitor.visitMaxs(1, 0);
/*  919 */     methodVisitor.visitEnd();
/*      */ 
/*      */     
/*  922 */     methodVisitor = classWriter.visitMethod(1, "<init>", "()V", null, null);
/*  923 */     methodVisitor.visitCode();
/*  924 */     label1 = new Label();
/*  925 */     methodVisitor.visitLabel(label1);
/*  926 */     methodVisitor.visitLineNumber(27, label1);
/*  927 */     methodVisitor.visitVarInsn(25, 0);
/*  928 */     methodVisitor.visitMethodInsn(183, "weaver/general/BaseBean", "<init>", "()V");
/*  929 */     label2 = new Label();
/*  930 */     methodVisitor.visitLabel(label2);
/*  931 */     methodVisitor.visitLineNumber(29, label2);
/*  932 */     methodVisitor.visitInsn(177);
/*  933 */     Label label3 = new Label();
/*  934 */     methodVisitor.visitLabel(label3);
/*  935 */     methodVisitor.visitLocalVariable("this", "Lwscheck/FileCheckFilter;", null, label1, label3, 0);
/*  936 */     methodVisitor.visitMaxs(1, 1);
/*  937 */     methodVisitor.visitEnd();
/*      */ 
/*      */     
/*  940 */     methodVisitor = classWriter.visitMethod(1, "doFilter", "(Ljavax/servlet/ServletRequest;Ljavax/servlet/ServletResponse;Ljavax/servlet/FilterChain;)V", null, new String[] { "java/io/IOException", "javax/servlet/ServletException" });
/*  941 */     methodVisitor.visitCode();
/*  942 */     label1 = new Label();
/*  943 */     methodVisitor.visitLabel(label1);
/*  944 */     methodVisitor.visitLineNumber(34, label1);
/*  945 */     methodVisitor.visitVarInsn(25, 1);
/*  946 */     methodVisitor.visitTypeInsn(193, "javax/servlet/http/HttpServletRequest");
/*  947 */     label2 = new Label();
/*  948 */     methodVisitor.visitJumpInsn(153, label2);
/*  949 */     label3 = new Label();
/*  950 */     methodVisitor.visitLabel(label3);
/*  951 */     methodVisitor.visitLineNumber(36, label3);
/*  952 */     methodVisitor.visitVarInsn(25, 1);
/*  953 */     methodVisitor.visitTypeInsn(192, "javax/servlet/http/HttpServletRequest");
/*  954 */     methodVisitor.visitVarInsn(58, 4);
/*  955 */     Label label4 = new Label();
/*  956 */     methodVisitor.visitLabel(label4);
/*  957 */     methodVisitor.visitLineNumber(37, label4);
/*  958 */     methodVisitor.visitVarInsn(25, 2);
/*  959 */     methodVisitor.visitTypeInsn(192, "javax/servlet/http/HttpServletResponse");
/*  960 */     methodVisitor.visitVarInsn(58, 5);
/*  961 */     Label label5 = new Label();
/*  962 */     methodVisitor.visitLabel(label5);
/*  963 */     methodVisitor.visitLineNumber(38, label5);
/*  964 */     methodVisitor.visitLdcInsn("");
/*  965 */     methodVisitor.visitFieldInsn(178, "wscheck/FileCheckFilter", "ischeck", "Ljava/lang/String;");
/*  966 */     methodVisitor.visitMethodInsn(182, "java/lang/String", "equals", "(Ljava/lang/Object;)Z");
/*  967 */     Label label6 = new Label();
/*  968 */     methodVisitor.visitJumpInsn(153, label6);
/*  969 */     Label label7 = new Label();
/*  970 */     methodVisitor.visitLabel(label7);
/*  971 */     methodVisitor.visitLineNumber(40, label7);
/*  972 */     methodVisitor.visitTypeInsn(187, "weaver/general/BaseBean");
/*  973 */     methodVisitor.visitInsn(89);
/*  974 */     methodVisitor.visitMethodInsn(183, "weaver/general/BaseBean", "<init>", "()V");
/*  975 */     methodVisitor.visitLdcInsn("FileCheck");
/*  976 */     methodVisitor.visitLdcInsn("ischeck");
/*  977 */     methodVisitor.visitMethodInsn(182, "weaver/general/BaseBean", "getPropValue", "(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;");
/*  978 */     methodVisitor.visitMethodInsn(184, "weaver/general/Util", "null2String", "(Ljava/lang/String;)Ljava/lang/String;");
/*  979 */     methodVisitor.visitMethodInsn(182, "java/lang/String", "trim", "()Ljava/lang/String;");
/*  980 */     methodVisitor.visitFieldInsn(179, "wscheck/FileCheckFilter", "ischeck", "Ljava/lang/String;");
/*  981 */     Label label8 = new Label();
/*  982 */     methodVisitor.visitLabel(label8);
/*  983 */     methodVisitor.visitLineNumber(41, label8);
/*  984 */     methodVisitor.visitFieldInsn(178, "wscheck/FileCheckFilter", "ischeck", "Ljava/lang/String;");
/*  985 */     methodVisitor.visitLdcInsn("");
/*  986 */     methodVisitor.visitMethodInsn(182, "java/lang/String", "equals", "(Ljava/lang/Object;)Z");
/*  987 */     Label label9 = new Label();
/*  988 */     methodVisitor.visitJumpInsn(153, label9);
/*  989 */     methodVisitor.visitLdcInsn("0");
/*  990 */     Label label10 = new Label();
/*  991 */     methodVisitor.visitJumpInsn(167, label10);
/*  992 */     methodVisitor.visitLabel(label9);
/*  993 */     methodVisitor.visitFieldInsn(178, "wscheck/FileCheckFilter", "ischeck", "Ljava/lang/String;");
/*  994 */     methodVisitor.visitLabel(label10);
/*  995 */     methodVisitor.visitFieldInsn(179, "wscheck/FileCheckFilter", "ischeck", "Ljava/lang/String;");
/*  996 */     methodVisitor.visitLabel(label6);
/*  997 */     methodVisitor.visitLineNumber(44, label6);
/*  998 */     methodVisitor.visitLdcInsn("0");
/*  999 */     methodVisitor.visitFieldInsn(178, "wscheck/FileCheckFilter", "ischeck", "Ljava/lang/String;");
/* 1000 */     methodVisitor.visitMethodInsn(182, "java/lang/String", "equals", "(Ljava/lang/Object;)Z");
/* 1001 */     Label label11 = new Label();
/* 1002 */     methodVisitor.visitJumpInsn(154, label11);
/* 1003 */     Label label12 = new Label();
/* 1004 */     methodVisitor.visitLabel(label12);
/* 1005 */     methodVisitor.visitLineNumber(45, label12);
/* 1006 */     methodVisitor.visitVarInsn(25, 4);
/* 1007 */     methodVisitor.visitVarInsn(25, 5);
/* 1008 */     methodVisitor.visitMethodInsn(184, "wscheck/CheckPage", "check", "(Ljavax/servlet/http/HttpServletRequest;Ljavax/servlet/http/HttpServletResponse;)V");
/* 1009 */     methodVisitor.visitLabel(label11);
/* 1010 */     methodVisitor.visitLineNumber(46, label11);
/* 1011 */     methodVisitor.visitVarInsn(25, 3);
/* 1012 */     methodVisitor.visitVarInsn(25, 1);
/* 1013 */     methodVisitor.visitVarInsn(25, 2);
/* 1014 */     methodVisitor.visitMethodInsn(185, "javax/servlet/FilterChain", "doFilter", "(Ljavax/servlet/ServletRequest;Ljavax/servlet/ServletResponse;)V");
/* 1015 */     methodVisitor.visitLabel(label2);
/* 1016 */     methodVisitor.visitLineNumber(48, label2);
/* 1017 */     methodVisitor.visitInsn(177);
/* 1018 */     Label label13 = new Label();
/* 1019 */     methodVisitor.visitLabel(label13);
/* 1020 */     methodVisitor.visitLocalVariable("this", "Lwscheck/FileCheckFilter;", null, label1, label13, 0);
/* 1021 */     methodVisitor.visitLocalVariable("servletrequest", "Ljavax/servlet/ServletRequest;", null, label1, label13, 1);
/* 1022 */     methodVisitor.visitLocalVariable("servletresponse", "Ljavax/servlet/ServletResponse;", null, label1, label13, 2);
/* 1023 */     methodVisitor.visitLocalVariable("filterchain", "Ljavax/servlet/FilterChain;", null, label1, label13, 3);
/* 1024 */     methodVisitor.visitLocalVariable("httpservletrequest", "Ljavax/servlet/http/HttpServletRequest;", null, label4, label2, 4);
/* 1025 */     methodVisitor.visitLocalVariable("httpservletresponse", "Ljavax/servlet/http/HttpServletResponse;", null, label5, label2, 5);
/* 1026 */     methodVisitor.visitMaxs(3, 6);
/* 1027 */     methodVisitor.visitEnd();
/*      */ 
/*      */     
/* 1030 */     methodVisitor = classWriter.visitMethod(1, "init", "(Ljavax/servlet/FilterConfig;)V", null, null);
/* 1031 */     methodVisitor.visitCode();
/* 1032 */     label1 = new Label();
/* 1033 */     methodVisitor.visitLabel(label1);
/* 1034 */     methodVisitor.visitLineNumber(52, label1);
/* 1035 */     methodVisitor.visitMethodInsn(184, "wscheck/InitCheck", "isIsinit", "()Z");
/* 1036 */     methodVisitor.visitVarInsn(54, 2);
/* 1037 */     label2 = new Label();
/* 1038 */     methodVisitor.visitLabel(label2);
/* 1039 */     methodVisitor.visitLineNumber(53, label2);
/* 1040 */     methodVisitor.visitVarInsn(21, 2);
/* 1041 */     label3 = new Label();
/* 1042 */     methodVisitor.visitJumpInsn(154, label3);
/* 1043 */     label4 = new Label();
/* 1044 */     methodVisitor.visitLabel(label4);
/* 1045 */     methodVisitor.visitLineNumber(55, label4);
/* 1046 */     methodVisitor.visitLdcInsn("");
/* 1047 */     methodVisitor.visitMethodInsn(184, "weaver/general/GCONST", "getRootPath", "()Ljava/lang/String;");
/* 1048 */     methodVisitor.visitMethodInsn(184, "weaver/general/Util", "null2String", "(Ljava/lang/String;)Ljava/lang/String;");
/* 1049 */     methodVisitor.visitMethodInsn(182, "java/lang/String", "equals", "(Ljava/lang/Object;)Z");
/* 1050 */     label5 = new Label();
/* 1051 */     methodVisitor.visitJumpInsn(153, label5);
/* 1052 */     label6 = new Label();
/* 1053 */     methodVisitor.visitLabel(label6);
/* 1054 */     methodVisitor.visitLineNumber(57, label6);
/* 1055 */     methodVisitor.visitVarInsn(25, 1);
/* 1056 */     methodVisitor.visitMethodInsn(185, "javax/servlet/FilterConfig", "getServletContext", "()Ljavax/servlet/ServletContext;");
/* 1057 */     methodVisitor.visitVarInsn(58, 3);
/* 1058 */     label7 = new Label();
/* 1059 */     methodVisitor.visitLabel(label7);
/* 1060 */     methodVisitor.visitLineNumber(58, label7);
/* 1061 */     methodVisitor.visitVarInsn(25, 3);
/* 1062 */     methodVisitor.visitLdcInsn("/");
/* 1063 */     methodVisitor.visitMethodInsn(185, "javax/servlet/ServletContext", "getRealPath", "(Ljava/lang/String;)Ljava/lang/String;");
/* 1064 */     methodVisitor.visitVarInsn(58, 4);
/* 1065 */     label8 = new Label();
/* 1066 */     methodVisitor.visitLabel(label8);
/* 1067 */     methodVisitor.visitLineNumber(59, label8);
/* 1068 */     methodVisitor.visitVarInsn(25, 4);
/* 1069 */     methodVisitor.visitTypeInsn(187, "java/lang/StringBuilder");
/* 1070 */     methodVisitor.visitInsn(89);
/* 1071 */     methodVisitor.visitMethodInsn(183, "java/lang/StringBuilder", "<init>", "()V");
/* 1072 */     methodVisitor.visitLdcInsn("");
/* 1073 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuilder", "append", "(Ljava/lang/String;)Ljava/lang/StringBuilder;");
/* 1074 */     methodVisitor.visitFieldInsn(178, "java/io/File", "separatorChar", "C");
/* 1075 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuilder", "append", "(C)Ljava/lang/StringBuilder;");
/* 1076 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuilder", "toString", "()Ljava/lang/String;");
/* 1077 */     methodVisitor.visitMethodInsn(182, "java/lang/String", "endsWith", "(Ljava/lang/String;)Z");
/* 1078 */     label9 = new Label();
/* 1079 */     methodVisitor.visitJumpInsn(154, label9);
/* 1080 */     label10 = new Label();
/* 1081 */     methodVisitor.visitLabel(label10);
/* 1082 */     methodVisitor.visitLineNumber(60, label10);
/* 1083 */     methodVisitor.visitTypeInsn(187, "java/lang/StringBuilder");
/* 1084 */     methodVisitor.visitInsn(89);
/* 1085 */     methodVisitor.visitMethodInsn(183, "java/lang/StringBuilder", "<init>", "()V");
/* 1086 */     methodVisitor.visitVarInsn(25, 4);
/* 1087 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuilder", "append", "(Ljava/lang/String;)Ljava/lang/StringBuilder;");
/* 1088 */     methodVisitor.visitFieldInsn(178, "java/io/File", "separatorChar", "C");
/* 1089 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuilder", "append", "(C)Ljava/lang/StringBuilder;");
/* 1090 */     methodVisitor.visitMethodInsn(182, "java/lang/StringBuilder", "toString", "()Ljava/lang/String;");
/* 1091 */     methodVisitor.visitVarInsn(58, 4);
/* 1092 */     methodVisitor.visitLabel(label9);
/* 1093 */     methodVisitor.visitLineNumber(61, label9);
/* 1094 */     methodVisitor.visitVarInsn(25, 4);
/* 1095 */     methodVisitor.visitMethodInsn(184, "weaver/general/GCONST", "setRootPath", "(Ljava/lang/String;)V");
/* 1096 */     methodVisitor.visitLabel(label5);
/* 1097 */     methodVisitor.visitLineNumber(63, label5);
/* 1098 */     methodVisitor.visitTypeInsn(187, "wscheck/InitCheck");
/* 1099 */     methodVisitor.visitInsn(89);
/* 1100 */     methodVisitor.visitMethodInsn(183, "wscheck/InitCheck", "<init>", "()V");
/* 1101 */     methodVisitor.visitVarInsn(58, 3);
/* 1102 */     label11 = new Label();
/* 1103 */     methodVisitor.visitLabel(label11);
/* 1104 */     methodVisitor.visitLineNumber(64, label11);
/* 1105 */     methodVisitor.visitTypeInsn(187, "java/lang/Thread");
/* 1106 */     methodVisitor.visitInsn(89);
/* 1107 */     methodVisitor.visitVarInsn(25, 3);
/* 1108 */     methodVisitor.visitMethodInsn(183, "java/lang/Thread", "<init>", "(Ljava/lang/Runnable;)V");
/* 1109 */     methodVisitor.visitVarInsn(58, 4);
/* 1110 */     label12 = new Label();
/* 1111 */     methodVisitor.visitLabel(label12);
/* 1112 */     methodVisitor.visitLineNumber(65, label12);
/* 1113 */     methodVisitor.visitMethodInsn(184, "weaver/general/InitServer", "getThreadPool", "()Ljava/util/ArrayList;");
/* 1114 */     methodVisitor.visitVarInsn(25, 4);
/* 1115 */     methodVisitor.visitMethodInsn(182, "java/util/ArrayList", "add", "(Ljava/lang/Object;)Z");
/* 1116 */     methodVisitor.visitInsn(87);
/* 1117 */     label13 = new Label();
/* 1118 */     methodVisitor.visitLabel(label13);
/* 1119 */     methodVisitor.visitLineNumber(66, label13);
/* 1120 */     methodVisitor.visitVarInsn(25, 4);
/* 1121 */     methodVisitor.visitMethodInsn(182, "java/lang/Thread", "start", "()V");
/* 1122 */     methodVisitor.visitLabel(label3);
/* 1123 */     methodVisitor.visitLineNumber(68, label3);
/* 1124 */     methodVisitor.visitInsn(177);
/* 1125 */     Label label14 = new Label();
/* 1126 */     methodVisitor.visitLabel(label14);
/* 1127 */     methodVisitor.visitLocalVariable("this", "Lwscheck/FileCheckFilter;", null, label1, label14, 0);
/* 1128 */     methodVisitor.visitLocalVariable("filterconfig", "Ljavax/servlet/FilterConfig;", null, label1, label14, 1);
/* 1129 */     methodVisitor.visitLocalVariable("flag", "Z", null, label2, label14, 2);
/* 1130 */     methodVisitor.visitLocalVariable("servletcontext", "Ljavax/servlet/ServletContext;", null, label7, label5, 3);
/* 1131 */     methodVisitor.visitLocalVariable("s", "Ljava/lang/String;", null, label8, label5, 4);
/* 1132 */     methodVisitor.visitLocalVariable("initcheck", "Lwscheck/InitCheck;", null, label11, label3, 3);
/* 1133 */     methodVisitor.visitLocalVariable("thread", "Ljava/lang/Thread;", null, label12, label3, 4);
/* 1134 */     methodVisitor.visitMaxs(3, 5);
/* 1135 */     methodVisitor.visitEnd();
/*      */ 
/*      */     
/* 1138 */     methodVisitor = classWriter.visitMethod(1, "destroy", "()V", null, null);
/* 1139 */     methodVisitor.visitCode();
/* 1140 */     label1 = new Label();
/* 1141 */     methodVisitor.visitLabel(label1);
/* 1142 */     methodVisitor.visitLineNumber(72, label1);
/* 1143 */     methodVisitor.visitInsn(177);
/* 1144 */     label2 = new Label();
/* 1145 */     methodVisitor.visitLabel(label2);
/* 1146 */     methodVisitor.visitLocalVariable("this", "Lwscheck/FileCheckFilter;", null, label1, label2, 0);
/* 1147 */     methodVisitor.visitMaxs(0, 1);
/* 1148 */     methodVisitor.visitEnd();
/*      */     
/* 1150 */     classWriter.visitEnd();
/* 1151 */     return classWriter.toByteArray();
/*      */   }
/*      */   
/*      */   public static byte[] dumpInit() throws Exception {
/* 1155 */     ClassWriter classWriter = new ClassWriter(0);
/*      */ 
/*      */ 
/*      */     
/* 1159 */     classWriter.visit(49, 33, "wscheck/InitCheck", null, "java/lang/Object", new String[] { "java/lang/Runnable" });
/*      */     
/* 1161 */     classWriter.visitSource("InitCheck.java", null);
/*      */     
/* 1163 */     FieldVisitor fieldVisitor = classWriter.visitField(10, "isinit", "Z", null, null);
/* 1164 */     fieldVisitor.visitEnd();
/*      */ 
/*      */     
/* 1167 */     MethodVisitor methodVisitor = classWriter.visitMethod(8, "<clinit>", "()V", null, null);
/* 1168 */     methodVisitor.visitCode();
/* 1169 */     Label label1 = new Label();
/* 1170 */     methodVisitor.visitLabel(label1);
/* 1171 */     methodVisitor.visitLineNumber(6, label1);
/* 1172 */     methodVisitor.visitInsn(3);
/* 1173 */     methodVisitor.visitFieldInsn(179, "wscheck/InitCheck", "isinit", "Z");
/* 1174 */     Label label2 = new Label();
/* 1175 */     methodVisitor.visitLabel(label2);
/* 1176 */     methodVisitor.visitLineNumber(5, label2);
/* 1177 */     methodVisitor.visitInsn(177);
/* 1178 */     methodVisitor.visitMaxs(1, 0);
/* 1179 */     methodVisitor.visitEnd();
/*      */ 
/*      */     
/* 1182 */     methodVisitor = classWriter.visitMethod(1, "<init>", "()V", null, null);
/* 1183 */     methodVisitor.visitCode();
/* 1184 */     label1 = new Label();
/* 1185 */     methodVisitor.visitLabel(label1);
/* 1186 */     methodVisitor.visitLineNumber(5, label1);
/* 1187 */     methodVisitor.visitVarInsn(25, 0);
/* 1188 */     methodVisitor.visitMethodInsn(183, "java/lang/Object", "<init>", "()V");
/* 1189 */     methodVisitor.visitInsn(177);
/* 1190 */     label2 = new Label();
/* 1191 */     methodVisitor.visitLabel(label2);
/* 1192 */     methodVisitor.visitLocalVariable("this", "Lwscheck/InitCheck;", null, label1, label2, 0);
/* 1193 */     methodVisitor.visitMaxs(1, 1);
/* 1194 */     methodVisitor.visitEnd();
/*      */ 
/*      */     
/* 1197 */     methodVisitor = classWriter.visitMethod(1, "run", "()V", null, null);
/* 1198 */     methodVisitor.visitCode();
/* 1199 */     label1 = new Label();
/* 1200 */     label2 = new Label();
/* 1201 */     Label label3 = new Label();
/* 1202 */     methodVisitor.visitTryCatchBlock(label1, label2, label3, "java/lang/Exception");
/* 1203 */     methodVisitor.visitLabel(label1);
/* 1204 */     methodVisitor.visitLineNumber(9, label1);
/* 1205 */     methodVisitor.visitInsn(4);
/* 1206 */     methodVisitor.visitFieldInsn(179, "wscheck/InitCheck", "isinit", "Z");
/* 1207 */     Label label4 = new Label();
/* 1208 */     methodVisitor.visitLabel(label4);
/* 1209 */     methodVisitor.visitLineNumber(11, label4);
/* 1210 */     methodVisitor.visitLdcInsn("wscheck.CheckFirst");
/* 1211 */     methodVisitor.visitMethodInsn(184, "java/lang/Class", "forName", "(Ljava/lang/String;)Ljava/lang/Class;");
/* 1212 */     methodVisitor.visitVarInsn(58, 1);
/* 1213 */     Label label5 = new Label();
/* 1214 */     methodVisitor.visitLabel(label5);
/* 1215 */     methodVisitor.visitLineNumber(12, label5);
/* 1216 */     methodVisitor.visitVarInsn(25, 1);
/* 1217 */     methodVisitor.visitMethodInsn(182, "java/lang/Class", "newInstance", "()Ljava/lang/Object;");
/* 1218 */     methodVisitor.visitVarInsn(58, 2);
/* 1219 */     Label label6 = new Label();
/* 1220 */     methodVisitor.visitLabel(label6);
/* 1221 */     methodVisitor.visitLineNumber(13, label6);
/* 1222 */     methodVisitor.visitVarInsn(25, 1);
/* 1223 */     methodVisitor.visitLdcInsn("execute");
/* 1224 */     methodVisitor.visitInsn(3);
/* 1225 */     methodVisitor.visitTypeInsn(189, "java/lang/Class");
/* 1226 */     methodVisitor.visitMethodInsn(182, "java/lang/Class", "getMethod", "(Ljava/lang/String;[Ljava/lang/Class;)Ljava/lang/reflect/Method;");
/*      */     
/* 1228 */     methodVisitor.visitVarInsn(58, 3);
/* 1229 */     Label label7 = new Label();
/* 1230 */     methodVisitor.visitLabel(label7);
/* 1231 */     methodVisitor.visitLineNumber(14, label7);
/* 1232 */     methodVisitor.visitVarInsn(25, 3);
/* 1233 */     methodVisitor.visitVarInsn(25, 2);
/* 1234 */     methodVisitor.visitInsn(1);
/* 1235 */     methodVisitor.visitMethodInsn(182, "java/lang/reflect/Method", "invoke", "(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;");
/*      */     
/* 1237 */     methodVisitor.visitInsn(87);
/* 1238 */     Label label8 = new Label();
/* 1239 */     methodVisitor.visitLabel(label8);
/* 1240 */     methodVisitor.visitLineNumber(17, label8);
/* 1241 */     methodVisitor.visitLdcInsn("wscheck.CheckScheduler");
/* 1242 */     methodVisitor.visitMethodInsn(184, "java/lang/Class", "forName", "(Ljava/lang/String;)Ljava/lang/Class;");
/* 1243 */     methodVisitor.visitVarInsn(58, 4);
/* 1244 */     Label label9 = new Label();
/* 1245 */     methodVisitor.visitLabel(label9);
/* 1246 */     methodVisitor.visitLineNumber(18, label9);
/* 1247 */     methodVisitor.visitVarInsn(25, 4);
/* 1248 */     methodVisitor.visitMethodInsn(182, "java/lang/Class", "newInstance", "()Ljava/lang/Object;");
/* 1249 */     methodVisitor.visitVarInsn(58, 5);
/* 1250 */     Label label10 = new Label();
/* 1251 */     methodVisitor.visitLabel(label10);
/* 1252 */     methodVisitor.visitLineNumber(19, label10);
/* 1253 */     methodVisitor.visitVarInsn(25, 4);
/* 1254 */     methodVisitor.visitLdcInsn("start");
/* 1255 */     methodVisitor.visitInsn(3);
/* 1256 */     methodVisitor.visitTypeInsn(189, "java/lang/Class");
/* 1257 */     methodVisitor.visitMethodInsn(182, "java/lang/Class", "getMethod", "(Ljava/lang/String;[Ljava/lang/Class;)Ljava/lang/reflect/Method;");
/*      */     
/* 1259 */     methodVisitor.visitVarInsn(58, 6);
/* 1260 */     Label label11 = new Label();
/* 1261 */     methodVisitor.visitLabel(label11);
/* 1262 */     methodVisitor.visitLineNumber(20, label11);
/* 1263 */     methodVisitor.visitVarInsn(25, 6);
/* 1264 */     methodVisitor.visitVarInsn(25, 5);
/* 1265 */     methodVisitor.visitInsn(1);
/* 1266 */     methodVisitor.visitMethodInsn(182, "java/lang/reflect/Method", "invoke", "(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;");
/*      */     
/* 1268 */     methodVisitor.visitInsn(87);
/* 1269 */     methodVisitor.visitLabel(label2);
/* 1270 */     Label label12 = new Label();
/* 1271 */     methodVisitor.visitJumpInsn(167, label12);
/* 1272 */     methodVisitor.visitLabel(label3);
/* 1273 */     methodVisitor.visitLineNumber(21, label3);
/* 1274 */     methodVisitor.visitVarInsn(58, 1);
/* 1275 */     Label label13 = new Label();
/* 1276 */     methodVisitor.visitLabel(label13);
/* 1277 */     methodVisitor.visitLineNumber(22, label13);
/* 1278 */     methodVisitor.visitVarInsn(25, 1);
/* 1279 */     methodVisitor.visitMethodInsn(182, "java/lang/Exception", "printStackTrace", "()V");
/* 1280 */     methodVisitor.visitLabel(label12);
/* 1281 */     methodVisitor.visitLineNumber(24, label12);
/* 1282 */     methodVisitor.visitInsn(177);
/* 1283 */     Label label14 = new Label();
/* 1284 */     methodVisitor.visitLabel(label14);
/* 1285 */     methodVisitor.visitLocalVariable("this", "Lwscheck/InitCheck;", null, label1, label14, 0);
/* 1286 */     methodVisitor.visitLocalVariable("clazz1", "Ljava/lang/Class;", null, label5, label3, 1);
/* 1287 */     methodVisitor.visitLocalVariable("object1", "Ljava/lang/Object;", null, label6, label3, 2);
/* 1288 */     methodVisitor.visitLocalVariable("method1", "Ljava/lang/reflect/Method;", null, label7, label3, 3);
/* 1289 */     methodVisitor.visitLocalVariable("clazz2", "Ljava/lang/Class;", null, label9, label3, 4);
/* 1290 */     methodVisitor.visitLocalVariable("object2", "Ljava/lang/Object;", null, label10, label3, 5);
/* 1291 */     methodVisitor.visitLocalVariable("method2", "Ljava/lang/reflect/Method;", null, label11, label3, 6);
/* 1292 */     methodVisitor.visitLocalVariable("e", "Ljava/lang/Exception;", null, label13, label12, 1);
/* 1293 */     methodVisitor.visitMaxs(3, 7);
/* 1294 */     methodVisitor.visitEnd();
/*      */ 
/*      */     
/* 1297 */     methodVisitor = classWriter.visitMethod(9, "isIsinit", "()Z", null, null);
/* 1298 */     methodVisitor.visitCode();
/* 1299 */     label1 = new Label();
/* 1300 */     methodVisitor.visitLabel(label1);
/* 1301 */     methodVisitor.visitLineNumber(27, label1);
/* 1302 */     methodVisitor.visitFieldInsn(178, "wscheck/InitCheck", "isinit", "Z");
/* 1303 */     methodVisitor.visitInsn(172);
/* 1304 */     methodVisitor.visitMaxs(1, 0);
/* 1305 */     methodVisitor.visitEnd();
/*      */     
/* 1307 */     classWriter.visitEnd();
/* 1308 */     return classWriter.toByteArray();
/*      */   }
/*      */   
/*      */   private static void checkresinweb() {
/* 1312 */     String str = GCONST.getRootPath() + "WEB-INF" + File.separatorChar + "resin-web.xml";
/* 1313 */     File file = new File(str);
/*      */     
/* 1315 */     if (file.exists()) {
/* 1316 */       ArrayList<String> arrayList = CommonUtil.getLineList(file);
/*      */       
/* 1318 */       String str1 = Util.null2String(CommonUtil.getLines(file));
/*      */       
/* 1320 */       if (str1.indexOf("wscheck.FileCheckFilter") < 0) {
/* 1321 */         byte b1 = -1;
/* 1322 */         byte b2 = -1; byte b3;
/* 1323 */         for (b3 = 0; b3 < arrayList.size(); b3++) {
/* 1324 */           String str2 = Util.null2String(arrayList.get(b3)).trim();
/*      */           
/* 1326 */           if (str2.equals("</web-app>")) {
/* 1327 */             b1 = b3;
/*      */           }
/*      */           
/* 1330 */           if (str2.equals("<session-config>")) {
/* 1331 */             b2 = b3;
/*      */           }
/*      */         } 
/*      */ 
/*      */         
/* 1336 */         b3 = 0;
/* 1337 */         StringBuffer stringBuffer = new StringBuffer();
/* 1338 */         for (byte b4 = 0; b4 < b1; b4++) {
/* 1339 */           String str2 = Util.null2String(arrayList.get(b4)).trim();
/*      */           
/* 1341 */           if (b2 > -1 && b2 == b4) {
/* 1342 */             stringBuffer.append("<filter>\n");
/* 1343 */             stringBuffer.append("<filter-name>FileCheckFilter</filter-name>\n");
/* 1344 */             stringBuffer.append("<filter-class>wscheck.FileCheckFilter</filter-class>\n");
/* 1345 */             stringBuffer.append("</filter>\n");
/* 1346 */             stringBuffer.append("<filter-mapping>\n");
/* 1347 */             stringBuffer.append("<filter-name>FileCheckFilter</filter-name>\n");
/* 1348 */             stringBuffer.append("<url-pattern>*.jsp</url-pattern>\n");
/* 1349 */             stringBuffer.append("</filter-mapping>\n");
/* 1350 */             stringBuffer.append("<filter-mapping>\n");
/* 1351 */             stringBuffer.append("<filter-name>FileCheckFilter</filter-name>\n");
/* 1352 */             stringBuffer.append("<url-pattern>*.html</url-pattern>\n");
/* 1353 */             stringBuffer.append("</filter-mapping>\n");
/* 1354 */             stringBuffer.append("<filter-mapping>\n");
/* 1355 */             stringBuffer.append("<filter-name>FileCheckFilter</filter-name>\n");
/* 1356 */             stringBuffer.append("<url-pattern>/api/*</url-pattern>\n");
/* 1357 */             stringBuffer.append("</filter-mapping>\n");
/* 1358 */             b3 = 1;
/*      */           } 
/* 1360 */           stringBuffer.append(str2).append("\n");
/*      */         } 
/* 1362 */         if (b3 == 0) {
/* 1363 */           stringBuffer.append("<filter>\n");
/* 1364 */           stringBuffer.append("<filter-name>FileCheckFilter</filter-name>\n");
/* 1365 */           stringBuffer.append("<filter-class>wscheck.FileCheckFilter</filter-class>\n");
/* 1366 */           stringBuffer.append("</filter>\n");
/* 1367 */           stringBuffer.append("<filter-mapping>\n");
/* 1368 */           stringBuffer.append("<filter-name>FileCheckFilter</filter-name>\n");
/* 1369 */           stringBuffer.append("<url-pattern>*.jsp</url-pattern>\n");
/* 1370 */           stringBuffer.append("</filter-mapping>\n");
/* 1371 */           stringBuffer.append("<filter-mapping>\n");
/* 1372 */           stringBuffer.append("<filter-name>FileCheckFilter</filter-name>\n");
/* 1373 */           stringBuffer.append("<url-pattern>*.html</url-pattern>\n");
/* 1374 */           stringBuffer.append("</filter-mapping>\n");
/* 1375 */           stringBuffer.append("<filter-mapping>\n");
/* 1376 */           stringBuffer.append("<filter-name>FileCheckFilter</filter-name>\n");
/* 1377 */           stringBuffer.append("<url-pattern>/api/*</url-pattern>\n");
/* 1378 */           stringBuffer.append("</filter-mapping>\n");
/*      */         } 
/* 1380 */         stringBuffer.append("</web-app>");
/* 1381 */         CommonUtil.rewritefile(str, stringBuffer.toString());
/*      */       }
/*      */       else {
/*      */         
/* 1385 */         str1 = str1.replaceAll("(?s)<\\!\\-\\-.+?\\-\\->", "");
/* 1386 */         str1 = str1.replaceAll("xmlns=\"\"", "");
/* 1387 */         str1 = str1.replaceAll("\n|\r", "").replaceAll(">\\s+", ">").replaceAll("\\s+<", "<").replaceAll("\\s", "");
/* 1388 */         StringBuffer stringBuffer1 = new StringBuffer();
/* 1389 */         String str2 = "";
/* 1390 */         if (str1.contains("FileCheckFilter")) {
/* 1391 */           stringBuffer1.append("<filter-mapping>\n");
/* 1392 */           stringBuffer1.append("<filter-name>FileCheckFilter</filter-name>\n");
/* 1393 */           stringBuffer1.append("<url-pattern>*.html</url-pattern>\n");
/* 1394 */           stringBuffer1.append("</filter-mapping>\n");
/*      */           
/* 1396 */           str2 = stringBuffer1.toString().replaceAll("\n|\r", "").replaceAll(">\\s+", ">").replaceAll("\\s+<", "<").replaceAll("\\s", "");
/*      */           
/* 1398 */           if (str1.contains(str2)) {
/* 1399 */             stringBuffer1 = new StringBuffer();
/*      */           }
/* 1401 */           stringBuffer1.append("<filter-mapping>\n");
/* 1402 */           stringBuffer1.append("<filter-name>FileCheckFilter</filter-name>\n");
/* 1403 */           stringBuffer1.append("<url-pattern>/api/*</url-pattern>\n");
/* 1404 */           stringBuffer1.append("</filter-mapping>\n");
/*      */           
/* 1406 */           str2 = stringBuffer1.toString().replaceAll("\n|\r", "").replaceAll(">\\s+", ">").replaceAll("\\s+<", "<").replaceAll("\\s", "");
/*      */         } 
/*      */         
/* 1409 */         StringBuffer stringBuffer2 = new StringBuffer();
/* 1410 */         if (!"".equals(str2) && !str1.contains(str2)) {
/* 1411 */           byte b1 = -1;
/* 1412 */           byte b2 = -1; byte b;
/* 1413 */           for (b = 0; b < arrayList.size(); b++) {
/* 1414 */             String str3 = Util.null2String(arrayList.get(b)).trim();
/* 1415 */             if (str3.equals("</web-app>")) {
/* 1416 */               b1 = b;
/*      */             }
/*      */           } 
/* 1419 */           for (b = 0; b < b1; b++) {
/* 1420 */             String str3 = Util.null2String(arrayList.get(b)).trim();
/* 1421 */             stringBuffer2.append(str3).append("\n");
/*      */           } 
/* 1423 */           stringBuffer2.append(stringBuffer1);
/* 1424 */           stringBuffer2.append("</web-app>");
/* 1425 */           CommonUtil.rewritefile(str, stringBuffer2.toString());
/*      */         } 
/*      */       } 
/*      */     } else {
/*      */ 
/*      */       
/*      */       try {
/* 1432 */         StringBuffer stringBuffer = new StringBuffer();
/* 1433 */         stringBuffer.append("<?xml version=\"1.0\" encoding=\"UTF-8\"?>");
/* 1434 */         stringBuffer.append("<!DOCTYPE web-app PUBLIC \"-//Sun Microsystems, Inc.//DTD Web Application 2.3//EN\" \"http://java.sun.com/dtd/web-app_2_3.dtd\">\n");
/* 1435 */         stringBuffer.append("<web-app>\n");
/* 1436 */         stringBuffer.append("<filter>\n");
/* 1437 */         stringBuffer.append("<filter-name>FileCheckFilter</filter-name>\n");
/* 1438 */         stringBuffer.append("<filter-class>wscheck.FileCheckFilter</filter-class>\n");
/* 1439 */         stringBuffer.append("</filter>\n");
/* 1440 */         stringBuffer.append("<filter-mapping>\n");
/* 1441 */         stringBuffer.append("<filter-name>FileCheckFilter</filter-name>\n");
/* 1442 */         stringBuffer.append("<url-pattern>*.jsp</url-pattern>\n");
/* 1443 */         stringBuffer.append("</filter-mapping>\n");
/* 1444 */         stringBuffer.append("<filter-mapping>\n");
/* 1445 */         stringBuffer.append("<filter-name>FileCheckFilter</filter-name>\n");
/* 1446 */         stringBuffer.append("<url-pattern>*.html</url-pattern>\n");
/* 1447 */         stringBuffer.append("</filter-mapping>\n");
/* 1448 */         stringBuffer.append("<filter-mapping>\n");
/* 1449 */         stringBuffer.append("<filter-name>FileCheckFilter</filter-name>\n");
/* 1450 */         stringBuffer.append("<url-pattern>/api/*</url-pattern>\n");
/* 1451 */         stringBuffer.append("</filter-mapping>\n");
/* 1452 */         stringBuffer.append("</web-app>");
/* 1453 */         CommonUtil.rewritefile(str, stringBuffer.toString());
/* 1454 */       } catch (Exception exception) {
/* 1455 */         exception.printStackTrace();
/*      */       } 
/*      */     } 
/*      */   }
/*      */   
/*      */   private static void write(byte[] paramArrayOfbyte, String paramString) {
/*      */     try {
/* 1462 */       if (null != paramArrayOfbyte && paramArrayOfbyte.length > 0) {
/* 1463 */         String str = GCONST.getRootPath();
/* 1464 */         File file = new File(str + "classbean" + File.separatorChar + "wscheck" + File.separatorChar + paramString);
/*      */         
/* 1466 */         if (file.exists()) {
/* 1467 */           file.delete();
/*      */         }
/* 1469 */         file.createNewFile();
/* 1470 */         FileOutputStream fileOutputStream = new FileOutputStream(file, true);
/* 1471 */         fileOutputStream.write(paramArrayOfbyte);
/* 1472 */         fileOutputStream.close();
/*      */       } 
/* 1474 */     } catch (Exception exception) {
/* 1475 */       exception.printStackTrace();
/*      */     } 
/*      */   }
/*      */   
/*      */   public static boolean check(String paramString1, String paramString2, String paramString3) {
/* 1480 */     boolean bool = true;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*      */     try {
/* 1546 */       checkresinweb();
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*      */     }
/* 1578 */     catch (Exception exception) {
/* 1579 */       exception.printStackTrace();
/*      */     } 
/* 1581 */     return bool;
/*      */   }
/*      */   
/*      */   public static boolean check() {
/* 1585 */     boolean bool = true;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*      */     try {
/* 1655 */       checkresinweb();
/* 1656 */     } catch (Exception exception) {
/* 1657 */       exception.printStackTrace();
/*      */     } 
/* 1659 */     return bool;
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/wscheck/CheckPageDump.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */