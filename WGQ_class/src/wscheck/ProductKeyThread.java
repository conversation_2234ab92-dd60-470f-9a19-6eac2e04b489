/*     */ package wscheck;
/*     */ 
/*     */ import java.io.File;
/*     */ import java.util.Date;
/*     */ import java.util.concurrent.Callable;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.TimeUtil;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ProductKeyThread
/*     */   implements Callable<String>
/*     */ {
/*     */   private String path;
/*     */   private String prefix;
/*     */   private String keyname;
/*     */   public static final String jsptype = ".jsp";
/*     */   public static final String javatype = ".java";
/*     */   public static final String classtype = ".class";
/*     */   public static final String jstype = ".js";
/*     */   public static final String csstype = ".css";
/*     */   public static final String jartype = ".jar";
/*  43 */   CheckScanAllFile checkScanAllFile = new CheckScanAllFile();
/*     */   
/*     */   public String getMD5File(String paramString1, String paramString2, String paramString3) {
/*  46 */     String str1 = GCONST.getRootPath();
/*  47 */     String str2 = "data" + File.separatorChar + "tempkey" + File.separatorChar + paramString3 + ".key";
/*  48 */     File file1 = new File(str1 + str2);
/*  49 */     if (file1.exists()) {
/*  50 */       file1.delete();
/*     */     }
/*  52 */     File file2 = new File(paramString1);
/*  53 */     setFileMap(file2, paramString2, paramString3);
/*  54 */     return str1 + str2;
/*     */   }
/*     */   
/*     */   public void setFileMap(File paramFile, String paramString1, String paramString2) {
/*  58 */     File[] arrayOfFile = paramFile.listFiles();
/*     */     
/*  60 */     boolean bool = false;
/*  61 */     for (byte b = 0; arrayOfFile != null && b < arrayOfFile.length; b++) {
/*  62 */       String str1 = arrayOfFile[b].getPath();
/*  63 */       String str2 = str1.substring(paramString1.length());
/*     */       
/*  65 */       if (str2.indexOf("" + File.separatorChar) == -1 || str2.indexOf("" + File.separatorChar) > 0) {
/*  66 */         str2 = "" + File.separatorChar + str2;
/*     */       }
/*     */       
/*  69 */       str2 = str2.replaceAll("\\\\", "/");
/*  70 */       if (arrayOfFile[b].isFile()) {
/*  71 */         bool = false;
/*  72 */         if (str1.endsWith(".jsp") || str1.endsWith(".class") || str1.endsWith(".js") || str1.endsWith(".java") || str1.endsWith(".css") || str1.endsWith(".jar")) {
/*     */           
/*  74 */           bool = this.checkScanAllFile.checkExtendFile(str2);
/*  75 */           if (bool) {
/*     */             continue;
/*     */           }
/*  78 */           String str3 = str2.substring(0, str2.lastIndexOf(".")).toLowerCase();
/*  79 */           if (str3.endsWith("bak") || str3.contains("副本") || str3.contains("复制") || str3.contains("复件") || str3.contains("备份")) {
/*     */             continue;
/*     */           }
/*  82 */           String str4 = getMD5(arrayOfFile[b]);
/*  83 */           Date date = new Date(arrayOfFile[b].lastModified());
/*  84 */           String str5 = TimeUtil.getDateString(date);
/*  85 */           writeFile(SecurityHelper.encrypt("weaververify", str2 + "==" + str4 + "==" + str5), paramString2);
/*     */         } 
/*     */       } 
/*     */       
/*  89 */       if (arrayOfFile[b].isDirectory() && (
/*  90 */         str2.indexOf("/WEB-INF") != 0 || str2.replace("/WEB-INF", "").equals("") || str2.replace("/WEB-INF", "").equals("/") || str2.indexOf("/lib") >= 0) && str2.indexOf("/wscheck") <= 0 && str2.indexOf("/keygenerator") != 0 && str2
/*  91 */         .indexOf("/updatetemp") != 0 && str2.indexOf("/src") != 0 && str2.indexOf("/data") != 0 && str2.indexOf("/sqlupgrade") != 0 && str2.indexOf("/jsp/") != 0 && str2.indexOf("_ubak") <= 0 && !str2.contains("副本") && 
/*  92 */         !str2.toLowerCase().endsWith("bak") && !str2.contains("复件") && !str2.contains("备份") && !str2.contains("复制"))
/*     */       {
/*     */         
/*  95 */         setFileMap(arrayOfFile[b], paramString1, paramString2);
/*     */       }
/*     */       continue;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private void writeFile(String paramString1, String paramString2) {
/* 104 */     String str1 = GCONST.getRootPath();
/* 105 */     String str2 = "tempkey";
/* 106 */     String str3 = "data" + File.separatorChar + str2 + File.separatorChar + paramString2 + ".key";
/* 107 */     CommonUtil.fileAppend(str1 + str3, paramString1);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getMD5(File paramFile) {
/* 120 */     String str = "";
/*     */     try {
/* 122 */       str = MD5Coder.fileMD5(paramFile);
/* 123 */     } catch (Exception exception) {
/* 124 */       exception.printStackTrace();
/* 125 */       str = "";
/*     */     } 
/*     */     
/* 128 */     return str;
/*     */   }
/*     */   
/*     */   public String getPath() {
/* 132 */     return this.path;
/*     */   }
/*     */   
/*     */   public void setPath(String paramString) {
/* 136 */     this.path = paramString;
/*     */   }
/*     */   
/*     */   public String getPrefix() {
/* 140 */     return this.prefix;
/*     */   }
/*     */   
/*     */   public void setPrefix(String paramString) {
/* 144 */     this.prefix = paramString;
/*     */   }
/*     */   
/*     */   public String getKeyname() {
/* 148 */     return this.keyname;
/*     */   }
/*     */   
/*     */   public void setKeyname(String paramString) {
/* 152 */     this.keyname = paramString;
/*     */   }
/*     */ 
/*     */   
/*     */   public ProductKeyThread(String paramString1, String paramString2, String paramString3) {
/* 157 */     this.path = paramString1;
/* 158 */     this.prefix = paramString2;
/* 159 */     this.keyname = paramString3;
/*     */   }
/*     */ 
/*     */   
/*     */   public String call() throws Exception {
/* 164 */     return getMD5File(this.path, this.prefix, this.keyname);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/wscheck/ProductKeyThread.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */