/*    */ package wscheck;
/*    */ 
/*    */ import java.util.ArrayList;
/*    */ import java.util.HashMap;
/*    */ import java.util.List;
/*    */ 
/*    */ public class PageErrorMap {
/*  8 */   private static HashMap map = new HashMap<>();
/*  9 */   private static List filelist = new ArrayList();
/* 10 */   private static List adminlist = new ArrayList();
/*    */   
/*    */   public static String get(String paramString) {
/* 13 */     return CommonUtil.getString((String)map.get(paramString));
/*    */   }
/*    */   
/*    */   public static void set(String paramString1, String paramString2) {
/* 17 */     map.put(paramString1, paramString2);
/*    */   }
/*    */   
/*    */   public static List getList() {
/* 21 */     return filelist;
/*    */   }
/*    */   
/*    */   public static void add(String paramString) {
/* 25 */     filelist.add(paramString);
/*    */   }
/*    */   
/*    */   public static void delete(String paramString) {
/* 29 */     filelist.remove(paramString);
/*    */   }
/*    */   
/*    */   public static void remove(String paramString) {
/* 33 */     map.remove(paramString);
/*    */   }
/*    */   
/*    */   public static boolean isEmpty() {
/* 37 */     return map.isEmpty();
/*    */   }
/*    */   
/*    */   public static boolean containsKey(String paramString) {
/* 41 */     return map.containsKey(paramString);
/*    */   }
/*    */   
/*    */   public static int size() {
/* 45 */     return map.size();
/*    */   }
/*    */   
/*    */   public static void clear() {
/* 49 */     map.clear();
/*    */   }
/*    */   
/*    */   public static boolean check(String paramString) {
/* 53 */     boolean bool = false;
/* 54 */     if (PageMD5Map.check()) {
/* 55 */       if (!get(MD5Coder.stringMD5(paramString)).equals("")) {
/* 56 */         bool = false;
/*    */       } else {
/* 58 */         bool = true;
/*    */       } 
/*    */     }
/*    */     
/* 62 */     return bool;
/*    */   }
/*    */   
/*    */   public static List getAdminlist() {
/* 66 */     return adminlist;
/*    */   }
/*    */   
/*    */   public static void setAdminlist(List paramList) {
/* 70 */     adminlist = paramList;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/wscheck/PageErrorMap.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */