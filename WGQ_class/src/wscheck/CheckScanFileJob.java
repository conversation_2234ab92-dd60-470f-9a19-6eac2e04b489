/*    */ package wscheck;
/*    */ 
/*    */ import java.lang.reflect.Method;
/*    */ import org.quartz.Job;
/*    */ import org.quartz.JobExecutionContext;
/*    */ import org.quartz.JobExecutionException;
/*    */ 
/*    */ 
/*    */ public class CheckScanFileJob
/*    */   implements Job
/*    */ {
/*    */   public void execute(JobExecutionContext paramJobExecutionContext) throws JobExecutionException {
/*    */     try {
/* 14 */       Class<?> clazz1 = Class.forName("wscheck.KeyUpgrade");
/* 15 */       Object object1 = clazz1.newInstance();
/* 16 */       Method method1 = clazz1.getMethod("execute", new Class[0]);
/* 17 */       method1.invoke(object1, null);
/*    */ 
/*    */       
/* 20 */       Class<?> clazz2 = Class.forName("wscheck.CheckScanFile");
/* 21 */       Object object2 = clazz2.newInstance();
/* 22 */       Method method2 = clazz2.getMethod("execute", new Class[0]);
/* 23 */       method2.invoke(object2, null);
/* 24 */     } catch (Exception exception) {
/* 25 */       exception.printStackTrace();
/*    */     } 
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public void invoke() {
/* 32 */     (new CheckScanFile()).execute();
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/wscheck/CheckScanFileJob.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */