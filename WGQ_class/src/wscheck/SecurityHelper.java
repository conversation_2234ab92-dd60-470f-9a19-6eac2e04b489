/*     */ package wscheck;
/*     */ 
/*     */ import java.io.File;
/*     */ import java.io.FileInputStream;
/*     */ import java.io.FileOutputStream;
/*     */ import java.io.IOException;
/*     */ import java.security.MessageDigest;
/*     */ import java.security.SecureRandom;
/*     */ import javax.crypto.Cipher;
/*     */ import javax.crypto.CipherInputStream;
/*     */ import javax.crypto.CipherOutputStream;
/*     */ import javax.crypto.KeyGenerator;
/*     */ import javax.crypto.SecretKey;
/*     */ import javax.crypto.SecretKeyFactory;
/*     */ import javax.crypto.spec.PBEKeySpec;
/*     */ import javax.crypto.spec.PBEParameterSpec;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SecurityHelper
/*     */ {
/*     */   private static final int ITERATIONS = 20;
/*     */   
/*     */   public static String encrypt(String paramString1, String paramString2) {
/*  26 */     String str = "";
/*     */     try {
/*  28 */       byte[] arrayOfByte1 = new byte[8];
/*  29 */       MessageDigest messageDigest = MessageDigest.getInstance("MD5");
/*  30 */       messageDigest.update(paramString1.getBytes());
/*  31 */       byte[] arrayOfByte2 = messageDigest.digest();
/*  32 */       for (byte b = 0; b < 8; b++) {
/*  33 */         arrayOfByte1[b] = arrayOfByte2[b];
/*     */       }
/*  35 */       PBEKeySpec pBEKeySpec = new PBEKeySpec(paramString1.toCharArray());
/*     */       
/*  37 */       SecretKeyFactory secretKeyFactory = SecretKeyFactory.getInstance("PBEWithMD5AndDES");
/*  38 */       SecretKey secretKey = secretKeyFactory.generateSecret(pBEKeySpec);
/*  39 */       PBEParameterSpec pBEParameterSpec = new PBEParameterSpec(arrayOfByte1, 20);
/*  40 */       Cipher cipher = Cipher.getInstance("PBEWithMD5AndDES");
/*  41 */       cipher.init(1, secretKey, pBEParameterSpec);
/*  42 */       byte[] arrayOfByte3 = cipher.doFinal(paramString2.getBytes());
/*  43 */       String str1 = new String(Base64.encode(arrayOfByte1));
/*  44 */       String str2 = new String(Base64.encode(arrayOfByte3));
/*  45 */       return str1 + str2;
/*  46 */     } catch (Exception exception) {
/*  47 */       exception.printStackTrace();
/*     */       
/*  49 */       return "";
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String decrypt(String paramString1, String paramString2) {
/*  62 */     byte b = 12;
/*     */     try {
/*  64 */       String str1 = paramString2.substring(0, b);
/*  65 */       String str2 = paramString2.substring(b, paramString2
/*  66 */           .length());
/*  67 */       byte[] arrayOfByte1 = Base64.decode(str1.getBytes());
/*  68 */       byte[] arrayOfByte2 = Base64.decode(str2.getBytes());
/*  69 */       PBEKeySpec pBEKeySpec = new PBEKeySpec(paramString1.toCharArray());
/*     */       
/*  71 */       SecretKeyFactory secretKeyFactory = SecretKeyFactory.getInstance("PBEWithMD5AndDES");
/*  72 */       SecretKey secretKey = secretKeyFactory.generateSecret(pBEKeySpec);
/*  73 */       PBEParameterSpec pBEParameterSpec = new PBEParameterSpec(arrayOfByte1, 20);
/*     */       
/*  75 */       Cipher cipher = Cipher.getInstance("PBEWithMD5AndDES");
/*  76 */       cipher.init(2, secretKey, pBEParameterSpec);
/*  77 */       byte[] arrayOfByte3 = cipher.doFinal(arrayOfByte2);
/*  78 */       return new String(arrayOfByte3);
/*  79 */     } catch (Exception exception) {
/*  80 */       exception.printStackTrace();
/*     */       
/*  82 */       return "";
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean encrypt(File paramFile, String paramString1, String paramString2) {
/*  92 */     FileInputStream fileInputStream = null;
/*  93 */     FileOutputStream fileOutputStream = null;
/*  94 */     CipherInputStream cipherInputStream = null;
/*     */     try {
/*  96 */       Cipher cipher = Cipher.getInstance("DES");
/*  97 */       KeyGenerator keyGenerator = KeyGenerator.getInstance("DES");
/*  98 */       keyGenerator.init(new SecureRandom(paramString2.getBytes()));
/*  99 */       SecretKey secretKey = keyGenerator.generateKey();
/* 100 */       keyGenerator = null;
/* 101 */       cipher.init(1, secretKey);
/* 102 */       fileInputStream = new FileInputStream(paramFile);
/* 103 */       fileOutputStream = new FileOutputStream(paramString1);
/* 104 */       cipherInputStream = new CipherInputStream(fileInputStream, cipher);
/* 105 */       byte[] arrayOfByte1 = new byte[1024];
/*     */ 
/*     */ 
/*     */       
/* 109 */       byte[] arrayOfByte2 = paramString2.getBytes();
/* 110 */       fileOutputStream.write(arrayOfByte2, 0, arrayOfByte2.length); int i;
/* 111 */       while ((i = cipherInputStream.read(arrayOfByte1)) > 0) {
/* 112 */         fileOutputStream.write(arrayOfByte1, 0, i);
/*     */       }
/* 114 */       cipherInputStream.close();
/* 115 */       fileInputStream.close();
/* 116 */       fileOutputStream.close();
/* 117 */       return true;
/* 118 */     } catch (Exception exception) {
/* 119 */       exception.printStackTrace();
/* 120 */       return false;
/*     */     } finally {
/* 122 */       if (cipherInputStream != null) {
/*     */         try {
/* 124 */           cipherInputStream.close();
/* 125 */         } catch (IOException iOException) {}
/*     */       }
/*     */       
/* 128 */       if (fileOutputStream != null) {
/*     */         try {
/* 130 */           fileOutputStream.close();
/* 131 */         } catch (IOException iOException) {}
/*     */       }
/*     */       
/* 134 */       if (fileInputStream != null) {
/*     */         try {
/* 136 */           fileInputStream.close();
/* 137 */         } catch (IOException iOException) {}
/*     */       }
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean decrypt(File paramFile, String paramString1, String paramString2) {
/* 152 */     FileInputStream fileInputStream = null;
/* 153 */     FileOutputStream fileOutputStream = null;
/* 154 */     CipherOutputStream cipherOutputStream = null;
/*     */     try {
/* 156 */       Cipher cipher = Cipher.getInstance("DES");
/* 157 */       KeyGenerator keyGenerator = KeyGenerator.getInstance("DES");
/* 158 */       keyGenerator.init(new SecureRandom(paramString2.getBytes()));
/* 159 */       SecretKey secretKey = keyGenerator.generateKey();
/* 160 */       keyGenerator = null;
/* 161 */       cipher.init(2, secretKey);
/* 162 */       fileInputStream = new FileInputStream(paramFile);
/* 163 */       fileOutputStream = new FileOutputStream(paramString1);
/* 164 */       cipherOutputStream = new CipherOutputStream(fileOutputStream, cipher);
/*     */       
/* 166 */       byte[] arrayOfByte1 = new byte[1024];
/*     */ 
/*     */       
/* 169 */       int j = (paramString2.getBytes()).length;
/* 170 */       byte[] arrayOfByte2 = new byte[j]; int i;
/* 171 */       if ((i = fileInputStream.read(arrayOfByte2)) >= 0) {
/* 172 */         String str = new String(arrayOfByte2);
/* 173 */         System.out.println("补丁包加密.....headerContent:" + str);
/* 174 */         if (paramString2.equals(str)) {
/* 175 */           System.out.println("补丁包加密.....");
/*     */         } else {
/*     */           
/* 178 */           fileInputStream = new FileInputStream(paramFile);
/*     */         } 
/*     */       } 
/*     */       
/* 182 */       while ((i = fileInputStream.read(arrayOfByte1)) >= 0) {
/* 183 */         cipherOutputStream.write(arrayOfByte1, 0, i);
/*     */       }
/*     */       
/* 186 */       cipherOutputStream.close();
/* 187 */       fileOutputStream.close();
/* 188 */       fileInputStream.close();
/* 189 */       return true;
/* 190 */     } catch (Exception exception) {
/* 191 */       exception.printStackTrace();
/* 192 */       return false;
/*     */     } finally {
/* 194 */       if (cipherOutputStream != null) {
/*     */         try {
/* 196 */           cipherOutputStream.close();
/* 197 */         } catch (IOException iOException) {}
/*     */       }
/*     */       
/* 200 */       if (fileOutputStream != null) {
/*     */         try {
/* 202 */           fileOutputStream.close();
/* 203 */         } catch (IOException iOException) {}
/*     */       }
/*     */       
/* 206 */       if (fileInputStream != null) {
/*     */         try {
/* 208 */           fileInputStream.close();
/* 209 */         } catch (IOException iOException) {}
/*     */       }
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void main(String[] paramArrayOfString) {
/* 224 */     String str1 = MD5Coder.stringMD5("test") + "=" + MD5Coder.stringMD5("test");
/* 225 */     String str2 = "";
/*     */     
/*     */     try {
/* 228 */       str2 = encrypt("weaververify", str1);
/*     */       
/* 230 */       str1 = decrypt("weaververify", "MQH5E9uKXes=NSG7V8+zZx2mb+qf9mkxupzLihsHY+ema/5MYWNa/+DWh4Utrgt4Z+YQ8vf0ZU6l00LskJxOgvSZl8T85PScBL0Rxb2f2py8");
/*     */ 
/*     */       
/* 233 */       String str3 = "E:/workspace/ecology_70/ecology/filesystem/updatetemp/A5641221564035190986/ecology";
/* 234 */       String str4 = "E:/workspace/ecology_70/ecology/filesystem/updatetemp/A5641221564035190986";
/* 235 */       String str5 = str3.replaceAll(str4, "");
/*     */     
/*     */     }
/* 238 */     catch (Exception exception) {
/* 239 */       exception.printStackTrace();
/* 240 */       System.exit(-1);
/*     */     } 
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/wscheck/SecurityHelper.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */