/*     */ package wscheck;
/*     */ 
/*     */ import java.io.File;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import org.apache.commons.logging.Log;
/*     */ import org.apache.commons.logging.LogFactory;
/*     */ import weaver.general.GCONST;
/*     */ 
/*     */ 
/*     */ 
/*     */ public class PageMD5Map
/*     */ {
/*  14 */   private static final Log log = LogFactory.getLog(PageMD5Map.class);
/*     */   
/*     */   private static boolean inited = false;
/*     */   private static boolean dupinit = false;
/*     */   private static boolean fileexist = true;
/*  19 */   private static HashMap map = new HashMap<>();
/*     */ 
/*     */ 
/*     */   
/*     */   public static String get(String paramString) {
/*  24 */     return CommonUtil.getString((String)map.get(paramString));
/*     */   }
/*     */ 
/*     */   
/*     */   public static void set(String paramString1, String paramString2) {
/*  29 */     map.put(paramString1, paramString2);
/*     */   }
/*     */ 
/*     */   
/*     */   public static void remove(String paramString) {
/*  34 */     map.remove(paramString);
/*     */   }
/*     */ 
/*     */   
/*     */   public static boolean isEmpty() {
/*  39 */     return map.isEmpty();
/*     */   }
/*     */ 
/*     */   
/*     */   public static void clear() {
/*  44 */     map.clear();
/*     */   }
/*     */ 
/*     */   
/*     */   public static void initMD5Map() {
/*  49 */     if (check()) {
/*  50 */       clear();
/*  51 */       PageErrorMap.clear();
/*  52 */       String str1 = GCONST.getRootPath();
/*  53 */       String str2 = "data" + File.separatorChar + "key" + File.separatorChar + "ecology.key";
/*  54 */       File file = new File(str1 + str2);
/*  55 */       if (!file.exists()) {
/*     */         
/*  57 */         fileexist = false;
/*  58 */         CheckKey.keyerror(0);
/*  59 */         fileexist = true;
/*     */       } else {
/*  61 */         boolean bool = CheckKey.verifykey();
/*     */         
/*  63 */         if (!bool) {
/*     */           return;
/*     */         }
/*  66 */         ArrayList<String> arrayList = CommonUtil.getLineList(file);
/*     */         
/*  68 */         if (arrayList.size() > 0) {
/*  69 */           for (byte b = 0; b < arrayList.size(); b++) {
/*  70 */             String str = arrayList.get(b);
/*  71 */             if (!"".equals(str)) {
/*     */ 
/*     */ 
/*     */               
/*  75 */               str = SecurityHelper.decrypt("weaververify", str);
/*     */               
/*  77 */               if (str != null && !str.equals("")) {
/*  78 */                 String[] arrayOfString = str.split("=");
/*  79 */                 if (arrayOfString.length == 2) {
/*  80 */                   if (!get(arrayOfString[0]).equals("")) {
/*  81 */                     if (!isinitdata(arrayOfString[0])) {
/*  82 */                       remove(arrayOfString[0]);
/*  83 */                       set(arrayOfString[0], arrayOfString[1]);
/*     */                     }
/*     */                   
/*     */                   }
/*  87 */                   else if (PageErrorMap.get(arrayOfString[0]).equals("")) {
/*  88 */                     set(arrayOfString[0], arrayOfString[1]);
/*     */                   
/*     */                   }
/*     */ 
/*     */                 
/*     */                 }
/*  94 */                 else if (arrayOfString.length == 1) {
/*     */                 
/*     */                 } 
/*     */               } 
/*     */             } 
/*     */           } 
/*     */         }
/*     */       } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 113 */       inited = true;
/*     */     
/*     */     }
/*     */     else {
/*     */       
/* 118 */       dupinit = true;
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   private static boolean isinitdata(String paramString) {
/* 124 */     if (paramString.equalsIgnoreCase(MD5Coder.stringMD5("createdate")))
/* 125 */       return true; 
/* 126 */     if (paramString.equalsIgnoreCase(MD5Coder.stringMD5("createversion")))
/* 127 */       return true; 
/* 128 */     if (paramString.equalsIgnoreCase(MD5Coder.stringMD5("weaver")))
/* 129 */       return true; 
/* 130 */     if (paramString.equalsIgnoreCase(MD5Coder.stringMD5("test"))) {
/* 131 */       return true;
/*     */     }
/* 133 */     return false;
/*     */   }
/*     */   
/*     */   public static void checkMD5Map() {
/* 137 */     if (check()) {
/* 138 */       clear();
/* 139 */       PageErrorMap.clear();
/* 140 */       String str1 = GCONST.getRootPath();
/* 141 */       String str2 = "data" + File.separatorChar + "key" + File.separatorChar + "ecology.key";
/* 142 */       File file = new File(str1 + str2);
/* 143 */       if (!file.exists()) {
/*     */         
/* 145 */         fileexist = false;
/*     */         
/* 147 */         fileexist = true;
/*     */       } else {
/* 149 */         ArrayList<String> arrayList = CommonUtil.getLineList(file);
/*     */         
/* 151 */         if (arrayList.size() > 0) {
/* 152 */           for (byte b = 0; b < arrayList.size(); b++) {
/* 153 */             String str = arrayList.get(b);
/* 154 */             if (!"".equals(str)) {
/*     */ 
/*     */ 
/*     */               
/* 158 */               str = SecurityHelper.decrypt("weaververify", str);
/*     */               
/* 160 */               if (str != null && !str.equals("")) {
/* 161 */                 String[] arrayOfString = str.split("=");
/* 162 */                 if (arrayOfString.length == 2) {
/* 163 */                   if (!get(arrayOfString[0]).equals("")) {
/* 164 */                     if (!isinitdata(arrayOfString[0])) {
/* 165 */                       remove(arrayOfString[0]);
/* 166 */                       set(arrayOfString[0], arrayOfString[1]);
/*     */                     }
/*     */                   
/*     */                   }
/* 170 */                   else if (PageErrorMap.get(arrayOfString[0]).equals("")) {
/* 171 */                     set(arrayOfString[0], arrayOfString[1]);
/*     */                   
/*     */                   }
/*     */ 
/*     */                 
/*     */                 }
/* 177 */                 else if (arrayOfString.length == 1) {
/*     */                 
/*     */                 } 
/*     */               } 
/*     */             } 
/*     */           } 
/*     */         }
/*     */       } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 196 */       inited = true;
/*     */     
/*     */     }
/*     */     else {
/*     */       
/* 201 */       dupinit = true;
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public static boolean check() {
/* 207 */     boolean bool = false;
/* 208 */     if (!inited) {
/* 209 */       if (!dupinit) {
/* 210 */         if (isEmpty()) {
/* 211 */           if (fileexist) {
/* 212 */             bool = true;
/*     */           } else {
/* 214 */             bool = false;
/*     */           } 
/*     */         } else {
/* 217 */           bool = false;
/*     */         } 
/*     */       } else {
/* 220 */         bool = false;
/*     */       }
/*     */     
/* 223 */     } else if (!dupinit) {
/* 224 */       if (isEmpty()) {
/* 225 */         bool = false;
/*     */       }
/* 227 */       else if (fileexist) {
/* 228 */         bool = true;
/*     */       } else {
/* 230 */         bool = false;
/*     */       } 
/*     */     } else {
/*     */       
/* 234 */       bool = false;
/*     */     } 
/*     */ 
/*     */     
/* 238 */     return bool;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/wscheck/PageMD5Map.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */