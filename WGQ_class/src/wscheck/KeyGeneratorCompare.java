/*     */ package wscheck;
/*     */ import java.io.BufferedInputStream;
/*     */ import java.io.BufferedOutputStream;
/*     */ import java.io.BufferedReader;
/*     */ import java.io.BufferedWriter;
/*     */ import java.io.ByteArrayOutputStream;
/*     */ import java.io.File;
/*     */ import java.io.FileOutputStream;
/*     */ import java.io.IOException;
/*     */ import java.io.InputStream;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Iterator;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.Set;
/*     */ import java.util.TreeMap;
/*     */ import java.util.Vector;
/*     */ import org.apache.commons.httpclient.HttpClient;
/*     */ import org.apache.commons.httpclient.NameValuePair;
/*     */ import org.apache.commons.httpclient.methods.PostMethod;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.file.FileUpload;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ public class KeyGeneratorCompare extends BaseBean {
/*  29 */   public static String secondarydirname = getSecondDirectoryName();
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  34 */   public static Map<String, String> filePathInfo = new HashMap<>();
/*     */ 
/*     */ 
/*     */   
/*  38 */   public static List allKeylist = new ArrayList();
/*     */ 
/*     */ 
/*     */   
/*  42 */   private static TreeMap localmaptemp = new TreeMap<>();
/*     */   
/*     */   private static Map backupMapTemp;
/*     */   
/*  46 */   public static int flag = 0;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private Map getCompareMap(File paramFile) {
/*  55 */     Map map = null;
/*  56 */     this; map = getMD5Map(paramFile);
/*  57 */     return map;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static Map getMD5Map(File paramFile) {
/*  68 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*  69 */     if (paramFile.exists()) {
/*     */ 
/*     */       
/*  72 */       ArrayList<String> arrayList = CommonUtil.getLineList(paramFile);
/*  73 */       if (arrayList.size() > 0) {
/*  74 */         for (byte b = 0; b < arrayList.size(); b++) {
/*  75 */           String str = arrayList.get(b);
/*  76 */           if (!"".equals(str)) {
/*     */ 
/*     */ 
/*     */             
/*  80 */             str = SecurityHelper.decrypt("weaververify", str);
/*     */             
/*  82 */             if (str != null && !str.equals("")) {
/*  83 */               String[] arrayOfString = str.split("==");
/*  84 */               if (arrayOfString.length > 2) {
/*  85 */                 hashMap.put(arrayOfString[0], arrayOfString[1] + "==" + arrayOfString[2]);
/*     */               } else {
/*  87 */                 hashMap.put(arrayOfString[0], arrayOfString[1]);
/*     */               } 
/*     */             } 
/*     */           } 
/*     */         } 
/*     */       }
/*     */     } 
/*  94 */     return hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean checkFileSize(FileUpload paramFileUpload) {
/* 104 */     RecordSet recordSet = new RecordSet();
/* 105 */     boolean bool = true;
/* 106 */     int i = Util.getIntValue(paramFileUpload.uploadFiles("filepath"), 0);
/*     */     
/* 108 */     String str1 = "select filerealpath from imagefile where imagefileid = " + i;
/* 109 */     recordSet.execute(str1);
/* 110 */     String str2 = "";
/*     */ 
/*     */     
/* 113 */     if (recordSet.next()) {
/* 114 */       str2 = recordSet.getString("filerealpath");
/*     */     }
/*     */ 
/*     */     
/* 118 */     if (!str2.equals("")) {
/* 119 */       File file = new File(str2);
/* 120 */       if (null != file) {
/* 121 */         long l = file.length() / 1024L / 1024L;
/* 122 */         if (l > 20L) {
/* 123 */           bool = false;
/*     */         }
/*     */       } 
/*     */     } 
/*     */     
/* 128 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean checkFileSize2(FileUpload paramFileUpload, String paramString) {
/* 136 */     String str = "";
/* 137 */     boolean bool = true;
/* 138 */     if (!"".equals(paramString)) {
/* 139 */       str = paramString;
/*     */     } else {
/* 141 */       RecordSet recordSet = new RecordSet();
/* 142 */       int i = Util.getIntValue(paramFileUpload.uploadFiles("filepath"), 0);
/* 143 */       String str1 = "select filerealpath from imagefile where imagefileid = " + i;
/* 144 */       recordSet.execute(str1);
/* 145 */       if (recordSet.next()) {
/* 146 */         str = recordSet.getString("filerealpath");
/*     */       }
/*     */     } 
/* 149 */     if (!"".equals(str)) {
/* 150 */       File file = new File(str);
/* 151 */       if (file.exists()) {
/* 152 */         long l = file.length() / 1024L / 1024L;
/* 153 */         if (l > 20L) {
/* 154 */           bool = false;
/*     */         }
/*     */       } 
/*     */     } 
/*     */     
/* 159 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map getCompareMap2(FileUpload paramFileUpload, String paramString) {
/* 170 */     keyFileDecrypt(paramFileUpload, paramString);
/*     */     
/* 172 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 173 */     String str = GCONST.getRootPath() + File.separatorChar + "keygenerator" + File.separatorChar + "ecologykeyss.txt";
/* 174 */     File file = new File(str);
/* 175 */     if (file.exists() && file.length() > 0L) {
/* 176 */       BufferedReader bufferedReader = null;
/*     */       try {
/* 178 */         bufferedReader = new BufferedReader(new InputStreamReader(new FileInputStream(str)));
/* 179 */         String str1 = "";
/* 180 */         while ((str1 = bufferedReader.readLine()) != null) {
/* 181 */           if (!str1.equals("")) {
/* 182 */             String[] arrayOfString = str1.split("==");
/* 183 */             if (arrayOfString.length > 2) {
/* 184 */               hashMap.put(arrayOfString[0], arrayOfString[1] + "==" + arrayOfString[2]); continue;
/*     */             } 
/* 186 */             hashMap.put(arrayOfString[0], arrayOfString[1]);
/*     */           } 
/*     */         } 
/*     */         
/* 190 */         bufferedReader.close();
/* 191 */       } catch (Exception exception) {
/* 192 */         exception.printStackTrace();
/*     */       } finally {
/*     */         try {
/* 195 */           if (bufferedReader != null) {
/* 196 */             bufferedReader.close();
/*     */           }
/* 198 */         } catch (Exception exception) {
/* 199 */           exception.printStackTrace();
/*     */         } 
/*     */       } 
/*     */     } 
/*     */     
/* 204 */     return hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map getCompareMap(FileUpload paramFileUpload, HttpServletResponse paramHttpServletResponse) {
/*     */     try {
/* 216 */       BaseBean baseBean = new BaseBean();
/* 217 */       String str = Util.null2String(baseBean.getPropValue("upgradesetting", "closeautoupdateextendtxt"));
/* 218 */       if (!"1".equals(str)) {
/* 219 */         remoteUpdateExtendtxt();
/*     */       }
/* 221 */     } catch (Exception exception) {
/* 222 */       exception.printStackTrace();
/*     */     } 
/*     */     
/* 225 */     Map map = null;
/*     */     try {
/* 227 */       RecordSet recordSet = new RecordSet();
/* 228 */       int i = Util.getIntValue(paramFileUpload.uploadFiles("filepath"), 0);
/*     */       
/* 230 */       String str1 = "select * from imagefile where imagefileid = " + i;
/* 231 */       recordSet.execute(str1);
/* 232 */       String str2 = "";
/* 233 */       String str3 = "";
/* 234 */       String str4 = "";
/* 235 */       if (recordSet.next()) {
/* 236 */         str2 = recordSet.getString("filerealpath");
/* 237 */         str3 = Util.null2String(recordSet.getString("isaesencrypt"));
/* 238 */         str4 = Util.null2String(recordSet.getString("aescode"));
/*     */       } 
/* 240 */       String str5 = GCONST.getRootPath();
/* 241 */       File file1 = new File(str5 + "data" + File.separatorChar + "tempkey" + File.separatorChar);
/* 242 */       if (!file1.exists()) {
/* 243 */         file1.mkdirs();
/*     */       }
/* 245 */       String str6 = "data" + File.separatorChar + "tempkey" + File.separatorChar + "ecologycheck.key";
/* 246 */       File file2 = new File(str5 + str6);
/* 247 */       if (!str2.equals("")) {
/* 248 */         File file = new File(str2);
/* 249 */         InputStream inputStream = null;
/* 250 */         inputStream = new BufferedInputStream(new FileInputStream(file));
/*     */         
/* 252 */         if (str3.equals("1")) {
/* 253 */           inputStream = AESCoder.decrypt(inputStream, str4);
/*     */         }
/* 255 */         FileOutputStream fileOutputStream = new FileOutputStream(file2);
/* 256 */         int j = 0;
/* 257 */         byte[] arrayOfByte = new byte[8192];
/* 258 */         while ((j = inputStream.read(arrayOfByte, 0, 8192)) != -1) {
/* 259 */           fileOutputStream.write(arrayOfByte, 0, j);
/*     */         }
/* 261 */         fileOutputStream.close();
/* 262 */         inputStream.close();
/*     */       } 
/* 264 */       if (file2.exists() && file2.length() > 0L)
/* 265 */         map = getCompareMap(file2); 
/* 266 */     } catch (Exception exception) {
/* 267 */       exception.printStackTrace();
/*     */     } 
/* 269 */     return map;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map getCompareResult(Map<?, ?> paramMap, String paramString, User paramUser, int paramInt1, int paramInt2) {
/*     */     List<String> list;
/* 279 */     CheckScanAllFile checkScanAllFile = new CheckScanAllFile(paramMap);
/* 280 */     String str1 = GCONST.getRootPath();
/* 281 */     if (!"".equals(paramString)) {
/* 282 */       str1 = paramString;
/*     */     }
/*     */     try {
/* 285 */       File file = new File(str1);
/* 286 */       if (localmaptemp.size() == 0) {
/* 287 */         checkScanAllFile.getForceFileMap(str1, "@#@", paramUser);
/* 288 */         checkScanAllFile.getFileMap3(file, str1, paramMap, paramUser, 0);
/*     */         
/* 290 */         localmaptemp = new TreeMap<>(checkScanAllFile.getMd5map());
/*     */       } 
/* 292 */       (new BaseBean()).writeLog("localMap : " + localmaptemp.size());
/* 293 */     } catch (Exception exception) {
/* 294 */       exception.printStackTrace();
/*     */     } 
/*     */     
/* 297 */     TreeMap<Object, Object> treeMap = new TreeMap<>();
/*     */ 
/*     */ 
/*     */     
/* 301 */     if (backupMapTemp == null) {
/* 302 */       backupMapTemp = new HashMap<>(paramMap);
/*     */     }
/*     */     
/* 305 */     Set<?> set = localmaptemp.keySet();
/* 306 */     if (allKeylist.size() == 0) {
/* 307 */       allKeylist = new ArrayList(set);
/*     */     }
/* 309 */     int i = allKeylist.size();
/*     */ 
/*     */ 
/*     */     
/* 313 */     if (paramInt1 < i && paramInt2 < i) {
/* 314 */       list = allKeylist.subList(paramInt1, paramInt2);
/* 315 */     } else if (paramInt1 < i && paramInt2 > i) {
/* 316 */       list = allKeylist.subList(paramInt1, i - 1);
/*     */     } else {
/* 318 */       list = new ArrayList();
/* 319 */       flag++;
/*     */     } 
/*     */     try {
/* 322 */       for (byte b = 0; b < list.size(); b++) {
/* 323 */         String str3 = Util.null2String(list.get(b)).trim();
/* 324 */         String str4 = Util.null2String((String)localmaptemp.get(str3));
/*     */         
/* 326 */         ArrayList arrayList = Util.TokenizerString(str4, "==");
/* 327 */         String str5 = (new StringBuilder()).append(arrayList.get(0)).append("").toString();
/*     */         
/* 329 */         if (paramMap.containsKey(str3)) {
/* 330 */           String str6 = Util.null2String((String)paramMap.get(str3));
/* 331 */           ArrayList arrayList1 = Util.TokenizerString(str6, "==");
/* 332 */           String str7 = (new StringBuilder()).append(arrayList1.get(0)).append("").toString();
/*     */           
/* 334 */           String str8 = (arrayList1.size() == 2) ? (new StringBuilder()).append(arrayList1.get(1)).append("").toString() : "-";
/*     */           
/* 336 */           if (str5.compareTo(str7) != 0) {
/* 337 */             treeMap.put(str3, "2+" + str8);
/*     */           }
/* 339 */           backupMapTemp.remove(str3);
/* 340 */         } else if (!paramMap.containsKey(str3)) {
/* 341 */           treeMap.put(str3, "1");
/*     */ 
/*     */         
/*     */         }
/*     */ 
/*     */ 
/*     */       
/*     */       }
/*     */ 
/*     */ 
/*     */     
/*     */     }
/* 353 */     catch (Exception exception) {
/* 354 */       exception.printStackTrace();
/*     */     } 
/*     */     
/* 357 */     if (flag == 1) {
/*     */       
/* 359 */       Set set1 = backupMapTemp.keySet();
/* 360 */       for (Iterator<String> iterator = set1.iterator(); iterator.hasNext(); ) {
/* 361 */         String str3 = Util.null2String(iterator.next()).trim();
/*     */         
/* 363 */         String str4 = (str1 + str3).replaceAll("/", "\\\\");
/* 364 */         if (str4.indexOf("\\src4js\\") > 0 || str4.indexOf("\\src4js-mobiletool-v1\\") > 0 || str4.indexOf("\\src4js-pctool-v1\\") > 0) {
/*     */           continue;
/*     */         }
/* 367 */         treeMap.put(str3, "0");
/*     */       } 
/*     */       
/* 370 */       localmaptemp = new TreeMap<>();
/* 371 */       allKeylist = new ArrayList();
/* 372 */       backupMapTemp = null;
/*     */     } 
/*     */ 
/*     */     
/* 376 */     String str2 = getEcologyVersion();
/* 377 */     if (str2 != null) {
/* 378 */       String str = str1 + "WEB-INF\\replaceFiles\\" + str2 + "\\ecology";
/* 379 */       Iterator<Map.Entry> iterator = treeMap.entrySet().iterator();
/* 380 */       while (iterator.hasNext()) {
/* 381 */         Map.Entry entry = iterator.next();
/* 382 */         String str3 = (String)entry.getKey();
/* 383 */         File file = new File(str + str3.replaceAll("/", "\\\\"));
/* 384 */         if (file.exists()) {
/* 385 */           iterator.remove();
/*     */         }
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 392 */     return treeMap;
/*     */   }
/*     */ 
/*     */   
/*     */   private void getMapResult(Map paramMap) {
/* 397 */     String str = GCONST.getRootPath() + File.separatorChar + "keygenerator" + File.separatorChar + "ecologykeyResult.txt";
/* 398 */     if (paramMap.size() > 0) {
/* 399 */       BufferedWriter bufferedWriter = null;
/*     */ 
/*     */ 
/*     */       
/*     */       try {
/* 404 */         bufferedWriter = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(str)));
/* 405 */         for (Map.Entry entry : paramMap.entrySet()) {
/*     */           
/* 407 */           String str1 = (String)((Map.Entry)entry).getKey();
/* 408 */           String str2 = (String)entry.getValue();
/* 409 */           bufferedWriter.write("\n" + str1 + "===" + str2);
/*     */         } 
/* 411 */         bufferedWriter.flush();
/* 412 */         bufferedWriter.close();
/*     */       }
/* 414 */       catch (Exception exception) {
/* 415 */         exception.printStackTrace();
/*     */       } finally {
/*     */         try {
/* 418 */           if (bufferedWriter != null)
/* 419 */             bufferedWriter.close(); 
/* 420 */         } catch (IOException iOException) {
/* 421 */           iOException.printStackTrace();
/*     */         } 
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map getCompareResultNew(Map paramMap1, Map paramMap2) {
/* 433 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 434 */     Map map = paramMap2;
/*     */     
/* 436 */     TreeMap<Object, Object> treeMap = new TreeMap<>();
/*     */     
/* 438 */     Set set1 = paramMap1.keySet();
/* 439 */     for (Iterator<String> iterator1 = set1.iterator(); iterator1.hasNext(); ) {
/* 440 */       String str1 = Util.null2String(iterator1.next()).trim();
/* 441 */       String str2 = Util.null2String((String)paramMap1.get(str1));
/* 442 */       ArrayList arrayList = Util.TokenizerString(str2, "==");
/* 443 */       String str3 = (new StringBuilder()).append(arrayList.get(0)).append("").toString();
/*     */       
/* 445 */       String str4 = (arrayList.size() == 2) ? (new StringBuilder()).append(arrayList.get(1)).append("").toString() : "-";
/*     */       
/* 447 */       if (paramMap2.containsKey(str1)) {
/* 448 */         String str5 = Util.null2String((String)paramMap2.get(str1));
/* 449 */         ArrayList arrayList1 = Util.TokenizerString(str5, "==");
/* 450 */         String str6 = (new StringBuilder()).append(arrayList1.get(0)).append("").toString();
/*     */         
/* 452 */         String str7 = (arrayList1.size() == 2) ? (new StringBuilder()).append(arrayList1.get(1)).append("").toString() : "-";
/*     */         
/* 454 */         if (str3.compareTo(str6) != 0) {
/* 455 */           treeMap.put(str1, "2+" + str4 + "+" + str7);
/*     */         }
/* 457 */         map.remove(str1); continue;
/* 458 */       }  if (!paramMap2.containsKey(str1)) {
/* 459 */         treeMap.put(str1, "1+" + str4 + "+-");
/*     */       }
/*     */     } 
/*     */ 
/*     */     
/* 464 */     Set set2 = map.keySet();
/* 465 */     for (Iterator<String> iterator2 = set2.iterator(); iterator2.hasNext(); ) {
/* 466 */       String str1 = Util.null2String(iterator2.next()).trim();
/* 467 */       String str2 = Util.null2String((String)map.get(str1));
/*     */       
/* 469 */       String[] arrayOfString = str2.split("==");
/* 470 */       String str3 = (arrayOfString.length == 2) ? arrayOfString[1] : "-";
/* 471 */       treeMap.put(str1, "0+-+" + str3);
/*     */     } 
/* 473 */     return treeMap;
/*     */   }
/*     */ 
/*     */   
/*     */   public static String getEcologyVersion() {
/* 478 */     RecordSet recordSet = new RecordSet();
/* 479 */     recordSet.execute("select * from license");
/* 480 */     if (recordSet.next()) {
/* 481 */       String str = Util.null2String(recordSet.getString("cversion"));
/* 482 */       if (str.length() > 1) {
/* 483 */         return "E" + str.charAt(0);
/*     */       }
/*     */     } 
/* 486 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void remoteUpdateExtendtxt() {
/* 494 */     InputStream inputStream = null;
/* 495 */     PostMethod postMethod = null;
/* 496 */     String str = "";
/* 497 */     byte[] arrayOfByte = null;
/*     */     try {
/* 499 */       String str1 = "https://www.e-cology.com.cn/keygenerator/RemoteUpdateExcludeFile.jsp";
/*     */ 
/*     */       
/* 502 */       postMethod = new PostMethod(str1);
/*     */ 
/*     */ 
/*     */       
/* 506 */       NameValuePair[] arrayOfNameValuePair = new NameValuePair[1];
/* 507 */       arrayOfNameValuePair[0] = new NameValuePair("sysVersion", "9");
/* 508 */       postMethod.setRequestBody(arrayOfNameValuePair);
/* 509 */       postMethod.getParams().setParameter("http.protocol.content-charset", "GBK");
/*     */ 
/*     */       
/* 512 */       HttpClient httpClient = new HttpClient();
/* 513 */       httpClient.getHttpConnectionManager().getParams().setConnectionTimeout(3600000);
/* 514 */       httpClient.getHttpConnectionManager().getParams().setSoTimeout(3600000);
/*     */       
/* 516 */       httpClient.executeMethod((HttpMethod)postMethod);
/* 517 */       inputStream = postMethod.getResponseBodyAsStream();
/* 518 */       if (inputStream == null) {
/* 519 */         (new BaseBean()).writeLog("远程调用结果为null");
/*     */         
/*     */         return;
/*     */       } 
/* 523 */       ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
/*     */       
/* 525 */       byte[] arrayOfByte1 = new byte[1024]; int i;
/* 526 */       while ((i = inputStream.read(arrayOfByte1, 0, arrayOfByte1.length)) != -1) {
/* 527 */         byteArrayOutputStream.write(arrayOfByte1, 0, i);
/*     */       }
/*     */       
/* 530 */       arrayOfByte = byteArrayOutputStream.toByteArray();
/* 531 */       if (arrayOfByte != null) {
/* 532 */         String str2 = byteArrayOutputStream.toString().trim();
/* 533 */         if (!str2.contains("RemoteUpdateExcludeFile_flag")) {
/*     */           return;
/*     */         }
/*     */         
/* 537 */         Pattern pattern = Pattern.compile("\\s|(\r\n)\\1+");
/* 538 */         Matcher matcher = pattern.matcher(str2);
/* 539 */         str2 = matcher.replaceAll("\r\n");
/* 540 */         str2 = str2.substring(str2.indexOf("RemoteUpdateExcludeFile_flag\">") + "RemoteUpdateExcludeFile_flag\">".length());
/* 541 */         str2 = str2.substring(0, str2.indexOf("</div>"));
/*     */         
/* 543 */         String[] arrayOfString = str2.split("\r\n");
/* 544 */         for (byte b = 0; b < arrayOfString.length; b++) {
/* 545 */           if (!"".equals(arrayOfString[b]) && !arrayOfString[b].contains("\r\n"))
/* 546 */             str = str + String.format("%s\r\n", new Object[] { SecurityHelper.decrypt("weaververify", arrayOfString[b]) }); 
/*     */         } 
/*     */       } else {
/*     */         return;
/*     */       } 
/* 551 */       inputStream.close();
/* 552 */       byteArrayOutputStream.flush();
/* 553 */       byteArrayOutputStream.close();
/*     */     }
/* 555 */     catch (Exception exception) {
/* 556 */       exception.printStackTrace();
/*     */       return;
/*     */     } finally {
/*     */       try {
/* 560 */         if (postMethod != null)
/* 561 */           postMethod.releaseConnection(); 
/* 562 */       } catch (Exception exception) {}
/*     */     } 
/*     */ 
/*     */     
/* 566 */     replaceExcludeFile(str.getBytes());
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void keyFileDecrypt(FileUpload paramFileUpload, String paramString) {
/* 574 */     String str1 = "";
/* 575 */     String str2 = "";
/* 576 */     String str3 = "";
/* 577 */     if (!"".equals(paramString)) {
/* 578 */       str1 = paramString;
/*     */     } else {
/* 580 */       int i = Util.getIntValue(paramFileUpload.uploadFiles("filepath"), 0);
/* 581 */       String str = "select filerealpath,isaesencrypt,aescode from imagefile where imagefileid = " + i;
/* 582 */       RecordSet recordSet = new RecordSet();
/* 583 */       recordSet.execute(str);
/* 584 */       if (recordSet.next()) {
/* 585 */         str1 = Util.null2String(recordSet.getString("filerealpath"));
/* 586 */         str2 = Util.null2String(recordSet.getString("isaesencrypt"));
/* 587 */         str3 = Util.null2String(recordSet.getString("aescode"));
/*     */       } 
/*     */     } 
/*     */     
/* 591 */     File file = new File(str1);
/* 592 */     String str4 = GCONST.getRootPath() + File.separatorChar + "keygenerator" + File.separatorChar + "ecologykeyss.txt";
/* 593 */     if (file.exists()) {
/* 594 */       InputStream inputStream = null;
/* 595 */       BufferedReader bufferedReader = null;
/* 596 */       BufferedWriter bufferedWriter = null;
/*     */ 
/*     */       
/*     */       try {
/* 600 */         inputStream = new BufferedInputStream(new FileInputStream(file));
/* 601 */         if ("1".equals(str2)) {
/* 602 */           inputStream = AESCoder.decrypt(inputStream, str3);
/*     */         }
/* 604 */         bufferedReader = new BufferedReader(new InputStreamReader(inputStream));
/*     */         
/* 606 */         bufferedWriter = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(str4)));
/* 607 */         String str = "";
/* 608 */         while ((str = bufferedReader.readLine()) != null) {
/* 609 */           if ("".equals(str))
/*     */             continue; 
/* 611 */           str = SecurityHelper.decrypt("weaververify", str);
/* 612 */           if (str.contains("#@#")) {
/* 613 */             String[] arrayOfString = str.split("#@#");
/* 614 */             for (String str5 : arrayOfString) {
/* 615 */               if (!"".equals(str5) && str5.startsWith("$$"))
/* 616 */                 bufferedWriter.write("\n" + str5); 
/*     */             } 
/*     */             continue;
/*     */           } 
/* 620 */           bufferedWriter.write("\n" + str);
/*     */         } 
/*     */ 
/*     */         
/* 624 */         bufferedWriter.flush();
/* 625 */         bufferedWriter.close();
/* 626 */         bufferedReader.close();
/* 627 */         inputStream.close();
/*     */       }
/* 629 */       catch (Exception exception) {
/* 630 */         exception.printStackTrace();
/*     */       } finally {
/*     */         try {
/* 633 */           if (bufferedWriter != null)
/* 634 */             bufferedWriter.close(); 
/* 635 */         } catch (IOException iOException) {
/* 636 */           iOException.printStackTrace();
/*     */         } 
/*     */         try {
/* 639 */           if (bufferedReader != null)
/* 640 */             bufferedReader.close(); 
/* 641 */         } catch (IOException iOException) {
/* 642 */           iOException.printStackTrace();
/*     */         } 
/*     */         try {
/* 645 */           if (inputStream != null)
/* 646 */             inputStream.close(); 
/* 647 */         } catch (IOException iOException) {
/* 648 */           iOException.printStackTrace();
/*     */         } 
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void replaceExcludeFile(byte[] paramArrayOfbyte) {
/* 661 */     BufferedOutputStream bufferedOutputStream = null;
/* 662 */     if (paramArrayOfbyte.length <= 0)
/*     */       return;  try {
/* 664 */       String str = GCONST.getRootPath() + "/keygenerator/excludeFile.txt";
/*     */       
/* 666 */       File file = new File(str);
/*     */       
/* 668 */       bufferedOutputStream = new BufferedOutputStream(new FileOutputStream(file));
/* 669 */       ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(paramArrayOfbyte);
/* 670 */       BufferedInputStream bufferedInputStream = new BufferedInputStream(byteArrayInputStream);
/* 671 */       byte[] arrayOfByte = new byte[1024];
/* 672 */       int i = 0;
/* 673 */       while ((i = bufferedInputStream.read(arrayOfByte)) > 0) {
/* 674 */         bufferedOutputStream.write(arrayOfByte, 0, i);
/*     */       }
/* 676 */       bufferedOutputStream.flush();
/* 677 */       if (bufferedOutputStream != null)
/* 678 */         bufferedOutputStream.close(); 
/* 679 */       if (bufferedInputStream != null)
/* 680 */         bufferedInputStream.close(); 
/* 681 */     } catch (FileNotFoundException fileNotFoundException) {
/* 682 */       fileNotFoundException.printStackTrace();
/* 683 */     } catch (IOException iOException) {
/* 684 */       iOException.printStackTrace();
/*     */     } finally {
/*     */       try {
/* 687 */         if (bufferedOutputStream != null)
/* 688 */           bufferedOutputStream.close(); 
/* 689 */       } catch (Exception exception) {
/* 690 */         writeLog(exception);
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static List getLikeByMap(Map paramMap, String paramString) {
/* 702 */     Vector vector = new Vector();
/* 703 */     for (Map.Entry entry : paramMap.entrySet()) {
/* 704 */       if (((String)((Map.Entry)entry).getKey()).indexOf(paramString) > -1) {
/* 705 */         vector.add(((Map.Entry)entry).getKey());
/*     */       }
/*     */     } 
/*     */ 
/*     */     
/* 710 */     return vector;
/*     */   }
/*     */   
/*     */   public List getLikeByMap2(Map paramMap, String paramString) {
/* 714 */     ArrayList<String> arrayList = new ArrayList();
/* 715 */     for (Map.Entry entry : paramMap.entrySet()) {
/* 716 */       if (((String)((Map.Entry)entry).getKey()).contains(paramString)) {
/* 717 */         String str = (String)entry.getValue();
/* 718 */         if (str.contains("==") && str.indexOf("==") > 0) {
/* 719 */           str = str.substring(0, str.indexOf("=="));
/*     */         }
/* 721 */         arrayList.add(str);
/* 722 */         arrayList.add("resultDiffer");
/* 723 */         arrayList.add("resultDiffer2");
/*     */         
/*     */         break;
/*     */       } 
/*     */     } 
/* 728 */     return arrayList;
/*     */   }
/*     */   
/*     */   public Map getCompareToolVersion(Map paramMap) {
/* 732 */     HashMap<Object, Object> hashMap = new HashMap<>();
/* 733 */     String str = getPropValue("filecheckutil", "version");
/*     */     
/* 735 */     Iterator<String> iterator = paramMap.keySet().iterator();
/* 736 */     while (iterator.hasNext()) {
/* 737 */       String str1 = iterator.next();
/* 738 */       if (str1.startsWith("$$filtertoolversion$$")) {
/* 739 */         String str2 = (String)paramMap.get(str1);
/* 740 */         if (str2.contains("==") && str2.indexOf("==") > 0) {
/* 741 */           String str3 = str2.substring(0, str2.indexOf("=="));
/*     */           
/* 743 */           if (Double.parseDouble(str) < Double.parseDouble(str3)) {
/* 744 */             hashMap.put("$$filtertoolversion$$", "new" + str + "low" + str3);
/*     */ 
/*     */             
/* 747 */             iterator.remove();
/* 748 */             paramMap.remove(str1);
/*     */             
/*     */             break;
/*     */           } 
/* 752 */           iterator.remove();
/* 753 */           paramMap.remove(str1);
/*     */         } 
/*     */       } 
/* 756 */       if (str1.startsWith("$$filtertoolnewversion$$")) {
/* 757 */         String str2 = (String)paramMap.get(str1);
/* 758 */         if (str2.contains("==") && str2.indexOf("==") > 0) {
/* 759 */           String str3 = str2.substring(0, str2.indexOf("=="));
/*     */           
/* 761 */           if (Double.parseDouble(str) < Double.parseDouble(str3)) {
/* 762 */             hashMap.put("$$filtertoolnewversion$$", "now" + str + "new" + str3);
/*     */           }
/*     */           
/* 765 */           iterator.remove();
/* 766 */           paramMap.remove(str1);
/* 767 */           if (!paramMap.containsKey("$$filtertoolversion$$")) {
/*     */             break;
/*     */           }
/*     */         } 
/*     */       } 
/*     */     } 
/*     */     
/* 774 */     return hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getHtmlLabelName(int paramInt1, int paramInt2) {
/* 784 */     String str = SystemEnv.getHtmlLabelName(paramInt1, paramInt2);
/* 785 */     if (str != null)
/*     */     {
/* 787 */       if (str.trim().equals("") || str.trim().contains("null") || str.trim().contains("NULL")) {
/* 788 */         str = null;
/*     */       }
/*     */     }
/* 791 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getSecondDirectoryName() {
/* 800 */     String str = "";
/*     */     
/* 802 */     str = Util.null2String(getContextPath());
/* 803 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getContextPath() {
/* 813 */     String str = "";
/*     */     try {
/* 815 */       Class<?> clazz = Class.forName("weaver.general.GCONST");
/* 816 */       Method method = clazz.getMethod("getContextPath", new Class[0]);
/* 817 */       if (method != null) {
/* 818 */         String str1 = (String)method.invoke(null, new Object[0]);
/* 819 */         if (str1 != null) {
/* 820 */           str = str1.trim();
/*     */         }
/*     */       }
/*     */     
/*     */     }
/* 825 */     catch (Throwable throwable) {}
/*     */ 
/*     */     
/* 828 */     return str;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/wscheck/KeyGeneratorCompare.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */