/*      */ package wscheck;
/*      */ 
/*      */ import java.io.ByteArrayInputStream;
/*      */ import java.io.File;
/*      */ import java.io.OutputStream;
/*      */ import java.util.ArrayList;
/*      */ import java.util.HashMap;
/*      */ import java.util.Iterator;
/*      */ import java.util.List;
/*      */ import java.util.Map;
/*      */ import java.util.Set;
/*      */ import java.util.zip.ZipOutputStream;
/*      */ import org.apache.commons.logging.Log;
/*      */ import org.apache.commons.logging.LogFactory;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.docs.docs.DocManagerNoRequest;
/*      */ import weaver.docs.docs.ImageFileIdUpdate;
/*      */ import weaver.formmode.data.FieldInfo;
/*      */ import weaver.formmode.interfaces.InterfacesUtil;
/*      */ import weaver.general.BaseBean;
/*      */ import weaver.general.TimeUtil;
/*      */ import weaver.general.Util;
/*      */ import weaver.hrm.User;
/*      */ import weaver.hrm.resource.ResourceComInfo;
/*      */ import weaver.soa.workflow.FileProcessor;
/*      */ import weaver.soa.workflow.WorkFlowInit;
/*      */ import weaver.soa.workflow.request.Cell;
/*      */ import weaver.soa.workflow.request.DetailTable;
/*      */ import weaver.soa.workflow.request.DetailTableInfo;
/*      */ import weaver.soa.workflow.request.MainTableInfo;
/*      */ import weaver.soa.workflow.request.Property;
/*      */ import weaver.soa.workflow.request.RequestInfo;
/*      */ import weaver.soa.workflow.request.RequestService;
/*      */ import weaver.soa.workflow.request.Row;
/*      */ import weaver.system.SystemComInfo;
/*      */ import weaver.workflow.field.DetailFieldComInfo;
/*      */ import weaver.workflow.field.FieldComInfo;
/*      */ import weaver.workflow.request.RequestCheckUser;
/*      */ import weaver.workflow.webservices.WorkflowBaseInfo;
/*      */ import weaver.workflow.webservices.WorkflowDetailTableInfo;
/*      */ import weaver.workflow.webservices.WorkflowMainTableInfo;
/*      */ import weaver.workflow.webservices.WorkflowRequestInfo;
/*      */ import weaver.workflow.webservices.WorkflowRequestTableField;
/*      */ import weaver.workflow.webservices.WorkflowRequestTableRecord;
/*      */ import weaver.workflow.workflow.WorkflowBillComInfo;
/*      */ import weaver.workflow.workflow.WorkflowComInfo;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ public class UpdateWorkflow
/*      */   extends BaseBean
/*      */ {
/*   58 */   private Log log = LogFactory.getLog(UpdateWorkflow.class.getName());
/*   59 */   private static ImageFileIdUpdate imageFileIdUpdate = new ImageFileIdUpdate();
/*   60 */   public static int workflowid = Util.getIntValue(Util.null2String((new BaseBean()).getPropValue("UpdateWorkflow", "createworkflowid")), 0);
/*   61 */   public static String cusnamefieldname = Util.null2String((new BaseBean()).getPropValue("UpdateWorkflow", "cusnamefieldname"));
/*   62 */   public static String updateremarkfieldname = Util.null2String((new BaseBean()).getPropValue("UpdateWorkflow", "updateremarkfieldname"));
/*   63 */   public static String filefieldname = Util.null2String((new BaseBean()).getPropValue("UpdateWorkflow", "filefieldname"));
/*   64 */   public static String createusername = Util.null2String((new BaseBean()).getPropValue("UpdateWorkflow", "createusername"));
/*   65 */   public static String receiveusername = Util.null2String((new BaseBean()).getPropValue("UpdateWorkflow", "receiveusername"));
/*   66 */   private User user = null;
/*   67 */   private Map dataMap = null;
/*   68 */   private List bhSelectList = null; private ArrayList Wf_ManTableFieldIds; private ArrayList Wf_ManTableFieldTypes; private ArrayList Wf_ManTableFieldHtmltypes; private ArrayList Wf_ManTableFieldFieldNames; private ArrayList Wf_ManTableFieldDBTypes;
/*      */   private ArrayList Wf_DetailTableFieldIds;
/*      */   
/*      */   public User getUser() {
/*   72 */     return this.user;
/*      */   }
/*      */   private ArrayList Wf_DetailFieldDBTypes; private ArrayList Wf_DetailFieldTypes; private ArrayList Wf_DetailDBFieldNames; private ArrayList Wf_DetailFieldHtmlTypes; private ArrayList Wf_DetailTableNames; private ArrayList Wf_DetailTableKeys;
/*      */   
/*      */   public void setUser(User paramUser) {
/*   77 */     this.user = paramUser;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void approvalData(String paramString1, String paramString2, String paramString3, int paramInt) {
/*   86 */     int i = 0;
/*   87 */     if (null != this.user) {
/*      */ 
/*      */       
/*   90 */       i = this.user.getUID();
/*      */     }
/*      */     else {
/*      */       
/*   94 */       i = 1;
/*      */     } 
/*   96 */     RecordSet recordSet = new RecordSet();
/*   97 */     String str1 = "";
/*   98 */     int j = 0;
/*   99 */     int k = 0;
/*  100 */     if (workflowid > 0) {
/*      */       
/*  102 */       str1 = "select formid,isbill from workflow_base where id = " + workflowid;
/*  103 */       recordSet.executeSql(str1);
/*  104 */       if (recordSet.next())
/*      */       {
/*  106 */         j = recordSet.getInt("isbill");
/*  107 */         k = recordSet.getInt("formid");
/*      */       }
/*      */     
/*      */     } else {
/*      */       
/*  112 */       writeLog("要触发的流程未设置!");
/*      */     } 
/*  114 */     getWfFieldInfo(j, k);
/*      */     
/*  116 */     this.mainFieldValuesMap.put(cusnamefieldname, paramString1);
/*      */     
/*  118 */     this.mainFieldValuesMap.put(updateremarkfieldname, paramString2);
/*      */     
/*  120 */     String str2 = "" + buildFile(i, "1", paramString3, paramInt);
/*  121 */     this.mainFieldValuesMap.put(filefieldname, str2);
/*  122 */     if (!"".equals(createusername))
/*  123 */       this.mainFieldValuesMap.put(createusername, "1"); 
/*  124 */     if (!"".equals(receiveusername)) {
/*  125 */       this.mainFieldValuesMap.put(receiveusername, "1");
/*      */     }
/*  127 */     String str3 = paramString1 + " 升级包提交申请";
/*  128 */     String str4 = "2";
/*  129 */     writeLog("requestname : " + str3 + " tempdocid : " + str2 + " updateremark : " + paramString2 + " imagefileid : " + paramInt);
/*      */     
/*      */     try {
/*  132 */       int m = createRequest(i, workflowid, str3, str4);
/*  133 */       writeLog("触发的流程id：" + m);
/*      */     }
/*  135 */     catch (Exception exception) {
/*      */       
/*  137 */       writeLog("" + exception.toString());
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int buildFile(int paramInt1, String paramString1, String paramString2, int paramInt2)
/*      */   {
/*  151 */     RecordSet recordSet = new RecordSet();
/*  152 */     OutputStream outputStream = null;
/*  153 */     ZipOutputStream zipOutputStream = null;
/*  154 */     ByteArrayInputStream byteArrayInputStream = null;
/*  155 */     int i = 0;
/*      */ 
/*      */     
/*  158 */     try { ResourceComInfo resourceComInfo = new ResourceComInfo();
/*  159 */       SystemComInfo systemComInfo = new SystemComInfo();
/*  160 */       String str1 = paramString2;
/*      */       
/*  162 */       DocManagerNoRequest docManagerNoRequest = new DocManagerNoRequest();
/*  163 */       String str2 = "";
/*  164 */       int j = 0;
/*  165 */       int k = 0;
/*  166 */       int m = 0;
/*  167 */       recordSet.executeSql(" select docCategory from workflow_base where id=" + workflowid);
/*  168 */       if (recordSet.next()) {
/*      */         
/*  170 */         str2 = Util.null2String(recordSet.getString("docCategory"));
/*  171 */         if (str2 != null && !str2.equals("")) {
/*      */           
/*  173 */           int i1 = str2.indexOf(',');
/*  174 */           int i2 = str2.lastIndexOf(',');
/*  175 */           j = Util.getIntValue(str2.substring(0, i1), 0);
/*  176 */           k = Util.getIntValue(str2.substring(i1 + 1, i2), 0);
/*  177 */           m = Util.getIntValue(str2.substring(i2 + 1), 0);
/*      */         } 
/*      */       } 
/*      */       
/*  181 */       String str3 = "";
/*  182 */       String str4 = Util.null2String(paramString2);
/*  183 */       int n = str4.lastIndexOf(".");
/*  184 */       if (n != -1)
/*      */       {
/*  186 */         str3 = str4.substring(n + 1);
/*      */       }
/*  188 */       HashMap<Object, Object> hashMap = new HashMap<>();
/*  189 */       String str5 = "";
/*  190 */       if (n == -1) {
/*  191 */         str5 = str1;
/*      */       } else {
/*  193 */         str5 = str1.substring(0, n);
/*  194 */       }  hashMap.put("docsubject", str5);
/*  195 */       hashMap.put("doccreaterid", "" + paramInt1);
/*  196 */       hashMap.put("docCreaterType", "1");
/*  197 */       hashMap.put("maincategory", "" + j);
/*  198 */       hashMap.put("subcategory", "" + k);
/*  199 */       hashMap.put("seccategory", "" + m);
/*  200 */       hashMap.put("fileids", "" + paramInt2);
/*  201 */       docManagerNoRequest.UploadDocNoRequest(hashMap);
/*  202 */       i = docManagerNoRequest.getId();
/*      */       
/*  204 */       if (paramString1.equals("0")) {
/*      */ 
/*      */         
/*  207 */         recordSet.executeSql("UPDATE docimagefile set isextfile='' WHERE docid=" + i + " AND imagefileid=" + paramInt2);
/*      */         
/*  209 */         recordSet
/*  210 */           .executeSql("UPDATE DocDetail SET doclastmoduserid=" + paramInt1 + ",doclastmoddate='" + 
/*  211 */             TimeUtil.getCurrentDateString() + "',doclastmodtime='" + 
/*  212 */             TimeUtil.getOnlyCurrentTimeString() + "',accessorycount=0,docdepartmentid=" + resourceComInfo
/*  213 */             .getDepartmentID("" + paramInt1) + ",doccreatedate='" + 
/*  214 */             TimeUtil.getCurrentDateString() + "',doccreatetime='" + 
/*  215 */             TimeUtil.getOnlyCurrentTimeString() + "',maincategory=" + j + ",subcategory=" + k + ",seccategory=" + m + ",usertype='1',doccreaterid='" + paramInt1 + "',ownerid='" + paramInt1 + "',doctype='2',docextendname='" + str3 + "' WHERE id=" + i);
/*      */ 
/*      */       
/*      */       }
/*      */       else {
/*      */ 
/*      */         
/*  222 */         recordSet.executeSql("UPDATE DocDetail SET doclastmoduserid=" + paramInt1 + ",doclastmoddate='" + 
/*  223 */             TimeUtil.getCurrentDateString() + "',doclastmodtime='" + TimeUtil.getOnlyCurrentTimeString() + "',docdepartmentid=" + resourceComInfo
/*  224 */             .getDepartmentID("" + paramInt1) + ",doccreatedate='" + 
/*  225 */             TimeUtil.getCurrentDateString() + "',doccreatetime='" + TimeUtil.getOnlyCurrentTimeString() + "',maincategory=" + j + ",subcategory=" + k + ",seccategory=" + m + ",usertype='1',doccreaterid='" + paramInt1 + "',ownerid='" + paramInt1 + "',doctype='1',docextendname='" + str3 + "' WHERE id=" + i);
/*      */       } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  258 */       return i; } catch (Exception exception) { return i; }
/*      */     finally { Exception exception = null; try { if (byteArrayInputStream != null)
/*      */           byteArrayInputStream.close();  if (outputStream != null)
/*      */           outputStream.close();  if (zipOutputStream != null)
/*      */           zipOutputStream.close();  }
/*      */       catch (Exception exception1) {} }
/*  264 */      } public static void main(String[] paramArrayOfString) { String str1 = "2012-10-10 11:11:11";
/*  265 */     String str2 = str1.substring(0, 10);
/*  266 */     String str3 = str1.substring(11, 19);
/*      */     
/*  268 */     str1 = "2012-9-10 1:1:1";
/*  269 */     str1 = TimeUtil.getTimeString(TimeUtil.getString2Date(str1, "yyyy'-'MM'-'dd' 'HH:mm:ss"));
/*  270 */     str2 = str1.substring(0, 10);
/*  271 */     str3 = str1.substring(11, 19);
/*  272 */     System.out.println("2 date1 : " + str2 + " time1 : " + str3); }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*  290 */   private HashMap Wf_Field_Table_Map = new HashMap<>();
/*  291 */   private HashMap Wf_Field_Name_Map = new HashMap<>();
/*      */ 
/*      */   
/*      */   public void getWfFieldInfo(int paramInt1, int paramInt2) {
/*  295 */     byte b = 7;
/*  296 */     FieldInfo fieldInfo = new FieldInfo();
/*  297 */     fieldInfo.setUser(this.user);
/*  298 */     fieldInfo.GetManTableField(paramInt2, paramInt1, b);
/*  299 */     this.Wf_ManTableFieldIds = fieldInfo.getManTableFieldIds();
/*  300 */     this.Wf_ManTableFieldFieldNames = fieldInfo.getManTableFieldFieldNames();
/*  301 */     this.Wf_ManTableFieldDBTypes = fieldInfo.getManTableFieldDBTypes();
/*  302 */     this.Wf_ManTableFieldTypes = fieldInfo.getManTableFieldTypes();
/*  303 */     this.Wf_ManTableFieldHtmltypes = fieldInfo.getManTableFieldHtmltypes();
/*  304 */     this.Wf_Field_Table_Map = fieldInfo.getField_Table_Map();
/*  305 */     this.Wf_Field_Name_Map = fieldInfo.getField_Name_Map();
/*  306 */     fieldInfo.GetNewDetailTableField(paramInt2, paramInt1, b);
/*  307 */     this.Wf_DetailTableFieldIds = fieldInfo.getDetailTableFieldIds();
/*  308 */     this.Wf_DetailFieldDBTypes = fieldInfo.getDetailFieldDBTypes();
/*  309 */     this.Wf_DetailFieldTypes = fieldInfo.getDetailFieldTypes();
/*  310 */     this.Wf_DetailDBFieldNames = fieldInfo.getDetailDBFieldNames();
/*  311 */     this.Wf_DetailFieldHtmlTypes = fieldInfo.getDetailFieldHtmlTypes();
/*  312 */     this.Wf_DetailTableNames = fieldInfo.getDetailTableNames();
/*  313 */     this.Wf_DetailTableKeys = fieldInfo.getDetailTableKeys();
/*  314 */     this.Wf_Field_Table_Map = fieldInfo.getField_Table_Map();
/*  315 */     this.Wf_Field_Name_Map = fieldInfo.getField_Name_Map();
/*      */   }
/*      */   
/*  318 */   private HashMap mainFieldValuesMap = new HashMap<>();
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int createRequest(int paramInt1, int paramInt2, String paramString1, String paramString2) throws Exception {
/*  330 */     InterfacesUtil interfacesUtil = new InterfacesUtil();
/*  331 */     int i = 0;
/*      */     
/*  333 */     WorkflowRequestTableField[] arrayOfWorkflowRequestTableField = new WorkflowRequestTableField[this.Wf_ManTableFieldIds.size()];
/*  334 */     byte b1 = 0;
/*  335 */     for (byte b2 = 0; b2 < this.Wf_ManTableFieldIds.size(); b2++) {
/*      */       
/*  337 */       String str1 = Util.null2String(this.Wf_ManTableFieldIds.get(b2));
/*  338 */       String str2 = Util.null2String(this.Wf_ManTableFieldFieldNames.get(b2));
/*  339 */       String str3 = Util.null2String(this.Wf_ManTableFieldDBTypes.get(b2));
/*  340 */       String str4 = Util.null2String(this.Wf_ManTableFieldTypes.get(b2));
/*  341 */       String str5 = Util.null2String(String.valueOf(this.Wf_ManTableFieldHtmltypes.get(b2)));
/*      */       
/*  343 */       String str6 = "";
/*      */       
/*  345 */       if (!str2.equals("") && this.mainFieldValuesMap.containsKey(str2.toLowerCase())) {
/*      */         
/*  347 */         str6 = Util.null2String((String)this.mainFieldValuesMap.get(str2.toLowerCase()));
/*      */         
/*  349 */         str6 = interfacesUtil.getSubStringValue(str3.toLowerCase(), str6);
/*  350 */         arrayOfWorkflowRequestTableField[b1] = new WorkflowRequestTableField();
/*  351 */         arrayOfWorkflowRequestTableField[b1].setFieldName(str2);
/*  352 */         arrayOfWorkflowRequestTableField[b1].setFieldValue(str6);
/*  353 */         arrayOfWorkflowRequestTableField[b1].setView(true);
/*  354 */         arrayOfWorkflowRequestTableField[b1].setEdit(true);
/*  355 */         b1++;
/*      */       } 
/*      */     } 
/*  358 */     WorkflowRequestTableRecord[] arrayOfWorkflowRequestTableRecord = new WorkflowRequestTableRecord[1];
/*  359 */     arrayOfWorkflowRequestTableRecord[0] = new WorkflowRequestTableRecord();
/*  360 */     arrayOfWorkflowRequestTableRecord[0].setWorkflowRequestTableFields(arrayOfWorkflowRequestTableField);
/*  361 */     WorkflowMainTableInfo workflowMainTableInfo = new WorkflowMainTableInfo();
/*  362 */     workflowMainTableInfo.setRequestRecords(arrayOfWorkflowRequestTableRecord);
/*  363 */     WorkflowBaseInfo workflowBaseInfo = new WorkflowBaseInfo();
/*  364 */     workflowBaseInfo.setWorkflowId(String.valueOf(paramInt2));
/*  365 */     WorkflowRequestInfo workflowRequestInfo = new WorkflowRequestInfo();
/*  366 */     workflowRequestInfo.setCreatorId(String.valueOf(paramInt1));
/*  367 */     workflowRequestInfo.setRequestLevel(paramString2);
/*  368 */     workflowRequestInfo.setRequestName(paramString1);
/*  369 */     workflowRequestInfo.setWorkflowMainTableInfo(workflowMainTableInfo);
/*  370 */     workflowRequestInfo.setWorkflowBaseInfo(workflowBaseInfo);
/*  371 */     RequestInfo requestInfo = toRequestInfo(workflowRequestInfo);
/*      */     
/*      */     try {
/*  374 */       i = Util.getIntValue(createRequest(requestInfo), 0);
/*      */     }
/*  376 */     catch (Exception exception) {
/*      */       
/*  378 */       writeLog(exception);
/*  379 */       throw new Exception(exception);
/*      */     } 
/*  381 */     return i;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private RequestInfo toRequestInfo(WorkflowRequestInfo paramWorkflowRequestInfo) {
/*  392 */     RequestService requestService = new RequestService();
/*  393 */     if (paramWorkflowRequestInfo == null)
/*  394 */       return null; 
/*  395 */     int i = 0;
/*  396 */     String str = "0";
/*  397 */     RecordSet recordSet = new RecordSet();
/*  398 */     int j = Util.getIntValue(paramWorkflowRequestInfo.getWorkflowBaseInfo().getWorkflowId(), 0);
/*      */     
/*  400 */     recordSet.executeProc("workflow_Workflowbase_SByID", j + "");
/*  401 */     if (recordSet.next()) {
/*      */       
/*  403 */       i = Util.getIntValue(recordSet.getString("formid"), 0);
/*  404 */       str = "" + Util.getIntValue(recordSet.getString("isbill"), 0);
/*      */     } 
/*  406 */     if ("1".equals(str) && i == 158) {
/*      */       
/*  408 */       String str1 = "0";
/*  409 */       WorkflowDetailTableInfo[] arrayOfWorkflowDetailTableInfo1 = paramWorkflowRequestInfo.getWorkflowDetailTableInfos();
/*  410 */       if (arrayOfWorkflowDetailTableInfo1 != null)
/*      */       {
/*  412 */         for (byte b1 = 0; b1 < arrayOfWorkflowDetailTableInfo1.length; b1++) {
/*      */           
/*  414 */           WorkflowRequestTableRecord[] arrayOfWorkflowRequestTableRecord = arrayOfWorkflowDetailTableInfo1[b1].getWorkflowRequestTableRecords();
/*  415 */           if (arrayOfWorkflowRequestTableRecord != null)
/*      */           {
/*  417 */             for (byte b2 = 0; b2 < arrayOfWorkflowRequestTableRecord.length; b2++) {
/*      */               
/*  419 */               if (arrayOfWorkflowRequestTableRecord[b2] != null && arrayOfWorkflowRequestTableRecord[b2].getRecordOrder() == -1) {
/*      */                 
/*  421 */                 WorkflowRequestTableField[] arrayOfWorkflowRequestTableField = arrayOfWorkflowRequestTableRecord[b2].getWorkflowRequestTableFields();
/*  422 */                 if (arrayOfWorkflowRequestTableField != null)
/*      */                 {
/*  424 */                   for (byte b3 = 0; b3 < arrayOfWorkflowRequestTableField.length; b3++) {
/*      */                     
/*  426 */                     if (arrayOfWorkflowRequestTableField[b3] != null && "amount".equals(arrayOfWorkflowRequestTableField[b3].getFieldName()))
/*      */                     {
/*  428 */                       str1 = arrayOfWorkflowRequestTableField[b3].getFieldValue();
/*      */                     }
/*      */                   } 
/*      */                 }
/*      */               } 
/*      */             } 
/*      */           }
/*      */         } 
/*      */       }
/*  437 */       WorkflowMainTableInfo workflowMainTableInfo1 = paramWorkflowRequestInfo.getWorkflowMainTableInfo();
/*  438 */       if (workflowMainTableInfo1 != null) {
/*      */         
/*  440 */         WorkflowRequestTableRecord[] arrayOfWorkflowRequestTableRecord = workflowMainTableInfo1.getRequestRecords();
/*  441 */         if (arrayOfWorkflowRequestTableRecord != null && arrayOfWorkflowRequestTableRecord[0] != null)
/*      */         {
/*  443 */           for (byte b1 = 0; b1 < (arrayOfWorkflowRequestTableRecord[0].getWorkflowRequestTableFields()).length; b1++) {
/*      */             
/*  445 */             WorkflowRequestTableField workflowRequestTableField = arrayOfWorkflowRequestTableRecord[0].getWorkflowRequestTableFields()[b1];
/*  446 */             if (workflowRequestTableField != null && "total".equals(workflowRequestTableField.getFieldName()))
/*      */             {
/*  448 */               workflowRequestTableField.setFieldValue(str1);
/*      */             }
/*      */           } 
/*      */         }
/*      */       } 
/*      */     } 
/*  454 */     RequestInfo requestInfo = new RequestInfo();
/*  455 */     if (Util.getIntValue(paramWorkflowRequestInfo.getRequestId()) > 0)
/*  456 */       requestInfo = requestService.getRequest(Util.getIntValue(paramWorkflowRequestInfo.getRequestId())); 
/*  457 */     requestInfo.setRequestid(paramWorkflowRequestInfo.getRequestId());
/*  458 */     requestInfo.setWorkflowid(paramWorkflowRequestInfo.getWorkflowBaseInfo().getWorkflowId());
/*  459 */     requestInfo.setCreatorid(paramWorkflowRequestInfo.getCreatorId());
/*  460 */     requestInfo.setDescription(paramWorkflowRequestInfo.getRequestName());
/*  461 */     requestInfo.setRequestlevel(paramWorkflowRequestInfo.getRequestLevel());
/*  462 */     requestInfo.setRemindtype(paramWorkflowRequestInfo.getMessageType());
/*  463 */     MainTableInfo mainTableInfo = new MainTableInfo();
/*  464 */     ArrayList<Property> arrayList = new ArrayList();
/*  465 */     WorkflowMainTableInfo workflowMainTableInfo = paramWorkflowRequestInfo.getWorkflowMainTableInfo();
/*  466 */     if (workflowMainTableInfo != null) {
/*      */       
/*  468 */       WorkflowRequestTableRecord[] arrayOfWorkflowRequestTableRecord = workflowMainTableInfo.getRequestRecords();
/*  469 */       if (arrayOfWorkflowRequestTableRecord != null && arrayOfWorkflowRequestTableRecord[0] != null)
/*      */       {
/*  471 */         for (byte b1 = 0; b1 < (arrayOfWorkflowRequestTableRecord[0].getWorkflowRequestTableFields()).length; b1++) {
/*      */           
/*  473 */           WorkflowRequestTableField workflowRequestTableField = arrayOfWorkflowRequestTableRecord[0].getWorkflowRequestTableFields()[b1];
/*  474 */           if (workflowRequestTableField != null)
/*      */           {
/*  476 */             if (workflowRequestTableField.getFieldName() != null && !"".equals(workflowRequestTableField.getFieldName()) && workflowRequestTableField
/*  477 */               .getFieldValue() != null && !"".equals(workflowRequestTableField.getFieldValue()) && workflowRequestTableField.isView() && workflowRequestTableField
/*  478 */               .isEdit()) {
/*      */               
/*  480 */               Property property = new Property();
/*  481 */               property.setName(workflowRequestTableField.getFieldName());
/*  482 */               property.setValue(workflowRequestTableField.getFieldValue());
/*  483 */               arrayList.add(property);
/*      */             } 
/*      */           }
/*      */         } 
/*      */       }
/*      */     } 
/*  489 */     Property[] arrayOfProperty = arrayList.<Property>toArray(new Property[arrayList.size()]);
/*  490 */     mainTableInfo.setProperty(arrayOfProperty);
/*  491 */     requestInfo.setMainTableInfo(mainTableInfo);
/*  492 */     DetailTableInfo detailTableInfo = new DetailTableInfo();
/*  493 */     WorkflowDetailTableInfo[] arrayOfWorkflowDetailTableInfo = paramWorkflowRequestInfo.getWorkflowDetailTableInfos();
/*      */ 
/*      */     
/*  496 */     ArrayList<DetailTable> arrayList1 = new ArrayList();
/*  497 */     for (byte b = 0; arrayOfWorkflowDetailTableInfo != null && b < arrayOfWorkflowDetailTableInfo.length; b++) {
/*      */       
/*  499 */       DetailTable detailTable = new DetailTable();
/*  500 */       detailTable.setId((b + 1) + "");
/*  501 */       WorkflowDetailTableInfo workflowDetailTableInfo = arrayOfWorkflowDetailTableInfo[b];
/*  502 */       WorkflowRequestTableRecord[] arrayOfWorkflowRequestTableRecord = workflowDetailTableInfo.getWorkflowRequestTableRecords();
/*  503 */       ArrayList<Row> arrayList2 = new ArrayList();
/*  504 */       for (byte b1 = 0; arrayOfWorkflowRequestTableRecord != null && b1 < arrayOfWorkflowRequestTableRecord.length; b1++) {
/*      */         
/*  506 */         Row row = new Row();
/*  507 */         row.setId(b1 + "");
/*  508 */         WorkflowRequestTableRecord workflowRequestTableRecord = arrayOfWorkflowRequestTableRecord[b1];
/*  509 */         WorkflowRequestTableField[] arrayOfWorkflowRequestTableField = workflowRequestTableRecord.getWorkflowRequestTableFields();
/*  510 */         ArrayList<Cell> arrayList3 = new ArrayList();
/*  511 */         for (byte b2 = 0; arrayOfWorkflowRequestTableField != null && b2 < arrayOfWorkflowRequestTableField.length; b2++) {
/*      */           
/*  513 */           WorkflowRequestTableField workflowRequestTableField = arrayOfWorkflowRequestTableField[b2];
/*  514 */           if (workflowRequestTableField != null)
/*      */           {
/*  516 */             if (workflowRequestTableField.getFieldName() != null && !"".equals(workflowRequestTableField.getFieldName()) && workflowRequestTableField
/*  517 */               .getFieldValue() != null && !"".equals(workflowRequestTableField.getFieldValue()) && workflowRequestTableField.isView() && workflowRequestTableField
/*  518 */               .isEdit()) {
/*      */               
/*  520 */               Cell cell = new Cell();
/*  521 */               cell.setName(workflowRequestTableField.getFieldName());
/*  522 */               cell.setValue(workflowRequestTableField.getFieldValue());
/*  523 */               arrayList3.add(cell);
/*      */             } 
/*      */           }
/*      */         } 
/*  527 */         if (arrayList3 != null && arrayList3.size() > 0) {
/*      */           
/*  529 */           Cell[] arrayOfCell = arrayList3.<Cell>toArray(new Cell[arrayList3.size()]);
/*  530 */           row.setCell(arrayOfCell);
/*      */         } 
/*  532 */         arrayList2.add(row);
/*      */       } 
/*  534 */       if (arrayList2 != null && arrayList2.size() > 0) {
/*      */         
/*  536 */         Row[] arrayOfRow = arrayList2.<Row>toArray(new Row[arrayList2.size()]);
/*  537 */         detailTable.setRow(arrayOfRow);
/*      */       } 
/*  539 */       arrayList1.add(detailTable);
/*      */     } 
/*  541 */     DetailTable[] arrayOfDetailTable = arrayList1.<DetailTable>toArray(new DetailTable[arrayList1.size()]);
/*  542 */     detailTableInfo.setDetailTable(arrayOfDetailTable);
/*  543 */     requestInfo.setDetailTableInfo(detailTableInfo);
/*  544 */     return requestInfo;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String createRequest(RequestInfo paramRequestInfo) throws Exception {
/*  555 */     String str1 = paramRequestInfo.getWorkflowid();
/*      */     
/*  557 */     RequestCheckUser requestCheckUser = new RequestCheckUser();
/*  558 */     requestCheckUser.setUserid(Util.getIntValue(paramRequestInfo.getCreatorid()));
/*  559 */     requestCheckUser.setWorkflowid(Util.getIntValue(str1, 0));
/*  560 */     requestCheckUser.setLogintype("1");
/*  561 */     requestCheckUser.checkUser();
/*  562 */     int i = requestCheckUser.getHasright();
/*  563 */     if (i == 0)
/*      */     {
/*  565 */       return "-2";
/*      */     }
/*  567 */     RecordSet recordSet = new RecordSet();
/*  568 */     String str2 = "";
/*  569 */     WorkflowComInfo workflowComInfo = new WorkflowComInfo();
/*  570 */     String str3 = workflowComInfo.getIsBill(str1);
/*  571 */     String str4 = workflowComInfo.getFormId(str1);
/*  572 */     WorkflowBillComInfo workflowBillComInfo = new WorkflowBillComInfo();
/*      */     
/*  574 */     WorkFlowInit workFlowInit = new WorkFlowInit();
/*  575 */     workFlowInit.setIsbill(Util.getIntValue(str3));
/*  576 */     if (str3.equals("1")) {
/*      */       
/*  578 */       String str = workflowBillComInfo.getTablename(str4);
/*  579 */       workFlowInit.setBillTableName(str);
/*      */     } 
/*  581 */     workFlowInit.SetWorkFlowID(Util.getIntValue(paramRequestInfo.getWorkflowid()));
/*  582 */     workFlowInit.SetCreater(Util.getIntValue(paramRequestInfo.getCreatorid()));
/*  583 */     workFlowInit.SetRequestName(paramRequestInfo.getDescription());
/*  584 */     workFlowInit.setRequestlevel("" + Util.getIntValue(paramRequestInfo.getRequestlevel(), 0));
/*  585 */     workFlowInit.setMessageType("" + Util.getIntValue(paramRequestInfo.getRemindtype(), 0));
/*  586 */     workFlowInit.generateRequestid();
/*  587 */     int j = workFlowInit.getRequestid();
/*  588 */     int k = workFlowInit.getBillid();
/*      */ 
/*      */ 
/*      */ 
/*      */     
/*  593 */     if (!str3.equals("1")) {
/*      */       
/*  595 */       if (j < 1)
/*  596 */         return "-3"; 
/*  597 */       String str5 = "update workflow_form set billid=" + k;
/*  598 */       String str6 = " where requestid=" + j;
/*  599 */       MainTableInfo mainTableInfo = paramRequestInfo.getMainTableInfo();
/*  600 */       FieldComInfo fieldComInfo = new FieldComInfo();
/*  601 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  602 */       HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  603 */       while (fieldComInfo.next()) {
/*      */         
/*  605 */         String str = fieldComInfo.getFieldname();
/*  606 */         List list = getPropertyByName(mainTableInfo, str);
/*  607 */         Iterator<Property> iterator1 = list.iterator();
/*  608 */         label171: while (iterator1.hasNext()) {
/*      */           
/*  610 */           Property property = iterator1.next();
/*      */           
/*  612 */           if (property.getType() != null && (property
/*  613 */             .getType().indexOf("http") > -1 || property.getType().indexOf("ftp") > -1 || property.getType()
/*  614 */             .indexOf("base64") > -1)) {
/*      */             
/*  616 */             if (hashMap1.get(property.getName()) == null) {
/*      */               
/*  618 */               ArrayList<String> arrayList1 = new ArrayList();
/*  619 */               ArrayList<String> arrayList2 = new ArrayList();
/*  620 */               arrayList1.add(property.getType());
/*  621 */               hashMap1.put(property.getName(), arrayList1);
/*  622 */               arrayList2.add(property.getValue());
/*  623 */               hashMap2.put(property.getName(), arrayList2);
/*      */               
/*      */               continue;
/*      */             } 
/*  627 */             List<String> list1 = (List)hashMap1.get(property.getName());
/*  628 */             list1.add(property.getType().substring(property.getType().indexOf(':') + 1));
/*  629 */             List<String> list2 = (List)hashMap2.get(property.getName());
/*  630 */             list2.add(property.getValue());
/*      */             
/*      */             continue;
/*      */           } 
/*  634 */           String str7 = fieldComInfo.getFielddbtype(fieldComInfo.getFieldid());
/*  635 */           if (str7.toUpperCase().indexOf("INT") < 0) { if (str7
/*  636 */               .toUpperCase().indexOf("NUMBER") < 0) { if (str7
/*  637 */                 .toUpperCase().indexOf("DECIMAL") < 0) { if (str7
/*  638 */                   .toUpperCase().indexOf("FLOAT") >= 0)
/*      */                   break label171;  }
/*  640 */               else { str5 = str5 + "," + str + "=" + property.getValue();
/*      */                 
/*      */                 continue; }
/*      */ 
/*      */               
/*  645 */               str5 = str5 + "," + str + "='" + parseSpecialChar(recordSet.getDBType(), property.getValue()) + "'"; continue; }
/*      */              break label171; }
/*      */            break label171;
/*      */         } 
/*      */       } 
/*  650 */       boolean bool = recordSet.executeSql(str5 + str6);
/*      */       
/*  652 */       Set set = hashMap1.keySet();
/*  653 */       Iterator<String> iterator = set.iterator();
/*  654 */       while (iterator.hasNext()) {
/*      */         
/*  656 */         String str7 = iterator.next();
/*  657 */         List list1 = (List)hashMap1.get(str7);
/*  658 */         List list2 = (List)hashMap2.get(str7);
/*  659 */         String[] arrayOfString1 = (String[])list1.toArray((Object[])new String[list1.size()]);
/*  660 */         String[] arrayOfString2 = (String[])list2.toArray((Object[])new String[list2.size()]);
/*  661 */         String str8 = addAttachments(arrayOfString1, arrayOfString2, workFlowInit.getDocCategory(), workFlowInit.getUser());
/*  662 */         if (!str8.equals(""))
/*      */         {
/*  664 */           str5 = "update workflow_form set " + str7 + "='" + str8 + "' where requestid=" + j;
/*  665 */           recordSet.executeSql(str5);
/*      */         }
/*      */       
/*      */       } 
/*      */     } else {
/*      */       
/*  671 */       if (j < 1)
/*  672 */         return "-3"; 
/*  673 */       String str7 = workflowBillComInfo.getTablename(str4);
/*  674 */       String str8 = "update " + str7 + " set ";
/*  675 */       String str5 = "";
/*  676 */       String str6 = " where requestid=" + j;
/*  677 */       MainTableInfo mainTableInfo = paramRequestInfo.getMainTableInfo();
/*  678 */       recordSet.executeSql("select * from workflow_billfield where viewtype=0 and billid=" + str4);
/*  679 */       HashMap<Object, Object> hashMap1 = new HashMap<>();
/*  680 */       HashMap<Object, Object> hashMap2 = new HashMap<>();
/*  681 */       while (recordSet.next()) {
/*      */         
/*  683 */         String str = recordSet.getString("fieldname");
/*      */         
/*  685 */         List list = getPropertyByName(mainTableInfo, str);
/*  686 */         Iterator<Property> iterator1 = list.iterator();
/*  687 */         while (iterator1.hasNext()) {
/*      */           
/*  689 */           Property property = iterator1.next();
/*      */           
/*  691 */           if (property.getType() != null && (property
/*  692 */             .getType().indexOf("http") > -1 || property.getType().indexOf("ftp") > -1 || property.getType()
/*  693 */             .indexOf("base64") > -1)) {
/*      */             
/*  695 */             if (hashMap1.get(property.getName()) == null) {
/*      */               
/*  697 */               ArrayList<String> arrayList1 = new ArrayList();
/*  698 */               ArrayList<String> arrayList2 = new ArrayList();
/*  699 */               arrayList1.add(property.getType());
/*  700 */               hashMap1.put(property.getName(), arrayList1);
/*  701 */               arrayList2.add(property.getValue());
/*  702 */               hashMap2.put(property.getName(), arrayList2);
/*      */               
/*      */               continue;
/*      */             } 
/*  706 */             List<String> list1 = (List)hashMap1.get(property.getName());
/*  707 */             list1.add(property.getType().substring(property.getType().indexOf(':') + 1));
/*  708 */             List<String> list2 = (List)hashMap2.get(property.getName());
/*  709 */             list2.add(property.getValue());
/*      */             
/*      */             continue;
/*      */           } 
/*  713 */           String str9 = Util.null2String(recordSet.getString("fielddbtype"));
/*  714 */           if (str9.toUpperCase().indexOf("INT") >= 0 || str9
/*  715 */             .toUpperCase().indexOf("NUMBER") >= 0 || str9
/*  716 */             .toUpperCase().indexOf("DECIMAL") >= 0 || str9
/*  717 */             .toUpperCase().indexOf("FLOAT") >= 0) {
/*      */             
/*  719 */             if (str5.equals("")) {
/*  720 */               str5 = str5 + str + "=" + property.getValue(); continue;
/*      */             } 
/*  722 */             str5 = str5 + "," + str + "=" + property.getValue();
/*      */             
/*      */             continue;
/*      */           } 
/*  726 */           if (str5.equals("")) {
/*      */             
/*  728 */             str5 = str5 + str + "='" + property.getValue() + "'";
/*      */             
/*      */             continue;
/*      */           } 
/*      */           
/*  733 */           str5 = str5 + "," + str + "='" + parseSpecialChar(recordSet.getDBType(), property.getValue()) + "'";
/*      */         } 
/*      */       } 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  740 */       boolean bool = recordSet.executeSql(str8 + str5 + str6);
/*      */       
/*  742 */       Set set = hashMap1.keySet();
/*  743 */       Iterator<String> iterator = set.iterator();
/*  744 */       while (iterator.hasNext()) {
/*      */         
/*  746 */         String str9 = iterator.next();
/*  747 */         List list1 = (List)hashMap1.get(str9);
/*  748 */         List list2 = (List)hashMap2.get(str9);
/*  749 */         String[] arrayOfString1 = (String[])list1.toArray((Object[])new String[list1.size()]);
/*  750 */         String[] arrayOfString2 = (String[])list2.toArray((Object[])new String[list2.size()]);
/*  751 */         String str10 = addAttachments(arrayOfString1, arrayOfString2, workFlowInit.getDocCategory(), workFlowInit.getUser());
/*  752 */         if (!str10.equals("")) {
/*      */           
/*  754 */           str5 = "update " + str7 + " set " + str9 + "='" + str10 + "' where requestid=" + j;
/*      */           
/*  756 */           recordSet.executeSql(str5);
/*      */         } 
/*      */       } 
/*      */     } 
/*      */     
/*  761 */     if (Util.null2String(paramRequestInfo.getIsNextFlow()).equals("0"))
/*  762 */       workFlowInit.setNextFlow(false); 
/*  763 */     int m = workFlowInit.NewFlow();
/*  764 */     if (m < 0) {
/*  765 */       return "" + m;
/*      */     }
/*  767 */     if (!str3.equals("1")) {
/*      */       
/*  769 */       DetailTableInfo detailTableInfo = paramRequestInfo.getDetailTableInfo();
/*  770 */       if (detailTableInfo == null)
/*  771 */         return "" + j; 
/*  772 */       DetailTable[] arrayOfDetailTable = detailTableInfo.getDetailTable();
/*  773 */       for (byte b = 0; b < arrayOfDetailTable.length; b++) {
/*      */         
/*  775 */         DetailTable detailTable = arrayOfDetailTable[b];
/*  776 */         int n = Util.getIntValue(detailTable.getId());
/*  777 */         if (n >= 1) {
/*      */           
/*  779 */           Row[] arrayOfRow = detailTable.getRow();
/*  780 */           for (byte b1 = 0; b1 < arrayOfRow.length; b1++) {
/*      */             
/*  782 */             Row row = arrayOfRow[b1];
/*  783 */             String str5 = "insert into workflow_formdetail(requestid,groupid";
/*  784 */             String str6 = " values(" + j + "," + (n - 1);
/*  785 */             DetailFieldComInfo detailFieldComInfo = new DetailFieldComInfo();
/*  786 */             while (detailFieldComInfo.next()) {
/*      */               
/*  788 */               String str = detailFieldComInfo.getFieldname();
/*  789 */               Cell cell = getCellByName(row, str);
/*  790 */               if (cell != null) {
/*      */                 
/*  792 */                 str5 = str5 + "," + str;
/*  793 */                 String str7 = detailFieldComInfo.getFielddbtype(detailFieldComInfo.getFieldid());
/*  794 */                 if (str7.toUpperCase().indexOf("INT") >= 0 || str7
/*  795 */                   .toUpperCase().indexOf("NUMBER") >= 0 || str7
/*  796 */                   .toUpperCase().indexOf("DECIMAL") >= 0 || str7
/*  797 */                   .toUpperCase().indexOf("FLOAT") >= 0) {
/*      */                   
/*  799 */                   str6 = str6 + "," + cell.getValue();
/*      */                   
/*      */                   continue;
/*      */                 } 
/*      */                 
/*  804 */                 str6 = str6 + ",'" + parseSpecialChar(recordSet.getDBType(), cell.getValue()) + "'";
/*      */               } 
/*      */             } 
/*      */             
/*  808 */             str5 = str5 + ")";
/*  809 */             str6 = str6 + ")";
/*  810 */             recordSet.executeSql(str5 + str6);
/*      */           } 
/*      */         } 
/*      */       } 
/*      */     } else {
/*      */       
/*  816 */       DetailTableInfo detailTableInfo = paramRequestInfo.getDetailTableInfo();
/*  817 */       if (detailTableInfo == null)
/*  818 */         return "" + j; 
/*  819 */       DetailTable[] arrayOfDetailTable = detailTableInfo.getDetailTable();
/*      */       
/*  821 */       String str5 = workflowBillComInfo.getDetailkeyfield(str4);
/*  822 */       if (str5.equals(""))
/*  823 */         str5 = "mainid"; 
/*  824 */       RecordSet recordSet1 = new RecordSet();
/*  825 */       String str6 = "select tablename as detailtablename from workflow_billdetailtable where billid=" + str4 + " order by orderid";
/*      */       
/*  827 */       recordSet1.executeSql(str6);
/*  828 */       int n = recordSet1.getCounts();
/*  829 */       boolean bool1 = false;
/*  830 */       boolean bool2 = false;
/*  831 */       if (n == 0) {
/*      */         
/*  833 */         bool1 = true;
/*  834 */         str6 = "select detailtablename from workflow_bill where id=" + str4;
/*  835 */         recordSet1.executeSql(str6);
/*  836 */         n = recordSet1.getCounts();
/*  837 */         recordSet1.next();
/*  838 */         String str = recordSet1.getString("detailtablename");
/*  839 */         if (str.equals(""))
/*  840 */           bool2 = true; 
/*      */       } 
/*  842 */       if (!bool2) {
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*  848 */         ArrayList<String> arrayList = new ArrayList();
/*  849 */         recordSet1.beforFirst();
/*  850 */         while (recordSet1.next())
/*      */         {
/*  852 */           arrayList.add(recordSet1.getString("detailtablename"));
/*      */         }
/*  854 */         for (byte b = 0; b < arrayOfDetailTable.length; b++) {
/*      */           
/*  856 */           DetailTable detailTable = arrayOfDetailTable[b];
/*  857 */           int i1 = Util.getIntValue(detailTable.getId());
/*  858 */           if (i1 >= 1)
/*      */             
/*      */             try {
/*      */               
/*  862 */               String str = arrayList.get(i1 - 1);
/*      */               
/*  864 */               if (bool1) {
/*  865 */                 str6 = "select * from workflow_billfield where billid=" + str4 + " and viewtype='1' ";
/*      */               } else {
/*  867 */                 str6 = "select * from workflow_billfield where billid=" + str4 + " and viewtype='1' and detailtable='" + str + "'";
/*      */               } 
/*  869 */               recordSet1.executeSql(str6);
/*  870 */               Row[] arrayOfRow = detailTable.getRow();
/*  871 */               for (byte b1 = 0; b1 < arrayOfRow.length; b1++)
/*      */               {
/*  873 */                 Row row = arrayOfRow[b1];
/*  874 */                 String str7 = "insert into " + str + "(" + str5;
/*  875 */                 String str8 = " values(" + k;
/*  876 */                 recordSet1.beforFirst();
/*  877 */                 while (recordSet1.next()) {
/*      */                   
/*  879 */                   String str9 = recordSet1.getString("fieldname");
/*  880 */                   Cell cell = getCellByName(row, str9);
/*  881 */                   if (cell != null) {
/*      */                     
/*  883 */                     str7 = str7 + "," + str9;
/*  884 */                     String str10 = Util.null2String(recordSet1.getString("fielddbtype"));
/*  885 */                     if (str10.toUpperCase().indexOf("INT") >= 0 || str10
/*  886 */                       .toUpperCase().indexOf("NUMBER") >= 0 || str10
/*  887 */                       .toUpperCase().indexOf("DECIMAL") >= 0 || str10
/*  888 */                       .toUpperCase().indexOf("FLOAT") >= 0) {
/*      */                       
/*  890 */                       str8 = str8 + "," + cell.getValue();
/*      */                       
/*      */                       continue;
/*      */                     } 
/*      */                     
/*  895 */                     str8 = str8 + ",'" + parseSpecialChar(recordSet.getDBType(), cell.getValue()) + "'";
/*      */                   } 
/*      */                 } 
/*      */                 
/*  899 */                 str7 = str7 + ")";
/*  900 */                 str8 = str8 + ")";
/*  901 */                 recordSet.executeSql(str7 + str8);
/*      */               }
/*      */             
/*  904 */             } catch (Exception exception) {
/*      */               
/*  906 */               writeLog(exception);
/*      */             }  
/*      */         } 
/*      */       } 
/*      */     } 
/*  911 */     return "" + j;
/*      */   }
/*      */ 
/*      */   
/*      */   private Cell getCellByName(Row paramRow, String paramString) {
/*  916 */     Cell[] arrayOfCell = paramRow.getCell();
/*  917 */     if (arrayOfCell != null)
/*      */     {
/*  919 */       for (byte b = 0; b < arrayOfCell.length; b++) {
/*      */         
/*  921 */         Cell cell = arrayOfCell[b];
/*  922 */         if (cell.getName().equalsIgnoreCase(paramString))
/*      */         {
/*  924 */           return cell;
/*      */         }
/*      */       } 
/*      */     }
/*  928 */     return null;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   private static String parseSpecialChar(String paramString1, String paramString2) {
/*  939 */     String str = Util.null2String(paramString2);
/*  940 */     if (!paramString2.equals("")) {
/*      */       
/*  942 */       if (str.indexOf("'") > -1)
/*      */       {
/*  944 */         str = str.replaceAll("'", "''");
/*      */       }
/*  946 */       if (paramString1.equalsIgnoreCase("oracle"))
/*      */       {
/*  948 */         if (str.indexOf("&") > -1)
/*      */         {
/*  950 */           str = str.replaceAll("&", "'||chr(38)||'");
/*      */         }
/*      */       }
/*      */     } 
/*  954 */     return str;
/*      */   }
/*      */ 
/*      */   
/*      */   private List getPropertyByName(MainTableInfo paramMainTableInfo, String paramString) {
/*  959 */     ArrayList<Property> arrayList = new ArrayList();
/*  960 */     Property[] arrayOfProperty = paramMainTableInfo.getProperty();
/*  961 */     if (arrayOfProperty != null)
/*      */     {
/*  963 */       for (byte b = 0; b < arrayOfProperty.length; b++) {
/*      */         
/*  965 */         Property property = arrayOfProperty[b];
/*      */         
/*  967 */         if (property.getName().equalsIgnoreCase(paramString))
/*      */         {
/*  969 */           arrayList.add(property);
/*      */         }
/*      */       } 
/*      */     }
/*  973 */     return arrayList;
/*      */   }
/*      */ 
/*      */   
/*      */   private String addAttachments(String[] paramArrayOfString1, String[] paramArrayOfString2, String paramString, User paramUser) {
/*  978 */     byte b = 0;
/*  979 */     String str = "";
/*      */     
/*  981 */     if (paramArrayOfString2 != null && paramArrayOfString2.length > 0) {
/*      */       
/*  983 */       FileProcessor fileProcessor = new FileProcessor();
/*  984 */       String[] arrayOfString = fileProcessor.Process(paramArrayOfString2, paramString, paramUser, paramArrayOfString1);
/*  985 */       for (b = 0; b < arrayOfString.length; b++) {
/*      */         
/*  987 */         if (arrayOfString[b] != null && !arrayOfString[b].equals("")) {
/*      */           
/*  989 */           str = str + "," + arrayOfString[b];
/*      */         }
/*      */         else {
/*      */           
/*  993 */           System.out.println("文件上传中第" + (b + 1) + "个出错！！");
/*      */           break;
/*      */         } 
/*      */       } 
/*  997 */       if (b < arrayOfString.length) {
/*      */ 
/*      */         
/* 1000 */         for (int i = b - 1; i >= 0; i--) {
/*      */           
/* 1002 */           int j = Util.getIntValue(arrayOfString[i]);
/* 1003 */           deleteDoc(j);
/*      */         } 
/* 1005 */         return "";
/*      */       } 
/* 1007 */       str = str.substring(1);
/*      */     } 
/*      */     
/* 1010 */     return str;
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   private void deleteDoc(int paramInt) {
/*      */     try {
/* 1017 */       RecordSet recordSet1 = new RecordSet();
/* 1018 */       RecordSet recordSet2 = new RecordSet();
/* 1019 */       RecordSet recordSet3 = new RecordSet();
/* 1020 */       String str1 = "delete from DocDetail where id=" + paramInt;
/* 1021 */       String str2 = "delete from DocShare where docid=" + paramInt;
/* 1022 */       String str3 = "delete from DocShareDetail where docid=" + paramInt;
/* 1023 */       String str4 = "delete from DocImageFile where docid=" + paramInt;
/* 1024 */       String str5 = "delete from docreadtag where docid=" + paramInt;
/* 1025 */       String str6 = "select  imagefileid  from DocImageFile where docid=" + paramInt;
/* 1026 */       recordSet1.executeSql(str6);
/* 1027 */       while (recordSet1.next()) {
/*      */         
/* 1029 */         String str7 = Util.null2String(recordSet1.getString(1));
/* 1030 */         String str8 = "select  filerealpath from imagefile  where imagefileid=" + str7;
/* 1031 */         recordSet2.executeSql(str8);
/* 1032 */         if (recordSet2.next()) {
/*      */           
/*      */           try {
/*      */             
/* 1036 */             File file = new File(Util.null2String(recordSet2.getString(1)));
/* 1037 */             file.delete();
/* 1038 */             recordSet3.executeSql("delete from imagefile where imagefileid=" + str7);
/*      */           }
/* 1040 */           catch (Exception exception) {}
/*      */         }
/*      */       } 
/*      */ 
/*      */ 
/*      */       
/* 1046 */       recordSet1.executeSql(str1);
/* 1047 */       recordSet1.executeSql(str2);
/* 1048 */       recordSet1.executeSql(str3);
/* 1049 */       recordSet1.executeSql(str4);
/* 1050 */       recordSet1.executeSql(str5);
/*      */     }
/* 1052 */     catch (Exception exception) {
/*      */       
/* 1054 */       writeLog(exception);
/*      */     } 
/*      */   }
/*      */ 
/*      */   
/*      */   public Map getDataMap() {
/* 1060 */     return this.dataMap;
/*      */   }
/*      */ 
/*      */   
/*      */   public void setDataMap(Map paramMap) {
/* 1065 */     this.dataMap = paramMap;
/*      */   }
/*      */ 
/*      */   
/*      */   public List getBhSelectList() {
/* 1070 */     return this.bhSelectList;
/*      */   }
/*      */ 
/*      */   
/*      */   public void setBhSelectList(List paramList) {
/* 1075 */     this.bhSelectList = paramList;
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/wscheck/UpdateWorkflow.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */