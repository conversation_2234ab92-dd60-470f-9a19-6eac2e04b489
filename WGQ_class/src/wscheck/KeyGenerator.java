/*     */ package wscheck;
/*     */ 
/*     */ import java.io.File;
/*     */ import java.lang.reflect.Method;
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ import java.util.Random;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ 
/*     */ public class KeyGenerator
/*     */   extends BaseBean
/*     */ {
/*  19 */   StringBuffer message = null;
/*     */   private User user;
/*     */   boolean valid = true;
/*     */   
/*     */   public KeyGenerator() {
/*  24 */     this.message = new StringBuffer();
/*  25 */     this.valid = true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public List getResourceBytes(String paramString) {
/*  35 */     ArrayList arrayList = new ArrayList();
/*  36 */     File file = new File(paramString);
/*  37 */     setFileList(arrayList, file, paramString, ".jsp", ".class");
/*  38 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void setFileList(List<String> paramList, File paramFile, String paramString1, String paramString2, String paramString3) {
/*  51 */     File[] arrayOfFile = paramFile.listFiles();
/*  52 */     for (byte b = 0; arrayOfFile != null && b < arrayOfFile.length; b++) {
/*  53 */       String str1 = arrayOfFile[b].getAbsolutePath();
/*     */       
/*  55 */       str1 = str1.replaceAll("\\\\", "/");
/*  56 */       paramString1 = paramString1.replaceAll("\\\\", "/");
/*  57 */       String str2 = str1.replaceAll(paramString1, "");
/*     */       
/*  59 */       if (str2.indexOf("/") == -1 || str2.indexOf("/") > 0) {
/*  60 */         str2 = "" + File.separatorChar + str2;
/*     */       }
/*     */       
/*  63 */       if (str2.indexOf("/ecology") == 0) {
/*  64 */         str2 = str2.replaceAll("/ecology", "");
/*     */         
/*  66 */         String str = MD5Coder.stringMD5(str2);
/*     */         
/*  68 */         if (arrayOfFile[b].isFile() && (
/*  69 */           str1.lastIndexOf(paramString2) > -1 || str1.lastIndexOf(paramString3) > -1)) {
/*     */           try {
/*  71 */             String str3 = MD5Coder.fileMD5(arrayOfFile[b], paramString1);
/*     */             
/*  73 */             paramList.add(SecurityHelper.encrypt("weaververify", str + "=" + str3));
/*  74 */           } catch (Exception exception) {}
/*     */         }
/*     */ 
/*     */ 
/*     */         
/*  79 */         if (arrayOfFile[b].isDirectory() && 
/*  80 */           str1.indexOf("WEB-INF") <= -1 && str1.indexOf("wscheck") <= -1 && str1.indexOf("keygenerator") <= -1 && str1.indexOf("src") <= -1 && str1.indexOf("data") <= -1 && str1.indexOf("sqlupgrade") <= -1 && str1.indexOf("js") <= -1)
/*     */         {
/*     */ 
/*     */           
/*  84 */           setFileList(paramList, arrayOfFile[b], paramString1, paramString2, paramString3);
/*     */         }
/*     */       } else {
/*     */         
/*  88 */         this.message.append("上传的文件解压路径，请确认路径是否正确!");
/*  89 */         this.valid = false;
/*     */         break;
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String unzipUpdatePack(String paramString) {
/* 104 */     Random random = new Random();
/* 105 */     int i = 1 + random.nextInt(26);
/* 106 */     long l = Math.abs(random.nextLong());
/* 107 */     String str1 = Util.getCharString(i) + l;
/* 108 */     String str2 = GCONST.getRootPath() + "filesystem" + File.separatorChar + "updatetemp" + File.separatorChar + str1;
/*     */ 
/*     */     
/* 111 */     ZipUtils.deleteFile(str2);
/*     */     try {
/* 113 */       ZipUtils.unZip(paramString, str2);
/* 114 */     } catch (Exception exception) {
/* 115 */       ZipUtils.deleteFile(str2);
/* 116 */       return "";
/*     */     } 
/* 118 */     return str2;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private List getMD5(List<File> paramList) {
/* 128 */     ArrayList<String> arrayList = new ArrayList();
/* 129 */     ArrayList arrayList1 = new ArrayList();
/* 130 */     if (null != paramList && paramList.size() > 0) {
/* 131 */       byte b; for (b = 0; b < paramList.size(); b++) {
/* 132 */         File file = paramList.get(b);
/*     */         
/* 134 */         if (ZipUtils.checkZip(file.getAbsolutePath())) {
/*     */ 
/*     */           
/* 137 */           String str = unzipUpdatePack(file.getAbsolutePath());
/*     */           
/* 139 */           if (str.equals("")) {
/* 140 */             this.message.append("上传的文件解压失败，请确认格式是否正确!");
/* 141 */             this.valid = false;
/*     */             break;
/*     */           } 
/* 144 */           File file1 = new File(str);
/*     */           
/* 146 */           if (file1.exists()) {
/* 147 */             List list = getResourceBytes(str);
/* 148 */             if (this.message.toString().equals("")) {
/* 149 */               arrayList1.addAll(list);
/*     */             } else {
/* 151 */               this.valid = false;
/*     */             } 
/*     */           } 
/*     */           
/* 155 */           arrayList.add(str);
/*     */         } else {
/*     */           
/* 158 */           this.message.append("上传的文件解压失败，请确认格式是否正确!");
/* 159 */           this.valid = false;
/*     */           break;
/*     */         } 
/*     */       } 
/* 163 */       for (b = 0; b < arrayList.size(); b++) {
/* 164 */         String str = arrayList.get(b);
/* 165 */         ZipUtils.deleteFile(str);
/*     */       } 
/*     */     } else {
/* 168 */       this.message.append("上传的文件解压失败，请确认格式是否正确!");
/* 169 */       this.valid = false;
/*     */     } 
/* 171 */     return arrayList1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String generatorinitkey(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/* 182 */     String str1 = "";
/* 183 */     String str2 = "";
/*     */     try {
/* 185 */       StringBuffer stringBuffer = new StringBuffer();
/* 186 */       String str3 = TimeUtil.getCurrentDateString();
/* 187 */       String str4 = TimeUtil.getCurrentTimeString().substring(11);
/* 188 */       stringBuffer.append(SecurityHelper.encrypt("weaververify", MD5Coder.stringMD5("createdate") + "=" + MD5Coder.stringMD5(str3 + " " + str4)));
/* 189 */       stringBuffer.append("\n");
/* 190 */       stringBuffer.append(SecurityHelper.encrypt("weaververify", MD5Coder.stringMD5("test") + "=" + MD5Coder.stringMD5("test")));
/* 191 */       stringBuffer.append("\n");
/* 192 */       stringBuffer.append(SecurityHelper.encrypt("weaververify", MD5Coder.stringMD5("createversion") + "=" + MD5Coder.stringMD5("1")));
/* 193 */       stringBuffer.append("\n");
/* 194 */       stringBuffer.append(SecurityHelper.encrypt("weaververify", MD5Coder.stringMD5("weaver") + "=" + MD5Coder.stringMD5("weaver")));
/* 195 */       stringBuffer.append("\n");
/* 196 */       stringBuffer.append(SecurityHelper.encrypt("weaververify", MD5Coder.stringMD5("test") + "=" + MD5Coder.stringMD5("test")));
/* 197 */       String str5 = "ecology.key";
/* 198 */       str1 = GCONST.getRootPath() + "filesystem" + File.separatorChar + "updatetemp" + File.separatorChar + str5;
/*     */ 
/*     */       
/* 201 */       CommonUtil.rewritefile(str1, stringBuffer.toString());
/*     */       
/* 203 */       str2 = str1.replace(GCONST.getRootPath(), "");
/* 204 */       str2 = str2.replaceAll("\\\\", "/");
/* 205 */       if (str2.indexOf("/") == -1 || str2.indexOf("/") > 0) {
/* 206 */         str2 = "/" + str2;
/*     */       }
/* 208 */     } catch (Exception exception) {}
/*     */ 
/*     */     
/* 211 */     return str2;
/*     */   }
/*     */   
/*     */   public static void main(String[] paramArrayOfString) {
/*     */     try {
/* 216 */       String str1 = "d:\\ecology.key";
/* 217 */       String str2 = str1;
/* 218 */       File file = new File(str2);
/* 219 */       if (!file.exists()) {
/*     */ 
/*     */         
/* 222 */         Class<?> clazz = Class.forName("wscheck.CommonUtil");
/* 223 */         Object object = clazz.newInstance();
/* 224 */         Class[] arrayOfClass = new Class[2];
/* 225 */         arrayOfClass[0] = String.class;
/* 226 */         arrayOfClass[1] = String.class;
/* 227 */         Method method = clazz.getMethod("fileAppend", arrayOfClass);
/* 228 */         Object[] arrayOfObject = new Object[2];
/* 229 */         arrayOfObject[0] = str2;
/* 230 */         String str3 = TimeUtil.getCurrentDateString();
/* 231 */         String str4 = TimeUtil.getCurrentTimeString().substring(11);
/* 232 */         arrayOfObject[1] = SecurityHelper.encrypt("weaververify", MD5Coder.stringMD5("createdate") + "=" + MD5Coder.stringMD5(str3 + " " + str4));
/* 233 */         method.invoke(object, arrayOfObject);
/* 234 */         arrayOfObject = new Object[2];
/* 235 */         arrayOfObject[0] = str2;
/* 236 */         arrayOfObject[1] = SecurityHelper.encrypt("weaververify", MD5Coder.stringMD5("test") + "=" + MD5Coder.stringMD5("test"));
/* 237 */         method.invoke(object, arrayOfObject);
/* 238 */         arrayOfObject = new Object[2];
/* 239 */         arrayOfObject[0] = str2;
/* 240 */         arrayOfObject[1] = SecurityHelper.encrypt("weaververify", MD5Coder.stringMD5("createversion") + "=" + MD5Coder.stringMD5("1"));
/* 241 */         method.invoke(object, arrayOfObject);
/* 242 */         arrayOfObject = new Object[2];
/* 243 */         arrayOfObject[0] = str2;
/* 244 */         arrayOfObject[1] = SecurityHelper.encrypt("weaververify", MD5Coder.stringMD5("weaver") + "=" + MD5Coder.stringMD5("weaver"));
/* 245 */         method.invoke(object, arrayOfObject);
/*     */       } 
/* 247 */     } catch (Exception exception) {}
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public User getUser() {
/* 253 */     return this.user;
/*     */   }
/*     */   
/*     */   public void setUser(User paramUser) {
/* 257 */     this.user = paramUser;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/wscheck/KeyGenerator.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */