/*     */ package wscheck;
/*     */ import java.io.BufferedReader;
/*     */ import java.io.File;
/*     */ import java.io.FileInputStream;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Date;
/*     */ import java.util.HashSet;
/*     */ import java.util.Iterator;
/*     */ import java.util.Map;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.hrm.User;
/*     */ 
/*     */ public class CheckScanAllFile {
/*  14 */   private static final Log log = LogFactory.getLog(CheckScanAllFile.class);
/*  15 */   Map md5map = new HashMap<>();
/*  16 */   Map md5map2 = new HashMap<>();
/*     */   
/*     */   public static final String jsptype = ".jsp";
/*     */   
/*     */   public static final String javatype = ".java";
/*     */   
/*     */   public static final String classtype = ".class";
/*     */   public static final String jstype = ".js";
/*     */   public static final String csstype = ".css";
/*     */   public static final String jartype = ".jar";
/*     */   public static final String lesstype = ".less";
/*     */   public static final String jsontype = ".json";
/*     */   public static final String mdtype = ".md";
/*     */   public static final String gitignoretype = ".gitignore";
/*     */   public static final String ziptype = ".zip";
/*  31 */   public String scanfiletypesE9 = ".jsp|.java|.class|.js|.css|.jar|.less|.json|.md|.gitignore";
/*     */   private boolean iscontentdate = false;
/*  33 */   private String customerid = "";
/*  34 */   Set excludeFileArr = new HashSet();
/*  35 */   Set excludeDirArr = new HashSet();
/*  36 */   Set forceFileArr = new HashSet();
/*  37 */   Set forceDirArr = new HashSet();
/*  38 */   public String forcefiletype = "";
/*     */   
/*     */   public boolean isIscontentdate() {
/*  41 */     return this.iscontentdate;
/*     */   }
/*     */   public void setIscontentdate(boolean paramBoolean) {
/*  44 */     this.iscontentdate = paramBoolean;
/*     */   }
/*     */   public CheckScanAllFile() {
/*  47 */     Properties properties = new Properties();
/*  48 */     FileInputStream fileInputStream = null;
/*     */     try {
/*  50 */       fileInputStream = new FileInputStream(GCONST.getRootPath() + File.separatorChar + "keygenerator" + File.separatorChar + "scanfiletype.properties");
/*  51 */       properties.load(fileInputStream);
/*  52 */       String str = Util.null2String(properties.getProperty("filetype"));
/*  53 */       if (!str.equals("")) {
/*  54 */         this.scanfiletypesE9 = str;
/*     */       }
/*  56 */     } catch (Exception exception) {
/*  57 */       exception.printStackTrace();
/*     */     } finally {
/*     */       
/*  60 */       if (fileInputStream != null) {
/*     */         try {
/*  62 */           fileInputStream.close();
/*  63 */         } catch (Exception exception) {}
/*     */       }
/*     */     } 
/*     */ 
/*     */     
/*  68 */     Map map = excludeFile();
/*  69 */     this.excludeFileArr = (HashSet)map.get("file");
/*  70 */     this.excludeDirArr = (HashSet)map.get("dir");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public CheckScanAllFile(Map paramMap) {
/*  77 */     boolean bool = false;
/*  78 */     Iterator<String> iterator = paramMap.keySet().iterator();
/*  79 */     while (iterator.hasNext()) {
/*  80 */       String str = iterator.next();
/*  81 */       if (str.startsWith("$$scanfiletype$$")) {
/*  82 */         String str1 = (String)paramMap.get(str);
/*  83 */         if (str1.contains("==") && str1.indexOf("==") > 0) {
/*  84 */           bool = true;
/*  85 */           this.scanfiletypesE9 = str1.substring(0, str1.indexOf("=="));
/*     */ 
/*     */           
/*  88 */           iterator.remove();
/*  89 */           paramMap.remove(str);
/*     */           
/*     */           break;
/*     */         } 
/*     */       } 
/*     */     } 
/*  95 */     if (!bool) {
/*  96 */       Properties properties = new Properties();
/*  97 */       FileInputStream fileInputStream = null;
/*     */       try {
/*  99 */         fileInputStream = new FileInputStream(GCONST.getRootPath() + File.separatorChar + "keygenerator" + File.separatorChar + "scanfiletype.properties");
/* 100 */         properties.load(fileInputStream);
/* 101 */         String str = Util.null2String(properties.getProperty("filetype"));
/* 102 */         if (!str.equals("")) {
/* 103 */           this.scanfiletypesE9 = str;
/*     */         }
/* 105 */       } catch (Exception exception) {
/* 106 */         exception.printStackTrace();
/*     */       } finally {
/* 108 */         if (fileInputStream != null) {
/*     */           try {
/* 110 */             fileInputStream.close();
/* 111 */           } catch (Exception exception) {}
/*     */         }
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 121 */     getForceCompareFile(paramMap);
/*     */ 
/*     */ 
/*     */     
/* 125 */     Map map = excludeFile();
/* 126 */     this.excludeFileArr = (HashSet)map.get("file");
/* 127 */     this.excludeDirArr = (HashSet)map.get("dir");
/*     */ 
/*     */     
/* 130 */     getExcludeCompareFile(paramMap);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void getExcludeCompareFile(Map paramMap) {
/* 139 */     String str = null;
/* 140 */     Iterator<String> iterator = paramMap.keySet().iterator();
/* 141 */     while (iterator.hasNext()) {
/* 142 */       String str1 = iterator.next();
/* 143 */       if (str1.startsWith("$$excludeFile$$")) {
/* 144 */         String str2 = (String)paramMap.get(str1);
/* 145 */         if (str2.contains("==") && str2.indexOf("==") > 0) {
/* 146 */           str = str2.substring(0, str2.indexOf("=="));
/*     */ 
/*     */           
/* 149 */           if (!"".equals(str) && !str.startsWith("#")) {
/* 150 */             str = str.replaceAll("\\\\", "/");
/* 151 */             if (str.startsWith("*") && str.endsWith("*")) {
/* 152 */               this.excludeDirArr.add(str);
/* 153 */             } else if (str.startsWith("/") && (str.endsWith("/") || !str.contains("."))) {
/* 154 */               this.excludeDirArr.add(str);
/*     */             } else {
/* 156 */               this.excludeFileArr.add(str);
/*     */             } 
/*     */           } 
/*     */ 
/*     */           
/* 161 */           iterator.remove();
/* 162 */           paramMap.remove(str1);
/*     */         } 
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void getForceCompareFile(Map paramMap) {
/* 175 */     BufferedReader bufferedReader = null;
/* 176 */     String str = GCONST.getRootPath() + "keygenerator" + File.separatorChar + "forceCompareFile.txt";
/*     */     try {
/* 178 */       String str1 = "";
/*     */ 
/*     */       
/* 181 */       File file = new File(str);
/* 182 */       if (file.exists() && file.length() > 0L) {
/* 183 */         bufferedReader = new BufferedReader(new InputStreamReader(new FileInputStream(str)));
/* 184 */         while ((str1 = bufferedReader.readLine()) != null)
/*     */         {
/*     */           
/* 187 */           getForceRecordFile(str1);
/*     */         }
/* 189 */         bufferedReader.close();
/*     */       } 
/*     */ 
/*     */       
/* 193 */       if (paramMap.size() > 0) {
/* 194 */         Iterator<String> iterator = paramMap.keySet().iterator();
/* 195 */         while (iterator.hasNext()) {
/* 196 */           String str2 = iterator.next();
/* 197 */           if (str2.startsWith("$$forceCompareFile$$")) {
/* 198 */             String str3 = (String)paramMap.get(str2);
/* 199 */             if (str3.contains("==") && str3.indexOf("==") > 0) {
/* 200 */               str1 = str3.substring(0, str3.indexOf("=="));
/*     */ 
/*     */               
/* 203 */               getForceRecordFile(str1);
/*     */ 
/*     */               
/* 206 */               iterator.remove();
/* 207 */               paramMap.remove(str2);
/*     */             }
/*     */           
/*     */           } 
/*     */         } 
/*     */       } 
/* 213 */     } catch (Exception exception) {
/* 214 */       exception.printStackTrace();
/*     */     } finally {
/*     */       try {
/* 217 */         if (bufferedReader != null)
/* 218 */           bufferedReader.close(); 
/* 219 */       } catch (IOException iOException) {
/* 220 */         iOException.printStackTrace();
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void getForceRecordFile(String paramString) {
/* 231 */     if (paramString != null && !"".equals(paramString) && !paramString.startsWith("#")) {
/* 232 */       if (paramString.startsWith("/") && paramString.endsWith("/")) {
/* 233 */         this.forceDirArr.add(paramString);
/* 234 */       } else if (paramString.startsWith("/") && !paramString.endsWith("/") && paramString.contains(".")) {
/* 235 */         this.forceFileArr.add(paramString);
/*     */       } 
/*     */     }
/*     */   }
/*     */ 
/*     */   
/*     */   public String getMD5File(String paramString, User paramUser) {
/* 242 */     String str1 = GCONST.getRootPath();
/* 243 */     String str2 = "data" + File.separatorChar + "tempkey" + File.separatorChar + "ecology.key";
/* 244 */     File file1 = new File(str1 + str2);
/* 245 */     if (file1.exists())
/*     */     {
/* 247 */       file1.delete();
/*     */     }
/* 249 */     File file2 = new File(paramString);
/* 250 */     setFileMap(file2, paramString, null, paramUser);
/* 251 */     return str1 + str2;
/*     */   }
/*     */   
/*     */   public String getMD5FileWithCid(String paramString1, String paramString2, String paramString3, String paramString4) {
/* 255 */     this.customerid = paramString3;
/* 256 */     String str1 = GCONST.getRootPath();
/* 257 */     String str2 = "tempkey";
/* 258 */     if (paramString4 != null && paramString4.equals("generatekey2")) {
/* 259 */       str2 = "tempcustomerkey";
/*     */     }
/* 261 */     String str3 = "data" + File.separatorChar + str2 + File.separatorChar + "ecology_" + paramString3 + ".key";
/* 262 */     File file1 = new File(str1 + str3);
/* 263 */     if (file1.exists())
/*     */     {
/* 265 */       file1.delete();
/*     */     }
/* 267 */     File file2 = new File(paramString1);
/* 268 */     setFileMap(file2, paramString2, paramString4, new User());
/* 269 */     return str1 + str3;
/*     */   }
/*     */ 
/*     */   
/*     */   private Map excludeFile() {
/* 274 */     HashSet<String> hashSet1 = new HashSet();
/* 275 */     HashSet<String> hashSet2 = new HashSet();
/* 276 */     HashMap<Object, Object> hashMap = new HashMap<>();
/*     */ 
/*     */ 
/*     */     
/* 280 */     File file = new File(GCONST.getRootPath() + "keygenerator" + File.separatorChar + "excludeFile.txt");
/* 281 */     if (file.exists()) {
/*     */       try {
/* 283 */         FileReader fileReader = new FileReader(file);
/* 284 */         BufferedReader bufferedReader = new BufferedReader(fileReader);
/* 285 */         String str = null;
/* 286 */         while ((str = bufferedReader.readLine()) != null) {
/* 287 */           str = str.trim();
/* 288 */           if ("".equals(str) || 
/* 289 */             str.startsWith("#"))
/* 290 */             continue;  str = str.replace("\\", "/");
/* 291 */           if (str.startsWith("*") && str.endsWith("*")) {
/* 292 */             hashSet2.add(str); continue;
/* 293 */           }  if (str.endsWith("/") || str.indexOf(".") == -1) {
/* 294 */             hashSet2.add(str); continue;
/*     */           } 
/* 296 */           hashSet1.add(str);
/*     */         }
/*     */       
/*     */       }
/* 300 */       catch (Exception exception) {
/* 301 */         exception.printStackTrace();
/*     */       } 
/*     */     }
/* 304 */     hashMap.put("file", hashSet1);
/* 305 */     hashMap.put("dir", hashSet2);
/*     */     
/* 307 */     return hashMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void getFileMap(File paramFile, String paramString) {
/* 317 */     File[] arrayOfFile = paramFile.listFiles();
/* 318 */     boolean bool = false;
/*     */     try {
/* 320 */       for (byte b = 0; arrayOfFile != null && b < arrayOfFile.length; b++) {
/* 321 */         String str1 = arrayOfFile[b].getPath();
/* 322 */         String str2 = str1.substring(paramString.length());
/*     */         
/* 324 */         if (str2.indexOf("" + File.separatorChar) == -1 || str2.indexOf("" + File.separatorChar) > 0) {
/* 325 */           str2 = "" + File.separatorChar + str2;
/*     */         }
/*     */         
/* 328 */         str2 = str2.replaceAll("\\\\", "/");
/* 329 */         if (arrayOfFile[b].isFile()) {
/* 330 */           bool = false;
/*     */ 
/*     */ 
/*     */           
/* 334 */           String str3 = arrayOfFile[b].getAbsolutePath();
/* 335 */           String str4 = GCONST.getRootPath();
/* 336 */           if (str4.endsWith(File.separator)) {
/* 337 */             str4 = str4 + "WEB-INF" + File.separator;
/*     */           } else {
/* 339 */             str4 = str4 + File.separator + "WEB-INF" + File.separator;
/*     */           } 
/* 341 */           if (str3.contains(str4)) {
/* 342 */             String str = str3.substring(str4.length());
/*     */             
/* 344 */             if (!str.contains(File.separator)) {
/*     */               continue;
/*     */             }
/*     */           } 
/*     */ 
/*     */           
/* 350 */           boolean bool1 = rightFileType(str1, str2).booleanValue();
/* 351 */           if (bool1) {
/* 352 */             Long long_ = Long.valueOf((new Date()).getTime());
/* 353 */             bool = checkExtendFile(str2);
/* 354 */             if (bool) {
/*     */               continue;
/*     */             }
/* 357 */             if (str2.endsWith("map")) {
/*     */               continue;
/*     */             }
/* 360 */             String str5 = str2.substring(0, str2.lastIndexOf(".")).toLowerCase();
/* 361 */             if (str5.endsWith("bak") || str5.contains("副本") || str5.contains("复制") || str5.contains("复件") || str5.contains("备份") || str5.indexOf(")") > 0 || str5.indexOf("(") > 0) {
/*     */               continue;
/*     */             }
/* 364 */             String str6 = getMD5(arrayOfFile[b]);
/* 365 */             this.md5map.put(str2, str6);
/*     */           } 
/*     */         } 
/*     */         
/* 369 */         if (arrayOfFile[b].isDirectory()) {
/* 370 */           boolean bool1 = true;
/* 371 */           String str = str2 + "/";
/*     */           
/* 373 */           if ((str.indexOf("/WEB-INF") == 0 && !str.replace("/WEB-INF", "").equals("/") && str.indexOf("/lib/") < 0 && str.indexOf("/classes/") < 0) || str
/*     */             
/* 375 */             .indexOf("/wscheck/") > 0 || str
/* 376 */             .indexOf("/keygenerator/") == 0 || str
/* 377 */             .indexOf("/updatetemp/") == 0 || str
/* 378 */             .indexOf("/src/") == 0 || str
/* 379 */             .indexOf("/data/") == 0 || str
/* 380 */             .indexOf("/filesystem/") == 0 || str
/* 381 */             .indexOf("/log/") == 0 || str
/* 382 */             .indexOf("/sqlupgrade/") == 0 || str
/* 383 */             .indexOf("/jsp/") == 0 || str.indexOf("_ubak") > 0 || str.contains("副本") || str.toLowerCase().endsWith("bak/") || str.contains("复件") || str.contains("备份") || str.contains("复制"))
/*     */           {
/* 385 */             bool1 = false;
/*     */           }
/* 387 */           if (bool1) {
/* 388 */             bool1 = !checkExtendFile(str2) ? true : false;
/*     */           }
/* 390 */           if (bool1)
/* 391 */             getFileMap(arrayOfFile[b], paramString); 
/*     */         } 
/*     */         continue;
/*     */       } 
/* 395 */     } catch (Exception exception) {
/* 396 */       log.error("==================================CheckScanAllFile.getFileMap Exception:" + exception.toString());
/* 397 */       exception.printStackTrace();
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void getFileMap2(File paramFile, String paramString, Map<String, String> paramMap) {
/* 407 */     File[] arrayOfFile = paramFile.listFiles();
/* 408 */     boolean bool = false;
/*     */     try {
/* 410 */       for (byte b = 0; arrayOfFile != null && b < arrayOfFile.length; b++) {
/* 411 */         String str1 = arrayOfFile[b].getPath();
/* 412 */         String str2 = str1.substring(paramString.length());
/*     */         
/* 414 */         if (str2.indexOf("" + File.separatorChar) == -1 || str2.indexOf("" + File.separatorChar) > 0) {
/* 415 */           str2 = "" + File.separatorChar + str2;
/*     */         }
/*     */         
/* 418 */         str2 = str2.replaceAll("\\\\", "/");
/* 419 */         if (arrayOfFile[b].isFile()) {
/* 420 */           bool = false;
/*     */ 
/*     */ 
/*     */ 
/*     */           
/* 425 */           String str3 = arrayOfFile[b].getAbsolutePath();
/* 426 */           String str4 = GCONST.getRootPath();
/* 427 */           if (str4.endsWith(File.separator)) {
/* 428 */             str4 = str4 + "WEB-INF" + File.separator;
/*     */           } else {
/* 430 */             str4 = str4 + File.separator + "WEB-INF" + File.separator;
/*     */           } 
/* 432 */           if (str3.contains(str4)) {
/* 433 */             String str = str3.substring(str4.length());
/*     */             
/* 435 */             if (!str.contains(File.separator)) {
/*     */               continue;
/*     */             }
/*     */           } 
/*     */ 
/*     */ 
/*     */           
/* 442 */           boolean bool1 = rightFileType(str1, str2).booleanValue();
/* 443 */           if (bool1) {
/* 444 */             Long long_ = Long.valueOf((new Date()).getTime());
/* 445 */             bool = checkExtendFile(str2);
/* 446 */             if (bool) {
/*     */               continue;
/*     */             }
/* 449 */             if (str2.endsWith("map")) {
/*     */               continue;
/*     */             }
/* 452 */             String str5 = str2.substring(0, str2.lastIndexOf(".")).toLowerCase();
/* 453 */             if (str5.endsWith("bak") || str5.contains("副本") || str5.contains("复制") || str5.contains("复件") || str5.contains("备份") || str5.indexOf(")") > 0 || str5.indexOf("(") > 0) {
/*     */               continue;
/*     */             }
/* 456 */             if (paramMap.containsKey(str2)) {
/* 457 */               String str7 = Util.null2String(paramMap.get(str2));
/* 458 */               ArrayList arrayList = Util.TokenizerString(str7, "==");
/* 459 */               String str8 = (new StringBuilder()).append(arrayList.get(0)).append("").toString();
/*     */               
/* 461 */               String str9 = (arrayList.size() == 2) ? (new StringBuilder()).append(arrayList.get(1)).append("").toString() : "-";
/* 462 */               if (!"-".equals(str9)) {
/*     */                 
/*     */                 try {
/*     */ 
/*     */                   
/* 467 */                   Date date = new Date(arrayOfFile[b].lastModified());
/* 468 */                   String str = date.getTime() + "";
/*     */                   
/* 470 */                   if (str.compareTo(str9) == 0) {
/*     */                     
/* 472 */                     paramMap.remove(str2);
/*     */                     continue;
/*     */                   } 
/* 475 */                 } catch (Exception exception) {}
/*     */               }
/*     */             } 
/* 478 */             String str6 = getMD5(arrayOfFile[b]);
/* 479 */             this.md5map.put(str2, str6);
/*     */           } 
/*     */         } 
/*     */         
/* 483 */         if (arrayOfFile[b].isDirectory()) {
/* 484 */           boolean bool1 = true;
/* 485 */           String str = str2 + "/";
/*     */           
/* 487 */           if ((str.indexOf("/WEB-INF") == 0 && !str.replace("/WEB-INF", "").equals("/") && str.indexOf("/lib/") < 0 && str.indexOf("/classes/") < 0) || str
/* 488 */             .indexOf("/wscheck/") > 0 || str
/* 489 */             .indexOf("/keygenerator/") == 0 || str
/* 490 */             .indexOf("/updatetemp/") == 0 || str
/* 491 */             .indexOf("/src/") == 0 || str
/* 492 */             .indexOf("/data/") == 0 || str
/* 493 */             .indexOf("/filesystem/") == 0 || str
/* 494 */             .indexOf("/log/") == 0 || str
/* 495 */             .indexOf("/sqlupgrade/") == 0 || str
/* 496 */             .indexOf("/jsp/") == 0 || str.indexOf("_ubak") > 0 || str.contains("副本") || str.toLowerCase().endsWith("bak/") || str.contains("复件") || str.contains("备份") || str.contains("复制"))
/*     */           {
/* 498 */             bool1 = false;
/*     */           }
/* 500 */           if (bool1) {
/* 501 */             bool1 = !checkExtendFile(str2) ? true : false;
/*     */           }
/* 503 */           if (bool1) {
/* 504 */             getFileMap2(arrayOfFile[b], paramString, paramMap);
/*     */           }
/*     */         } 
/*     */         
/*     */         continue;
/*     */       } 
/* 510 */     } catch (Exception exception) {
/* 511 */       log.error("==================================CheckScanAllFile.getFileMap Exception:" + exception.toString());
/* 512 */       exception.printStackTrace();
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void getFileMap3(File paramFile, String paramString, Map paramMap, User paramUser, int paramInt) {
/* 523 */     File[] arrayOfFile = paramFile.listFiles();
/* 524 */     boolean bool = false;
/*     */     try {
/* 526 */       int i = paramInt + 1;
/* 527 */       if (i > 20) {
/* 528 */         log.info("checkScanAllFile533::::当前目录层级大于20，自动跳过，目录为：" + paramFile.getPath());
/*     */         return;
/*     */       } 
/* 531 */       for (byte b = 0; arrayOfFile != null && b < arrayOfFile.length; b++) {
/* 532 */         String str1 = arrayOfFile[b].getPath();
/* 533 */         String str2 = str1.substring(paramString.length());
/* 534 */         if (!str2.contains("" + File.separatorChar) || str2.indexOf("" + File.separatorChar) > 0) {
/* 535 */           str2 = "" + File.separatorChar + str2;
/*     */         }
/*     */         
/* 538 */         str2 = str2.replaceAll("\\\\", "/");
/*     */ 
/*     */         
/* 541 */         if (arrayOfFile[b].isFile()) {
/* 542 */           bool = false;
/*     */ 
/*     */           
/* 545 */           boolean bool1 = rightFileType(str1, str2).booleanValue();
/* 546 */           if (bool1) {
/* 547 */             if (str2.endsWith("map")) {
/*     */               continue;
/*     */             }
/*     */             
/* 551 */             String str3 = str2;
/* 552 */             if (str2.substring(str2.lastIndexOf("/")).indexOf(".") > 0) {
/* 553 */               str3 = str2.substring(0, str2.lastIndexOf(".")).toLowerCase();
/*     */             }
/* 555 */             if (str3.endsWith("bak") || str3.contains("副本") || str3.contains("复制") || str3
/* 556 */               .contains("复件") || str3.contains("备份") || str3.indexOf(")") > 0 || str3.indexOf("(") > 0) {
/*     */               continue;
/*     */             }
/*     */ 
/*     */             
/* 561 */             bool = checkExtendFile(str2);
/* 562 */             if (bool) {
/*     */               continue;
/*     */             }
/*     */             
/* 566 */             if (paramMap.containsKey(str2)) {
/* 567 */               String str5 = Util.null2String((String)paramMap.get(str2));
/* 568 */               ArrayList arrayList = Util.TokenizerString(str5, "==");
/* 569 */               String str6 = "";
/* 570 */               String str7 = "-";
/* 571 */               if (arrayList.size() >= 2) {
/* 572 */                 str6 = (new StringBuilder()).append(arrayList.get(0)).append("").toString();
/*     */                 
/* 574 */                 str7 = (arrayList.size() == 2) ? (new StringBuilder()).append(arrayList.get(1)).append("").toString() : "-";
/*     */               } 
/* 576 */               if (!"-".equals(str7))
/*     */                 
/*     */                 try {
/* 579 */                   Date date = new Date(arrayOfFile[b].lastModified());
/* 580 */                   String str = date.getTime() + "";
/* 581 */                   if (str.compareTo(str7) == 0) {
/*     */                     
/* 583 */                     this.md5map.remove(str2);
/* 584 */                     paramMap.remove(str2);
/*     */                     continue;
/*     */                   } 
/* 587 */                 } catch (Exception exception) {
/* 588 */                   exception.printStackTrace();
/*     */                 }  
/*     */             } 
/* 591 */             String str4 = getMD5(arrayOfFile[b]);
/* 592 */             this.md5map.put(str2, str4);
/*     */           } 
/*     */         } 
/*     */ 
/*     */         
/* 597 */         if (arrayOfFile[b].isDirectory()) {
/* 598 */           boolean bool1 = true;
/* 599 */           String str = str2 + "/";
/* 600 */           if (str.indexOf("_ubak") > 0 || str.contains("副本") || str.toLowerCase().endsWith("bak/") || str.contains("复制") || str
/* 601 */             .contains("复件") || str.contains("备份")) {
/* 602 */             bool1 = false;
/*     */           }
/* 604 */           if (bool1) {
/* 605 */             bool1 = !checkExtendFile(str) ? true : false;
/*     */           }
/* 607 */           if (bool1) {
/* 608 */             getFileMap3(arrayOfFile[b], paramString, paramMap, paramUser, i);
/*     */           }
/*     */         } 
/*     */         
/*     */         continue;
/*     */       } 
/* 614 */     } catch (Exception exception) {
/* 615 */       log.error("==================================CheckScanAllFile.getFileMap3 Exception:" + exception.toString());
/* 616 */       exception.printStackTrace();
/*     */     } 
/*     */   }
/*     */   
/*     */   private void setFileMap(File paramFile, String paramString1, String paramString2, User paramUser) {
/* 621 */     File[] arrayOfFile = paramFile.listFiles();
/* 622 */     boolean bool = false;
/*     */     try {
/* 624 */       for (byte b = 0; arrayOfFile != null && b < arrayOfFile.length; b++) {
/* 625 */         String str1 = arrayOfFile[b].getPath();
/* 626 */         String str2 = str1.substring(paramString1.length());
/*     */         
/* 628 */         if (str2.indexOf("" + File.separatorChar) == -1 || str2.indexOf("" + File.separatorChar) > 0) {
/* 629 */           str2 = "" + File.separatorChar + str2;
/*     */         }
/*     */         
/* 632 */         str2 = str2.replaceAll("\\\\", "/");
/*     */         
/* 634 */         if (arrayOfFile[b].isFile()) {
/* 635 */           bool = false;
/*     */ 
/*     */           
/* 638 */           boolean bool1 = rightFileType(str1, str2).booleanValue();
/* 639 */           if (bool1) {
/* 640 */             if (str2.endsWith("map")) {
/*     */               continue;
/*     */             }
/*     */             
/* 644 */             String str3 = str2;
/* 645 */             if (str2.substring(str2.lastIndexOf("/")).indexOf(".") > 0) {
/* 646 */               str3 = str2.substring(0, str2.lastIndexOf(".")).toLowerCase();
/*     */             }
/* 648 */             if (str3.endsWith("bak") || str3.contains("副本") || str3.contains("复制") || str3
/* 649 */               .contains("复件") || str3.contains("备份") || str3.indexOf(")") > 0 || str3.indexOf("(") > 0) {
/*     */               continue;
/*     */             }
/*     */             
/* 653 */             bool = checkExtendFile(str2);
/* 654 */             if (bool) {
/*     */               continue;
/*     */             }
/* 657 */             String str4 = getMD5(arrayOfFile[b]);
/* 658 */             this.md5map2.put(str2, str4);
/* 659 */             Date date = new Date(arrayOfFile[b].lastModified());
/* 660 */             String str5 = date.getTime() + "";
/* 661 */             if (!this.iscontentdate) {
/* 662 */               writeFile(SecurityHelper.encrypt("weaververify", str2 + "==" + str4), paramString2);
/*     */             } else {
/* 664 */               writeFile(SecurityHelper.encrypt("weaververify", str2 + "==" + str4 + "==" + str5), paramString2);
/*     */             } 
/*     */           } 
/*     */         } 
/*     */ 
/*     */         
/* 670 */         if (arrayOfFile[b].isDirectory()) {
/* 671 */           boolean bool1 = true;
/* 672 */           String str = str2 + "/";
/* 673 */           if (str.indexOf("_ubak") > 0 || str.contains("副本") || str.toLowerCase().endsWith("bak/") || str.contains("复制") || str
/* 674 */             .contains("复件") || str.contains("备份")) {
/* 675 */             bool1 = false;
/*     */           }
/* 677 */           if (bool1) {
/* 678 */             bool1 = !checkExtendFile(str) ? true : false;
/*     */           }
/* 680 */           if (bool1)
/* 681 */             setFileMap(arrayOfFile[b], paramString1, paramString2, paramUser); 
/*     */         } 
/*     */         continue;
/*     */       } 
/* 685 */     } catch (Exception exception) {
/* 686 */       log.error("==================================CheckScanAllFile.setFileMap Exception:" + exception.toString());
/* 687 */       exception.printStackTrace();
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getMD5(File paramFile) {
/* 699 */     String str = "";
/*     */     try {
/* 701 */       str = MD5Coder.fileMD5(paramFile);
/* 702 */     } catch (Exception exception) {
/* 703 */       exception.printStackTrace();
/* 704 */       str = "";
/*     */     } 
/*     */     
/* 707 */     return str;
/*     */   }
/*     */   
/*     */   private String getMD5(String paramString) {
/* 711 */     return MD5Coder.stringMD5(paramString);
/*     */   }
/*     */   
/*     */   private void writeFile(String paramString1, String paramString2) {
/* 715 */     String str1 = GCONST.getRootPath();
/* 716 */     String str2 = "tempkey";
/* 717 */     if (paramString2 != null && paramString2.equals("generatekey2")) {
/* 718 */       str2 = "tempcustomerkey";
/*     */     }
/*     */     
/* 721 */     String str3 = "data" + File.separatorChar + str2 + File.separatorChar + "ecology.key";
/* 722 */     if (this.customerid != null && !"".equals(this.customerid)) {
/* 723 */       str3 = "data" + File.separatorChar + str2 + File.separatorChar + "ecology_" + this.customerid + ".key";
/*     */     }
/* 725 */     CommonUtil.fileAppend(str1 + str3, paramString1);
/*     */   }
/*     */   
/*     */   public Map getMd5map() {
/* 729 */     return this.md5map;
/*     */   }
/*     */   public void setMd5map(Map paramMap) {
/* 732 */     this.md5map = paramMap;
/*     */   }
/*     */   
/*     */   public boolean isExtendFile(String paramString, HashSet paramHashSet, boolean paramBoolean) {
/* 736 */     boolean bool = false;
/* 737 */     String str = "";
/* 738 */     String[] arrayOfString = paramString.split("/");
/* 739 */     if (paramBoolean) log.info("=========path:" + paramString + "=====set.size:" + paramHashSet.size() + "========s.length:" + arrayOfString.length); 
/* 740 */     for (byte b = 0; b < arrayOfString.length; b++) {
/* 741 */       if (b == arrayOfString.length - 1 && arrayOfString[b].indexOf(".") >= 0) {
/* 742 */         str = paramString;
/*     */       } else {
/* 744 */         str = str + arrayOfString[b] + "/";
/*     */       } 
/* 746 */       if (paramBoolean) log.info("=========set.contains(p):" + paramHashSet.contains(str) + "=====p:" + str); 
/* 747 */       if (paramHashSet.contains(str) || paramHashSet.contains(str.substring(0, str.length() - 1))) {
/* 748 */         bool = true;
/*     */         break;
/*     */       } 
/*     */     } 
/* 752 */     return bool;
/*     */   }
/*     */   
/*     */   public boolean checkExtendFile(String paramString) {
/* 756 */     boolean bool = false;
/* 757 */     paramString = paramString.replace("\\", "/");
/* 758 */     if (paramString == null || paramString.trim().equals("") || paramString.indexOf("/") == -1) {
/* 759 */       return false;
/*     */     }
/* 761 */     boolean bool1 = (paramString.substring(paramString.lastIndexOf("/"), paramString.length()).indexOf(".") == -1) ? true : false;
/*     */     
/* 763 */     for (String str1 : this.excludeDirArr) {
/* 764 */       String str2 = str1;
/* 765 */       if (str2.startsWith("*") && str2.endsWith("*") && paramString.indexOf(str2.substring(1, str2.length() - 1)) >= 0) {
/* 766 */         bool = true; break;
/*     */       } 
/* 768 */       if (paramString.indexOf(str2) == 0) {
/* 769 */         bool = true;
/*     */         break;
/*     */       } 
/*     */     } 
/* 773 */     if (bool) return bool;
/*     */ 
/*     */     
/* 776 */     if (paramString.substring(paramString.lastIndexOf("/"), paramString.length()).indexOf(".") > -1) {
/* 777 */       bool = this.excludeFileArr.contains(paramString);
/*     */     }
/* 779 */     return bool;
/*     */   }
/*     */   public String appendEcologyVersionInfo(String paramString1, String paramString2, String paramString3) {
/* 782 */     String str1 = "$$EcologyVersionInfo$$" + paramString2 + "+" + paramString3 + "$$" + (new Date()).getTime() + "$$.txt";
/* 783 */     String str2 = paramString1 + File.separatorChar + "data" + File.separatorChar + "EcologyVersionInfoTemp";
/* 784 */     File file1 = new File(str2);
/* 785 */     if (!file1.exists()) {
/* 786 */       file1.mkdirs();
/*     */     }
/* 788 */     File file2 = new File(str2 + File.separatorChar + str1);
/* 789 */     if (file2.exists()) {
/* 790 */       file2.delete();
/*     */     }
/*     */     try {
/* 793 */       file2.createNewFile();
/* 794 */     } catch (IOException iOException) {
/* 795 */       iOException.printStackTrace();
/*     */     } 
/*     */     
/* 798 */     String str3 = getMD5(file2);
/* 799 */     String str4 = SecurityHelper.encrypt("weaververify", str1 + "==" + str3 + "==" + TimeUtil.getDateString(new Date()));
/* 800 */     file2.delete();
/* 801 */     return str4;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Boolean rightFileType(String paramString1, String paramString2) {
/* 811 */     String[] arrayOfString = this.scanfiletypesE9.split("\\|");
/* 812 */     for (String str : arrayOfString) {
/* 813 */       if (paramString1.endsWith(str.trim())) {
/* 814 */         return Boolean.valueOf(true);
/*     */       }
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 820 */     return Boolean.valueOf(false);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void getForceFileMap(String paramString1, String paramString2, User paramUser) {
/* 828 */     for (String str1 : this.forceFileArr) {
/* 829 */       String str2 = str1;
/* 830 */       if (str2.substring(str2.lastIndexOf("/") + 1).contains("*.")) {
/* 831 */         this.forcefiletype = str2.substring(str2.indexOf("*.") + 1);
/* 832 */         String str = str2.substring(0, str2.indexOf("*."));
/* 833 */         File file1 = new File(paramString1 + str);
/* 834 */         forceFileExcludeHandle(file1, paramString1, paramString2, paramUser); continue;
/*     */       } 
/* 836 */       File file = new File(paramString1 + str2);
/* 837 */       if (file.exists() && file.isFile()) {
/* 838 */         if ("@#@".equals(paramString2)) {
/* 839 */           String str = getMD5(file);
/* 840 */           this.md5map.put(str2, str); continue;
/*     */         } 
/* 842 */         if (!this.md5map2.containsKey(str2)) {
/* 843 */           String str3 = getMD5(file);
/* 844 */           this.md5map2.put(str2, str3);
/* 845 */           Date date = new Date(file.lastModified());
/* 846 */           String str4 = date.getTime() + "";
/* 847 */           if (!this.iscontentdate) {
/* 848 */             writeFile(SecurityHelper.encrypt("weaververify", str2 + "==" + str3), paramString2); continue;
/*     */           } 
/* 850 */           writeFile(SecurityHelper.encrypt("weaververify", str2 + "==" + str3 + "==" + str4), paramString2);
/*     */         } 
/*     */       } 
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 858 */     for (String str1 : this.forceDirArr) {
/* 859 */       String str2 = str1;
/* 860 */       this.forcefiletype = "";
/* 861 */       File file = new File(paramString1 + str2);
/* 862 */       forceFileExcludeHandle(file, paramString1, paramString2, paramUser);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void forceFileExcludeHandle(File paramFile, String paramString1, String paramString2, User paramUser) {
/* 872 */     if (paramFile.exists()) {
/* 873 */       File[] arrayOfFile = paramFile.listFiles();
/* 874 */       if (arrayOfFile != null && arrayOfFile.length > 0) {
/* 875 */         for (byte b = 0; b < arrayOfFile.length; b++) {
/* 876 */           String str1 = arrayOfFile[b].getPath();
/* 877 */           String str2 = str1.substring(paramString1.length());
/* 878 */           if (!str2.contains("" + File.separatorChar) || str2.indexOf("" + File.separatorChar) > 0) {
/* 879 */             str2 = "" + File.separatorChar + str2;
/*     */           }
/*     */           
/* 882 */           str2 = str2.replaceAll("\\\\", "/");
/*     */           
/* 884 */           if (arrayOfFile[b].isFile()) {
/*     */             
/* 886 */             if (getForceFileExcludeResult(str2, arrayOfFile[b], paramUser))
/* 887 */               continue;  if ("@#@".equals(paramString2)) {
/*     */               
/* 889 */               String str = getMD5(arrayOfFile[b]);
/* 890 */               this.md5map.put(str2, str);
/*     */             }
/* 892 */             else if (!this.md5map2.containsKey(str2)) {
/* 893 */               String str3 = getMD5(arrayOfFile[b]);
/* 894 */               this.md5map2.put(str2, str3);
/* 895 */               Date date = new Date(arrayOfFile[b].lastModified());
/* 896 */               String str4 = date.getTime() + "";
/* 897 */               if (!this.iscontentdate) {
/* 898 */                 writeFile(SecurityHelper.encrypt("weaververify", str2 + "==" + str3), paramString2);
/*     */               } else {
/* 900 */                 writeFile(SecurityHelper.encrypt("weaververify", str2 + "==" + str3 + "==" + str4), paramString2);
/*     */               } 
/*     */             } 
/*     */           } 
/*     */           
/* 905 */           if (arrayOfFile[b].isDirectory()) {
/* 906 */             String str = str2 + "/";
/*     */             
/* 908 */             boolean bool = !getForceFileExcludeResult(str, arrayOfFile[b], paramUser) ? true : false;
/* 909 */             if (bool) {
/* 910 */               forceFileExcludeHandle(arrayOfFile[b], paramString1, paramString2, paramUser);
/*     */             }
/*     */           } 
/*     */           continue;
/*     */         } 
/*     */       }
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean getForceFileExcludeResult(String paramString, File paramFile, User paramUser) {
/* 924 */     boolean bool = false;
/*     */ 
/*     */     
/* 927 */     if (paramFile.isFile()) {
/* 928 */       String str = paramString;
/* 929 */       if (paramString.substring(paramString.lastIndexOf("/")).indexOf(".") > 0) {
/* 930 */         str = paramString.substring(0, paramString.lastIndexOf(".")).toLowerCase();
/*     */       }
/* 932 */       if (str.endsWith("bak") || str.contains("副本") || str.contains("复制") || str
/* 933 */         .contains("复件") || str.contains("备份") || str.indexOf(")") > 0 || str.indexOf("(") > 0) {
/* 934 */         bool = true;
/*     */       
/*     */       }
/*     */     }
/* 938 */     else if (paramString.indexOf("_ubak") > 0 || paramString.contains("副本") || paramString.toLowerCase().endsWith("bak/") || paramString.contains("复制") || paramString
/* 939 */       .contains("复件") || paramString.contains("备份")) {
/* 940 */       bool = true;
/*     */     } 
/*     */     
/* 943 */     if (bool) return bool;
/*     */ 
/*     */     
/* 946 */     if (paramFile.isFile()) {
/* 947 */       if (!"".equals(this.forcefiletype) && !paramString.endsWith(this.forcefiletype.trim())) return true;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 963 */       bool = this.excludeFileArr.contains(paramString);
/*     */     } 
/*     */     
/* 966 */     return bool;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/wscheck/CheckScanAllFile.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */