/*     */ package wscheck;
/*     */ 
/*     */ import javax.servlet.ServletOutputStream;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import javax.servlet.http.HttpServletResponse;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ 
/*     */ public class CheckPage
/*     */ {
/*     */   public static void check(HttpServletRequest paramHttpServletRequest, HttpServletResponse paramHttpServletResponse) {
/*     */     try {
/*  13 */       String str = paramHttpServletRequest.getRequestURI();
/*  14 */       User user = (User)paramHttpServletRequest.getSession(true).getAttribute("weaver_user@bean");
/*  15 */       if (str.indexOf("Homepage.jsp") > 0) {
/*  16 */         paramHttpServletResponse.setContentType("text/html;charset=UTF-8");
/*  17 */         ServletOutputStream servletOutputStream = paramHttpServletResponse.getOutputStream();
/*  18 */         StringBuffer stringBuffer = new StringBuffer();
/*  19 */         String str1 = "";
/*  20 */         String str2 = "";
/*  21 */         if (!Util.null2String(PageErrorMap.get("-1")).equals("")) {
/*  22 */           str1 = Util.null2String(PageErrorMap.get("-1"));
/*     */         }
/*  24 */         if (!Util.null2String(PageErrorMap.get("-2")).equals("")) {
/*  25 */           str1 = Util.null2String(PageErrorMap.get("-2"));
/*     */         }
/*  27 */         if (!Util.null2String(PageErrorMap.get("-3")).equals("")) {
/*  28 */           str1 = Util.null2String(PageErrorMap.get("-3"));
/*     */         }
/*  30 */         if (PageErrorMap.size() > 0 && "".equals(str1))
/*     */         {
/*  32 */           if (CheckScanFile.hasOldModifyMoreThan30DaysFile()) {
/*  33 */             str2 = "&nbsp;&nbsp;当前系统存在文件未通过校验,请与管理员联系或者与泛微人员联系！&nbsp;&nbsp;";
/*  34 */             if (null != user && user.getUID() == 1) {
/*  35 */               str2 = "&nbsp;&nbsp;当前系统存在文件未通过校验,请与管理员联系或者与泛微人员联系！&nbsp;<a href='/keygenerator/getNoCheckFiles.jsp' target='_blank'>点击获取文件</a>&nbsp;&nbsp;";
/*     */             }
/*     */           }
/*  38 */           else if (null != user && PageErrorMap.getAdminlist().indexOf("" + user.getUID()) > -1) {
/*  39 */             str2 = "&nbsp;&nbsp;当前系统存在文件未通过校验,请与管理员联系或者与泛微人员联系！&nbsp;&nbsp;";
/*  40 */             if (null != user && user.getUID() == 1) {
/*  41 */               str2 = "&nbsp;&nbsp;当前系统存在文件未通过校验,请与管理员联系或者与泛微人员联系！&nbsp;<a href='/keygenerator/getNoCheckFiles.jsp' target='_blank'>点击获取文件</a>&nbsp;&nbsp;";
/*     */             }
/*     */           } 
/*     */         }
/*     */         
/*  46 */         if (!"".equals(str1)) {
/*  47 */           str2 = str1;
/*     */         }
/*  49 */         stringBuffer.append("<SCRIPT language=\"javascript\">\n");
/*  50 */         stringBuffer.append("var backMessage = \"\";\n");
/*  51 */         stringBuffer.append("var backMessagex = \"\";\n");
/*  52 */         stringBuffer.append("var backMessagey = \"\";\n");
/*  53 */         stringBuffer.append("var backMessagedelay = 0;\n");
/*  54 */         stringBuffer.append("function feedBackMessage(){\n");
/*  55 */         stringBuffer.append("var message = backMessage;\n");
/*  56 */         stringBuffer.append("var x = backMessagex;\n");
/*  57 */         stringBuffer.append("var y = backMessagey;\n");
/*  58 */         stringBuffer.append("var delay = backMessagedelay;\n");
/*  59 */         stringBuffer.append(" if(!message) return;\n");
/*  60 */         stringBuffer.append(" x=/\\d{1,2}%|100%|left|right/.test(x)?x:(parseInt(x)||0)+'px';\n");
/*  61 */         stringBuffer.append(" y=/\\d{1,2}%|100%|top|bottom/.test(y)?y:(parseInt(y)||0)+'px';\n");
/*  62 */         stringBuffer.append(" delay=parseInt(delay)||-1;\n");
/*  63 */         stringBuffer.append(" var fdDiv=document.getElementById('show_feedBack_message');\n");
/*  64 */         stringBuffer.append(" if(!fdDiv){\n");
/*  65 */         stringBuffer.append("  var showMessage=document.createElement(\"div\");\n");
/*  66 */         stringBuffer.append("  showMessage.setAttribute(\"id\",\"show_feedBack_message\");\n");
/*  67 */         stringBuffer.append("  showMessage.setAttribute(\"style\",\"z-index:10000;filter:alpha(opacity=100);position:absolute;white-space:nowrapborder:1px solid #f00;background:#fc0;line-height:18px;padding:3px;font-size:12px;\");\n");
/*  68 */         stringBuffer.append("  showMessage.style.cssText = \"z-index:10000;filter:alpha(opacity=100);position:absolute;white-space:nowrapborder:1px solid #f00;background:#fc0;line-height:18px;padding:3px;font-size:12px;\";\n");
/*  69 */         stringBuffer.append("  document.body.appendChild(showMessage);\n");
/*  70 */         stringBuffer.append("  fdDiv=document.getElementById('show_feedBack_message');\n");
/*  71 */         stringBuffer.append(" }\n");
/*  72 */         stringBuffer.append(" if(feedBackMessage.timer){clearInterval(feedBackMessage.timer)}\n");
/*  73 */         stringBuffer.append(" fdDiv.innerHTML=message;\n");
/*  74 */         stringBuffer.append(" fdDiv.style.display=\"\";\n");
/*  75 */         stringBuffer.append(" var docWidth=document.documentElement.scrollWidth>document.documentElement.clientWidth?document.documentElement.scrollWidth:document.documentElement.clientWidth;\n");
/*  76 */         stringBuffer.append(" var docHeight=document.documentElement.scrollHeight>document.documentElement.clientHeight?document.documentElement.scrollHeight:document.documentElement.clientHeight;\n");
/*  77 */         stringBuffer.append(" if(/left|right/.test(x)){\n");
/*  78 */         stringBuffer.append("  x=(x==\"left\")?\"0px\":(docWidth-fdDiv.offsetWidth)+\"px\";\n");
/*  79 */         stringBuffer.append(" }\n");
/*  80 */         stringBuffer.append(" if(/top|bottom/.test(y)){");
/*  81 */         stringBuffer.append("  y=(y==\"top\")?\"0px\":(docHeight-fdDiv.offsetHeight)+\"px\";\n");
/*  82 */         stringBuffer.append(" }\n");
/*  83 */         stringBuffer.append(" fdDiv.style.left=x;\n");
/*  84 */         stringBuffer.append(" fdDiv.style.top=y;\n");
/*  85 */         stringBuffer.append(" try{\n");
/*  86 */         stringBuffer.append(" fdDiv.filters.Alpha.Opacity=100;\n");
/*  87 */         stringBuffer.append(" var step=parseInt(delay/100);\n");
/*  88 */         stringBuffer.append(" var alpha=fdDiv.filters.Alpha.Opacity;\n");
/*  89 */         stringBuffer.append(" if(delay!=-1){\n");
/*  90 */         stringBuffer.append("  feedBackMessage.timer=setInterval(function(){\n");
/*  91 */         stringBuffer.append("   if(fdDiv.filters.Alpha.Opacity>0){\n");
/*  92 */         stringBuffer.append("    fdDiv.filters.Alpha.Opacity--;\n");
/*  93 */         stringBuffer.append("   }else{\n");
/*  94 */         stringBuffer.append("    clearInterval(feedBackMessage.timer);\n");
/*  95 */         stringBuffer.append("    fdDiv.style.display=\"none\";\n");
/*  96 */         stringBuffer.append("   }\n");
/*  97 */         stringBuffer.append("  },step);\n");
/*  98 */         stringBuffer.append(" }\n");
/*  99 */         stringBuffer.append(" }catch(e){\n");
/* 100 */         stringBuffer.append(" setTimeout(function(){fdDiv.setAttribute(\"style\",\"display:none;\");},3000); \n");
/* 101 */         stringBuffer.append(" }\n");
/* 102 */         stringBuffer.append("\t}\n");
/*     */         
/* 104 */         stringBuffer.append("backMessage = \"" + str2 + "\";\n");
/* 105 */         stringBuffer.append("backMessagex = 'left';\n");
/* 106 */         stringBuffer.append("backMessagey = 'top';\n");
/* 107 */         stringBuffer.append("backMessagedelay = 3000;\n");
/* 108 */         stringBuffer.append("if (window.addEventListener){\n");
/* 109 */         stringBuffer.append("    window.addEventListener(\"load\", feedBackMessage, false);\n");
/* 110 */         stringBuffer.append("}else if (window.attachEvent){\n");
/* 111 */         stringBuffer.append("    window.attachEvent(\"onload\", feedBackMessage);\n");
/* 112 */         stringBuffer.append("}else{\n");
/* 113 */         stringBuffer.append("    window.onload=feedBackMessage;\n");
/* 114 */         stringBuffer.append("}\n");
/* 115 */         stringBuffer.append("</script>");
/*     */         
/* 117 */         servletOutputStream.print(stringBuffer.toString());
/* 118 */         servletOutputStream.flush();
/*     */       } 
/* 120 */     } catch (Exception exception) {}
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/wscheck/CheckPage.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */