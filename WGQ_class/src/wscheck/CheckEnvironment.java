/*     */ package wscheck;
/*     */ 
/*     */ import java.io.File;
/*     */ import java.io.FileOutputStream;
/*     */ import java.io.IOException;
/*     */ import java.text.ParseException;
/*     */ import java.text.SimpleDateFormat;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Date;
/*     */ import java.util.HashMap;
/*     */ import java.util.Hashtable;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.TreeMap;
/*     */ import java.util.concurrent.ExecutionException;
/*     */ import java.util.concurrent.ExecutorService;
/*     */ import java.util.concurrent.Executors;
/*     */ import java.util.concurrent.Future;
/*     */ import java.util.concurrent.TimeUnit;
/*     */ import java.util.regex.Matcher;
/*     */ import java.util.regex.Pattern;
/*     */ import jcifs.smb.SmbAuthException;
/*     */ import jcifs.smb.SmbFile;
/*     */ import org.apache.poi.hssf.usermodel.HSSFCell;
/*     */ import org.apache.poi.hssf.usermodel.HSSFCellStyle;
/*     */ import org.apache.poi.hssf.usermodel.HSSFDataFormat;
/*     */ import org.apache.poi.hssf.usermodel.HSSFFont;
/*     */ import org.apache.poi.hssf.usermodel.HSSFRow;
/*     */ import org.apache.poi.hssf.usermodel.HSSFSheet;
/*     */ import org.apache.poi.hssf.usermodel.HSSFWorkbook;
/*     */ import org.apache.poi.ss.usermodel.CellType;
/*     */ import org.apache.poi.ss.usermodel.FillPatternType;
/*     */ import org.apache.poi.ss.usermodel.HorizontalAlignment;
/*     */ import org.apache.poi.ss.usermodel.VerticalAlignment;
/*     */ import weaver.file.ExcelFile;
/*     */ import weaver.file.ExcelRow;
/*     */ import weaver.file.ExcelSheet;
/*     */ import weaver.file.ExcelStyle;
/*     */ import weaver.file.Prop;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.Util;
/*     */ import weaver.templetecheck.PropertiesUtil;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CheckEnvironment
/*     */   extends Thread
/*     */ {
/*  55 */   Map md5map = new HashMap<>();
/*     */   public static final String jsptype = ".jsp";
/*     */   public static final String javatype = ".java";
/*     */   public static final String classtype = ".class";
/*     */   public static final String jstype = ".js";
/*     */   public static final String csstype = ".css";
/*     */   public static final String jartype = ".jar";
/*  62 */   SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
/*  63 */   SimpleDateFormat ymd = new SimpleDateFormat("yyyyMMdd");
/*  64 */   KeyGeneratorCompare keyGeneratorCompare = new KeyGeneratorCompare();
/*  65 */   CheckScanAllFile checkScanAllFile = new CheckScanAllFile();
/*     */ 
/*     */   
/*  68 */   private static Hashtable styleht = null;
/*  69 */   PropertiesUtil prop = new PropertiesUtil();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public Map<String, String> getCheckResult(String paramString1, String paramString2) {
/*  79 */     deleteNoUseKeyFile();
/*     */     
/*  81 */     TreeMap<Object, Object> treeMap = new TreeMap<>();
/*  82 */     String str1 = "";
/*  83 */     String str2 = "";
/*  84 */     byte b = 2;
/*  85 */     ExecutorService executorService = null;
/*     */     
/*     */     try {
/*  88 */       executorService = Executors.newFixedThreadPool(b);
/*     */       
/*  90 */       ArrayList arrayList = new ArrayList();
/*  91 */       Date date = new Date();
/*  92 */       String str3 = "ecology_source_" + this.sdf.format(date);
/*  93 */       String str4 = "ecology_target_" + this.sdf.format(date);
/*  94 */       ProductKeyThread productKeyThread1 = new ProductKeyThread(paramString1, paramString1, str3);
/*  95 */       ProductKeyThread productKeyThread2 = new ProductKeyThread(paramString2, paramString2, str4);
/*  96 */       Future<?> future1 = executorService.submit(productKeyThread1);
/*  97 */       Future<?> future2 = executorService.submit(productKeyThread2);
/*  98 */       executorService.shutdown();
/*     */ 
/*     */       
/* 101 */       executorService.awaitTermination(Long.MAX_VALUE, TimeUnit.MINUTES);
/* 102 */       str1 = ((String)future1.get()).toString();
/* 103 */       str2 = ((String)future2.get()).toString();
/* 104 */     } catch (InterruptedException interruptedException) {
/* 105 */       interruptedException.printStackTrace();
/* 106 */     } catch (ExecutionException executionException) {
/* 107 */       executionException.printStackTrace();
/*     */     } finally {
/* 109 */       if (executorService != null) {
/* 110 */         executorService.shutdown();
/*     */       }
/*     */     } 
/*     */     
/* 114 */     Map map1 = KeyGeneratorCompare.getMD5Map(new File(str1));
/* 115 */     Map map2 = KeyGeneratorCompare.getMD5Map(new File(str2));
/* 116 */     return this.keyGeneratorCompare.getCompareResultNew(map1, map2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int checkPath(String paramString1, String paramString2, String paramString3) {
/*     */     try {
/* 129 */       String str1 = (getIPAddress(paramString1).size() > 0) ? ((String)getIPAddress(paramString1).get(0)).trim() : "";
/* 130 */       String str2 = "";
/* 131 */       if ((paramString1.startsWith("\\\\192.168.") || paramString1.startsWith("//192.168.")) && !(new File(paramString1)).isDirectory()) {
/* 132 */         if (str1.equals("")) return 0; 
/* 133 */         String str = "";
/* 134 */         if (!paramString2.equals("") && !paramString3.equals("")) {
/* 135 */           str = paramString2 + ":" + paramString3;
/*     */         } else {
/* 137 */           str = Util.null2String(Prop.getPropValue("LANAuth", str1));
/*     */         } 
/* 139 */         if (str == null || str.equals("")) {
/* 140 */           str = Util.null2String(Prop.getPropValue("LANAuth", "default"));
/* 141 */           str2 = ("smb://" + str + str1.substring(str1.lastIndexOf("."), str1.length()) + "@" + paramString1.substring(2, paramString1.length())).replace("\\", "/");
/*     */         } else {
/* 143 */           str2 = ("smb://" + str + "@" + paramString1.substring(2, paramString1.length())).replace("\\", "/");
/*     */         } 
/* 145 */         if (str == null || str.equals("")) {
/* 146 */           return 0;
/*     */         }
/*     */         
/* 149 */         SmbFile smbFile = new SmbFile(str2);
/* 150 */         if (smbFile.isDirectory()) {
/*     */           try {
/* 152 */             String str3 = "net use " + paramString1.substring(0, paramString1.length() - 1) + " /user:" + str.substring(0, str.indexOf(":")) + " " + str.substring(str.indexOf(":") + 1, str.length());
/* 153 */             Runtime.getRuntime().exec(str3).waitFor();
/* 154 */           } catch (IOException iOException) {
/* 155 */             iOException.printStackTrace();
/* 156 */           } catch (InterruptedException interruptedException) {
/* 157 */             interruptedException.printStackTrace();
/*     */           } 
/* 159 */           File file1 = new File(paramString1 + "WEB-INF" + File.separator);
/* 160 */           if (file1.exists())
/*     */           {
/*     */ 
/*     */ 
/*     */             
/* 165 */             return 1;
/*     */           }
/* 167 */           return 0;
/*     */         } 
/*     */         
/* 170 */         return 0;
/*     */       } 
/*     */       
/* 173 */       File file = new File(paramString1);
/* 174 */       if (!file.isDirectory()) {
/* 175 */         return 0;
/*     */       }
/* 177 */       return 1;
/*     */     }
/* 179 */     catch (SmbAuthException smbAuthException) {
/* 180 */       return 2;
/* 181 */     } catch (Exception exception) {
/* 182 */       exception.printStackTrace();
/*     */       
/* 184 */       return 0;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static List<String> getIPAddress(String paramString) {
/* 193 */     ArrayList<String> arrayList = new ArrayList();
/* 194 */     String str = "\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}";
/* 195 */     Pattern pattern = Pattern.compile(str);
/* 196 */     Matcher matcher = pattern.matcher(paramString);
/* 197 */     boolean bool = matcher.find();
/* 198 */     while (bool) {
/* 199 */       arrayList.add(matcher.group());
/* 200 */       bool = matcher.find();
/*     */     } 
/* 202 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void getFileMap(File paramFile, String paramString) {
/* 212 */     File[] arrayOfFile = paramFile.listFiles();
/*     */     
/* 214 */     boolean bool = false;
/* 215 */     for (byte b = 0; arrayOfFile != null && b < arrayOfFile.length; b++) {
/* 216 */       String str1 = arrayOfFile[b].getPath();
/* 217 */       String str2 = str1.substring(paramString.length());
/*     */       
/* 219 */       if (str2.indexOf("" + File.separatorChar) == -1 || str2.indexOf("" + File.separatorChar) > 0) {
/* 220 */         str2 = "" + File.separatorChar + str2;
/*     */       }
/*     */       
/* 223 */       str2 = str2.replaceAll("\\\\", "/");
/* 224 */       if (arrayOfFile[b].isFile()) {
/* 225 */         bool = false;
/* 226 */         if (str1.endsWith(".jsp") || str1.endsWith(".class") || str1.endsWith(".js") || str1
/* 227 */           .endsWith(".java") || str1.endsWith(".css") || str1.endsWith(".jar")) {
/*     */           
/* 229 */           bool = this.checkScanAllFile.checkExtendFile(str2);
/* 230 */           if (bool) {
/*     */             continue;
/*     */           }
/* 233 */           String str3 = str2.substring(0, str2.lastIndexOf(".")).toLowerCase();
/* 234 */           if (str3.endsWith("bak") || str3.contains("副本") || str3.contains("复制") || str3.contains("复件") || str3.contains("备份")) {
/*     */             continue;
/*     */           }
/* 237 */           String str4 = getMD5(arrayOfFile[b]);
/* 238 */           this.md5map.put(str2, str4);
/*     */         } 
/*     */       } 
/* 241 */       if (arrayOfFile[b].isDirectory() && (
/* 242 */         str2.indexOf("/WEB-INF") != 0 || str2.replace("/WEB-INF", "").equals("") || str2.replace("/WEB-INF", "").equals("/") || str2.indexOf("/lib") >= 0) && str2.indexOf("/wscheck") <= 0 && str2.indexOf("/keygenerator") != 0 && str2.indexOf("/updatetemp") != 0 && str2
/* 243 */         .indexOf("/src") != 0 && str2
/* 244 */         .indexOf("/data") != 0 && str2
/* 245 */         .indexOf("/sqlupgrade") != 0 && str2
/* 246 */         .indexOf("/jsp/") != 0 && str2.indexOf("_ubak") <= 0 && !str2.toLowerCase().endsWith("bak") && !str2.contains("副本") && !str2.contains("复件") && !str2.contains("备份") && !str2.contains("复制"))
/*     */       {
/*     */ 
/*     */         
/* 250 */         getFileMap(arrayOfFile[b], paramString);
/*     */       }
/*     */       continue;
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getMD5(File paramFile) {
/* 263 */     String str = "";
/*     */     try {
/* 265 */       str = MD5Coder.fileMD5(paramFile);
/* 266 */     } catch (Exception exception) {
/* 267 */       exception.printStackTrace();
/* 268 */       str = "";
/*     */     } 
/*     */     
/* 271 */     return str;
/*     */   }
/*     */   
/*     */   public Map getMd5map() {
/* 275 */     return this.md5map;
/*     */   }
/*     */   public void setMd5map(Map paramMap) {
/* 278 */     this.md5map = paramMap;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void deleteNoUseKeyFile() {
/* 285 */     String str = GCONST.getRootPath() + "data" + File.separatorChar + "tempkey" + File.separatorChar;
/* 286 */     File file = new File(str);
/* 287 */     if (file.exists() && file.isDirectory()) {
/*     */       
/*     */       try {
/* 290 */         Date date = new Date(this.ymd.parse(this.ymd.format(new Date())).getTime() - 7200000L);
/*     */ 
/*     */         
/* 293 */         String str1 = this.sdf.format(date);
/* 294 */         File[] arrayOfFile = file.listFiles();
/* 295 */         for (File file1 : arrayOfFile) {
/* 296 */           if (file1.isFile() && file1.getName().endsWith(".key") && (file1.getName().contains("ecology_source_") || file1.getName().contains("ecology_target_")) && 
/* 297 */             file1.getName().substring(file1.getName().lastIndexOf("_") + 1, file1.getName().indexOf(".key")).compareTo(str1) < 0) {
/* 298 */             file1.delete();
/*     */           }
/*     */         }
/*     */       
/* 302 */       } catch (ParseException parseException) {
/* 303 */         parseException.printStackTrace();
/*     */       } 
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String toFile(ExcelFile paramExcelFile, String paramString) throws Exception {
/* 318 */     ExcelSheet excelSheet = null;
/* 319 */     ExcelRow excelRow = null;
/*     */     
/* 321 */     HSSFWorkbook hSSFWorkbook = null;
/* 322 */     HSSFSheet hSSFSheet = null;
/* 323 */     HSSFRow hSSFRow = null;
/* 324 */     HSSFCell hSSFCell = null;
/* 325 */     HSSFCellStyle hSSFCellStyle = null;
/*     */     
/* 327 */     if (paramExcelFile == null) {
/* 328 */       return "";
/*     */     }
/* 330 */     hSSFWorkbook = new HSSFWorkbook();
/*     */     
/* 332 */     initStyle(paramExcelFile, hSSFWorkbook);
/*     */     
/* 334 */     byte b = 0;
/*     */     
/* 336 */     while (paramExcelFile.next()) {
/* 337 */       String str = paramExcelFile.getSheetname();
/* 338 */       excelSheet = paramExcelFile.getSheet();
/*     */       
/* 340 */       if (excelSheet == null)
/*     */         continue; 
/* 342 */       hSSFSheet = hSSFWorkbook.createSheet();
/* 343 */       hSSFWorkbook.setSheetName(b, Util.fromScreen2(str, 7));
/* 344 */       b++;
/*     */       byte b1;
/* 346 */       for (b1 = 0; b1 < excelSheet.size(); b1++) {
/*     */         
/* 348 */         excelRow = excelSheet.getExcelRow(b1);
/*     */         
/* 350 */         if (excelRow != null) {
/*     */           
/* 352 */           short s = excelRow.getHight();
/*     */           
/* 354 */           hSSFRow = hSSFSheet.createRow((short)b1);
/*     */           
/* 356 */           if (s != 255) {
/* 357 */             hSSFRow.setHeightInPoints(s);
/*     */           }
/* 359 */           byte b2 = 0;
/* 360 */           boolean bool1 = false;
/* 361 */           boolean bool2 = false;
/*     */           
/* 363 */           if (excelRow.stylesize() == excelRow.size())
/* 364 */             bool1 = true; 
/* 365 */           if (excelRow.spansize() == excelRow.size()) {
/* 366 */             bool2 = true;
/*     */           }
/* 368 */           for (byte b3 = 0; b3 < excelRow.size(); b3++) {
/*     */             
/* 370 */             hSSFCell = hSSFRow.createCell((short)b2);
/*     */             
/* 372 */             String str3 = Util.null2String(excelRow.getValue(b3));
/* 373 */             String str4 = str3.substring(0, 2);
/* 374 */             String str5 = str3.substring(2);
/*     */ 
/*     */ 
/*     */ 
/*     */             
/* 379 */             if (str4.indexOf("s_") == 0) {
/*     */               
/* 381 */               HSSFCellStyle hSSFCellStyle1 = hSSFWorkbook.createCellStyle();
/* 382 */               HSSFDataFormat hSSFDataFormat = hSSFWorkbook.createDataFormat();
/* 383 */               hSSFCellStyle1.setDataFormat(hSSFDataFormat.getFormat("@"));
/* 384 */               hSSFCell.setCellStyle(hSSFCellStyle1);
/*     */               
/* 386 */               hSSFCell.setCellType(CellType.STRING);
/* 387 */               hSSFCell.setCellValue(Util.fromScreen4(str5, 7));
/* 388 */             } else if (str4.indexOf("i_") == 0) {
/* 389 */               int i = Util.getIntValue(str5);
/* 390 */               if (i != 0)
/* 391 */                 hSSFCell.setCellValue(i); 
/* 392 */             } else if (str4.indexOf("f_") == 0) {
/* 393 */               float f = Util.getFloatValue(str5);
/* 394 */               if (f != 0.0D)
/* 395 */                 hSSFCell.setCellValue(f); 
/* 396 */             } else if (str4.indexOf("d_") == 0) {
/* 397 */               double d = Util.getDoubleValue(str5);
/* 398 */               if (d != 0.0D)
/* 399 */                 hSSFCell.setCellValue(d); 
/* 400 */             } else if (str4.indexOf("o_") == 0) {
/* 401 */               hSSFCell.setCellFormula(str5);
/* 402 */             } else if (str4.indexOf("n_") == 0) {
/*     */ 
/*     */ 
/*     */               
/* 406 */               hSSFCell.setCellValue(Util.fromScreen4(str5, 7));
/*     */             } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */             
/* 415 */             if (bool1) {
/* 416 */               String str6 = Util.null2String(excelRow.getStyle(b3));
/* 417 */               if (!str6.equals("")) {
/* 418 */                 hSSFCellStyle = getStyle(str6);
/* 419 */                 if (hSSFCellStyle != null) {
/* 420 */                   hSSFCell.setCellStyle(hSSFCellStyle);
/*     */                 }
/*     */               } 
/*     */             } 
/* 424 */             if (bool2) {
/* 425 */               int i = excelRow.getSpan(b3);
/* 426 */               if (i > 1) {
/* 427 */                 for (byte b4 = 0; b4 < i - 1; b4++) {
/* 428 */                   b2++;
/* 429 */                   hSSFCell = hSSFRow.createCell((short)b2);
/* 430 */                   hSSFCell.setCellValue("");
/* 431 */                   if (bool1 && hSSFCellStyle != null) {
/* 432 */                     hSSFCell.setCellStyle(hSSFCellStyle);
/*     */                   }
/*     */                 } 
/*     */               }
/*     */             } 
/*     */             
/* 438 */             b2++;
/*     */           } 
/*     */         } 
/*     */       } 
/* 442 */       for (b1 = 0; b1 < excelSheet.columnsize(); b1++) {
/* 443 */         hSSFSheet.setColumnWidth((short)b1, excelSheet.getColumnwidth(b1));
/*     */       }
/*     */     } 
/*     */     
/* 447 */     String str1 = paramExcelFile.getFilename();
/*     */     
/* 449 */     String str2 = GCONST.getRootPath() + paramString + ".xls";
/* 450 */     File file = new File(str2);
/* 451 */     if (file.exists()) {
/* 452 */       file.delete();
/*     */     }
/* 454 */     FileOutputStream fileOutputStream = new FileOutputStream(file);
/* 455 */     hSSFWorkbook.write(fileOutputStream);
/* 456 */     fileOutputStream.close();
/* 457 */     return str2;
/*     */   }
/*     */   
/*     */   private static void initStyle(ExcelFile paramExcelFile, HSSFWorkbook paramHSSFWorkbook) {
/* 461 */     styleht = new Hashtable<>();
/* 462 */     HSSFCellStyle hSSFCellStyle = null;
/* 463 */     HSSFFont hSSFFont = null;
/*     */     
/* 465 */     while (paramExcelFile.nextStyle()) {
/* 466 */       String str = paramExcelFile.getStyleName();
/* 467 */       ExcelStyle excelStyle = paramExcelFile.getStyleValue();
/*     */       
/* 469 */       if (excelStyle != null) {
/* 470 */         hSSFCellStyle = paramHSSFWorkbook.createCellStyle();
/* 471 */         hSSFFont = paramHSSFWorkbook.createFont();
/*     */         
/* 473 */         if (excelStyle.getGroundcolor() != 9) {
/* 474 */           hSSFCellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
/* 475 */           hSSFCellStyle.setFillForegroundColor(excelStyle.getGroundcolor());
/*     */         } 
/* 477 */         hSSFCellStyle.setRotation(excelStyle.getScale());
/* 478 */         if (excelStyle.getAlign() != 10)
/* 479 */           hSSFCellStyle.setAlignment(HorizontalAlignment.forInt(excelStyle.getAlign())); 
/* 480 */         if (excelStyle.getDataformart() != 0)
/* 481 */           hSSFCellStyle.setDataFormat(excelStyle.getDataformart()); 
/* 482 */         hSSFCellStyle.setVerticalAlignment(VerticalAlignment.forInt(excelStyle.getValign()));
/*     */         
/* 484 */         hSSFFont.setColor(excelStyle.getFontcolor());
/* 485 */         hSSFFont.setBold((excelStyle.getFontbold() == 700));
/* 486 */         hSSFFont.setFontHeightInPoints(excelStyle.getFontheight());
/*     */         
/* 488 */         hSSFCellStyle.setFont(hSSFFont);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 497 */         styleht.put(str, hSSFCellStyle);
/*     */       } 
/*     */     } 
/*     */   }
/*     */   
/*     */   private static HSSFCellStyle getStyle(String paramString) {
/* 503 */     return (HSSFCellStyle)styleht.get(paramString);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/wscheck/CheckEnvironment.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */