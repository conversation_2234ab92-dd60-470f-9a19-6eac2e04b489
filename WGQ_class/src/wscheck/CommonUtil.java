/*     */ package wscheck;
/*     */ 
/*     */ import java.io.BufferedReader;
/*     */ import java.io.ByteArrayOutputStream;
/*     */ import java.io.File;
/*     */ import java.io.FileInputStream;
/*     */ import java.io.FileNotFoundException;
/*     */ import java.io.FileWriter;
/*     */ import java.io.IOException;
/*     */ import java.io.InputStream;
/*     */ import java.io.InputStreamReader;
/*     */ import java.io.PrintWriter;
/*     */ import java.util.ArrayList;
/*     */ import weaver.file.FileManage;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ public class CommonUtil
/*     */ {
/*     */   public static String getString(String paramString) {
/*  23 */     return (paramString == null) ? "" : paramString;
/*     */   }
/*     */   
/*     */   private static BufferedReader getReader(File paramFile) {
/*  27 */     BufferedReader bufferedReader = null;
/*     */     try {
/*  29 */       bufferedReader = new BufferedReader(new InputStreamReader(new FileInputStream(paramFile)));
/*  30 */     } catch (FileNotFoundException fileNotFoundException) {
/*  31 */       fileNotFoundException.printStackTrace();
/*     */     } 
/*  33 */     return bufferedReader;
/*     */   }
/*     */   
/*     */   public static ArrayList getLineList(File paramFile) {
/*  37 */     ArrayList<String> arrayList = new ArrayList();
/*  38 */     BufferedReader bufferedReader = getReader(paramFile);
/*  39 */     String str = null;
/*     */     try {
/*  41 */       while ((str = bufferedReader.readLine()) != null) {
/*  42 */         arrayList.add(str);
/*     */       }
/*  44 */       bufferedReader.close();
/*  45 */     } catch (IOException iOException) {
/*  46 */       iOException.printStackTrace();
/*     */     } 
/*  48 */     return arrayList;
/*     */   }
/*     */   public static String getLines(File paramFile) {
/*  51 */     StringBuffer stringBuffer = new StringBuffer();
/*  52 */     ArrayList<String> arrayList = new ArrayList();
/*  53 */     BufferedReader bufferedReader = getReader(paramFile);
/*  54 */     String str = null;
/*     */     try {
/*  56 */       while ((str = bufferedReader.readLine()) != null) {
/*  57 */         arrayList.add(str);
/*     */       }
/*  59 */       for (byte b = 0; b < arrayList.size(); b++) {
/*     */         
/*  61 */         stringBuffer.append(Util.null2String(arrayList.get(b)).trim());
/*  62 */         if (b < arrayList.size() - 1)
/*  63 */           stringBuffer.append("\n"); 
/*     */       } 
/*  65 */     } catch (IOException iOException) {
/*  66 */       iOException.printStackTrace();
/*     */     }
/*     */     finally {
/*     */       
/*  70 */       if (null != bufferedReader) {
/*     */         
/*     */         try {
/*     */           
/*  74 */           bufferedReader.close();
/*     */         }
/*  76 */         catch (IOException iOException) {
/*     */ 
/*     */           
/*  79 */           iOException.printStackTrace();
/*     */         } 
/*     */       }
/*     */     } 
/*  83 */     return stringBuffer.toString();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void fileAppend(String paramString1, String paramString2) {
/*     */     try {
/*  91 */       String str = paramString1.substring(0, paramString1.lastIndexOf("" + File.separatorChar));
/*  92 */       createFolders(str);
/*     */       
/*  94 */       FileWriter fileWriter = new FileWriter(paramString1, true);
/*  95 */       if (paramString2.equals("")) {
/*  96 */         fileWriter.write(paramString2);
/*     */       } else {
/*  98 */         File file = new File(paramString1);
/*  99 */         if (file.length() == 0L) {
/* 100 */           fileWriter.write(paramString2);
/*     */         } else {
/* 102 */           fileWriter.write("\n" + paramString2);
/*     */         } 
/*     */       } 
/*     */       
/* 106 */       fileWriter.close();
/* 107 */     } catch (IOException iOException) {
/* 108 */       iOException.printStackTrace();
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public static void rewritefile(String paramString1, String paramString2) {
/*     */     try {
/* 115 */       String str = paramString1.substring(0, paramString1.lastIndexOf("" + File.separatorChar));
/* 116 */       createFolders(str);
/*     */       
/* 118 */       FileWriter fileWriter = new FileWriter(paramString1, false);
/* 119 */       if (paramString2.equals("")) {
/* 120 */         fileWriter.write(paramString2);
/*     */       } else {
/* 122 */         File file = new File(paramString1);
/* 123 */         if (file.length() == 0L) {
/* 124 */           fileWriter.write(paramString2);
/*     */         } else {
/* 126 */           fileWriter.write("\n" + paramString2);
/*     */         } 
/*     */       } 
/* 129 */       fileWriter.close();
/* 130 */     } catch (IOException iOException) {
/* 131 */       iOException.printStackTrace();
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static final boolean createFolders(String paramString) {
/* 143 */     if (paramString == null || "".equals(paramString)) {
/* 144 */       return false;
/*     */     }
/*     */ 
/*     */ 
/*     */     
/*     */     try {
/* 150 */       File file = new File(paramString);
/* 151 */       if (!file.exists())
/*     */       {
/* 153 */         file.mkdirs();
/*     */       }
/* 155 */     } catch (Exception exception) {
/* 156 */       exception.printStackTrace();
/*     */     } 
/* 158 */     return true;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static final boolean createFolder(String paramString) {
/* 169 */     if (paramString == null || "".equals(paramString) || ("" + File.separatorChar).equals(paramString) || ":".equals(paramString)) {
/* 170 */       return false;
/*     */     }
/*     */     try {
/* 173 */       File file = new File(paramString);
/* 174 */       if (!file.exists()) {
/* 175 */         file.mkdir();
/*     */       }
/* 177 */       return true;
/* 178 */     } catch (Exception exception) {
/* 179 */       exception.printStackTrace();
/*     */ 
/*     */       
/* 182 */       return false;
/*     */     } 
/*     */   }
/*     */   public static void writeErrorLog(String paramString1, String paramString2, String paramString3) {
/*     */     try {
/* 187 */       String str1 = GCONST.getRootPath();
/* 188 */       String str2 = str1 + "data" + File.separatorChar + "keylog" + File.separatorChar;
/* 189 */       FileManage.createDir(str2);
/* 190 */       String str3 = str2 + TimeUtil.getCurrentDateString() + ".log";
/*     */       
/* 192 */       if (!paramString1.equals("")) {
/* 193 */         PrintWriter printWriter = new PrintWriter(new FileWriter(str3, true));
/* 194 */         printWriter.println("错误时间:");
/* 195 */         printWriter.println("  " + TimeUtil.getCurrentTimeString());
/* 196 */         printWriter.println("错误文件:");
/* 197 */         printWriter.println("  " + paramString1);
/* 198 */         printWriter.println("错误原因:");
/* 199 */         printWriter.println("  " + paramString2);
/* 200 */         printWriter.close();
/*     */       } else {
/* 202 */         str3 = "";
/*     */       }
/*     */     
/*     */     }
/* 206 */     catch (Exception exception) {
/* 207 */       exception.printStackTrace();
/*     */     } 
/*     */   }
/*     */   
/*     */   public static byte[] readByteArrayClassByJar(String paramString) {
/* 212 */     InputStream inputStream = CommonUtil.class.getResourceAsStream(paramString);
/*     */     
/* 214 */     return readByteArrayInputStream(inputStream);
/*     */   }
/*     */   public static byte[] readByteArrayInputStream(InputStream paramInputStream) {
/* 217 */     byte[] arrayOfByte1 = new byte[1024];
/* 218 */     byte[] arrayOfByte2 = null;
/* 219 */     int i = -1;
/* 220 */     ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
/*     */     try {
/* 222 */       while ((i = paramInputStream.read(arrayOfByte1)) != -1) {
/* 223 */         byteArrayOutputStream.write(arrayOfByte1, 0, i);
/*     */       }
/* 225 */       arrayOfByte2 = byteArrayOutputStream.toByteArray();
/* 226 */     } catch (IOException iOException) {
/* 227 */       throw new RuntimeException(iOException);
/*     */     } finally {
/*     */ 
/*     */       
/*     */       try {
/*     */ 
/*     */         
/* 234 */         if (null != paramInputStream)
/* 235 */           paramInputStream.close(); 
/* 236 */         if (null != byteArrayOutputStream) {
/* 237 */           byteArrayOutputStream.close();
/*     */         }
/* 239 */       } catch (IOException iOException) {
/*     */ 
/*     */         
/* 242 */         iOException.printStackTrace();
/*     */       } 
/*     */     } 
/* 245 */     return arrayOfByte2;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/wscheck/CommonUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */