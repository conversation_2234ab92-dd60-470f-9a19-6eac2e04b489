/*    */ package wscheck;
/*    */ 
/*    */ import java.lang.reflect.Method;
/*    */ import weaver.general.BaseBean;
/*    */ import weaver.general.Util;
/*    */ 
/*    */ public class InitCheck
/*    */   implements Runnable {
/*    */   private static boolean isinit = false;
/*    */   
/*    */   public void run() {
/*    */     try {
/* 13 */       isinit = true;
/*    */       
/* 15 */       Class<?> clazz = Class.forName("wscheck.CheckPageDump");
/* 16 */       Object object = clazz.newInstance();
/* 17 */       Method method = clazz.getMethod("check", new Class[0]);
/* 18 */       method.invoke(object, null);
/*    */       
/* 20 */       String str = Util.null2String((new BaseBean()).getPropValue("FileCheck", "ischeck")).trim();
/* 21 */       str = str.equals("") ? "0" : str;
/*    */       
/* 23 */       if (!"0".equals(str)) {
/* 24 */         CheckKey.checkKeyFile();
/*    */         
/* 26 */         Class<?> clazz1 = Class.forName("wscheck.CheckFirst");
/* 27 */         Object object1 = clazz1.newInstance();
/* 28 */         Method method1 = clazz1.getMethod("execute", new Class[0]);
/* 29 */         method1.invoke(object1, null);
/*    */ 
/*    */         
/* 32 */         Class<?> clazz2 = Class.forName("wscheck.CheckScheduler");
/* 33 */         Object object2 = clazz2.newInstance();
/* 34 */         Method method2 = clazz2.getMethod("start", new Class[0]);
/* 35 */         method2.invoke(object2, null);
/*    */       } 
/* 37 */     } catch (Exception exception) {
/* 38 */       exception.printStackTrace();
/*    */     } 
/*    */   }
/*    */   
/*    */   public static boolean isIsinit() {
/* 43 */     return isinit;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/wscheck/InitCheck.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */