/*     */ package wscheck;
/*     */ 
/*     */ import java.io.InputStream;
/*     */ import java.io.OutputStream;
/*     */ import java.security.Key;
/*     */ import java.security.NoSuchAlgorithmException;
/*     */ import java.security.SecureRandom;
/*     */ import javax.crypto.Cipher;
/*     */ import javax.crypto.CipherInputStream;
/*     */ import javax.crypto.CipherOutputStream;
/*     */ import javax.crypto.KeyGenerator;
/*     */ import javax.crypto.SecretKey;
/*     */ import javax.crypto.spec.SecretKeySpec;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class AESCoder
/*     */ {
/*     */   private static final String KEY_ALGORITHM = "AES";
/*     */   private static final String DEFAULT_CIPHER_ALGORITHM = "AES/ECB/PKCS5Padding";
/*     */   
/*     */   public static byte[] initSecretKey(String paramString) {
/*  42 */     KeyGenerator keyGenerator = null;
/*     */     try {
/*  44 */       keyGenerator = KeyGenerator.getInstance("AES");
/*     */ 
/*     */ 
/*     */       
/*  48 */       SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");
/*  49 */       secureRandom.setSeed(paramString.getBytes());
/*  50 */       keyGenerator.init(128, secureRandom);
/*  51 */     } catch (NoSuchAlgorithmException noSuchAlgorithmException) {
/*  52 */       noSuchAlgorithmException.printStackTrace();
/*  53 */       return new byte[0];
/*     */     } 
/*     */     
/*  56 */     SecretKey secretKey = keyGenerator.generateKey();
/*  57 */     return secretKey.getEncoded();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private static Key toKey(byte[] paramArrayOfbyte) {
/*  68 */     return new SecretKeySpec(paramArrayOfbyte, "AES");
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static InputStream encrypt(InputStream paramInputStream, String paramString) throws Exception {
/*  82 */     byte[] arrayOfByte = initSecretKey(paramString);
/*     */     
/*  84 */     Key key = toKey(arrayOfByte);
/*     */     
/*  86 */     Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
/*  87 */     cipher.init(1, key);
/*     */     
/*  89 */     return new CipherInputStream(paramInputStream, cipher);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static OutputStream encrypt(OutputStream paramOutputStream, String paramString) throws Exception {
/* 105 */     byte[] arrayOfByte = initSecretKey(paramString);
/*     */     
/* 107 */     Key key = toKey(arrayOfByte);
/*     */     
/* 109 */     Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
/* 110 */     cipher.init(1, key);
/*     */     
/* 112 */     return new CipherOutputStream(paramOutputStream, cipher);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static InputStream decrypt(InputStream paramInputStream, String paramString) throws Exception {
/* 126 */     byte[] arrayOfByte = initSecretKey(paramString);
/* 127 */     Key key = toKey(arrayOfByte);
/* 128 */     Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
/* 129 */     cipher.init(2, key);
/* 130 */     return new CipherInputStream(paramInputStream, cipher);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static OutputStream decrypt(OutputStream paramOutputStream, String paramString) throws Exception {
/* 145 */     byte[] arrayOfByte = initSecretKey(paramString);
/*     */     
/* 147 */     Key key = toKey(arrayOfByte);
/*     */     
/* 149 */     Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
/* 150 */     cipher.init(2, key);
/*     */     
/* 152 */     return new CipherOutputStream(paramOutputStream, cipher);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/wscheck/AESCoder.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */