/*     */ package wscheck;
/*     */ 
/*     */ import java.io.File;
/*     */ import java.io.FileInputStream;
/*     */ import java.io.IOException;
/*     */ import java.security.DigestInputStream;
/*     */ import java.security.MessageDigest;
/*     */ import org.apache.commons.codec.digest.DigestUtils;
/*     */ import weaver.general.BaseBean;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class MD5Coder
/*     */ {
/*     */   public static final String SALT = "wEAVER@check#2019";
/*     */   public static final String WEAVERCODE = "WEAVERCODE";
/*     */   static final int S11 = 7;
/*     */   static final int S12 = 12;
/*     */   static final int S13 = 17;
/*     */   static final int S14 = 22;
/*     */   static final int S21 = 5;
/*     */   static final int S22 = 9;
/*     */   static final int S23 = 14;
/*     */   static final int S24 = 20;
/*     */   static final int S31 = 4;
/*     */   static final int S32 = 11;
/*     */   static final int S33 = 16;
/*     */   static final int S34 = 23;
/*     */   static final int S41 = 6;
/*     */   static final int S42 = 10;
/*     */   static final int S43 = 15;
/*     */   static final int S44 = 21;
/*  35 */   static final byte[] PADDING = new byte[] { Byte.MIN_VALUE, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 };
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*  42 */   private long[] state = new long[4];
/*  43 */   private long[] count = new long[2];
/*  44 */   private byte[] buffer = new byte[64];
/*     */ 
/*     */ 
/*     */   
/*     */   public String digestHexStr;
/*     */ 
/*     */   
/*  51 */   private byte[] digest = new byte[16];
/*     */ 
/*     */   
/*     */   public MD5Coder() {
/*  55 */     md5Init();
/*     */   }
/*     */ 
/*     */   
/*     */   private void md5Init() {
/*  60 */     this.count[0] = 0L;
/*  61 */     this.count[1] = 0L;
/*     */     
/*  63 */     this.state[0] = 1732584193L;
/*  64 */     this.state[1] = 4023233417L;
/*  65 */     this.state[2] = 2562383102L;
/*  66 */     this.state[3] = 271733878L;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private long F(long paramLong1, long paramLong2, long paramLong3) {
/*  74 */     return paramLong1 & paramLong2 | (paramLong1 ^ 0xFFFFFFFFFFFFFFFFL) & paramLong3;
/*     */   }
/*     */   
/*     */   private long G(long paramLong1, long paramLong2, long paramLong3) {
/*  78 */     return paramLong1 & paramLong3 | paramLong2 & (paramLong3 ^ 0xFFFFFFFFFFFFFFFFL);
/*     */   }
/*     */   
/*     */   private long H(long paramLong1, long paramLong2, long paramLong3) {
/*  82 */     return paramLong1 ^ paramLong2 ^ paramLong3;
/*     */   }
/*     */   
/*     */   private long I(long paramLong1, long paramLong2, long paramLong3) {
/*  86 */     return paramLong2 ^ (paramLong1 | paramLong3 ^ 0xFFFFFFFFFFFFFFFFL);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private long FF(long paramLong1, long paramLong2, long paramLong3, long paramLong4, long paramLong5, long paramLong6, long paramLong7) {
/*  95 */     paramLong1 += F(paramLong2, paramLong3, paramLong4) + paramLong5 + paramLong7;
/*  96 */     paramLong1 = ((int)paramLong1 << (int)paramLong6 | (int)paramLong1 >>> (int)(32L - paramLong6));
/*  97 */     paramLong1 += paramLong2;
/*  98 */     return paramLong1;
/*     */   }
/*     */   
/*     */   private long GG(long paramLong1, long paramLong2, long paramLong3, long paramLong4, long paramLong5, long paramLong6, long paramLong7) {
/* 102 */     paramLong1 += G(paramLong2, paramLong3, paramLong4) + paramLong5 + paramLong7;
/* 103 */     paramLong1 = ((int)paramLong1 << (int)paramLong6 | (int)paramLong1 >>> (int)(32L - paramLong6));
/* 104 */     paramLong1 += paramLong2;
/* 105 */     return paramLong1;
/*     */   }
/*     */   
/*     */   private long HH(long paramLong1, long paramLong2, long paramLong3, long paramLong4, long paramLong5, long paramLong6, long paramLong7) {
/* 109 */     paramLong1 += H(paramLong2, paramLong3, paramLong4) + paramLong5 + paramLong7;
/* 110 */     paramLong1 = ((int)paramLong1 << (int)paramLong6 | (int)paramLong1 >>> (int)(32L - paramLong6));
/* 111 */     paramLong1 += paramLong2;
/* 112 */     return paramLong1;
/*     */   }
/*     */   
/*     */   private long II(long paramLong1, long paramLong2, long paramLong3, long paramLong4, long paramLong5, long paramLong6, long paramLong7) {
/* 116 */     paramLong1 += I(paramLong2, paramLong3, paramLong4) + paramLong5 + paramLong7;
/* 117 */     paramLong1 = ((int)paramLong1 << (int)paramLong6 | (int)paramLong1 >>> (int)(32L - paramLong6));
/* 118 */     paramLong1 += paramLong2;
/* 119 */     return paramLong1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void md5Update(byte[] paramArrayOfbyte, int paramInt) {
/*     */     byte b;
/* 128 */     byte[] arrayOfByte = new byte[64];
/* 129 */     int i = (int)(this.count[0] >>> 3L) & 0x3F;
/*     */     
/* 131 */     this.count[0] = this.count[0] + (paramInt << 3); if (this.count[0] + (paramInt << 3) < (paramInt << 3))
/* 132 */       this.count[1] = this.count[1] + 1L; 
/* 133 */     this.count[1] = this.count[1] + (paramInt >>> 29);
/* 134 */     int j = 64 - i;
/*     */     
/* 136 */     if (paramInt >= j) {
/* 137 */       md5Memcpy(this.buffer, paramArrayOfbyte, i, 0, j);
/* 138 */       md5Transform(this.buffer);
/* 139 */       for (b = j; b + 63 < paramInt; b += 64) {
/* 140 */         md5Memcpy(arrayOfByte, paramArrayOfbyte, 0, b, 64);
/* 141 */         md5Transform(arrayOfByte);
/*     */       } 
/* 143 */       i = 0;
/*     */     } else {
/* 145 */       b = 0;
/*     */     } 
/* 147 */     md5Memcpy(this.buffer, paramArrayOfbyte, i, b, paramInt - b);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void md5Final() {
/* 154 */     byte[] arrayOfByte = new byte[8];
/*     */ 
/*     */     
/* 157 */     Encode(arrayOfByte, this.count, 8);
/*     */     
/* 159 */     int i = (int)(this.count[0] >>> 3L) & 0x3F;
/* 160 */     int j = (i < 56) ? (56 - i) : (120 - i);
/* 161 */     md5Update(PADDING, j);
/*     */     
/* 163 */     md5Update(arrayOfByte, 8);
/*     */     
/* 165 */     Encode(this.digest, this.state, 16);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void md5Memcpy(byte[] paramArrayOfbyte1, byte[] paramArrayOfbyte2, int paramInt1, int paramInt2, int paramInt3) {
/* 173 */     for (byte b = 0; b < paramInt3; b++) {
/* 174 */       paramArrayOfbyte1[paramInt1 + b] = paramArrayOfbyte2[paramInt2 + b];
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private void md5Transform(byte[] paramArrayOfbyte) {
/* 181 */     long l1 = this.state[0], l2 = this.state[1], l3 = this.state[2], l4 = this.state[3];
/* 182 */     long[] arrayOfLong = new long[16];
/* 183 */     Decode(arrayOfLong, paramArrayOfbyte, 64);
/*     */     
/* 185 */     l1 = FF(l1, l2, l3, l4, arrayOfLong[0], 7L, 3614090360L);
/* 186 */     l4 = FF(l4, l1, l2, l3, arrayOfLong[1], 12L, 3905402710L);
/* 187 */     l3 = FF(l3, l4, l1, l2, arrayOfLong[2], 17L, 606105819L);
/* 188 */     l2 = FF(l2, l3, l4, l1, arrayOfLong[3], 22L, 3250441966L);
/* 189 */     l1 = FF(l1, l2, l3, l4, arrayOfLong[4], 7L, 4118548399L);
/* 190 */     l4 = FF(l4, l1, l2, l3, arrayOfLong[5], 12L, 1200080426L);
/* 191 */     l3 = FF(l3, l4, l1, l2, arrayOfLong[6], 17L, 2821735955L);
/* 192 */     l2 = FF(l2, l3, l4, l1, arrayOfLong[7], 22L, 4249261313L);
/* 193 */     l1 = FF(l1, l2, l3, l4, arrayOfLong[8], 7L, 1770035416L);
/* 194 */     l4 = FF(l4, l1, l2, l3, arrayOfLong[9], 12L, 2336552879L);
/* 195 */     l3 = FF(l3, l4, l1, l2, arrayOfLong[10], 17L, 4294925233L);
/* 196 */     l2 = FF(l2, l3, l4, l1, arrayOfLong[11], 22L, 2304563134L);
/* 197 */     l1 = FF(l1, l2, l3, l4, arrayOfLong[12], 7L, 1804603682L);
/* 198 */     l4 = FF(l4, l1, l2, l3, arrayOfLong[13], 12L, 4254626195L);
/* 199 */     l3 = FF(l3, l4, l1, l2, arrayOfLong[14], 17L, 2792965006L);
/* 200 */     l2 = FF(l2, l3, l4, l1, arrayOfLong[15], 22L, 1236535329L);
/*     */     
/* 202 */     l1 = GG(l1, l2, l3, l4, arrayOfLong[1], 5L, 4129170786L);
/* 203 */     l4 = GG(l4, l1, l2, l3, arrayOfLong[6], 9L, 3225465664L);
/* 204 */     l3 = GG(l3, l4, l1, l2, arrayOfLong[11], 14L, 643717713L);
/* 205 */     l2 = GG(l2, l3, l4, l1, arrayOfLong[0], 20L, 3921069994L);
/* 206 */     l1 = GG(l1, l2, l3, l4, arrayOfLong[5], 5L, 3593408605L);
/* 207 */     l4 = GG(l4, l1, l2, l3, arrayOfLong[10], 9L, 38016083L);
/* 208 */     l3 = GG(l3, l4, l1, l2, arrayOfLong[15], 14L, 3634488961L);
/* 209 */     l2 = GG(l2, l3, l4, l1, arrayOfLong[4], 20L, 3889429448L);
/* 210 */     l1 = GG(l1, l2, l3, l4, arrayOfLong[9], 5L, 568446438L);
/* 211 */     l4 = GG(l4, l1, l2, l3, arrayOfLong[14], 9L, 3275163606L);
/* 212 */     l3 = GG(l3, l4, l1, l2, arrayOfLong[3], 14L, 4107603335L);
/* 213 */     l2 = GG(l2, l3, l4, l1, arrayOfLong[8], 20L, 1163531501L);
/* 214 */     l1 = GG(l1, l2, l3, l4, arrayOfLong[13], 5L, 2850285829L);
/* 215 */     l4 = GG(l4, l1, l2, l3, arrayOfLong[2], 9L, 4243563512L);
/* 216 */     l3 = GG(l3, l4, l1, l2, arrayOfLong[7], 14L, 1735328473L);
/* 217 */     l2 = GG(l2, l3, l4, l1, arrayOfLong[12], 20L, 2368359562L);
/*     */     
/* 219 */     l1 = HH(l1, l2, l3, l4, arrayOfLong[5], 4L, 4294588738L);
/* 220 */     l4 = HH(l4, l1, l2, l3, arrayOfLong[8], 11L, 2272392833L);
/* 221 */     l3 = HH(l3, l4, l1, l2, arrayOfLong[11], 16L, 1839030562L);
/* 222 */     l2 = HH(l2, l3, l4, l1, arrayOfLong[14], 23L, 4259657740L);
/* 223 */     l1 = HH(l1, l2, l3, l4, arrayOfLong[1], 4L, 2763975236L);
/* 224 */     l4 = HH(l4, l1, l2, l3, arrayOfLong[4], 11L, 1272893353L);
/* 225 */     l3 = HH(l3, l4, l1, l2, arrayOfLong[7], 16L, 4139469664L);
/* 226 */     l2 = HH(l2, l3, l4, l1, arrayOfLong[10], 23L, 3200236656L);
/* 227 */     l1 = HH(l1, l2, l3, l4, arrayOfLong[13], 4L, 681279174L);
/* 228 */     l4 = HH(l4, l1, l2, l3, arrayOfLong[0], 11L, 3936430074L);
/* 229 */     l3 = HH(l3, l4, l1, l2, arrayOfLong[3], 16L, 3572445317L);
/* 230 */     l2 = HH(l2, l3, l4, l1, arrayOfLong[6], 23L, 76029189L);
/* 231 */     l1 = HH(l1, l2, l3, l4, arrayOfLong[9], 4L, 3654602809L);
/* 232 */     l4 = HH(l4, l1, l2, l3, arrayOfLong[12], 11L, 3873151461L);
/* 233 */     l3 = HH(l3, l4, l1, l2, arrayOfLong[15], 16L, 530742520L);
/* 234 */     l2 = HH(l2, l3, l4, l1, arrayOfLong[2], 23L, 3299628645L);
/*     */     
/* 236 */     l1 = II(l1, l2, l3, l4, arrayOfLong[0], 6L, 4096336452L);
/* 237 */     l4 = II(l4, l1, l2, l3, arrayOfLong[7], 10L, 1126891415L);
/* 238 */     l3 = II(l3, l4, l1, l2, arrayOfLong[14], 15L, 2878612391L);
/* 239 */     l2 = II(l2, l3, l4, l1, arrayOfLong[5], 21L, 4237533241L);
/* 240 */     l1 = II(l1, l2, l3, l4, arrayOfLong[12], 6L, 1700485571L);
/* 241 */     l4 = II(l4, l1, l2, l3, arrayOfLong[3], 10L, 2399980690L);
/* 242 */     l3 = II(l3, l4, l1, l2, arrayOfLong[10], 15L, 4293915773L);
/* 243 */     l2 = II(l2, l3, l4, l1, arrayOfLong[1], 21L, 2240044497L);
/* 244 */     l1 = II(l1, l2, l3, l4, arrayOfLong[8], 6L, 1873313359L);
/* 245 */     l4 = II(l4, l1, l2, l3, arrayOfLong[15], 10L, 4264355552L);
/* 246 */     l3 = II(l3, l4, l1, l2, arrayOfLong[6], 15L, 2734768916L);
/* 247 */     l2 = II(l2, l3, l4, l1, arrayOfLong[13], 21L, 1309151649L);
/* 248 */     l1 = II(l1, l2, l3, l4, arrayOfLong[4], 6L, 4149444226L);
/* 249 */     l4 = II(l4, l1, l2, l3, arrayOfLong[11], 10L, 3174756917L);
/* 250 */     l3 = II(l3, l4, l1, l2, arrayOfLong[2], 15L, 718787259L);
/* 251 */     l2 = II(l2, l3, l4, l1, arrayOfLong[9], 21L, 3951481745L);
/* 252 */     this.state[0] = this.state[0] + l1;
/* 253 */     this.state[1] = this.state[1] + l2;
/* 254 */     this.state[2] = this.state[2] + l3;
/* 255 */     this.state[3] = this.state[3] + l4;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void Encode(byte[] paramArrayOfbyte, long[] paramArrayOflong, int paramInt) {
/* 263 */     for (byte b1 = 0, b2 = 0; b2 < paramInt; b1++, b2 += 4) {
/* 264 */       paramArrayOfbyte[b2] = (byte)(int)(paramArrayOflong[b1] & 0xFFL);
/* 265 */       paramArrayOfbyte[b2 + 1] = (byte)(int)(paramArrayOflong[b1] >>> 8L & 0xFFL);
/* 266 */       paramArrayOfbyte[b2 + 2] = (byte)(int)(paramArrayOflong[b1] >>> 16L & 0xFFL);
/* 267 */       paramArrayOfbyte[b2 + 3] = (byte)(int)(paramArrayOflong[b1] >>> 24L & 0xFFL);
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void Decode(long[] paramArrayOflong, byte[] paramArrayOfbyte, int paramInt) {
/* 276 */     for (byte b1 = 0, b2 = 0; b2 < paramInt; b1++, b2 += 4) {
/* 277 */       paramArrayOflong[b1] = b2iu(paramArrayOfbyte[b2]) | b2iu(paramArrayOfbyte[b2 + 1]) << 8L | b2iu(paramArrayOfbyte[b2 + 2]) << 16L | 
/* 278 */         b2iu(paramArrayOfbyte[b2 + 3]) << 24L;
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static long b2iu(byte paramByte) {
/* 286 */     return (paramByte < 0) ? (paramByte & 0xFF) : paramByte;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String byteHEX(byte paramByte) {
/* 294 */     char[] arrayOfChar1 = { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F' };
/*     */     
/* 296 */     char[] arrayOfChar2 = new char[2];
/* 297 */     arrayOfChar2[0] = arrayOfChar1[paramByte >>> 4 & 0xF];
/* 298 */     arrayOfChar2[1] = arrayOfChar1[paramByte & 0xF];
/* 299 */     return new String(arrayOfChar2);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getMD5ofStr(String paramString) {
/* 308 */     md5Init();
/* 309 */     md5Update(paramString.getBytes(), paramString.length());
/* 310 */     md5Final();
/* 311 */     this.digestHexStr = "";
/* 312 */     for (byte b = 0; b < 16; b++) {
/* 313 */       this.digestHexStr += byteHEX(this.digest[b]);
/*     */     }
/* 315 */     return this.digestHexStr;
/*     */   }
/*     */   
/*     */   public static String stringMD5(String paramString) {
/* 319 */     MD5Coder mD5Coder = new MD5Coder();
/* 320 */     return mD5Coder.getMD5ofStr(paramString).toLowerCase();
/*     */   }
/*     */   
/*     */   public String getFileMD5String(File paramFile) throws IOException {
/* 324 */     String str = "";
/*     */     try {
/* 326 */       String str1 = paramFile.getAbsolutePath();
/* 327 */       str = fileMD5(str1);
/* 328 */     } catch (Exception exception) {
/* 329 */       throw new IOException(exception);
/*     */     } finally {}
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 337 */     return str;
/*     */   }
/*     */   
/*     */   public String getFileMD5String(File paramFile, String paramString) throws IOException {
/* 341 */     String str = "";
/*     */     try {
/* 343 */       String str1 = paramFile.getAbsolutePath();
/* 344 */       str = fileMD5(str1);
/* 345 */     } catch (Exception exception) {
/* 346 */       throw new IOException(exception);
/*     */     } finally {}
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 354 */     return str;
/*     */   }
/*     */ 
/*     */   
/*     */   public String getFileMD5StringIO(File paramFile, String paramString) throws Exception {
/* 359 */     String str = "";
/*     */     try {
/* 361 */       String str1 = paramFile.getAbsolutePath();
/* 362 */       str = fileMD5(str1);
/* 363 */     } catch (Exception exception) {
/* 364 */       throw new Exception(exception);
/*     */     } finally {}
/*     */     
/* 367 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String fileMD5(String paramString) {
/* 386 */     FileInputStream fileInputStream = null;
/*     */     try {
/* 388 */       MessageDigest messageDigest = MessageDigest.getInstance("MD5");
/* 389 */       fileInputStream = new FileInputStream(paramString);
/* 390 */       byte[] arrayOfByte = new byte[8192];
/*     */       int i;
/* 392 */       while ((i = fileInputStream.read(arrayOfByte)) != -1) {
/* 393 */         messageDigest.update(arrayOfByte, 0, i);
/*     */       }
/* 395 */       return DigestUtils.md5Hex(messageDigest.digest());
/* 396 */     } catch (Exception exception) {
/* 397 */       exception.printStackTrace();
/* 398 */       return null;
/*     */     } finally {
/*     */       try {
/* 401 */         if (fileInputStream != null) {
/* 402 */           fileInputStream.close();
/*     */         }
/* 404 */       } catch (IOException iOException) {
/* 405 */         iOException.printStackTrace();
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public static String fileMD5BigFile(String paramString) {
/* 412 */     int i = 262144;
/* 413 */     FileInputStream fileInputStream = null;
/* 414 */     DigestInputStream digestInputStream = null;
/* 415 */     MD5Coder mD5Coder = new MD5Coder();
/*     */     
/*     */     try {
/* 418 */       MessageDigest messageDigest = MessageDigest.getInstance("MD5");
/*     */       
/* 420 */       fileInputStream = new FileInputStream(paramString);
/* 421 */       digestInputStream = new DigestInputStream(fileInputStream, messageDigest);
/*     */       
/* 423 */       byte[] arrayOfByte1 = new byte[i];
/* 424 */       while (digestInputStream.read(arrayOfByte1) > 0);
/*     */ 
/*     */       
/* 427 */       messageDigest = digestInputStream.getMessageDigest();
/*     */       
/* 429 */       byte[] arrayOfByte2 = messageDigest.digest();
/*     */       
/* 431 */       return mD5Coder.getMD5ofStr(new String(arrayOfByte2)).toLowerCase();
/* 432 */     } catch (Exception exception) {
/* 433 */       exception.printStackTrace();
/* 434 */       return null;
/*     */     } finally {
/*     */       try {
/* 437 */         digestInputStream.close();
/* 438 */       } catch (Exception exception) {}
/*     */       
/*     */       try {
/* 441 */         fileInputStream.close();
/* 442 */       } catch (Exception exception) {}
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public static String fileMD5(File paramFile) throws Exception {
/* 448 */     MD5Coder mD5Coder = new MD5Coder();
/* 449 */     return mD5Coder.getFileMD5String(paramFile).toLowerCase();
/*     */   }
/*     */   
/*     */   public static String fileMD5(File paramFile, String paramString) throws Exception {
/* 453 */     MD5Coder mD5Coder = new MD5Coder();
/* 454 */     return mD5Coder.getFileMD5String(paramFile, paramString).toLowerCase();
/*     */   }
/*     */   
/*     */   public static String fileMD5IO(File paramFile, String paramString) throws Exception {
/* 458 */     MD5Coder mD5Coder = new MD5Coder();
/* 459 */     return mD5Coder.getFileMD5StringIO(paramFile, paramString).toLowerCase();
/*     */   }
/*     */   
/*     */   public static void getFileMD5FromFilepath(String paramString) {
/* 463 */     File file1 = null;
/* 464 */     File file2 = new File(paramString);
/* 465 */     if (null != file2 && 
/* 466 */       file2.isDirectory()) {
/* 467 */       File[] arrayOfFile = file2.listFiles();
/* 468 */       if (null != arrayOfFile) {
/* 469 */         for (byte b = 0; b < arrayOfFile.length; b++) {
/* 470 */           file1 = null;
/* 471 */           file1 = arrayOfFile[b];
/* 472 */           if (file1.isDirectory() && file1.exists()) {
/* 473 */             getFileMD5FromFilepath(file1.getAbsolutePath());
/*     */           }
/* 475 */           else if (file1.isFile() && file1.exists() && (
/* 476 */             file1.getName().indexOf(".jsp") > -1 || file1
/* 477 */             .getName().indexOf(".java") > -1 || file1
/* 478 */             .getName().indexOf(".class") > -1)) {
/*     */             try {
/* 480 */               (new BaseBean()).writeLog(fileMD5(file1.getAbsolutePath()));
/* 481 */             } catch (Exception exception) {
/* 482 */               exception.printStackTrace();
/*     */             } 
/*     */           } 
/*     */         } 
/*     */       }
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getMD5BigFile(File paramFile) {
/* 500 */     FileInputStream fileInputStream = null;
/*     */     try {
/* 502 */       MessageDigest messageDigest = MessageDigest.getInstance("MD5");
/* 503 */       fileInputStream = new FileInputStream(paramFile);
/* 504 */       byte[] arrayOfByte = new byte[8192];
/*     */       int i;
/* 506 */       while ((i = fileInputStream.read(arrayOfByte)) != -1) {
/* 507 */         messageDigest.update(arrayOfByte, 0, i);
/*     */       }
/* 509 */       return DigestUtils.md5Hex(messageDigest.digest());
/* 510 */     } catch (Exception exception) {
/* 511 */       exception.printStackTrace();
/* 512 */       return null;
/*     */     } finally {
/*     */       try {
/* 515 */         if (fileInputStream != null) {
/* 516 */           fileInputStream.close();
/*     */         }
/* 518 */       } catch (IOException iOException) {
/* 519 */         iOException.printStackTrace();
/*     */       } 
/*     */     } 
/*     */   }
/*     */   
/*     */   public static void main(String[] paramArrayOfString) {
/* 525 */     long l1 = System.currentTimeMillis();
/* 526 */     getFileMD5FromFilepath("E:\\workspace\\ecology_70\\ecology");
/* 527 */     long l2 = System.currentTimeMillis();
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/wscheck/MD5Coder.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */