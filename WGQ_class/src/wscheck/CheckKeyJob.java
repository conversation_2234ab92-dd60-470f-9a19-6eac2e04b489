/*    */ package wscheck;
/*    */ 
/*    */ import java.lang.reflect.Method;
/*    */ import org.quartz.Job;
/*    */ import org.quartz.JobExecutionContext;
/*    */ import org.quartz.JobExecutionException;
/*    */ 
/*    */ public class CheckKeyJob
/*    */   implements Job {
/*    */   public void execute(JobExecutionContext paramJobExecutionContext) throws JobExecutionException {
/*    */     try {
/* 12 */       Class<?> clazz = Class.forName("wscheck.CheckKey");
/* 13 */       Object object = clazz.newInstance();
/* 14 */       Method method = clazz.getMethod("execute", new Class[0]);
/* 15 */       method.invoke(object, null);
/* 16 */     } catch (Exception exception) {
/* 17 */       exception.printStackTrace();
/*    */     } 
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/wscheck/CheckKeyJob.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */