/*    */ package wscheck;
/*    */ 
/*    */ import org.quartz.CronExpression;
/*    */ import org.quartz.JobDetail;
/*    */ import org.quartz.Scheduler;
/*    */ import org.quartz.Trigger;
/*    */ import org.quartz.impl.JobDetailImpl;
/*    */ import org.quartz.impl.StdSchedulerFactory;
/*    */ import org.quartz.impl.triggers.CronTriggerImpl;
/*    */ 
/*    */ public class CheckScheduler
/*    */ {
/*    */   public static void start() {
/*    */     try {
/* 15 */       String str1 = System.currentTimeMillis() + "";
/* 16 */       Scheduler scheduler = (new StdSchedulerFactory()).getScheduler();
/*    */ 
/*    */       
/* 19 */       JobDetailImpl jobDetailImpl1 = new JobDetailImpl(str1, "3", CheckKeyJob.class);
/* 20 */       CronTriggerImpl cronTriggerImpl1 = new CronTriggerImpl("3_trigger" + str1, "tgroup3");
/* 21 */       String str2 = "0 0/30 * * * ?";
/* 22 */       CronExpression cronExpression1 = new CronExpression(str2);
/* 23 */       cronTriggerImpl1.setCronExpression(cronExpression1);
/* 24 */       scheduler.scheduleJob((JobDetail)jobDetailImpl1, (Trigger)cronTriggerImpl1);
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */       
/* 35 */       JobDetailImpl jobDetailImpl2 = new JobDetailImpl(str1, "1", CheckScanFileJob.class);
/* 36 */       CronTriggerImpl cronTriggerImpl2 = new CronTriggerImpl("1_trigger" + str1, "tgroup1");
/* 37 */       String str3 = "0 0/30 * * * ?";
/* 38 */       CronExpression cronExpression2 = new CronExpression(str3);
/* 39 */       cronTriggerImpl2.setCronExpression(cronExpression2);
/* 40 */       scheduler.scheduleJob((JobDetail)jobDetailImpl2, (Trigger)cronTriggerImpl2);
/*    */     }
/* 42 */     catch (Throwable throwable) {
/* 43 */       throwable.printStackTrace();
/*    */     } 
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/wscheck/CheckScheduler.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */