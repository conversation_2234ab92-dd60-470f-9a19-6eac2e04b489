/*     */ package wscheck;
/*     */ 
/*     */ import java.io.BufferedWriter;
/*     */ import java.io.File;
/*     */ import java.io.FileNotFoundException;
/*     */ import java.io.FileOutputStream;
/*     */ import java.io.IOException;
/*     */ import java.io.OutputStreamWriter;
/*     */ import java.text.SimpleDateFormat;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Date;
/*     */ import java.util.Hashtable;
/*     */ import java.util.Iterator;
/*     */ import java.util.Map;
/*     */ import java.util.Set;
/*     */ import org.apache.commons.logging.Log;
/*     */ import org.apache.commons.logging.LogFactory;
/*     */ import org.apache.poi.hssf.usermodel.HSSFCell;
/*     */ import org.apache.poi.hssf.usermodel.HSSFCellStyle;
/*     */ import org.apache.poi.hssf.usermodel.HSSFDataFormat;
/*     */ import org.apache.poi.hssf.usermodel.HSSFFont;
/*     */ import org.apache.poi.hssf.usermodel.HSSFRow;
/*     */ import org.apache.poi.hssf.usermodel.HSSFSheet;
/*     */ import org.apache.poi.hssf.usermodel.HSSFWorkbook;
/*     */ import org.apache.poi.ss.usermodel.CellType;
/*     */ import org.apache.poi.ss.usermodel.FillPatternType;
/*     */ import org.apache.poi.ss.usermodel.HorizontalAlignment;
/*     */ import org.apache.poi.ss.usermodel.VerticalAlignment;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.file.ExcelFile;
/*     */ import weaver.file.ExcelRow;
/*     */ import weaver.file.ExcelSheet;
/*     */ import weaver.file.ExcelStyle;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.User;
/*     */ import weaver.systeminfo.SystemEnv;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class SystemUpdateInfo
/*     */ {
/*  46 */   private static final Log log = LogFactory.getLog(SystemUpdateInfo.class);
/*     */   
/*     */   public static String getSystemUpdateInfo(String paramString, User paramUser) {
/*  49 */     if (paramString == null || paramString.equals("")) {
/*  50 */       paramString = GCONST.getRootPath();
/*     */     }
/*  52 */     else if (!paramString.endsWith(File.separator)) {
/*  53 */       paramString = paramString + File.separator;
/*     */     } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*  60 */     String str1 = "";
/*  61 */     RecordSet recordSet = new RecordSet();
/*  62 */     recordSet.executeSql("select cversion from license");
/*  63 */     if (recordSet.next()) {
/*  64 */       str1 = recordSet.getString(1);
/*     */     }
/*  66 */     String str2 = "";
/*  67 */     recordSet.executeSql("select max(label) from ecologyuplist");
/*  68 */     if (recordSet.next()) {
/*  69 */       str2 = recordSet.getString(1);
/*     */     }
/*  71 */     String str3 = "";
/*  72 */     String str4 = "";
/*     */     try {
/*  74 */       StringBuffer stringBuffer = new StringBuffer();
/*  75 */       String str5 = TimeUtil.getCurrentTimeString();
/*  76 */       stringBuffer.append(SystemEnv.getHtmlLabelName(532948, paramUser.getLanguage()) + str5);
/*  77 */       stringBuffer.append("\n");
/*  78 */       stringBuffer.append(SystemEnv.getHtmlLabelName(532949, paramUser.getLanguage()) + str1);
/*  79 */       stringBuffer.append("\n");
/*  80 */       stringBuffer.append(SystemEnv.getHtmlLabelName(532950, paramUser.getLanguage()) + str2);
/*  81 */       stringBuffer.append("\n");
/*  82 */       String str6 = "update.info";
/*  83 */       str3 = paramString + str6;
/*     */       
/*  85 */       CommonUtil.rewritefile(str3, stringBuffer.toString());
/*     */       
/*  87 */       str4 = str3;
/*  88 */     } catch (Exception exception) {}
/*     */ 
/*     */     
/*  91 */     return str4;
/*     */   }
/*     */   
/*     */   public static String getSystemUpdateInfo(User paramUser) {
/*  95 */     return getSystemUpdateInfo(null, paramUser);
/*     */   }
/*     */   
/*     */   public static String getModifyAll(Map paramMap, String paramString, boolean paramBoolean) {
/*  99 */     return getModifyAll(paramMap, paramString, paramBoolean, null, new User());
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String getModifyAll(Map paramMap, String paramString1, boolean paramBoolean, String paramString2, User paramUser) {
/* 111 */     if (paramString1 == null) {
/* 112 */       paramString1 = "";
/*     */     }
/* 114 */     StringBuilder stringBuilder = new StringBuilder();
/* 115 */     stringBuilder.append("<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">\n<html xmlns=\"http://www.w3.org/1999/xhtml\">\n<head>\n<meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\" />\n<title>" + 
/*     */ 
/*     */ 
/*     */         
/* 119 */         SystemEnv.getHtmlLabelName(532951, paramUser.getLanguage()) + "</title>\n</head>\n<body>\n<span style=\"color:red;font-weight:bold\">" + paramString1 + "</span><br>\n<table border=1px solid; cellspacing=0px;>\n    <thead>\n    <tr>\n<th width=\"40%\" scope=\"col\"  style=\"background-color:#969696\"><b>          " + 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 126 */         SystemEnv.getHtmlLabelName(25391, paramUser.getLanguage()) + "          </b></th>\n<th width=\"10%\" scope=\"col\" style=\"background-color:#969696\"><b>" + 
/* 127 */         SystemEnv.getHtmlLabelName(126207, paramUser.getLanguage()) + "</b></th>\n<th width=\"10%\" scope=\"col\" style=\"background-color:#969696\"><b>" + 
/* 128 */         SystemEnv.getHtmlLabelName(526400, paramUser.getLanguage()) + "</b></th>\n<th width=\"10%\" scope=\"col\" style=\"background-color:#969696\"><b>" + 
/* 129 */         SystemEnv.getHtmlLabelName(526401, paramUser.getLanguage()) + "</b></th>\n<th width=\"10%\" scope=\"col\" style=\"background-color:#969696\"><b>" + 
/* 130 */         SystemEnv.getHtmlLabelName(25734, paramUser.getLanguage()) + "</b></th>\n        </tr>\n    </thead>\n    <tbody>\n");
/*     */ 
/*     */ 
/*     */     
/* 134 */     String str1 = paramBoolean ? ("" + SystemEnv.getHtmlLabelName(532951, paramUser.getLanguage())) : ("" + SystemEnv.getHtmlLabelName(532952, paramUser.getLanguage()));
/* 135 */     String str2 = GCONST.getRootPath() + str1 + ".xls";
/* 136 */     String str3 = "";
/* 137 */     byte b = 1;
/* 138 */     if (null != paramMap && paramMap.size() > 0) {
/* 139 */       Set set = paramMap.keySet();
/* 140 */       for (Iterator<String> iterator = set.iterator(); iterator.hasNext(); ) {
/* 141 */         boolean bool = false;
/* 142 */         if (b % 2 == 0) {
/* 143 */           str3 = " style=\"background-color:#FFFFFF\" ";
/*     */         } else {
/* 145 */           str3 = "style=\"background-color:#C0C0C0\"";
/*     */         } 
/* 147 */         String str4 = Util.null2String(iterator.next()).trim();
/* 148 */         if ("".equals(str4)) {
/*     */           continue;
/*     */         }
/* 151 */         String str5 = (GCONST.getRootPath() + str4).replaceAll("/+", "\\\\").replaceAll("\\\\+", "\\\\");
/* 152 */         if ("\\".equals("" + File.separatorChar)) {
/* 153 */           str5 = str5.replaceAll("/", "\\\\");
/*     */         } else {
/* 155 */           str5 = str5.replaceAll("\\\\", "/");
/*     */         } 
/* 157 */         String str6 = Util.null2String((String)paramMap.get(str4)).trim();
/* 158 */         String str7 = "";
/* 159 */         String str8 = "";
/*     */         
/* 161 */         Date date = null;
/*     */         try {
/* 163 */           if (!str6.equals("0")) {
/* 164 */             File file = new File(str5);
/* 165 */             date = new Date(file.lastModified());
/*     */           } 
/* 167 */         } catch (Exception exception) {}
/*     */ 
/*     */         
/* 170 */         if (str6.indexOf("2") == 0) {
/* 171 */           ArrayList<String> arrayList = Util.TokenizerString(str6, "+");
/* 172 */           if (arrayList.size() == 2) {
/* 173 */             str6 = arrayList.get(0);
/* 174 */             str7 = arrayList.get(1);
/*     */             try {
/* 176 */               SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
/* 177 */               str7 = simpleDateFormat.format(new Date(Long.parseLong(str7)));
/*     */               
/* 179 */               str7 = TimeUtil.getDateString(TimeUtil.getString2Date(str7, "yyyy'-'MM'-'dd"));
/* 180 */             } catch (Exception exception) {}
/*     */           } 
/*     */         } 
/*     */ 
/*     */         
/* 185 */         if (null != date) {
/* 186 */           str8 = TimeUtil.getDateString(date);
/* 187 */           bool = (str8.compareTo(str7) < 0) ? true : false;
/*     */         } 
/*     */         
/* 190 */         if (!paramBoolean && bool) {
/*     */           continue;
/*     */         }
/* 193 */         String str9 = "";
/* 194 */         if (str6.equals("0")) {
/* 195 */           str9 = "" + SystemEnv.getHtmlLabelName(91, paramUser.getLanguage());
/* 196 */         } else if (str6.equals("1")) {
/* 197 */           str9 = "" + SystemEnv.getHtmlLabelName(1421, paramUser.getLanguage());
/* 198 */         } else if (str6.equals("2")) {
/* 199 */           str9 = "" + SystemEnv.getHtmlLabelName(103, paramUser.getLanguage());
/*     */         } 
/* 201 */         stringBuilder.append("<tr style=\"height:40px\">\n");
/* 202 */         stringBuilder.append("        <td " + str3 + ">" + str5 + "</td>\n");
/* 203 */         stringBuilder.append("        <td " + str3 + ">" + str9 + "</td>\n");
/* 204 */         stringBuilder.append("        <td " + str3 + ">" + (str8.equals("") ? "" : str8) + "</td>\n");
/* 205 */         stringBuilder.append("        <td " + str3 + ">" + str7 + "</td>\n");
/* 206 */         stringBuilder.append("        <td " + str3 + ">" + (bool ? ("" + SystemEnv.getHtmlLabelName(526402, paramUser.getLanguage())) : "") + "</td>\n");
/* 207 */         stringBuilder.append("</tr>\n");
/* 208 */         b++;
/*     */       } 
/* 210 */       stringBuilder.append("  </tbody>\n</table>\n</body>\n</html>\n");
/*     */     } 
/*     */ 
/*     */ 
/*     */     
/* 215 */     writeReportFile(stringBuilder.toString(), str2);
/* 216 */     return str2;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static void writeReportFile(String paramString1, String paramString2) {
/* 225 */     byte[] arrayOfByte = paramString1.getBytes();
/* 226 */     FileOutputStream fileOutputStream = null;
/* 227 */     BufferedWriter bufferedWriter = null;
/* 228 */     if (arrayOfByte.length <= 0)
/* 229 */       return;  File file1 = new File(paramString2);
/* 230 */     File file2 = new File(GCONST.getRootPath() + File.separatorChar + "templetecheck" + File.separatorChar + "report" + File.separatorChar);
/* 231 */     file2.mkdirs();
/* 232 */     if (file1.exists()) {
/* 233 */       file1.delete();
/*     */     }
/*     */     try {
/* 236 */       file1.createNewFile();
/* 237 */     } catch (IOException iOException) {
/* 238 */       iOException.printStackTrace();
/*     */     } 
/*     */     try {
/* 241 */       fileOutputStream = new FileOutputStream(paramString2);
/* 242 */       bufferedWriter = new BufferedWriter(new OutputStreamWriter(fileOutputStream, "UTF-8"));
/* 243 */       bufferedWriter.write(paramString1);
/* 244 */     } catch (FileNotFoundException fileNotFoundException) {
/* 245 */       fileNotFoundException.printStackTrace();
/* 246 */     } catch (IOException iOException) {
/* 247 */       iOException.printStackTrace();
/*     */     } finally {
/* 249 */       if (bufferedWriter != null) {
/*     */         try {
/* 251 */           bufferedWriter.close();
/* 252 */         } catch (IOException iOException) {
/* 253 */           iOException.printStackTrace();
/*     */         } 
/*     */       }
/* 256 */       if (fileOutputStream != null) {
/*     */         try {
/* 258 */           fileOutputStream.close();
/* 259 */         } catch (IOException iOException) {
/* 260 */           iOException.printStackTrace();
/*     */         } 
/*     */       }
/*     */     } 
/*     */   }
/*     */   
/* 266 */   private static Hashtable styleht = null;
/*     */ 
/*     */ 
/*     */   
/*     */   public static String toFile(ExcelFile paramExcelFile, String paramString) throws Exception {
/* 271 */     return toFile(paramExcelFile, paramString, null);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static String toFile(ExcelFile paramExcelFile, String paramString1, String paramString2) throws Exception {
/* 281 */     if (paramString2 == null || paramString2.equals("")) {
/* 282 */       paramString2 = GCONST.getRootPath();
/*     */     }
/* 284 */     else if (!paramString2.endsWith(File.separator)) {
/* 285 */       paramString2 = paramString2 + File.separator;
/*     */     } 
/*     */     
/* 288 */     ExcelSheet excelSheet = null;
/* 289 */     ExcelRow excelRow = null;
/*     */     
/* 291 */     HSSFWorkbook hSSFWorkbook = null;
/* 292 */     HSSFSheet hSSFSheet = null;
/* 293 */     HSSFRow hSSFRow = null;
/* 294 */     HSSFCell hSSFCell = null;
/* 295 */     HSSFCellStyle hSSFCellStyle1 = null;
/*     */     
/* 297 */     if (paramExcelFile == null) {
/* 298 */       return "";
/*     */     }
/* 300 */     hSSFWorkbook = new HSSFWorkbook();
/*     */     
/* 302 */     initStyle(paramExcelFile, hSSFWorkbook);
/*     */     
/* 304 */     byte b = 0;
/* 305 */     HSSFCellStyle hSSFCellStyle2 = hSSFWorkbook.createCellStyle();
/* 306 */     while (paramExcelFile.next()) {
/* 307 */       String str = paramExcelFile.getSheetname();
/* 308 */       excelSheet = paramExcelFile.getSheet();
/*     */       
/* 310 */       if (excelSheet == null)
/*     */         continue; 
/* 312 */       hSSFSheet = hSSFWorkbook.createSheet();
/* 313 */       hSSFWorkbook.setSheetName(b, Util.fromScreen2(str, 7));
/* 314 */       b++; byte b1;
/* 315 */       for (b1 = 0; b1 < excelSheet.size(); b1++) {
/*     */         
/* 317 */         excelRow = excelSheet.getExcelRow(b1);
/*     */         
/* 319 */         if (excelRow != null) {
/*     */           
/* 321 */           short s = excelRow.getHight();
/*     */           
/* 323 */           hSSFRow = hSSFSheet.createRow((short)b1);
/*     */           
/* 325 */           if (s != 255) {
/* 326 */             hSSFRow.setHeightInPoints(s);
/*     */           }
/* 328 */           byte b2 = 0;
/* 329 */           boolean bool1 = false;
/* 330 */           boolean bool2 = false;
/*     */           
/* 332 */           if (excelRow.stylesize() == excelRow.size())
/* 333 */             bool1 = true; 
/* 334 */           if (excelRow.spansize() == excelRow.size())
/* 335 */             bool2 = true; 
/* 336 */           for (byte b3 = 0; b3 < excelRow.size(); b3++) {
/*     */             
/* 338 */             hSSFCell = hSSFRow.createCell((short)b2);
/*     */             
/* 340 */             String str3 = Util.null2String(excelRow.getValue(b3));
/* 341 */             String str4 = str3.substring(0, 2);
/* 342 */             String str5 = str3.substring(2);
/*     */ 
/*     */ 
/*     */ 
/*     */             
/* 347 */             if (str4.indexOf("s_") == 0) {
/*     */               
/* 349 */               HSSFDataFormat hSSFDataFormat = hSSFWorkbook.createDataFormat();
/* 350 */               hSSFCellStyle2.setDataFormat(hSSFDataFormat.getFormat("@"));
/* 351 */               hSSFCell.setCellStyle(hSSFCellStyle2);
/*     */               
/* 353 */               hSSFCell.setCellType(CellType.STRING);
/* 354 */               hSSFCell.setCellValue(Util.fromScreen4(str5, 7));
/* 355 */             } else if (str4.indexOf("i_") == 0) {
/* 356 */               int i = Util.getIntValue(str5);
/* 357 */               if (i != 0)
/* 358 */                 hSSFCell.setCellValue(i); 
/* 359 */             } else if (str4.indexOf("f_") == 0) {
/* 360 */               float f = Util.getFloatValue(str5);
/* 361 */               if (f != 0.0D)
/* 362 */                 hSSFCell.setCellValue(f); 
/* 363 */             } else if (str4.indexOf("d_") == 0) {
/* 364 */               double d = Util.getDoubleValue(str5);
/* 365 */               if (d != 0.0D)
/* 366 */                 hSSFCell.setCellValue(d); 
/* 367 */             } else if (str4.indexOf("o_") == 0) {
/* 368 */               hSSFCell.setCellFormula(str5);
/* 369 */             } else if (str4.indexOf("n_") == 0) {
/*     */ 
/*     */ 
/*     */               
/* 373 */               hSSFCell.setCellValue(Util.fromScreen4(str5, 7));
/*     */             } 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */             
/* 382 */             if (bool1) {
/* 383 */               String str6 = Util.null2String(excelRow.getStyle(b3));
/* 384 */               if (!str6.equals("")) {
/* 385 */                 hSSFCellStyle1 = getStyle(str6);
/* 386 */                 if (hSSFCellStyle1 != null) {
/* 387 */                   hSSFCell.setCellStyle(hSSFCellStyle1);
/*     */                 }
/*     */               } 
/*     */             } 
/* 391 */             if (bool2) {
/* 392 */               int i = excelRow.getSpan(b3);
/* 393 */               if (i > 1) {
/* 394 */                 for (byte b4 = 0; b4 < i - 1; b4++) {
/* 395 */                   b2++;
/* 396 */                   hSSFCell = hSSFRow.createCell((short)b2);
/* 397 */                   hSSFCell.setCellValue("");
/* 398 */                   if (bool1 && hSSFCellStyle1 != null) {
/* 399 */                     hSSFCell.setCellStyle(hSSFCellStyle1);
/*     */                   }
/*     */                 } 
/*     */               }
/*     */             } 
/*     */             
/* 405 */             b2++;
/*     */           } 
/*     */         } 
/*     */       } 
/* 409 */       for (b1 = 0; b1 < excelSheet.columnsize(); b1++) {
/* 410 */         hSSFSheet.setColumnWidth((short)b1, excelSheet.getColumnwidth(b1));
/*     */       }
/*     */     } 
/*     */     
/* 414 */     String str1 = paramExcelFile.getFilename();
/*     */     
/* 416 */     String str2 = paramString2 + paramString1 + ".xls";
/*     */     
/* 418 */     File file = new File(str2);
/* 419 */     if (file.exists()) {
/* 420 */       file.delete();
/*     */     }
/* 422 */     FileOutputStream fileOutputStream = new FileOutputStream(file);
/* 423 */     hSSFWorkbook.write(fileOutputStream);
/* 424 */     fileOutputStream.close();
/* 425 */     return str2;
/*     */   }
/*     */   
/*     */   private static void initStyle(ExcelFile paramExcelFile, HSSFWorkbook paramHSSFWorkbook) {
/* 429 */     styleht = new Hashtable<>();
/* 430 */     HSSFCellStyle hSSFCellStyle = null;
/* 431 */     HSSFFont hSSFFont = null;
/*     */     
/* 433 */     while (paramExcelFile.nextStyle()) {
/* 434 */       String str = paramExcelFile.getStyleName();
/* 435 */       ExcelStyle excelStyle = paramExcelFile.getStyleValue();
/*     */       
/* 437 */       if (excelStyle != null) {
/* 438 */         hSSFCellStyle = paramHSSFWorkbook.createCellStyle();
/* 439 */         hSSFFont = paramHSSFWorkbook.createFont();
/*     */         
/* 441 */         if (excelStyle.getGroundcolor() != 9) {
/* 442 */           hSSFCellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
/* 443 */           hSSFCellStyle.setFillForegroundColor(excelStyle.getGroundcolor());
/*     */         } 
/* 445 */         hSSFCellStyle.setRotation(excelStyle.getScale());
/* 446 */         if (excelStyle.getAlign() != 10)
/* 447 */           hSSFCellStyle.setAlignment(HorizontalAlignment.forInt(excelStyle.getAlign())); 
/* 448 */         if (excelStyle.getDataformart() != 0)
/* 449 */           hSSFCellStyle.setDataFormat(excelStyle.getDataformart()); 
/* 450 */         hSSFCellStyle.setVerticalAlignment(VerticalAlignment.forInt(excelStyle.getValign()));
/*     */         
/* 452 */         hSSFFont.setColor(excelStyle.getFontcolor());
/* 453 */         hSSFFont.setBold((excelStyle.getFontbold() == 700));
/* 454 */         hSSFFont.setFontHeightInPoints(excelStyle.getFontheight());
/*     */         
/* 456 */         hSSFCellStyle.setFont(hSSFFont);
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */         
/* 465 */         styleht.put(str, hSSFCellStyle);
/*     */       } 
/*     */     } 
/*     */   }
/*     */   
/*     */   private static HSSFCellStyle getStyle(String paramString) {
/* 471 */     return (HSSFCellStyle)styleht.get(paramString);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/wscheck/SystemUpdateInfo.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */