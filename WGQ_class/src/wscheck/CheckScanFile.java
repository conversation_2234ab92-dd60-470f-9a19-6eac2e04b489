/*     */ package wscheck;
/*     */ 
/*     */ import java.io.File;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Date;
/*     */ import java.util.List;
/*     */ import org.apache.commons.logging.Log;
/*     */ import org.apache.commons.logging.LogFactory;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.TimeUtil;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CheckScanFile
/*     */ {
/*  18 */   private static final Log log = LogFactory.getLog(CheckScanFile.class);
/*     */   
/*     */   public static final String jsptype = ".jsp";
/*     */   public static final String classtype = ".class";
/*  22 */   private static String checkmd5 = "";
/*  23 */   private static long checksource = 0L;
/*  24 */   private static String classnocheck = "&nbsp;&nbsp;当前系统存在class文件未通过校验,请与管理员联系或者与泛微人员联系！&nbsp;&nbsp;";
/*     */   
/*     */   private static boolean isaddcreatedate = false;
/*     */   
/*     */   private static boolean isaddcreateversion = false;
/*     */   private static boolean isaddcreatetest1 = false;
/*     */   private static boolean isaddcreateweaver1 = false;
/*     */   
/*     */   public void setFileMap(boolean paramBoolean, String paramString1, String paramString2) {
/*  33 */     if (checkmd5.equals("") || !checkmd5.equals("1")) {
/*  34 */       if (checksource == 0L || checksource != -1L) {
/*  35 */         checksource = System.currentTimeMillis();
/*  36 */         checkmd5 = getMD5(checksource + "");
/*  37 */         File file = new File(paramString1);
/*  38 */         setFileMap(paramBoolean, file, paramString2, ".jsp", ".class");
/*     */       } else {
/*  40 */         checkmd5 = "1";
/*  41 */         checksource = -1L;
/*     */       } 
/*     */     } else {
/*  44 */       checkmd5 = "1";
/*  45 */       checksource = -1L;
/*     */     } 
/*     */   }
/*     */   
/*     */   private void setFileMap(boolean paramBoolean, File paramFile, String paramString1, String paramString2, String paramString3) {
/*  50 */     String str1 = TimeUtil.getCurrentDateString();
/*  51 */     String str2 = TimeUtil.getCurrentTimeString().substring(11);
/*     */     
/*  53 */     File[] arrayOfFile = paramFile.listFiles();
/*  54 */     for (byte b = 0; arrayOfFile != null && b < arrayOfFile.length; b++) {
/*  55 */       String str3 = arrayOfFile[b].getPath();
/*  56 */       String str4 = str3.substring(paramString1.length());
/*     */       
/*  58 */       if (str4.indexOf("" + File.separatorChar) == -1 || str4.indexOf("" + File.separatorChar) > 0) {
/*  59 */         str4 = "" + File.separatorChar + str4;
/*     */       }
/*     */       
/*  62 */       str4 = str4.replaceAll("\\\\", "/");
/*  63 */       String str5 = MD5Coder.stringMD5(str4);
/*     */ 
/*     */ 
/*     */       
/*  67 */       if (arrayOfFile[b].isFile() && (
/*  68 */         str3.endsWith(paramString2) || str3.endsWith(paramString3))) {
/*     */         
/*  70 */         String str6 = getMD5(arrayOfFile[b]);
/*  71 */         String str7 = PageMD5Map.get(str5);
/*  72 */         String str8 = PageErrorMap.get(str5);
/*     */         
/*  74 */         if (str8.equals(""))
/*     */         {
/*     */ 
/*     */ 
/*     */           
/*  79 */           if (!str6.equals("")) {
/*  80 */             if (!str6.equals(str7)) {
/*  81 */               if (str7.equals("")) {
/*  82 */                 if (str8.equals("") && paramBoolean) {
/*  83 */                   PageMD5Map.set(str5, str6);
/*  84 */                   writeFile(SecurityHelper.encrypt("weaververify", str5 + "=" + str6));
/*     */                   
/*  86 */                   if (str4.indexOf("workflow") > 0)
/*     */                   {
/*  88 */                     if (!isaddcreatetest1) {
/*     */ 
/*     */                       
/*  91 */                       writeFile(SecurityHelper.encrypt("weaververify", MD5Coder.stringMD5("createversion") + "=" + MD5Coder.stringMD5("" + b)));
/*  92 */                       isaddcreatetest1 = true;
/*     */                     } 
/*     */                   }
/*  95 */                   if (str4.indexOf("page") > 0)
/*     */                   {
/*  97 */                     if (!isaddcreateweaver1) {
/*     */ 
/*     */                       
/* 100 */                       writeFile(SecurityHelper.encrypt("weaververify", MD5Coder.stringMD5("weaver") + "=" + MD5Coder.stringMD5("weaver" + b)));
/* 101 */                       isaddcreateweaver1 = true;
/*     */                     } 
/*     */                   }
/* 104 */                   if (str4.indexOf("homepage") > 0)
/*     */                   {
/*     */                     
/* 107 */                     if (!isaddcreatedate) {
/*     */ 
/*     */                       
/* 110 */                       writeFile(SecurityHelper.encrypt("weaververify", MD5Coder.stringMD5("createdate") + "=" + MD5Coder.stringMD5(str1 + " " + str2)));
/* 111 */                       isaddcreatedate = true;
/*     */                     } 
/*     */                   }
/* 114 */                   if (str4.indexOf("page") > 0)
/*     */                   {
/* 116 */                     if (!isaddcreateversion) {
/*     */ 
/*     */                       
/* 119 */                       writeFile(SecurityHelper.encrypt("weaververify", MD5Coder.stringMD5("test") + "=" + MD5Coder.stringMD5("test" + b)));
/* 120 */                       isaddcreateversion = true;
/*     */                     } 
/*     */                   }
/* 123 */                   PageErrorMap.remove(str5);
/* 124 */                   PageErrorMap.delete(str3);
/*     */                 }
/*     */                 else {
/*     */                   
/* 128 */                   PageErrorMap.set(str5, checkmd5);
/* 129 */                   if (PageErrorMap.getList().indexOf(str3) < 0)
/*     */                   {
/* 131 */                     PageErrorMap.add(str3);
/*     */                   
/*     */                   }
/*     */                 }
/*     */               
/*     */               }
/* 137 */               else if (str8.equals("")) {
/*     */                 
/* 139 */                 PageErrorMap.set(str5, checkmd5);
/* 140 */                 if (PageErrorMap.getList().indexOf(str3) < 0)
/*     */                 {
/* 142 */                   PageErrorMap.add(str3);
/*     */ 
/*     */                 
/*     */                 }
/*     */ 
/*     */               
/*     */               }
/*     */ 
/*     */             
/*     */             }
/*     */           
/*     */           }
/*     */           else {
/*     */ 
/*     */             
/* 157 */             PageErrorMap.set(str5, checkmd5);
/*     */             
/* 159 */             log.error(str4 + "非法文件");
/*     */           } 
/*     */         }
/*     */       } 
/*     */       
/* 164 */       if (arrayOfFile[b].isDirectory() && 
/* 165 */         str4.indexOf("/WEB-INF") != 0 && str4.indexOf("/wscheck") <= 0 && str4.indexOf("/keygenerator") != 0 && str4.indexOf("/updatetemp") != 0 && str4
/* 166 */         .indexOf("/src") != 0 && str4
/* 167 */         .indexOf("/data") != 0 && str4
/* 168 */         .indexOf("/sqlupgrade") != 0 && str4
/* 169 */         .indexOf("/jsp/") != 0 && str4.indexOf("_ubak") <= 0)
/*     */       {
/*     */ 
/*     */         
/* 173 */         setFileMap(paramBoolean, arrayOfFile[b], paramString1, paramString2, paramString3);
/*     */       }
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private void getCodeAdmins() {
/* 183 */     ArrayList<String> arrayList = new ArrayList();
/* 184 */     RecordSet recordSet = new RecordSet();
/* 185 */     String str = "select m.resourceid \t  from (SELECT roleid, rightdetail, rolelevel \t          from SystemRightDetail, SystemRightRoles \t         where SystemRightDetail.rightid = SystemRightRoles.rightid \t           and SystemRightDetail.Rightdetail = 'code:Administrator') r, \t       HrmRoleMembers m \t where r.roleid = m.roleid";
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 192 */     recordSet.executeSql(str);
/* 193 */     while (recordSet.next()) {
/*     */       
/* 195 */       String str1 = recordSet.getString(1);
/* 196 */       if (arrayList.indexOf(str1) == -1)
/*     */       {
/* 198 */         arrayList.add(str1);
/*     */       }
/*     */     } 
/* 201 */     arrayList.add("1");
/* 202 */     PageErrorMap.setAdminlist(arrayList);
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getMD5(File paramFile) {
/* 212 */     String str = "";
/*     */     try {
/* 214 */       str = MD5Coder.fileMD5(paramFile);
/* 215 */     } catch (Exception exception) {
/* 216 */       exception.printStackTrace();
/* 217 */       str = "";
/*     */     } 
/*     */     
/* 220 */     return str;
/*     */   }
/*     */   
/*     */   private String getMD5(String paramString) {
/* 224 */     return MD5Coder.stringMD5(paramString);
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   private void writeFile(String paramString) {
/* 230 */     String str1 = GCONST.getRootPath();
/* 231 */     String str2 = "data" + File.separatorChar + "key" + File.separatorChar + "ecology.key";
/* 232 */     CommonUtil.fileAppend(str1 + str2, paramString);
/*     */   }
/*     */ 
/*     */   
/*     */   public void execute() {
/* 237 */     boolean bool = false;
/* 238 */     boolean bool1 = CheckKey.verifykey();
/*     */     
/* 240 */     if (!bool1) {
/*     */       return;
/*     */     }
/*     */     
/* 244 */     String str1 = GCONST.getRootPath();
/* 245 */     String str2 = "data" + File.separatorChar + "key" + File.separatorChar + "ecology.key";
/* 246 */     String str3 = str1 + str2;
/* 247 */     File file = new File(str3);
/* 248 */     if (file.exists()) {
/*     */       
/* 250 */       ArrayList arrayList = CommonUtil.getLineList(file);
/* 251 */       bool = (arrayList.size() == 5) ? true : false;
/*     */     } 
/*     */     
/* 254 */     if (bool)
/*     */     {
/* 256 */       CommonUtil.rewritefile(str3, "");
/*     */     }
/* 258 */     setFileMap(bool, GCONST.getRootPath(), GCONST.getRootPath());
/* 259 */     getCodeAdmins();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean hasOldModifyFile() {
/* 267 */     if (PageErrorMap.size() > 0) {
/*     */       
/* 269 */       List<String> list = PageErrorMap.getList();
/* 270 */       if (null != list && list.size() > 0)
/*     */       {
/* 272 */         for (byte b = 0; b < list.size(); b++) {
/*     */           
/* 274 */           String str = Util.null2String(list.get(b));
/* 275 */           File file = new File(str);
/* 276 */           Date date = new Date(file.lastModified());
/* 277 */           if (TimeUtil.getCurrentDateString().compareTo(TimeUtil.getDateString(date)) > 0)
/*     */           {
/* 279 */             return true;
/*     */           }
/*     */         } 
/*     */       }
/*     */     } 
/*     */     
/* 285 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static boolean hasOldModifyMoreThan30DaysFile() {
/* 293 */     if (PageErrorMap.size() > 0) {
/*     */       
/* 295 */       List<String> list = PageErrorMap.getList();
/* 296 */       if (null != list && list.size() > 0)
/*     */       {
/* 298 */         for (byte b = 0; b < list.size(); b++) {
/*     */           
/* 300 */           String str = Util.null2String(list.get(b));
/* 301 */           File file = new File(str);
/* 302 */           Date date = new Date(file.lastModified());
/* 303 */           if (TimeUtil.dateAdd(TimeUtil.getCurrentDateString(), -30).compareTo(TimeUtil.getDateString(date)) > 0)
/*     */           {
/* 305 */             return true;
/*     */           }
/*     */         } 
/*     */       }
/*     */     } 
/*     */     
/* 311 */     return false;
/*     */   }
/*     */   public static void main(String[] paramArrayOfString) {
/* 314 */     File file = new File("E:\\workspace\\ecology_hc\\ecology\\activex\\TestByReport.jsp");
/* 315 */     Date date = new Date(file.lastModified());
/*     */     
/* 317 */     if (TimeUtil.getCurrentDateString().compareTo(TimeUtil.getDateString(date)) > 0);
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/wscheck/CheckScanFile.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */