/*     */ package wscheck;
/*     */ 
/*     */ import java.io.BufferedReader;
/*     */ import java.io.File;
/*     */ import java.io.FileInputStream;
/*     */ import java.io.IOException;
/*     */ import java.io.InputStreamReader;
/*     */ import java.net.InetAddress;
/*     */ import java.net.NetworkInterface;
/*     */ import java.net.SocketException;
/*     */ import java.net.UnknownHostException;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Enumeration;
/*     */ import java.util.regex.Matcher;
/*     */ import java.util.regex.Pattern;
/*     */ import org.json.JSONObject;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.file.Prop;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.GCONST;
/*     */ import weaver.general.Util;
/*     */ 
/*     */ 
/*     */ 
/*     */ public class CheckSystemConditionUtil
/*     */ {
/*     */   public String getSystemInfo() {
/*  28 */     JSONObject jSONObject = new JSONObject();
/*  29 */     String str = GCONST.getRootPath();
/*     */     
/*     */     try {
/*  32 */       boolean bool = checkSlqupgrdeFileExist();
/*  33 */       jSONObject.put("existsqlfile", bool);
/*  34 */     } catch (Exception exception) {}
/*     */ 
/*     */ 
/*     */     
/*  38 */     ArrayList<String> arrayList = getRealIp();
/*  39 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean checkSystemReadAndWrite() {
/*  48 */     String str = GCONST.getRootPath();
/*  49 */     boolean bool = true;
/*  50 */     if (!dirCanReadAndWrite(str)) {
/*  51 */       bool = false;
/*     */     }
/*  53 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean dirCanReadAndWrite(String paramString) {
/*  62 */     boolean bool = true;
/*     */     try {
/*  64 */       File file1 = new File(paramString);
/*  65 */       if (!file1.canWrite()) {
/*  66 */         bool = false;
/*     */       }
/*  68 */       File file2 = new File(paramString + File.separatorChar + "sqlupgrade" + File.separatorChar + "testtest12.txt");
/*  69 */       if (file2.exists()) {
/*  70 */         file2.delete();
/*     */       }
/*  72 */       if (file2.exists()) {
/*  73 */         bool = false;
/*     */       }
/*  75 */       file2.createNewFile();
/*  76 */       if (!file2.exists()) {
/*  77 */         bool = false;
/*     */       } else {
/*  79 */         file2.delete();
/*     */       } 
/*  81 */     } catch (Exception exception) {
/*  82 */       (new BaseBean()).writeLog(exception.toString());
/*  83 */       bool = false;
/*     */     } 
/*  85 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getLocalHostName() throws UnknownHostException {
/*  94 */     return InetAddress.getLocalHost().getHostName();
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean isWindowsOS() {
/* 102 */     boolean bool = true;
/* 103 */     String str = System.getProperty("os.name");
/* 104 */     if (!str.toLowerCase().startsWith("win")) {
/* 105 */       bool = false;
/*     */     }
/* 107 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean checkSlqupgrdeFileExist() {
/* 115 */     String str = GCONST.getRootPath();
/*     */     
/* 117 */     boolean bool = true;
/*     */     try {
/* 119 */       String str1 = str + File.separatorChar + "sqlupgrade" + File.separatorChar;
/* 120 */       RecordSet recordSet = new RecordSet();
/* 121 */       String str2 = recordSet.getDBType();
/* 122 */       if (str2.equalsIgnoreCase("oracle")) {
/* 123 */         str1 = str1 + "Oracle";
/* 124 */       } else if (str2.equalsIgnoreCase("Mysql")) {
/* 125 */         str1 = str1 + "Mysql";
/* 126 */       } else if (str2.equalsIgnoreCase("DM")) {
/* 127 */         str1 = str1 + "DM";
/* 128 */       } else if (str2.equalsIgnoreCase("ST")) {
/* 129 */         str1 = str1 + "ST";
/*     */       }
/* 131 */       else if (str2.equalsIgnoreCase("PG")) {
/* 132 */         str1 = str1 + "PG";
/*     */       }
/* 134 */       else if (str2.equalsIgnoreCase("HG")) {
/* 135 */         str1 = str1 + "PG";
/*     */       }
/* 137 */       else if (str2.equalsIgnoreCase("OG")) {
/* 138 */         str1 = str1 + "PG";
/*     */       } else {
/*     */         
/* 141 */         str1 = str1 + "SQLServer";
/*     */       } 
/* 143 */       File file = new File(str1);
/* 144 */       if (file.exists() && file.isDirectory())
/*     */       {
/*     */         
/* 147 */         if (file.exists() && file.isDirectory()) {
/* 148 */           File[] arrayOfFile = file.listFiles();
/* 149 */           if (arrayOfFile.length > 0) {
/* 150 */             bool = false;
/*     */           }
/*     */         } 
/*     */       }
/* 154 */     } catch (Exception exception) {
/* 155 */       bool = false;
/*     */     } 
/* 157 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static ArrayList<String> getRealIp() {
/* 166 */     String str1 = null;
/* 167 */     String str2 = null;
/* 168 */     ArrayList<String> arrayList = new ArrayList();
/* 169 */     Enumeration<NetworkInterface> enumeration = null;
/*     */     try {
/* 171 */       enumeration = NetworkInterface.getNetworkInterfaces();
/* 172 */     } catch (SocketException socketException) {
/* 173 */       socketException.printStackTrace();
/*     */     } 
/* 175 */     InetAddress inetAddress = null;
/* 176 */     while (enumeration.hasMoreElements()) {
/* 177 */       NetworkInterface networkInterface = enumeration.nextElement();
/* 178 */       Enumeration<InetAddress> enumeration1 = networkInterface.getInetAddresses();
/* 179 */       while (enumeration1.hasMoreElements()) {
/* 180 */         inetAddress = enumeration1.nextElement();
/* 181 */         if (!inetAddress.isSiteLocalAddress() && !inetAddress.isLoopbackAddress() && inetAddress.getHostAddress().indexOf(":") == -1) {
/* 182 */           str2 = inetAddress.getHostAddress();
/* 183 */           if (str2 != null && !"".equals(str2))
/* 184 */             arrayList.add(str2);  continue;
/*     */         } 
/* 186 */         if (inetAddress.isSiteLocalAddress() && !inetAddress.isLoopbackAddress() && inetAddress.getHostAddress().indexOf(":") == -1) {
/* 187 */           str1 = inetAddress.getHostAddress();
/* 188 */           arrayList.add(str1);
/*     */         } 
/*     */       } 
/*     */     } 
/* 192 */     return arrayList;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getLinuxLocalIp() {
/* 201 */     String str = "";
/*     */     try {
/* 203 */       for (Enumeration<NetworkInterface> enumeration = NetworkInterface.getNetworkInterfaces(); enumeration.hasMoreElements(); ) {
/* 204 */         NetworkInterface networkInterface = enumeration.nextElement();
/* 205 */         String str1 = networkInterface.getName();
/* 206 */         if (!str1.contains("docker") && !str1.contains("lo")) {
/* 207 */           for (Enumeration<InetAddress> enumeration1 = networkInterface.getInetAddresses(); enumeration1.hasMoreElements(); ) {
/* 208 */             InetAddress inetAddress = enumeration1.nextElement();
/* 209 */             if (!inetAddress.isLoopbackAddress()) {
/* 210 */               String str2 = inetAddress.getHostAddress().toString();
/* 211 */               if (!str2.contains("::") && !str2.contains("0:0:") && !str2.contains("fe80")) {
/* 212 */                 str = str2;
/* 213 */                 System.out.println(str2);
/*     */               } 
/*     */             } 
/*     */           } 
/*     */         }
/*     */       } 
/* 219 */     } catch (SocketException socketException) {
/* 220 */       (new BaseBean()).writeLog("获取ip地址异常");
/* 221 */       str = "127.0.0.1";
/* 222 */       socketException.printStackTrace();
/*     */     } 
/*     */     
/* 225 */     return str;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public int parseWebXMl() {
/* 233 */     byte b = 0;
/*     */     
/*     */     try {
/* 236 */       File file = new File(GCONST.getRootPath() + File.separatorChar + "WEB-INF" + File.separatorChar + "web.xml");
/* 237 */       if (file.exists()) {
/* 238 */         StringBuffer stringBuffer = readFile(file);
/* 239 */         String str = stringBuffer.toString();
/* 240 */         Pattern pattern = Pattern.compile("(?i)(<filter>)([\\s\\S]*?)(</filter>)");
/* 241 */         Matcher matcher = pattern.matcher(str);
/* 242 */         while (matcher.find()) {
/* 243 */           String str1 = matcher.group(2);
/* 244 */           if (str1.toUpperCase().indexOf("CAS") > -1 && (
/* 245 */             str1.matches("(?i)[\\s\\S]*?<filter-name>[\\s\\S]*?cas[\\s\\S]*?</filter-name>[\\s\\S]*?") || str1.matches("(?i)[\\s\\S]*?<filter-class>[\\s\\S]*?cas[\\s\\S]*?</filter-class>[\\s\\S]*?")) && 
/* 246 */             str1.matches("(?i)[\\s\\S]*?<param-name>.*?exclude.*?</param-name>[\\s\\S]*?")) {
/* 247 */             (new BaseBean()).writeLog("符合条件的配置：" + str1);
/* 248 */             String str2 = str1;
/* 249 */             b = 1;
/* 250 */             if (!str2.contains("/login/Upgrade.jsp") || !str2.contains("/login/UpgradeAdminLogin.jsp") || !str2.contains("/login/upgradeCheck.jsp") || 
/* 251 */               !str2.contains("/login/UpgradeMessage.jsp") || !str2.contains("/login/continueExcute.jsp") || !str2.contains("/clusterupgrade/clusterUpgrade.jsp") || 
/* 252 */               !str2.contains("/join/UpgradeInter4Monitor.jsp")) {
/* 253 */               b = 2;
/*     */               
/*     */               continue;
/*     */             } 
/*     */             
/*     */             break;
/*     */           } 
/*     */         } 
/*     */       } 
/* 262 */     } catch (Exception exception) {
/* 263 */       b = 2;
/* 264 */       (new BaseBean()).writeLog("web.xml文件读取错误" + exception.toString());
/* 265 */       exception.printStackTrace();
/*     */     } 
/* 267 */     return b;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getClusterNodeInfo() {
/* 275 */     Prop prop = Prop.getInstance();
/* 276 */     String str = Util.null2String(Prop.getPropValue(GCONST.getConfigFile(), "MainControlIP"));
/* 277 */     if ("".equalsIgnoreCase(str)) {
/* 278 */       return "系统未配置集群";
/*     */     }
/* 280 */     ArrayList<String> arrayList = getRealIp();
/* 281 */     if (arrayList.contains(str)) {
/* 282 */       return "本系统为集群主节点";
/*     */     }
/* 284 */     return "<span style=\"color:red\">当前系统为集群子节点，不跑脚本</span>";
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean checkWebappContextListener() {
/* 294 */     boolean bool = false;
/* 295 */     File file = new File(GCONST.getRootPath() + "WEB-INF" + File.separatorChar + "web.xml");
/* 296 */     if (!file.exists()) {
/* 297 */       return bool;
/*     */     }
/* 299 */     StringBuffer stringBuffer = readFile(file);
/* 300 */     String str = stringBuffer.toString();
/* 301 */     str = str.replaceAll("<!--[\\s\\S]*?-->", "").replaceAll("//.*?", "");
/*     */     
/* 303 */     if (str.indexOf("com.converter.system.WebappContextListener") > -1) {
/* 304 */       (new BaseBean()).writeLog("验证失败");
/*     */     } else {
/* 306 */       bool = true;
/*     */     } 
/* 308 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean getUpgradeStatus() {
/* 318 */     BaseBean baseBean = new BaseBean();
/* 319 */     String str = Util.null2String(baseBean.getPropValue("Upgrade", "STATUS"));
/* 320 */     if ("".equals(str)) {
/* 321 */       return false;
/*     */     }
/* 323 */     if ("0".equals(str)) {
/* 324 */       return true;
/*     */     }
/* 326 */     return false;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDataBaseIp() {
/* 336 */     RecordSet recordSet = new RecordSet();
/*     */     
/* 338 */     String str1 = "未知";
/* 339 */     Prop prop = Prop.getInstance();
/* 340 */     String str2 = Util.null2String(Prop.getPropValue(GCONST.getConfigFile(), "ecology.url"));
/* 341 */     Pattern pattern = Pattern.compile("(\\d{1,3}\\.)(\\d{1,3}\\.\\d{1,3})(\\.\\d{1,3})");
/* 342 */     Matcher matcher = pattern.matcher(str2);
/* 343 */     if (matcher.find()) {
/* 344 */       str1 = matcher.group(1) + "**.**" + matcher.group(3);
/*     */     }
/* 346 */     return str1;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean checkDBCharset() {
/* 354 */     BaseBean baseBean = new BaseBean();
/* 355 */     String str1 = Util.null2String(baseBean.getPropValue("upgradesetting", "checkdbcharset"));
/* 356 */     if (!"".equals(str1) && !"-1".equals(str1) && !"1".equals(str1)) {
/* 357 */       return true;
/*     */     }
/*     */     
/* 360 */     boolean bool = true;
/* 361 */     RecordSet recordSet = new RecordSet();
/* 362 */     String str2 = "";
/* 363 */     String str3 = "";
/*     */     
/* 365 */     if (!recordSet.getDBType().equals("dm"))
/*     */     {
/* 367 */       if (!recordSet.getDBType().equals("st"))
/*     */       {
/* 369 */         if (recordSet.getDBType().equals("oracle")) {
/* 370 */           str2 = "select value from nls_database_parameters where parameter='NLS_CHARACTERSET'";
/* 371 */           recordSet.executeSql(str2);
/* 372 */           if (recordSet.next()) {
/* 373 */             str3 = recordSet.getString("value");
/*     */           }
/* 375 */         } else if (!recordSet.getDBType().equals("mysql")) {
/*     */           
/* 377 */           if (!recordSet.getDBType().equals("db2"))
/*     */           {
/*     */             
/* 380 */             if (!recordSet.getDBType().equals("postgresql")) {
/*     */ 
/*     */ 
/*     */               
/* 384 */               str3 = "UTF-8";
/* 385 */               str2 = "SELECT cast(COLLATIONPROPERTY('Chinese_PRC_Stroke_CI_AI_KS_WS', 'CodePage') as varchar(500)) as value";
/* 386 */               recordSet.executeSql(str2);
/* 387 */               if (recordSet.next()) {
/* 388 */                 str3 = recordSet.getString("value").trim();
/* 389 */                 if ("936".equals(str3))
/* 390 */                   str3 = "GBK"; 
/*     */               } 
/*     */             }  } 
/*     */         }  } 
/*     */     }
/* 395 */     recordSet.executeSql("select  cversion  from license");
/*     */     
/* 397 */     String str4 = recordSet.getString("cversion").trim();
/* 398 */     String str5 = "" + str4.charAt(0);
/* 399 */     Integer integer = Integer.valueOf(str5);
/* 400 */     if (recordSet.next() && integer.compareTo(new Integer(7)) <= 0 && str3.indexOf("GBK") < 0) {
/* 401 */       bool = false;
/*     */     }
/*     */     
/* 404 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public boolean checkResinWebXml() {
/* 412 */     boolean bool = false;
/* 413 */     File file = new File(GCONST.getRootPath() + "WEB-INF" + File.separatorChar + "resin-web.xml");
/* 414 */     if (!file.exists()) {
/* 415 */       return bool;
/*     */     }
/* 417 */     StringBuffer stringBuffer = readFile(file);
/* 418 */     String str = stringBuffer.toString();
/* 419 */     str = str.replaceAll("<!--[\\s\\S]*?-->", "").replaceAll("//.*?", "");
/*     */     
/* 421 */     if (str.indexOf("FileCheckFilter") > -1) {
/* 422 */       (new BaseBean()).writeLog("验证成功");
/* 423 */       bool = true;
/*     */     } else {
/* 425 */       (new BaseBean()).writeLog("验证失败");
/*     */     } 
/* 427 */     return bool;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public StringBuffer readFile(File paramFile) {
/* 437 */     String str = "GBK";
/*     */     
/* 439 */     StringBuffer stringBuffer = new StringBuffer();
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     try {
/* 447 */       InputStreamReader inputStreamReader = new InputStreamReader(new FileInputStream(paramFile), str);
/* 448 */       BufferedReader bufferedReader = new BufferedReader(inputStreamReader);
/* 449 */       String str1 = null;
/* 450 */       while ((str1 = bufferedReader.readLine()) != null) {
/* 451 */         stringBuffer.append(str1).append("\r\n");
/*     */       }
/* 453 */     } catch (IOException iOException) {
/* 454 */       iOException.printStackTrace();
/*     */     } 
/* 456 */     return stringBuffer;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/wscheck/CheckSystemConditionUtil.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */