
#values are "system-env" or "this";
#if value is "this" , using the paoding.dic.home as dicHome if configed!
paoding.dic.home.config-fisrt=this

#dictionary home (directory)
#"classpath:xxx" means dictionary home is in classpath.
#e.g "classpath:dic" means dictionaries are in "classes/dic" directory or any other classpath directory
paoding.dic.home=D:/Workspace/paoding/dic

#seconds for dic modification detection
#paoding.dic.detector.interval=60
