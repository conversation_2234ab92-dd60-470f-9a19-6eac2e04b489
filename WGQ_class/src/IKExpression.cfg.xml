<?xml version="1.0" encoding="UTF-8"?>
<function-configuration>
	<!-- 系统函数默认配置 -->
	<bean class="org.wltea.expression.function.SystemFunctions">
		<function name="CONTAINS" method="contains">
			<parameter-type>java.lang.String</parameter-type>
			<parameter-type>java.lang.String</parameter-type>
		</function>
		<function name="STARTSWITH" method="startsWith">
			<parameter-type>java.lang.String</parameter-type>
			<parameter-type>java.lang.String</parameter-type>
		</function>
		<function name="ENDSWITH" method="endsWith">
			<parameter-type>java.lang.String</parameter-type>
			<parameter-type>java.lang.String</parameter-type>
		</function>
		<function name="CALCDATE" method="calcDate">
			<parameter-type>java.util.Date</parameter-type>
			<parameter-type>int</parameter-type>
			<parameter-type>int</parameter-type>
			<parameter-type>int</parameter-type>
			<parameter-type>int</parameter-type>
			<parameter-type>int</parameter-type>
			<parameter-type>int</parameter-type>
		</function>
		<function name="SYSDATE" method="sysDate" />
		<function name="DAYEQUALS" method="dayEquals">
			<parameter-type>java.util.Date</parameter-type>
			<parameter-type>java.util.Date</parameter-type>
		</function>
	</bean>
	
	<!-- 用户函数配置  请在这里定制您自己的函数-->
	<bean class="weaver.workflow.ruleDesign.WeaverFunctions">
		<function name="NOTCONTAINS" method="doesNotContains">
			<parameter-type>java.lang.String</parameter-type>
			<parameter-type>java.lang.String</parameter-type>
		</function>
		<function name="BELONG" method="belong">
			<parameter-type>java.lang.String</parameter-type>
			<parameter-type>java.lang.String</parameter-type>
		</function>
		<function name="NOTBELONG" method="doesNotBelong">
			<parameter-type>java.lang.String</parameter-type>
			<parameter-type>java.lang.String</parameter-type>
		</function>
		<function name="HRMLEVEL" method="hrmLevel">
			<parameter-type>java.lang.Object</parameter-type>
			<parameter-type>java.lang.String</parameter-type>
			<parameter-type>java.lang.String</parameter-type>
		</function>
		<function name="BROWSERCONTAINS" method="browserContains">
			<parameter-type>java.lang.Object</parameter-type>
			<parameter-type>java.lang.Object</parameter-type>
		</function>
		<function name="NOTBROWSERCONTAINS" method="notBrowserContains">
			<parameter-type>java.lang.Object</parameter-type>
			<parameter-type>java.lang.Object</parameter-type>
		</function>
		<function name="BROWSERINCLUDINGLOWER" method="browserIncludingLower">
			<parameter-type>java.lang.Object</parameter-type>
			<parameter-type>java.lang.Object</parameter-type>
		</function>
		<function name="NOTBROWSERINCLUDINGLOWER" method="notBrowserIncludingLower">
			<parameter-type>java.lang.Object</parameter-type>
			<parameter-type>java.lang.Object</parameter-type>
		</function>
		<function name="INLOCATION" method="inLocation">
			<parameter-type>java.lang.Object</parameter-type>
			<parameter-type>java.lang.Object</parameter-type>
			<parameter-type>java.lang.Object</parameter-type>
			<parameter-type>java.lang.Object</parameter-type>
			<parameter-type>java.lang.Object</parameter-type>
			<parameter-type>java.lang.Object</parameter-type>
			<parameter-type>java.lang.Object</parameter-type>
		</function>
		<function name="NOTINLOCATION" method="notInLocation">
			<parameter-type>java.lang.Object</parameter-type>
			<parameter-type>java.lang.Object</parameter-type>
			<parameter-type>java.lang.Object</parameter-type>
			<parameter-type>java.lang.Object</parameter-type>
			<parameter-type>java.lang.Object</parameter-type>
			<parameter-type>java.lang.Object</parameter-type>
			<parameter-type>java.lang.Object</parameter-type>
		</function>
		<function name="NOTSTARTSWITH" method="notStartsWith">
			<parameter-type>java.lang.String</parameter-type>
			<parameter-type>java.lang.String</parameter-type>
		</function>
		<function name="NOTENDSWITH" method="notEndsWith">
			<parameter-type>java.lang.String</parameter-type>
			<parameter-type>java.lang.String</parameter-type>
		</function>
		<function name="ISEMPTY" method="isEmpty">
			<parameter-type>java.lang.String</parameter-type>
		</function>
		<function name="ISNOTEMPTY" method="isNotEmpty">
			<parameter-type>java.lang.String</parameter-type>
		</function>
	</bean>
<!-- 
	<bean class="org.wltea.expression.test.TestFunctions">
		<constructor-args>
			<constructor-arg type="java.lang.Integer">123</constructor-arg>
			<constructor-arg type="java.lang.String">aa</constructor-arg>
		</constructor-args>
		<function name="问好" method="sayHello">
			<parameter-type>java.lang.String</parameter-type>
		</function>
	</bean>
 -->


</function-configuration>