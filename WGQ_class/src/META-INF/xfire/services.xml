<?xml version="1.0" encoding="UTF-8"?>

<beans xmlns="http://xfire.codehaus.org/config/1.0">  
    <!--增加新的webservice  -->  
    <!-- ESB WebService 触发 -->  
    <!-- 公文交换平台 -->  
    <!-- 统一待办接口 -->  
    <service> 
        <name>DocService</name>  
        <namespace>http://localhost/services/DocService</namespace>  
        <serviceClass>weaver.docs.webservices.DocService</serviceClass>  
        <implementationClass>weaver.docs.webservices.DocServiceImpl</implementationClass>  
        <serviceFactory>org.codehaus.xfire.annotations.AnnotationServiceFactory</serviceFactory> 
    </service>
    <service> 
        <name>HrmService</name>  
        <namespace>http://localhost/services/HrmService</namespace>  
        <serviceClass>weaver.hrm.webservice.HrmService</serviceClass>  
        <implementationClass>weaver.hrm.webservice.HrmServiceImpl</implementationClass>  
        <serviceFactory>org.codehaus.xfire.annotations.AnnotationServiceFactory</serviceFactory> 
    </service>
    <service> 
        <name>HrmServiceCust</name>  
        <namespace>http://localhost/services/HrmServiceCust</namespace>  
        <serviceClass>chinasws.webservices.HrmServiceCust</serviceClass>  
        <implementationClass>chinasws.webservices.HrmServiceCustImpl</implementationClass>  
        <serviceFactory>org.codehaus.xfire.annotations.AnnotationServiceFactory</serviceFactory> 
    </service>
    <service> 
        <name>ServiceMessageCustom</name>  
        <namespace>http://localhost/services/ServiceMessageCustom</namespace>  
        <serviceClass>com.cloudstore.dev.api.service.ServiceMessageCustom</serviceClass>  
        <implementationClass>com.cloudstore.dev.api.service.ServiceMessageCustomImpl</implementationClass> 
    </service>
    <service> 
        <name>WorkflowService</name>  
        <namespace>webservices.services.weaver.com.cn</namespace>  
        <serviceClass>weaver.workflow.webservices.WorkflowService</serviceClass>  
        <implementationClass>weaver.workflow.webservices.WorkflowServiceImpl</implementationClass>  
        <serviceFactory>org.codehaus.xfire.annotations.AnnotationServiceFactory</serviceFactory> 
    </service>
    <service> 
        <name>ModeDateService</name>  
        <namespace>http://localhost/services/ModeDateService</namespace>  
        <serviceClass>weaver.formmode.webservices.ModeDateService</serviceClass>  
        <implementationClass>weaver.formmode.webservices.ModeDataServiceImpl</implementationClass> 
    </service>
    <service> 
        <name>WorkflowServiceXml</name>  
        <serviceClass>weaver.workflow.webservices.WorkflowServiceXml</serviceClass>  
        <implementationClass>weaver.workflow.webservices.WorkflowServiceImplXml</implementationClass> 
    </service>
    <service> 
        <name>ProjectService</name>  
        <namespace>webservices.prj.weaver.com.cn</namespace>  
        <serviceClass>weaver.pmp.ws.ProjectWs</serviceClass>  
        <implementationClass>weaver.pmp.ws.ProjectWs</implementationClass> 
    </service>
    <service> 
        <name>LoginLogService</name>  
        <namespace>webservices.services.weaver.com.cn</namespace>  
        <serviceClass>weaver.login.webservices.LoginLogService</serviceClass>  
        <implementationClass>weaver.login.webservices.LoginLogServiceImpl</implementationClass> 
    </service>
    <service> 
        <name>ESBService</name>  
        <namespace>webservices.esb.weaver.com.cn</namespace>  
        <serviceClass>com.api.integration.esb.trigger.WsTrigger</serviceClass>  
        <implementationClass>com.api.integration.esb.trigger.WsTriggerImpl</implementationClass>  
        <serviceFactory>org.codehaus.xfire.annotations.AnnotationServiceFactory</serviceFactory> 
    </service>
    <service> 
        <name>OdocExchangeWebService</name>  
        <namespace>http://localhost/services/OdocExchangeWebService</namespace>  
        <serviceClass>com.engine.odocExchange.ws.OdocExchangeWebService</serviceClass>  
        <implementationClass>com.engine.odocExchange.ws.OdocExchangeWebServiceImpl</implementationClass>  
        <serviceFactory>org.codehaus.xfire.annotations.AnnotationServiceFactory</serviceFactory> 
    </service>
    <service> 
        <name>OfsTodoDataWebService</name>  
        <namespace>webservices.ofs.weaver.com.cn</namespace>  
        <serviceClass>weaver.ofs.webservices.OfsTodoDataWebService</serviceClass>  
        <implementationClass>weaver.ofs.webservices.OfsTodoDataWebServiceImpl</implementationClass> 
    </service>
    <service> 
        <name>WorkPlanService</name>  
        <namespace>webservices.workplan.weaver.com.cn</namespace>  
        <serviceClass>weaver.WorkPlan.webservices.WorkplanService</serviceClass>  
        <implementationClass>weaver.WorkPlan.webservices.WorkplanServiceImpl</implementationClass> 
    </service>
    <service> 
        <name>CrmService</name>  
        <namespace>http://localhost/services/CrmService</namespace>  
        <serviceClass>com.engine.crm.interfaces.webservice.CrmService</serviceClass>  
        <implementationClass>com.engine.crm.interfaces.webservice.CrmServiceImpl</implementationClass>  
        <serviceFactory>org.codehaus.xfire.annotations.AnnotationServiceFactory</serviceFactory> 
    </service>
    <service> 
        <name>BlogService</name>  
        <namespace>webservices.blog.weaver.com.cn</namespace>  
        <serviceClass>weaver.blog.webservices.BlogServiceNew</serviceClass>  
        <implementationClass>weaver.blog.webservices.BlogServiceImplSecNew</implementationClass> 
    </service>
    <service> 
        <name>MeetingService</name>  
        <namespace>webservices.meeting.weaver.com.cn</namespace>  
        <serviceClass>weaver.meeting.webservices.MeetingService</serviceClass>  
        <implementationClass>weaver.meeting.webservices.MeetingServiceImpl</implementationClass> 
    </service>
    <service> 
        <name>ESBServiceV2</name>  
        <namespace>webservicesv2.esb.weaver.com.cn</namespace>  
        <serviceClass>com.weaver.esb.server.api.EsbWebService</serviceClass>  
        <implementationClass>com.weaver.esb.server.api.EsbWebServiceImpl</implementationClass>  
        <serviceFactory>org.codehaus.xfire.annotations.AnnotationServiceFactory</serviceFactory> 
    </service>
</beans>
