/*     */ package chinasws.webservices;
/*     */ 
/*     */ import com.engine.common.service.impl.HrmCommonServiceImpl;
/*     */ import java.io.StringReader;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import org.jdom.Document;
/*     */ import org.jdom.Element;
/*     */ import org.jdom.input.SAXBuilder;
/*     */ import weaver.common.StringUtil;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.hrm.webservice.JobTitleBean;
/*     */ import weaver.hrm.webservice.OrgXmlBean;
/*     */ import weaver.toolbox.db.recordset.ExecuteUtil;
/*     */ import weaver.toolbox.db.recordset.QueryUtil;
/*     */ import weaver.toolbox.json.JSONArray;
/*     */ import weaver.toolbox.json.JSONObject;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ParseXmlCust
/*     */   extends BaseBean
/*     */ {
/*  32 */   private HashMap h_orgInfo = new HashMap<>();
/*  33 */   private HashMap h_orgInfo_add = new HashMap<>();
/*  34 */   private HashMap h_orgInfo_update = new HashMap<>();
/*  35 */   private HashMap h_orgInfo_delete = new HashMap<>();
/*  36 */   private List h_delOrg = new ArrayList();
/*  37 */   private List h_addOrg = new ArrayList();
/*  38 */   private List h_updateOrg = new ArrayList();
/*     */   
/*     */   public HashMap getH_orgInfo() {
/*  41 */     return this.h_orgInfo;
/*     */   }
/*     */   
/*     */   public void setH_orgInfo(HashMap info) {
/*  45 */     this.h_orgInfo = info;
/*     */   }
/*     */   
/*     */   public HashMap getH_orgInfo_add() {
/*  49 */     return this.h_orgInfo_add;
/*     */   }
/*     */   
/*     */   public void setH_orgInfo_add(HashMap info) {
/*  53 */     this.h_orgInfo_add = info;
/*     */   }
/*     */   
/*     */   public HashMap getH_orgInfo_update() {
/*  57 */     return this.h_orgInfo_update;
/*     */   }
/*     */   
/*     */   public void setH_orgInfo_update(HashMap info) {
/*  61 */     this.h_orgInfo_update = info;
/*     */   }
/*     */   
/*     */   public List getH_delOrg() {
/*  65 */     return this.h_delOrg;
/*     */   }
/*     */   
/*     */   public void setH_delOrg(List org) {
/*  69 */     this.h_delOrg = org;
/*     */   }
/*     */   
/*     */   public List getH_addOrg() {
/*  73 */     return this.h_addOrg;
/*     */   }
/*     */   
/*     */   public void setH_addOrg(List org) {
/*  77 */     this.h_addOrg = org;
/*     */   }
/*     */   
/*     */   public List getH_updateOrg() {
/*  81 */     return this.h_updateOrg;
/*     */   }
/*     */   
/*     */   public void setH_updateOrg(List org) {
/*  85 */     this.h_updateOrg = org;
/*     */   }
/*     */   
/*     */   public HashMap getH_orgInfo_delete() {
/*  89 */     return this.h_orgInfo_delete;
/*     */   }
/*     */ 
/*     */   
/*  93 */   Map<String, String> hrmAllCacheMap = new HashMap<>();
/*     */   
/*  95 */   Map<String, String> hrmLeaveCacheMap = new HashMap<>();
/*     */   
/*     */   public void setH_orgInfo_delete(HashMap h_orgInfo_delete) {
/*  98 */     this.h_orgInfo_delete = h_orgInfo_delete;
/*     */   }
/*     */   
/*     */   public String trimNull(Object o) {
/* 102 */     if (o != null) {
/* 103 */       return Util.toHtmlForWorkflow((String)o);
/*     */     }
/*     */     
/* 106 */     return "";
/*     */   }
/*     */   
/*     */   public String getTrimNull(Object o) {
/* 110 */     if (o != null) {
/* 111 */       return Util.toHtmlForWorkflow((String)o);
/*     */     }
/*     */     
/* 114 */     return null;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void parseOrg(String xmlStr) throws Exception {
/* 126 */     xmlStr = StringUtil.vString(xmlStr);
/* 127 */     Document doc = new Document();
/*     */     try {
/* 129 */       writeLog("组织架构xml字符串:" + xmlStr);
/* 130 */       SAXBuilder builder = new SAXBuilder();
/* 131 */       doc = builder.build(new StringReader(xmlStr));
/* 132 */     } catch (Exception e) {
/* 133 */       writeLog("装载分部,部门XML字符串时发生异常:" + e);
/*     */     } 
/*     */     
/* 136 */     Element rootElement = doc.getRootElement();
/* 137 */     Element orglistElement = rootElement.getChild("orglist");
/*     */     
/* 139 */     if (orglistElement != null) {
/* 140 */       List<Element> orgList = orglistElement.getChildren("org");
/* 141 */       if (orgList != null) {
/* 142 */         for (int i = 0; i < orgList.size(); i++) {
/* 143 */           Element orgElement = orgList.get(i);
/* 144 */           processOrg("", orgElement);
/*     */         } 
/*     */       }
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   protected void processOrg(String parentId, Element orgElement) throws Exception {
/* 159 */     if (orgElement == null) {
/*     */       return;
/*     */     }
/*     */ 
/*     */     
/* 164 */     String action = trimNull(orgElement.getAttributeValue("action"));
/*     */     
/* 166 */     String code = trimNull(orgElement.getChildText("code"));
/*     */     
/* 168 */     String shortname = trimNull(orgElement.getChildText("shortname"));
/*     */     
/* 170 */     String fullname = trimNull(orgElement.getChildText("fullname"));
/*     */     
/* 172 */     String org_code = trimNull(orgElement.getChildText("org_code"));
/*     */     
/* 174 */     String parent_code = trimNull(orgElement.getChildText("parent_code"));
/*     */     
/* 176 */     String canceled = trimNull(orgElement.getChildText("canceled"));
/*     */     
/* 178 */     String order = trimNull(orgElement.getChildText("order"));
/*     */     
/* 180 */     if (action.equalsIgnoreCase("add")) {
/* 181 */       this.h_addOrg.add(code);
/* 182 */     } else if (action.equalsIgnoreCase("edit")) {
/* 183 */       this.h_updateOrg.add(code);
/* 184 */     } else if (action.equalsIgnoreCase("1")) {
/* 185 */       this.h_delOrg.add(code);
/*     */     } 
/*     */     
/* 188 */     if (this.h_orgInfo.get(code) == null) {
/* 189 */       OrgXmlBean org = new OrgXmlBean();
/* 190 */       org.setAction(action);
/* 191 */       org.setCode(code);
/* 192 */       org.setShortname(shortname);
/* 193 */       org.setFullname(fullname);
/* 194 */       org.setOrg_code(org_code);
/* 195 */       org.setParent_code(parent_code);
/* 196 */       org.setOrder(order);
/* 197 */       org.setCanceled(canceled);
/* 198 */       this.h_orgInfo.put(code, org);
/*     */     } 
/*     */     
/* 201 */     List<Element> orgList = orgElement.getChildren("org");
/* 202 */     if (orgList != null) {
/* 203 */       for (int i = 0; i < orgList.size(); i++) {
/* 204 */         Element childOrgElement = orgList.get(i);
/* 205 */         processOrg(code, childOrgElement);
/*     */       } 
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void parseJobTitle(String xmlStr) throws Exception {
/* 219 */     xmlStr = StringUtil.vString(xmlStr);
/* 220 */     Document doc = new Document();
/*     */     try {
/* 222 */       writeLog("岗位xml字符串:" + xmlStr);
/* 223 */       SAXBuilder builder = new SAXBuilder();
/* 224 */       doc = builder.build(new StringReader(xmlStr));
/* 225 */     } catch (Exception e) {
/* 226 */       writeLog("装载岗位XML字符串时发生异常:" + e);
/*     */     } 
/*     */     
/* 229 */     Element rootElement = doc.getRootElement();
/* 230 */     Element jobtitlelistElement = rootElement.getChild("jobtitlelist");
/*     */     
/* 232 */     if (jobtitlelistElement != null) {
/* 233 */       List<Element> jobtitleList = jobtitlelistElement.getChildren("jobtitle");
/* 234 */       if (jobtitleList != null) {
/* 235 */         for (int i = 0; i < jobtitleList.size(); i++) {
/* 236 */           Element jobtitleElement = jobtitleList.get(i);
/*     */           
/* 238 */           String action = trimNull(jobtitleElement.getAttributeValue("action"));
/*     */           
/* 240 */           String jobtitlecode = trimNull(jobtitleElement.getChildText("jobtitlecode"));
/*     */           
/* 242 */           String jobtitlename = trimNull(jobtitleElement.getChildText("jobtitlename"));
/*     */           
/* 244 */           String jobtitleremark = trimNull(jobtitleElement.getChildText("jobtitleremark"));
/*     */           
/* 246 */           String jobtitledept = trimNull(jobtitleElement.getChildText("jobtitledept"));
/*     */           
/* 248 */           if (action.equalsIgnoreCase("add")) {
/* 249 */             this.h_addOrg.add(jobtitlecode);
/* 250 */           } else if (action.equalsIgnoreCase("edit")) {
/* 251 */             this.h_updateOrg.add(jobtitlecode);
/* 252 */           } else if (action.equalsIgnoreCase("delete")) {
/* 253 */             this.h_delOrg.add(jobtitlecode);
/*     */           } 
/*     */           
/* 256 */           if (this.h_orgInfo.get(jobtitlecode) == null) {
/* 257 */             JobTitleBean jobxml = new JobTitleBean();
/* 258 */             jobxml.setAction(action);
/* 259 */             jobxml.set_code(jobtitlecode);
/* 260 */             jobxml.set_shortname(jobtitlename);
/* 261 */             jobxml.set_fullname(jobtitleremark);
/* 262 */             jobxml.set_departmentid(jobtitledept);
/* 263 */             this.h_orgInfo.put(jobtitlecode, jobxml);
/*     */           } 
/*     */         } 
/*     */       }
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void parseHrmResource(String xmlStr) throws Exception {
/* 289 */     writeLog("parseHrmResource cust -- ");
/*     */     
/* 291 */     RecordSet rs = new RecordSet();
/* 292 */     String sql = "select swshrguid,id,loginid,workcode,accounttype,status from hrmResource where status<=5 order by id desc";
/* 293 */     rs.execute(sql);
/*     */     
/* 295 */     while (rs.next()) {
/* 296 */       String userId = Util.null2String(rs.getString("id"));
/* 297 */       String swshrguid = Util.null2String(rs.getString("swshrguid"));
/* 298 */       String status = Util.null2String(rs.getString("status"));
/*     */       
/* 300 */       if ("5".equals(status)) {
/* 301 */         this.hrmLeaveCacheMap.put(swshrguid, userId); continue;
/*     */       } 
/* 303 */       if (!swshrguid.equals(""))
/*     */       {
/* 305 */         this.hrmAllCacheMap.put(swshrguid, userId);
/*     */       }
/*     */     } 
/*     */ 
/*     */     
/* 310 */     writeLog("缓存全部人员 hrmAllCacheMap -- " + this.hrmAllCacheMap.size());
/*     */     
/* 312 */     xmlStr = StringUtil.vString(xmlStr);
/* 313 */     Document doc = new Document();
/*     */     try {
/* 315 */       writeLog("人员xml字符串:" + xmlStr);
/* 316 */       SAXBuilder builder = new SAXBuilder();
/* 317 */       doc = builder.build(new StringReader(xmlStr));
/* 318 */     } catch (Exception e) {
/* 319 */       writeLog("装载人员XML字符串时发生异常:" + e);
/*     */     } 
/*     */     
/* 322 */     Element rootElement = doc.getRootElement();
/* 323 */     Element hrmlistElement = rootElement.getChild("hrmlist");
/*     */     
/* 325 */     if (hrmlistElement != null) {
/* 326 */       List<Element> hrmList = hrmlistElement.getChildren("hrm");
/* 327 */       if (hrmList != null) {
/*     */ 
/*     */         
/* 330 */         Map<String, String> hrmMap = new HashMap<>(); int i;
/* 331 */         for (i = 0; i < hrmList.size(); i++) {
/* 332 */           Element hrmElement = hrmList.get(i);
/* 333 */           String swshrguid = trimNull(hrmElement.getChildText("swshrguid"));
/* 334 */           String hrid = swshrguid.split("\\|")[0];
/*     */ 
/*     */           
/* 337 */           if (!hrmMap.containsKey(hrid)) {
/* 338 */             hrmMap.put(hrid, hrid);
/*     */ 
/*     */             
/* 341 */             Map<String, Element> hrmElementMap = new HashMap<>();
/* 342 */             for (int k = 0; k < hrmList.size(); k++) {
/* 343 */               Element curHrmElement = hrmList.get(k);
/* 344 */               String curSwshrguid = trimNull(curHrmElement.getChildText("swshrguid"));
/* 345 */               if (!hrmElementMap.containsKey(curSwshrguid)) {
/* 346 */                 hrmElementMap.put(curSwshrguid, curHrmElement);
/*     */               }
/*     */             } 
/*     */ 
/*     */             
/* 351 */             String qryAllAccountSql = "select id,swshrguid,accounttype from hrmresource where swshrguid like '" + hrid + "|%'";
/* 352 */             JSONArray allAccountArr = QueryUtil.doQuery(qryAllAccountSql, "", "id,swshrguid,accounttype");
/*     */ 
/*     */             
/* 355 */             preprocessData(hrmElementMap, allAccountArr);
/*     */           } 
/*     */         } 
/*     */ 
/*     */         
/* 360 */         for (i = 0; i < hrmList.size(); i++) {
/* 361 */           String str1; Element hrmElement = hrmList.get(i);
/* 362 */           String swshrguid = trimNull(hrmElement.getChildText("swshrguid"));
/* 363 */           String residentplace = trimNull(hrmElement.getChildText("residentplace"));
/* 364 */           String lastname = trimNull(hrmElement.getChildText("lastname"));
/* 365 */           String lastnamePinyin = (new HrmCommonServiceImpl()).generateQuickSearchStr(lastname);
/*     */           
/* 367 */           writeLog("swshrguid ==> " + swshrguid);
/* 368 */           if (swshrguid != null) {
/* 369 */             swshrguid = swshrguid.trim();
/*     */           }
/*     */           
/* 372 */           writeLog("residentplace ==> " + residentplace);
/*     */ 
/*     */ 
/*     */           
/* 376 */           if (!swshrguid.contains("|")) {
/*     */             
/* 378 */             this.h_delOrg.add(swshrguid);
/* 379 */             str1 = "delete";
/*     */           } else {
/* 381 */             if (this.hrmAllCacheMap.containsKey(swshrguid)) {
/* 382 */               writeLog("edit 111");
/* 383 */               str1 = "edit";
/*     */             } else {
/* 385 */               writeLog("add 111");
/* 386 */               str1 = "add";
/*     */             } 
/*     */ 
/*     */             
/* 390 */             if (this.hrmLeaveCacheMap.containsKey(swshrguid)) {
/* 391 */               str1 = "edit";
/*     */             }
/*     */           } 
/*     */           
/* 395 */           writeLog("action ==> " + str1);
/*     */           
/* 397 */           if (this.h_orgInfo.get(swshrguid) == null) {
/* 398 */             HrmResourceVoCust hrmvo = new HrmResourceVoCust();
/*     */             
/* 400 */             hrmvo.setSwshrguid(swshrguid);
/* 401 */             hrmvo.setAccounttype("1".equals(residentplace) ? "0" : "1");
/* 402 */             hrmvo.setSubcompanyid1(getTrimNull(hrmElement.getChildText("subcompany")));
/* 403 */             hrmvo.setDepartmentid(getTrimNull(hrmElement.getChildText("department")));
/* 404 */             hrmvo.setWorkcode(getTrimNull(hrmElement.getChildText("workcode")));
/* 405 */             hrmvo.setLastname(lastname);
/* 406 */             hrmvo.setPinyinlastname(lastnamePinyin);
/* 407 */             hrmvo.setEcology_pinyin_search(lastnamePinyin);
/* 408 */             hrmvo.setLoginid(getTrimNull(hrmElement.getChildText("loginid")));
/* 409 */             hrmvo.setPassword(getTrimNull(hrmElement.getChildText("password")));
/* 410 */             hrmvo.setSeclevel(getTrimNull(hrmElement.getChildText("seclevel")));
/* 411 */             hrmvo.setSex(getTrimNull(hrmElement.getChildText("sex")));
/* 412 */             hrmvo.setJobtitle(getTrimNull(hrmElement.getChildText("jobtitle")));
/* 413 */             hrmvo.setJobactivityid(getTrimNull(hrmElement.getChildText("jobactivityid")));
/* 414 */             hrmvo.setJobgroupid(getTrimNull(hrmElement.getChildText("jobgroupid")));
/* 415 */             hrmvo.setJobcall(getTrimNull(hrmElement.getChildText("jobcall")));
/* 416 */             hrmvo.setJoblevel(getTrimNull(hrmElement.getChildText("joblevel")));
/* 417 */             hrmvo.setJobactivitydesc(getTrimNull(hrmElement.getChildText("jobactivitydesc")));
/* 418 */             hrmvo.setManagerid(getTrimNull(hrmElement.getChildText("managerid")));
/* 419 */             hrmvo.setAssistantid(getTrimNull(hrmElement.getChildText("assistantid")));
/*     */             
/* 421 */             String statusTemp = getTrimNull(hrmElement.getChildText("status"));
/* 422 */             statusTemp = statusTemp.equals("在职") ? "1" : "5";
/* 423 */             hrmvo.setStatus(statusTemp);
/*     */             
/* 425 */             hrmvo.setLocationid(getTrimNull(hrmElement.getChildText("locationid")));
/* 426 */             hrmvo.setWorkroom(getTrimNull(hrmElement.getChildText("workroom")));
/* 427 */             hrmvo.setTelephone(getTrimNull(hrmElement.getChildText("telephone")));
/* 428 */             hrmvo.setMobile(getTrimNull(hrmElement.getChildText("mobile")));
/* 429 */             hrmvo.setMobilecall(getTrimNull(hrmElement.getChildText("mobilecall")));
/* 430 */             hrmvo.setFax(getTrimNull(hrmElement.getChildText("fax")));
/* 431 */             hrmvo.setEmail("");
/* 432 */             hrmvo.setSystemlanguage(getTrimNull(hrmElement.getChildText("systemlanguage")));
/* 433 */             hrmvo.setBirthday(getTrimNull(hrmElement.getChildText("birthday")));
/* 434 */             hrmvo.setFolk(getTrimNull(hrmElement.getChildText("folk")));
/* 435 */             hrmvo.setNativeplace(getTrimNull(hrmElement.getChildText("nativeplace")));
/* 436 */             hrmvo.setResidentplace(getTrimNull(hrmElement.getChildText("residentplace")));
/* 437 */             hrmvo.setCertificatenum(getTrimNull(hrmElement.getChildText("certificatenum")));
/* 438 */             hrmvo.setMaritalstatus(getTrimNull(hrmElement.getChildText("maritalstatus")));
/* 439 */             hrmvo.setPolicy(getTrimNull(hrmElement.getChildText("policy")));
/* 440 */             hrmvo.setBememberdate(getTrimNull(hrmElement.getChildText("bememberdate")));
/* 441 */             hrmvo.setBepartydate(getTrimNull(hrmElement.getChildText("bepartydate")));
/* 442 */             hrmvo.setIslabouunion(getTrimNull(hrmElement.getChildText("islabouunion")));
/* 443 */             hrmvo.setEducationlevel(getTrimNull(hrmElement.getChildText("educationlevel")));
/* 444 */             hrmvo.setDegree(getTrimNull(hrmElement.getChildText("degree")));
/* 445 */             hrmvo.setHealthinfo(getTrimNull(hrmElement.getChildText("healthinfo")));
/* 446 */             hrmvo.setHeight(getTrimNull(hrmElement.getChildText("height")));
/* 447 */             if (hrmElement.getChildText("weight") != null) {
/* 448 */               hrmvo.setWeight(new Integer(Util.getIntValue(getTrimNull(hrmElement.getChildText("weight")), 0)));
/*     */             }
/* 450 */             hrmvo.setHomeaddress(getTrimNull(hrmElement.getChildText("homeaddress")));
/* 451 */             hrmvo.setTempresidentnumber(getTrimNull(hrmElement.getChildText("tempresidentnumber")));
/* 452 */             hrmvo.setDsporder(getTrimNull(hrmElement.getChildText("dsporder")));
/*     */             
/* 454 */             this.h_orgInfo.put(swshrguid, hrmvo);
/*     */             
/* 456 */             writeLog("hrmvo getStatus ==> " + hrmvo.getStatus());
/*     */             
/* 458 */             if (str1.equalsIgnoreCase("add")) {
/* 459 */               this.h_orgInfo_add.put(swshrguid, hrmvo);
/* 460 */             } else if (str1.equalsIgnoreCase("edit")) {
/* 461 */               this.h_orgInfo_update.put(swshrguid, hrmvo);
/*     */             } 
/*     */           } 
/*     */         } 
/*     */         
/* 466 */         rs.execute(sql);
/* 467 */         ArrayList<E> leaveList = getAllLeave(hrmList, rs);
/* 468 */         for (int j = 0; j < leaveList.size(); j++) {
/* 469 */           writeLog("h_delOrg new ==> " + leaveList.get(j).toString());
/* 470 */           this.h_delOrg.add(leaveList.get(j).toString());
/*     */         } 
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   private void preprocessData(Map<String, Element> hrmElementMap, JSONArray allAccountArr) {
/* 478 */     if (allAccountArr.size() == 0) {
/*     */       return;
/*     */     }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/* 491 */     Element mainHrmElement = null;
/* 492 */     for (Element hrmElement : hrmElementMap.values()) {
/* 493 */       if ("1".equals(trimNull(hrmElement.getChildText("residentplace")))) {
/* 494 */         mainHrmElement = hrmElement;
/*     */         break;
/*     */       } 
/*     */     } 
/* 498 */     String hrMainSwshrguid = trimNull(mainHrmElement.getChildText("swshrguid"));
/*     */ 
/*     */     
/* 501 */     JSONObject mainAccount = null;
/* 502 */     for (int i = 0; i < allAccountArr.size(); i++) {
/* 503 */       JSONObject account = allAccountArr.getJSONObject(Integer.valueOf(i));
/* 504 */       if ("0".equals(trimNull(account.getStr("accounttype")))) {
/* 505 */         mainAccount = account;
/*     */         break;
/*     */       } 
/*     */     } 
/* 509 */     String oaMainSwshrguid = trimNull(mainAccount.getStr("swshrguid"));
/* 510 */     String oaMainId = trimNull(mainAccount.getStr("id"));
/*     */ 
/*     */     
/* 513 */     boolean isHrMainSwshrguidExist = false;
/* 514 */     for (int j = 0; j < allAccountArr.size(); j++) {
/* 515 */       JSONObject account = allAccountArr.getJSONObject(Integer.valueOf(j));
/* 516 */       if (hrMainSwshrguid.equals(trimNull(account.getStr("swshrguid")))) {
/* 517 */         isHrMainSwshrguidExist = true;
/*     */         
/*     */         break;
/*     */       } 
/*     */     } 
/* 522 */     if (!isHrMainSwshrguidExist) {
/*     */       
/* 524 */       String updateSql21 = "update hrmresource set outkey = swshrguid, swshrguid = '" + hrMainSwshrguid + "' where id = " + oaMainId;
/* 525 */       writeLog("preprocessData updateSql21 ==> " + updateSql21);
/* 526 */       if (ExecuteUtil.executeSql(updateSql21)) {
/* 527 */         this.hrmAllCacheMap.put(hrMainSwshrguid, oaMainId);
/*     */       }
/*     */     }
/* 530 */     else if (!hrMainSwshrguid.equals(oaMainSwshrguid)) {
/*     */       
/* 532 */       if (hrmElementMap.size() == 1) {
/* 533 */         String updateSql231_1 = "update hrmresource set outkey = swshrguid, swshrguid = '" + hrMainSwshrguid + "' where id = " + oaMainId;
/* 534 */         writeLog("preprocessData updateSql231_1 ==> " + updateSql231_1);
/* 535 */         ExecuteUtil.executeSql(updateSql231_1);
/*     */         
/* 537 */         String hrid = hrMainSwshrguid.split("\\|")[0];
/* 538 */         String updateSql231_2 = "update hrmresource set swshrguid = concat(swshrguid,'#invalid') where swshrguid like '" + hrid + "|%' and id <> " + oaMainId;
/* 539 */         writeLog("preprocessData updateSql231_2 ==> " + updateSql231_2);
/* 540 */         ExecuteUtil.executeSql(updateSql231_2);
/*     */       } 
/*     */ 
/*     */       
/* 544 */       boolean isHrSubSwshrguidOaMain = false;
/* 545 */       for (Element hrmElement : hrmElementMap.values()) {
/* 546 */         if ("0".equals(trimNull(hrmElement.getChildText("residentplace")))) {
/* 547 */           String hrSubSwshrguid = trimNull(hrmElement.getChildText("swshrguid"));
/* 548 */           if (hrSubSwshrguid.equals(oaMainSwshrguid)) {
/* 549 */             isHrSubSwshrguidOaMain = true;
/*     */             
/*     */             break;
/*     */           } 
/*     */         } 
/*     */       } 
/* 555 */       if (isHrSubSwshrguidOaMain) {
/*     */         
/* 557 */         String oaSubId = "";
/* 558 */         for (int k = 0; k < allAccountArr.size(); k++) {
/* 559 */           JSONObject account = allAccountArr.getJSONObject(Integer.valueOf(k));
/* 560 */           if (hrMainSwshrguid.equals(trimNull(account.getStr("swshrguid")))) {
/* 561 */             oaSubId = trimNull(account.getStr("id"));
/*     */             
/*     */             break;
/*     */           } 
/*     */         } 
/*     */         
/* 567 */         String updateSql232_1 = "update hrmresource set outkey = swshrguid, swshrguid = '" + hrMainSwshrguid + "' where id = " + oaMainId;
/* 568 */         writeLog("preprocessData updateSql232_1 ==> " + updateSql232_1);
/* 569 */         ExecuteUtil.executeSql(updateSql232_1);
/*     */         
/* 571 */         String updateSql232_2 = "update hrmresource set outkey = swshrguid, swshrguid = '" + oaMainSwshrguid + "' where id = " + oaSubId;
/* 572 */         writeLog("preprocessData updateSql232_2 ==> " + updateSql232_2);
/* 573 */         ExecuteUtil.executeSql(updateSql232_2);
/*     */       } else {
/*     */         
/* 576 */         String updateSql233_1 = "update hrmresource set outkey = swshrguid, swshrguid = '" + hrMainSwshrguid + "' where id = " + oaMainId;
/* 577 */         writeLog("preprocessData updateSql233_1 ==> " + updateSql233_1);
/* 578 */         ExecuteUtil.executeSql(updateSql233_1);
/*     */ 
/*     */         
/* 581 */         String updateSql233_2 = "update hrmresource set swshrguid = concat(swshrguid,'#invalid') where swshrguid = '" + hrMainSwshrguid + "' and id <> " + oaMainId;
/* 582 */         writeLog("preprocessData updateSql233_2 ==> " + updateSql233_2);
/* 583 */         ExecuteUtil.executeSql(updateSql233_2);
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public ArrayList getAllLeave(List<Element> hrmList, RecordSet hrmRs) {
/* 600 */     ArrayList<String> list = new ArrayList();
/*     */     
/* 602 */     if (hrmList != null) {
/*     */       
/* 604 */       Map<String, String> hridMap = new HashMap<>();
/* 605 */       for (int i = 0; i < hrmList.size(); i++) {
/* 606 */         Element hrmElement = hrmList.get(i);
/* 607 */         String swshrguid = trimNull(hrmElement.getChildText("swshrguid"));
/* 608 */         if (swshrguid.contains("|")) {
/* 609 */           String hrid = swshrguid.split("\\|")[0];
/* 610 */           if (!hridMap.containsKey(hrid)) {
/* 611 */             hridMap.put(hrid, hrid);
/*     */           }
/*     */         } 
/*     */       } 
/*     */       
/* 616 */       writeLog("hridMap ==> " + hridMap);
/*     */ 
/*     */       
/* 619 */       for (String key : hridMap.keySet()) {
/* 620 */         hrmRs.beforFirst();
/* 621 */         while (hrmRs.next()) {
/* 622 */           String swshrguid_oa = Util.null2String(hrmRs.getString("swshrguid"));
/* 623 */           String hrid_oa = swshrguid_oa.split("\\|")[0];
/*     */           
/* 625 */           if (key.equals(hrid_oa)) {
/* 626 */             writeLog("swshrguid_oa ==> " + swshrguid_oa);
/* 627 */             boolean leave = true;
/*     */ 
/*     */             
/* 630 */             for (int j = 0; j < hrmList.size(); j++) {
/* 631 */               Element hrmElement = hrmList.get(j);
/* 632 */               String swshrguid_idm = trimNull(hrmElement.getChildText("swshrguid"));
/* 633 */               writeLog("swshrguid_idm ==> " + swshrguid_idm);
/* 634 */               if (swshrguid_oa.equals(swshrguid_idm)) {
/* 635 */                 leave = false;
/*     */               }
/*     */             } 
/*     */             
/* 639 */             if (leave) {
/* 640 */               list.add(swshrguid_oa);
/*     */             }
/*     */           } 
/*     */         } 
/*     */       } 
/*     */     } 
/*     */     
/* 647 */     return list;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/chinasws/webservices/ParseXmlCust.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */