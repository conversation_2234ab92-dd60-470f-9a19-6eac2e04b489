/*     */ package chinasws.webservices;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmResourceVoCust
/*     */   implements Serializable
/*     */ {
/*     */   private String swshrguid;
/*     */   private String pinyinlastname;
/*     */   private String ecology_pinyin_search;
/*     */   private String swssigleaccount;
/*     */   private Integer id;
/*     */   private String subcompanyid1;
/*     */   private String departmentid;
/*     */   private String workcode;
/*     */   private String lastname;
/*     */   private String loginid;
/*     */   private String password;
/*     */   private String seclevel;
/*     */   private String sex;
/*     */   private String jobtitle;
/*     */   private String jobactivityid;
/*     */   private String jobgroupid;
/*     */   private String jobcall;
/*     */   private String joblevel;
/*     */   private String jobactivitydesc;
/*     */   private String managerid;
/*     */   private String assistantid;
/*     */   private String status;
/*     */   private String locationid;
/*     */   private String workroom;
/*     */   private String telephone;
/*     */   private String mobile;
/*     */   private String mobilecall;
/*     */   private String fax;
/*     */   private String email;
/*     */   private String systemlanguage;
/*     */   private String birthday;
/*     */   private String folk;
/*     */   private String nativeplace;
/*     */   private String regresidentplace;
/*     */   private String certificatenum;
/*     */   private String maritalstatus;
/*     */   private String policy;
/*     */   private String bememberdate;
/*     */   private String bepartydate;
/*     */   private String islabouunion;
/*     */   private String educationlevel;
/*     */   private String degree;
/*     */   private String healthinfo;
/*     */   private String height;
/*     */   private Integer weight;
/*     */   private String residentplace;
/*     */   private String homeaddress;
/*     */   private String tempresidentnumber;
/*     */   private String startdate;
/*     */   private String enddate;
/*     */   private String datefield1;
/*     */   private String datefield2;
/*     */   private String datefield3;
/*     */   private String datefield4;
/*     */   private String datefield5;
/*     */   private Float numberfield1;
/*     */   private Float numberfield2;
/*     */   private Float numberfield3;
/*     */   private Float numberfield4;
/*     */   private Float numberfield5;
/*     */   private String textfield1;
/*     */   private String textfield2;
/*     */   private String textfield3;
/*     */   private String textfield4;
/*     */   private String textfield5;
/*     */   private Short tinyintfield1;
/*     */   private Short tinyintfield2;
/*     */   private Short tinyintfield3;
/*     */   private Short tinyintfield4;
/*     */   private Short tinyintfield5;
/*     */   
/*     */   public String getSubcompanyid1() {
/* 106 */     return this.subcompanyid1;
/*     */   }
/*     */   
/*     */   public void setSubcompanyid1(String subcompanyid1) {
/* 110 */     this.subcompanyid1 = subcompanyid1;
/*     */   }
/*     */   
/*     */   public String getDepartmentid() {
/* 114 */     return this.departmentid;
/*     */   }
/*     */   
/*     */   public void setDepartmentid(String departmentid) {
/* 118 */     this.departmentid = departmentid;
/*     */   }
/*     */   
/*     */   public String getWorkcode() {
/* 122 */     return this.workcode;
/*     */   }
/*     */   
/*     */   public void setWorkcode(String workcode) {
/* 126 */     this.workcode = workcode;
/*     */   }
/*     */   
/*     */   public String getLastname() {
/* 130 */     return this.lastname;
/*     */   }
/*     */   
/*     */   public void setLastname(String lastname) {
/* 134 */     this.lastname = lastname;
/*     */   }
/*     */   
/*     */   public String getLoginid() {
/* 138 */     return this.loginid;
/*     */   }
/*     */   
/*     */   public void setLoginid(String loginid) {
/* 142 */     this.loginid = loginid;
/*     */   }
/*     */   
/*     */   public String getPassword() {
/* 146 */     return this.password;
/*     */   }
/*     */   
/*     */   public void setPassword(String password) {
/* 150 */     this.password = password;
/*     */   }
/*     */   
/*     */   public String getSeclevel() {
/* 154 */     return this.seclevel;
/*     */   }
/*     */   
/*     */   public void setSeclevel(String seclevel) {
/* 158 */     this.seclevel = seclevel;
/*     */   }
/*     */   
/*     */   public String getSex() {
/* 162 */     return this.sex;
/*     */   }
/*     */   
/*     */   public void setSex(String sex) {
/* 166 */     this.sex = sex;
/*     */   }
/*     */   
/*     */   public String getJobtitle() {
/* 170 */     return this.jobtitle;
/*     */   }
/*     */   
/*     */   public void setJobtitle(String jobtitle) {
/* 174 */     this.jobtitle = jobtitle;
/*     */   }
/*     */   
/*     */   public String getJobactivityid() {
/* 178 */     return this.jobactivityid;
/*     */   }
/*     */   
/*     */   public void setJobactivityid(String jobactivityid) {
/* 182 */     this.jobactivityid = jobactivityid;
/*     */   }
/*     */   
/*     */   public String getJobcall() {
/* 186 */     return this.jobcall;
/*     */   }
/*     */   
/*     */   public void setJobcall(String jobcall) {
/* 190 */     this.jobcall = jobcall;
/*     */   }
/*     */   
/*     */   public String getJoblevel() {
/* 194 */     return this.joblevel;
/*     */   }
/*     */   
/*     */   public void setJoblevel(String joblevel) {
/* 198 */     this.joblevel = joblevel;
/*     */   }
/*     */   
/*     */   public String getJobactivitydesc() {
/* 202 */     return this.jobactivitydesc;
/*     */   }
/*     */   
/*     */   public void setJobactivitydesc(String jobactivitydesc) {
/* 206 */     this.jobactivitydesc = jobactivitydesc;
/*     */   }
/*     */   
/*     */   public String getManagerid() {
/* 210 */     return this.managerid;
/*     */   }
/*     */   
/*     */   public void setManagerid(String managerid) {
/* 214 */     this.managerid = managerid;
/*     */   }
/*     */   
/*     */   public String getAssistantid() {
/* 218 */     return this.assistantid;
/*     */   }
/*     */   
/*     */   public void setAssistantid(String assistantid) {
/* 222 */     this.assistantid = assistantid;
/*     */   }
/*     */   
/*     */   public String getStatus() {
/* 226 */     return this.status;
/*     */   }
/*     */   
/*     */   public void setStatus(String status) {
/* 230 */     this.status = status;
/*     */   }
/*     */   
/*     */   public String getLocationid() {
/* 234 */     return this.locationid;
/*     */   }
/*     */   
/*     */   public void setLocationid(String locationid) {
/* 238 */     this.locationid = locationid;
/*     */   }
/*     */   
/*     */   public String getWorkroom() {
/* 242 */     return this.workroom;
/*     */   }
/*     */   
/*     */   public void setWorkroom(String workroom) {
/* 246 */     this.workroom = workroom;
/*     */   }
/*     */   
/*     */   public String getTelephone() {
/* 250 */     return this.telephone;
/*     */   }
/*     */   
/*     */   public void setTelephone(String telephone) {
/* 254 */     this.telephone = telephone;
/*     */   }
/*     */   
/*     */   public String getMobile() {
/* 258 */     return this.mobile;
/*     */   }
/*     */   
/*     */   public void setMobile(String mobile) {
/* 262 */     this.mobile = mobile;
/*     */   }
/*     */   
/*     */   public String getMobilecall() {
/* 266 */     return this.mobilecall;
/*     */   }
/*     */   
/*     */   public void setMobilecall(String mobilecall) {
/* 270 */     this.mobilecall = mobilecall;
/*     */   }
/*     */   
/*     */   public String getFax() {
/* 274 */     return this.fax;
/*     */   }
/*     */   
/*     */   public void setFax(String fax) {
/* 278 */     this.fax = fax;
/*     */   }
/*     */   
/*     */   public String getEmail() {
/* 282 */     return this.email;
/*     */   }
/*     */   
/*     */   public void setEmail(String email) {
/* 286 */     this.email = email;
/*     */   }
/*     */   
/*     */   public String getSystemlanguage() {
/* 290 */     return this.systemlanguage;
/*     */   }
/*     */   
/*     */   public void setSystemlanguage(String systemlanguage) {
/* 294 */     this.systemlanguage = systemlanguage;
/*     */   }
/*     */   
/*     */   public String getBirthday() {
/* 298 */     return this.birthday;
/*     */   }
/*     */   
/*     */   public void setBirthday(String birthday) {
/* 302 */     this.birthday = birthday;
/*     */   }
/*     */   
/*     */   public String getFolk() {
/* 306 */     return this.folk;
/*     */   }
/*     */   
/*     */   public void setFolk(String folk) {
/* 310 */     this.folk = folk;
/*     */   }
/*     */   
/*     */   public String getNativeplace() {
/* 314 */     return this.nativeplace;
/*     */   }
/*     */   
/*     */   public void setNativeplace(String nativeplace) {
/* 318 */     this.nativeplace = nativeplace;
/*     */   }
/*     */   
/*     */   public String getRegresidentplace() {
/* 322 */     return this.regresidentplace;
/*     */   }
/*     */   
/*     */   public void setRegresidentplace(String regresidentplace) {
/* 326 */     this.regresidentplace = regresidentplace;
/*     */   }
/*     */   
/*     */   public String getCertificatenum() {
/* 330 */     return this.certificatenum;
/*     */   }
/*     */   
/*     */   public void setCertificatenum(String certificatenum) {
/* 334 */     this.certificatenum = certificatenum;
/*     */   }
/*     */   
/*     */   public String getMaritalstatus() {
/* 338 */     return this.maritalstatus;
/*     */   }
/*     */   
/*     */   public void setMaritalstatus(String maritalstatus) {
/* 342 */     this.maritalstatus = maritalstatus;
/*     */   }
/*     */   
/*     */   public String getPolicy() {
/* 346 */     return this.policy;
/*     */   }
/*     */   
/*     */   public void setPolicy(String policy) {
/* 350 */     this.policy = policy;
/*     */   }
/*     */   
/*     */   public String getBememberdate() {
/* 354 */     return this.bememberdate;
/*     */   }
/*     */   
/*     */   public void setBememberdate(String bememberdate) {
/* 358 */     this.bememberdate = bememberdate;
/*     */   }
/*     */   
/*     */   public String getBepartydate() {
/* 362 */     return this.bepartydate;
/*     */   }
/*     */   
/*     */   public void setBepartydate(String bepartydate) {
/* 366 */     this.bepartydate = bepartydate;
/*     */   }
/*     */   
/*     */   public String getIslabouunion() {
/* 370 */     return this.islabouunion;
/*     */   }
/*     */   
/*     */   public void setIslabouunion(String islabouunion) {
/* 374 */     this.islabouunion = islabouunion;
/*     */   }
/*     */   
/*     */   public String getEducationlevel() {
/* 378 */     return this.educationlevel;
/*     */   }
/*     */   
/*     */   public void setEducationlevel(String educationlevel) {
/* 382 */     this.educationlevel = educationlevel;
/*     */   }
/*     */   
/*     */   public String getDegree() {
/* 386 */     return this.degree;
/*     */   }
/*     */   
/*     */   public void setDegree(String degree) {
/* 390 */     this.degree = degree;
/*     */   }
/*     */   
/*     */   public String getHealthinfo() {
/* 394 */     return this.healthinfo;
/*     */   }
/*     */   
/*     */   public void setHealthinfo(String healthinfo) {
/* 398 */     this.healthinfo = healthinfo;
/*     */   }
/*     */   
/*     */   public String getHeight() {
/* 402 */     return this.height;
/*     */   }
/*     */   
/*     */   public void setHeight(String height) {
/* 406 */     this.height = height;
/*     */   }
/*     */   
/*     */   public Integer getWeight() {
/* 410 */     return this.weight;
/*     */   }
/*     */   
/*     */   public void setWeight(Integer weight) {
/* 414 */     this.weight = weight;
/*     */   }
/*     */   
/*     */   public String getResidentplace() {
/* 418 */     return this.residentplace;
/*     */   }
/*     */   
/*     */   public void setResidentplace(String residentplace) {
/* 422 */     this.residentplace = residentplace;
/*     */   }
/*     */   
/*     */   public String getHomeaddress() {
/* 426 */     return this.homeaddress;
/*     */   }
/*     */   
/*     */   public void setHomeaddress(String homeaddress) {
/* 430 */     this.homeaddress = homeaddress;
/*     */   }
/*     */   
/*     */   public String getTempresidentnumber() {
/* 434 */     return this.tempresidentnumber;
/*     */   }
/*     */   
/*     */   public void setTempresidentnumber(String tempresidentnumber) {
/* 438 */     this.tempresidentnumber = tempresidentnumber;
/*     */   }
/*     */   
/*     */   public String getJobgroupid() {
/* 442 */     return this.jobgroupid;
/*     */   }
/*     */   
/*     */   public void setJobgroupid(String jobgroupid) {
/* 446 */     this.jobgroupid = jobgroupid;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/* 479 */   private String baseFieldsValue = "";
/*     */   
/* 481 */   private String baseFields = "";
/*     */   
/* 483 */   private String personFieldsValue = "";
/*     */   
/* 485 */   private String personFields = "";
/*     */   
/* 487 */   private String workFieldsValue = "";
/*     */   
/* 489 */   private String workFields = "";
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String usekind;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String probationenddate;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String dsporder;
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String accounttype;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDsporder() {
/* 516 */     return this.dsporder;
/*     */   }
/*     */   
/*     */   public void setDsporder(String dsporder) {
/* 520 */     this.dsporder = dsporder;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getDatefield1() {
/* 542 */     return this.datefield1;
/*     */   }
/*     */   
/*     */   public void setDatefield1(String datefield1) {
/* 546 */     this.datefield1 = datefield1;
/*     */   }
/*     */   
/*     */   public String getDatefield2() {
/* 550 */     return this.datefield2;
/*     */   }
/*     */   
/*     */   public void setDatefield2(String datefield2) {
/* 554 */     this.datefield2 = datefield2;
/*     */   }
/*     */   
/*     */   public String getDatefield3() {
/* 558 */     return this.datefield3;
/*     */   }
/*     */   
/*     */   public void setDatefield3(String datefield3) {
/* 562 */     this.datefield3 = datefield3;
/*     */   }
/*     */   
/*     */   public String getDatefield4() {
/* 566 */     return this.datefield4;
/*     */   }
/*     */   
/*     */   public void setDatefield4(String datefield4) {
/* 570 */     this.datefield4 = datefield4;
/*     */   }
/*     */   
/*     */   public String getDatefield5() {
/* 574 */     return this.datefield5;
/*     */   }
/*     */   
/*     */   public void setDatefield5(String datefield5) {
/* 578 */     this.datefield5 = datefield5;
/*     */   }
/*     */   
/*     */   public Float getNumberfield1() {
/* 582 */     return this.numberfield1;
/*     */   }
/*     */   
/*     */   public void setNumberfield1(Float numberfield1) {
/* 586 */     this.numberfield1 = numberfield1;
/*     */   }
/*     */   
/*     */   public Float getNumberfield2() {
/* 590 */     return this.numberfield2;
/*     */   }
/*     */   
/*     */   public void setNumberfield2(Float numberfield2) {
/* 594 */     this.numberfield2 = numberfield2;
/*     */   }
/*     */   
/*     */   public Float getNumberfield3() {
/* 598 */     return this.numberfield3;
/*     */   }
/*     */   
/*     */   public void setNumberfield3(Float numberfield3) {
/* 602 */     this.numberfield3 = numberfield3;
/*     */   }
/*     */   
/*     */   public Float getNumberfield4() {
/* 606 */     return this.numberfield4;
/*     */   }
/*     */   
/*     */   public void setNumberfield4(Float numberfield4) {
/* 610 */     this.numberfield4 = numberfield4;
/*     */   }
/*     */   
/*     */   public Float getNumberfield5() {
/* 614 */     return this.numberfield5;
/*     */   }
/*     */   
/*     */   public void setNumberfield5(Float numberfield5) {
/* 618 */     this.numberfield5 = numberfield5;
/*     */   }
/*     */   
/*     */   public String getTextfield1() {
/* 622 */     return this.textfield1;
/*     */   }
/*     */   
/*     */   public void setTextfield1(String textfield1) {
/* 626 */     this.textfield1 = textfield1;
/*     */   }
/*     */   
/*     */   public String getTextfield2() {
/* 630 */     return this.textfield2;
/*     */   }
/*     */   
/*     */   public void setTextfield2(String textfield2) {
/* 634 */     this.textfield2 = textfield2;
/*     */   }
/*     */   
/*     */   public String getTextfield3() {
/* 638 */     return this.textfield3;
/*     */   }
/*     */   
/*     */   public void setTextfield3(String textfield3) {
/* 642 */     this.textfield3 = textfield3;
/*     */   }
/*     */   
/*     */   public String getTextfield4() {
/* 646 */     return this.textfield4;
/*     */   }
/*     */   
/*     */   public void setTextfield4(String textfield4) {
/* 650 */     this.textfield4 = textfield4;
/*     */   }
/*     */   
/*     */   public String getTextfield5() {
/* 654 */     return this.textfield5;
/*     */   }
/*     */   
/*     */   public void setTextfield5(String textfield5) {
/* 658 */     this.textfield5 = textfield5;
/*     */   }
/*     */   
/*     */   public Short getTinyintfield1() {
/* 662 */     return this.tinyintfield1;
/*     */   }
/*     */   
/*     */   public void setTinyintfield1(Short tinyintfield1) {
/* 666 */     this.tinyintfield1 = tinyintfield1;
/*     */   }
/*     */   
/*     */   public Short getTinyintfield2() {
/* 670 */     return this.tinyintfield2;
/*     */   }
/*     */   
/*     */   public void setTinyintfield2(Short tinyintfield2) {
/* 674 */     this.tinyintfield2 = tinyintfield2;
/*     */   }
/*     */   
/*     */   public Short getTinyintfield3() {
/* 678 */     return this.tinyintfield3;
/*     */   }
/*     */   
/*     */   public void setTinyintfield3(Short tinyintfield3) {
/* 682 */     this.tinyintfield3 = tinyintfield3;
/*     */   }
/*     */   
/*     */   public Short getTinyintfield4() {
/* 686 */     return this.tinyintfield4;
/*     */   }
/*     */   
/*     */   public void setTinyintfield4(Short tinyintfield4) {
/* 690 */     this.tinyintfield4 = tinyintfield4;
/*     */   }
/*     */   
/*     */   public Short getTinyintfield5() {
/* 694 */     return this.tinyintfield5;
/*     */   }
/*     */   
/*     */   public void setTinyintfield5(Short tinyintfield5) {
/* 698 */     this.tinyintfield5 = tinyintfield5;
/*     */   }
/*     */   
/*     */   public String getPersonFields() {
/* 702 */     return this.personFields;
/*     */   }
/*     */   
/*     */   public void setPersonFields(String personFields) {
/* 706 */     this.personFields = personFields;
/*     */   }
/*     */   
/*     */   public String getWorkFields() {
/* 710 */     return this.workFields;
/*     */   }
/*     */   
/*     */   public void setWorkFields(String workFields) {
/* 714 */     this.workFields = workFields;
/*     */   }
/*     */   
/*     */   public String getPersonFieldsValue() {
/* 718 */     return this.personFieldsValue;
/*     */   }
/*     */   
/*     */   public void setPersonFieldsValue(String personFieldsValue) {
/* 722 */     this.personFieldsValue = personFieldsValue;
/*     */   }
/*     */   
/*     */   public String getWorkFieldsValue() {
/* 726 */     return this.workFieldsValue;
/*     */   }
/*     */   
/*     */   public void setWorkFieldsValue(String workFieldsValue) {
/* 730 */     this.workFieldsValue = workFieldsValue;
/*     */   }
/*     */   
/*     */   public Integer getId() {
/* 734 */     return this.id;
/*     */   }
/*     */   
/*     */   public void setId(Integer id) {
/* 738 */     this.id = id;
/*     */   }
/*     */   
/*     */   public String getBaseFields() {
/* 742 */     return this.baseFields;
/*     */   }
/*     */   
/*     */   public void setBaseFields(String baseFields) {
/* 746 */     this.baseFields = baseFields;
/*     */   }
/*     */   
/*     */   public String getBaseFieldsValue() {
/* 750 */     return this.baseFieldsValue;
/*     */   }
/*     */   
/*     */   public void setBaseFieldsValue(String baseFieldsValue) {
/* 754 */     this.baseFieldsValue = baseFieldsValue;
/*     */   }
/*     */   
/*     */   public String getStartdate() {
/* 758 */     return this.startdate;
/*     */   }
/*     */   
/*     */   public void setStartdate(String startdate) {
/* 762 */     this.startdate = startdate;
/*     */   }
/*     */   
/*     */   public String getEnddate() {
/* 766 */     return this.enddate;
/*     */   }
/*     */   
/*     */   public void setEnddate(String enddate) {
/* 770 */     this.enddate = enddate;
/*     */   }
/*     */   
/*     */   public String getUsekind() {
/* 774 */     return this.usekind;
/*     */   }
/*     */   
/*     */   public void setUsekind(String usekind) {
/* 778 */     this.usekind = usekind;
/*     */   }
/*     */   
/*     */   public String getProbationenddate() {
/* 782 */     return this.probationenddate;
/*     */   }
/*     */   
/*     */   public void setProbationenddate(String probationenddate) {
/* 786 */     this.probationenddate = probationenddate;
/*     */   }
/*     */   
/*     */   public String getSwshrguid() {
/* 790 */     return this.swshrguid;
/*     */   }
/*     */   
/*     */   public void setSwshrguid(String swshrguid) {
/* 794 */     this.swshrguid = swshrguid;
/*     */   }
/*     */   
/*     */   public String getPinyinlastname() {
/* 798 */     return this.pinyinlastname;
/*     */   }
/*     */   
/*     */   public void setPinyinlastname(String pinyinlastname) {
/* 802 */     this.pinyinlastname = pinyinlastname;
/*     */   }
/*     */   
/*     */   public String getEcology_pinyin_search() {
/* 806 */     return this.ecology_pinyin_search;
/*     */   }
/*     */   
/*     */   public void setEcology_pinyin_search(String ecology_pinyin_search) {
/* 810 */     this.ecology_pinyin_search = ecology_pinyin_search;
/*     */   }
/*     */   
/*     */   public String getSwssigleaccount() {
/* 814 */     return this.swssigleaccount;
/*     */   }
/*     */   
/*     */   public void setSwssigleaccount(String swssigleaccount) {
/* 818 */     this.swssigleaccount = swssigleaccount;
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public String getAccounttype() {
/* 824 */     return this.accounttype;
/*     */   }
/*     */   
/*     */   public void setAccounttype(String accounttype) {
/* 828 */     this.accounttype = accounttype;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/chinasws/webservices/HrmResourceVoCust.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */