/*     */ package chinasws.webservices;
/*     */ 
/*     */ import com.engine.common.service.impl.HrmCommonServiceImpl;
/*     */ import java.util.ArrayList;
/*     */ import java.util.HashMap;
/*     */ import java.util.Iterator;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import javax.servlet.http.HttpServletRequest;
/*     */ import org.codehaus.xfire.transport.http.XFireServletController;
/*     */ import weaver.conn.RecordSet;
/*     */ import weaver.general.BaseBean;
/*     */ import weaver.general.Util;
/*     */ import weaver.join.hrm.in.ImportLog;
/*     */ import weaver.toolbox.core.util.StrUtil;
/*     */ import weaver.toolbox.db.recordset.ExecuteUtil;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class HrmServiceCustImpl
/*     */   extends BaseBean
/*     */   implements HrmServiceCust
/*     */ {
/*  25 */   private HashMap h_orgInfo = new HashMap<>();
/*     */ 
/*     */   
/*  28 */   private HashMap h_orgInfo_add = new HashMap<>();
/*     */ 
/*     */   
/*  31 */   private HashMap h_orgInfo_update = new HashMap<>();
/*     */ 
/*     */   
/*  34 */   private List h_addOrg = new ArrayList();
/*     */ 
/*     */   
/*  37 */   private List h_updateOrg = new ArrayList();
/*     */ 
/*     */   
/*  40 */   private List h_delOrg = new ArrayList();
/*     */   
/*  42 */   private String configip = "," + getPropValue("HrmWebserviceIP", "ipaddress") + ",";
/*     */ 
/*     */   
/*     */   public String SynHrmResource(String ipaddress, String xmlData) throws Exception {
/*  46 */     writeLog("HrmServiceCustImpl SynHrmResource -------");
/*     */     
/*  48 */     ipaddress = getClientIpXfire();
/*  49 */     String result = "<?xml version=\"1.0\" encoding=\"UTF-8\"?><result>";
/*  50 */     if (("," + this.configip + ",").indexOf("," + ipaddress + ",") < 0) {
/*  51 */       writeLog("IP" + ipaddress + "不在设定的范围内,无权限访问!");
/*  52 */       result = result + "<message value=\"2\">IP不在设定的范围内,无权限访问</message></result>";
/*  53 */       return result;
/*     */     } 
/*     */     
/*  56 */     ParseXmlCust parseXml = new ParseXmlCust();
/*     */     try {
/*  58 */       writeLog("xmlData -- 开始解析");
/*  59 */       parseXml.parseHrmResource(xmlData);
/*  60 */       this.h_orgInfo_add = parseXml.getH_orgInfo_add();
/*  61 */       writeLog("h_orgInfo_add size -- " + this.h_orgInfo_add.size());
/*  62 */       writeLog("h_orgInfo_add -- " + this.h_orgInfo_add.toString());
/*  63 */       this.h_orgInfo_update = parseXml.getH_orgInfo_update();
/*  64 */       writeLog("h_orgInfo_update size -- " + this.h_orgInfo_update.size());
/*  65 */       writeLog("h_orgInfo_update -- " + this.h_orgInfo_update.toString());
/*  66 */       this.h_delOrg = parseXml.getH_delOrg();
/*  67 */       writeLog("h_delOrg size -- " + this.h_delOrg.size());
/*  68 */       writeLog("h_delOrg -- " + this.h_delOrg.toString());
/*     */       
/*  70 */       HrmImportProcessCust importProcess = new HrmImportProcessCust();
/*     */ 
/*     */       
/*  73 */       List<ImportLog> addresultList = importProcess.processMap("swshrguid", this.h_orgInfo_add, "add");
/*     */       
/*  75 */       String errlogRecord = "";
/*  76 */       String sulogrecord = "";
/*  77 */       for (int j = 0; j < addresultList.size(); j++) {
/*  78 */         ImportLog addlog = addresultList.get(j);
/*  79 */         if (addlog.getStatus().equals("失败"))
/*     */         {
/*     */           
/*  82 */           errlogRecord = errlogRecord + ((addlog.getWorkCode() != null) ? addlog.getWorkCode() : "") + "|" + ((addlog.getLastname() != null) ? addlog.getLastname() : "") + "|" + addlog.getOperation() + "|" + addlog.getStatus() + "|" + addlog.getReason() + ",";
/*     */         }
/*     */       } 
/*     */       
/*  86 */       if (!"".equals(errlogRecord)) {
/*  87 */         result = result + "<message value=\"0\">" + errlogRecord + "</message>";
/*     */       }
/*     */       
/*  90 */       for (int k = 0; k < addresultList.size(); k++) {
/*  91 */         ImportLog addlog = addresultList.get(k);
/*  92 */         if (addlog.getStatus().equals("成功"))
/*     */         {
/*     */           
/*  95 */           sulogrecord = sulogrecord + ((addlog.getWorkCode() != null) ? addlog.getWorkCode() : "") + "|" + ((addlog.getLastname() != null) ? addlog.getLastname() : "") + "|" + addlog.getOperation() + "|" + addlog.getStatus() + "|" + addlog.getReason() + ",";
/*     */         }
/*     */       } 
/*     */       
/*  99 */       if (!"".equals(sulogrecord)) {
/* 100 */         result = result + "<message value=\"1\">" + sulogrecord + "</message>";
/*     */       }
/*     */ 
/*     */ 
/*     */       
/* 105 */       List<ImportLog> editresultList = importProcess.processMap("swshrguid", this.h_orgInfo_update, "update");
/*     */       
/* 107 */       String errlogRecord1 = "";
/* 108 */       String sulogrecord1 = "";
/* 109 */       for (int s = 0; s < editresultList.size(); s++) {
/* 110 */         ImportLog editlog = editresultList.get(s);
/* 111 */         if (editlog.getStatus().equals("失败"))
/*     */         {
/*     */           
/* 114 */           errlogRecord1 = errlogRecord1 + ((editlog.getWorkCode() != null) ? editlog.getWorkCode() : "") + "|" + ((editlog.getLastname() != null) ? editlog.getLastname() : "") + "|" + editlog.getOperation() + "|" + editlog.getStatus() + "|" + editlog.getReason() + ",";
/*     */         }
/*     */       } 
/*     */       
/* 118 */       if (!"".equals(errlogRecord1)) {
/* 119 */         result = result + "<message value=\"0\">" + errlogRecord1 + "</message>";
/*     */       }
/*     */       
/* 122 */       for (int h = 0; h < editresultList.size(); h++) {
/* 123 */         ImportLog editlog = editresultList.get(h);
/* 124 */         if (editlog.getStatus().equals("成功"))
/*     */         {
/*     */           
/* 127 */           sulogrecord1 = sulogrecord1 + ((editlog.getWorkCode() != null) ? editlog.getWorkCode() : "") + "|" + ((editlog.getLastname() != null) ? editlog.getLastname() : "") + "|" + editlog.getOperation() + "|" + editlog.getStatus() + "|" + editlog.getReason() + ",";
/*     */         }
/*     */       } 
/*     */       
/* 131 */       if (!"".equals(sulogrecord1)) {
/* 132 */         result = result + "<message value=\"1\">" + sulogrecord1 + "</message>";
/*     */       }
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 138 */       writeLog("--------处理人员拼音搜索问题 历史数据------");
/* 139 */       RecordSet rs = new RecordSet();
/* 140 */       String sql = "select id,lastname,pinyinlastname,ecology_pinyin_search from hrmresource where pinyinlastname is null or ecology_pinyin_search is null";
/*     */       
/* 142 */       rs.execute(sql);
/*     */ 
/*     */       
/* 145 */       RecordSet pinyinRS = new RecordSet();
/* 146 */       while (rs.next()) {
/* 147 */         String idTemp = Util.null2String(rs.getString("id"));
/* 148 */         String pinyinTemp = Util.null2String(rs.getString("lastname"));
/* 149 */         pinyinTemp = (new HrmCommonServiceImpl()).generateQuickSearchStr(pinyinTemp);
/* 150 */         sql = "update HrmResource set pinyinlastname = '" + pinyinTemp + "', ecology_pinyin_search = '" + pinyinTemp + "' where id =" + idTemp;
/* 151 */         pinyinRS.execute(sql);
/*     */       } 
/*     */ 
/*     */ 
/*     */ 
/*     */       
/* 157 */       String errlogRecord2 = "";
/* 158 */       String sulogrecord2 = "";
/* 159 */       if (this.h_delOrg.size() > 0) {
/* 160 */         ArrayList<String> sqlList = new ArrayList();
/* 161 */         for (int i = 0; i < this.h_delOrg.size(); i++) {
/* 162 */           String sid = this.h_delOrg.get(i).toString();
/* 163 */           if (sid.contains("|")) {
/*     */             
/* 165 */             String sqlTemp = "update hrmresource set status=5 where swshrguid = '{}'";
/* 166 */             sql = StrUtil.format(sqlTemp, new Object[] { this.h_delOrg.get(i) });
/*     */           } else {
/*     */             
/* 169 */             String sqlTemp = "update hrmresource set status=5 where swshrguid like '{}|%'";
/* 170 */             sql = StrUtil.format(sqlTemp, new Object[] { this.h_delOrg.get(i) });
/*     */           } 
/* 172 */           sqlList.add(sql);
/*     */           
/* 174 */           sulogrecord2 = sulogrecord2 + this.h_delOrg.get(i) + "|离职成功,";
/* 175 */           errlogRecord2 = errlogRecord2 + this.h_delOrg.get(i) + "|离职失败,";
/*     */         } 
/*     */         try {
/* 178 */           ExecuteUtil.executeBatchSqlWithTrans(sqlList);
/* 179 */           writeLog("离职人员 sql " + sqlList);
/*     */           
/* 181 */           result = result + "<message value=\"1\">" + sulogrecord2 + "</message>";
/* 182 */         } catch (Exception e) {
/* 183 */           writeLog("离职人员 异常 -- ");
/* 184 */           writeLog(e);
/*     */           
/* 186 */           result = result + "<message value=\"0\">" + errlogRecord2 + "</message>";
/*     */         } 
/*     */       } 
/*     */ 
/*     */ 
/*     */       
/* 192 */       Map<String, String> workcodeMap = new HashMap<>();
/*     */       
/* 194 */       Iterator<HrmResourceVoCust> iter = this.h_orgInfo_add.values().iterator();
/* 195 */       while (iter.hasNext()) {
/* 196 */         HrmResourceVoCust hrmvo = iter.next();
/*     */         
/* 198 */         String workcode = hrmvo.getWorkcode();
/* 199 */         if (!workcodeMap.containsKey(workcode)) {
/* 200 */           workcodeMap.put(workcode, workcode);
/*     */         }
/*     */       } 
/*     */       
/* 204 */       Iterator<HrmResourceVoCust> iter2 = this.h_orgInfo_update.values().iterator();
/* 205 */       while (iter2.hasNext()) {
/* 206 */         HrmResourceVoCust hrmvo = iter2.next();
/*     */         
/* 208 */         String workcode = hrmvo.getWorkcode();
/* 209 */         if (!workcodeMap.containsKey(workcode)) {
/* 210 */           workcodeMap.put(workcode, workcode);
/*     */         }
/*     */       } 
/*     */       
/* 214 */       for (String key : workcodeMap.keySet()) {
/* 215 */         String updatePwd = StrUtil.format("UPDATE hrmresource SET password = (SELECT password FROM hrmresource WHERE workcode = '{}' AND accounttype = 0) WHERE workcode='{}' AND accounttype = 1", new Object[] { key, key });
/* 216 */         ExecuteUtil.executeSql(updatePwd);
/*     */       } 
/*     */ 
/*     */ 
/*     */       
/* 221 */       for (String key : workcodeMap.keySet()) {
/* 222 */         String str1 = StrUtil.format("UPDATE hrmresource SET belongto = (SELECT id FROM hrmresource WHERE workcode = '{}' AND accounttype = 0) WHERE workcode='{}' AND accounttype = 1", new Object[] { key, key });
/* 223 */         writeLog("次账号，关联上主账号信息 sql -- " + str1);
/* 224 */         ExecuteUtil.executeSql(str1);
/*     */       } 
/*     */ 
/*     */ 
/*     */       
/* 229 */       String updateBelongto = "UPDATE hrmresource SET belongto = null WHERE accounttype = 0";
/* 230 */       ExecuteUtil.executeSql(updateBelongto);
/*     */     }
/* 232 */     catch (Exception e) {
/* 233 */       writeLog(e);
/* 234 */       result = result + "<message value=\"0\">数据异常</message>";
/*     */     } 
/* 236 */     result = result + "</result>";
/*     */     
/* 238 */     writeLog("result -- " + result);
/* 239 */     return result;
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public String getClientIpXfire() {
/* 249 */     String ip = "";
/*     */     try {
/* 251 */       HttpServletRequest request = XFireServletController.getRequest();
/* 252 */       ip = getRemoteAddress(request);
/*     */       
/* 254 */       return ip;
/* 255 */     } catch (Exception e) {
/* 256 */       e.printStackTrace();
/* 257 */       return "";
/*     */     } 
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   private String getRemoteAddress(HttpServletRequest request) {
/* 270 */     String ip = request.getHeader("x-forwarded-for");
/* 271 */     if (ip == null || ip.length() == 0 || ip.equalsIgnoreCase("unknown")) {
/* 272 */       ip = request.getHeader("Proxy-Client-IP");
/*     */     }
/* 274 */     if (ip == null || ip.length() == 0 || ip.equalsIgnoreCase("unknown")) {
/* 275 */       ip = request.getHeader("WL-Proxy-Client-IP");
/*     */     }
/* 277 */     if (ip == null || ip.length() == 0 || ip.equalsIgnoreCase("unknown")) {
/* 278 */       ip = request.getRemoteAddr();
/*     */     }
/* 280 */     if (ip.indexOf(",") >= 0) {
/* 281 */       ip = ip.substring(0, ip.indexOf(","));
/*     */     }
/* 283 */     return ip;
/*     */   }
/*     */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/chinasws/webservices/HrmServiceCustImpl.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */