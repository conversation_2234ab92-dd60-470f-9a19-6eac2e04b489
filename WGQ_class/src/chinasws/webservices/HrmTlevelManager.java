/*    */ package chinasws.webservices;
/*    */ 
/*    */ import weaver.conn.RecordSet;
/*    */ 
/*    */ public class HrmTlevelManager
/*    */ {
/*    */   private static boolean isUpdate = true;
/*    */   
/*    */   static {
/* 10 */     (new Thread() {
/*    */         public void run() {
/*    */           while (true) {
/* 13 */             if (!HrmTlevelManager.isUpdate) {
/* 14 */               HrmTlevelManager.isUpdate = true;
/* 15 */               HrmTlevelManager.updateTlevel();
/*    */             } 
/*    */             
/*    */             try {
/* 19 */               sleep(600000L);
/* 20 */             } catch (InterruptedException e) {
/* 21 */               e.printStackTrace();
/*    */             }
/*    */           
/*    */           } 
/*    */         }
/* 26 */       }).start();
/*    */   }
/*    */   
/*    */   private static final void updateTlevel() {
/* 30 */     RecordSet rs = new RecordSet();
/* 31 */     String tmpField = "oracle".equals(rs.getDBType()) ? "templevel" : "level";
/* 32 */     rs.executeQuery("select id," + tmpField + " as tlevel from tempHrmSubCompanyView a where not exists (select 1 from HrmSubCompany b where a.id=b.id and a." + tmpField + "=b.tlevel and b.tlevel is not null) and a." + tmpField + " is not null", new Object[0]);
/* 33 */     RecordSet updateRs = new RecordSet();
/* 34 */     while (rs.next()) {
/* 35 */       String id = rs.getString("id");
/* 36 */       int level = rs.getInt("tlevel");
/* 37 */       updateRs.executeUpdate("update HrmSubCompany set tlevel=? where id=?", new Object[] { Integer.valueOf(level), id });
/*    */     } 
/*    */     
/* 40 */     rs.executeQuery("select id," + tmpField + " as tlevel from tempHrmDepartmentView a where not exists (select 1 from HrmDepartment b where a.id=b.id and a." + tmpField + "=b.tlevel and b.tlevel is not null) and a." + tmpField + " is not null", new Object[0]);
/* 41 */     while (rs.next()) {
/* 42 */       String id = rs.getString("id");
/* 43 */       int level = rs.getInt("tlevel");
/* 44 */       updateRs.executeUpdate("update HrmDepartment set tlevel=? where id=?", new Object[] { Integer.valueOf(level), id });
/*    */     } 
/*    */   }
/*    */   
/*    */   public static void setUpdate() {
/* 49 */     isUpdate = false;
/*    */   }
/*    */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/chinasws/webservices/HrmTlevelManager.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */