/*      */ package chinasws.webservices;
/*      */ 
/*      */ import java.io.BufferedWriter;
/*      */ import java.io.File;
/*      */ import java.io.FileWriter;
/*      */ import java.io.IOException;
/*      */ import java.lang.reflect.Field;
/*      */ import java.text.SimpleDateFormat;
/*      */ import java.util.ArrayList;
/*      */ import java.util.Date;
/*      */ import java.util.HashMap;
/*      */ import java.util.List;
/*      */ import java.util.Map;
/*      */ import java.util.Set;
/*      */ import java.util.regex.Matcher;
/*      */ import java.util.regex.Pattern;
/*      */ import javax.servlet.http.HttpSession;
/*      */ import ln.LN;
/*      */ import org.json.JSONObject;
/*      */ import weaver.common.StringUtil;
/*      */ import weaver.conn.RecordSet;
/*      */ import weaver.general.BaseBean;
/*      */ import weaver.general.GCONST;
/*      */ import weaver.general.Util;
/*      */ import weaver.hrm.User;
/*      */ import weaver.hrm.appdetach.AppDetachComInfo;
/*      */ import weaver.hrm.common.DbFunctionUtil;
/*      */ import weaver.hrm.company.DepartmentComInfo;
/*      */ import weaver.hrm.company.SubCompanyComInfo;
/*      */ import weaver.hrm.definedfield.HrmFieldManager;
/*      */ import weaver.hrm.job.JobActivitiesComInfo;
/*      */ import weaver.hrm.job.JobGroupsComInfo;
/*      */ import weaver.hrm.job.JobTitlesComInfo;
/*      */ import weaver.hrm.job.UseKindComInfo;
/*      */ import weaver.hrm.location.LocationComInfo;
/*      */ import weaver.hrm.passwordprotection.manager.HrmResourceManager;
/*      */ import weaver.hrm.resource.ResourceComInfo;
/*      */ import weaver.hrm.settings.ChgPasswdReminder;
/*      */ import weaver.hrm.settings.RemindSettings;
/*      */ import weaver.interfaces.email.CoreMailAPI;
/*      */ import weaver.join.hrm.in.IHrmImportProcess;
/*      */ import weaver.join.hrm.in.ImportLog;
/*      */ import weaver.rtx.OrganisationCom;
/*      */ import weaver.systeminfo.SystemEnv;
/*      */ import weaver.systeminfo.setting.HrmUserSettingManager;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ public class HrmImportProcessCust
/*      */   extends BaseBean
/*      */   implements IHrmImportProcess
/*      */ {
/*   69 */   private Map<String, Integer> keyMap = new HashMap<>();
/*      */   
/*   71 */   private Map<String, Integer> certificateNums = new HashMap<>();
/*      */   
/*      */   private Map<String, JSONObject> baseTypeMap;
/*      */   
/*      */   private Map<String, JSONObject> personTypeMap;
/*      */   
/*      */   private Map<String, JSONObject> workTypeMap;
/*      */   
/*   79 */   private String date = (new SimpleDateFormat("yyyy-MM-dd")).format(new Date());
/*      */   
/*   81 */   private String logFile = "";
/*      */ 
/*      */ 
/*      */   
/*   85 */   private CoreMailAPI coremailapi = null;
/*      */   
/*   87 */   private OrganisationCom rtxService = null;
/*      */   
/*   89 */   private int userlanguage = 7;
/*      */   
/*   91 */   char separator = Util.getSeparator();
/*      */   
/*   93 */   LN license = new LN();
/*      */   
/*   95 */   String keyField = "";
/*      */   
/*   97 */   private int scCount = 0;
/*      */   
/*   99 */   private HrmResourceVoCust vo = null;
/*      */   
/*      */   private String multilanguage;
/*      */   
/*  103 */   private Map<String, Integer> sysLanguage = null;
/*      */   
/*  105 */   private Map<String, Integer> jobcallMap = null;
/*  106 */   private Map<String, Integer> locationMap = null;
/*  107 */   private Map<String, Integer> usekindMap = null;
/*  108 */   private Map<String, Integer> educationlevelMap = null;
/*      */   
/*  110 */   private int cnLanguageId = 7;
/*      */   
/*      */   public HrmImportProcessCust() {
/*  113 */     int F_Y = 0;
/*  114 */     int F_N = 1;
/*  115 */     int type = 1;
/*  116 */     LN license = new LN();
/*  117 */     license.InLicense();
/*  118 */     type = StringUtil.parseToInt(license.getScType(), 1);
/*  119 */     this.scCount = StringUtil.parseToInt(license.getScCount(), 0);
/*  120 */     this.scCount = (type == 0) ? ((this.scCount < 0) ? 0 : this.scCount) : 0;
/*  121 */     RecordSet recordSet = new RecordSet();
/*      */     
/*  123 */     String sql = "select multilanguage,(select id from syslanguage where language='简体中文' or language='中文') as cnLanguageId from license ";
/*  124 */     recordSet.execute(sql);
/*  125 */     recordSet.next();
/*  126 */     this.multilanguage = recordSet.getString("multilanguage");
/*  127 */     this.cnLanguageId = recordSet.getInt(2);
/*      */     
/*  129 */     this.sysLanguage = new HashMap<>();
/*  130 */     recordSet.execute("select id,language from syslanguage");
/*  131 */     while (recordSet.next()) {
/*  132 */       this.sysLanguage.put(recordSet.getString(2), Integer.valueOf(recordSet.getInt(1)));
/*      */     }
/*      */     
/*  135 */     this.educationlevelMap = new HashMap<>();
/*  136 */     recordSet.execute("select id,name from HrmEducationLevel");
/*  137 */     while (recordSet.next()) {
/*  138 */       this.educationlevelMap.put(recordSet.getString(2), Integer.valueOf(recordSet.getInt(1)));
/*      */     }
/*      */     
/*  141 */     this.jobcallMap = new HashMap<>();
/*  142 */     recordSet.execute("select id,name from HrmJobCall");
/*  143 */     while (recordSet.next()) {
/*  144 */       this.jobcallMap.put(recordSet.getString(2), Integer.valueOf(recordSet.getInt(1)));
/*      */     }
/*      */     
/*  147 */     this.locationMap = new HashMap<>();
/*  148 */     recordSet.execute("select id,locationname from HrmLocations where countryid=1");
/*  149 */     while (recordSet.next()) {
/*  150 */       this.locationMap.put(recordSet.getString(2), Integer.valueOf(recordSet.getInt(1)));
/*      */     }
/*      */     
/*  153 */     this.usekindMap = new HashMap<>();
/*  154 */     recordSet.execute("select id,name from HrmUseKind");
/*  155 */     while (recordSet.next()) {
/*  156 */       this.usekindMap.put(recordSet.getString(2), Integer.valueOf(recordSet.getInt(1)));
/*      */     }
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List processMap(String keyField, Map hrMap, String importType) {
/*  170 */     return processMap(keyField, hrMap, importType, (HttpSession)null);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public List processMap(String keyField, Map hrMap, String importType, HttpSession session) {
/*  182 */     List<ImportLog> resultList = new ArrayList<>();
/*  183 */     RecordSet recordSet = new RecordSet();
/*      */ 
/*      */     
/*      */     try {
/*  187 */       String subCompanyName = "";
/*  188 */       String departmentName = "";
/*  189 */       int subcompanyid1 = 0;
/*  190 */       int departmentid = 0;
/*  191 */       String key = "";
/*  192 */       getKeyMap(keyField);
/*  193 */       this.keyField = keyField;
/*  194 */       int id = 0;
/*  195 */       if (session != null) {
/*  196 */         session.setAttribute("importStatus", "importing");
/*      */       }
/*  198 */       this.rtxService = new OrganisationCom();
/*  199 */       this.coremailapi = CoreMailAPI.getInstance();
/*      */       
/*  201 */       ResourceComInfo resourcecominfo = new ResourceComInfo();
/*  202 */       DepartmentComInfo departmentComInfo = new DepartmentComInfo();
/*  203 */       SubCompanyComInfo subCompanyComInfo = new SubCompanyComInfo();
/*  204 */       JobActivitiesComInfo jobActivitiesComInfo = new JobActivitiesComInfo();
/*  205 */       JobTitlesComInfo jobTitlesComInfo = new JobTitlesComInfo();
/*  206 */       JobGroupsComInfo jobGroupsComInfo = new JobGroupsComInfo();
/*  207 */       LocationComInfo locationComInfo = new LocationComInfo();
/*  208 */       UseKindComInfo useKindComInfo = new UseKindComInfo();
/*      */       
/*  210 */       Set keySet = hrMap.keySet();
/*  211 */       Object[] keyArray = keySet.toArray();
/*      */       
/*  213 */       Class<HrmResourceCust> hrmClass = HrmResourceCust.class;
/*  214 */       Class<HrmResourceVoCust> voClass = HrmResourceVoCust.class;
/*      */       
/*  216 */       String field = "pinyinlastname,ecology_pinyin_search,dsporder,swshrguid,id,subcompanyid1,departmentid,workcode,lastname,loginid,password,seclevel,sex,jobtitle,jobcall,joblevel,jobactivitydesc,managerid,assistantid,status,locationid,workroom,telephone,mobile,mobilecall,fax,email,systemlanguage,birthday,folk,nativeplace,regresidentplace,certificatenum,maritalstatus,policy,bememberdate,bepartydate,islabouunion,educationlevel,degree,healthinfo,height,weight,usekind,startdate,enddate,probationenddate,residentplace,homeaddress,tempresidentnumber,datefield1,datefield2,datefield3,datefield4,datefield5,textfield1,textfield2,textfield3,textfield4,textfield5,numberfield1,numberfield2,numberfield3,numberfield4,numberfield5,tinyintfield1,tinyintfield2,tinyintfield3,tinyintfield4,tinyintfield5";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  231 */       String[] fields = field.split(",");
/*      */       
/*  233 */       User user = null;
/*  234 */       if (session != null) {
/*  235 */         user = (User)session.getAttribute("weaver_user@bean");
/*      */       }
/*      */       
/*  238 */       int createrid = 1;
/*  239 */       int lastmodid = 1;
/*  240 */       if (user != null) {
/*  241 */         createrid = user.getUID();
/*  242 */         lastmodid = user.getUID();
/*      */       } 
/*      */       
/*  245 */       String createdate = this.date;
/*  246 */       String lastmoddate = this.date;
/*  247 */       String lastlogindate = this.date;
/*      */ 
/*      */ 
/*      */       
/*  251 */       for (int i = 0; i < keyArray.length; i++) {
/*  252 */         Object obj = keyArray[i];
/*  253 */         this.vo = (HrmResourceVoCust)hrMap.get(obj);
/*  254 */         key = (String)obj;
/*  255 */         HrmResourceCust hrm = new HrmResourceCust();
/*      */ 
/*      */         
/*      */         try {
/*  259 */           if (importType.equals("add")) {
/*      */             
/*  261 */             if (this.keyMap.get(key) != null) {
/*  262 */               resultList.add(createLog(this.vo, "创建", "失败#01", SystemEnv.getHtmlLabelName(83520, this.userlanguage)));
/*  263 */               if (session != null) {
/*  264 */                 session.setAttribute("resultList", resultList);
/*      */               }
/*      */               
/*      */               continue;
/*      */             } 
/*      */             
/*  270 */             if (this.keyMap.get(key) == null && 
/*  271 */               this.license.CkHrmnum() >= 0) {
/*  272 */               resultList.add(
/*  273 */                   createLog(this.vo, "创建", "失败#02", SystemEnv.getHtmlLabelName(83522, this.userlanguage)));
/*  274 */               if (session != null) {
/*  275 */                 session.setAttribute("resultList", resultList);
/*      */               }
/*      */ 
/*      */               
/*      */               continue;
/*      */             } 
/*      */             
/*  282 */             hrm.setSwshrguid(this.vo.getSwshrguid());
/*      */ 
/*      */             
/*  285 */             String str2 = this.vo.getSubcompanyid1();
/*  286 */             String tempSubCompanyName = "";
/*  287 */             if (str2 != null && !str2.equals("")) {
/*  288 */               if (!str2.equals(subCompanyName)) {
/*  289 */                 tempSubCompanyName = subCompanyName;
/*  290 */                 subCompanyName = str2;
/*  291 */                 subcompanyid1 = getSubCompanyId(subCompanyName);
/*      */               } 
/*      */               
/*  294 */               if (subcompanyid1 == -9) {
/*  295 */                 resultList.add(createLog(this.vo, "创建", "失败#03", SystemEnv.getErrorMsgName(56, this.userlanguage)));
/*  296 */                 if (session != null) {
/*  297 */                   session.setAttribute("resultList", resultList);
/*      */                 }
/*      */                 continue;
/*      */               } 
/*  301 */               if (subcompanyid1 == -2) {
/*  302 */                 resultList.add(
/*  303 */                     createLog(this.vo, "创建", "失败#04", SystemEnv.getHtmlLabelName(126274, this.userlanguage)));
/*  304 */                 if (session != null) {
/*  305 */                   session.setAttribute("resultList", resultList);
/*      */                 }
/*      */                 continue;
/*      */               } 
/*  309 */               if (subcompanyid1 == -1) {
/*  310 */                 resultList.add(
/*  311 */                     createLog(this.vo, "创建", "失败#05", SystemEnv.getHtmlLabelName(83524, this.userlanguage)));
/*  312 */                 if (session != null) {
/*  313 */                   session.setAttribute("resultList", resultList);
/*      */                 }
/*      */                 continue;
/*      */               } 
/*  317 */               if (subcompanyid1 == 0) {
/*  318 */                 resultList.add(
/*  319 */                     createLog(this.vo, "创建", "失败#06", SystemEnv.getHtmlLabelName(83536, this.userlanguage)));
/*  320 */                 if (session != null) {
/*  321 */                   session.setAttribute("resultList", resultList);
/*      */                 }
/*      */                 continue;
/*      */               } 
/*  325 */               if (StringUtil.parseToInt(this.license.getConcurrentFlag(), 0) != 1 && (
/*  326 */                 new HrmResourceManager()).noMore(String.valueOf(subcompanyid1))) {
/*  327 */                 resultList.add(createLog(this.vo, "创建", "失败#07", SystemEnv.getHtmlLabelName(83525, this.userlanguage)));
/*  328 */                 if (session != null) {
/*  329 */                   session.setAttribute("resultList", resultList);
/*      */                 }
/*      */                 
/*      */                 continue;
/*      */               } 
/*  334 */               hrm.setSubcompanyid1(new Integer(subcompanyid1));
/*      */             } else {
/*  336 */               resultList.add(createLog(this.vo, "创建", "失败#08", SystemEnv.getHtmlLabelName(83526, this.userlanguage)));
/*  337 */               if (session != null) {
/*  338 */                 session.setAttribute("resultList", resultList);
/*      */               }
/*      */               
/*      */               continue;
/*      */             } 
/*      */             
/*  344 */             String departmentNames = this.vo.getDepartmentid();
/*  345 */             if (departmentNames != null) {
/*  346 */               if (!str2.equals(tempSubCompanyName) || 
/*  347 */                 !departmentNames.equals(departmentName)) {
/*  348 */                 departmentName = departmentNames;
/*  349 */                 departmentid = getDeptId(departmentName, subcompanyid1);
/*      */               } 
/*  351 */               if (departmentid == 0) {
/*  352 */                 resultList.add(
/*  353 */                     createLog(this.vo, "创建", "失败#09", SystemEnv.getHtmlLabelName(83537, this.userlanguage)));
/*  354 */                 if (session != null) {
/*  355 */                   session.setAttribute("resultList", resultList);
/*      */                 }
/*      */                 continue;
/*      */               } 
/*  359 */               if (departmentid == -2) {
/*  360 */                 resultList.add(
/*  361 */                     createLog(this.vo, "创建", "失败#10", SystemEnv.getHtmlLabelName(126275, this.userlanguage)));
/*  362 */                 if (session != null) {
/*  363 */                   session.setAttribute("resultList", resultList);
/*      */                 }
/*      */                 continue;
/*      */               } 
/*  367 */               hrm.setDepartmentid(new Integer(departmentid));
/*      */             } else {
/*  369 */               resultList.add(createLog(this.vo, "创建", "失败#11", SystemEnv.getHtmlLabelName(83527, this.userlanguage)));
/*  370 */               if (session != null) {
/*  371 */                 session.setAttribute("resultList", resultList);
/*      */               }
/*      */               
/*      */               continue;
/*      */             } 
/*  376 */             if (this.vo.getLastname() == null) {
/*  377 */               resultList.add(createLog(this.vo, "创建", "失败#12", SystemEnv.getHtmlLabelName(83529, this.userlanguage)));
/*  378 */               if (session != null) {
/*  379 */                 session.setAttribute("resultList", resultList);
/*      */               }
/*      */ 
/*      */               
/*      */               continue;
/*      */             } 
/*      */             
/*  386 */             if (this.vo.getJobtitle() != null && this.vo.getJobactivityid() != null && this.vo.getJobgroupid() != null) {
/*  387 */               int jobtitle = getJobTitles(this.vo.getJobtitle(), this.vo.getJobactivityid(), this.vo.getJobgroupid());
/*  388 */               hrm.setJobtitle(new Integer(jobtitle));
/*      */             } else {
/*  390 */               resultList.add(createLog(this.vo, "创建", "失败#13", SystemEnv.getHtmlLabelName(83531, this.userlanguage)));
/*  391 */               if (session != null) {
/*  392 */                 session.setAttribute("resultList", resultList);
/*      */               }
/*      */               
/*      */               continue;
/*      */             } 
/*      */             
/*  398 */             if (this.vo.getManagerid() != null) {
/*  399 */               int managerid = 0;
/*  400 */               String managerstr = "";
/*  401 */               Map managerInfo = getManagerIdAndStr("", this.vo.getManagerid(), keyField);
/*  402 */               if (managerInfo != null) {
/*      */                 
/*  404 */                 managerid = (managerInfo.get("managerid") != null) ? ((Integer)managerInfo.get("managerid")).intValue() : 0;
/*      */ 
/*      */                 
/*  407 */                 managerstr = (managerInfo.get("managerstr") != null) ? (String)managerInfo.get("managerstr") : "";
/*      */               } 
/*      */               
/*  410 */               hrm.setManagerid(new Integer(managerid));
/*  411 */               hrm.setManagerstr(managerstr);
/*      */ 
/*      */               
/*  414 */               if (!this.vo.getManagerid().equals("") && managerid == 0) {
/*  415 */                 resultList.add(
/*  416 */                     createLog(this.vo, "创建", "失败#14", SystemEnv.getHtmlLabelName(83532, this.userlanguage)));
/*  417 */                 if (session != null) {
/*  418 */                   session.setAttribute("resultList", resultList);
/*      */                 }
/*      */                 
/*      */                 continue;
/*      */               } 
/*      */             } else {
/*  424 */               hrm.setManagerid(new Integer(0));
/*  425 */               hrm.setManagerstr("");
/*      */             } 
/*      */ 
/*      */             
/*  429 */             if (this.vo.getAssistantid() != null) {
/*  430 */               int assistantid = 0;
/*  431 */               assistantid = getAssistantid(this.vo.getAssistantid(), keyField);
/*  432 */               hrm.setAssistantid(new Integer(assistantid));
/*  433 */               if (!this.vo.getAssistantid().equals("") && assistantid == 0) {
/*  434 */                 resultList.add(createLog(this.vo, "创建", "失败#15", SystemEnv.getHtmlLabelName(24678, this.userlanguage)));
/*  435 */                 if (session != null) {
/*  436 */                   session.setAttribute("resultList", resultList);
/*      */                 }
/*      */                 
/*      */                 continue;
/*      */               } 
/*      */             } 
/*      */             
/*  443 */             if (this.vo.getLocationid() != null) {
/*  444 */               int locationid = getLocationid(this.vo.getLocationid());
/*  445 */               hrm.setLocationid(new Integer(locationid));
/*      */             } else {
/*  447 */               resultList.add(createLog(this.vo, "创建", "失败#16", SystemEnv.getHtmlLabelName(83533, this.userlanguage)));
/*  448 */               if (session != null) {
/*  449 */                 session.setAttribute("resultList", resultList);
/*      */               }
/*      */               
/*      */               continue;
/*      */             } 
/*  454 */             if (this.vo.getEmail() != null && 
/*  455 */               !"".equals(this.vo.getEmail())) {
/*  456 */               Pattern pattern = Pattern.compile("^([a-zA-Z0-9_\\-\\.]+)@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.)|(([a-zA-Z0-9\\-]+\\.)+))([a-zA-Z]{2,4}|[0-9]{1,3})(\\]?)$");
/*      */               
/*  458 */               Matcher matcher = pattern.matcher(this.vo.getEmail());
/*  459 */               if (!matcher.matches()) {
/*  460 */                 resultList.add(createLog(this.vo, "创建", "失败#17", SystemEnv.getHtmlLabelName(24570, this.userlanguage)));
/*  461 */                 if (session != null) {
/*  462 */                   session.setAttribute("resultList", resultList);
/*      */                 }
/*      */ 
/*      */                 
/*      */                 continue;
/*      */               } 
/*      */             } 
/*      */             
/*  470 */             String sex = "女".equals(this.vo.getSex()) ? "1" : "0";
/*  471 */             hrm.setSex(sex);
/*      */ 
/*      */             
/*  474 */             if (this.vo.getJobcall() != null) {
/*  475 */               int jobcall = getJobcall(this.vo.getJobcall());
/*  476 */               hrm.setJobcall(new Integer(jobcall));
/*      */             } 
/*      */             
/*  479 */             if (this.vo.getSystemlanguage() != null) {
/*  480 */               int systemlanguage = getSystemlanguage(this.vo.getSystemlanguage());
/*      */               
/*  482 */               if (systemlanguage == -1) {
/*  483 */                 resultList.add(
/*  484 */                     createLog(this.vo, "创建", "失败#18", SystemEnv.getHtmlLabelName(83534, this.userlanguage)));
/*  485 */                 if (session != null) {
/*  486 */                   session.setAttribute("resultList", resultList);
/*      */                 }
/*      */                 continue;
/*      */               } 
/*  490 */               if (systemlanguage == 0) {
/*  491 */                 resultList.add(
/*  492 */                     createLog(this.vo, "创建", "失败#19", SystemEnv.getHtmlLabelName(84810, this.userlanguage)));
/*  493 */                 if (session != null) {
/*  494 */                   session.setAttribute("resultList", resultList);
/*      */                 }
/*      */                 continue;
/*      */               } 
/*  498 */               hrm.setSystemlanguage(new Integer(systemlanguage));
/*      */             } else {
/*  500 */               hrm.setSystemlanguage(new Integer(7));
/*      */             } 
/*      */ 
/*      */ 
/*      */             
/*  505 */             String maritalstatus = "已婚".equals(this.vo.getMaritalstatus()) ? "1" : ("离异".equals(this.vo.getMaritalstatus()) ? "2" : "0");
/*  506 */             hrm.setMaritalstatus(maritalstatus);
/*      */ 
/*      */             
/*  509 */             if (!"".equals(Util.null2String(this.vo.getStatus()))) {
/*  510 */               int status = getStatus(this.vo.getStatus());
/*  511 */               hrm.setStatus(new Integer(status));
/*      */             } else {
/*  513 */               hrm.setStatus(new Integer(1));
/*      */             } 
/*      */ 
/*      */             
/*  517 */             if (this.vo.getEducationlevel() != null) {
/*  518 */               int educationlevel = getEducationlevel(this.vo.getEducationlevel());
/*  519 */               hrm.setEducationlevel(new Integer(educationlevel));
/*      */             } 
/*      */ 
/*      */             
/*  523 */             String islabouunion = "是".equals(this.vo.getIslabouunion()) ? "1" : "0";
/*  524 */             hrm.setIslabouunion(islabouunion);
/*      */ 
/*      */             
/*  527 */             if (this.vo.getHealthinfo() != null) {
/*  528 */               String healthinfo = getHealthinfo(this.vo.getHealthinfo());
/*  529 */               hrm.setHealthinfo(healthinfo);
/*      */             } else {
/*  531 */               hrm.setHealthinfo("0");
/*      */             } 
/*      */ 
/*      */             
/*  535 */             if (isInteger(this.vo.getSeclevel())) {
/*  536 */               int seclevel = this.vo.getSeclevel().equals("") ? 0 : Integer.parseInt(this.vo.getSeclevel());
/*  537 */               hrm.setSeclevel(new Short((short)seclevel));
/*      */             } else {
/*  539 */               hrm.setSeclevel(new Short((short)0));
/*      */             } 
/*      */ 
/*      */             
/*  543 */             if (isInteger(this.vo.getJoblevel())) {
/*  544 */               int joblevel = this.vo.getJoblevel().equals("") ? 0 : Integer.parseInt(this.vo.getJoblevel());
/*  545 */               hrm.setJoblevel(new Short((short)joblevel));
/*      */             } else {
/*  547 */               hrm.setJoblevel(new Short((short)0));
/*      */             } 
/*      */             
/*  550 */             if (this.vo.getUsekind() != null) {
/*  551 */               int usekind = getUseKind(this.vo.getUsekind());
/*  552 */               hrm.setUsekind(Integer.valueOf(usekind));
/*      */             } else {
/*  554 */               hrm.setUsekind(Integer.valueOf(0));
/*      */             } 
/*      */             
/*  557 */             if (this.vo.getLoginid() != null) {
/*  558 */               this.license.reloadLicenseInfo();
/*  559 */               if (this.license.CkHrmnum() >= 0) {
/*  560 */                 resultList.add(createLog(this.vo, "创建", "失败#20", SystemEnv.getHtmlLabelName(83522, this.userlanguage)));
/*  561 */                 if (session != null) {
/*  562 */                   session.setAttribute("resultList", resultList);
/*      */                 }
/*      */                 
/*      */                 continue;
/*      */               } 
/*      */             } 
/*      */             
/*  569 */             recordSet.executeProc("HrmResourceMaxId_Get", "");
/*  570 */             recordSet.next();
/*  571 */             id = recordSet.getInt(1);
/*  572 */             hrm.setId(new Integer(id));
/*      */ 
/*      */             
/*  575 */             String password_tmp = Util.null2String(this.vo.getPassword()).trim();
/*  576 */             if ("".equals(password_tmp)) {
/*  577 */               password_tmp = "1";
/*      */             }
/*  579 */             String password = Util.getEncrypt(password_tmp);
/*  580 */             hrm.setPassword(password);
/*      */             
/*  582 */             boolean flag = true;
/*      */ 
/*      */             
/*  585 */             String insertStr = "insert into HrmResource(";
/*  586 */             String insertFields = "";
/*  587 */             String insertValues = "";
/*      */             
/*  589 */             for (int k = 0; k < fields.length; k++) {
/*  590 */               Field hrmField = hrmClass.getDeclaredField(fields[k]);
/*  591 */               Field voField = voClass.getDeclaredField(fields[k]);
/*      */               
/*  593 */               String hrmFieldType = hrmField.getType().getName();
/*  594 */               String voFieldType = voField.getType().getName();
/*      */               
/*  596 */               hrmField.setAccessible(true);
/*  597 */               voField.setAccessible(true);
/*      */ 
/*      */               
/*  600 */               if (hrmField.get(hrm) != null) {
/*  601 */                 if (hrmFieldType.endsWith("String")) {
/*  602 */                   insertFields = insertFields + fields[k] + ",";
/*  603 */                   insertValues = insertValues + "'" + hrmField.get(hrm) + "',";
/*  604 */                 } else if (hrmFieldType.endsWith("Integer") || hrmFieldType.endsWith("Short") || hrmFieldType
/*  605 */                   .endsWith("Float")) {
/*  606 */                   insertFields = insertFields + fields[k] + ",";
/*  607 */                   insertValues = insertValues + "" + hrmField.get(hrm) + ",";
/*      */                 } 
/*  609 */               } else if (voField.get(this.vo) != null) {
/*  610 */                 if (voFieldType.endsWith("String")) {
/*  611 */                   insertFields = insertFields + fields[k] + ",";
/*  612 */                   insertValues = insertValues + "'" + voField.get(this.vo) + "',";
/*  613 */                 } else if (hrmFieldType.endsWith("Integer") || hrmFieldType.endsWith("Short") || hrmFieldType
/*  614 */                   .endsWith("Float")) {
/*  615 */                   insertFields = insertFields + fields[k] + ",";
/*  616 */                   insertValues = insertValues + "" + voField.get(this.vo) + ",";
/*      */                 } 
/*      */               } 
/*      */             } 
/*  620 */             ChgPasswdReminder cpr = new ChgPasswdReminder();
/*  621 */             RemindSettings hrmsettings = cpr.getRemindSettings();
/*      */ 
/*      */             
/*  624 */             String accountType = "1".equals(this.vo.getResidentplace()) ? "0" : "1";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */             
/*  632 */             insertStr = insertStr + insertFields + "createrid,createdate,lastmodid,lastmoddate,lastlogindate,managerstr,accounttype,mobileshowtype ," + DbFunctionUtil.getInsertColumnSql() + ") values(" + insertValues + createrid + ",'" + createdate + "'," + lastmodid + ",'" + lastmoddate + "','" + lastlogindate + "','" + hrm.getManagerstr() + "'," + accountType + "," + Util.getIntValue(hrmsettings.getMobileShowTypeDefault(), 0) + " ," + DbFunctionUtil.getInsertColumnValueSql(recordSet.getDBType(), lastmodid) + ")";
/*      */             
/*  634 */             boolean resourceInsertFlag = true;
/*  635 */             if (!execSql(insertStr)) {
/*  636 */               flag = false;
/*  637 */               resourceInsertFlag = false;
/*      */             } 
/*  639 */             if (resourceInsertFlag) {
/*  640 */               if (!updateBaseData(this.vo.getBaseFields(), this.vo.getBaseFieldsValue(), id))
/*      */               {
/*  642 */                 flag = false;
/*      */               }
/*  644 */               if (!updatePersonData(this.vo.getPersonFields(), this.vo.getPersonFieldsValue(), id))
/*      */               {
/*  646 */                 flag = false;
/*      */               }
/*  648 */               if (!updateWorkData(this.vo.getWorkFields(), this.vo.getWorkFieldsValue(), id))
/*      */               {
/*  650 */                 flag = false;
/*      */               }
/*      */             } 
/*      */ 
/*      */             
/*  655 */             if (flag) {
/*      */               
/*  657 */               HrmUserSettingManager.checkUserSettingInit(id);
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */               
/*  666 */               String para = "" + id + this.separator + hrm.getManagerid() + this.separator + hrm.getDepartmentid() + this.separator + hrm.getSubcompanyid1() + this.separator + hrm.getSeclevel() + this.separator + hrm.getManagerstr();
/*  667 */               recordSet.executeProc("HrmResource_Trigger_Insert", para);
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */               
/*  679 */               String sql_1 = "insert into HrmInfoStatus (itemid,hrmid,status) values(1," + id + ",1)";
/*  680 */               recordSet.executeSql(sql_1);
/*  681 */               String sql_2 = "insert into HrmInfoStatus (itemid,hrmid) values(2," + id + ")";
/*  682 */               recordSet.executeSql(sql_2);
/*  683 */               String sql_3 = "insert into HrmInfoStatus (itemid,hrmid) values(3," + id + ")";
/*  684 */               recordSet.executeSql(sql_3);
/*      */               
/*  686 */               String sql_10 = "insert into HrmInfoStatus (itemid,hrmid) values(10," + id + ")";
/*  687 */               recordSet.executeSql(sql_10);
/*      */             } 
/*      */ 
/*      */ 
/*      */             
/*  692 */             if (flag) {
/*  693 */               resultList.add(createLog(this.vo, "创建", "成功", ""));
/*  694 */               if (session != null) {
/*  695 */                 session.setAttribute("resultList", resultList);
/*      */               }
/*  697 */               if (!"".equals(this.vo.getLoginid())) {
/*  698 */                 this.rtxService.addUser(id);
/*  699 */                 CoreMailAPI.synUser("" + id);
/*      */               } 
/*      */             } else {
/*  702 */               resultList.add(createLog(this.vo, "创建", "失败#21", ""));
/*  703 */               if (session != null) {
/*  704 */                 session.setAttribute("resultList", resultList);
/*      */               }
/*      */             } 
/*      */             
/*      */             continue;
/*      */           } 
/*  710 */           writeLog("key ===> " + key);
/*      */           
/*  712 */           if (this.keyMap.get(key) == null) {
/*      */ 
/*      */             
/*  715 */             RecordSet recordSet1 = new RecordSet();
/*  716 */             String hrid = key.split("\\|")[0] + "|";
/*  717 */             String qry = "select id,swshrguid from hrmresource where (accounttype is null or accounttype = 0) and loginid like '" + hrid + "%'";
/*  718 */             recordSet1.execute(qry);
/*  719 */             if (recordSet1.next()) {
/*  720 */               int userid = recordSet1.getInt("id");
/*  721 */               this.keyMap.put(key, Integer.valueOf(userid));
/*      */             } else {
/*  723 */               resultList.add(createLog(this.vo, "更新", "失败#30", SystemEnv.getHtmlLabelName(83535, this.userlanguage)));
/*  724 */               if (session != null) {
/*  725 */                 session.setAttribute("resultList", resultList);
/*      */               }
/*      */               
/*      */               continue;
/*      */             } 
/*      */           } 
/*      */           
/*  732 */           String hrmId = String.valueOf(this.keyMap.get(key));
/*      */           
/*  734 */           writeLog("hrmId ===> " + hrmId);
/*      */ 
/*      */           
/*  737 */           String subCompanyNames = this.vo.getSubcompanyid1();
/*  738 */           writeLog("subCompanyNames ===> " + subCompanyNames);
/*  739 */           boolean issameSub = true;
/*  740 */           if (!"".equals(Util.null2String(subCompanyNames)))
/*  741 */           { if (!subCompanyNames.equals(subCompanyName)) {
/*  742 */               subCompanyName = subCompanyNames;
/*  743 */               subcompanyid1 = getSubCompanyId(subCompanyName);
/*  744 */               writeLog("getSubCompanyId ===> " + subcompanyid1);
/*  745 */               issameSub = false;
/*      */             } 
/*      */ 
/*      */             
/*  749 */             if (subcompanyid1 == -9) {
/*  750 */               resultList.add(createLog(this.vo, "更新", "失败#31", SystemEnv.getErrorMsgName(56, this.userlanguage)));
/*  751 */               if (session != null) {
/*  752 */                 session.setAttribute("resultList", resultList);
/*      */               }
/*      */               
/*      */               continue;
/*      */             } 
/*  757 */             if (subcompanyid1 == -2) {
/*  758 */               resultList.add(createLog(this.vo, "创建", "失败#32", SystemEnv.getHtmlLabelName(126274, this.userlanguage)));
/*  759 */               if (session != null) {
/*  760 */                 session.setAttribute("resultList", resultList);
/*      */               }
/*      */               
/*      */               continue;
/*      */             } 
/*  765 */             if (subcompanyid1 == 0 || subcompanyid1 == -1) {
/*  766 */               resultList.add(createLog(this.vo, "更新", "失败#33", (subcompanyid1 == 0) ? 
/*  767 */                     SystemEnv.getHtmlLabelName(83536, this.userlanguage) : 
/*  768 */                     SystemEnv.getHtmlLabelName(83524, this.userlanguage)));
/*  769 */               if (session != null) {
/*  770 */                 session.setAttribute("resultList", resultList);
/*      */               }
/*      */               
/*      */               continue;
/*      */             } 
/*      */             
/*  776 */             this.license.CkHrmnum();
/*  777 */             if (StringUtil.parseToInt(this.license.getConcurrentFlag(), 0) != 1 && 
/*  778 */               !issameSub && (new HrmResourceManager()).noMore(String.valueOf(subcompanyid1))) {
/*  779 */               resultList.add(createLog(this.vo, "更新", "失败#34", SystemEnv.getHtmlLabelName(83525, this.userlanguage)));
/*  780 */               if (session != null) {
/*  781 */                 session.setAttribute("resultList", resultList);
/*      */               }
/*      */               
/*      */               continue;
/*      */             } 
/*  786 */             hrm.setSubcompanyid1(new Integer(subcompanyid1)); }
/*      */           else
/*  788 */           { String subCompanyID = resourcecominfo.getSubCompanyID(hrmId);
/*  789 */             int intSubcompanyid1 = (new Integer(Integer.parseInt(subCompanyID))).intValue();
/*  790 */             if (intSubcompanyid1 == -9)
/*  791 */             { resultList.add(createLog(this.vo, "更新", "失败#35", SystemEnv.getErrorMsgName(56, this.userlanguage)));
/*  792 */               if (session != null) {
/*  793 */                 session.setAttribute("resultList", resultList);
/*      */               
/*      */               }
/*      */                }
/*      */             
/*  798 */             else if (intSubcompanyid1 == -2)
/*  799 */             { resultList.add(createLog(this.vo, "创建", "失败#36", SystemEnv.getHtmlLabelName(126274, this.userlanguage)));
/*  800 */               if (session != null) {
/*  801 */                 session.setAttribute("resultList", resultList);
/*      */               
/*      */               }
/*      */                }
/*      */             
/*  806 */             else if (intSubcompanyid1 == 0 || intSubcompanyid1 == -1)
/*  807 */             { resultList.add(createLog(this.vo, "更新", "失败#37", (intSubcompanyid1 == 0) ? 
/*  808 */                     SystemEnv.getHtmlLabelName(83536, this.userlanguage) : 
/*  809 */                     SystemEnv.getHtmlLabelName(83524, this.userlanguage)));
/*  810 */               if (session != null) {
/*  811 */                 session.setAttribute("resultList", resultList);
/*      */               } }
/*      */             
/*      */             else
/*      */             
/*  816 */             { hrm.setSubcompanyid1(Integer.valueOf(intSubcompanyid1));
/*  817 */               String subIdStr = subCompanyComInfo.getAllSupCompany(subCompanyID);
/*  818 */               subCompanyNames = "";
/*  819 */               if (!subIdStr.equals("")) {
/*  820 */                 String[] subIds = subIdStr.split(",");
/*  821 */                 for (int n = 0; n < subIds.length; n++) {
/*  822 */                   subCompanyNames = subCompanyNames + subCompanyComInfo.getSubCompanyname(subIds[n]) + ">";
/*      */                 }
/*      */               } 
/*  825 */               this.vo.setSubcompanyid1(subCompanyNames + subCompanyComInfo.getSubCompanyname(subCompanyID));
/*      */ 
/*      */ 
/*      */ 
/*      */               
/*  830 */               String departmentNames = this.vo.getDepartmentid();
/*  831 */               writeLog("departmentNames ===> ", departmentNames); }  continue; }  String str1 = this.vo.getDepartmentid(); writeLog("departmentNames ===> ", str1);
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/*      */         }
/* 1332 */         catch (Exception e) {
/*      */           
/* 1334 */           writeLog(e);
/* 1335 */           resultList.add(createLog(this.vo, "更新", "失败#50", SystemEnv.getHtmlLabelName(83548, this.userlanguage)));
/* 1336 */           if (session != null) {
/* 1337 */             session.setAttribute("resultList", resultList);
/*      */           }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */           
/*      */           continue;
/*      */         } 
/*      */       } 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/* 1370 */       HrmTlevelManager.setUpdate();
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */       
/* 1378 */       resourcecominfo.removeResourceCache();
/* 1379 */       departmentComInfo.removeCompanyCache();
/* 1380 */       subCompanyComInfo.removeCompanyCache();
/* 1381 */       jobActivitiesComInfo.removeJobActivitiesCache();
/* 1382 */       jobTitlesComInfo.removeJobTitlesCache();
/* 1383 */       jobGroupsComInfo.removeCompanyCache();
/* 1384 */       locationComInfo.removeLocationCache();
/* 1385 */       useKindComInfo.removeUseKindCache();
/*      */       
/* 1387 */       (new AppDetachComInfo()).initSubDepAppData();
/*      */ 
/*      */ 
/*      */ 
/*      */       
/* 1392 */       writeImportLog(resultList);
/* 1393 */       if (session != null) {
/* 1394 */         session.setAttribute("logFile", this.logFile);
/*      */       }
/* 1396 */       return resultList;
/* 1397 */     } catch (Exception e) {
/* 1398 */       writeLog(e);
/* 1399 */       return resultList;
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean addBaseData(String baseFild, String baseValue, int id) {
/* 1413 */     if (baseFild == null || baseFild.equals("")) {
/* 1414 */       return true;
/*      */     }
/* 1416 */     String[] baseValues = baseValue.split(";");
/* 1417 */     String[] baseFields = baseFild.split(",");
/* 1418 */     String fielddbType = "";
/* 1419 */     String sql = "";
/* 1420 */     RecordSet recordSet = new RecordSet();
/*      */     try {
/* 1422 */       if (this.baseTypeMap == null) {
/* 1423 */         this.baseTypeMap = new HashMap<>();
/* 1424 */         String baseSql = "select t1.fieldid,t1.hrm_fieldlable,t1.ismand,t2.fielddbtype,t2.fieldhtmltype,t2.type,t1.dmlurl from cus_formfield t1, cus_formdict t2 where t1.scope='HrmCustomFieldByInfoType' and t1.scopeid=-1 and t1.fieldid=t2.id order by t1.fieldorder";
/* 1425 */         recordSet.execute(baseSql);
/* 1426 */         while (recordSet.next()) {
/* 1427 */           String fieldid = Util.null2String(recordSet.getString("fieldid"));
/* 1428 */           String fielddbtype = Util.null2String(recordSet.getString("fielddbtype"));
/* 1429 */           String fieldhtmltype = Util.null2String(recordSet.getString("fieldhtmltype"));
/* 1430 */           String type = Util.null2String(recordSet.getString("type"));
/* 1431 */           String dmlurl = Util.null2String(recordSet.getString("dmlurl"));
/*      */           
/* 1433 */           JSONObject jsonObject = new JSONObject();
/* 1434 */           jsonObject.put("fieldid", fieldid);
/* 1435 */           jsonObject.put("fielddbtype", fielddbtype);
/* 1436 */           jsonObject.put("fieldhtmltype", fieldhtmltype);
/* 1437 */           jsonObject.put("type", type);
/* 1438 */           jsonObject.put("dmlurl", dmlurl);
/* 1439 */           this.baseTypeMap.put("field" + recordSet.getInt("fieldid"), jsonObject);
/*      */         } 
/*      */       } 
/*      */       
/* 1443 */       sql = "insert into cus_fielddata  ";
/* 1444 */       String valueStr = "";
/* 1445 */       for (int i = 0; i < baseFields.length; i++) {
/* 1446 */         JSONObject jsonObject = this.baseTypeMap.get(baseFields[i]);
/* 1447 */         fielddbType = jsonObject.getString("fielddbtype");
/* 1448 */         jsonObject.put("fieldvalue", baseValues[i]);
/* 1449 */         String fieldvalue = HrmFieldManager.getReallyFieldvalue(jsonObject);
/* 1450 */         if (fielddbType.startsWith("char") || fielddbType.startsWith("varchar") || fielddbType
/* 1451 */           .startsWith("text")) {
/* 1452 */           valueStr = valueStr + ",'" + (!fieldvalue.equals("") ? fieldvalue : "") + "'";
/*      */         } else {
/* 1454 */           valueStr = valueStr + "," + (!fieldvalue.equals("") ? fieldvalue : "NULL");
/*      */         } 
/*      */       } 
/* 1457 */       valueStr = valueStr.substring(1);
/* 1458 */       sql = sql + "(scope,scopeid,id," + baseFild + ") values('HrmCustomFieldByInfoType'," + -1 + "," + id + "," + valueStr + ")";
/*      */     }
/* 1460 */     catch (Exception e) {
/* 1461 */       writeLog(e);
/*      */     } 
/* 1463 */     return recordSet.execute(sql);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean updateBaseData(String baseFild, String baseValue, int id) {
/* 1475 */     if (baseFild == null || baseFild.equals("")) {
/* 1476 */       return true;
/*      */     }
/*      */ 
/*      */     
/* 1480 */     RecordSet recordSet = new RecordSet();
/* 1481 */     String checkbaseInfo = "select id from cus_fielddata where scope='HrmCustomFieldByInfoType' and scopeid=-1 and id=" + id;
/* 1482 */     recordSet.execute(checkbaseInfo);
/* 1483 */     if (!recordSet.next()) {
/* 1484 */       return addBaseData(baseFild, baseValue, id);
/*      */     }
/*      */     
/* 1487 */     String[] baseValues = baseValue.split(";");
/* 1488 */     String[] baseFields = baseFild.split(",");
/* 1489 */     String fielddbType = "";
/* 1490 */     String sql = "";
/*      */     try {
/* 1492 */       if (this.baseTypeMap == null) {
/* 1493 */         this.baseTypeMap = new HashMap<>();
/* 1494 */         String personSql = "select t1.fieldid,t1.hrm_fieldlable,t1.ismand,t2.fielddbtype,t2.fieldhtmltype,t2.type,t1.dmlurl from cus_formfield t1, cus_formdict t2 where t1.scope='HrmCustomFieldByInfoType' and t1.scopeid=-1 and t1.fieldid=t2.id order by t1.fieldorder";
/* 1495 */         recordSet.execute(personSql);
/* 1496 */         while (recordSet.next()) {
/* 1497 */           String fieldid = Util.null2String(recordSet.getString("fieldid"));
/* 1498 */           String fielddbtype = Util.null2String(recordSet.getString("fielddbtype"));
/* 1499 */           String fieldhtmltype = Util.null2String(recordSet.getString("fieldhtmltype"));
/* 1500 */           String type = Util.null2String(recordSet.getString("type"));
/* 1501 */           String dmlurl = Util.null2String(recordSet.getString("dmlurl"));
/*      */           
/* 1503 */           JSONObject jsonObject = new JSONObject();
/* 1504 */           jsonObject.put("fieldid", fieldid);
/* 1505 */           jsonObject.put("fielddbtype", fielddbtype);
/* 1506 */           jsonObject.put("fieldhtmltype", fieldhtmltype);
/* 1507 */           jsonObject.put("type", type);
/* 1508 */           jsonObject.put("dmlurl", dmlurl);
/* 1509 */           this.baseTypeMap.put("field" + recordSet.getInt("fieldid"), jsonObject);
/*      */         } 
/*      */       } 
/*      */       
/* 1513 */       sql = "update cus_fielddata set ";
/* 1514 */       String setStr = "";
/* 1515 */       for (int i = 0; i < baseFields.length; i++) {
/* 1516 */         JSONObject jsonObject = this.baseTypeMap.get(baseFields[i]);
/* 1517 */         fielddbType = jsonObject.getString("fielddbtype");
/* 1518 */         jsonObject.put("fieldvalue", baseValues[i]);
/* 1519 */         String fieldvalue = HrmFieldManager.getReallyFieldvalue(jsonObject);
/* 1520 */         if (fieldvalue.startsWith(",")) {
/* 1521 */           fieldvalue = fieldvalue.substring(1, fieldvalue.length());
/*      */         }
/* 1523 */         if (fieldvalue.endsWith(",")) {
/* 1524 */           fieldvalue = fieldvalue.substring(0, fieldvalue.length() - 1);
/*      */         }
/* 1526 */         if (fielddbType.startsWith("char") || fielddbType.startsWith("varchar") || fielddbType
/* 1527 */           .startsWith("text")) {
/* 1528 */           setStr = setStr + "," + baseFields[i] + "='" + (!fieldvalue.equals("") ? fieldvalue : "") + "'";
/*      */         } else {
/* 1530 */           setStr = setStr + "," + baseFields[i] + "=" + (!fieldvalue.equals("") ? fieldvalue : "NULL");
/*      */         } 
/*      */       } 
/* 1533 */       sql = sql + setStr.substring(1) + " where scope='HrmCustomFieldByInfoType' and scopeid=-1 and id=" + id;
/* 1534 */     } catch (Exception e) {
/* 1535 */       writeLog(e);
/*      */     } 
/* 1537 */     return recordSet.execute(sql);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean addPersonData(String personFild, String personValue, int id) {
/* 1549 */     if (personFild == null || personFild.equals("")) {
/* 1550 */       return true;
/*      */     }
/* 1552 */     String[] personValues = personValue.split(";");
/* 1553 */     String[] personFields = personFild.split(",");
/* 1554 */     String fielddbType = "";
/* 1555 */     String sql = "";
/* 1556 */     RecordSet recordSet = new RecordSet();
/*      */     try {
/* 1558 */       if (this.personTypeMap == null) {
/* 1559 */         this.personTypeMap = new HashMap<>();
/* 1560 */         String personSql = "select t1.fieldid,t1.hrm_fieldlable,t1.ismand,t2.fielddbtype,t2.fieldhtmltype,t2.type,t1.dmlurl from cus_formfield t1, cus_formdict t2 where t1.scope='HrmCustomFieldByInfoType' and t1.scopeid=1 and t1.fieldid=t2.id order by t1.fieldorder";
/* 1561 */         recordSet.execute(personSql);
/* 1562 */         while (recordSet.next()) {
/* 1563 */           String fieldid = Util.null2String(recordSet.getString("fieldid"));
/* 1564 */           String fielddbtype = Util.null2String(recordSet.getString("fielddbtype"));
/* 1565 */           String fieldhtmltype = Util.null2String(recordSet.getString("fieldhtmltype"));
/* 1566 */           String type = Util.null2String(recordSet.getString("type"));
/* 1567 */           String dmlurl = Util.null2String(recordSet.getString("dmlurl"));
/*      */           
/* 1569 */           JSONObject jsonObject = new JSONObject();
/* 1570 */           jsonObject.put("fieldid", fieldid);
/* 1571 */           jsonObject.put("fielddbtype", fielddbtype);
/* 1572 */           jsonObject.put("fieldhtmltype", fieldhtmltype);
/* 1573 */           jsonObject.put("type", type);
/* 1574 */           jsonObject.put("dmlurl", dmlurl);
/* 1575 */           this.personTypeMap.put("field" + recordSet.getInt("fieldid"), jsonObject);
/*      */         } 
/*      */       } 
/*      */       
/* 1579 */       sql = "insert into cus_fielddata  ";
/* 1580 */       String valueStr = "";
/* 1581 */       for (int i = 0; i < personFields.length; i++) {
/* 1582 */         JSONObject jsonObject = this.personTypeMap.get(personFields[i]);
/* 1583 */         fielddbType = jsonObject.getString("fielddbtype");
/* 1584 */         jsonObject.put("fieldvalue", personValues[i]);
/* 1585 */         String fieldvalue = HrmFieldManager.getReallyFieldvalue(jsonObject);
/* 1586 */         if (fielddbType.startsWith("char") || fielddbType.startsWith("varchar") || fielddbType
/* 1587 */           .startsWith("text")) {
/* 1588 */           valueStr = valueStr + ",'" + (!fieldvalue.equals("") ? fieldvalue : "") + "'";
/*      */         } else {
/* 1590 */           valueStr = valueStr + "," + (!fieldvalue.equals("") ? fieldvalue : "NULL");
/*      */         } 
/*      */       } 
/* 1593 */       valueStr = valueStr.substring(1);
/* 1594 */       sql = sql + "(scope,scopeid,id," + personFild + ") values('HrmCustomFieldByInfoType'," + '\001' + "," + id + "," + valueStr + ")";
/*      */     }
/* 1596 */     catch (Exception e) {
/* 1597 */       writeLog(e);
/*      */     } 
/* 1599 */     return recordSet.execute(sql);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean updatePersonData(String personFild, String personValue, int id) {
/* 1611 */     if (personFild == null || personFild.equals("")) {
/* 1612 */       return true;
/*      */     }
/*      */     
/* 1615 */     String checkWorkInfo = "select id from cus_fielddata where scope='HrmCustomFieldByInfoType' and scopeid=1 and id=" + id;
/*      */     
/* 1617 */     RecordSet recordSet = new RecordSet();
/* 1618 */     recordSet.execute(checkWorkInfo);
/* 1619 */     if (!recordSet.next()) {
/* 1620 */       return addPersonData(personFild, personValue, id);
/*      */     }
/*      */     
/* 1623 */     String[] personValues = personValue.split(";");
/* 1624 */     String[] personFields = personFild.split(",");
/* 1625 */     String fielddbType = "";
/* 1626 */     String sql = "";
/*      */     try {
/* 1628 */       if (this.personTypeMap == null) {
/* 1629 */         this.personTypeMap = new HashMap<>();
/* 1630 */         String personSql = "select t1.fieldid,t1.hrm_fieldlable,t1.ismand,t2.fielddbtype,t2.fieldhtmltype,t2.type,t1.dmlurl from cus_formfield t1, cus_formdict t2 where t1.scope='HrmCustomFieldByInfoType' and t1.scopeid=1 and t1.fieldid=t2.id order by t1.fieldorder";
/* 1631 */         recordSet.execute(personSql);
/* 1632 */         while (recordSet.next()) {
/* 1633 */           String fieldid = Util.null2String(recordSet.getString("fieldid"));
/* 1634 */           String fielddbtype = Util.null2String(recordSet.getString("fielddbtype"));
/* 1635 */           String fieldhtmltype = Util.null2String(recordSet.getString("fieldhtmltype"));
/* 1636 */           String type = Util.null2String(recordSet.getString("type"));
/* 1637 */           String dmlurl = Util.null2String(recordSet.getString("dmlurl"));
/*      */           
/* 1639 */           JSONObject jsonObject = new JSONObject();
/* 1640 */           jsonObject.put("fieldid", fieldid);
/* 1641 */           jsonObject.put("fielddbtype", fielddbtype);
/* 1642 */           jsonObject.put("fieldhtmltype", fieldhtmltype);
/* 1643 */           jsonObject.put("type", type);
/* 1644 */           jsonObject.put("dmlurl", dmlurl);
/* 1645 */           this.personTypeMap.put("field" + recordSet.getInt("fieldid"), jsonObject);
/*      */         } 
/*      */       } 
/*      */       
/* 1649 */       sql = "update cus_fielddata set ";
/* 1650 */       String setStr = "";
/* 1651 */       for (int i = 0; i < personFields.length; i++) {
/* 1652 */         JSONObject jsonObject = this.personTypeMap.get(personFields[i]);
/* 1653 */         fielddbType = jsonObject.getString("fielddbtype");
/* 1654 */         jsonObject.put("fieldvalue", personValues[i]);
/* 1655 */         String fieldvalue = HrmFieldManager.getReallyFieldvalue(jsonObject);
/* 1656 */         if (fieldvalue.startsWith(",")) {
/* 1657 */           fieldvalue = fieldvalue.substring(1, fieldvalue.length());
/*      */         }
/* 1659 */         if (fieldvalue.endsWith(",")) {
/* 1660 */           fieldvalue = fieldvalue.substring(0, fieldvalue.length() - 1);
/*      */         }
/* 1662 */         if (fielddbType.startsWith("char") || fielddbType.startsWith("varchar") || fielddbType
/* 1663 */           .startsWith("text")) {
/* 1664 */           setStr = setStr + "," + personFields[i] + "='" + (!fieldvalue.equals("") ? fieldvalue : "") + "'";
/*      */         } else {
/* 1666 */           setStr = setStr + "," + personFields[i] + "=" + (!fieldvalue.equals("") ? fieldvalue : "NULL");
/*      */         } 
/*      */       } 
/* 1669 */       sql = sql + setStr.substring(1) + " where scope='HrmCustomFieldByInfoType' and scopeid=1 and id=" + id;
/* 1670 */     } catch (Exception e) {
/* 1671 */       writeLog(e);
/*      */     } 
/* 1673 */     return recordSet.execute(sql);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean addWorkData(String workField, String workValue, int id) {
/* 1685 */     if (workField == null || workField.equals("")) {
/* 1686 */       return true;
/*      */     }
/* 1688 */     String[] workValues = workValue.split(";");
/* 1689 */     String[] workFields = workField.split(",");
/* 1690 */     String fielddbType = "";
/* 1691 */     String sql = "";
/* 1692 */     RecordSet recordSet = new RecordSet();
/*      */     try {
/* 1694 */       if (this.workTypeMap == null) {
/* 1695 */         this.workTypeMap = new HashMap<>();
/* 1696 */         String workSql = "select t1.fieldid,t1.hrm_fieldlable,t1.ismand,t2.fielddbtype,t2.fieldhtmltype,t2.type,t1.dmlurl from cus_formfield t1, cus_formdict t2 where t1.scope='HrmCustomFieldByInfoType' and t1.scopeid=3 and t1.fieldid=t2.id order by t1.fieldorder";
/* 1697 */         recordSet.execute(workSql);
/* 1698 */         while (recordSet.next()) {
/* 1699 */           String fieldid = Util.null2String(recordSet.getString("fieldid"));
/* 1700 */           String fielddbtype = Util.null2String(recordSet.getString("fielddbtype"));
/* 1701 */           String fieldhtmltype = Util.null2String(recordSet.getString("fieldhtmltype"));
/* 1702 */           String type = Util.null2String(recordSet.getString("type"));
/* 1703 */           String dmlurl = Util.null2String(recordSet.getString("dmlurl"));
/*      */           
/* 1705 */           JSONObject jsonObject = new JSONObject();
/* 1706 */           jsonObject.put("fieldid", fieldid);
/* 1707 */           jsonObject.put("fielddbtype", fielddbtype);
/* 1708 */           jsonObject.put("fieldhtmltype", fieldhtmltype);
/* 1709 */           jsonObject.put("type", type);
/* 1710 */           jsonObject.put("dmlurl", dmlurl);
/* 1711 */           this.workTypeMap.put("field" + recordSet.getInt("fieldid"), jsonObject);
/*      */         } 
/*      */       } 
/* 1714 */       sql = "insert into cus_fielddata  ";
/* 1715 */       String valueStr = "";
/* 1716 */       for (int i = 0; i < workFields.length; i++) {
/* 1717 */         JSONObject jsonObject = this.workTypeMap.get(workFields[i]);
/* 1718 */         fielddbType = jsonObject.getString("fielddbtype");
/* 1719 */         jsonObject.put("fieldvalue", workValues[i]);
/* 1720 */         String fieldvalue = HrmFieldManager.getReallyFieldvalue(jsonObject);
/* 1721 */         if (fielddbType.startsWith("char") || fielddbType.startsWith("varchar") || fielddbType
/* 1722 */           .startsWith("text")) {
/* 1723 */           valueStr = valueStr + ",'" + (!fieldvalue.equals("") ? fieldvalue : "") + "'";
/*      */         } else {
/* 1725 */           valueStr = valueStr + "," + (!fieldvalue.equals("") ? fieldvalue : "NULL");
/*      */         } 
/*      */       } 
/* 1728 */       valueStr = valueStr.substring(1);
/* 1729 */       sql = sql + "(scope,scopeid,id," + workField + ") values('HrmCustomFieldByInfoType'," + '\003' + "," + id + "," + valueStr + ")";
/*      */     }
/* 1731 */     catch (Exception e) {
/* 1732 */       writeLog(e);
/*      */     } 
/* 1734 */     return recordSet.execute(sql);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean updateWorkData(String workField, String workValue, int id) {
/* 1746 */     if (workField == null || workField.equals("")) {
/* 1747 */       return true;
/*      */     }
/*      */ 
/*      */     
/* 1751 */     String checkWorkInfo = "select id from cus_fielddata where scope='HrmCustomFieldByInfoType' and scopeid=3 and id=" + id;
/*      */     
/* 1753 */     RecordSet recordSet = new RecordSet();
/* 1754 */     recordSet.execute(checkWorkInfo);
/* 1755 */     if (!recordSet.next()) {
/* 1756 */       return addWorkData(workField, workValue, id);
/*      */     }
/*      */     
/* 1759 */     String[] workValues = workValue.split(";");
/* 1760 */     String[] workFields = workField.split(",");
/* 1761 */     String fielddbType = "";
/* 1762 */     String sql = "";
/*      */     try {
/* 1764 */       if (this.workTypeMap == null) {
/* 1765 */         this.workTypeMap = new HashMap<>();
/* 1766 */         String workSql = "select t1.fieldid,t1.hrm_fieldlable,t1.ismand,t2.fielddbtype,t2.fieldhtmltype,t2.type,t1.dmlurl from cus_formfield t1, cus_formdict t2 where t1.scope='HrmCustomFieldByInfoType' and t1.scopeid=3 and t1.fieldid=t2.id order by t1.fieldorder";
/* 1767 */         recordSet.execute(workSql);
/* 1768 */         while (recordSet.next()) {
/* 1769 */           String fieldid = Util.null2String(recordSet.getString("fieldid"));
/* 1770 */           String fielddbtype = Util.null2String(recordSet.getString("fielddbtype"));
/* 1771 */           String fieldhtmltype = Util.null2String(recordSet.getString("fieldhtmltype"));
/* 1772 */           String type = Util.null2String(recordSet.getString("type"));
/* 1773 */           String dmlurl = Util.null2String(recordSet.getString("dmlurl"));
/*      */           
/* 1775 */           JSONObject jsonObject = new JSONObject();
/* 1776 */           jsonObject.put("fieldid", fieldid);
/* 1777 */           jsonObject.put("fielddbtype", fielddbtype);
/* 1778 */           jsonObject.put("fieldhtmltype", fieldhtmltype);
/* 1779 */           jsonObject.put("type", type);
/* 1780 */           jsonObject.put("dmlurl", dmlurl);
/* 1781 */           this.workTypeMap.put("field" + recordSet.getInt("fieldid"), jsonObject);
/*      */         } 
/*      */       } 
/*      */       
/* 1785 */       sql = "update cus_fielddata set ";
/* 1786 */       String setStr = "";
/* 1787 */       for (int i = 0; i < workFields.length; i++) {
/* 1788 */         JSONObject jsonObject = this.workTypeMap.get(workFields[i]);
/* 1789 */         fielddbType = jsonObject.getString("fielddbtype");
/* 1790 */         jsonObject.put("fieldvalue", workValues[i]);
/* 1791 */         String fieldvalue = HrmFieldManager.getReallyFieldvalue(jsonObject);
/* 1792 */         if (fieldvalue.startsWith(",")) {
/* 1793 */           fieldvalue = fieldvalue.substring(1, fieldvalue.length());
/*      */         }
/* 1795 */         if (fieldvalue.endsWith(",")) {
/* 1796 */           fieldvalue = fieldvalue.substring(0, fieldvalue.length() - 1);
/*      */         }
/* 1798 */         if (fielddbType.startsWith("char") || fielddbType.startsWith("varchar") || fielddbType
/* 1799 */           .startsWith("text")) {
/* 1800 */           setStr = setStr + "," + workFields[i] + "='" + (!fieldvalue.equals("") ? fieldvalue : "") + "'";
/*      */         } else {
/* 1802 */           setStr = setStr + "," + workFields[i] + "=" + (!fieldvalue.equals("") ? fieldvalue : "NULL");
/*      */         } 
/*      */       } 
/*      */       
/* 1806 */       sql = sql + setStr.substring(1) + " where scope='HrmCustomFieldByInfoType' and scopeid=3 and id=" + id;
/* 1807 */     } catch (Exception e) {
/* 1808 */       writeLog(e);
/*      */     } 
/* 1810 */     return recordSet.execute(sql);
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int getSubCompanyId(String subCompanyName) {
/* 1821 */     String[] subCompanyNames = subCompanyName.split(">");
/* 1822 */     if (subCompanyNames != null && subCompanyNames.length >= 10) {
/* 1823 */       return -9;
/*      */     }
/* 1825 */     int currentId = 0;
/* 1826 */     int parentId = 0;
/* 1827 */     int curCount = 0;
/* 1828 */     int iscanceled = 0;
/*      */     
/* 1830 */     String currentidsql = "";
/* 1831 */     String canceledsql = "";
/* 1832 */     String sql = "";
/* 1833 */     String sqlInsert = "";
/* 1834 */     RecordSet recordSet = new RecordSet();
/* 1835 */     for (int i = 0; i < subCompanyNames.length; i++) {
/* 1836 */       if (!StringUtil.isNull(subCompanyNames[i])) {
/*      */ 
/*      */ 
/*      */ 
/*      */         
/* 1841 */         sql = "select id from HrmSubCompany where ltrim(rtrim(dbo.convToMultiLang(ltrim(rtrim(subcompanyname))," + this.userlanguage + ")))='" + subCompanyNames[i].trim() + "' and supsubcomid=" + parentId;
/* 1842 */         if (recordSet.getDBType().equalsIgnoreCase("oracle"))
/*      */         {
/* 1844 */           sql = "select id from HrmSubCompany where ltrim(rtrim(convToMultiLang(ltrim(rtrim(subcompanyname))," + this.userlanguage + ")))='" + subCompanyNames[i].trim() + "' and supsubcomid=" + parentId;
/*      */         }
/* 1846 */         currentidsql = sql + " and (canceled is null or canceled != 1)";
/* 1847 */         currentId = getResultSetId(currentidsql);
/*      */         
/* 1849 */         if (parentId == 0 && currentId == 0) {
/* 1850 */           recordSet.executeSql("select COUNT(id) from HrmSubCompany where supsubcomid = 0 and (canceled is null or canceled != '1')");
/*      */           
/* 1852 */           if (recordSet.next()) {
/* 1853 */             curCount = recordSet.getInt(1);
/*      */           }
/*      */         } 
/*      */ 
/*      */         
/* 1858 */         if (currentId == 0) {
/* 1859 */           canceledsql = sql + " and canceled='1' ";
/* 1860 */           iscanceled = getResultSetId(canceledsql);
/* 1861 */           if (iscanceled == 0)
/*      */           
/*      */           { 
/*      */ 
/*      */             
/* 1866 */             if (this.scCount != 0 && curCount >= this.scCount)
/*      */             {
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */               
/* 1898 */               currentId = -1; }  }
/*      */           else { currentId = -2; break; }
/*      */         
/* 1901 */         }  parentId = currentId;
/* 1902 */         if (currentId != -1)
/* 1903 */           this.rtxService.addSubCompany(parentId); 
/*      */       } 
/*      */     } 
/* 1906 */     return currentId;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int getDeptId(String deptNames, int subCompanyId) {
/* 1918 */     String[] deptName = deptNames.split(">");
/* 1919 */     int currentId = 0;
/* 1920 */     int parentId = 0;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */     
/* 1926 */     RecordSet recordSet = new RecordSet();
/* 1927 */     for (int i = 0; i < deptName.length; i++) {
/* 1928 */       if (deptName[i] != null && !deptName[i].equals("")) {
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/* 1934 */         String sql = "select id from HrmDepartment where subcompanyid1=" + subCompanyId + " and ltrim(rtrim(dbo.convToMultiLang(ltrim(rtrim(departmentname))," + this.userlanguage + ")))='" + deptName[i].trim() + "' and supdepid=" + parentId;
/* 1935 */         if (recordSet.getDBType().equalsIgnoreCase("oracle"))
/*      */         {
/*      */           
/* 1938 */           sql = "select id from HrmDepartment where subcompanyid1=" + subCompanyId + " and ltrim(rtrim(convToMultiLang(ltrim(rtrim(departmentname))," + this.userlanguage + ")))='" + deptName[i].trim() + "' and supdepid=" + parentId;
/*      */         }
/* 1940 */         String currentidsql = sql + " and (canceled  !=1 or canceled is null)";
/* 1941 */         writeLog("currentidsql ===> " + currentidsql);
/* 1942 */         currentId = getResultSetId(currentidsql);
/* 1943 */         writeLog("currentId ===> " + currentId);
/*      */         
/* 1945 */         if (currentId == 0) {
/* 1946 */           String canceledsql = sql + " and canceled='1' ";
/* 1947 */           int iscanceled = getResultSetId(canceledsql);
/* 1948 */           writeLog("iscanceled ===> " + iscanceled);
/* 1949 */           if (iscanceled != 0) {
/*      */             
/* 1951 */             currentId = -2;
/*      */             break;
/*      */           } 
/*      */         } 
/* 1955 */         parentId = currentId;
/*      */         
/* 1957 */         writeLog("parentId ===> " + parentId);
/*      */       } 
/*      */     } 
/* 1960 */     return currentId;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int getJobTitles(String jobtitlename, String jobactivityname, String jobgroupname) {
/* 1972 */     RecordSet rs = new RecordSet();
/* 1973 */     RecordSet recordSet = new RecordSet();
/* 1974 */     String sqlInsert = "";
/*      */     
/* 1976 */     jobgroupname = Util.convertInput2DB4(jobgroupname);
/* 1977 */     jobactivityname = Util.convertInput2DB4(jobactivityname);
/*      */ 
/*      */     
/* 1980 */     String selSql = "select id from HrmJobGroups where ltrim(rtrim(dbo.convToMultiLang(ltrim(rtrim(jobgroupname))," + this.userlanguage + ")))='" + jobgroupname + "'";
/*      */     
/* 1982 */     if (recordSet.getDBType().equalsIgnoreCase("oracle")) {
/* 1983 */       selSql = "select id from HrmJobGroups where ltrim(rtrim(convToMultiLang(ltrim(rtrim(jobgroupname))," + this.userlanguage + ")))='" + jobgroupname + "'";
/*      */     }
/*      */ 
/*      */     
/* 1987 */     int jobgroupid = getResultSetId(selSql);
/*      */     
/* 1989 */     if (jobgroupid == 0) {
/* 1990 */       if (jobgroupname.length() == 0) {
/* 1991 */         selSql = "select max(id) as id from HrmJobGroups";
/* 1992 */         jobgroupid = getResultSetId(selSql);
/*      */       }
/*      */       else {
/*      */         
/* 1996 */         sqlInsert = "insert into HrmJobGroups (jobgroupname,jobgroupremark ," + DbFunctionUtil.getInsertColumnSql() + ") values('" + jobgroupname + "','" + jobgroupname + "'," + DbFunctionUtil.getInsertColumnValueSql(recordSet.getDBType(), 1) + ")";
/* 1997 */         execSql(sqlInsert);
/* 1998 */         jobgroupid = getResultSetId(selSql);
/*      */       } 
/*      */     }
/*      */ 
/*      */     
/* 2003 */     selSql = "select id from HrmJobActivities where ltrim(rtrim(dbo.convToMultiLang(ltrim(rtrim(jobactivityname))," + this.userlanguage + ")))='" + jobactivityname + "' and jobgroupid=" + jobgroupid;
/*      */     
/* 2005 */     if (recordSet.getDBType().equalsIgnoreCase("oracle")) {
/* 2006 */       selSql = "select id from HrmJobActivities where ltrim(rtrim(convToMultiLang(ltrim(rtrim(jobactivityname))," + this.userlanguage + ")))='" + jobactivityname + "' and jobgroupid=" + jobgroupid;
/*      */     }
/*      */ 
/*      */     
/* 2010 */     int jobactivityid = getResultSetId(selSql);
/*      */     
/* 2012 */     if (jobactivityid == 0) {
/*      */ 
/*      */       
/* 2015 */       sqlInsert = "insert into HrmJobActivities (jobactivityname,Jobactivitymark,jobgroupid ," + DbFunctionUtil.getInsertColumnSql() + ") values('" + jobactivityname + "','" + jobactivityname + "'," + jobgroupid + " ," + DbFunctionUtil.getInsertColumnValueSql(recordSet.getDBType(), 1) + ")";
/* 2016 */       execSql(sqlInsert);
/* 2017 */       jobactivityid = getResultSetId(selSql);
/*      */     } 
/*      */ 
/*      */     
/* 2021 */     selSql = "select id from HrmJobTitles where ltrim(rtrim(dbo.convToMultiLang(ltrim(rtrim(jobtitlemark))," + this.userlanguage + ")))='" + jobtitlename + "' and jobactivityid=" + jobactivityid;
/*      */     
/* 2023 */     if (recordSet.getDBType().equalsIgnoreCase("oracle")) {
/* 2024 */       selSql = "select id from HrmJobTitles where ltrim(rtrim(convToMultiLang(ltrim(rtrim(jobtitlemark))," + this.userlanguage + ")))='" + jobtitlename + "' and jobactivityid=" + jobactivityid;
/*      */     }
/*      */ 
/*      */     
/* 2028 */     int jobtitle = getResultSetId(selSql);
/*      */     
/* 2030 */     if (jobtitle == 0) {
/*      */ 
/*      */       
/* 2033 */       sqlInsert = "insert into HrmJobTitles (jobtitlename,Jobtitlemark,jobactivityid ," + DbFunctionUtil.getInsertColumnSql() + ") values('" + jobtitlename + "','" + jobtitlename + "'," + jobactivityid + " ," + DbFunctionUtil.getInsertColumnValueSql(recordSet.getDBType(), 1) + ")";
/* 2034 */       execSql(sqlInsert);
/* 2035 */       jobtitle = getResultSetId(selSql);
/*      */     } else {
/* 2037 */       selSql = "select * from HrmJobTitles where id = '" + jobtitle + "' and jobactivityid = '" + jobactivityid + "' ";
/*      */       
/* 2039 */       recordSet.executeSql(selSql);
/* 2040 */       if (!recordSet.next()) {
/*      */         
/* 2042 */         sqlInsert = "update HrmJobTitles set jobactivityid = '" + jobactivityid + "'," + DbFunctionUtil.getUpdateSetSql(rs.getDBType(), 1) + " where id = '" + jobtitle + "' ";
/* 2043 */         rs.executeSql(sqlInsert);
/*      */       } 
/*      */     } 
/* 2046 */     return jobtitle;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public Map getManagerIdAndStr(String hrmid, String keyFieldValue, String keyField) {
/* 2058 */     int managerId = 0;
/* 2059 */     String managerstr = "";
/* 2060 */     RecordSet recordSet = new RecordSet();
/* 2061 */     Map<String, Comparable> managerInfo = new HashMap<>();
/* 2062 */     if (!keyFieldValue.equals("")) {
/* 2063 */       String selSql = "select id,managerstr from HrmResource where " + keyField + "='" + keyFieldValue + "'";
/* 2064 */       recordSet.execute(selSql);
/* 2065 */       while (recordSet.next()) {
/* 2066 */         managerId = recordSet.getInt("id");
/* 2067 */         managerstr = recordSet.getString("managerstr");
/* 2068 */         if (!hrmid.equals("" + managerId)) {
/* 2069 */           managerstr = "," + managerId + managerstr;
/*      */         } else {
/* 2071 */           managerstr = "," + managerId + ",";
/*      */         } 
/*      */         
/* 2074 */         managerstr = managerstr.endsWith(",") ? managerstr : (managerstr + ",");
/* 2075 */         managerInfo.put("managerid", new Integer(managerId));
/* 2076 */         managerInfo.put("managerstr", managerstr);
/*      */       } 
/*      */     } 
/* 2079 */     return managerInfo;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int getAssistantid(String keyFieldValue, String keyField) {
/* 2091 */     int getAssistantid = 0;
/* 2092 */     if (!keyFieldValue.equals("")) {
/* 2093 */       String selSql = "select id from HrmResource where " + keyField + "='" + keyFieldValue + "'";
/* 2094 */       getAssistantid = getResultSetId(selSql);
/*      */     } 
/* 2096 */     return getAssistantid;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int getLocationid(String locationname) {
/* 2106 */     int locationid = 0;
/* 2107 */     if (!locationname.equals("")) {
/* 2108 */       locationid = this.locationMap.containsKey(locationname) ? ((Integer)this.locationMap.get(locationname)).intValue() : 0;
/* 2109 */       if (locationid == 0) {
/* 2110 */         String insertSql = "insert into HrmLocations(locationname,locationdesc,countryid) values('" + locationname + "','" + locationname + "',1)";
/*      */         
/* 2112 */         execSql(insertSql);
/* 2113 */         String selSql = "select id from HrmLocations where countryid=1 and locationname='" + locationname + "'";
/* 2114 */         locationid = getResultSetId(selSql);
/* 2115 */         this.locationMap.put(locationname, Integer.valueOf(locationid));
/*      */       } 
/*      */     } 
/* 2118 */     return locationid;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int getUseKind(String usekindname) {
/* 2128 */     int usekindid = 0;
/* 2129 */     if (!usekindname.equals("")) {
/* 2130 */       usekindid = this.usekindMap.containsKey(usekindname) ? ((Integer)this.usekindMap.get(usekindname)).intValue() : 0;
/* 2131 */       if (usekindid == 0) {
/* 2132 */         String insertSql = "insert into HrmUseKind(name,description) values('" + usekindname + "','" + usekindname + "')";
/*      */         
/* 2134 */         execSql(insertSql);
/* 2135 */         String selSql = "select id from HrmUseKind where name='" + usekindname + "'";
/* 2136 */         usekindid = getResultSetId(selSql);
/* 2137 */         this.usekindMap.put(usekindname, Integer.valueOf(usekindid));
/*      */       } 
/*      */     } 
/* 2140 */     return usekindid;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int getJobcall(String jobcall) {
/* 2151 */     int jobcalld = 0;
/* 2152 */     if (!jobcall.equals("")) {
/* 2153 */       jobcalld = this.jobcallMap.containsKey(jobcall) ? ((Integer)this.jobcallMap.get(jobcall)).intValue() : 0;
/* 2154 */       if (jobcalld == 0) {
/* 2155 */         String insertSql = "insert into HrmJobCall(name) values('" + jobcall + "')";
/* 2156 */         execSql(insertSql);
/* 2157 */         String selSql = "select id from HrmJobCall where name='" + jobcall + "'";
/* 2158 */         jobcalld = getResultSetId(selSql);
/* 2159 */         this.jobcallMap.put(jobcall, Integer.valueOf(jobcalld));
/*      */       } 
/*      */     } 
/* 2162 */     return jobcalld;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int getSystemlanguage(String language) {
/* 2172 */     int systemlanguageid = 7;
/*      */ 
/*      */     
/* 2175 */     if (!language.equals("English") && !language.equals("繁體中文") && !language.equals("简体中文") && language != "") {
/* 2176 */       return -1;
/*      */     }
/*      */ 
/*      */     
/* 2180 */     if ((language.equals("English") || language.equals("繁體中文")) && !this.multilanguage.toLowerCase().equals("y")) {
/* 2181 */       return 0;
/*      */     }
/*      */     
/* 2184 */     if (!language.equals("")) {
/* 2185 */       if (language.equals("简体中文")) {
/* 2186 */         systemlanguageid = this.cnLanguageId;
/*      */       } else {
/* 2188 */         systemlanguageid = ((Integer)this.sysLanguage.get(language)).intValue();
/*      */       } 
/*      */     }
/* 2191 */     return systemlanguageid;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int getStatus(String status) {
/* 2202 */     int statusid = 1;
/* 2203 */     switch (status)
/*      */     { case "试用":
/* 2205 */         statusid = 0;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/* 2232 */         return statusid;case "正式": statusid = 1; return statusid;case "临时": statusid = 2; return statusid;case "试用延期": statusid = 3; return statusid;case "解聘": statusid = 4; return statusid;case "离职": statusid = 5; return statusid;case "退休": statusid = 6; return statusid;case "无效": statusid = 7; return statusid; }  statusid = 1; return statusid;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int getEducationlevel(String educationlevel) {
/* 2242 */     int educationlevelid = 0;
/* 2243 */     if (!educationlevel.equals("")) {
/* 2244 */       educationlevelid = this.educationlevelMap.containsKey(educationlevel) ? ((Integer)this.educationlevelMap.get(educationlevel)).intValue() : 0;
/*      */       
/* 2246 */       if (educationlevelid == 0) {
/* 2247 */         String insertSql = "insert into HrmEducationLevel(name,description) values('" + educationlevel + "','" + educationlevel + "')";
/*      */         
/* 2249 */         execSql(insertSql);
/* 2250 */         String selSql = "select id from HrmEducationLevel where name='" + educationlevel + "'";
/* 2251 */         educationlevelid = getResultSetId(selSql);
/* 2252 */         this.educationlevelMap.put(educationlevel, Integer.valueOf(educationlevelid));
/*      */       } 
/*      */     } 
/* 2255 */     return educationlevelid;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public String getHealthinfo(String healthinfo) {
/* 2266 */     String healthinfoid = "0";
/*      */     
/* 2268 */     switch (healthinfo)
/*      */     { case "良好":
/* 2270 */         healthinfoid = "1";
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */         
/* 2283 */         return healthinfoid;case "一般": healthinfoid = "2"; return healthinfoid;case "较差": healthinfoid = "3"; return healthinfoid; }  healthinfoid = "0"; return healthinfoid;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void getKeyMap(String keyField) {
/* 2292 */     RecordSet recordSet = new RecordSet();
/* 2293 */     String sql = "";
/* 2294 */     sql = "select id, certificatenum, ltrim(rtrim(dbo.convToMultiLang(ltrim(rtrim(" + keyField + "))," + this.userlanguage + "))) as " + keyField + " from HrmResource";
/*      */     
/* 2296 */     if (recordSet.getDBType().equalsIgnoreCase("oracle")) {
/* 2297 */       sql = "select id, certificatenum, ltrim(rtrim(convToMultiLang(ltrim(rtrim(" + keyField + "))," + this.userlanguage + "))) as " + keyField + " from HrmResource";
/*      */     }
/*      */     
/* 2300 */     recordSet.execute(sql);
/* 2301 */     String cerNum = "";
/* 2302 */     while (recordSet.next()) {
/* 2303 */       cerNum = recordSet.getString("certificatenum");
/* 2304 */       this.keyMap.put(recordSet.getString(keyField), new Integer(recordSet.getInt("id")));
/* 2305 */       if (StringUtil.isNotNull(cerNum)) {
/* 2306 */         this.certificateNums.put(StringUtil.vString(cerNum), new Integer(recordSet.getInt("id")));
/*      */       }
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public void writeImportLog(List<ImportLog> resultList) {
/* 2318 */     if (this.logFile.equals("")) {
/* 2319 */       SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmssSSS");
/*      */       
/* 2321 */       String logFile1 = GCONST.getRootPath() + "/log/hrmImportLog";
/* 2322 */       logFile1 = logFile1.replace("\\", "/");
/* 2323 */       File logFile2 = new File(logFile1);
/* 2324 */       if (!logFile2.exists()) {
/* 2325 */         logFile2.mkdir();
/*      */       }
/*      */       
/* 2328 */       this
/* 2329 */         .logFile = GCONST.getRootPath() + "log" + File.separator + "hrmImportLog" + File.separator + "人员导入_" + dateFormat.format(new Date()) + ".txt";
/* 2330 */       this.logFile = this.logFile.replace("\\", "/");
/* 2331 */       File file = new File(this.logFile);
/*      */       
/*      */       try {
/* 2334 */         file.createNewFile();
/* 2335 */       } catch (IOException e) {
/* 2336 */         writeLog(e);
/*      */       } 
/*      */     } 
/*      */     try {
/* 2340 */       BufferedWriter out = new BufferedWriter(new FileWriter(this.logFile, true));
/* 2341 */       ImportLog log = new ImportLog();
/*      */       
/* 2343 */       SimpleDateFormat timeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
/* 2344 */       String logRecord = "导入时间 " + timeFormat.format(new Date()) + "\r\n";
/* 2345 */       out.write(logRecord);
/*      */       int i;
/* 2347 */       for (i = 0; i < resultList.size(); i++) {
/* 2348 */         log = resultList.get(i);
/*      */         
/* 2350 */         if (log.getStatus().equals("失败")) {
/* 2351 */           switch (this.keyField) {
/*      */             case "workcode":
/* 2353 */               logRecord = log.getWorkCode();
/*      */               break;
/*      */             case "loginid":
/* 2356 */               logRecord = log.getLoginid();
/*      */               break;
/*      */             case "lastname":
/* 2359 */               logRecord = log.getLastname();
/*      */               break;
/*      */           } 
/*      */           
/* 2363 */           logRecord = logRecord + "|" + log.getDepartment() + "|" + log.getOperation() + "|" + log.getStatus() + "|" + log.getReason() + "\r\n";
/* 2364 */           out.write(logRecord);
/*      */         } 
/*      */       } 
/*      */ 
/*      */       
/* 2369 */       for (i = 0; i < resultList.size(); i++) {
/* 2370 */         log = resultList.get(i);
/* 2371 */         if (log.getStatus().equals("成功")) {
/*      */           
/* 2373 */           switch (this.keyField) {
/*      */             case "workcode":
/* 2375 */               logRecord = log.getWorkCode();
/*      */               break;
/*      */             case "loginid":
/* 2378 */               logRecord = log.getLoginid();
/*      */               break;
/*      */             case "lastname":
/* 2381 */               logRecord = log.getLastname();
/*      */               break;
/*      */           } 
/*      */           
/* 2385 */           logRecord = logRecord + "|" + log.getDepartment() + "|" + log.getOperation() + "|" + log.getStatus() + "|" + log.getReason() + "\r\n";
/* 2386 */           out.write(logRecord);
/*      */         } 
/*      */       } 
/* 2389 */       out.close();
/* 2390 */     } catch (IOException e) {
/* 2391 */       writeLog(e);
/*      */     } 
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public boolean execSql(String sql) {
/* 2403 */     RecordSet recordSet = new RecordSet();
/* 2404 */     if (recordSet.execute(sql)) {
/* 2405 */       return true;
/*      */     }
/* 2407 */     return false;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public int getResultSetId(String sql) {
/* 2418 */     int currentId = 0;
/* 2419 */     RecordSet recordSet = new RecordSet();
/* 2420 */     recordSet.execute(sql);
/*      */     try {
/* 2422 */       while (recordSet.next()) {
/* 2423 */         currentId = recordSet.getInt("id");
/*      */       }
/* 2425 */     } catch (Exception e) {
/* 2426 */       e.printStackTrace();
/*      */     } 
/* 2428 */     return currentId;
/*      */   }
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */   
/*      */   public ImportLog createLog(HrmResourceVoCust vo, String operation, String status, String reason) {
/* 2441 */     ImportLog log = new ImportLog();
/*      */     
/* 2443 */     log.setWorkCode(vo.getWorkcode());
/* 2444 */     log.setLastname(vo.getLastname());
/* 2445 */     log.setLoginid(vo.getLoginid());
/* 2446 */     log.setOperation(operation);
/* 2447 */     if (vo.getSubcompanyid1() != null && vo.getDepartmentid() != null) {
/* 2448 */       log.setDepartment(vo.getSubcompanyid1() + ">" + vo.getDepartmentid());
/*      */     } else {
/* 2450 */       log.setDepartment("");
/*      */     } 
/* 2452 */     log.setStatus(status);
/* 2453 */     log.setReason(reason);
/*      */     
/* 2455 */     return log;
/*      */   }
/*      */ 
/*      */   
/*      */   public static boolean isInteger(String str) {
/* 2460 */     if (str == null) {
/* 2461 */       return false;
/*      */     }
/* 2463 */     Pattern pattern = Pattern.compile("[0-9]+");
/* 2464 */     return pattern.matcher(str).matches();
/*      */   }
/*      */   
/*      */   public int getUserlanguage() {
/* 2468 */     return this.userlanguage;
/*      */   }
/*      */   
/*      */   public void setUserlanguage(int userlanguage) {
/* 2472 */     this.userlanguage = userlanguage;
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/chinasws/webservices/HrmImportProcessCust.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */