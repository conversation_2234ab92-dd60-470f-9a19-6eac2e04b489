/*      */ package chinasws.webservices;
/*      */ 
/*      */ import java.io.Serializable;
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ 
/*      */ public class HrmResourceCust
/*      */   implements Serializable
/*      */ {
/*      */   private String swshrguid;
/*      */   private String pinyinlastname;
/*      */   private String ecology_pinyin_search;
/*      */   private String swssigleaccount;
/*      */   private Integer id;
/*      */   private String loginid;
/*      */   private String password;
/*      */   private String lastname;
/*      */   private String sex;
/*      */   private String birthday;
/*      */   private Integer nationality;
/*      */   private Integer systemlanguage;
/*      */   private String maritalstatus;
/*      */   private String telephone;
/*      */   private String mobile;
/*      */   private String mobilecall;
/*      */   private String email;
/*      */   private Integer locationid;
/*      */   private String workroom;
/*      */   private String homeaddress;
/*      */   private String resourcetype;
/*      */   private String startdate;
/*      */   private String enddate;
/*      */   private Integer jobtitle;
/*      */   private String jobactivitydesc;
/*      */   private Short joblevel;
/*      */   private Short seclevel;
/*      */   private Integer departmentid;
/*      */   private Integer subcompanyid1;
/*      */   private Integer costcenterid;
/*      */   private Integer managerid;
/*      */   private Integer assistantid;
/*      */   private Integer bankid1;
/*      */   private String accountid1;
/*      */   private Integer resourceimageid;
/*      */   private Integer createrid;
/*      */   private String createdate;
/*      */   private Integer lastmodid;
/*      */   private String lastmoddate;
/*      */   private String lastlogindate;
/*      */   private String datefield1;
/*      */   private String datefield2;
/*      */   private String datefield3;
/*      */   private String datefield4;
/*      */   private String datefield5;
/*      */   private Float numberfield1;
/*      */   private Float numberfield2;
/*      */   private Float numberfield3;
/*      */   private Float numberfield4;
/*      */   private Float numberfield5;
/*      */   private String textfield1;
/*      */   private String textfield2;
/*      */   private String textfield3;
/*      */   private String textfield4;
/*      */   private String textfield5;
/*      */   private Short tinyintfield1;
/*      */   private Short tinyintfield2;
/*      */   private Short tinyintfield3;
/*      */   private Short tinyintfield4;
/*      */   private Short tinyintfield5;
/*      */   private String certificatenum;
/*      */   private String nativeplace;
/*      */   private Integer educationlevel;
/*      */   private String bememberdate;
/*      */   private String bepartydate;
/*      */   private String workcode;
/*      */   private String regresidentplace;
/*      */   private String healthinfo;
/*      */   private String residentplace;
/*      */   private String policy;
/*      */   private String degree;
/*      */   private String height;
/*      */   private Integer usekind;
/*      */   private Integer jobcall;
/*      */   private String accumfundaccount;
/*      */   private String birthplace;
/*      */   private String folk;
/*      */   private String residentphone;
/*      */   private String residentpostcode;
/*      */   private String extphone;
/*      */   private String managerstr;
/*      */   private Integer status;
/*      */   private String fax;
/*      */   private String islabouunion;
/*      */   private Integer weight;
/*      */   private String tempresidentnumber;
/*      */   private String probationenddate;
/*      */   private Integer countryid;
/*      */   private String passwdchgdate;
/*      */   private Integer needusb;
/*      */   private String serial;
/*      */   private String account;
/*      */   private String lloginid;
/*      */   private Integer needdynapass;
/*      */   private Integer srcfrom;
/*      */   private Integer srcContacterid;
/*      */   private String ncpkcode;
/*      */   private String dsporder;
/*      */   private Integer passwordstate;
/*      */   private Double spaceSize;
/*      */   private Integer accounttype;
/*      */   private Integer belongto;
/*      */   private String isusedactylogram;
/*      */   private String dactylogram;
/*      */   private String assistantdactylogram;
/*      */   private String loginnet;
/*      */   private Integer passwordlock;
/*      */   private Integer sumpasswordwrong;
/*      */   private String oldpassword1;
/*      */   private String oldpassword2;
/*      */   private String messagerurl;
/*      */   private String msgStyle;
/*      */   
/*      */   public HrmResourceCust() {}
/*      */   
/*      */   public HrmResourceCust(String loginid, String password, String lastname, String sex, String birthday, Integer nationality, Integer systemlanguage, String maritalstatus, String telephone, String mobile, String mobilecall, String email, Integer locationid, String workroom, String homeaddress, String resourcetype, String startdate, String enddate, Integer jobtitle, String jobactivitydesc, Short joblevel, Short seclevel, Integer departmentid, Integer subcompanyid1, Integer costcenterid, Integer managerid, Integer assistantid, Integer bankid1, String accountid1, Integer resourceimageid, Integer createrid, String createdate, Integer lastmodid, String lastmoddate, String lastlogindate, String datefield1, String datefield2, String datefield3, String datefield4, String datefield5, Float numberfield1, Float numberfield2, Float numberfield3, Float numberfield4, Float numberfield5, String textfield1, String textfield2, String textfield3, String textfield4, String textfield5, Short tinyintfield1, Short tinyintfield2, Short tinyintfield3, Short tinyintfield4, Short tinyintfield5, String certificatenum, String nativeplace, Integer educationlevel, String bememberdate, String bepartydate, String workcode, String regresidentplace, String healthinfo, String residentplace, String policy, String degree, String height, Integer usekind, Integer jobcall, String accumfundaccount, String birthplace, String folk, String residentphone, String residentpostcode, String extphone, String managerstr, Integer status, String fax, String islabouunion, Integer weight, String tempresidentnumber, String probationenddate, Integer countryid, String passwdchgdate, Integer needusb, String serial, String account, String lloginid, Integer needdynapass, Integer srcfrom, Integer srcContacterid, String ncpkcode, String dsporder, Integer passwordstate, Double spaceSize, Integer accounttype, Integer belongto, String isusedactylogram, String dactylogram, String assistantdactylogram, String loginnet, Integer passwordlock, Integer sumpasswordwrong, String oldpassword1, String oldpassword2, String messagerurl, String msgStyle) {
/*  159 */     this.loginid = loginid;
/*  160 */     this.password = password;
/*  161 */     this.lastname = lastname;
/*  162 */     this.sex = sex;
/*  163 */     this.birthday = birthday;
/*  164 */     this.nationality = nationality;
/*  165 */     this.systemlanguage = systemlanguage;
/*  166 */     this.maritalstatus = maritalstatus;
/*  167 */     this.telephone = telephone;
/*  168 */     this.mobile = mobile;
/*  169 */     this.mobilecall = mobilecall;
/*  170 */     this.email = email;
/*  171 */     this.locationid = locationid;
/*  172 */     this.workroom = workroom;
/*  173 */     this.homeaddress = homeaddress;
/*  174 */     this.resourcetype = resourcetype;
/*  175 */     this.startdate = startdate;
/*  176 */     this.enddate = enddate;
/*  177 */     this.jobtitle = jobtitle;
/*  178 */     this.jobactivitydesc = jobactivitydesc;
/*  179 */     this.joblevel = joblevel;
/*  180 */     this.seclevel = seclevel;
/*  181 */     this.departmentid = departmentid;
/*  182 */     this.subcompanyid1 = subcompanyid1;
/*  183 */     this.costcenterid = costcenterid;
/*  184 */     this.managerid = managerid;
/*  185 */     this.assistantid = assistantid;
/*  186 */     this.bankid1 = bankid1;
/*  187 */     this.accountid1 = accountid1;
/*  188 */     this.resourceimageid = resourceimageid;
/*  189 */     this.createrid = createrid;
/*  190 */     this.createdate = createdate;
/*  191 */     this.lastmodid = lastmodid;
/*  192 */     this.lastmoddate = lastmoddate;
/*  193 */     this.lastlogindate = lastlogindate;
/*  194 */     this.datefield1 = datefield1;
/*  195 */     this.datefield2 = datefield2;
/*  196 */     this.datefield3 = datefield3;
/*  197 */     this.datefield4 = datefield4;
/*  198 */     this.datefield5 = datefield5;
/*  199 */     this.numberfield1 = numberfield1;
/*  200 */     this.numberfield2 = numberfield2;
/*  201 */     this.numberfield3 = numberfield3;
/*  202 */     this.numberfield4 = numberfield4;
/*  203 */     this.numberfield5 = numberfield5;
/*  204 */     this.textfield1 = textfield1;
/*  205 */     this.textfield2 = textfield2;
/*  206 */     this.textfield3 = textfield3;
/*  207 */     this.textfield4 = textfield4;
/*  208 */     this.textfield5 = textfield5;
/*  209 */     this.tinyintfield1 = tinyintfield1;
/*  210 */     this.tinyintfield2 = tinyintfield2;
/*  211 */     this.tinyintfield3 = tinyintfield3;
/*  212 */     this.tinyintfield4 = tinyintfield4;
/*  213 */     this.tinyintfield5 = tinyintfield5;
/*  214 */     this.certificatenum = certificatenum;
/*  215 */     this.nativeplace = nativeplace;
/*  216 */     this.educationlevel = educationlevel;
/*  217 */     this.bememberdate = bememberdate;
/*  218 */     this.bepartydate = bepartydate;
/*  219 */     this.workcode = workcode;
/*  220 */     this.regresidentplace = regresidentplace;
/*  221 */     this.healthinfo = healthinfo;
/*  222 */     this.residentplace = residentplace;
/*  223 */     this.policy = policy;
/*  224 */     this.degree = degree;
/*  225 */     this.height = height;
/*  226 */     this.usekind = usekind;
/*  227 */     this.jobcall = jobcall;
/*  228 */     this.accumfundaccount = accumfundaccount;
/*  229 */     this.birthplace = birthplace;
/*  230 */     this.folk = folk;
/*  231 */     this.residentphone = residentphone;
/*  232 */     this.residentpostcode = residentpostcode;
/*  233 */     this.extphone = extphone;
/*  234 */     this.managerstr = managerstr;
/*  235 */     this.status = status;
/*  236 */     this.fax = fax;
/*  237 */     this.islabouunion = islabouunion;
/*  238 */     this.weight = weight;
/*  239 */     this.tempresidentnumber = tempresidentnumber;
/*  240 */     this.probationenddate = probationenddate;
/*  241 */     this.countryid = countryid;
/*  242 */     this.passwdchgdate = passwdchgdate;
/*  243 */     this.needusb = needusb;
/*  244 */     this.serial = serial;
/*  245 */     this.account = account;
/*  246 */     this.lloginid = lloginid;
/*  247 */     this.needdynapass = needdynapass;
/*  248 */     this.srcfrom = srcfrom;
/*  249 */     this.srcContacterid = srcContacterid;
/*  250 */     this.ncpkcode = ncpkcode;
/*  251 */     this.dsporder = dsporder;
/*  252 */     this.passwordstate = passwordstate;
/*  253 */     this.spaceSize = spaceSize;
/*  254 */     this.accounttype = accounttype;
/*  255 */     this.belongto = belongto;
/*  256 */     this.isusedactylogram = isusedactylogram;
/*  257 */     this.dactylogram = dactylogram;
/*  258 */     this.assistantdactylogram = assistantdactylogram;
/*  259 */     this.loginnet = loginnet;
/*  260 */     this.passwordlock = passwordlock;
/*  261 */     this.sumpasswordwrong = sumpasswordwrong;
/*  262 */     this.oldpassword1 = oldpassword1;
/*  263 */     this.oldpassword2 = oldpassword2;
/*  264 */     this.messagerurl = messagerurl;
/*  265 */     this.msgStyle = msgStyle;
/*      */   }
/*      */ 
/*      */ 
/*      */   
/*      */   public Integer getId() {
/*  271 */     return this.id;
/*      */   }
/*      */   
/*      */   public void setId(Integer id) {
/*  275 */     this.id = id;
/*      */   }
/*      */   
/*      */   public String getLoginid() {
/*  279 */     return this.loginid;
/*      */   }
/*      */   
/*      */   public void setLoginid(String loginid) {
/*  283 */     this.loginid = loginid;
/*      */   }
/*      */   
/*      */   public String getPassword() {
/*  287 */     return this.password;
/*      */   }
/*      */   
/*      */   public void setPassword(String password) {
/*  291 */     this.password = password;
/*      */   }
/*      */   
/*      */   public String getLastname() {
/*  295 */     return this.lastname;
/*      */   }
/*      */   
/*      */   public void setLastname(String lastname) {
/*  299 */     this.lastname = lastname;
/*      */   }
/*      */   
/*      */   public String getSex() {
/*  303 */     return this.sex;
/*      */   }
/*      */   
/*      */   public void setSex(String sex) {
/*  307 */     this.sex = sex;
/*      */   }
/*      */   
/*      */   public String getBirthday() {
/*  311 */     return this.birthday;
/*      */   }
/*      */   
/*      */   public void setBirthday(String birthday) {
/*  315 */     this.birthday = birthday;
/*      */   }
/*      */   
/*      */   public Integer getNationality() {
/*  319 */     return this.nationality;
/*      */   }
/*      */   
/*      */   public void setNationality(Integer nationality) {
/*  323 */     this.nationality = nationality;
/*      */   }
/*      */   
/*      */   public Integer getSystemlanguage() {
/*  327 */     return this.systemlanguage;
/*      */   }
/*      */   
/*      */   public void setSystemlanguage(Integer systemlanguage) {
/*  331 */     this.systemlanguage = systemlanguage;
/*      */   }
/*      */   
/*      */   public String getMaritalstatus() {
/*  335 */     return this.maritalstatus;
/*      */   }
/*      */   
/*      */   public void setMaritalstatus(String maritalstatus) {
/*  339 */     this.maritalstatus = maritalstatus;
/*      */   }
/*      */   
/*      */   public String getTelephone() {
/*  343 */     return this.telephone;
/*      */   }
/*      */   
/*      */   public void setTelephone(String telephone) {
/*  347 */     this.telephone = telephone;
/*      */   }
/*      */   
/*      */   public String getMobile() {
/*  351 */     return this.mobile;
/*      */   }
/*      */   
/*      */   public void setMobile(String mobile) {
/*  355 */     this.mobile = mobile;
/*      */   }
/*      */   
/*      */   public String getMobilecall() {
/*  359 */     return this.mobilecall;
/*      */   }
/*      */   
/*      */   public void setMobilecall(String mobilecall) {
/*  363 */     this.mobilecall = mobilecall;
/*      */   }
/*      */   
/*      */   public String getEmail() {
/*  367 */     return this.email;
/*      */   }
/*      */   
/*      */   public void setEmail(String email) {
/*  371 */     this.email = email;
/*      */   }
/*      */   
/*      */   public Integer getLocationid() {
/*  375 */     return this.locationid;
/*      */   }
/*      */   
/*      */   public void setLocationid(Integer locationid) {
/*  379 */     this.locationid = locationid;
/*      */   }
/*      */   
/*      */   public String getWorkroom() {
/*  383 */     return this.workroom;
/*      */   }
/*      */   
/*      */   public void setWorkroom(String workroom) {
/*  387 */     this.workroom = workroom;
/*      */   }
/*      */   
/*      */   public String getHomeaddress() {
/*  391 */     return this.homeaddress;
/*      */   }
/*      */   
/*      */   public void setHomeaddress(String homeaddress) {
/*  395 */     this.homeaddress = homeaddress;
/*      */   }
/*      */   
/*      */   public String getResourcetype() {
/*  399 */     return this.resourcetype;
/*      */   }
/*      */   
/*      */   public void setResourcetype(String resourcetype) {
/*  403 */     this.resourcetype = resourcetype;
/*      */   }
/*      */   
/*      */   public String getStartdate() {
/*  407 */     return this.startdate;
/*      */   }
/*      */   
/*      */   public void setStartdate(String startdate) {
/*  411 */     this.startdate = startdate;
/*      */   }
/*      */   
/*      */   public String getEnddate() {
/*  415 */     return this.enddate;
/*      */   }
/*      */   
/*      */   public void setEnddate(String enddate) {
/*  419 */     this.enddate = enddate;
/*      */   }
/*      */   
/*      */   public Integer getJobtitle() {
/*  423 */     return this.jobtitle;
/*      */   }
/*      */   
/*      */   public void setJobtitle(Integer jobtitle) {
/*  427 */     this.jobtitle = jobtitle;
/*      */   }
/*      */   
/*      */   public String getJobactivitydesc() {
/*  431 */     return this.jobactivitydesc;
/*      */   }
/*      */   
/*      */   public void setJobactivitydesc(String jobactivitydesc) {
/*  435 */     this.jobactivitydesc = jobactivitydesc;
/*      */   }
/*      */   
/*      */   public Short getJoblevel() {
/*  439 */     return this.joblevel;
/*      */   }
/*      */   
/*      */   public void setJoblevel(Short joblevel) {
/*  443 */     this.joblevel = joblevel;
/*      */   }
/*      */   
/*      */   public Short getSeclevel() {
/*  447 */     return this.seclevel;
/*      */   }
/*      */   
/*      */   public void setSeclevel(Short seclevel) {
/*  451 */     this.seclevel = seclevel;
/*      */   }
/*      */   
/*      */   public Integer getDepartmentid() {
/*  455 */     return this.departmentid;
/*      */   }
/*      */   
/*      */   public void setDepartmentid(Integer departmentid) {
/*  459 */     this.departmentid = departmentid;
/*      */   }
/*      */   
/*      */   public Integer getSubcompanyid1() {
/*  463 */     return this.subcompanyid1;
/*      */   }
/*      */   
/*      */   public void setSubcompanyid1(Integer subcompanyid1) {
/*  467 */     this.subcompanyid1 = subcompanyid1;
/*      */   }
/*      */   
/*      */   public Integer getCostcenterid() {
/*  471 */     return this.costcenterid;
/*      */   }
/*      */   
/*      */   public void setCostcenterid(Integer costcenterid) {
/*  475 */     this.costcenterid = costcenterid;
/*      */   }
/*      */   
/*      */   public Integer getManagerid() {
/*  479 */     return this.managerid;
/*      */   }
/*      */   
/*      */   public void setManagerid(Integer managerid) {
/*  483 */     this.managerid = managerid;
/*      */   }
/*      */   
/*      */   public Integer getAssistantid() {
/*  487 */     return this.assistantid;
/*      */   }
/*      */   
/*      */   public void setAssistantid(Integer assistantid) {
/*  491 */     this.assistantid = assistantid;
/*      */   }
/*      */   
/*      */   public Integer getBankid1() {
/*  495 */     return this.bankid1;
/*      */   }
/*      */   
/*      */   public void setBankid1(Integer bankid1) {
/*  499 */     this.bankid1 = bankid1;
/*      */   }
/*      */   
/*      */   public String getAccountid1() {
/*  503 */     return this.accountid1;
/*      */   }
/*      */   
/*      */   public void setAccountid1(String accountid1) {
/*  507 */     this.accountid1 = accountid1;
/*      */   }
/*      */   
/*      */   public Integer getResourceimageid() {
/*  511 */     return this.resourceimageid;
/*      */   }
/*      */   
/*      */   public void setResourceimageid(Integer resourceimageid) {
/*  515 */     this.resourceimageid = resourceimageid;
/*      */   }
/*      */   
/*      */   public Integer getCreaterid() {
/*  519 */     return this.createrid;
/*      */   }
/*      */   
/*      */   public void setCreaterid(Integer createrid) {
/*  523 */     this.createrid = createrid;
/*      */   }
/*      */   
/*      */   public String getCreatedate() {
/*  527 */     return this.createdate;
/*      */   }
/*      */   
/*      */   public void setCreatedate(String createdate) {
/*  531 */     this.createdate = createdate;
/*      */   }
/*      */   
/*      */   public Integer getLastmodid() {
/*  535 */     return this.lastmodid;
/*      */   }
/*      */   
/*      */   public void setLastmodid(Integer lastmodid) {
/*  539 */     this.lastmodid = lastmodid;
/*      */   }
/*      */   
/*      */   public String getLastmoddate() {
/*  543 */     return this.lastmoddate;
/*      */   }
/*      */   
/*      */   public void setLastmoddate(String lastmoddate) {
/*  547 */     this.lastmoddate = lastmoddate;
/*      */   }
/*      */   
/*      */   public String getLastlogindate() {
/*  551 */     return this.lastlogindate;
/*      */   }
/*      */   
/*      */   public void setLastlogindate(String lastlogindate) {
/*  555 */     this.lastlogindate = lastlogindate;
/*      */   }
/*      */   
/*      */   public String getDatefield1() {
/*  559 */     return this.datefield1;
/*      */   }
/*      */   
/*      */   public void setDatefield1(String datefield1) {
/*  563 */     this.datefield1 = datefield1;
/*      */   }
/*      */   
/*      */   public String getDatefield2() {
/*  567 */     return this.datefield2;
/*      */   }
/*      */   
/*      */   public void setDatefield2(String datefield2) {
/*  571 */     this.datefield2 = datefield2;
/*      */   }
/*      */   
/*      */   public String getDatefield3() {
/*  575 */     return this.datefield3;
/*      */   }
/*      */   
/*      */   public void setDatefield3(String datefield3) {
/*  579 */     this.datefield3 = datefield3;
/*      */   }
/*      */   
/*      */   public String getDatefield4() {
/*  583 */     return this.datefield4;
/*      */   }
/*      */   
/*      */   public void setDatefield4(String datefield4) {
/*  587 */     this.datefield4 = datefield4;
/*      */   }
/*      */   
/*      */   public String getDatefield5() {
/*  591 */     return this.datefield5;
/*      */   }
/*      */   
/*      */   public void setDatefield5(String datefield5) {
/*  595 */     this.datefield5 = datefield5;
/*      */   }
/*      */   
/*      */   public Float getNumberfield1() {
/*  599 */     return this.numberfield1;
/*      */   }
/*      */   
/*      */   public void setNumberfield1(Float numberfield1) {
/*  603 */     this.numberfield1 = numberfield1;
/*      */   }
/*      */   
/*      */   public Float getNumberfield2() {
/*  607 */     return this.numberfield2;
/*      */   }
/*      */   
/*      */   public void setNumberfield2(Float numberfield2) {
/*  611 */     this.numberfield2 = numberfield2;
/*      */   }
/*      */   
/*      */   public Float getNumberfield3() {
/*  615 */     return this.numberfield3;
/*      */   }
/*      */   
/*      */   public void setNumberfield3(Float numberfield3) {
/*  619 */     this.numberfield3 = numberfield3;
/*      */   }
/*      */   
/*      */   public Float getNumberfield4() {
/*  623 */     return this.numberfield4;
/*      */   }
/*      */   
/*      */   public void setNumberfield4(Float numberfield4) {
/*  627 */     this.numberfield4 = numberfield4;
/*      */   }
/*      */   
/*      */   public Float getNumberfield5() {
/*  631 */     return this.numberfield5;
/*      */   }
/*      */   
/*      */   public void setNumberfield5(Float numberfield5) {
/*  635 */     this.numberfield5 = numberfield5;
/*      */   }
/*      */   
/*      */   public String getTextfield1() {
/*  639 */     return this.textfield1;
/*      */   }
/*      */   
/*      */   public void setTextfield1(String textfield1) {
/*  643 */     this.textfield1 = textfield1;
/*      */   }
/*      */   
/*      */   public String getTextfield2() {
/*  647 */     return this.textfield2;
/*      */   }
/*      */   
/*      */   public void setTextfield2(String textfield2) {
/*  651 */     this.textfield2 = textfield2;
/*      */   }
/*      */   
/*      */   public String getTextfield3() {
/*  655 */     return this.textfield3;
/*      */   }
/*      */   
/*      */   public void setTextfield3(String textfield3) {
/*  659 */     this.textfield3 = textfield3;
/*      */   }
/*      */   
/*      */   public String getTextfield4() {
/*  663 */     return this.textfield4;
/*      */   }
/*      */   
/*      */   public void setTextfield4(String textfield4) {
/*  667 */     this.textfield4 = textfield4;
/*      */   }
/*      */   
/*      */   public String getTextfield5() {
/*  671 */     return this.textfield5;
/*      */   }
/*      */   
/*      */   public void setTextfield5(String textfield5) {
/*  675 */     this.textfield5 = textfield5;
/*      */   }
/*      */   
/*      */   public Short getTinyintfield1() {
/*  679 */     return this.tinyintfield1;
/*      */   }
/*      */   
/*      */   public void setTinyintfield1(Short tinyintfield1) {
/*  683 */     this.tinyintfield1 = tinyintfield1;
/*      */   }
/*      */   
/*      */   public Short getTinyintfield2() {
/*  687 */     return this.tinyintfield2;
/*      */   }
/*      */   
/*      */   public void setTinyintfield2(Short tinyintfield2) {
/*  691 */     this.tinyintfield2 = tinyintfield2;
/*      */   }
/*      */   
/*      */   public Short getTinyintfield3() {
/*  695 */     return this.tinyintfield3;
/*      */   }
/*      */   
/*      */   public void setTinyintfield3(Short tinyintfield3) {
/*  699 */     this.tinyintfield3 = tinyintfield3;
/*      */   }
/*      */   
/*      */   public Short getTinyintfield4() {
/*  703 */     return this.tinyintfield4;
/*      */   }
/*      */   
/*      */   public void setTinyintfield4(Short tinyintfield4) {
/*  707 */     this.tinyintfield4 = tinyintfield4;
/*      */   }
/*      */   
/*      */   public Short getTinyintfield5() {
/*  711 */     return this.tinyintfield5;
/*      */   }
/*      */   
/*      */   public void setTinyintfield5(Short tinyintfield5) {
/*  715 */     this.tinyintfield5 = tinyintfield5;
/*      */   }
/*      */   
/*      */   public String getCertificatenum() {
/*  719 */     return this.certificatenum;
/*      */   }
/*      */   
/*      */   public void setCertificatenum(String certificatenum) {
/*  723 */     this.certificatenum = certificatenum;
/*      */   }
/*      */   
/*      */   public String getNativeplace() {
/*  727 */     return this.nativeplace;
/*      */   }
/*      */   
/*      */   public void setNativeplace(String nativeplace) {
/*  731 */     this.nativeplace = nativeplace;
/*      */   }
/*      */   
/*      */   public Integer getEducationlevel() {
/*  735 */     return this.educationlevel;
/*      */   }
/*      */   
/*      */   public void setEducationlevel(Integer educationlevel) {
/*  739 */     this.educationlevel = educationlevel;
/*      */   }
/*      */   
/*      */   public String getBememberdate() {
/*  743 */     return this.bememberdate;
/*      */   }
/*      */   
/*      */   public void setBememberdate(String bememberdate) {
/*  747 */     this.bememberdate = bememberdate;
/*      */   }
/*      */   
/*      */   public String getBepartydate() {
/*  751 */     return this.bepartydate;
/*      */   }
/*      */   
/*      */   public void setBepartydate(String bepartydate) {
/*  755 */     this.bepartydate = bepartydate;
/*      */   }
/*      */   
/*      */   public String getWorkcode() {
/*  759 */     return this.workcode;
/*      */   }
/*      */   
/*      */   public void setWorkcode(String workcode) {
/*  763 */     this.workcode = workcode;
/*      */   }
/*      */   
/*      */   public String getRegresidentplace() {
/*  767 */     return this.regresidentplace;
/*      */   }
/*      */   
/*      */   public void setRegresidentplace(String regresidentplace) {
/*  771 */     this.regresidentplace = regresidentplace;
/*      */   }
/*      */   
/*      */   public String getHealthinfo() {
/*  775 */     return this.healthinfo;
/*      */   }
/*      */   
/*      */   public void setHealthinfo(String healthinfo) {
/*  779 */     this.healthinfo = healthinfo;
/*      */   }
/*      */   
/*      */   public String getResidentplace() {
/*  783 */     return this.residentplace;
/*      */   }
/*      */   
/*      */   public void setResidentplace(String residentplace) {
/*  787 */     this.residentplace = residentplace;
/*      */   }
/*      */   
/*      */   public String getPolicy() {
/*  791 */     return this.policy;
/*      */   }
/*      */   
/*      */   public void setPolicy(String policy) {
/*  795 */     this.policy = policy;
/*      */   }
/*      */   
/*      */   public String getDegree() {
/*  799 */     return this.degree;
/*      */   }
/*      */   
/*      */   public void setDegree(String degree) {
/*  803 */     this.degree = degree;
/*      */   }
/*      */   
/*      */   public String getHeight() {
/*  807 */     return this.height;
/*      */   }
/*      */   
/*      */   public void setHeight(String height) {
/*  811 */     this.height = height;
/*      */   }
/*      */   
/*      */   public Integer getUsekind() {
/*  815 */     return this.usekind;
/*      */   }
/*      */   
/*      */   public void setUsekind(Integer usekind) {
/*  819 */     this.usekind = usekind;
/*      */   }
/*      */   
/*      */   public Integer getJobcall() {
/*  823 */     return this.jobcall;
/*      */   }
/*      */   
/*      */   public void setJobcall(Integer jobcall) {
/*  827 */     this.jobcall = jobcall;
/*      */   }
/*      */   
/*      */   public String getAccumfundaccount() {
/*  831 */     return this.accumfundaccount;
/*      */   }
/*      */   
/*      */   public void setAccumfundaccount(String accumfundaccount) {
/*  835 */     this.accumfundaccount = accumfundaccount;
/*      */   }
/*      */   
/*      */   public String getBirthplace() {
/*  839 */     return this.birthplace;
/*      */   }
/*      */   
/*      */   public void setBirthplace(String birthplace) {
/*  843 */     this.birthplace = birthplace;
/*      */   }
/*      */   
/*      */   public String getFolk() {
/*  847 */     return this.folk;
/*      */   }
/*      */   
/*      */   public void setFolk(String folk) {
/*  851 */     this.folk = folk;
/*      */   }
/*      */   
/*      */   public String getResidentphone() {
/*  855 */     return this.residentphone;
/*      */   }
/*      */   
/*      */   public void setResidentphone(String residentphone) {
/*  859 */     this.residentphone = residentphone;
/*      */   }
/*      */   
/*      */   public String getResidentpostcode() {
/*  863 */     return this.residentpostcode;
/*      */   }
/*      */   
/*      */   public void setResidentpostcode(String residentpostcode) {
/*  867 */     this.residentpostcode = residentpostcode;
/*      */   }
/*      */   
/*      */   public String getExtphone() {
/*  871 */     return this.extphone;
/*      */   }
/*      */   
/*      */   public void setExtphone(String extphone) {
/*  875 */     this.extphone = extphone;
/*      */   }
/*      */   
/*      */   public String getManagerstr() {
/*  879 */     return this.managerstr;
/*      */   }
/*      */   
/*      */   public void setManagerstr(String managerstr) {
/*  883 */     this.managerstr = managerstr;
/*      */   }
/*      */   
/*      */   public Integer getStatus() {
/*  887 */     return this.status;
/*      */   }
/*      */   
/*      */   public void setStatus(Integer status) {
/*  891 */     this.status = status;
/*      */   }
/*      */   
/*      */   public String getFax() {
/*  895 */     return this.fax;
/*      */   }
/*      */   
/*      */   public void setFax(String fax) {
/*  899 */     this.fax = fax;
/*      */   }
/*      */   
/*      */   public String getIslabouunion() {
/*  903 */     return this.islabouunion;
/*      */   }
/*      */   
/*      */   public void setIslabouunion(String islabouunion) {
/*  907 */     this.islabouunion = islabouunion;
/*      */   }
/*      */   
/*      */   public Integer getWeight() {
/*  911 */     return this.weight;
/*      */   }
/*      */   
/*      */   public void setWeight(Integer weight) {
/*  915 */     this.weight = weight;
/*      */   }
/*      */   
/*      */   public String getTempresidentnumber() {
/*  919 */     return this.tempresidentnumber;
/*      */   }
/*      */   
/*      */   public void setTempresidentnumber(String tempresidentnumber) {
/*  923 */     this.tempresidentnumber = tempresidentnumber;
/*      */   }
/*      */   
/*      */   public String getProbationenddate() {
/*  927 */     return this.probationenddate;
/*      */   }
/*      */   
/*      */   public void setProbationenddate(String probationenddate) {
/*  931 */     this.probationenddate = probationenddate;
/*      */   }
/*      */   
/*      */   public Integer getCountryid() {
/*  935 */     return this.countryid;
/*      */   }
/*      */   
/*      */   public void setCountryid(Integer countryid) {
/*  939 */     this.countryid = countryid;
/*      */   }
/*      */   
/*      */   public String getPasswdchgdate() {
/*  943 */     return this.passwdchgdate;
/*      */   }
/*      */   
/*      */   public void setPasswdchgdate(String passwdchgdate) {
/*  947 */     this.passwdchgdate = passwdchgdate;
/*      */   }
/*      */   
/*      */   public Integer getNeedusb() {
/*  951 */     return this.needusb;
/*      */   }
/*      */   
/*      */   public void setNeedusb(Integer needusb) {
/*  955 */     this.needusb = needusb;
/*      */   }
/*      */   
/*      */   public String getSerial() {
/*  959 */     return this.serial;
/*      */   }
/*      */   
/*      */   public void setSerial(String serial) {
/*  963 */     this.serial = serial;
/*      */   }
/*      */   
/*      */   public String getAccount() {
/*  967 */     return this.account;
/*      */   }
/*      */   
/*      */   public void setAccount(String account) {
/*  971 */     this.account = account;
/*      */   }
/*      */   
/*      */   public String getLloginid() {
/*  975 */     return this.lloginid;
/*      */   }
/*      */   
/*      */   public void setLloginid(String lloginid) {
/*  979 */     this.lloginid = lloginid;
/*      */   }
/*      */   
/*      */   public Integer getNeeddynapass() {
/*  983 */     return this.needdynapass;
/*      */   }
/*      */   
/*      */   public void setNeeddynapass(Integer needdynapass) {
/*  987 */     this.needdynapass = needdynapass;
/*      */   }
/*      */   
/*      */   public Integer getSrcfrom() {
/*  991 */     return this.srcfrom;
/*      */   }
/*      */   
/*      */   public void setSrcfrom(Integer srcfrom) {
/*  995 */     this.srcfrom = srcfrom;
/*      */   }
/*      */   
/*      */   public Integer getSrcContacterid() {
/*  999 */     return this.srcContacterid;
/*      */   }
/*      */   
/*      */   public void setSrcContacterid(Integer srcContacterid) {
/* 1003 */     this.srcContacterid = srcContacterid;
/*      */   }
/*      */   
/*      */   public String getNcpkcode() {
/* 1007 */     return this.ncpkcode;
/*      */   }
/*      */   
/*      */   public void setNcpkcode(String ncpkcode) {
/* 1011 */     this.ncpkcode = ncpkcode;
/*      */   }
/*      */   
/*      */   public String getDsporder() {
/* 1015 */     return this.dsporder;
/*      */   }
/*      */   
/*      */   public void setDsporder(String dsporder) {
/* 1019 */     this.dsporder = dsporder;
/*      */   }
/*      */   
/*      */   public Integer getPasswordstate() {
/* 1023 */     return this.passwordstate;
/*      */   }
/*      */   
/*      */   public void setPasswordstate(Integer passwordstate) {
/* 1027 */     this.passwordstate = passwordstate;
/*      */   }
/*      */   
/*      */   public Double getSpaceSize() {
/* 1031 */     return this.spaceSize;
/*      */   }
/*      */   
/*      */   public void setSpaceSize(Double spaceSize) {
/* 1035 */     this.spaceSize = spaceSize;
/*      */   }
/*      */   
/*      */   public Integer getAccounttype() {
/* 1039 */     return this.accounttype;
/*      */   }
/*      */   
/*      */   public void setAccounttype(Integer accounttype) {
/* 1043 */     this.accounttype = accounttype;
/*      */   }
/*      */   
/*      */   public Integer getBelongto() {
/* 1047 */     return this.belongto;
/*      */   }
/*      */   
/*      */   public void setBelongto(Integer belongto) {
/* 1051 */     this.belongto = belongto;
/*      */   }
/*      */   
/*      */   public String getIsusedactylogram() {
/* 1055 */     return this.isusedactylogram;
/*      */   }
/*      */   
/*      */   public void setIsusedactylogram(String isusedactylogram) {
/* 1059 */     this.isusedactylogram = isusedactylogram;
/*      */   }
/*      */   
/*      */   public String getDactylogram() {
/* 1063 */     return this.dactylogram;
/*      */   }
/*      */   
/*      */   public void setDactylogram(String dactylogram) {
/* 1067 */     this.dactylogram = dactylogram;
/*      */   }
/*      */   
/*      */   public String getAssistantdactylogram() {
/* 1071 */     return this.assistantdactylogram;
/*      */   }
/*      */   
/*      */   public void setAssistantdactylogram(String assistantdactylogram) {
/* 1075 */     this.assistantdactylogram = assistantdactylogram;
/*      */   }
/*      */   
/*      */   public String getLoginnet() {
/* 1079 */     return this.loginnet;
/*      */   }
/*      */   
/*      */   public void setLoginnet(String loginnet) {
/* 1083 */     this.loginnet = loginnet;
/*      */   }
/*      */   
/*      */   public Integer getPasswordlock() {
/* 1087 */     return this.passwordlock;
/*      */   }
/*      */   
/*      */   public void setPasswordlock(Integer passwordlock) {
/* 1091 */     this.passwordlock = passwordlock;
/*      */   }
/*      */   
/*      */   public Integer getSumpasswordwrong() {
/* 1095 */     return this.sumpasswordwrong;
/*      */   }
/*      */   
/*      */   public void setSumpasswordwrong(Integer sumpasswordwrong) {
/* 1099 */     this.sumpasswordwrong = sumpasswordwrong;
/*      */   }
/*      */   
/*      */   public String getOldpassword1() {
/* 1103 */     return this.oldpassword1;
/*      */   }
/*      */   
/*      */   public void setOldpassword1(String oldpassword1) {
/* 1107 */     this.oldpassword1 = oldpassword1;
/*      */   }
/*      */   
/*      */   public String getOldpassword2() {
/* 1111 */     return this.oldpassword2;
/*      */   }
/*      */   
/*      */   public void setOldpassword2(String oldpassword2) {
/* 1115 */     this.oldpassword2 = oldpassword2;
/*      */   }
/*      */   
/*      */   public String getMessagerurl() {
/* 1119 */     return this.messagerurl;
/*      */   }
/*      */   
/*      */   public void setMessagerurl(String messagerurl) {
/* 1123 */     this.messagerurl = messagerurl;
/*      */   }
/*      */   
/*      */   public String getMsgStyle() {
/* 1127 */     return this.msgStyle;
/*      */   }
/*      */   
/*      */   public void setMsgStyle(String msgStyle) {
/* 1131 */     this.msgStyle = msgStyle;
/*      */   }
/*      */   
/*      */   public String getSwshrguid() {
/* 1135 */     return this.swshrguid;
/*      */   }
/*      */   
/*      */   public void setSwshrguid(String swshrguid) {
/* 1139 */     this.swshrguid = swshrguid;
/*      */   }
/*      */   
/*      */   public String getPinyinlastname() {
/* 1143 */     return this.pinyinlastname;
/*      */   }
/*      */   
/*      */   public void setPinyinlastname(String pinyinlastname) {
/* 1147 */     this.pinyinlastname = pinyinlastname;
/*      */   }
/*      */   
/*      */   public String getEcology_pinyin_search() {
/* 1151 */     return this.ecology_pinyin_search;
/*      */   }
/*      */   
/*      */   public void setEcology_pinyin_search(String ecology_pinyin_search) {
/* 1155 */     this.ecology_pinyin_search = ecology_pinyin_search;
/*      */   }
/*      */   
/*      */   public String getSwssigleaccount() {
/* 1159 */     return this.swssigleaccount;
/*      */   }
/*      */   
/*      */   public void setSwssigleaccount(String swssigleaccount) {
/* 1163 */     this.swssigleaccount = swssigleaccount;
/*      */   }
/*      */ }


/* Location:              /Users/<USER>/Documents/WorkSpace/IDEA/classbean/!/chinasws/webservices/HrmResourceCust.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */