package com.engine.interfaces.cy.worlkflow.contract.action;

import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;

import java.util.HashMap;
import java.util.Map;

public class ChangeWorkflowNumAction extends BaseBean implements Action {

    private String colstart;
    private String colend;
    private String overwrite;

    @Override
    public String execute(RequestInfo requestInfo) {

        String moduletable = "formtable_main_38";
        String col = "lcbh";
        String workflowtable = "";

        Property[] propertys = requestInfo.getMainTableInfo().getProperty();
        Map<String, String> map = new HashMap<>();
        for (Property property : propertys) {
            String str = property.getName();
            String value = property.getValue();
            map.put(str, value);
        }
        RecordSet recordSet = new RecordSet();
        String allcol="";
        if(!"".equals(map.get(colstart))&&!"".equals(map.get(colend))){
            allcol = "*"+map.get(colstart)+map.get(colend);
        }
        if(!"".equals(allcol)){
            recordSet.execute("SELECT * FROM "+moduletable+" WHERE "+col+" like "+allcol+"+'%'");
                if(recordSet.next()){
                  String bh = recordSet.getString(col);
                  RecordSet rs = new RecordSet();
                  rs.execute("UPDATE "+workflowtable+" set "+ overwrite +" = '"+bh+"' where requestid = '"+requestInfo.getRequestid()+"'");
                }
        }
        return Action.SUCCESS;
    }

    public String getColstart() {
        return colstart;
    }

    public void setColstart(String colstart) {
        this.colstart = colstart;
    }

    public String getColend() {
        return colend;
    }

    public void setColend(String colend) {
        this.colend = colend;
    }

    public String getOverwrite() {
        return overwrite;
    }

    public void setOverwrite(String overwrite) {
        this.overwrite = overwrite;
    }
}
